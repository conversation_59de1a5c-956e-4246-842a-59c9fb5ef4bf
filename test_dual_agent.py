#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双Agent系统测试脚本
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_dual_agent():
    print('🧪 开始测试双Agent系统...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        print('✅ 组件导入成功')
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print('✅ Agent注册成功')
        
        # 测试会话
        session_id = 'test_session_001'
        
        # 第一步：提供完整信息
        print('\n📝 第一步：提供完整生辰信息')
        result1 = await coordinator.handle_user_message(
            session_id, 
            '我是1988年6月1日午时出生的男命，想算命'
        )
        
        print(f'响应成功: {result1.get("success")}')
        print(f'响应内容: {result1.get("response", "")[:200]}...')
        print(f'会话阶段: {result1.get("stage")}')
        
        # 等待一下，看后台是否启动
        print('\n⏳ 等待5秒，观察后台处理...')
        time.sleep(5)
        
        # 第二步：立即提问
        print('\n💬 第二步：立即询问财运')
        result2 = await coordinator.handle_user_message(
            session_id, 
            '我想看看财运'
        )
        
        print(f'响应成功: {result2.get("success")}')
        print(f'响应内容: {result2.get("response", "")[:200]}...')
        
        # 检查会话状态
        session_state = master_agent.get_session_state(session_id)
        print(f'\n📊 会话状态:')
        print(f'  阶段: {session_state.get("stage")}')
        print(f'  生辰信息: {session_state.get("birth_info")}')
        print(f'  结果ID: {session_state.get("result_id")}')
        
        # 检查后台分析进度
        result_id = session_state.get("result_id")
        if result_id:
            progress = master_agent.get_analysis_progress(result_id)
            print(f'\n📈 分析进度: {progress}')
        else:
            print('\n❌ 没有找到result_id，后台分析可能失败')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_dual_agent())
