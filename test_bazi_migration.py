#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试八字工具迁移到人性化交互系统
"""

import sys
import os
sys.path.append('.')

def test_humanized_bazi_tool():
    """测试人性化八字工具"""
    print("测试人性化八字工具")
    print("-" * 50)
    
    try:
        from core.tools.humanized_bazi_tool import HumanizedBaziTool
        
        # 创建工具实例
        tool = HumanizedBaziTool()
        
        # 测试数据
        intent = {
            "intent": "bazi",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6", 
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        context = {}
        
        # 执行测试
        result = tool.execute(intent, context)
        
        if result.get("success"):
            print("✅ 人性化八字工具执行成功")
            print(f"   类型: {result.get('type')}")
            print(f"   消息: {result.get('message')}")
            
            # 检查计算结果
            calc_result = result.get("calculation_result", {})
            if calc_result.get("success"):
                print("✅ 八字计算成功")
                raw_result = calc_result.get("raw_result", {})
                if "干支" in raw_result:
                    ganzhi = raw_result["干支"]
                    print(f"   四柱: {ganzhi.get('文本', '未知')}")
                if "五行" in raw_result:
                    print("   五行分析: 包含完整五行数据")
            else:
                print(f"❌ 八字计算失败: {calc_result.get('error')}")
        else:
            print(f"❌ 人性化八字工具执行失败: {result.get('error')}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selector_bazi():
    """测试工具选择器的八字处理"""
    print("\n测试工具选择器的八字处理")
    print("-" * 50)
    
    try:
        from core.tools.tool_selector import ToolSelector
        
        selector = ToolSelector()
        
        # 测试意图
        intent_result = {
            "intent": "bazi",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1", 
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        context = {}
        
        # 选择工具
        tool_result = selector.select_tool(intent_result, context)
        
        if tool_result.get("success"):
            print("✅ 工具选择成功")
            print(f"   工具名称: {tool_result.get('tool_name')}")
            print(f"   工具描述: {tool_result.get('tool_description')}")
            
            result = tool_result.get("result", {})
            if result.get("success"):
                print("✅ 八字分析执行成功")
                print(f"   分析类型: {result.get('type')}")
                print(f"   消息: {result.get('message')}")
            else:
                print(f"❌ 八字分析执行失败: {result.get('error')}")
        else:
            print(f"❌ 工具选择失败: {tool_result.get('error')}")
        
        return tool_result.get("success", False)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_humanized_engine_bazi():
    """测试人性化引擎的八字分析"""
    print("\n测试人性化引擎的八字分析")
    print("-" * 50)
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        session_id = "test_bazi_migration"
        
        # 测试八字算命请求
        message = "我1988年6月1日午时出生，男，想看八字算命"
        
        print(f"用户消息: {message}")
        
        responses = engine.process_user_message(message, session_id)
        
        print(f"总响应数: {len(responses)}")
        
        # 分析响应类型
        response_types = {}
        bazi_analysis_count = 0
        
        for response in responses:
            response_type = response.get("type", "unknown")
            response_types[response_type] = response_types.get(response_type, 0) + 1
            
            if response_type == "detailed_analysis":
                bazi_analysis_count += 1
                content = response.get("content", "")
                print(f"\n八字分析 {bazi_analysis_count}:")
                print(f"  长度: {len(content)} 字符")
                print(f"  预览: {content[:100]}...")
                
                # 检查是否包含八字特征
                bazi_features = ["四柱", "干支", "五行", "根据您的八字"]
                found_features = [f for f in bazi_features if f in content]
                print(f"  八字特征: {len(found_features)}/{len(bazi_features)} ({found_features})")
        
        print(f"\n响应类型分布:")
        for response_type, count in response_types.items():
            print(f"  {response_type}: {count} 个")
        
        # 检查关键响应类型
        required_types = ["chart_presentation", "analysis_intro", "detailed_analysis", "synthesis"]
        missing_types = [t for t in required_types if t not in response_types]
        
        if missing_types:
            print(f"\n⚠️ 缺少关键响应类型: {missing_types}")
        else:
            print(f"\n✅ 所有关键响应类型都存在")
        
        success = (
            len(responses) >= 10 and  # 至少10个响应
            bazi_analysis_count >= 4 and  # 至少4个详细分析
            len(missing_types) == 0  # 没有缺少的类型
        )
        
        print(f"\n人性化八字分析: {'✅ 成功' if success else '⚠️ 需要改进'}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bazi_fewshot_integration():
    """测试八字Few-shot Learning集成"""
    print("\n测试八字Few-shot Learning集成")
    print("-" * 50)
    
    try:
        from core.nlu.llm_client import LLMClient
        
        client = LLMClient()
        
        # 测试八字相关的Few-shot调用
        test_cases = [
            ("请分析我的八字性格特质", "general"),
            ("我的八字事业运势如何", "career"),
            ("八字看我的财运状况", "wealth"),
            ("从八字角度分析我的感情", "love")
        ]
        
        success_count = 0
        
        for message, category in test_cases:
            print(f"\n测试: {message}")
            
            response = client.fewshot_chat(
                user_message=message,
                category=category,
                temperature=0.7,
                max_tokens=500
            )
            
            if response and len(response) > 100:
                print(f"✅ 响应成功: {len(response)} 字符")
                
                # 检查八字专业术语
                bazi_terms = ["根据", "八字", "建议", "保持", "通过", "将会"]
                found_terms = [term for term in bazi_terms if term in response]
                
                print(f"   专业术语: {len(found_terms)}/{len(bazi_terms)}")
                print(f"   内容预览: {response[:80]}...")
                
                if len(found_terms) >= 4:
                    success_count += 1
                    print(f"   质量: ✅ 优秀")
                else:
                    print(f"   质量: ⚠️ 一般")
            else:
                print(f"❌ 响应失败或过短")
        
        print(f"\nFew-shot八字集成: {success_count}/{len(test_cases)} 优秀")
        
        return success_count >= len(test_cases) * 0.75
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("阶段3.3：八字工具迁移测试")
    print("=" * 80)
    print("目标: 将八字算命功能迁移到人性化交互系统")
    print("=" * 80)
    
    # 执行测试
    test_results = []
    
    # 1. 人性化八字工具测试
    test_results.append(("人性化八字工具", test_humanized_bazi_tool()))
    
    # 2. 工具选择器八字处理测试
    test_results.append(("工具选择器八字处理", test_tool_selector_bazi()))
    
    # 3. 人性化引擎八字分析测试
    test_results.append(("人性化引擎八字分析", test_humanized_engine_bazi()))
    
    # 4. 八字Few-shot集成测试
    test_results.append(("八字Few-shot集成", test_bazi_fewshot_integration()))
    
    # 汇总结果
    print(f"\n阶段3.3八字工具迁移测试结果")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 阶段3.3八字工具迁移成功！")
        print("\n🎯 实现的核心功能:")
        print("  ✅ 人性化八字工具 - 统一接口设计")
        print("  ✅ 工具选择器集成 - 智能路由到八字分析")
        print("  ✅ 人性化交互体验 - 15段式八字分析")
        print("  ✅ Few-shot Learning - 专业八字话术")
        print("\n🌟 八字分析特色:")
        print("  📜 真实八字算法 - 四柱排盘、五行分析")
        print("  💬 专业话术融入 - 基于微调模型训练数据")
        print("  🔄 分段式交互 - 性格、事业、财运、感情")
        print("  🎯 智能分析 - 结合真实八字数据的专业解读")
        print("\n📋 PRD阶段3.3目标达成！")
    else:
        print("💥 部分功能存在问题，需要修复后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
