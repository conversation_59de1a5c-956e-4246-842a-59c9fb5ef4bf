#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试紫薇+八字融合系统
"""

def test_fusion_complete():
    """完整测试融合系统"""
    print("🧪 完整测试紫薇+八字融合系统")
    print("=" * 80)
    
    try:
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        
        # 测试数据
        test_data = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"📊 测试数据: {test_data}")
        
        # 执行融合分析
        result = engine.calculate_fusion_analysis(**test_data)
        
        if result.get("success"):
            print("✅ 融合分析成功！")
            
            # 1. 基础信息
            birth_info = result.get("birth_info", {})
            print(f"\n📅 出生信息:")
            print(f"  时间: {birth_info.get('datetime', '')}")
            print(f"  农历: {birth_info.get('lunar', '')}")
            print(f"  生肖: {birth_info.get('zodiac', '')}")
            print(f"  星座: {birth_info.get('sign', '')}")
            
            # 2. 紫薇斗数结果
            ziwei_analysis = result.get("ziwei_analysis", {})
            print(f"\n🌟 紫薇斗数分析:")
            if "error" in ziwei_analysis:
                print(f"  ❌ 错误: {ziwei_analysis['error']}")
            else:
                print(f"  ✅ 紫薇斗数计算成功")
                if "palaces" in ziwei_analysis:
                    print(f"  宫位数量: {len(ziwei_analysis['palaces'])}个")
                if "birth_info" in ziwei_analysis:
                    ziwei_bazi = ziwei_analysis["birth_info"].get("chinese_date", "")
                    print(f"  紫薇八字: {ziwei_bazi}")
            
            # 3. 八字命理结果
            bazi_analysis = result.get("bazi_analysis", {})
            print(f"\n🔮 八字命理分析:")
            if "error" in bazi_analysis:
                print(f"  ❌ 错误: {bazi_analysis['error']}")
            else:
                print(f"  ✅ 八字计算成功")
                if "bazi_info" in bazi_analysis:
                    bazi_bazi = bazi_analysis["bazi_info"].get("chinese_date", "")
                    print(f"  八字四柱: {bazi_bazi}")
                
                if "analysis" in bazi_analysis:
                    analysis = bazi_analysis["analysis"]
                    
                    # 五行分析
                    if "wuxing" in analysis:
                        wuxing = analysis["wuxing"]
                        print(f"  五行分析:")
                        for element, count in wuxing["count"].items():
                            strength = wuxing["strength"][element]
                            print(f"    {element}: {count}个 ({strength})")
                    
                    # 日主分析
                    if "day_master" in analysis:
                        day_master = analysis["day_master"]
                        print(f"  日主: {day_master['gan']} ({day_master['element']}) - {day_master['strength']}")
                    
                    # 大运分析
                    if "dayun" in analysis:
                        dayun = analysis["dayun"]
                        print(f"  大运: 起运{dayun['start_age']}岁")
                        if dayun["dayun_list"]:
                            first_dayun = dayun["dayun_list"][0]
                            print(f"    首步: {first_dayun['ganzhi']} ({first_dayun['shishen']}) {first_dayun['age_range']}")
                
                # 传统分析
                if "traditional_analysis" in bazi_analysis:
                    traditional = bazi_analysis["traditional_analysis"]
                    print(f"  传统分析:")
                    if "命格总评" in traditional:
                        mingge = traditional["命格总评"]
                        print(f"    格局: {mingge.get('格局', '')}")
                    if "人生建议" in traditional:
                        advice = traditional["人生建议"]
                        print(f"    职业: {advice.get('职业方向', '')}")
            
            # 4. 融合分析结果
            fusion_analysis = result.get("fusion_analysis", {})
            print(f"\n🔗 融合分析:")
            
            # 交叉验证
            if "cross_validation" in fusion_analysis:
                validation = fusion_analysis["cross_validation"]
                confidence = validation.get("confidence_level", 0)
                print(f"  交叉验证:")
                print(f"    一致性得分: {confidence:.2f}")
                
                consistency = validation.get("consistency_check", {})
                for item, check in consistency.items():
                    if isinstance(check, dict):
                        status = "✅" if check.get("consistent", False) else "❌"
                        print(f"    {item}: {status}")
                        if not check.get("consistent", False):
                            print(f"      紫薇: {check.get('ziwei', '')}")
                            print(f"      八字: {check.get('bazi', '')}")
            
            # 综合分析
            if "comprehensive_analysis" in fusion_analysis:
                comprehensive = fusion_analysis["comprehensive_analysis"]
                print(f"  综合分析:")
                for aspect, analysis in comprehensive.items():
                    if isinstance(analysis, dict) and "confidence" in analysis:
                        confidence = analysis["confidence"]
                        print(f"    {aspect}: 置信度{confidence:.2f}")
            
            # 5. 图表配置
            chart_data = result.get("chart_data", {})
            print(f"\n🎨 图表配置:")
            print(f"  布局模式: {chart_data.get('fusion_layout', '')}")
            
            image_config = chart_data.get("image_config", {})
            print(f"  图片尺寸: {image_config.get('width', 0)}x{image_config.get('height', 0)}")
            print(f"  布局样式: {image_config.get('layout', '')}")
            
            # 紫薇图表
            ziwei_chart = chart_data.get("ziwei_chart", {})
            if ziwei_chart:
                print(f"  紫薇图表: {ziwei_chart.get('layout', '')}")
                if "palaces" in ziwei_chart:
                    print(f"    宫位数据: 已包含")
            
            # 八字图表
            bazi_chart = chart_data.get("bazi_chart", {})
            if bazi_chart:
                print(f"  八字图表: {bazi_chart.get('layout', '')}")
                if "bazi" in bazi_chart:
                    print(f"    八字数据: {bazi_chart['bazi']}")
            
            return True
        else:
            print(f"❌ 融合分析失败: {result.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fusion_advantages():
    """测试融合系统的优势"""
    print(f"\n🎯 融合系统优势展示")
    print("=" * 80)
    
    print("✅ 融合系统的优势:")
    print("1. 数据一致性验证:")
    print("   - 紫薇和八字使用相同的py-iztro核心")
    print("   - 自动验证生肖、八字等基础信息一致性")
    print("   - 提供置信度评分")
    
    print("\n2. 互相印证分析:")
    print("   - 紫薇斗数：基于星曜宫位的性格事业分析")
    print("   - 八字命理：基于五行干支的运势格局分析")
    print("   - 融合结论：两种方法互相验证，提高准确性")
    
    print("\n3. 图表融合展示:")
    print("   - 中央：紫薇12宫圆形布局")
    print("   - 周围：八字四柱和五行信息")
    print("   - 尺寸：1200x800，适合详细展示")
    
    print("\n4. 解读层面融合:")
    print("   - 不再分开解读紫薇和八字")
    print("   - 直接提供融合后的综合分析")
    print("   - 基于两种算法的交叉验证结果")
    
    print("\n5. 用户体验提升:")
    print("   - 一次输入，获得两种算法结果")
    print("   - 避免重复输入生辰信息")
    print("   - 统一的图表展示")

def main():
    """主函数"""
    print("🧪 紫薇+八字融合系统完整测试")
    print("=" * 100)
    
    # 完整功能测试
    success = test_fusion_complete()
    
    # 优势展示
    test_fusion_advantages()
    
    print("\n" + "=" * 100)
    if success:
        print("🎉 融合系统测试成功！")
        print("✅ 紫薇斗数+八字命理融合分析完全可行")
        print("✅ 数据层面：完美融合，互相验证")
        print("✅ 图表层面：可以设计复合布局")
        print("✅ 解读层面：可以统一分析，互相印证")
    else:
        print("❌ 融合系统测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
