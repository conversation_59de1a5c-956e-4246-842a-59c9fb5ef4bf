# 独立排盘模块使用说明

## 📋 项目概述

这个独立排盘模块是从主项目中提炼出来的，专门用于处理单人的紫薇斗数+八字命理排盘功能。它直接使用主项目的 `ZiweiBaziFusionEngine` 融合引擎，确保算法的准确性和稳定性。

## ✅ 已完成功能

### 1. 核心功能
- ✅ 紫薇斗数排盘计算
- ✅ 八字命理排盘计算  
- ✅ 融合分析和交叉验证
- ✅ 多种输入格式支持
- ✅ 自动结果保存

### 2. 输入支持
- ✅ 数值输入：年、月、日、时、性别
- ✅ 字符串输入：`1990-3-15-8`、`1990年3月15日8时`
- ✅ 性别支持：男、女
- ✅ 时间范围：理论上支持所有年份

### 3. 输出格式
- ✅ JSON格式：完整的计算数据
- ✅ TXT格式：格式化的可读输出
- ✅ 摘要格式：关键信息提取

## 🚀 快速使用

### 最简单的使用方式

```python
# 1. 导入模块
from simple_interface import SimplePaipanInterface

# 2. 创建接口
interface = SimplePaipanInterface()

# 3. 计算排盘
result = interface.calculate_and_save(1990, 3, 15, 8, "女")

# 4. 查看结果
interface.print_result(result)
```

### 命令行使用

```bash
# 进入目录
cd standalone_paipan

# 运行快速演示
python quick_demo.py

# 或运行交互式界面
python simple_interface.py

# 或运行测试
python test_standalone.py
```

## 📁 文件结构

```
standalone_paipan/
├── __init__.py              # 模块初始化
├── core_calculator.py       # 核心计算器（基于融合引擎）
├── result_saver.py         # 结果保存模块
├── simple_interface.py     # 简单使用接口
├── quick_demo.py           # 快速演示脚本
├── test_standalone.py      # 测试脚本
├── README.md              # 详细说明文档
├── 使用说明.md             # 中文使用说明
└── paipan_outputs/        # 输出目录（自动创建）
    ├── *.json             # JSON格式结果
    ├── *.txt              # 文本格式结果
    └── *_summary.txt      # 摘要格式结果
```

## 📊 输出示例

### 文本输出格式
```
============================================================
紫薇斗数+八字命理 完整排盘
============================================================
出生时间: 1990年3月15日8时
性别: 女
计算时间: 2025-06-25T01:11:22

【紫薇斗数排盘】
----------------------------------------
阳历: 1990年3月15日8时
农历: 一九九〇年二月十九
干支: 庚午 己卯 己卯 戊辰
生肖: 马
星座: 双鱼座

十二宫星曜分布:
  命宫(亥): 主星: 天同 | 煞星: 三台, 恩光, 天官, 月德
  兄弟宫(戌): 主星: 破军 | 煞星: 龙池, 台辅, 华盖, 旬空, 天刑
  ...

【八字命理排盘】
----------------------------------------
出生时间: 1990年3月15日8时
八字: 庚午 己卯 己卯 戊辰
年柱: 庚午
月柱: 己卯
日柱: 己卯
时柱: 戊辰
============================================================
```

## 🔧 技术特点

### 1. 基于现有项目
- 直接使用主项目的 `ZiweiBaziFusionEngine`
- 保证算法的准确性和一致性
- 包含完整的融合分析功能

### 2. 独立运行
- 不影响主项目的稳定性
- 可以独立部署和使用
- 简化的接口设计

### 3. 数据完整性
- 包含紫薇斗数的十二宫星曜分布
- 包含八字的四柱干支信息
- 包含融合分析和交叉验证结果

## ⚠️ 注意事项

### 1. 依赖关系
- 本模块依赖主项目的算法模块
- 需要确保主项目的 `core/` 和 `algorithms/` 目录可访问
- 需要安装 `py-iztro` 等依赖包

### 2. 输出目录
- 默认输出到 `paipan_outputs/` 目录
- 会自动创建目录
- 文件名包含时间戳，避免覆盖

### 3. 错误处理
- 如果某个算法失败，会在结果中标明
- 保存的文件中会包含错误信息
- 建议检查输出文件的完整性

## 🎯 使用建议

### 1. 开发环境
- 确保主项目环境正常
- 测试算法模块是否可用
- 检查依赖包安装情况

### 2. 生产使用
- 建议先运行测试脚本验证功能
- 检查输出文件的质量
- 根据需要调整输出格式

### 3. 扩展开发
- 可以基于 `core_calculator.py` 进行扩展
- 可以修改 `result_saver.py` 调整输出格式
- 可以在 `simple_interface.py` 中添加新功能

## 📞 技术支持

如果遇到问题：
1. 首先运行 `python test_standalone.py` 检查基础功能
2. 检查主项目的算法模块是否正常
3. 查看输出文件中的错误信息
4. 确认依赖包安装情况

## 🎉 总结

这个独立排盘模块成功地从主项目中提炼出了核心的排盘功能，提供了简单易用的接口，同时保持了算法的准确性和完整性。它可以独立运行，不影响主项目的稳定性，是一个很好的功能分离实践。
