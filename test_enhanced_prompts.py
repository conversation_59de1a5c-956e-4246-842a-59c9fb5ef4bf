#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版提示词系统
验证客观性、针对性、一致性改进
"""

import asyncio
import sys

def test_enhanced_prompts():
    """测试增强版提示词系统"""
    try:
        print("🚀 增强版提示词系统测试")
        print("=" * 80)
        print("验证改进效果:")
        print("1. 性别差异化关注点")
        print("2. 年龄特定分析要求")
        print("3. 客观性和一致性要求")
        print("=" * 80)
        
        from core.prompts.enhanced_analysis_prompts import EnhancedAnalysisPrompts
        from core.analysis.consistency_manager import ConsistencyManager
        
        enhanced_prompts = EnhancedAnalysisPrompts()
        consistency_manager = ConsistencyManager()
        
        # 测试案例1：女性用户提示词
        print("\n👩 测试案例1：女性用户提示词")
        print("-" * 50)
        
        female_birth_info = {
            'year': '1990',
            'month': '8', 
            'day': '15',
            'hour': '14',
            'gender': '女'
        }
        
        raw_data = {
            'palaces': {
                '命宫': {'position': '亥', 'major_stars': ['天相'], 'minor_stars': []},
                '财帛宫': {'position': '未', 'major_stars': ['天府'], 'minor_stars': ['左辅', '右弼']},
                '夫妻宫': {'position': '酉', 'major_stars': ['紫微', '贪狼'], 'minor_stars': []}
            }
        }
        
        # 生成婚姻分析提示词
        marriage_prompt = enhanced_prompts.build_enhanced_angle_prompt(
            angle_name="婚姻分析",
            analysis_key="marriage_love",
            description="感情婚姻、桃花运势与配偶关系",
            raw_data=raw_data,
            birth_info=female_birth_info,
            calc_name="紫薇斗数",
            previous_analyses=None,
            section_index=1,
            total_sections=4
        )
        
        print(f"✅ 女性婚姻分析提示词生成成功")
        print(f"📝 提示词长度: {len(marriage_prompt)}字符")
        
        # 检查是否包含女性特定关注点
        female_keywords = ['嫁入豪门', '上嫁', '配偶条件', '贵妇', '姻缘', '颜值', '气质']
        female_focus_count = sum(1 for keyword in female_keywords if keyword in marriage_prompt)
        print(f"💕 女性特定关注点: {female_focus_count}/7")
        
        # 检查客观性要求
        objective_keywords = ['负面分析', '风险', '挑战', '注意事项', '客观']
        objective_count = sum(1 for keyword in objective_keywords if keyword in marriage_prompt)
        print(f"⚖️ 客观性要求: {objective_count}/5")
        
        # 测试案例2：男性用户提示词
        print("\n👨 测试案例2：男性用户提示词")
        print("-" * 50)
        
        male_birth_info = {
            'year': '1985',
            'month': '3',
            'day': '20', 
            'hour': '10',
            'gender': '男'
        }
        
        # 生成财富分析提示词
        wealth_prompt = enhanced_prompts.build_enhanced_angle_prompt(
            angle_name="财富分析",
            analysis_key="wealth_fortune",
            description="财运状况、理财投资与财富积累",
            raw_data=raw_data,
            birth_info=male_birth_info,
            calc_name="紫薇斗数",
            previous_analyses=None,
            section_index=1,
            total_sections=4
        )
        
        print(f"✅ 男性财富分析提示词生成成功")
        print(f"📝 提示词长度: {len(wealth_prompt)}字符")
        
        # 检查是否包含男性特定关注点
        male_keywords = ['发财', '创业', '投资', '当官', '提拔', '升职', '领导']
        male_focus_count = sum(1 for keyword in male_keywords if keyword in wealth_prompt)
        print(f"💰 男性特定关注点: {male_focus_count}/7")
        
        # 测试案例3：儿童用户提示词
        print("\n👶 测试案例3：儿童用户提示词")
        print("-" * 50)
        
        child_birth_info = {
            'year': '2015',
            'month': '6',
            'day': '1',
            'hour': '8',
            'gender': '男'
        }
        
        # 生成学业分析提示词
        education_prompt = enhanced_prompts.build_enhanced_angle_prompt(
            angle_name="学业分析",
            analysis_key="education_learning",
            description="学习教育、智慧发展与知识积累",
            raw_data=raw_data,
            birth_info=child_birth_info,
            calc_name="紫薇斗数",
            previous_analyses=None,
            section_index=1,
            total_sections=4
        )
        
        print(f"✅ 儿童学业分析提示词生成成功")
        print(f"📝 提示词长度: {len(education_prompt)}字符")
        
        # 检查是否包含儿童特定关注点
        child_keywords = ['学霸', '聪明', '智商', '学习能力', '记忆力', '健康', '孝顺']
        child_focus_count = sum(1 for keyword in child_keywords if keyword in education_prompt)
        print(f"📚 儿童特定关注点: {child_focus_count}/7")
        
        # 测试案例4：一致性检查
        print("\n🔍 测试案例4：一致性检查功能")
        print("-" * 50)
        
        # 模拟有矛盾的分析内容
        mock_analyses = {
            "personality_destiny": "您性格开朗活泼，善于交际，具有很强的领导能力。财运很好，容易发财致富。",
            "wealth_fortune": "您性格内向保守，不善交际，缺乏领导才能。财运不佳，难以积累财富。",
            "marriage_love": "您今年会遇到真爱，明年结婚最合适。感情运势非常好。",
            "health_wellness": "您后年才会有姻缘，三年后结婚比较好。需要注意感情方面的问题。"
        }
        
        consistency_report = consistency_manager.check_consistency(mock_analyses)
        consistency_score = consistency_report.get('overall_score', 0)
        
        print(f"✅ 一致性检查完成")
        print(f"📊 一致性评分: {consistency_score}/100")
        print(f"🔍 发现矛盾: {len(consistency_report.get('contradictions', []))}处")
        print(f"🔄 发现重复: {len(consistency_report.get('repetitions', []))}处")
        print(f"⚖️ 特质平衡: {consistency_report.get('trait_balance', {}).get('is_balanced', False)}")
        
        # 测试整合提示词生成
        if consistency_score < 80:
            integration_prompt = consistency_manager.generate_integration_prompt(
                mock_analyses, consistency_report
            )
            print(f"✅ 整合提示词生成成功")
            print(f"📝 整合提示词长度: {len(integration_prompt)}字符")
        
        # 最终评估
        print(f"\n🎯 增强版提示词系统测试评估")
        print("=" * 80)
        
        success_criteria = [
            female_focus_count >= 3,  # 女性关注点
            male_focus_count >= 3,    # 男性关注点
            child_focus_count >= 3,   # 儿童关注点
            objective_count >= 2,     # 客观性要求
            consistency_score < 100   # 一致性检查有效
        ]
        
        success_count = sum(success_criteria)
        
        if success_count >= 4:
            print(f"🎉 增强版提示词系统测试成功！")
            print(f"\n💪 改进效果验证:")
            print(f"   ✅ 性别差异化分析: {'通过' if success_criteria[0] and success_criteria[1] else '需改进'}")
            print(f"   ✅ 年龄特定关注点: {'通过' if success_criteria[2] else '需改进'}")
            print(f"   ✅ 客观性分析要求: {'通过' if success_criteria[3] else '需改进'}")
            print(f"   ✅ 一致性检查机制: {'通过' if success_criteria[4] else '需改进'}")
            
            print(f"\n🌟 系统特点:")
            print(f"   🎯 针对性强：根据性别年龄自动调整关注点")
            print(f"   🔍 一致性好：自动检查矛盾和重复内容")
            print(f"   ⚖️ 客观平衡：强制要求包含负面分析")
            print(f"   📝 内容丰富：详细的分段生成策略")
            
            return True
        else:
            print(f"💥 增强版提示词系统需要进一步优化")
            print(f"   成功率: {success_count}/5")
            print(f"   女性关注点: {female_focus_count}/7")
            print(f"   男性关注点: {male_focus_count}/7")
            print(f"   儿童关注点: {child_focus_count}/7")
            print(f"   客观性要求: {objective_count}/5")
            print(f"   一致性检查: {consistency_score}/100")
            return False
            
    except Exception as e:
        print(f"❌ 增强版提示词测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 增强版提示词系统功能测试")
    print("=" * 80)
    print("解决的核心问题:")
    print("1. LLM提示词缺乏客观性要求")
    print("2. 缺乏针对性分析（性别年龄差异）")
    print("3. 缺乏一致性检查机制")
    print("=" * 80)
    
    success = test_enhanced_prompts()
    
    if success:
        print(f"\n🎉 恭喜！增强版提示词系统开发成功！")
        print(f"\n✅ 您的问题完美解决:")
        print(f"   - 不再只说好话，强制包含客观的负面分析")
        print(f"   - 不再泛泛而谈，针对性强的差异化分析")
        print(f"   - 不再重复矛盾，具有一致性检查机制")
        print(f"   - 女性：颜值、贵妇命、嫁入豪门分析")
        print(f"   - 男性：财富、官运、提拔时间分析")
        print(f"   - 儿童：学霸、聪明、健康、孝顺分析")
        
        print(f"\n🔧 下一步可以:")
        print(f"   1. 集成到完整的12角度分析系统")
        print(f"   2. 在Web界面中测试实际效果")
        print(f"   3. 根据用户反馈进一步优化")
    else:
        print(f"\n💥 增强版提示词系统需要进一步优化")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
