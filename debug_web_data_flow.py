#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Web界面数据流
检查用户输入到分析结果的完整数据传递过程
"""

import asyncio

async def debug_web_data_flow():
    """调试Web界面数据流"""
    print("🔍 调试Web界面数据流")
    print("=" * 60)
    
    try:
        # 模拟Web界面的用户输入
        birth_info = {
            "year": "1988",
            "month": "6", 
            "day": "1",
            "hour": "午时",  # 注意这里是时辰名称，不是数字
            "gender": "男"
        }
        
        print(f"📅 模拟用户输入: {birth_info}")
        
        # 1. 测试融合分析引擎
        print(f"\n1️⃣ 测试融合分析引擎")
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        fusion_engine = ZiweiBaziFusionEngine()
        
        # 转换时辰为数字（模拟Web界面的处理）
        hour_map = {
            "子时": 0, "丑时": 1, "寅时": 3, "卯时": 5,
            "辰时": 7, "巳时": 9, "午时": 11, "未时": 13,
            "申时": 15, "酉时": 17, "戌时": 19, "亥时": 21
        }
        
        hour_num = hour_map.get(birth_info["hour"], 11)
        print(f"🕐 时辰转换: {birth_info['hour']} -> {hour_num}点")
        
        # 调用融合分析
        fusion_result = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=hour_num,
            gender=birth_info["gender"]
        )
        
        if fusion_result.get("success"):
            print("✅ 融合分析成功")
            
            # 检查紫薇数据完整性
            ziwei_analysis = fusion_result.get("ziwei_analysis", {})
            palaces = ziwei_analysis.get("palaces", {})
            
            print(f"\n📊 紫薇斗数数据检查:")
            print(f"  宫位总数: {len(palaces)}")
            print(f"  主要宫位:")
            
            important_palaces = ["命宫", "财帛宫", "夫妻宫", "事业宫", "健康宫"]
            for palace in important_palaces:
                if palace in palaces:
                    stars = palaces[palace].get("主星", [])
                    print(f"    {palace}: {stars}")
                else:
                    print(f"    {palace}: ❌ 缺失")
            
            # 检查八字数据完整性
            bazi_analysis = fusion_result.get("bazi_analysis", {})
            bazi_info = bazi_analysis.get("bazi_info", {})
            
            print(f"\n🔮 八字数据检查:")
            print(f"  八字: {bazi_info.get('chinese_date', '❌ 缺失')}")
            print(f"  年柱: {bazi_info.get('year_pillar', '❌ 缺失')}")
            print(f"  月柱: {bazi_info.get('month_pillar', '❌ 缺失')}")
            print(f"  日柱: {bazi_info.get('day_pillar', '❌ 缺失')}")
            print(f"  时柱: {bazi_info.get('hour_pillar', '❌ 缺失')}")
            
            return fusion_result
        else:
            print(f"❌ 融合分析失败: {fusion_result.get('error', '')}")
            return None
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def debug_analysis_generation(fusion_result):
    """调试分析生成过程"""
    print(f"\n2️⃣ 调试分析生成过程")
    print("=" * 40)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("debug_calc")
        
        # 模拟生辰信息
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1", 
            "hour": "午时",
            "gender": "男"
        }
        
        print(f"📅 传递给分析的生辰信息: {birth_info}")
        
        # 执行分析
        analysis_result = await calculator_agent._analyze_single_angle(
            "命宫分析", "personality_destiny", "性格命运核心特征",
            fusion_result, birth_info, "紫薇+八字融合分析"
        )
        
        if analysis_result:
            print(f"✅ 分析生成成功，字数: {len(analysis_result)}")
            
            # 检查分析内容中的问题
            problematic_phrases = [
                "信息有限",
                "需要确认",
                "是否还有其他",
                "若您能补充",
                "由于信息不完整"
            ]
            
            found_problems = []
            for phrase in problematic_phrases:
                if phrase in analysis_result:
                    found_problems.append(phrase)
            
            if found_problems:
                print(f"⚠️ 发现问题短语: {found_problems}")
            else:
                print("✅ 分析内容正常，没有信息不足的提示")
            
            # 显示分析内容的开头
            print(f"\n📝 分析内容开头:")
            print("-" * 50)
            print(analysis_result[:300] + "..." if len(analysis_result) > 300 else analysis_result)
            print("-" * 50)
            
            return True
        else:
            print("❌ 分析生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 分析生成调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_prompt_content(fusion_result):
    """调试提示词内容"""
    print(f"\n3️⃣ 调试提示词内容")
    print("=" * 40)
    
    try:
        from core.analysis.data_processor import DataProcessor
        from core.analysis.prompt_builder import PromptBuilder
        
        # 提取分析数据
        processor = DataProcessor()
        analysis_data = processor.extract_analysis_data(fusion_result, "personality_destiny")
        
        # 构建提示词
        prompt_builder = PromptBuilder()
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        prompt = prompt_builder.build_analysis_prompt(analysis_data, birth_info, "personality_destiny")
        
        print(f"📝 提示词长度: {len(prompt)}字符")
        
        # 检查提示词中的数据完整性
        print(f"\n📊 提示词数据检查:")
        
        # 检查紫薇数据
        if "命宫" in prompt and "财帛宫" in prompt:
            print("✅ 紫薇斗数数据完整")
        else:
            print("❌ 紫薇斗数数据不完整")
        
        # 检查八字数据
        if "戊辰 丁巳 丁亥 丙午" in prompt:
            print("✅ 八字数据正确")
        elif "八字" in prompt:
            print("⚠️ 八字数据存在但可能不正确")
        else:
            print("❌ 八字数据缺失")
        
        # 显示提示词的关键部分
        lines = prompt.split('\n')
        print(f"\n📋 提示词关键部分:")
        for i, line in enumerate(lines):
            if "排盘结果" in line:
                print(f"第{i+1}行: {line}")
                # 显示后续几行
                for j in range(1, 10):
                    if i+j < len(lines) and lines[i+j].strip():
                        print(f"第{i+j+1}行: {lines[i+j]}")
                    if i+j < len(lines) and lines[i+j].startswith('【'):
                        break
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 Web界面数据流完整调试")
    print("=" * 70)
    
    # 1. 调试数据获取
    fusion_result = await debug_web_data_flow()
    
    if fusion_result:
        # 2. 调试分析生成
        analysis_success = await debug_analysis_generation(fusion_result)
        
        # 3. 调试提示词内容
        prompt_success = await debug_prompt_content(fusion_result)
        
        print("\n" + "=" * 70)
        print("🎯 调试结果总结:")
        
        if analysis_success and prompt_success:
            print("✅ 数据流正常，问题可能在其他地方")
        else:
            print("❌ 发现数据流问题")
        
        print("\n💡 可能的问题原因:")
        print("1. Web界面传递的数据格式与分析系统期望的不一致")
        print("2. 时辰转换过程中出现问题")
        print("3. 分析系统对数据完整性的判断过于严格")
        print("4. LLM生成时使用了错误的提示词模板")
    else:
        print("\n❌ 融合分析阶段就失败了，需要检查算法")

if __name__ == "__main__":
    asyncio.run(main())
