#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试 - 避免Unicode问题
"""

import sys
import os
sys.path.append('.')

def test_algorithm_modules():
    """测试算法模块"""
    print("1. 算法模块测试")
    print("-" * 40)
    
    results = {}
    
    # 测试紫薇算法
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator, IZTRO_AVAILABLE
        if IZTRO_AVAILABLE:
            calc = RealZiweiCalculator()
            result = calc.calculate_chart(1988, 6, 1, 12, "男")
            success = "error" not in result
            print(f"紫薇算法: {'成功' if success else '失败'}")
            results['ziwei_algo'] = success
        else:
            print("紫薇算法: 模块不可用")
            results['ziwei_algo'] = False
    except Exception as e:
        print(f"紫薇算法: 异常 - {e}")
        results['ziwei_algo'] = False
    
    # 测试八字算法
    try:
        from algorithms.yxf_yixue_py.yxf_yixue.bazi import BaziApi
        import datetime
        api = BaziApi()
        dt = datetime.datetime(1988, 6, 1, 12, 0)
        result = api.paipan(dt, "男")
        success = "干支" in result
        print(f"八字算法: {'成功' if success else '失败'}")
        results['bazi_algo'] = success
    except Exception as e:
        print(f"八字算法: 异常 - {e}")
        results['bazi_algo'] = False
    
    # 测试六爻算法
    try:
        from algorithms.yxf_yixue_py.yxf_yixue.liuyao import LiuyaoApi
        api = LiuyaoApi()
        result = api.paipan_by_time(2024, 6, 19, 14, 30)
        success = result.get("success", False)
        print(f"六爻算法: {'成功' if success else '失败'}")
        results['liuyao_algo'] = success
    except Exception as e:
        print(f"六爻算法: 异常 - {e}")
        results['liuyao_algo'] = False
    
    return results

def test_humanized_tools():
    """测试人性化工具"""
    print("\n2. 人性化工具测试")
    print("-" * 40)
    
    results = {}
    
    # 测试紫薇工具
    try:
        from core.tools.humanized_ziwei_tool import HumanizedZiweiTool
        tool = HumanizedZiweiTool()
        intent = {
            'intent': 'ziwei',
            'entities': {
                'birth_year': '1988', 'birth_month': '6', 'birth_day': '1',
                'birth_hour': '午时', 'gender': '男'
            }
        }
        result = tool.execute(intent, {})
        success = result.get('success', False)
        print(f"紫薇工具: {'成功' if success else '失败'}")
        results['ziwei_tool'] = success
    except Exception as e:
        print(f"紫薇工具: 异常 - {e}")
        results['ziwei_tool'] = False
    
    # 测试八字工具
    try:
        from core.tools.humanized_bazi_tool import HumanizedBaziTool
        tool = HumanizedBaziTool()
        intent = {
            'intent': 'bazi',
            'original_message': '我1988年6月1日午时出生，男，想看八字算命',
            'entities': {
                'birth_year': '1988', 'birth_month': '6', 'birth_day': '1',
                'birth_hour': '午时', 'gender': '男'
            }
        }
        result = tool.execute(intent, {})
        success = result.get('success', False)
        print(f"八字工具: {'成功' if success else '失败'}")
        if not success:
            print(f"  错误: {result.get('message', '未知')}")
        results['bazi_tool'] = success
    except Exception as e:
        print(f"八字工具: 异常 - {e}")
        results['bazi_tool'] = False
    
    # 测试六爻工具
    try:
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool
        tool = HumanizedLiuyaoTool()
        intent = {
            'intent': 'liuyao',
            'original_message': '我想用六爻占卜我的感情运势',
            'entities': {'question': '我的感情运势如何？'}
        }
        result = tool.execute(intent, {})
        success = result.get('success', False)
        print(f"六爻工具: {'成功' if success else '失败'}")
        if not success:
            print(f"  错误: {result.get('message', '未知')}")
        results['liuyao_tool'] = success
    except Exception as e:
        print(f"六爻工具: 异常 - {e}")
        results['liuyao_tool'] = False
    
    return results

def test_tool_selector():
    """测试工具选择器"""
    print("\n3. 工具选择器测试")
    print("-" * 40)
    
    results = {}
    
    try:
        from core.tools.tool_selector import ToolSelector
        selector = ToolSelector()
        
        test_cases = [
            ('ziwei', {
                'intent': 'ziwei', 'confidence': 0.9,
                'entities': {
                    'birth_year': '1988', 'birth_month': '6', 'birth_day': '1',
                    'birth_hour': '午时', 'gender': '男'
                }
            }),
            ('bazi', {
                'intent': 'bazi', 'confidence': 0.9,
                'original_message': '我1988年6月1日午时出生，男，想看八字算命',
                'entities': {
                    'birth_year': '1988', 'birth_month': '6', 'birth_day': '1',
                    'birth_hour': '午时', 'gender': '男'
                }
            }),
            ('liuyao', {
                'intent': 'liuyao', 'confidence': 0.9,
                'original_message': '我想用六爻占卜我的感情运势',
                'entities': {'question': '我的感情运势如何？'}
            })
        ]
        
        for test_name, intent_result in test_cases:
            tool_result = selector.select_tool(intent_result, {})
            success = tool_result.get('success', False)
            print(f"{test_name}工具选择: {'成功' if success else '失败'}")
            results[f'{test_name}_selector'] = success
        
    except Exception as e:
        print(f"工具选择器: 异常 - {e}")
        results = {'ziwei_selector': False, 'bazi_selector': False, 'liuyao_selector': False}
    
    return results

def test_intent_recognition():
    """测试意图识别"""
    print("\n4. 意图识别测试")
    print("-" * 40)
    
    results = {}
    
    try:
        from core.nlu.llm_client import LLMClient
        client = LLMClient()
        
        test_cases = [
            ("我想看紫薇斗数命盘", "ziwei"),
            ("我想看八字算命", "bazi"),
            ("我想用六爻占卜", "liuyao"),
            ("请帮我分析紫薇斗数", "ziwei"),
            ("八字排盘", "bazi"),
            ("六爻算卦", "liuyao")
        ]
        
        correct_count = 0
        total_count = len(test_cases)
        
        for message, expected_intent in test_cases:
            try:
                result = client.intent_recognition(message)
                actual_intent = result.get('intent', 'unknown')
                confidence = result.get('confidence', 0)
                
                if actual_intent == expected_intent:
                    correct_count += 1
                    print(f"'{message}' -> {actual_intent} (置信度: {confidence:.2f}) [正确]")
                else:
                    print(f"'{message}' -> {actual_intent} (期望: {expected_intent}) [错误]")
            except Exception as e:
                print(f"'{message}' -> 异常: {e}")
        
        accuracy = correct_count / total_count
        print(f"\n意图识别准确率: {correct_count}/{total_count} ({accuracy:.1%})")
        results['intent_accuracy'] = accuracy >= 0.8
        
    except Exception as e:
        print(f"意图识别: 异常 - {e}")
        results['intent_accuracy'] = False
    
    return results

def test_humanized_engine():
    """测试人性化引擎"""
    print("\n5. 人性化引擎测试")
    print("-" * 40)
    
    results = {}
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        engine = HumanizedFortuneEngine()
        
        test_cases = [
            ("我想看紫薇斗数命盘，1988年6月1日午时男", "ziwei"),
            ("我1988年6月1日午时出生，男，想看八字算命", "bazi"),
            ("我想用六爻占卜我的感情运势", "liuyao")
        ]
        
        for message, expected_type in test_cases:
            try:
                print(f"\n测试: {message}")
                responses = engine.process_user_message(message, f'test_{expected_type}')
                
                if len(responses) >= 5:
                    print(f"  {expected_type}引擎: 成功 (响应数: {len(responses)})")
                    
                    # 检查响应类型分布
                    response_types = {}
                    for response in responses:
                        response_type = response.get('type', 'unknown')
                        response_types[response_type] = response_types.get(response_type, 0) + 1
                    
                    print(f"  响应类型: {list(response_types.keys())}")
                    results[f'{expected_type}_engine'] = True
                else:
                    print(f"  {expected_type}引擎: 失败 (响应数: {len(responses)})")
                    results[f'{expected_type}_engine'] = False
                    
            except Exception as e:
                print(f"  {expected_type}引擎: 异常 - {e}")
                results[f'{expected_type}_engine'] = False
        
    except Exception as e:
        print(f"人性化引擎: 异常 - {e}")
        results = {'ziwei_engine': False, 'bazi_engine': False, 'liuyao_engine': False}
    
    return results

def main():
    """主测试函数"""
    print("完整系统测试")
    print("=" * 60)
    print("目标: 验证所有功能是否正常工作")
    print("=" * 60)
    
    # 执行所有测试
    all_results = {}
    
    # 1. 算法模块测试
    all_results.update(test_algorithm_modules())
    
    # 2. 人性化工具测试
    all_results.update(test_humanized_tools())
    
    # 3. 工具选择器测试
    all_results.update(test_tool_selector())
    
    # 4. 意图识别测试
    all_results.update(test_intent_recognition())
    
    # 5. 人性化引擎测试
    all_results.update(test_humanized_engine())
    
    # 汇总结果
    print(f"\n完整系统测试结果")
    print("=" * 60)
    
    # 按类别分组显示结果
    categories = {
        "算法层": ['ziwei_algo', 'bazi_algo', 'liuyao_algo'],
        "工具层": ['ziwei_tool', 'bazi_tool', 'liuyao_tool'],
        "选择器层": ['ziwei_selector', 'bazi_selector', 'liuyao_selector'],
        "引擎层": ['ziwei_engine', 'bazi_engine', 'liuyao_engine'],
        "系统层": ['intent_accuracy']
    }
    
    all_passed = True
    category_results = {}
    
    for category, keys in categories.items():
        passed_count = sum(1 for key in keys if all_results.get(key, False))
        total_count = len(keys)
        category_results[category] = (passed_count, total_count)
        
        print(f"\n{category}: {passed_count}/{total_count}")
        for key in keys:
            status = "通过" if all_results.get(key, False) else "失败"
            print(f"  {key}: {status}")
        
        if passed_count < total_count:
            all_passed = False
    
    # 总体评估
    print("\n" + "=" * 60)
    if all_passed:
        print("系统测试全部通过！")
        print("\n系统状态:")
        print("  - 所有核心功能正常工作")
        print("  - 功能间无相互干扰")
        print("  - 人性化交互体验完整")
        print("  - 只使用真实算法，无备用机制")
        print("\n系统已准备好进入下一阶段！")
    else:
        print("系统测试发现问题")
        print("\n需要修复的问题:")
        for category, (passed, total) in category_results.items():
            if passed < total:
                print(f"  - {category}: {passed}/{total} 通过")
        print("\n建议先修复这些问题再继续下一阶段")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    print(f"\n测试结果: {'全部通过' if success else '存在问题'}")
    sys.exit(0 if success else 1)
