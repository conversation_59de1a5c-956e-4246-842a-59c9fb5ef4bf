#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段3.2测试：紫薇工具迁移和Web界面集成
"""

import sys
import os
sys.path.append('.')

def test_new_web_interface():
    """测试新Web界面组件"""
    print("🌐 测试新Web界面组件")
    print("-" * 50)
    
    try:
        # 检查新Web界面文件
        web_file = "web_demo/new_prompt_web.py"
        if not os.path.exists(web_file):
            print(f"❌ 新Web界面文件不存在: {web_file}")
            return False
        
        print("✅ 新Web界面文件存在")
        
        # 测试组件导入
        from core.chat.session_manager import SessionManager
        from core.nlu.llm_client import LLMClient
        from core.tools.tool_selector import ToolSelector
        
        print("✅ 新架构组件导入成功")
        
        # 测试组件初始化
        session_manager = SessionManager()
        llm_client = LLMClient()
        tool_selector = ToolSelector()
        
        print("✅ 新架构组件初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 新Web界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_integration_flow():
    """测试Web界面集成流程"""
    print("\n🔄 测试Web界面集成流程")
    print("-" * 50)
    
    try:
        from core.chat.session_manager import SessionManager
        from core.nlu.llm_client import LLMClient
        from core.tools.tool_selector import ToolSelector
        from datetime import datetime
        
        # 初始化组件
        session_manager = SessionManager()
        llm_client = LLMClient()
        tool_selector = ToolSelector()
        
        session_id = "web_integration_test"
        
        # 模拟Web界面处理流程
        def simulate_web_process(user_message):
            """模拟Web界面处理用户消息"""
            print(f"  用户输入: {user_message}")
            
            # 1. 获取会话上下文
            context = session_manager.get_conversation_context(session_id)
            
            # 2. 智能意图识别
            intent_result = llm_client.intent_recognition(user_message, context)
            
            if not intent_result or intent_result.get("intent") == "error":
                return {"success": False, "message": "意图识别失败"}
            
            intent = intent_result["intent"]
            confidence = intent_result["confidence"]
            entities = intent_result.get("entities", {})
            
            print(f"  识别意图: {intent} (置信度: {confidence:.2f})")
            
            # 3. 工具选择和执行
            tool_result = tool_selector.select_tool(intent_result, context)
            
            if not tool_result.get("success"):
                return {"success": False, "message": f"工具执行失败: {tool_result.get('error')}"}
            
            # 4. 处理结果
            result_data = tool_result.get("result", {})
            result_type = result_data.get("type", "unknown")
            
            # 5. 更新会话状态
            message_record = {
                "user_message": user_message,
                "intent": intent_result,
                "tool_result": tool_result,
                "timestamp": datetime.now().isoformat()
            }
            
            context_updates = {}
            if entities:
                birth_info = {}
                for key, value in entities.items():
                    if key.startswith("birth_") and value:
                        birth_info[key.replace("birth_", "")] = value
                    elif key == "gender" and value:
                        birth_info["gender"] = value
                
                if birth_info:
                    context_updates["birth_info"] = birth_info
            
            session_manager.update_session(session_id, context_updates, message_record)
            
            # 6. 格式化响应
            response = format_web_response(result_data, intent_result)
            
            return {
                "success": True,
                "message": response,
                "intent": intent,
                "confidence": confidence,
                "result_type": result_type
            }
        
        def format_web_response(result_data, intent_result):
            """格式化Web响应"""
            result_type = result_data.get("type", "unknown")
            
            if result_type == "chat_response":
                return result_data.get("message", "您好！")
            elif result_type == "entity_collection":
                return result_data.get("message", "请提供更多信息。")
            elif result_type == "ziwei_analysis":
                calc_result = result_data.get("calculation_result", {})
                if "error" in calc_result:
                    return f"❌ 紫薇斗数计算失败: {calc_result['error']}"
                else:
                    palaces_count = len(calc_result.get("palaces", {}))
                    return f"🔮 紫薇斗数命盘分析完成，包含{palaces_count}个宫位"
            elif result_type == "error":
                return f"❌ {result_data.get('message', '分析出现问题')}"
            else:
                return result_data.get("message", "分析完成。")
        
        # 测试对话流程
        test_messages = [
            "你好",
            "我想算命",
            "我1988年6月1日午时出生，男，想看紫薇斗数"
        ]
        
        success_count = 0
        for i, message in enumerate(test_messages, 1):
            print(f"\n步骤 {i}:")
            result = simulate_web_process(message)
            
            if result["success"]:
                print(f"  ✅ 处理成功: {result['result_type']}")
                print(f"  响应: {result['message'][:100]}...")
                success_count += 1
            else:
                print(f"  ❌ 处理失败: {result['message']}")
        
        print(f"\nWeb集成流程测试结果: {success_count}/{len(test_messages)} 成功")
        
        # 检查最终会话状态
        final_context = session_manager.get_conversation_context(session_id)
        print(f"最终会话状态:")
        print(f"  总消息数: {final_context['total_messages']}")
        if final_context.get('birth_info'):
            print(f"  出生信息: {final_context['birth_info']}")
        
        return success_count >= len(test_messages) * 0.8  # 80%成功率
        
    except Exception as e:
        print(f"❌ Web集成流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_old_vs_new_comparison():
    """对比旧Web界面和新Web界面"""
    print("\n⚖️ 对比旧Web界面和新Web界面")
    print("-" * 50)
    
    try:
        # 检查文件存在性
        old_web = "web_demo/prompt_web.py"
        new_web = "web_demo/new_prompt_web.py"
        
        old_exists = os.path.exists(old_web)
        new_exists = os.path.exists(new_web)
        
        print(f"旧Web界面 ({old_web}): {'✅ 存在' if old_exists else '❌ 不存在'}")
        print(f"新Web界面 ({new_web}): {'✅ 存在' if new_exists else '❌ 不存在'}")
        
        if old_exists and new_exists:
            # 比较文件大小
            old_size = os.path.getsize(old_web)
            new_size = os.path.getsize(new_web)
            
            print(f"\n文件大小对比:")
            print(f"  旧界面: {old_size} 字节")
            print(f"  新界面: {new_size} 字节")
            
            # 功能对比
            print(f"\n功能对比:")
            print(f"  旧界面特点:")
            print(f"    - 使用旧的FortuneEngine")
            print(f"    - 依赖外部API调用")
            print(f"    - 多角度分析合并")
            print(f"    - 图片生成功能")
            
            print(f"  新界面特点:")
            print(f"    - 使用新架构组件")
            print(f"    - 智能语义理解")
            print(f"    - 自动工具选择")
            print(f"    - 多轮对话支持")
            print(f"    - 上下文记忆")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        return False

def test_web_startup():
    """测试Web界面启动状态"""
    print("\n🚀 测试Web界面启动状态")
    print("-" * 50)
    
    try:
        import subprocess
        import time
        
        # 检查端口占用情况
        print("检查Web服务端口状态...")
        
        # 检查8501端口（旧界面）
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result_8501 = sock.connect_ex(('localhost', 8501))
            sock.close()
            
            if result_8501 == 0:
                print("✅ 端口8501: 旧Web界面正在运行")
            else:
                print("⚠️ 端口8501: 未运行")
        except:
            print("⚠️ 端口8501: 检查失败")
        
        # 检查8502端口（新界面）
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result_8502 = sock.connect_ex(('localhost', 8502))
            sock.close()
            
            if result_8502 == 0:
                print("✅ 端口8502: 新Web界面正在运行")
            else:
                print("⚠️ 端口8502: 未运行")
        except:
            print("⚠️ 端口8502: 检查失败")
        
        print("\n访问地址:")
        print("  旧界面: http://localhost:8501")
        print("  新界面: http://localhost:8502")
        
        return True
        
    except Exception as e:
        print(f"❌ Web启动状态测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 阶段3.2：紫薇工具迁移和Web界面集成测试")
    print("=" * 80)
    
    # 测试结果
    results = []
    
    # 1. 新Web界面组件测试
    results.append(("新Web界面组件", test_new_web_interface()))
    
    # 2. Web界面集成流程测试
    results.append(("Web界面集成流程", test_web_integration_flow()))
    
    # 3. 新旧界面对比
    results.append(("新旧界面对比", test_old_vs_new_comparison()))
    
    # 4. Web界面启动状态
    results.append(("Web界面启动状态", test_web_startup()))
    
    # 汇总结果
    print("\n📊 阶段3.2测试结果汇总")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 阶段3.2测试全部通过！紫薇工具迁移和Web界面集成成功！")
        print("\n🎯 完成功能:")
        print("  ✅ 新架构Web界面创建")
        print("  ✅ 智能对话系统集成")
        print("  ✅ 多轮对话支持")
        print("  ✅ 上下文记忆功能")
        print("  ✅ 自动工具选择")
        print("  ✅ 端到端Web流程")
        print("\n🌐 Web界面访问:")
        print("  新架构界面: http://localhost:8502")
        print("  旧版界面: http://localhost:8501")
        print("\n📋 下一步：继续阶段3.3 - 八字工具迁移")
    else:
        print("💥 部分测试失败，需要修复问题后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
