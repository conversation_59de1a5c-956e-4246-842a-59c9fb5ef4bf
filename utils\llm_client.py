#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM客户端 - 统一的大语言模型调用接口
"""

import json
import requests
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class LLMClient:
    """LLM客户端 - 支持多种模型和提供商"""
    
    def __init__(self, api_key: str, base_url: str, model_name: str, 
                 default_timeout: int = 600, max_tokens: int = 12000):
        """
        初始化LLM客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
            default_timeout: 默认超时时间（秒）
            max_tokens: 最大token数
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
        self.default_timeout = default_timeout
        self.max_tokens = max_tokens
        
        # 请求统计
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0
        }
        
        logger.info(f"LLM客户端初始化完成 - 模型: {model_name}, 超时: {default_timeout}秒")
    
    def chat(self, prompt: str, system_prompt: Optional[str] = None, 
             temperature: float = 0.7, timeout: Optional[int] = None) -> str:
        """
        发送聊天请求
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            temperature: 温度参数
            timeout: 超时时间
            
        Returns:
            模型响应内容
        """
        messages = []
        
        # 添加系统提示词
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        
        # 添加用户提示词
        messages.append({
            "role": "user",
            "content": prompt
        })
        
        return self._call_api(messages, temperature, timeout)
    
    def chat_with_history(self, messages: List[Dict[str, str]], 
                         temperature: float = 0.7, timeout: Optional[int] = None) -> str:
        """
        带历史记录的聊天请求
        
        Args:
            messages: 消息历史列表
            temperature: 温度参数
            timeout: 超时时间
            
        Returns:
            模型响应内容
        """
        return self._call_api(messages, temperature, timeout)
    
    def _call_api(self, messages: List[Dict[str, str]], 
                  temperature: float = 0.7, timeout: Optional[int] = None) -> str:
        """
        调用API的核心方法
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            timeout: 超时时间
            
        Returns:
            模型响应内容
        """
        self.stats["total_requests"] += 1
        
        try:
            # 准备请求数据
            data = {
                "model": self.model_name,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": self.max_tokens,
                "stream": False
            }
            
            # 准备请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求
            timeout = timeout or self.default_timeout
            url = f"{self.base_url}/chat/completions"
            
            logger.debug(f"发送LLM请求 - URL: {url}, 消息数: {len(messages)}")
            
            response = requests.post(
                url, 
                headers=headers, 
                json=data, 
                timeout=timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # 解析响应
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                
                # 更新统计
                self.stats["successful_requests"] += 1
                if "usage" in result:
                    self.stats["total_tokens"] += result["usage"].get("total_tokens", 0)
                
                logger.debug(f"LLM请求成功 - 响应长度: {len(content)}")
                return content
            else:
                raise Exception("API返回格式异常")
                
        except requests.exceptions.Timeout:
            self.stats["failed_requests"] += 1
            logger.error(f"LLM请求超时 - 超时时间: {timeout}秒")
            return "请求超时，请稍后重试"
            
        except requests.exceptions.RequestException as e:
            self.stats["failed_requests"] += 1
            logger.error(f"LLM请求失败 - 网络错误: {e}")
            return f"网络请求失败: {str(e)}"
            
        except Exception as e:
            self.stats["failed_requests"] += 1
            logger.error(f"LLM请求失败 - 未知错误: {e}")
            return f"请求失败: {str(e)}"
    
    def parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """
        解析JSON格式的响应
        
        Args:
            response: LLM响应内容
            
        Returns:
            解析后的字典或None
        """
        try:
            # 清理响应内容
            cleaned_response = response.strip()
            
            # 移除可能的markdown代码块标记
            if "```json" in cleaned_response:
                start = cleaned_response.find("```json") + 7
                end = cleaned_response.find("```", start)
                if end != -1:
                    cleaned_response = cleaned_response[start:end].strip()
            elif "```" in cleaned_response:
                start = cleaned_response.find("```") + 3
                end = cleaned_response.find("```", start)
                if end != -1:
                    cleaned_response = cleaned_response[start:end].strip()
            
            # 查找JSON部分
            if "{" in cleaned_response and "}" in cleaned_response:
                start = cleaned_response.find("{")
                end = cleaned_response.rfind("}") + 1
                json_str = cleaned_response[start:end]
                
                return json.loads(json_str)
                
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"JSON解析失败: {e}")
            
        return None
    
    def extract_keywords(self, response: str, keywords: List[str]) -> Optional[str]:
        """
        从响应中提取关键词
        
        Args:
            response: LLM响应内容
            keywords: 候选关键词列表
            
        Returns:
            匹配的关键词或None
        """
        response_lower = response.lower().strip()
        
        # 直接匹配
        for keyword in keywords:
            if response_lower == keyword.lower():
                return keyword
        
        # 包含匹配
        for keyword in keywords:
            if keyword.lower() in response_lower:
                return keyword
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取客户端统计信息
        
        Returns:
            统计信息字典
        """
        success_rate = 0
        if self.stats["total_requests"] > 0:
            success_rate = self.stats["successful_requests"] / self.stats["total_requests"] * 100
        
        return {
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{success_rate:.2f}%",
            "total_tokens": self.stats["total_tokens"],
            "model_name": self.model_name
        }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0
        }
        logger.info("LLM客户端统计信息已重置")

class LLMClientFactory:
    """LLM客户端工厂 - 创建不同配置的客户端"""
    
    @staticmethod
    def create_siliconflow_client(api_key: str, model_name: str = "deepseek-ai/DeepSeek-V3") -> LLMClient:
        """
        创建SiliconFlow客户端
        
        Args:
            api_key: API密钥
            model_name: 模型名称
            
        Returns:
            LLM客户端实例
        """
        return LLMClient(
            api_key=api_key,
            base_url="https://api.siliconflow.cn/v1",
            model_name=model_name,
            default_timeout=600,  # 10分钟超时
            max_tokens=12000
        )
    
    @staticmethod
    def create_openai_client(api_key: str, model_name: str = "gpt-3.5-turbo") -> LLMClient:
        """
        创建OpenAI客户端
        
        Args:
            api_key: API密钥
            model_name: 模型名称
            
        Returns:
            LLM客户端实例
        """
        return LLMClient(
            api_key=api_key,
            base_url="https://api.openai.com/v1",
            model_name=model_name,
            default_timeout=300,  # 5分钟超时
            max_tokens=4000
        )
