#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析控制器
协调整个分析流程，确保数据准确性和结果质量
"""

import logging
import asyncio
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from .data_processor import DataProcessor
from .prompt_builder import PromptBuilder
from .llm_analyzer import LLMAnalyzer
from .result_validator import ResultValidator

logger = logging.getLogger(__name__)

class AnalysisController:
    """分析控制器"""

    def __init__(self):
        """初始化分析控制器"""
        self.data_processor = DataProcessor()
        self.prompt_builder = PromptBuilder()
        self.llm_analyzer = LLMAnalyzer()
        self.result_validator = ResultValidator()

        logger.info("✅ 分析控制器初始化完成")

    async def execute_single_analysis(self, raw_data: Dict[str, Any],
                                    birth_info: Dict[str, Any],
                                    analysis_type: str) -> Dict[str, Any]:
        """执行单个角度分析"""
        try:
            logger.info(f"🎯 开始执行{analysis_type}分析")

            analysis_result = {
                "success": False,
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
                "content": "",
                "validation_report": {},
                "error": None
            }

            # 1. 数据处理
            logger.info("📊 步骤1: 数据处理")
            analysis_data = self.data_processor.extract_analysis_data(raw_data, analysis_type)

            if not analysis_data:
                analysis_result["error"] = "数据提取失败"
                return analysis_result

            # 验证数据完整性
            try:
                if not self.data_processor.validate_data_integrity(analysis_data):
                    analysis_result["error"] = "数据完整性验证失败"
                    return analysis_result
            except Exception as e:
                logger.error(f"数据完整性验证异常: {e}")
                analysis_result["error"] = f"数据完整性验证异常: {str(e)}"
                return analysis_result

            # 2. 构建提示词
            logger.info("🔨 步骤2: 构建提示词")
            prompt = self.prompt_builder.build_analysis_prompt(
                analysis_data, birth_info, analysis_type
            )

            if not self.prompt_builder.validate_prompt(prompt):
                analysis_result["error"] = "提示词验证失败"
                return analysis_result

            # 3. LLM分析
            logger.info("🤖 步骤3: LLM分析")
            llm_result = await self.llm_analyzer.analyze(prompt, analysis_type)

            if not llm_result:
                analysis_result["error"] = "LLM分析失败"
                return analysis_result

            # 4. 结果验证
            logger.info("🔍 步骤4: 结果验证")
            is_valid, validation_report = self.result_validator.validate_comprehensive(
                llm_result, analysis_data, analysis_type
            )

            analysis_result["validation_report"] = validation_report

            # 无论验证结果如何，都使用LLM生成的原始内容
            analysis_result["success"] = True
            analysis_result["content"] = llm_result

            if is_valid:
                logger.info(f"✅ {analysis_type}分析成功完成，验证通过")
            else:
                logger.warning(f"⚠️ {analysis_type}分析验证失败，但仍使用原始内容")
                # 记录验证失败的详细信息，但不影响输出
                errors = validation_report.get('errors', [])
                if errors:
                    logger.warning(f"验证错误详情: {errors[:3]}")  # 只记录前3个错误

            return analysis_result

        except Exception as e:
            logger.error(f"❌ {analysis_type}分析执行异常: {e}")
            return {
                "success": False,
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
                "content": f"【{analysis_type}】分析执行异常: {str(e)}",
                "error": str(e)
            }

    async def execute_batch_analysis(self, raw_data: Dict[str, Any],
                                   birth_info: Dict[str, Any],
                                   analysis_types: list) -> Dict[str, Any]:
        """执行批量分析"""
        try:
            logger.info(f"🎯 开始执行批量分析，共{len(analysis_types)}个角度")

            batch_result = {
                "success": False,
                "timestamp": datetime.now().isoformat(),
                "total_analyses": len(analysis_types),
                "completed_analyses": 0,
                "failed_analyses": 0,
                "results": {},
                "summary": {}
            }

            # 并发执行分析（限制并发数）
            semaphore = asyncio.Semaphore(2)  # 最多同时2个分析

            async def analyze_with_semaphore(analysis_type):
                async with semaphore:
                    return await self.execute_single_analysis(raw_data, birth_info, analysis_type)

            # 创建任务
            tasks = [analyze_with_semaphore(analysis_type) for analysis_type in analysis_types]

            # 执行任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            for i, result in enumerate(results):
                analysis_type = analysis_types[i]

                if isinstance(result, Exception):
                    logger.error(f"❌ {analysis_type}分析异常: {result}")
                    batch_result["results"][analysis_type] = {
                        "success": False,
                        "error": str(result),
                        "content": self._generate_fallback_content(analysis_type, {})
                    }
                    batch_result["failed_analyses"] += 1
                else:
                    batch_result["results"][analysis_type] = result
                    if result["success"]:
                        batch_result["completed_analyses"] += 1
                    else:
                        batch_result["failed_analyses"] += 1

            # 生成摘要
            batch_result["summary"] = self._generate_batch_summary(batch_result)
            batch_result["success"] = batch_result["completed_analyses"] > 0

            logger.info(f"✅ 批量分析完成: {batch_result['completed_analyses']}/{batch_result['total_analyses']} 成功")
            return batch_result

        except Exception as e:
            logger.error(f"❌ 批量分析执行异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _generate_fallback_content(self, analysis_type: str, analysis_data: Dict[str, Any]) -> str:
        """生成备用内容"""
        try:
            type_names = {
                "personality_destiny": "命宫性格命运",
                "wealth_fortune": "财帛财富运势",
                "marriage_love": "夫妻婚姻感情",
                "health_wellness": "疾厄健康养生",
                "career_development": "官禄事业发展",
                "family_relationship": "父母家庭关系",
                "children_education": "子女教育培养",
                "social_network": "奴仆人际社交",
                "living_environment": "田宅居住环境",
                "spiritual_growth": "福德精神成长",
                "travel_migration": "迁移出行变动",
                "sibling_friendship": "兄弟手足友谊"
            }

            analysis_name = type_names.get(analysis_type, analysis_type)

            # 尝试从数据中提取基本信息
            basic_info = ""
            if analysis_data:
                ziwei_data = analysis_data.get("ziwei", {})
                palaces = ziwei_data.get("palaces", {})

                if analysis_type == "personality_destiny" and "命宫" in palaces:
                    mingong = palaces["命宫"]
                    position = mingong.get("位置", "")
                    major_stars = mingong.get("主星", [])
                    if position and major_stars:
                        basic_info = f"根据您的命宫配置（{position}宫，主星{major_stars}），"

                elif analysis_type == "wealth_fortune" and "财帛宫" in palaces:
                    caibo = palaces["财帛宫"]
                    position = caibo.get("位置", "")
                    major_stars = caibo.get("主星", [])
                    if position and major_stars:
                        basic_info = f"根据您的财帛宫配置（{position}宫，主星{major_stars}），"

            return f"""# 【{analysis_name}】分析

## 基础分析

{basic_info}您在{analysis_name}方面显示出良好的发展潜力。您的先天条件为此方面的发展奠定了坚实的基础。

## 发展趋势

从整体趋势来看，您在此方面的发展呈现稳步上升的态势。建议您把握当前的机遇，发挥自身的优势和特长。

## 实用建议

针对{analysis_name}方面，建议您：

1. **积极进取** - 把握当前的机遇，主动出击
2. **稳步发展** - 注意平衡发展，避免过于急躁
3. **发挥优势** - 充分利用自身的特长和资源
4. **持续学习** - 保持学习和成长的心态

## 注意事项

在发展过程中，需要特别注意：

1. **避免急躁** - 稳步前进，不要急于求成
2. **重视细节** - 注重质量，关注细节处理
3. **适时调整** - 根据实际情况调整策略和方向
4. **保持平衡** - 在追求发展的同时保持生活平衡

---

*注：详细的专业分析正在完善中，以上内容基于基础配置提供参考建议。*
"""

        except Exception as e:
            logger.error(f"生成{analysis_type}备用内容失败: {e}")
            return f"【{analysis_type}】分析暂时无法完成，请稍后重试。"

    def _generate_batch_summary(self, batch_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成批量分析摘要"""
        try:
            summary = {
                "success_rate": 0.0,
                "average_score": 0.0,
                "total_words": 0,
                "quality_distribution": {"优秀": 0, "良好": 0, "一般": 0, "较差": 0}
            }

            total_analyses = batch_result["total_analyses"]
            completed_analyses = batch_result["completed_analyses"]

            if total_analyses > 0:
                summary["success_rate"] = (completed_analyses / total_analyses) * 100

            # 计算平均分和质量分布
            scores = []
            total_words = 0

            for result in batch_result["results"].values():
                if result.get("success"):
                    validation_report = result.get("validation_report", {})
                    score = validation_report.get("score", 0)
                    scores.append(score)

                    content = result.get("content", "")
                    total_words += len(content)

                    # 质量分级
                    if score >= 90:
                        summary["quality_distribution"]["优秀"] += 1
                    elif score >= 80:
                        summary["quality_distribution"]["良好"] += 1
                    elif score >= 70:
                        summary["quality_distribution"]["一般"] += 1
                    else:
                        summary["quality_distribution"]["较差"] += 1

            if scores:
                summary["average_score"] = sum(scores) / len(scores)

            summary["total_words"] = total_words

            return summary

        except Exception as e:
            logger.error(f"生成批量分析摘要失败: {e}")
            return {}
