#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查合盘分析状态
"""

import sys
sys.path.append('.')

def check_compatibility_status():
    """检查合盘分析状态"""
    try:
        from backend_agent_web import get_all_cache_records
        
        print("检查当前缓存记录:")
        records = get_all_cache_records()
        print(f"总记录数: {len(records)}")
        
        compatibility_records = []
        liuyao_records = []
        normal_records = []
        
        for r in records:
            calc_type = r.get('calculation_type', 'unknown')
            if calc_type == 'compatibility':
                compatibility_records.append(r)
            elif calc_type == 'liuyao':
                liuyao_records.append(r)
            else:
                normal_records.append(r)
        
        print(f"\n分类统计:")
        print(f"  合盘分析: {len(compatibility_records)} 条")
        print(f"  六爻占卜: {len(liuyao_records)} 条")
        print(f"  命理分析: {len(normal_records)} 条")
        
        if compatibility_records:
            print(f"\n合盘分析记录详情:")
            for i, r in enumerate(compatibility_records, 1):
                print(f"  {i}. ID: {r['result_id'][:12]}...")
                print(f"     信息: {r['birth_info']}")
                print(f"     完成度: {r['completed_angles']}")
                print(f"     字数: {r['total_words']}")
                print(f"     有分析: {r['has_analysis']}")
                
                # 判断状态
                if r['completed_angles'] > 0 and r['total_words'] > 0:
                    status = "已完成"
                elif r['has_analysis']:
                    status = "进行中"
                else:
                    status = "待处理"
                print(f"     状态: {status}")
        else:
            print("\n没有找到合盘分析记录")
        
        # 测试进度监控逻辑
        print(f"\n测试进度监控逻辑:")
        active_tasks = []
        completed_tasks = []
        
        for r in records:
            calculation_type = r.get('calculation_type', 'ziwei')
            
            if calculation_type == 'compatibility':
                if r['completed_angles'] > 0 and r['total_words'] > 0:
                    completed_tasks.append(r)
                else:
                    active_tasks.append(r)
        
        print(f"  进行中的合盘任务: {len(active_tasks)}")
        print(f"  已完成的合盘任务: {len(completed_tasks)}")
        
        if active_tasks:
            print(f"  进行中的任务:")
            for task in active_tasks:
                print(f"    - {task['birth_info']}")
        
        if completed_tasks:
            print(f"  已完成的任务:")
            for task in completed_tasks:
                print(f"    - {task['birth_info']} ({task['total_words']}字)")
        
        return True
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_compatibility_status()
