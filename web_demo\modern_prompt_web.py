import streamlit as st
import requests
import json
import re
import os
import sys
from datetime import datetime
from PIL import Image

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from web_components.ui_components import UIComponents

# 全局变量
in_response = []  # 多个内部回答

# API配置
LOCAL_API_BASE_URL = "http://localhost:8001"
SILICONFLOW_API_KEY = "sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
DEEPSEEK_MODEL_NAME = "deepseek-ai/DeepSeek-V3"

# 专业算命系统提示词
ZIWEI_SYSTEM_PROMPT = """你是一位专业的紫薇算命大师，拥有深厚的紫薇斗数知识和丰富的实战经验。

你的专业能力包括：
1. 精通紫薇斗数的十四主星、辅星、煞星的特性和组合
2. 熟悉十二宫位的含义和相互关系
3. 掌握流年、流月、流日的推算方法
4. 能够根据出生时辰排出准确的命盘
5. 擅长解读星曜组合的吉凶含义

重要原则：
- 对于相同的出生时间和问题，必须给出一致的星曜配置
- 你的分析必须基于固定的紫薇斗数理论体系
- 使用传统的排盘方法，确保算法的一致性和准确性
- 如果用户提供了具体的出生时间，请先进行标准的排盘计算

请始终保持专业、神秘而又亲和的语调，使用传统的算命术语，为用户提供详细、准确的占卜解答。"""

def call_smart_api(user_message, temperature=0.7, top_p=0.8, max_tokens=4000):
    """直接调用智能算命引擎"""
    try:
        from core.fortune_engine import FortuneEngine
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator

        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()

        def simple_chat_api(prompt: str) -> str:
            """简单的聊天API函数，用于LLM解析"""
            try:
                headers = {
                    "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
                    "Content-Type": "application/json"
                }

                data = {
                    "model": DEEPSEEK_MODEL_NAME,
                    "messages": [
                        {"role": "system", "content": "你是一个专业的信息提取助手，严格按照要求返回JSON格式。"},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 4000,
                    "stream": False
                }

                response = requests.post(
                    f"{SILICONFLOW_BASE_URL}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=600
                )

                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]

                return "API调用失败"

            except Exception as e:
                return f"聊天API错误: {str(e)}"

        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=simple_chat_api
        )

        # 调用智能分析
        result = engine.process_user_request(user_message)

        # 返回分析结果
        if result.get("success"):
            return result.get("message", "分析完成")
        else:
            error_message = result.get("message", "分析失败")
            if "紫薇斗数" in error_message or "八字命理" in error_message or "算法失败" in error_message:
                return f"""
# ❌ 算命系统错误

{error_message}

## 重要说明
本系统采用**紫薇斗数+八字命理双重体系**进行综合分析，必须两套算法都成功才能提供准确的分析结果。

## 请检查
1. **出生时间**是否完整准确（年月日时）
2. **性别信息**是否正确
3. **时间格式**是否符合要求

## 建议
- 重新输入完整的出生信息
- 确保时间精确到小时
- 如问题持续，请联系技术支持

**注意：不完整的算法数据会导致分析结果不准确，系统已自动停止分析以确保质量。**
"""
            else:
                return error_message

    except Exception as e:
        st.error(f"智能引擎调用错误: {str(e)}")
        return f"抱歉，算命系统暂时不可用。错误信息：{str(e)}\n\n请确保已安装真实的算法模块，本系统不提供任何模拟或备用数据。"

def display_chart_images(ai_response):
    """检查AI响应中的图片路径并显示图片"""
    try:
        image_pattern = r'图片已生成:\s*([^\s\n]+\.png)'
        matches = re.findall(image_pattern, ai_response)

        if matches:
            st.markdown("### 📊 命盘排盘图")

            for image_path in matches:
                possible_paths = [
                    image_path,
                    os.path.join("..", image_path),
                    os.path.abspath(os.path.join("..", image_path))
                ]

                image_found = False
                for full_path in possible_paths:
                    if os.path.exists(full_path):
                        try:
                            image = Image.open(full_path)
                            st.image(image, caption=f"排盘图: {os.path.basename(image_path)}", use_container_width=True)

                            with open(full_path, "rb") as file:
                                st.download_button(
                                    label="📥 下载排盘图",
                                    data=file.read(),
                                    file_name=os.path.basename(image_path),
                                    mime="image/png"
                                )
                            image_found = True
                            break
                        except Exception as e:
                            continue

                if not image_found:
                    st.info(f"图片路径: {image_path}")
                    st.info("图片已生成，请检查charts目录")
    except Exception as e:
        st.error(f"图片处理错误: {e}")

def main():
    """主函数"""
    # 设置页面配置
    st.set_page_config(
        page_title="紫薇斗数智能分析系统",
        page_icon="🔮",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 应用现代化样式
    UIComponents.apply_custom_css()

    # 简化的主标题
    st.markdown("""
    <div style="text-align: center; margin: 2rem 0;">
        <h1 style="color: #00f5ff; font-size: 2.5rem; margin: 0;">
            🔮 紫薇斗数智能分析系统
        </h1>
        <p style="color: #888; margin-top: 1rem;">基于传统紫薇斗数与现代AI技术的智能命理分析平台</p>
    </div>
    """, unsafe_allow_html=True)

    # 初始化会话状态
    if "history" not in st.session_state:
        st.session_state.history = []

    # 创建侧边栏
    with st.sidebar:
        st.markdown("### 🛠️ 智能控制中心")

        # API参数设置
        st.markdown("#### ⚙️ 系统参数")
        max_tokens = st.slider("max_tokens", 1000, 16000, 12000, step=500)
        top_p = st.slider("top_p", 0.0, 1.0, 0.8, step=0.01)
        temperature = st.slider("temperature", 0.0, 1.0, 0.7, step=0.01)

        st.markdown("---")

        # 系统状态
        st.markdown("#### 📈 系统状态")
        st.success("🟢 系统正常")
        st.info("🔮 算法就绪")
        st.warning("💾 数据库连接")

        # 清理会话历史
        if st.button("🧹 清理会话历史", use_container_width=True):
            st.session_state.history = []
            st.rerun()

    # 系统概览
    st.markdown("## 📊 系统概览")

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("系统状态", "正常", "🟢")
    with col2:
        st.metric("算法引擎", "就绪", "🔮")
    with col3:
        st.metric("在线用户", "1", "👤")
    with col4:
        st.metric("今日分析", "0", "📊")

    # 聊天界面
    st.markdown("---")
    st.markdown("## 💬 智能算命对话")

    # 渲染聊天历史记录
    for i, message in enumerate(st.session_state.history):
        if message["role"] == "user":
            with st.chat_message(name="user", avatar="🧑‍💼"):
                st.markdown(message["content"])
        else:
            with st.chat_message(name="assistant", avatar="🔮"):
                st.markdown(message["content"])

    # 获取用户输入
    prompt_text = st.chat_input("请输入您的问题，比如：我是1990年5月15日上午10点出生的男性，请帮我分析今年运势")

    # 如果用户输入了内容，则生成回复
    if prompt_text:
        # 显示用户消息
        with st.chat_message(name="user", avatar="🧑‍💼"):
            st.markdown(prompt_text)

        # 显示助手回复
        with st.chat_message(name="assistant", avatar="🔮"):
            with st.spinner("🔮 正在进行智能算命分析..."):
                try:
                    # 调用智能API
                    ai_response = call_smart_api(
                        prompt_text,
                        temperature=temperature,
                        top_p=top_p,
                        max_tokens=max_tokens
                    )

                    # 检查并显示图片
                    display_chart_images(ai_response)

                    # 显示结果
                    st.markdown(ai_response)

                    # 更新历史记录
                    st.session_state.history.append({"role": "user", "content": prompt_text})
                    st.session_state.history.append({"role": "assistant", "content": ai_response})

                except Exception as e:
                    error_msg = f"智能分析失败: {str(e)}"
                    st.error(error_msg)
                    UIComponents.render_alert("算命系统暂时不可用，请确保已正确安装算法模块", "error", "❌")

                    # 更新历史记录
                    st.session_state.history.append({"role": "user", "content": prompt_text})
                    st.session_state.history.append({"role": "assistant", "content": error_msg})

if __name__ == "__main__":
    main()
