#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证五行计算的准确性
"""

def manual_wuxing_analysis():
    """手动分析1988年6月1日11时的五行"""
    print("🔍 手动验证五行计算")
    print("=" * 50)
    
    # 正确的八字：戊辰 丁巳 丁亥 丙午
    bazi = "戊辰 丁巳 丁亥 丙午"
    print(f"八字: {bazi}")
    
    # 天干地支五行对应表
    tiangan_wuxing = {
        '甲': '木', '乙': '木',
        '丙': '火', '丁': '火', 
        '戊': '土', '己': '土',
        '庚': '金', '辛': '金',
        '壬': '水', '癸': '水'
    }
    
    dizhi_wuxing = {
        '子': '水', '丑': '土', '寅': '木', '卯': '木',
        '辰': '土', '巳': '火', '午': '火', '未': '土',
        '申': '金', '酉': '金', '戌': '土', '亥': '水'
    }
    
    # 地支藏干表（简化版）
    dizhi_canggan = {
        '辰': ['戊', '乙', '癸'],  # 土、木、水
        '巳': ['丙', '戊', '庚'],  # 火、土、金
        '亥': ['壬', '甲'],        # 水、木
        '午': ['丁', '己']         # 火、土
    }
    
    print("\n📊 天干五行分析:")
    tiangan_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
    
    # 分析天干
    tiangang_list = ['戊', '丁', '丁', '丙']
    for i, tg in enumerate(tiangang_list):
        wuxing = tiangan_wuxing[tg]
        tiangan_count[wuxing] += 1
        pillar_names = ['年柱', '月柱', '日柱', '时柱']
        print(f"  {pillar_names[i]} {tg}: {wuxing}")
    
    print(f"\n天干五行统计: {tiangan_count}")
    
    print("\n📊 地支五行分析:")
    dizhi_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
    
    # 分析地支本气
    dizhi_list = ['辰', '巳', '亥', '午']
    for i, dz in enumerate(dizhi_list):
        wuxing = dizhi_wuxing[dz]
        dizhi_count[wuxing] += 1
        pillar_names = ['年柱', '月柱', '日柱', '时柱']
        print(f"  {pillar_names[i]} {dz}: {wuxing}")
    
    print(f"\n地支本气统计: {dizhi_count}")
    
    print("\n📊 地支藏干分析:")
    canggan_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
    
    for i, dz in enumerate(dizhi_list):
        if dz in dizhi_canggan:
            canggan_list = dizhi_canggan[dz]
            pillar_names = ['年柱', '月柱', '日柱', '时柱']
            print(f"  {pillar_names[i]} {dz}藏干: {canggan_list}")
            
            # 藏干通常按权重计算，这里简化处理
            for j, cg in enumerate(canggan_list):
                if cg in tiangan_wuxing:
                    wuxing = tiangan_wuxing[cg]
                    # 主气权重1.0，中气0.7，余气0.3
                    weight = [1.0, 0.7, 0.3][j] if j < 3 else 0.3
                    canggan_count[wuxing] += weight
    
    print(f"\n地支藏干统计: {canggan_count}")
    
    # 总计五行
    print("\n📊 五行总计:")
    total_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
    
    for element in total_count:
        total_count[element] = tiangan_count[element] + dizhi_count[element] + canggan_count[element]
        print(f"  {element}: {total_count[element]:.1f} (天干{tiangan_count[element]} + 地支{dizhi_count[element]} + 藏干{canggan_count[element]:.1f})")
    
    return total_count

def test_system_wuxing():
    """测试系统的五行计算"""
    print("\n🧪 测试系统五行计算")
    print("=" * 30)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        calc = RealBaziCalculator()
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        if result["success"]:
            print("✅ 八字计算成功")
            
            # 检查是否有五行数据
            raw_result = result.get("raw_result", {})
            wuxing_data = raw_result.get("五行", {})
            
            if wuxing_data:
                print("系统五行计算结果:")
                for element, info in wuxing_data.items():
                    if isinstance(info, dict):
                        count = info.get("五行数", "")
                        strength = info.get("旺衰", "")
                        print(f"  {element}: {count} ({strength})")
                return wuxing_data
            else:
                print("⚠️ 系统未返回五行数据")
                return None
        else:
            print(f"❌ 八字计算失败: {result['error']}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def compare_wuxing_results():
    """对比手动计算和系统计算的五行结果"""
    print("\n🔍 对比五行计算结果")
    print("=" * 40)
    
    # 手动计算
    manual_result = manual_wuxing_analysis()
    
    # 系统计算
    system_result = test_system_wuxing()
    
    # 从截图获取的数据
    screenshot_result = {
        '木': 1.0,
        '火': 6.0, 
        '土': 3.5,
        '金': 0.5,
        '水': 2.0
    }
    
    print("\n📊 三种结果对比:")
    print("元素  手动计算  系统计算  截图显示")
    print("-" * 35)
    
    for element in ['木', '火', '土', '金', '水']:
        manual = manual_result.get(element, 0)
        
        # 处理系统结果
        if system_result and element in system_result:
            system_info = system_result[element]
            if isinstance(system_info, dict):
                system = system_info.get("五行数", "N/A")
            else:
                system = system_info
        else:
            system = "N/A"
        
        screenshot = screenshot_result.get(element, 0)
        
        print(f"{element:2s}    {manual:6.1f}    {system:>8}    {screenshot:6.1f}")
    
    # 分析差异
    print("\n🔍 分析:")
    print("1. 手动计算基于标准藏干权重")
    print("2. 系统计算可能使用不同的藏干算法")
    print("3. 截图显示的是当前Web界面的结果")
    
    # 检查截图结果的合理性
    total_screenshot = sum(screenshot_result.values())
    print(f"\n截图五行总数: {total_screenshot}")
    
    if 12 <= total_screenshot <= 15:
        print("✅ 截图五行总数在合理范围内")
    else:
        print("⚠️ 截图五行总数可能有问题")

def main():
    """主函数"""
    print("🔍 五行计算准确性验证")
    print("=" * 60)
    
    compare_wuxing_results()
    
    print("\n" + "=" * 60)
    print("🎯 结论:")
    print("需要检查五行计算算法是否使用了正确的藏干权重")
    print("建议对比权威网站的五行计算结果")

if __name__ == "__main__":
    main()
