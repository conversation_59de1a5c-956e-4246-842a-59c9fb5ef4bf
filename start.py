#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能算命AI系统 v4.0 启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查依赖包
    required_packages = [
        'streamlit',
        'requests', 
        'pandas',
        'py-iztro',
        'python-dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r config/requirements.txt")
        return False
    
    return True

def check_config():
    """检查配置"""
    print("\n🔧 检查配置...")
    
    # 检查环境变量文件
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ .env文件不存在，将使用默认配置")
        print("💡 建议复制config/.env.example为.env并配置API密钥")
    else:
        print("✅ .env文件存在")
    
    # 检查API密钥
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("SILICONFLOW_API_KEY")
    if api_key:
        print("✅ API密钥已配置")
    else:
        print("⚠️ API密钥未配置，部分功能可能无法使用")
    
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录...")
    
    directories = [
        "data/cache",
        "data/results", 
        "data/sessions",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")

def start_app():
    """启动应用"""
    print("\n🚀 启动应用...")
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(Path.cwd())
    
    # 启动Streamlit应用
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "web/app.py",
        "--server.port=8501",
        "--server.address=0.0.0.0",
        "--browser.gatherUsageStats=false"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("🌐 应用将在 http://localhost:8501 启动")
    print("按 Ctrl+C 停止应用")
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔮 智能算命AI系统 v4.0 启动器")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败")
        return
    
    # 创建目录
    create_directories()
    
    # 启动应用
    print("\n" + "=" * 50)
    start_app()

if __name__ == "__main__":
    main()
