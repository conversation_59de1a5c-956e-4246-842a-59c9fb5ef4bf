#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成终极版专业紫薇斗数+八字融合图表
完全参考网站专业排盘样式
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, FancyBboxPatch, Circle
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_ultimate_chart():
    """生成终极版专业紫薇斗数图表"""
    print("🎨 生成终极版专业紫薇斗数+八字融合图表")
    print("=" * 60)
    
    try:
        # 获取融合数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        # 创建超大画布以容纳所有专业信息
        fig, ax = plt.subplots(1, 1, figsize=(28, 20))
        ax.set_xlim(0, 28)
        ax.set_ylim(0, 20)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 设置专业背景
        fig.patch.set_facecolor('#f8f9fa')
        
        # 1. 绘制专业标题和信息栏
        draw_professional_header(ax, result)
        
        # 2. 绘制终极版12宫格
        draw_ultimate_palaces(ax, result)
        
        # 3. 在中央添加完整的八字和分析
        draw_complete_center_info(ax, result)
        
        # 4. 添加侧边栏信息
        draw_sidebar_info(ax, result)
        
        # 保存图片
        output_file = "ultimate_chart.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                   facecolor='#f8f9fa', edgecolor='none')
        
        print(f"✅ 终极版图表生成成功: {output_file}")
        plt.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def draw_professional_header(ax, result):
    """绘制专业标题栏"""
    birth_info = result.get("birth_info", {})
    
    # 主标题背景
    header_rect = Rectangle((2, 17.5), 24, 2,
                           linewidth=2, edgecolor='#6c757d', 
                           facecolor='#e9ecef', alpha=0.9)
    ax.add_patch(header_rect)
    
    # 主标题
    ax.text(14, 18.8, "紫薇斗数命盘", ha='center', va='center', 
           fontsize=24, fontweight='bold', color='#495057')
    
    # 出生信息
    datetime_str = birth_info.get('datetime', '')
    lunar_str = birth_info.get('lunar', '')
    zodiac = birth_info.get('zodiac', '')
    
    ax.text(14, 18.2, f"{datetime_str} ({zodiac}年)", ha='center', va='center', 
           fontsize=16, color='#6c757d')
    ax.text(14, 17.8, f"农历: {lunar_str}", ha='center', va='center', 
           fontsize=14, color='#6c757d')

def draw_ultimate_palaces(ax, result):
    """绘制终极版12宫格"""
    ziwei_data = result.get("ziwei_analysis", {})
    palaces = ziwei_data.get("palaces", {})
    
    # 12宫的专业布局
    palace_layout = [
        ["子女宫", "财帛宫", "疾厄宫", "迁移宫"],
        ["夫妻宫", "", "", "奴仆宫"],
        ["兄弟宫", "", "", "官禄宫"],
        ["命宫", "父母宫", "福德宫", "田宅宫"]
    ]
    
    # 宫格大小 - 更大以容纳更多信息
    cell_width = 6
    cell_height = 4
    start_x = 2
    start_y = 16
    
    for row in range(4):
        for col in range(4):
            palace_name = palace_layout[row][col]
            if palace_name:  # 不是空格
                x = start_x + col * cell_width
                y = start_y - row * cell_height
                
                palace_data = palaces.get(palace_name, {})
                draw_ultimate_palace_cell(ax, x, y, cell_width, cell_height, 
                                         palace_name, palace_data, row, col)

def draw_ultimate_palace_cell(ax, x, y, width, height, palace_name, palace_data, row, col):
    """绘制终极版单个宫格"""
    # 根据宫位重要性和位置选择样式
    important_palaces = ["命宫", "财帛宫", "夫妻宫", "官禄宫"]
    is_important = palace_name in important_palaces
    
    # 背景色
    if is_important:
        bg_color = "#fff3cd"  # 重要宫位用淡黄色
        border_color = "#856404"
        border_width = 3
    else:
        bg_color = "#f8f9fa"
        border_color = "#6c757d"
        border_width = 2
    
    # 宫格边框
    rect = FancyBboxPatch((x, y), width, height,
                         boxstyle="round,pad=0.1",
                         linewidth=border_width, 
                         edgecolor=border_color,
                         facecolor=bg_color, alpha=0.95)
    ax.add_patch(rect)
    
    # 宫位名称（左上角）
    ax.text(x + 0.3, y + height - 0.3, palace_name, 
           ha='left', va='top', fontsize=12, 
           fontweight='bold', color='#495057')
    
    # 地支（右上角）
    position = palace_data.get("position", "")
    if position:
        ax.text(x + width - 0.3, y + height - 0.3, f"({position})", 
               ha='right', va='top', fontsize=11, 
               fontweight='bold', color='#6c757d')
    
    # 绘制详细星曜信息
    draw_detailed_stars(ax, x, y, width, height, palace_data)
    
    # 添加数字标记和小图标
    add_professional_markers(ax, x, y, width, height, palace_name, palace_data)

def draw_detailed_stars(ax, x, y, width, height, palace_data):
    """绘制详细的星曜信息"""
    # 主星区域（上半部分）
    major_stars = palace_data.get("major_stars", [])
    if major_stars:
        y_start = y + height - 0.8
        for i, star in enumerate(major_stars[:6]):  # 最多显示6个主星
            # 3列2行布局
            col = i % 3
            row = i // 3
            star_x = x + 0.4 + col * 1.7
            star_y = y_start - row * 0.4
            
            # 根据星曜重要性选择样式
            star_color, star_size = get_star_style(star)
            
            ax.text(star_x, star_y, star, 
                   ha='left', va='top', fontsize=star_size, 
                   fontweight='bold', color=star_color)
    
    # 副星区域（中间部分）
    minor_stars = palace_data.get("minor_stars", [])
    if minor_stars:
        y_start = y + height - 2.2
        for i, star in enumerate(minor_stars[:9]):  # 最多显示9个副星
            # 3列3行布局
            col = i % 3
            row = i // 3
            star_x = x + 0.3 + col * 1.6
            star_y = y_start - row * 0.3
            
            ax.text(star_x, star_y, star, 
                   ha='left', va='top', fontsize=9, 
                   color='#28a745')
    
    # 四化（底部）
    transformations = palace_data.get("transformations", [])
    if transformations:
        y_start = y + 0.8
        for i, trans in enumerate(transformations[:4]):
            col = i % 2
            row = i // 2
            trans_x = x + 0.3 + col * 2.5
            trans_y = y_start - row * 0.3
            
            # 四化用特殊样式
            trans_color = get_transformation_style(trans)
            ax.text(trans_x, trans_y, trans, 
                   ha='left', va='bottom', fontsize=9, 
                   fontweight='bold', color=trans_color,
                   bbox=dict(boxstyle="round,pad=0.2", 
                           facecolor='white', alpha=0.8))

def get_star_style(star):
    """获取星曜的显示样式"""
    # 紫微星系 - 紫色
    ziwei_stars = ["紫微", "天机", "太阳", "武曲", "天同", "廉贞"]
    if star in ziwei_stars:
        return '#6f42c1', 11
    
    # 天府星系 - 蓝色
    tianfu_stars = ["天府", "太阴", "贪狼", "巨门", "天相", "天梁", "七杀", "破军"]
    if star in tianfu_stars:
        return '#007bff', 11
    
    # 辅助星曜 - 绿色
    helper_stars = ["左辅", "右弼", "文昌", "文曲", "天魁", "天钺"]
    if star in helper_stars:
        return '#28a745', 10
    
    # 其他星曜 - 橙色
    return '#fd7e14', 9

def get_transformation_style(trans):
    """获取四化的显示样式"""
    if "禄" in trans:
        return '#28a745'  # 绿色
    elif "权" in trans:
        return '#ffc107'  # 黄色
    elif "科" in trans:
        return '#17a2b8'  # 青色
    elif "忌" in trans:
        return '#dc3545'  # 红色
    return '#6c757d'

def add_professional_markers(ax, x, y, width, height, palace_name, palace_data):
    """添加专业标记和图标"""
    # 添加宫位序号（左上角小圆圈）
    palace_numbers = {
        "命宫": 1, "兄弟宫": 2, "夫妻宫": 3, "子女宫": 4,
        "财帛宫": 5, "疾厄宫": 6, "迁移宫": 7, "奴仆宫": 8,
        "官禄宫": 9, "田宅宫": 10, "福德宫": 11, "父母宫": 12
    }
    
    if palace_name in palace_numbers:
        number = palace_numbers[palace_name]
        # 小圆圈
        circle = Circle((x + 0.4, y + height - 0.6), 0.15, 
                       facecolor='#007bff', edgecolor='white', 
                       linewidth=1, alpha=0.9)
        ax.add_patch(circle)
        
        # 数字
        ax.text(x + 0.4, y + height - 0.6, str(number), 
               ha='center', va='center', fontsize=8, 
               fontweight='bold', color='white')
    
    # 添加强弱标记（右下角）
    major_stars = palace_data.get("major_stars", [])
    if major_stars:
        star_count = len(major_stars)
        if star_count >= 3:
            # 星多为旺
            ax.text(x + width - 0.3, y + 0.3, "旺", 
                   ha='center', va='center', fontsize=8, 
                   fontweight='bold', color='white',
                   bbox=dict(boxstyle="circle,pad=0.1", 
                           facecolor='#dc3545', alpha=0.8))
        elif star_count == 1:
            # 星少为弱
            ax.text(x + width - 0.3, y + 0.3, "弱", 
                   ha='center', va='center', fontsize=8, 
                   fontweight='bold', color='white',
                   bbox=dict(boxstyle="circle,pad=0.1", 
                           facecolor='#6c757d', alpha=0.8))

def draw_complete_center_info(ax, result):
    """在中央绘制完整的八字和分析信息"""
    # 中央区域
    center_x = 8
    center_y = 4
    center_width = 12
    center_height = 8
    
    # 中央背景
    center_rect = FancyBboxPatch((center_x, center_y), center_width, center_height,
                                boxstyle="round,pad=0.3",
                                linewidth=3, 
                                edgecolor='#495057',
                                facecolor='#ffffff', alpha=0.95)
    ax.add_patch(center_rect)
    
    # 绘制八字四柱（上半部分）
    draw_complete_bazi(ax, result, center_x, center_y + 5)
    
    # 绘制五行分析（中间）
    draw_complete_wuxing(ax, result, center_x, center_y + 2.5)
    
    # 绘制融合验证（下半部分）
    draw_complete_validation(ax, result, center_x, center_y + 0.5)

def draw_complete_bazi(ax, result, x, y):
    """绘制完整的八字信息"""
    bazi_data = result.get("bazi_analysis", {})
    
    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")
        
        # 八字标题
        ax.text(x + 6, y + 2, "八字命理", ha='center', va='center', 
               fontsize=18, fontweight='bold', color='#495057')
        
        # 分割四柱
        pillars = chinese_date.split()
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        
        for i, (name, pillar) in enumerate(zip(pillar_names, pillars)):
            col_x = x + 1.5 + i * 2.5
            
            # 柱名背景
            name_rect = Rectangle((col_x - 0.5, y + 1.2), 1, 0.4,
                                 facecolor='#e9ecef', alpha=0.8)
            ax.add_patch(name_rect)
            
            # 柱名
            ax.text(col_x, y + 1.4, name, ha='center', va='center', 
                   fontsize=11, fontweight='bold', color='#495057')
            
            # 天干
            if len(pillar) >= 1:
                ax.text(col_x, y + 0.8, pillar[0], ha='center', va='center', 
                       fontsize=16, fontweight='bold', color='#dc3545')
            
            # 地支
            if len(pillar) >= 2:
                ax.text(col_x, y + 0.2, pillar[1], ha='center', va='center', 
                       fontsize=16, fontweight='bold', color='#007bff')

def draw_complete_wuxing(ax, result, x, y):
    """绘制完整的五行分析"""
    bazi_data = result.get("bazi_analysis", {})
    
    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})
        
        # 五行标题
        ax.text(x + 6, y + 1.5, "五行分析", ha='center', va='center', 
               fontsize=16, fontweight='bold', color='#495057')
        
        elements = ["木", "火", "土", "金", "水"]
        colors = ["#28a745", "#dc3545", "#ffc107", "#6c757d", "#007bff"]
        
        for i, (element, color) in enumerate(zip(elements, colors)):
            col_x = x + 1 + i * 2.2
            count = wuxing_count.get(element, 0)
            
            # 五行圆圈
            circle = Circle((col_x, y + 0.5), 0.4, 
                          facecolor=color, edgecolor='white', 
                          linewidth=2, alpha=0.9)
            ax.add_patch(circle)
            
            # 五行名称
            ax.text(col_x, y + 0.5, element, ha='center', va='center', 
                   fontsize=14, fontweight='bold', color='white')
            
            # 数量
            ax.text(col_x, y - 0.2, f"{count:.1f}", ha='center', va='center', 
                   fontsize=12, fontweight='bold', color=color)

def draw_complete_validation(ax, result, x, y):
    """绘制完整的验证信息"""
    fusion = result.get("fusion_analysis", {})
    
    if "cross_validation" in fusion:
        validation = fusion["cross_validation"]
        confidence = validation.get("confidence_level", 0)
        
        # 验证标题
        ax.text(x + 6, y + 1, "交叉验证结果", ha='center', va='center', 
               fontsize=14, fontweight='bold', color='#495057')
        
        # 一致性得分
        color = '#28a745' if confidence > 0.8 else '#ffc107' if confidence > 0.6 else '#dc3545'
        ax.text(x + 6, y + 0.3, f"紫薇+八字一致性: {confidence:.0%}", 
               ha='center', va='center', fontsize=13, 
               fontweight='bold', color=color)

def draw_sidebar_info(ax, result):
    """绘制侧边栏信息"""
    # 右侧信息栏
    sidebar_x = 21
    sidebar_y = 2
    sidebar_width = 6
    sidebar_height = 14
    
    # 侧边栏背景
    sidebar_rect = Rectangle((sidebar_x, sidebar_y), sidebar_width, sidebar_height,
                           linewidth=2, edgecolor='#6c757d', 
                           facecolor='#f8f9fa', alpha=0.9)
    ax.add_patch(sidebar_rect)
    
    # 侧边栏标题
    ax.text(sidebar_x + 3, sidebar_y + 13, "命理要点", ha='center', va='center', 
           fontsize=16, fontweight='bold', color='#495057')
    
    # 添加一些关键信息
    info_items = [
        "命宫主星: 紫微",
        "财帛宫: 武曲",
        "夫妻宫: 天同",
        "官禄宫: 天机",
        "五行: 土旺金弱",
        "格局: 紫微独坐",
        "运势: 中年发达"
    ]
    
    for i, item in enumerate(info_items):
        ax.text(sidebar_x + 0.3, sidebar_y + 11.5 - i * 1.5, f"• {item}", 
               ha='left', va='center', fontsize=11, color='#495057')

def main():
    """主函数"""
    print("🎨 终极版专业紫薇斗数图表生成器")
    print("=" * 70)
    
    success = generate_ultimate_chart()
    
    if success:
        print("\n✅ 终极版图表生成成功！")
        print("📁 文件保存为: ultimate_chart.png")
        print("🖼️ 终极版特点:")
        print("  - 完全参考专业网站样式")
        print("  - 超大画布容纳所有信息")
        print("  - 每个宫格包含完整星曜信息")
        print("  - 专业的颜色编码和标记")
        print("  - 数字序号和强弱标记")
        print("  - 完整的八字和五行分析")
        print("  - 侧边栏显示关键信息")
        print("  - 专业的排版和视觉效果")
    else:
        print("\n❌ 终极版图表生成失败")

if __name__ == "__main__":
    main()
