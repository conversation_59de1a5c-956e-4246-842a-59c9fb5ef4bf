#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六爻算卦工具 - 将现有六爻算卦功能包装为新架构工具
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from .base_tool import BaseTool

logger = logging.getLogger(__name__)

class LiuyaoTool(BaseTool):
    """六爻算卦工具 - 基于现有FortuneEngine的六爻算卦功能"""
    
    def __init__(self, liuyao_calc, llm_client, prompt_manager):
        """
        初始化六爻算卦工具
        
        Args:
            liuyao_calc: 六爻算法实例
            llm_client: LLM客户端
            prompt_manager: 提示词管理器
        """
        super().__init__(
            name="liuyao",
            description="六爻算卦 - 基于易经卦象进行占卜预测",
            version="2.0.0"
        )
        
        self.liuyao_calc = liuyao_calc
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
        
        logger.info("六爻算卦工具初始化完成")
    
    def can_handle(self, intent: Dict[str, Any]) -> bool:
        """
        判断是否能处理六爻算卦相关请求
        
        Args:
            intent: 意图信息
            
        Returns:
            是否能处理
        """
        tool_name = intent.get("tool_name", "").lower()
        
        # 直接指定六爻算卦
        if tool_name == "liuyao":
            return True
        
        # 检查关键词
        raw_response = intent.get("raw_response", "").lower()
        liuyao_keywords = ["算卦", "占卜", "起卦", "卦象", "六爻", "问卦"]
        
        return any(keyword in raw_response for keyword in liuyao_keywords)
    
    def get_required_entities(self) -> List[str]:
        """获取六爻算卦所需的实体 - 六爻不需要出生信息"""
        return ["question"]  # 只需要问题
    
    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行六爻算卦分析
        
        Args:
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            分析结果
        """
        start_time = time.time()
        
        try:
            logger.info("开始执行六爻算卦分析")
            
            # 1. 获取问题
            user_question = self._extract_question(intent, context)
            if not user_question:
                return {
                    "success": False,
                    "error": "缺少问题",
                    "message": "请告诉我您想要占卜的具体问题"
                }
            
            # 2. 调用六爻算法
            algorithm_result = self._call_liuyao_algorithm(user_question)
            if not algorithm_result.get("success"):
                return {
                    "success": False,
                    "error": algorithm_result.get("error", "算法调用失败"),
                    "message": "六爻起卦失败，请稍后重试"
                }
            
            # 3. 生成AI分析
            analysis_result = self._generate_ai_analysis(
                algorithm_result, intent, context, user_question
            )
            
            # 4. 生成图片
            chart_image = self._generate_chart_image(algorithm_result)
            
            execution_time = time.time() - start_time
            logger.info(f"六爻算卦分析完成 - 耗时: {execution_time:.2f}秒")
            
            return {
                "success": True,
                "tool_name": "liuyao",
                "analysis": analysis_result,
                "chart_data": algorithm_result,
                "chart_image": chart_image,
                "question": user_question,
                "execution_time": execution_time,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"六爻算卦分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "六爻算卦分析过程中出现错误，请稍后重试"
            }
    
    def _extract_question(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Optional[str]:
        """提取用户问题"""
        
        # 从意图中获取
        question = intent.get("entities", {}).get("question")
        if question:
            return question
        
        # 从原始消息中获取
        raw_response = intent.get("raw_response", "")
        if raw_response and len(raw_response.strip()) > 0:
            return raw_response.strip()
        
        # 从上下文中获取最近的消息
        recent_history = context.get("recent_history", [])
        if recent_history:
            last_message = recent_history[-1].get("user_message", "")
            if last_message:
                return last_message
        
        return None
    
    def _call_liuyao_algorithm(self, user_question: str) -> Dict[str, Any]:
        """调用六爻算法"""
        
        try:
            if not self.liuyao_calc:
                return {"success": False, "error": "六爻算法未初始化"}
            
            # 六爻算卦通常基于时间起卦
            result = self.liuyao_calc.divine_by_time(question=user_question)
            
            if result.get("success"):
                return result
            else:
                return {
                    "success": False,
                    "error": result.get("error", "算法计算失败")
                }
                
        except Exception as e:
            logger.error(f"六爻算法调用失败: {e}")
            return {
                "success": False,
                "error": f"算法调用异常: {str(e)}"
            }
    
    def _generate_ai_analysis(self, algorithm_result: Dict[str, Any], 
                            intent: Dict[str, Any], context: Dict[str, Any],
                            user_question: str) -> str:
        """生成AI分析"""
        
        try:
            # 获取问题类型
            question_type = intent.get("question_type", "general")
            
            # 构建分析提示词
            prompt = self.prompt_manager.get_liuyao_analysis_prompt(
                algorithm_result=algorithm_result,
                question_type=question_type,
                user_question=user_question,
                context=context
            )
            
            # 调用LLM生成分析
            analysis = self.llm_client.chat(
                prompt=prompt,
                system_prompt="你是专业的六爻算卦大师，精通易经卦象分析，必须基于提供的真实卦象数据进行专业分析，不得编造任何信息。",
                temperature=0.7
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"AI分析生成失败: {e}")
            return f"分析生成失败: {str(e)}"
    
    def _generate_chart_image(self, algorithm_result: Dict[str, Any]) -> Optional[str]:
        """生成六爻卦象图片"""
        
        try:
            # 导入图片生成器
            import sys
            sys.path.append('utils')
            from image_generator import generate_liuyao_chart
            
            chart_data = algorithm_result.get("data", {})
            if not chart_data:
                return None
            
            # 生成图片
            image_path = generate_liuyao_chart(chart_data)
            return image_path
            
        except Exception as e:
            logger.warning(f"六爻卦象图片生成失败: {e}")
            return None
    
    def get_prompt_template(self, task_type: str) -> Optional[str]:
        """获取六爻算卦专用提示词模板"""
        
        templates = {
            "career": "请重点分析官鬼爻、父母爻对事业的影响...",
            "love": "请重点分析世应关系、桃花爻的情况...",
            "wealth": "请重点分析妻财爻、生克关系...",
            "health": "请重点分析子孙爻、忌神的影响...",
            "fortune": "请重点分析卦象整体吉凶、动爻变化...",
            "general": "请进行全面的六爻卦象分析..."
        }
        
        return templates.get(task_type, templates["general"])
    
    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """验证六爻算卦输入"""
        
        # 基础验证
        base_validation = super().validate_input(intent, context)
        if not base_validation["valid"]:
            return base_validation
        
        # 问题验证
        question = self._extract_question(intent, context)
        if not question:
            return {
                "valid": False,
                "message": "六爻算卦需要明确的问题",
                "missing_entities": ["question"]
            }
        
        # 问题长度验证
        if len(question.strip()) < 3:
            return {
                "valid": False,
                "message": "请提供更具体的问题",
                "missing_entities": ["detailed_question"]
            }
        
        return {
            "valid": True,
            "message": "六爻算卦输入验证通过",
            "missing_entities": []
        }
    
    def get_info(self) -> Dict[str, Any]:
        """获取六爻算卦工具信息"""
        
        base_info = super().get_info()
        base_info.update({
            "supported_questions": ["career", "love", "wealth", "health", "fortune", "general"],
            "required_input": ["question"],
            "features": [
                "时间起卦法",
                "六爻卦象分析",
                "动爻变卦解读",
                "世应关系分析"
            ],
            "algorithm_status": "已连接" if self.liuyao_calc else "未连接",
            "divination_methods": ["时间起卦", "传统分析"]
        })
        
        return base_info
