#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实客户多次沟通场景
模拟客户的多轮对话，验证渐进式互动体验
"""

import asyncio
import sys
import time
from datetime import datetime
sys.path.append('.')

async def simulate_customer_conversation():
    """模拟真实客户的多次沟通"""
    print("👥 模拟真实客户多次沟通场景")
    print("=" * 80)
    print("场景: 客户从咨询到深度互动的完整流程")
    print("验证: 渐进式分析 + 智能查询 + 多轮对话")
    print("=" * 80)
    
    try:
        # 创建系统
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 清除Agent注册
        agent_registry.agents.clear()
        
        # 创建Agent实例
        master_agent = MasterCustomerAgent("customer_master")
        calculator_agent = FortuneCalculatorAgent("customer_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "customer_conversation_session"
        
        # 模拟客户对话流程
        conversation_flow = [
            # 第1轮：初次咨询
            {
                "round": 1,
                "user": "你好，我想算命",
                "expect": "引导提供生辰信息"
            },
            
            # 第2轮：提供生辰信息
            {
                "round": 2, 
                "user": "我是1990年5月12日午时出生的女性，想看紫薇斗数",
                "expect": "开始分析，后台生成角度"
            },
            
            # 第3轮：问性格（应该基于命宫分析回答）
            {
                "round": 3,
                "user": "我的性格怎么样？有什么特点？",
                "expect": "基于命宫分析的详细回答"
            },
            
            # 第4轮：问财运（可能触发财富分析生成）
            {
                "round": 4,
                "user": "我的财运如何？什么时候财运最好？",
                "expect": "基于财富分析或提示正在生成"
            },
            
            # 第5轮：问感情
            {
                "round": 5,
                "user": "感情方面呢？我什么时候能遇到真爱？",
                "expect": "基于婚姻分析或提示正在生成"
            },
            
            # 第6轮：问事业
            {
                "round": 6,
                "user": "我适合什么工作？事业发展怎么样？",
                "expect": "基于事业分析或提示正在生成"
            },
            
            # 第7轮：问健康
            {
                "round": 7,
                "user": "健康方面需要注意什么？",
                "expect": "基于健康分析或提示正在生成"
            },
            
            # 第8轮：再次问财运（应该有更详细的回答）
            {
                "round": 8,
                "user": "刚才问的财运，能再详细说说吗？",
                "expect": "基于已完成的财富分析详细回答"
            },
            
            # 第9轮：问具体建议
            {
                "round": 9,
                "user": "根据我的命盘，你有什么具体的人生建议？",
                "expect": "综合已完成角度的建议"
            },
            
            # 第10轮：感谢
            {
                "round": 10,
                "user": "谢谢你的分析，很有帮助！",
                "expect": "礼貌回复，可能提示其他角度"
            }
        ]
        
        print(f"🎭 开始模拟{len(conversation_flow)}轮客户对话...")
        print("=" * 60)
        
        successful_rounds = 0
        analysis_progress = {"completed_angles": 0, "total_angles": 12}
        
        for conversation in conversation_flow:
            round_num = conversation["round"]
            user_message = conversation["user"]
            expected = conversation["expect"]
            
            print(f"\n🔄 第{round_num}轮对话")
            print(f"👤 客户: {user_message}")
            print(f"📋 期望: {expected}")
            
            start_time = time.time()
            
            try:
                # 发送消息
                result = await coordinator.handle_user_message(session_id, user_message)
                response_time = time.time() - start_time
                
                if result.get('success'):
                    response = result.get('response', '')
                    print(f"🤖 AI ({response_time:.1f}s): {response[:300]}...")
                    
                    # 检查分析进度
                    if round_num >= 2:  # 从第2轮开始检查进度
                        new_progress = master_agent.get_analysis_progress(
                            master_agent.get_session_state(session_id).get("result_id", "")
                        )
                        if new_progress.get("completed_angles", 0) > analysis_progress.get("completed_angles", 0):
                            analysis_progress = new_progress
                            print(f"📊 分析进度更新: {analysis_progress['completed_angles']}/12 角度完成")
                    
                    # 评估回答质量
                    quality_score = 0
                    if len(response) > 200:
                        quality_score += 1
                    if "根据您的" in response or "基于" in response:
                        quality_score += 1
                    if round_num >= 3 and ("命宫" in response or "财运" in response or "感情" in response):
                        quality_score += 1
                    
                    if quality_score >= 2:
                        print(f"✅ 回答质量良好 (评分: {quality_score}/3)")
                        successful_rounds += 1
                    else:
                        print(f"⚠️  回答质量一般 (评分: {quality_score}/3)")
                    
                else:
                    print(f"❌ 系统错误: {result.get('error')}")
                
                print("-" * 50)
                
                # 适当延迟，模拟真实对话节奏
                if round_num <= 2:
                    await asyncio.sleep(1)  # 前两轮快一些
                else:
                    await asyncio.sleep(0.5)  # 后续轮次稍快
                    
            except Exception as e:
                print(f"❌ 第{round_num}轮对话失败: {e}")
                break
        
        # 评估整体对话效果
        print(f"\n🎯 客户对话测试评估")
        print("=" * 60)
        
        success_rate = successful_rounds / len(conversation_flow) * 100
        final_progress = analysis_progress.get("completed_angles", 0)
        
        print(f"📊 测试结果:")
        print(f"   成功对话轮次: {successful_rounds}/{len(conversation_flow)}")
        print(f"   成功率: {success_rate:.1f}%")
        print(f"   最终分析进度: {final_progress}/12 角度")
        print(f"   总字数: {analysis_progress.get('total_word_count', 0)}")
        
        if success_rate >= 80 and final_progress >= 3:
            print(f"\n🎉 客户对话体验优秀！")
            print(f"\n✅ 验证成功的功能:")
            print(f"   - 多轮对话流畅自然")
            print(f"   - 渐进式分析按需生成")
            print(f"   - 智能查询已完成角度")
            print(f"   - 基于真实算法的专业回答")
            print(f"   - 客户体验连贯一致")
            
            print(f"\n🌟 真实客户体验:")
            print(f"   1. 客户咨询 → 系统引导提供信息")
            print(f"   2. 提供生辰 → 后台开始12角度分析")
            print(f"   3. 问性格 → 基于命宫4000字专业回答")
            print(f"   4. 问财运 → 基于财富4000字深度解读")
            print(f"   5. 继续互动 → 每个话题都有充分支撑")
            
            return True
        else:
            print(f"\n⚠️  客户对话体验需要优化")
            print(f"   问题可能在于:")
            print(f"   - 回答质量不够专业")
            print(f"   - 角度生成速度太慢")
            print(f"   - 智能查询逻辑问题")
            return False
            
    except Exception as e:
        print(f"❌ 客户对话测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🎭 真实客户多次沟通测试")
    print("=" * 80)
    print("验证您的核心需求:")
    print("1. 客户可以多轮对话，体验自然流畅")
    print("2. 后台渐进式生成12个角度分析")
    print("3. 前端智能查询，基于已完成角度回答")
    print("4. 每个话题都有4000字专业内容支撑")
    print("5. 真正的专业大师级算命体验")
    print("=" * 80)
    
    success = await simulate_customer_conversation()
    
    if success:
        print(f"\n🎉 恭喜！真实客户多次沟通完美实现！")
        print(f"\n✅ 您的渐进式互动架构完全成功:")
        print(f"   - 前端Agent专注客户体验")
        print(f"   - 后端Agent专注专业分析")
        print(f"   - 12个角度按需渐进式生成")
        print(f"   - 客户可以随时深度互动")
        print(f"   - 每个话题都有充分的专业支撑")
        
        print(f"\n🚀 现在可以为真实客户提供服务：")
        print(f"   访问: http://localhost:8505")
        print(f"   体验真正的专业大师级算命服务！")
    else:
        print(f"\n💥 客户沟通体验需要进一步优化")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
