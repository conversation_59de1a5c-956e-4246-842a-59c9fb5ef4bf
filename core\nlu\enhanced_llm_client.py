#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版LLM客户端 - 集成高级提示词管理器
"""

import json
import requests
import logging
from typing import Dict, Any, Optional, List
from config.settings import get_config
from core.prompts.advanced_prompt_manager import AdvancedPromptManager, PromptContext, AnalysisDepth, PromptQuality

logger = logging.getLogger(__name__)

class EnhancedLLMClient:
    """增强版LLM客户端 - 支持智能提示词管理"""

    def __init__(self):
        """初始化增强版LLM客户端"""
        self.config = get_config()
        self.api_key = self.config.llm.api_key
        self.model_name = self.config.llm.model_name
        self.timeout = self.config.llm.timeout
        self.base_url = f"{self.config.llm.base_url}/chat/completions"
        
        # 初始化高级提示词管理器
        self.prompt_manager = AdvancedPromptManager()
        
        logger.info(f"增强版LLM客户端初始化完成 - 模型: {self.model_name}")

    def chat_completion(self, messages: List[Dict[str, str]],
                       temperature: float = 0.7,
                       max_tokens: int = 2000) -> Optional[str]:
        """
        调用LLM进行对话补全
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            temperature: 温度参数，控制随机性
            max_tokens: 最大token数
            
        Returns:
            LLM回复内容或None
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }

            logger.debug(f"发送LLM请求: {len(messages)} 条消息")

            response = requests.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                logger.debug(f"LLM响应成功: {len(content)} 字符")
                return content
            else:
                logger.error(f"LLM API错误: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("LLM API请求超时")
            return None
        except Exception as e:
            logger.error(f"LLM API调用失败: {e}")
            return None

    def enhanced_analysis(self, tool_name: str, analysis_type: str, 
                         algorithm_result: Dict[str, Any], 
                         user_id: str = None,
                         analysis_depth: str = "standard",
                         quality_level: str = "professional",
                         **kwargs) -> Optional[str]:
        """
        使用高级提示词管理器进行增强分析
        
        Args:
            tool_name: 工具名称 (ziwei, bazi, liuyao)
            analysis_type: 分析类型 (analysis, career, wealth, love, health)
            algorithm_result: 算法计算结果
            user_id: 用户ID（用于个性化）
            analysis_depth: 分析深度 (brief, standard, detailed, comprehensive)
            quality_level: 质量等级 (basic, professional, premium)
            **kwargs: 其他参数（如birth_info, user_question等）
            
        Returns:
            增强的分析结果
        """
        try:
            # 创建提示词上下文
            if user_id:
                context = self.prompt_manager.create_context_from_user(
                    user_id, analysis_depth, quality_level
                )
            else:
                context = PromptContext(
                    analysis_depth=AnalysisDepth(analysis_depth),
                    quality_level=PromptQuality(quality_level)
                )
            
            # 获取优化的提示词
            optimized_prompt = self.prompt_manager.get_optimized_prompt(
                tool_name=tool_name,
                analysis_type=analysis_type,
                algorithm_result=algorithm_result,
                context=context,
                **kwargs
            )
            
            # 构建消息
            messages = [
                {"role": "user", "content": optimized_prompt}
            ]
            
            # 根据分析深度调整参数
            max_tokens = self._get_max_tokens_by_depth(analysis_depth)
            temperature = self._get_temperature_by_quality(quality_level)
            
            logger.info(f"增强分析调用 - 工具: {tool_name}, 类型: {analysis_type}, 深度: {analysis_depth}")
            
            # 调用LLM
            response = self.chat_completion(
                messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            if response:
                logger.info(f"增强分析成功 - 响应长度: {len(response)}")
                
                # 记录用户反馈（这里可以后续添加满意度评分）
                if user_id:
                    self.prompt_manager.update_user_profile(user_id, {
                        "satisfaction": 4.0  # 默认满意度，后续可以通过用户反馈更新
                    })
                
                return response
            else:
                logger.warning("增强分析失败，LLM无响应")
                return None
                
        except Exception as e:
            logger.error(f"增强分析调用失败: {e}")
            return None
    
    def _get_max_tokens_by_depth(self, analysis_depth: str) -> int:
        """根据分析深度获取最大token数"""
        depth_tokens = {
            "brief": 800,
            "standard": 1500,
            "detailed": 3000,
            "comprehensive": 5000
        }
        return depth_tokens.get(analysis_depth, 1500)
    
    def _get_temperature_by_quality(self, quality_level: str) -> float:
        """根据质量等级获取温度参数"""
        quality_temps = {
            "basic": 0.8,      # 更随意
            "professional": 0.6, # 平衡
            "premium": 0.4     # 更严谨
        }
        return quality_temps.get(quality_level, 0.6)
    
    def intent_recognition(self, user_message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        意图识别（保持与原版兼容）
        """
        # 这里可以复用原版的意图识别逻辑
        # 或者使用高级提示词管理器优化意图识别提示词
        
        system_prompt = """你是一个专业的算命AI助手，具备强大的语义理解能力。

算命服务类型说明：
1. ziwei - 紫薇斗数：中国传统命理学，通过出生时间排出命盘
2. bazi - 八字算命：根据出生年月日时的天干地支组合分析命运
3. liuyao - 六爻占卜：通过起卦的方式预测具体事件
4. general - 一般算命咨询：用户想算命但没有明确指定具体方法
5. chat - 普通对话：非算命相关的日常交流

请深度分析用户消息的语义含义，返回JSON格式：
{
    "intent": "服务类型",
    "confidence": 0.0-1.0,
    "entities": {
        "birth_year": "年份或null",
        "birth_month": "月份或null", 
        "birth_day": "日期或null",
        "birth_hour": "时辰或null",
        "gender": "性别或null"
    },
    "reasoning": "详细的语义分析理由"
}

重要：必须返回有效的JSON格式。"""

        user_content = f"用户消息：{user_message}"
        
        if context and context.get("recent_history"):
            recent_msgs = context["recent_history"][-3:]
            history_text = "\n".join([f"- {msg.get('user_message', '')}" for msg in recent_msgs if msg.get('user_message')])
            if history_text:
                user_content += f"\n\n对话历史：\n{history_text}"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content}
        ]

        # 多次重试
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"LLM意图识别尝试 {attempt + 1}/{max_retries}")
                
                response = self.chat_completion(
                    messages,
                    temperature=0.1,
                    max_tokens=800
                )
                
                if response:
                    result = self._parse_llm_intent_response(response, user_message)
                    if result and result.get("intent"):
                        logger.info(f"LLM意图识别成功: {result.get('intent')} (置信度: {result.get('confidence')})")
                        return result
                
                logger.warning(f"LLM意图识别尝试 {attempt + 1} 失败，准备重试...")
                
            except Exception as e:
                logger.error(f"LLM意图识别尝试 {attempt + 1} 出错: {e}")

        # 所有重试都失败
        logger.error("LLM意图识别完全失败")
        return {
            "intent": "error",
            "confidence": 0.0,
            "entities": {},
            "reasoning": "LLM语义理解服务暂时不可用",
            "error": "LLM_SERVICE_UNAVAILABLE"
        }
    
    def _parse_llm_intent_response(self, response: str, user_message: str) -> Optional[Dict[str, Any]]:
        """解析LLM意图识别响应"""
        try:
            cleaned_response = response.strip()
            json_str = None

            # 查找JSON内容
            if "```json" in cleaned_response:
                start = cleaned_response.find("```json") + 7
                end = cleaned_response.find("```", start)
                if end > start:
                    json_str = cleaned_response[start:end].strip()
            elif "{" in cleaned_response and "}" in cleaned_response:
                start = cleaned_response.find("{")
                brace_count = 0
                end = start
                for i, char in enumerate(cleaned_response[start:], start):
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            end = i + 1
                            break
                json_str = cleaned_response[start:end]
            else:
                json_str = cleaned_response

            if json_str:
                result = json.loads(json_str)
                
                if "intent" in result:
                    standardized = {
                        "intent": result.get("intent", "chat"),
                        "confidence": float(result.get("confidence", 0.5)),
                        "entities": result.get("entities", {}),
                        "reasoning": result.get("reasoning", "LLM语义理解"),
                        "raw_response": response
                    }

                    # 验证意图类型
                    valid_intents = ["ziwei", "bazi", "liuyao", "general", "chat"]
                    if standardized["intent"] not in valid_intents:
                        standardized["intent"] = "general"

                    # 验证置信度范围
                    standardized["confidence"] = max(0.0, min(1.0, standardized["confidence"]))

                    return standardized

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
        except Exception as e:
            logger.error(f"响应解析出错: {e}")

        return None
    
    def get_prompt_statistics(self) -> Dict[str, Any]:
        """获取提示词使用统计"""
        return self.prompt_manager.get_prompt_statistics()
    
    def update_user_feedback(self, user_id: str, feedback: Dict[str, Any]):
        """更新用户反馈"""
        self.prompt_manager.update_user_profile(user_id, feedback)
