#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实算命测试 - 紫薇、八字、六爻完整测试
专注测试后台Agent的长时间计算和详细分析
"""

import asyncio
import sys
import time
import logging
from datetime import datetime
sys.path.append('.')

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_ziwei_fortune_telling():
    """测试紫薇斗数算命 - 完整流程"""
    print("🔮 测试紫薇斗数算命")
    print("=" * 60)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建系统
        master_agent = MasterCustomerAgent("ziwei_master")
        calculator_agent = FortuneCalculatorAgent("ziwei_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "ziwei_test_session"
        
        print("1️⃣ 开始紫薇斗数对话...")
        
        # 完整的紫薇斗数测试
        messages = [
            "你好，我想看紫薇斗数",
            "我是1988年6月1日出生的",
            "午时，男性",
            "我的事业运势怎么样？",
            "感情方面有什么建议？",
            "今年财运如何？"
        ]
        
        start_time = time.time()
        
        for i, message in enumerate(messages, 1):
            print(f"\n👤 用户 ({i}/6): {message}")
            
            result = await coordinator.handle_user_message(session_id, message)
            
            if result.get('success'):
                response = result.get('response', '')
                stage = result.get('stage', 'unknown')
                processing_time = result.get('processing_time', 0)
                
                print(f"🤖 AI: {response[:300]}...")
                print(f"📊 阶段: {stage} | 耗时: {processing_time:.2f}s")
                
                # 检查是否包含详细分析
                if "分析已完成" in response or "专业解读" in response:
                    print("✅ 检测到详细分析完成")
                    
                    # 检查缓存
                    session_state = master_agent.get_session_state(session_id)
                    if session_state and session_state.get("result_id"):
                        result_id = session_state["result_id"]
                        print(f"✅ 结果已缓存: {result_id[:8]}...")
                        
                        # 测试缓存查询
                        cached_result = calculator_agent.get_cached_result(result_id)
                        if cached_result:
                            print("✅ 缓存查询成功")
                            print(f"   简要总结: {cached_result['summary'][:100]}...")
                        else:
                            print("❌ 缓存查询失败")
            else:
                print(f"❌ 处理失败: {result.get('error')}")
            
            # 等待一下
            await asyncio.sleep(1)
        
        total_time = time.time() - start_time
        print(f"\n📊 紫薇斗数测试完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   平均响应: {total_time/len(messages):.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 紫薇斗数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_bazi_fortune_telling():
    """测试八字算命 - 完整流程"""
    print("\n🔮 测试八字算命")
    print("=" * 60)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建新的系统实例
        master_agent = MasterCustomerAgent("bazi_master")
        calculator_agent = FortuneCalculatorAgent("bazi_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "bazi_test_session"
        
        print("1️⃣ 开始八字算命对话...")
        
        # 完整的八字算命测试
        messages = [
            "我想看八字算命",
            "1990年5月15日下午3点出生，女性",
            "我的性格特点是什么？",
            "工作方面有什么建议？",
            "什么时候结婚比较好？"
        ]
        
        start_time = time.time()
        
        for i, message in enumerate(messages, 1):
            print(f"\n👤 用户 ({i}/5): {message}")
            
            result = await coordinator.handle_user_message(session_id, message)
            
            if result.get('success'):
                response = result.get('response', '')
                stage = result.get('stage', 'unknown')
                processing_time = result.get('processing_time', 0)
                
                print(f"🤖 AI: {response[:300]}...")
                print(f"📊 阶段: {stage} | 耗时: {processing_time:.2f}s")
                
                # 检查是否包含详细分析
                if "分析已完成" in response or "专业解读" in response:
                    print("✅ 检测到八字详细分析完成")
            else:
                print(f"❌ 处理失败: {result.get('error')}")
            
            await asyncio.sleep(1)
        
        total_time = time.time() - start_time
        print(f"\n📊 八字算命测试完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   平均响应: {total_time/len(messages):.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 八字算命测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_liuyao_divination():
    """测试六爻占卜 - 完整流程"""
    print("\n🔮 测试六爻占卜")
    print("=" * 60)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建新的系统实例
        master_agent = MasterCustomerAgent("liuyao_master")
        calculator_agent = FortuneCalculatorAgent("liuyao_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "liuyao_test_session"
        
        print("1️⃣ 开始六爻占卜对话...")
        
        # 完整的六爻占卜测试
        messages = [
            "我想用六爻占卜",
            "我想问工作方面的事情",
            "1992年3月8日上午10点，女性",
            "卦象显示什么？",
            "有什么需要注意的？"
        ]
        
        start_time = time.time()
        
        for i, message in enumerate(messages, 1):
            print(f"\n👤 用户 ({i}/5): {message}")
            
            result = await coordinator.handle_user_message(session_id, message)
            
            if result.get('success'):
                response = result.get('response', '')
                stage = result.get('stage', 'unknown')
                processing_time = result.get('processing_time', 0)
                
                print(f"🤖 AI: {response[:300]}...")
                print(f"📊 阶段: {stage} | 耗时: {processing_time:.2f}s")
                
                # 检查是否包含详细分析
                if "分析已完成" in response or "专业解读" in response:
                    print("✅ 检测到六爻详细分析完成")
            else:
                print(f"❌ 处理失败: {result.get('error')}")
            
            await asyncio.sleep(1)
        
        total_time = time.time() - start_time
        print(f"\n📊 六爻占卜测试完成")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   平均响应: {total_time/len(messages):.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 六爻占卜测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cache_performance():
    """测试缓存性能"""
    print("\n💾 测试缓存性能")
    print("=" * 60)
    
    try:
        from core.storage.calculation_cache import CalculationCache
        
        cache = CalculationCache()
        
        # 获取缓存统计
        stats = cache.get_cache_stats()
        print(f"缓存统计: {stats}")
        
        # 搜索结果
        all_results = cache.search_results()
        print(f"总缓存结果数: {len(all_results)}")
        
        if all_results:
            # 测试查询性能
            result_id = all_results[0]
            
            start_time = time.time()
            cached_result = cache.get_result(result_id)
            query_time = time.time() - start_time
            
            print(f"缓存查询耗时: {query_time*1000:.2f}ms")
            
            if cached_result:
                print(f"✅ 缓存查询成功")
                print(f"   结果类型: {cached_result.calculation_type}")
                print(f"   创建时间: {cached_result.created_at}")
                print(f"   访问次数: {cached_result.access_count}")
                print(f"   简要总结: {cached_result.summary[:100]}...")
                
                # 测试详细分析查询
                detailed = cache.get_detailed_analysis(result_id)
                if detailed:
                    print(f"✅ 详细分析查询成功")
                    word_count = detailed.get("word_count", 0)
                    print(f"   分析字数: {word_count}")
                else:
                    print(f"❌ 详细分析查询失败")
            else:
                print(f"❌ 缓存查询失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存性能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 真实算命完整测试")
    print("=" * 80)
    print("目标: 测试紫薇、八字、六爻三种算命的完整流程")
    print("重点: 验证后台Agent长时间计算和缓存机制")
    print("=" * 80)
    
    start_time = time.time()
    
    # 执行三种算命测试
    ziwei_success = await test_ziwei_fortune_telling()
    bazi_success = await test_bazi_fortune_telling()
    liuyao_success = await test_liuyao_divination()
    
    # 测试缓存性能
    cache_success = await test_cache_performance()
    
    total_time = time.time() - start_time
    
    # 最终评估
    print(f"\n" + "=" * 80)
    print("🏁 真实算命测试结果")
    print("=" * 80)
    
    results = [
        ("紫薇斗数", ziwei_success),
        ("八字算命", bazi_success),
        ("六爻占卜", liuyao_success),
        ("缓存系统", cache_success)
    ]
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"测试结果:")
    for name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {name}: {status}")
    
    print(f"\n📊 总体统计:")
    print(f"   成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   平均耗时: {total_time/total_count:.2f}秒")
    
    if success_count >= total_count * 0.75:
        print(f"\n🎉 真实算命测试成功！")
        print(f"✅ 三种算命类型全部可用")
        print(f"✅ 后台Agent长时间计算正常")
        print(f"✅ 缓存机制工作正常")
        print(f"✅ 主Agent持续聊天正常")
        
        print(f"\n🌟 系统优势验证:")
        print(f"   💬 自然对话体验")
        print(f"   🧮 专业算命分析")
        print(f"   💾 智能缓存机制")
        print(f"   ⚡ 资源优化效果")
        
        print(f"\n🚀 现在可以在Web界面体验完整功能：")
        print(f"   访问: http://localhost:8505")
        
        return True
    else:
        print(f"\n💥 真实算命测试存在问题")
        print(f"需要进一步调试和优化")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
