#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Web应用测试
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 确保在正确的目录中
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """主应用"""
    st.set_page_config(
        page_title="智能算命AI系统 v4.0",
        page_icon="🔮",
        layout="wide"
    )
    
    # 测试导入
    try:
        from config import config
        st.success(f"✅ 配置模块导入成功 - API: {'已配置' if config.llm.api_key else '未配置'}")
    except Exception as e:
        st.error(f"❌ 配置模块导入失败: {e}")
        return
    
    try:
        from models.birth_info import BirthInfo
        birth = BirthInfo(1988, 6, 1, 11, "男")
        st.success(f"✅ 数据模型正常: {birth.to_display_string()}")
    except Exception as e:
        st.error(f"❌ 数据模型失败: {e}")
        return
    
    try:
        from services.chart_service import ChartService
        chart_service = ChartService()
        st.success("✅ 排盘服务正常")
        
        # 测试排盘生成
        if st.button("🔮 测试排盘生成"):
            with st.spinner("生成排盘中..."):
                chart_data = chart_service.generate_chart(birth)
                
                if chart_data.success:
                    st.success("🎉 排盘生成成功！")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.info(f"紫薇: {'✅' if chart_data.ziwei_chart else '❌'}")
                    with col2:
                        st.info(f"八字: {'✅' if chart_data.bazi_chart else '❌'}")
                    
                    # 显示排盘摘要
                    st.markdown("### 📊 排盘摘要")
                    st.text(chart_data.get_summary())
                    
                else:
                    st.error(f"❌ 排盘失败: {chart_data.error_message}")
        
    except Exception as e:
        st.error(f"❌ 排盘服务失败: {e}")
        st.exception(e)
        return
    
    # 主界面
    st.markdown("""
    # 🔮 智能算命AI系统 v4.0 重构版
    
    ## ✅ 系统状态检查
    
    基础功能测试通过！重构项目运行正常。
    
    ### 🎯 已完成功能
    - ✅ 配置管理
    - ✅ 数据模型
    - ✅ 排盘服务
    - ✅ 缓存机制
    - ✅ 日志系统
    
    ### 🚀 下一步计划
    1. 完善分析功能
    2. 添加六爻占卜
    3. 实现合婚分析
    4. 优化用户界面
    
    ### 💡 使用说明
    点击上方的"测试排盘生成"按钮来验证核心功能。
    """)
    
    # 显示系统信息
    with st.expander("🔧 系统信息"):
        st.markdown(f"""
        **当前目录**: {current_dir}  
        **Python路径**: {sys.path[0]}  
        **配置状态**: {'正常' if 'config' in locals() else '异常'}  
        **服务状态**: {'正常' if 'chart_service' in locals() else '异常'}
        """)

if __name__ == "__main__":
    main()
