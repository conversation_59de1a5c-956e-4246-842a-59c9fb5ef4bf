#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析结果数据模型
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum

class AnalysisAngle(Enum):
    """分析角度枚举"""
    PERSONALITY_DESTINY = "personality_destiny"  # 命宫性格命运
    WEALTH_FORTUNE = "wealth_fortune"           # 财富运势
    MARRIAGE_LOVE = "marriage_love"             # 婚姻感情
    HEALTH_WELLNESS = "health_wellness"         # 健康养生
    CHILDREN_CREATIVITY = "children_creativity" # 子女创造
    CAREER_ACHIEVEMENT = "career_achievement"   # 事业成就
    EDUCATION_WISDOM = "education_wisdom"       # 学业智慧
    FAMILY_KINSHIP = "family_kinship"          # 家庭亲情
    SOCIAL_FRIENDSHIP = "social_friendship"     # 社交友谊
    TRAVEL_MIGRATION = "travel_migration"       # 迁移出行
    SPIRITUAL_BELIEF = "spiritual_belief"       # 精神信仰
    OVERALL_FORTUNE = "overall_fortune"         # 整体运势

# 分析角度中文名称映射
ANGLE_NAMES = {
    AnalysisAngle.PERSONALITY_DESTINY: "命宫性格命运",
    AnalysisAngle.WEALTH_FORTUNE: "财富运势",
    AnalysisAngle.MARRIAGE_LOVE: "婚姻感情",
    AnalysisAngle.HEALTH_WELLNESS: "健康养生",
    AnalysisAngle.CHILDREN_CREATIVITY: "子女创造",
    AnalysisAngle.CAREER_ACHIEVEMENT: "事业成就",
    AnalysisAngle.EDUCATION_WISDOM: "学业智慧",
    AnalysisAngle.FAMILY_KINSHIP: "家庭亲情",
    AnalysisAngle.SOCIAL_FRIENDSHIP: "社交友谊",
    AnalysisAngle.TRAVEL_MIGRATION: "迁移出行",
    AnalysisAngle.SPIRITUAL_BELIEF: "精神信仰",
    AnalysisAngle.OVERALL_FORTUNE: "整体运势"
}

# 分析优先级（数字越小优先级越高）
ANGLE_PRIORITY = {
    AnalysisAngle.PERSONALITY_DESTINY: 1,
    AnalysisAngle.WEALTH_FORTUNE: 2,
    AnalysisAngle.MARRIAGE_LOVE: 3,
    AnalysisAngle.HEALTH_WELLNESS: 4,
    AnalysisAngle.CAREER_ACHIEVEMENT: 5,
    AnalysisAngle.CHILDREN_CREATIVITY: 6,
    AnalysisAngle.EDUCATION_WISDOM: 7,
    AnalysisAngle.FAMILY_KINSHIP: 8,
    AnalysisAngle.SOCIAL_FRIENDSHIP: 9,
    AnalysisAngle.TRAVEL_MIGRATION: 10,
    AnalysisAngle.SPIRITUAL_BELIEF: 11,
    AnalysisAngle.OVERALL_FORTUNE: 12
}

@dataclass
class SingleAnalysis:
    """单个角度的分析结果"""
    angle: AnalysisAngle
    content: str
    word_count: int
    analysis_time: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: str = ""
    
    def get_angle_name(self) -> str:
        """获取角度中文名称"""
        return ANGLE_NAMES.get(self.angle, str(self.angle.value))
    
    def is_valid(self) -> bool:
        """检查分析是否有效"""
        return (self.success and 
                self.content and 
                len(self.content.strip()) > 100 and  # 至少100字
                self.word_count >= 1000)  # 至少1000字

@dataclass
class AnalysisResult:
    """完整分析结果"""
    chart_cache_key: str  # 对应的排盘缓存键
    analyses: Dict[AnalysisAngle, SingleAnalysis] = field(default_factory=dict)
    creation_time: datetime = field(default_factory=datetime.now)
    last_update_time: datetime = field(default_factory=datetime.now)
    
    def add_analysis(self, analysis: SingleAnalysis):
        """添加单个分析"""
        self.analyses[analysis.angle] = analysis
        self.last_update_time = datetime.now()
    
    def get_analysis(self, angle: AnalysisAngle) -> Optional[SingleAnalysis]:
        """获取指定角度的分析"""
        return self.analyses.get(angle)
    
    def get_completed_angles(self) -> List[AnalysisAngle]:
        """获取已完成的分析角度"""
        return [angle for angle, analysis in self.analyses.items() 
                if analysis.is_valid()]
    
    def get_pending_angles(self) -> List[AnalysisAngle]:
        """获取待分析的角度"""
        completed = set(self.get_completed_angles())
        all_angles = set(AnalysisAngle)
        pending = list(all_angles - completed)
        
        # 按优先级排序
        pending.sort(key=lambda x: ANGLE_PRIORITY.get(x, 999))
        return pending
    
    def get_progress(self) -> Dict[str, Any]:
        """获取分析进度"""
        completed = len(self.get_completed_angles())
        total = len(AnalysisAngle)
        
        return {
            "completed": completed,
            "total": total,
            "percentage": round(completed / total * 100, 1),
            "completed_angles": [ANGLE_NAMES[angle] for angle in self.get_completed_angles()],
            "pending_angles": [ANGLE_NAMES[angle] for angle in self.get_pending_angles()]
        }
    
    def is_complete(self) -> bool:
        """检查是否所有分析都已完成"""
        return len(self.get_completed_angles()) == len(AnalysisAngle)
    
    def get_total_word_count(self) -> int:
        """获取总字数"""
        return sum(analysis.word_count for analysis in self.analyses.values() 
                  if analysis.is_valid())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "chart_cache_key": self.chart_cache_key,
            "creation_time": self.creation_time.isoformat(),
            "last_update_time": self.last_update_time.isoformat(),
            "analyses": {
                angle.value: {
                    "angle": angle.value,
                    "content": analysis.content,
                    "word_count": analysis.word_count,
                    "analysis_time": analysis.analysis_time.isoformat(),
                    "success": analysis.success,
                    "error_message": analysis.error_message
                }
                for angle, analysis in self.analyses.items()
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisResult':
        """从字典创建实例"""
        result = cls(
            chart_cache_key=data["chart_cache_key"],
            creation_time=datetime.fromisoformat(data["creation_time"]),
            last_update_time=datetime.fromisoformat(data["last_update_time"])
        )
        
        for angle_value, analysis_data in data["analyses"].items():
            angle = AnalysisAngle(angle_value)
            analysis = SingleAnalysis(
                angle=angle,
                content=analysis_data["content"],
                word_count=analysis_data["word_count"],
                analysis_time=datetime.fromisoformat(analysis_data["analysis_time"]),
                success=analysis_data["success"],
                error_message=analysis_data["error_message"]
            )
            result.analyses[angle] = analysis
        
        return result

def get_angle_by_name(name: str) -> Optional[AnalysisAngle]:
    """根据中文名称获取分析角度"""
    for angle, angle_name in ANGLE_NAMES.items():
        if angle_name == name:
            return angle
    return None
