#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具注册中心 - 管理所有可用工具的注册、发现和调用
"""

import logging
from typing import Dict, List, Optional, Any, Type
from datetime import datetime
from .base_tool import BaseTool, ToolExecutionContext

logger = logging.getLogger(__name__)

class ToolRegistry:
    """工具注册中心 - 统一管理所有功能工具"""
    
    def __init__(self):
        """初始化工具注册中心"""
        self.tools: Dict[str, BaseTool] = {}
        self.tool_categories: Dict[str, List[str]] = {}
        self.created_at = datetime.now()
        
        # 全局统计
        self.global_stats = {
            "total_registrations": 0,
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0
        }
        
        logger.info("工具注册中心初始化完成")
    
    def register_tool(self, tool: BaseTool, category: str = "general") -> bool:
        """
        注册工具
        
        Args:
            tool: 工具实例
            category: 工具分类
            
        Returns:
            是否注册成功
        """
        try:
            if not isinstance(tool, BaseTool):
                raise ValueError(f"工具必须继承自BaseTool: {type(tool)}")
            
            if tool.name in self.tools:
                logger.warning(f"工具 {tool.name} 已存在，将被覆盖")
            
            self.tools[tool.name] = tool
            
            # 添加到分类
            if category not in self.tool_categories:
                self.tool_categories[category] = []
            
            if tool.name not in self.tool_categories[category]:
                self.tool_categories[category].append(tool.name)
            
            self.global_stats["total_registrations"] += 1
            
            logger.info(f"工具 {tool.name} 注册成功 - 分类: {category}")
            return True
            
        except Exception as e:
            logger.error(f"工具注册失败: {e}")
            return False
    
    def unregister_tool(self, tool_name: str) -> bool:
        """
        注销工具
        
        Args:
            tool_name: 工具名称
            
        Returns:
            是否注销成功
        """
        if tool_name in self.tools:
            del self.tools[tool_name]
            
            # 从分类中移除
            for category, tools in self.tool_categories.items():
                if tool_name in tools:
                    tools.remove(tool_name)
            
            logger.info(f"工具 {tool_name} 已注销")
            return True
        
        logger.warning(f"工具 {tool_name} 不存在，无法注销")
        return False
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """
        获取工具实例
        
        Args:
            tool_name: 工具名称
            
        Returns:
            工具实例或None
        """
        return self.tools.get(tool_name)
    
    def list_tools(self, category: Optional[str] = None) -> List[str]:
        """
        列出工具
        
        Args:
            category: 工具分类，None表示所有工具
            
        Returns:
            工具名称列表
        """
        if category is None:
            return list(self.tools.keys())
        
        return self.tool_categories.get(category, [])
    
    def list_categories(self) -> List[str]:
        """
        列出所有分类
        
        Returns:
            分类列表
        """
        return list(self.tool_categories.keys())
    
    def find_tools_for_intent(self, intent: Dict[str, Any]) -> List[BaseTool]:
        """
        根据意图查找合适的工具
        
        Args:
            intent: 意图信息
            
        Returns:
            可处理该意图的工具列表
        """
        suitable_tools = []
        
        for tool in self.tools.values():
            try:
                if tool.can_handle(intent):
                    suitable_tools.append(tool)
            except Exception as e:
                logger.warning(f"工具 {tool.name} 意图检查失败: {e}")
        
        # 按优先级排序（可以根据工具的成功率等指标）
        suitable_tools.sort(key=lambda t: t.stats["successful_calls"], reverse=True)
        
        return suitable_tools
    
    def execute_tool(self, tool_name: str, intent: Dict[str, Any], 
                    context: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """
        执行工具
        
        Args:
            tool_name: 工具名称
            intent: 意图信息
            context: 上下文信息
            session_id: 会话ID
            
        Returns:
            执行结果
        """
        start_time = datetime.now()
        self.global_stats["total_executions"] += 1
        
        try:
            # 获取工具
            tool = self.get_tool(tool_name)
            if not tool:
                raise ValueError(f"工具 {tool_name} 不存在")
            
            # 验证输入
            validation = tool.validate_input(intent, context)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["message"],
                    "missing_entities": validation["missing_entities"],
                    "tool_name": tool_name
                }
            
            # 创建执行上下文
            exec_context = ToolExecutionContext(session_id)
            exec_context.add_metadata("tool_name", tool_name)
            exec_context.add_metadata("intent", intent)
            
            # 执行工具
            logger.debug(f"开始执行工具 {tool_name}")
            result = tool.execute(intent, context)
            
            # 格式化结果
            formatted_result = tool.format_response(result, intent)
            
            # 更新统计
            execution_time = (datetime.now() - start_time).total_seconds()
            tool.update_stats(True, execution_time)
            self.global_stats["successful_executions"] += 1
            
            logger.info(f"工具 {tool_name} 执行成功 - 耗时: {execution_time:.2f}秒")
            
            return formatted_result
            
        except Exception as e:
            # 处理错误
            execution_time = (datetime.now() - start_time).total_seconds()
            
            if tool_name in self.tools:
                self.tools[tool_name].update_stats(False, execution_time)
                error_result = self.tools[tool_name].handle_error(e, intent)
            else:
                error_result = {
                    "success": False,
                    "error": str(e),
                    "tool_name": tool_name,
                    "timestamp": datetime.now()
                }
            
            self.global_stats["failed_executions"] += 1
            
            logger.error(f"工具 {tool_name} 执行失败: {e}")
            
            return error_result
    
    def auto_select_and_execute(self, intent: Dict[str, Any], 
                               context: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """
        自动选择并执行最合适的工具
        
        Args:
            intent: 意图信息
            context: 上下文信息
            session_id: 会话ID
            
        Returns:
            执行结果
        """
        # 查找合适的工具
        suitable_tools = self.find_tools_for_intent(intent)
        
        if not suitable_tools:
            return {
                "success": False,
                "error": "没有找到合适的工具处理该请求",
                "intent": intent
            }
        
        # 选择最佳工具（第一个）
        best_tool = suitable_tools[0]
        
        logger.info(f"自动选择工具: {best_tool.name}")
        
        # 执行工具
        return self.execute_tool(best_tool.name, intent, context, session_id)
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """
        获取工具信息
        
        Args:
            tool_name: 工具名称
            
        Returns:
            工具信息或None
        """
        tool = self.get_tool(tool_name)
        if tool:
            return tool.get_info()
        return None
    
    def get_all_tools_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有工具信息
        
        Returns:
            所有工具信息
        """
        return {name: tool.get_info() for name, tool in self.tools.items()}
    
    def get_tools_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有工具统计信息
        
        Returns:
            所有工具统计信息
        """
        return {name: tool.get_stats() for name, tool in self.tools.items()}
    
    def get_global_stats(self) -> Dict[str, Any]:
        """
        获取全局统计信息
        
        Returns:
            全局统计信息
        """
        success_rate = 0
        if self.global_stats["total_executions"] > 0:
            success_rate = (self.global_stats["successful_executions"] / 
                          self.global_stats["total_executions"] * 100)
        
        return {
            "total_tools": len(self.tools),
            "total_categories": len(self.tool_categories),
            "total_registrations": self.global_stats["total_registrations"],
            "total_executions": self.global_stats["total_executions"],
            "successful_executions": self.global_stats["successful_executions"],
            "failed_executions": self.global_stats["failed_executions"],
            "success_rate": f"{success_rate:.2f}%",
            "created_at": self.created_at.isoformat()
        }
    
    def reset_all_stats(self) -> None:
        """重置所有统计信息"""
        for tool in self.tools.values():
            tool.reset_stats()
        
        self.global_stats = {
            "total_registrations": len(self.tools),
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0
        }
        
        logger.info("所有工具统计信息已重置")
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        healthy_tools = 0
        unhealthy_tools = []
        
        for name, tool in self.tools.items():
            try:
                # 简单的健康检查 - 检查工具是否可以正常响应
                test_intent = {"tool_name": name, "entities": {}}
                tool.can_handle(test_intent)
                healthy_tools += 1
            except Exception as e:
                unhealthy_tools.append({"name": name, "error": str(e)})
        
        return {
            "status": "healthy" if not unhealthy_tools else "degraded",
            "total_tools": len(self.tools),
            "healthy_tools": healthy_tools,
            "unhealthy_tools": unhealthy_tools,
            "timestamp": datetime.now().isoformat()
        }
