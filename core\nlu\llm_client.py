#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM客户端 - 与DeepSeek-V3模型通信
"""

import json
import requests
import logging
from typing import Dict, Any, Optional, List
from config.settings import get_config

logger = logging.getLogger(__name__)

class LLMClient:
    """LLM客户端 - 支持DeepSeek-V3模型调用"""

    def __init__(self):
        """初始化LLM客户端"""
        self.config = get_config()
        self.api_key = self.config.llm.api_key
        self.model_name = self.config.llm.model_name
        self.timeout = self.config.llm.timeout
        self.base_url = f"{self.config.llm.base_url}/chat/completions"

        logger.info(f"LLM客户端初始化完成 - 模型: {self.model_name}")

    def chat_completion(self, messages: List[Dict[str, str]],
                       temperature: float = 0.3,
                       max_tokens: int = 4096) -> Optional[str]:
        """
        调用LLM进行对话补全

        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            temperature: 温度参数，控制随机性
            max_tokens: 最大token数

        Returns:
            LLM回复内容或None
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }

            logger.debug(f"发送LLM请求: {len(messages)} 条消息")

            response = requests.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                logger.debug(f"LLM响应成功: {len(content)} 字符")
                return content
            else:
                logger.error(f"LLM API错误: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("LLM API请求超时")
            return None
        except Exception as e:
            logger.error(f"LLM API调用失败: {e}")
            return None

    def intent_recognition(self, user_message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        真正的LLM语义理解意图识别 - 不依赖关键词匹配

        Args:
            user_message: 用户消息
            context: 对话上下文

        Returns:
            意图识别结果
        """
        # 构建更智能的语义理解提示词
        system_prompt = """你是一个专业的算命AI助手，具备强大的语义理解能力。你需要通过深度理解用户的真实意图，而不是简单的关键词匹配来识别用户需求。

算命服务类型说明：
1. ziwei - 紫薇斗数：中国传统命理学，通过出生时间排出命盘，分析十二宫位和星曜来预测命运
2. bazi - 八字算命：根据出生年月日时的天干地支组合，分析五行生克来判断命运
3. liuyao - 六爻占卜：通过起卦的方式，用六个爻位的变化来预测具体事件
4. general - 一般算命咨询：用户想算命但没有明确指定具体方法
5. chat - 普通对话：非算命相关的日常交流

语义理解要点：
- 理解用户的真实意图，而不是表面词汇
- 考虑上下文和对话历史
- 识别隐含的需求表达
- 提取关键的个人信息

请深度分析用户消息的语义含义，返回JSON格式：
{
    "intent": "服务类型",
    "confidence": 0.0-1.0,
    "entities": {
        "birth_year": "年份或null",
        "birth_month": "月份或null",
        "birth_day": "日期或null",
        "birth_hour": "时辰或null",
        "gender": "性别或null"
    },
    "reasoning": "详细的语义分析理由"
}

重要：必须返回有效的JSON格式，不要添加其他文字说明。"""

        # 构建上下文感知的用户消息
        user_content = f"用户消息：{user_message}"

        if context and context.get("recent_history"):
            recent_msgs = context["recent_history"][-3:]  # 最近3条消息
            history_text = "\n".join([f"- {msg.get('user_message', '')}" for msg in recent_msgs if msg.get('user_message')])
            if history_text:
                user_content += f"\n\n对话历史：\n{history_text}"

        if context and context.get("birth_info"):
            user_content += f"\n\n已知用户信息：{context['birth_info']}"

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content}
        ]

        # 多次重试确保LLM响应
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"LLM意图识别尝试 {attempt + 1}/{max_retries}")

                response = self.chat_completion(
                    messages,
                    temperature=0.1,  # 降低温度确保稳定性
                    max_tokens=800
                )

                if response:
                    # 解析JSON响应
                    result = self._parse_llm_intent_response(response, user_message)
                    if result and result.get("intent"):
                        logger.info(f"LLM意图识别成功: {result.get('intent')} (置信度: {result.get('confidence')})")
                        return result

                logger.warning(f"LLM意图识别尝试 {attempt + 1} 失败，准备重试...")

            except Exception as e:
                logger.error(f"LLM意图识别尝试 {attempt + 1} 出错: {e}")

        # 所有重试都失败，返回错误而不是降级到关键词匹配
        logger.error("LLM意图识别完全失败，无法提供语义理解服务")
        return {
            "intent": "error",
            "confidence": 0.0,
            "entities": {},
            "reasoning": "LLM语义理解服务暂时不可用，请稍后重试",
            "error": "LLM_SERVICE_UNAVAILABLE"
        }

    def _parse_llm_intent_response(self, response: str, user_message: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM意图识别响应

        Args:
            response: LLM响应内容
            user_message: 原始用户消息

        Returns:
            解析后的意图结果或None
        """
        try:
            # 清理响应内容
            cleaned_response = response.strip()

            # 尝试多种JSON提取方式
            json_str = None

            # 方式1: 查找```json代码块
            if "```json" in cleaned_response:
                start = cleaned_response.find("```json") + 7
                end = cleaned_response.find("```", start)
                if end > start:
                    json_str = cleaned_response[start:end].strip()

            # 方式2: 查找第一个完整的JSON对象
            elif "{" in cleaned_response and "}" in cleaned_response:
                start = cleaned_response.find("{")
                # 找到匹配的右括号
                brace_count = 0
                end = start
                for i, char in enumerate(cleaned_response[start:], start):
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            end = i + 1
                            break
                json_str = cleaned_response[start:end]

            # 方式3: 整个响应就是JSON
            else:
                json_str = cleaned_response

            if json_str:
                # 解析JSON
                result = json.loads(json_str)

                # 验证必要字段
                if "intent" in result:
                    # 标准化字段
                    standardized = {
                        "intent": result.get("intent", "chat"),
                        "confidence": float(result.get("confidence", 0.5)),
                        "entities": result.get("entities", {}),
                        "reasoning": result.get("reasoning", "LLM语义理解"),
                        "raw_response": response
                    }

                    # 验证意图类型
                    valid_intents = ["ziwei", "bazi", "liuyao", "general", "chat"]
                    if standardized["intent"] not in valid_intents:
                        logger.warning(f"无效意图类型: {standardized['intent']}, 改为general")
                        standardized["intent"] = "general"

                    # 验证置信度范围
                    standardized["confidence"] = max(0.0, min(1.0, standardized["confidence"]))

                    return standardized

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}, 响应内容: {response[:200]}...")
        except Exception as e:
            logger.error(f"响应解析出错: {e}")

        return None

    def fewshot_chat(self, user_message: str, category: str = None,
                     temperature: float = 0.3, max_tokens: int = 4096) -> Optional[str]:
        """
        使用Few-shot Learning进行专业算命对话

        Args:
            user_message: 用户消息
            category: 问题类别 (career, wealth, love, health, general)
            temperature: 温度参数
            max_tokens: 最大token数

        Returns:
            专业的算命回复
        """
        try:
            # 导入Few-shot样本管理器
            from core.conversation.fewshot_samples import format_fewshot_prompt

            # 生成包含训练样本的prompt
            enhanced_prompt = format_fewshot_prompt(user_message, category, max_samples=3)

            messages = [
                {"role": "user", "content": enhanced_prompt}
            ]

            logger.info(f"Few-shot Learning调用 - 类别: {category}, 消息长度: {len(user_message)}")

            response = self.chat_completion(
                messages,
                temperature=temperature,
                max_tokens=max_tokens
            )

            if response:
                logger.info(f"Few-shot Learning成功 - 响应长度: {len(response)}")
                return response
            else:
                logger.warning("Few-shot Learning失败，返回空响应")
                return None

        except Exception as e:
            logger.error(f"Few-shot Learning调用失败: {e}")
            return None

def test_llm_client():
    """测试LLM客户端"""
    print("🤖 测试LLM客户端")
    print("-" * 30)

    try:
        client = LLMClient()
        print("✅ LLM客户端创建成功")

        # 测试意图识别
        test_messages = [
            "我想看紫薇斗数",
            "帮我算八字",
            "我要六爻占卜",
            "我1988年6月1日午时出生，男，想算命",
            "你好"
        ]

        for message in test_messages:
            print(f"\n用户: {message}")
            result = client.intent_recognition(message)
            print(f"意图: {result['intent']} (置信度: {result['confidence']})")
            print(f"理由: {result['reasoning']}")
            if result['entities']:
                print(f"实体: {result['entities']}")

        return True

    except Exception as e:
        print(f"❌ LLM客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_llm_client()
