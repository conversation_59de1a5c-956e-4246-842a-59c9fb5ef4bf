#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构项目启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🔮 智能算命AI系统 v4.0 重构版启动")
    print("=" * 50)
    
    # 检查是否在正确目录
    refactor_dir = Path("ziwei_refactor")
    if refactor_dir.exists():
        print("📁 进入重构项目目录...")
        os.chdir(refactor_dir)
    else:
        print("❌ 未找到重构项目目录")
        return
    
    # 检查环境变量文件
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 创建环境变量文件...")
        parent_env = Path("../ziwei_refactor.env")
        if parent_env.exists():
            import shutil
            shutil.copy(parent_env, ".env")
            print("✅ 环境变量文件已创建")
        else:
            print("⚠️ 环境变量文件不存在，请手动配置")
    
    # 检查依赖
    print("🔍 检查Python依赖...")
    required_packages = ["streamlit", "requests", "pandas", "python-dotenv"]
    missing = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"❌ {package}")
    
    if missing:
        print(f"\n📦 安装缺失依赖: {' '.join(missing)}")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + missing, check=True)
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return
    
    # 测试配置
    print("\n🧪 测试项目配置...")
    try:
        sys.path.insert(0, ".")
        from config import config
        print("✅ 配置模块正常")
        print(f"   API密钥: {'已配置' if config.llm.api_key else '未配置'}")
        print(f"   LLM模型: {config.llm.model_name}")
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return
    
    # 启动应用
    print("\n🚀 启动Web应用...")
    print("🌐 应用将在 http://localhost:8501 启动")
    print("按 Ctrl+C 停止应用")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "web/app.py",
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--browser.gatherUsageStats=false"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
