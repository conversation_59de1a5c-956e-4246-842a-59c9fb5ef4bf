#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本 - 展示独立排盘功能
"""

import sys
import os

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from simple_interface import SimplePaipanInterface

def main():
    """快速演示"""
    print("🔮 独立排盘模块快速演示")
    print("=" * 50)
    
    # 创建接口
    interface = SimplePaipanInterface()
    
    # 演示案例
    print("\n📋 演示案例: 1990年3月15日8时 女命")
    print("-" * 50)
    
    try:
        # 计算排盘
        result = interface.calculate_and_save(1990, 3, 15, 8, "女")
        
        # 显示结果
        interface.print_result(result)
        
        print(f"\n📁 文件保存位置: paipan_outputs/")
        print("💡 提示: 可以查看生成的 .txt 文件获得完整排盘结果")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")

if __name__ == "__main__":
    main()
