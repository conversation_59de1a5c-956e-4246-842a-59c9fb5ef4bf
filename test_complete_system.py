#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统端到端测试 - 确保所有通路都是通的
"""

import sys
import os
sys.path.append('.')

def test_basic_components():
    """测试基础组件"""
    print("🔧 测试基础组件")
    print("-" * 50)

    results = {}

    # 1. 配置系统
    try:
        from config.settings import get_config, validate_config
        config = get_config()
        validation = validate_config()

        print(f"✅ 配置系统: API密钥配置正常")
        print(f"   模型: {config.llm.model_name}")
        print(f"   验证: {'通过' if validation['valid'] else '失败'}")
        results['config'] = True
    except Exception as e:
        print(f"❌ 配置系统失败: {e}")
        results['config'] = False

    # 2. 会话管理
    try:
        from core.chat.session_manager import SessionManager
        session_manager = SessionManager()
        session = session_manager.get_session("test_session")
        context = session_manager.get_conversation_context("test_session")

        print(f"✅ 会话管理: 正常工作")
        print(f"   会话ID: {session['id']}")
        print(f"   上下文字段: {len(context)} 个")
        results['session'] = True
    except Exception as e:
        print(f"❌ 会话管理失败: {e}")
        results['session'] = False

    # 3. LLM客户端
    try:
        from core.nlu.llm_client import LLMClient
        llm_client = LLMClient()

        # 简单测试
        test_response = llm_client.chat_completion([
            {"role": "user", "content": "你好，请简单回复"}
        ], max_tokens=50)

        if test_response:
            print(f"✅ LLM客户端: 正常工作")
            print(f"   响应: {test_response[:50]}...")
            results['llm'] = True
        else:
            print(f"❌ LLM客户端: 无响应")
            results['llm'] = False
    except Exception as e:
        print(f"❌ LLM客户端失败: {e}")
        results['llm'] = False

    # 4. 算法模块
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        ziwei_calc = RealZiweiCalculator()
        ziwei_result = ziwei_calc.calculate_chart(1988, 6, 1, 12, "男")

        if "error" not in ziwei_result:
            print(f"✅ 紫薇算法: 正常工作")
            print(f"   宫位数: {len(ziwei_result.get('palaces', {}))}")
            results['ziwei'] = True
        else:
            print(f"❌ 紫薇算法错误: {ziwei_result['error']}")
            results['ziwei'] = False
    except Exception as e:
        print(f"❌ 紫薇算法失败: {e}")
        results['ziwei'] = False

    # 5. 工具选择器
    try:
        from core.tools.tool_selector import ToolSelector
        tool_selector = ToolSelector()
        tools = tool_selector.get_available_tools()

        print(f"✅ 工具选择器: 正常工作")
        print(f"   可用工具: {len(tools)} 个")
        results['tools'] = True
    except Exception as e:
        print(f"❌ 工具选择器失败: {e}")
        results['tools'] = False

    return results

def test_semantic_understanding_flow():
    """测试语义理解流程"""
    print("\n🧠 测试语义理解流程")
    print("-" * 50)

    try:
        from core.nlu.llm_client import LLMClient
        from core.chat.session_manager import SessionManager

        llm_client = LLMClient()
        session_manager = SessionManager()
        session_id = "semantic_test"

        # 测试用例
        test_cases = [
            {
                "message": "我想看紫薇斗数",
                "expected_intent": "ziwei"
            },
            {
                "message": "我1988年6月1日午时出生，男，想算命",
                "expected_intent": ["ziwei", "bazi", "general"]
            },
            {
                "message": "你好",
                "expected_intent": "chat"
            }
        ]

        success_count = 0
        for i, test_case in enumerate(test_cases, 1):
            message = test_case["message"]
            expected = test_case["expected_intent"]

            print(f"\n测试 {i}: {message}")

            # 为每个测试使用独立的会话ID，避免上下文污染
            test_session_id = f"{session_id}_{i}"
            context = session_manager.get_conversation_context(test_session_id)

            # 意图识别
            result = llm_client.intent_recognition(message, context)

            if result and result.get("intent") != "error":
                intent = result["intent"]
                confidence = result["confidence"]
                entities = result.get("entities", {})

                print(f"   意图: {intent} (置信度: {confidence:.2f})")
                if entities:
                    print(f"   实体: {entities}")

                # 检查结果
                if isinstance(expected, list):
                    is_correct = intent in expected
                else:
                    is_correct = intent == expected

                if is_correct:
                    print("   ✅ 识别正确")
                    success_count += 1
                else:
                    print("   ❌ 识别错误")

                # 更新会话
                message_record = {
                    "user_message": message,
                    "intent": result,
                    "timestamp": "now"
                }
                session_manager.update_session(test_session_id, {}, message_record)

            else:
                print("   ❌ 识别失败")

        print(f"\n语义理解测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count >= len(test_cases) * 0.7  # 70%通过率

    except Exception as e:
        print(f"❌ 语义理解流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selection_flow():
    """测试工具选择流程"""
    print("\n🛠️ 测试工具选择流程")
    print("-" * 50)

    try:
        from core.tools.tool_selector import ToolSelector

        selector = ToolSelector()

        # 测试完整信息的紫薇斗数
        print("测试紫薇斗数工具选择...")
        ziwei_intent = {
            "intent": "ziwei",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }

        result = selector.select_tool(ziwei_intent, {})

        if result.get("success"):
            result_data = result.get("result", {})
            if result_data.get("type") == "ziwei_analysis":
                calc_result = result_data.get("calculation_result", {})
                if "error" not in calc_result:
                    print(f"✅ 紫薇斗数: 计算成功，{len(calc_result.get('palaces', {}))} 个宫位")
                    ziwei_success = True
                else:
                    print(f"❌ 紫薇斗数计算错误: {calc_result['error']}")
                    ziwei_success = False
            else:
                print(f"⚠️ 紫薇斗数结果类型: {result_data.get('type')}")
                ziwei_success = False
        else:
            print(f"❌ 紫薇斗数工具选择失败: {result.get('error')}")
            ziwei_success = False

        # 测试缺少信息的情况
        print("\n测试缺少信息处理...")
        incomplete_intent = {
            "intent": "bazi",
            "confidence": 0.8,
            "entities": {
                "birth_year": "1990"
            }
        }

        result = selector.select_tool(incomplete_intent, {})

        if result.get("success"):
            result_data = result.get("result", {})
            if result_data.get("type") == "entity_collection":
                print("✅ 缺少信息处理: 正确提示用户补充信息")
                incomplete_success = True
            else:
                print(f"⚠️ 缺少信息处理结果: {result_data.get('type')}")
                incomplete_success = False
        else:
            print(f"❌ 缺少信息处理失败: {result.get('error')}")
            incomplete_success = False

        # 测试聊天工具
        print("\n测试聊天工具...")
        chat_intent = {
            "intent": "chat",
            "confidence": 0.9,
            "entities": {}
        }

        result = selector.select_tool(chat_intent, {})

        if result.get("success"):
            result_data = result.get("result", {})
            if result_data.get("type") == "chat_response":
                print("✅ 聊天工具: 正常响应")
                chat_success = True
            else:
                print(f"⚠️ 聊天工具结果: {result_data.get('type')}")
                chat_success = False
        else:
            print(f"❌ 聊天工具失败: {result.get('error')}")
            chat_success = False

        return ziwei_success and incomplete_success and chat_success

    except Exception as e:
        print(f"❌ 工具选择流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_conversation_flow():
    """测试完整对话流程"""
    print("\n💬 测试完整对话流程")
    print("-" * 50)

    try:
        from core.nlu.llm_client import LLMClient
        from core.tools.tool_selector import ToolSelector
        from core.chat.session_manager import SessionManager

        # 创建组件
        llm_client = LLMClient()
        tool_selector = ToolSelector()
        session_manager = SessionManager()

        session_id = "complete_flow_test"

        # 模拟完整对话流程
        conversation_steps = [
            {
                "step": "问候",
                "message": "你好",
                "expected_flow": "chat"
            },
            {
                "step": "表达需求",
                "message": "我想算命",
                "expected_flow": "general"
            },
            {
                "step": "提供信息",
                "message": "我1988年6月1日午时出生，男，想看紫薇斗数",
                "expected_flow": "ziwei_analysis"
            }
        ]

        success_steps = 0

        for i, step in enumerate(conversation_steps, 1):
            step_name = step["step"]
            message = step["message"]
            expected_flow = step["expected_flow"]

            print(f"\n步骤 {i}: {step_name}")
            print(f"用户: {message}")

            # 1. 获取上下文
            context = session_manager.get_conversation_context(session_id)

            # 2. 意图识别
            intent_result = llm_client.intent_recognition(message, context)

            if intent_result and intent_result.get("intent") != "error":
                intent = intent_result["intent"]
                confidence = intent_result["confidence"]

                print(f"意图识别: {intent} (置信度: {confidence:.2f})")

                # 3. 工具选择
                tool_result = tool_selector.select_tool(intent_result, context)

                if tool_result.get("success"):
                    result_data = tool_result.get("result", {})
                    result_type = result_data.get("type", "unknown")

                    print(f"工具执行: {result_type}")

                    # 检查是否符合预期
                    if expected_flow == "chat" and result_type == "chat_response":
                        print("✅ 聊天流程正常")
                        success_steps += 1
                    elif expected_flow == "general" and result_type in ["entity_collection", "clarification"]:
                        print("✅ 一般咨询流程正常")
                        success_steps += 1
                    elif expected_flow == "ziwei_analysis" and result_type == "ziwei_analysis":
                        calc_result = result_data.get("calculation_result", {})
                        if "error" not in calc_result:
                            print(f"✅ 紫薇分析流程正常，{len(calc_result.get('palaces', {}))} 个宫位")
                            success_steps += 1
                        else:
                            print(f"❌ 紫薇计算错误: {calc_result['error']}")
                    else:
                        print(f"⚠️ 流程不符合预期: 期望 {expected_flow}, 实际 {result_type}")

                    # 4. 更新会话
                    message_record = {
                        "user_message": message,
                        "intent": intent_result,
                        "tool_result": tool_result,
                        "timestamp": "now"
                    }

                    context_updates = {}
                    entities = intent_result.get("entities", {})
                    if entities:
                        birth_info = {}
                        for key, value in entities.items():
                            if key.startswith("birth_") and value:
                                birth_info[key.replace("birth_", "")] = value
                            elif key == "gender" and value:
                                birth_info["gender"] = value

                        if birth_info:
                            context_updates["birth_info"] = birth_info

                    session_manager.update_session(session_id, context_updates, message_record)
                    print("会话状态已更新")

                else:
                    print(f"❌ 工具执行失败: {tool_result.get('error')}")
            else:
                print("❌ 意图识别失败")

        print(f"\n完整对话流程测试结果: {success_steps}/{len(conversation_steps)} 步骤成功")

        # 检查最终会话状态
        final_context = session_manager.get_conversation_context(session_id)
        print(f"最终会话状态:")
        print(f"  总消息数: {final_context['total_messages']}")
        print(f"  历史记录: {len(final_context['recent_history'])} 条")
        if final_context.get('birth_info'):
            print(f"  出生信息: {final_context['birth_info']}")

        return success_steps >= len(conversation_steps) * 0.8  # 80%成功率

    except Exception as e:
        print(f"❌ 完整对话流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface():
    """测试Web界面"""
    print("\n🌐 测试Web界面")
    print("-" * 50)

    try:
        # 检查Web界面文件是否存在
        web_files = [
            "web_demo/prompt_web.py",
            "web_demo/__init__.py"
        ]

        missing_files = []
        for file_path in web_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            print(f"❌ Web界面文件缺失: {missing_files}")
            return False

        print("✅ Web界面文件存在")

        # 检查Streamlit是否可用
        try:
            import streamlit
            print("✅ Streamlit模块可用")
        except ImportError:
            print("❌ Streamlit模块未安装")
            return False

        print("✅ Web界面基础检查通过")
        print("   注意: Web界面需要手动启动测试")
        print("   命令: streamlit run web_demo/prompt_web.py")

        return True

    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 完整系统端到端测试")
    print("=" * 80)
    print("目标: 确保所有通路都是通的")
    print("=" * 80)

    # 执行所有测试
    test_results = {}

    # 1. 基础组件测试
    basic_results = test_basic_components()
    test_results["基础组件"] = all(basic_results.values())

    # 2. 语义理解流程测试
    test_results["语义理解流程"] = test_semantic_understanding_flow()

    # 3. 工具选择流程测试
    test_results["工具选择流程"] = test_tool_selection_flow()

    # 4. 完整对话流程测试
    test_results["完整对话流程"] = test_complete_conversation_flow()

    # 5. Web界面测试
    test_results["Web界面"] = test_web_interface()

    # 汇总结果
    print("\n📊 完整系统测试结果")
    print("=" * 80)

    all_passed = True
    for test_name, passed in test_results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有通路测试全部通过！系统完全正常！")
        print("\n🎯 验证完成的功能:")
        print("  ✅ 配置系统和API密钥")
        print("  ✅ 会话管理和上下文记忆")
        print("  ✅ LLM语义理解")
        print("  ✅ 紫薇斗数算法")
        print("  ✅ 工具选择和路由")
        print("  ✅ 端到端对话流程")
        print("  ✅ Web界面基础")
        print("\n🚀 系统已准备好进行下一步开发！")
    else:
        print("💥 部分通路存在问题，需要修复后再继续")
        print("\n🔧 建议检查:")
        for test_name, passed in test_results.items():
            if not passed:
                print(f"  - {test_name}")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
