#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人性化算命Web界面 - 支持分段式交互的真人算命师体验
"""

import streamlit as st
import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 设置页面配置
st.set_page_config(
    page_title="智能算命AI - 人性化体验",
    page_icon="🔮",
    layout="wide"
)

# 导入人性化算命引擎
try:
    from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
    ENGINE_AVAILABLE = True
except ImportError as e:
    st.error(f"人性化算命引擎导入失败: {e}")
    ENGINE_AVAILABLE = False

# 初始化引擎
@st.cache_resource
def init_humanized_engine():
    """初始化人性化算命引擎"""
    if not ENGINE_AVAILABLE:
        return None
    
    try:
        engine = HumanizedFortuneEngine()
        st.success("✅ 人性化算命引擎初始化成功")
        return engine
    except Exception as e:
        st.error(f"引擎初始化失败: {e}")
        return None

def display_response_with_typing(response_content: str, response_type: str):
    """显示带有打字效果的响应"""
    # 根据响应类型设置不同的图标和样式
    type_icons = {
        "chat": "💬",
        "info_collection": "📝", 
        "chart_presentation": "🔮",
        "analysis_intro": "📋",
        "aspect_analysis": "🎯",
        "detailed_analysis": "📊",
        "interaction_check": "❓",
        "synthesis": "🔄",
        "conclusion": "✨",
        "error": "❌"
    }
    
    icon = type_icons.get(response_type, "🤖")
    
    # 创建响应容器
    with st.container():
        # 显示图标和类型
        if response_type in ["interaction_check"]:
            st.markdown(f"### {icon} 互动时间")
        elif response_type in ["chart_presentation"]:
            st.markdown(f"### {icon} 排盘结果")
        elif response_type in ["synthesis"]:
            st.markdown(f"### {icon} 综合分析")
        elif response_type in ["conclusion"]:
            st.markdown(f"### {icon} 分析总结")
        
        # 显示内容
        st.markdown(response_content)
        
        # 添加分隔线（除了最后的结论）
        if response_type not in ["conclusion", "error"]:
            st.markdown("---")

def process_user_input(user_input: str, engine, session_id: str):
    """处理用户输入并显示分段响应"""
    if not user_input.strip():
        return
    
    # 显示用户消息
    with st.chat_message("user"):
        st.markdown(user_input)
    
    # 添加到消息历史
    st.session_state.messages.append({"role": "user", "content": user_input})
    
    # 处理用户消息
    with st.chat_message("assistant"):
        with st.spinner("🤔 正在思考..."):
            responses = engine.process_user_message(user_input, session_id)
        
        if responses:
            # 逐个显示响应，模拟真人交流的节奏
            for i, response in enumerate(responses):
                response_type = response.get("type", "general")
                response_content = response.get("content", "")
                
                # 添加短暂延迟，模拟思考时间
                if i > 0:
                    time.sleep(0.5)
                
                # 显示响应
                display_response_with_typing(response_content, response_type)
                
                # 如果是互动检查点，暂停等待用户响应
                if response_type == "interaction_check":
                    st.info("💡 您可以随时提问，或者让我继续分析")
            
            # 将所有响应合并添加到消息历史
            combined_response = "\n\n".join([r.get("content", "") for r in responses])
            st.session_state.messages.append({
                "role": "assistant", 
                "content": combined_response,
                "response_count": len(responses)
            })
        else:
            st.error("抱歉，处理您的消息时出现了问题。")

def main():
    """主界面"""
    st.title("🔮 智能算命AI - 人性化体验")
    st.markdown("### 真人算命师般的自然交流体验")
    st.markdown("---")
    
    # 初始化引擎
    engine = init_humanized_engine()
    
    if not engine:
        st.error("❌ 系统初始化失败，无法提供服务")
        st.info("请检查系统配置和依赖")
        return
    
    # 侧边栏信息
    with st.sidebar:
        st.markdown("## 🎯 人性化特性")
        st.markdown("✅ 分段式自然对话")
        st.markdown("✅ 口语化表达")
        st.markdown("✅ 互动检查点")
        st.markdown("✅ 随时打断提问")
        st.markdown("✅ 真人交流节奏")
        
        st.markdown("---")
        st.markdown("## 📊 对话体验")
        st.markdown("🔮 **排盘展示** - 先看结果")
        st.markdown("📋 **分析引导** - 明确方向")
        st.markdown("🎯 **分段分析** - 逐步深入")
        st.markdown("❓ **互动检查** - 随时提问")
        st.markdown("🔄 **综合总结** - 整体把握")
        
        st.markdown("---")
        st.markdown("## 🎭 使用示例")
        st.markdown("👤 **您**: 你好")
        st.markdown("🤖 **AI**: 欢迎！我来为您分析命理...")
        st.markdown("👤 **您**: 我想看紫薇斗数")
        st.markdown("🤖 **AI**: 请提供出生信息...")
        st.markdown("👤 **您**: 1988年6月1日午时男")
        st.markdown("🤖 **AI**: 15段分析...")
        
        # 清除会话按钮
        if st.button("🗑️ 开始新的算命"):
            if "session_id" in st.session_state:
                del st.session_state.session_id
            if "messages" in st.session_state:
                st.session_state.messages = []
            st.rerun()
    
    # 初始化会话
    if "session_id" not in st.session_state:
        st.session_state.session_id = f"humanized_web_{int(datetime.now().timestamp())}"
    
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    # 显示欢迎信息
    if not st.session_state.messages:
        with st.chat_message("assistant"):
            st.markdown("### 🔮 欢迎来到智能算命AI")
            st.markdown("我是您的专属算命师，会用真人般的自然方式为您分析命理。")
            st.markdown("**特色体验**：")
            st.markdown("- 🗣️ 分段式对话，不是一次性输出")
            st.markdown("- ❓ 每个分析后都可以提问")
            st.markdown("- 🔄 随时打断，深入探讨")
            st.markdown("- 💬 口语化表达，贴近真人交流")
            st.markdown("---")
            st.markdown("请告诉我您想了解什么，比如：")
            st.markdown("- 你好")
            st.markdown("- 我想算命")
            st.markdown("- 我想看紫薇斗数")
    
    # 显示对话历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            
            # 显示响应统计
            if message["role"] == "assistant" and "response_count" in message:
                st.caption(f"📊 本次回复包含 {message['response_count']} 个分段")
    
    # 用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        process_user_input(prompt, engine, st.session_state.session_id)
    
    # 底部信息
    st.markdown("---")
    with st.expander("💡 使用提示", expanded=False):
        st.markdown("**如何获得最佳体验**：")
        st.markdown("1. **自然交流**：用您平时说话的方式提问")
        st.markdown("2. **逐步提供信息**：可以分步骤提供出生信息")
        st.markdown("3. **随时提问**：看到互动检查点时，可以随时提问")
        st.markdown("4. **深入探讨**：对任何分析都可以要求详细解释")
        st.markdown("5. **打断对话**：不用等分析完，随时可以插话")
        
        st.markdown("**示例对话流程**：")
        st.markdown("```")
        st.markdown("您: 你好")
        st.markdown("AI: 欢迎！我来为您分析命理...")
        st.markdown("AI: 请告诉我您的出生信息...")
        st.markdown("")
        st.markdown("您: 我1988年6月1日午时出生，男，想看紫薇斗数")
        st.markdown("AI: 好的，我已经为您排好了紫薇斗数命盘...")
        st.markdown("AI: 接下来我会分几个方面来解读...")
        st.markdown("AI: 关于性格特质...")
        st.markdown("AI: 关于这个方面，您有什么想问的吗？")
        st.markdown("")
        st.markdown("您: 我的财运怎么样？")
        st.markdown("AI: 好的，我先回答您这个问题...")
        st.markdown("```")

if __name__ == "__main__":
    main()
