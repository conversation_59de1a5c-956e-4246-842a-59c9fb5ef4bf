#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的LLM语义理解测试 - 验证非关键词匹配的智能理解
"""

import sys
import os
sys.path.append('.')

def test_semantic_understanding():
    """测试真正的语义理解能力"""
    print("🧠 测试真正的LLM语义理解")
    print("=" * 60)
    print("目标：验证系统能通过语义理解而非关键词匹配来识别意图")
    print()
    
    try:
        from core.nlu.llm_client import LLMClient
        
        client = LLMClient()
        print("✅ LLM客户端创建成功")
        print()
        
        # 设计专门的语义理解测试用例 - 这些都不包含明显的关键词
        semantic_test_cases = [
            {
                "message": "我想了解一下我的命运走向",
                "expected_category": "算命相关",
                "description": "没有明确关键词，但表达了算命需求"
            },
            {
                "message": "能帮我看看我这辈子会怎么样吗？",
                "expected_category": "算命相关", 
                "description": "口语化表达，隐含算命意图"
            },
            {
                "message": "我对中国传统的那种用星星排盘的方法很感兴趣",
                "expected_category": "紫薇相关",
                "description": "描述性语言，没有直接说紫薇斗数"
            },
            {
                "message": "我想用我的生辰来分析一下我的性格和未来",
                "expected_category": "算命相关",
                "description": "描述需求而非直接说算命"
            },
            {
                "message": "听说可以通过出生时间看出很多东西，我想试试",
                "expected_category": "算命相关",
                "description": "间接表达，需要理解上下文"
            },
            {
                "message": "我想问个事情，但需要先起个卦",
                "expected_category": "六爻相关",
                "description": "没有说六爻，但表达了起卦需求"
            },
            {
                "message": "我1990年3月15日早上8点出生，女，想了解自己",
                "expected_category": "算命相关",
                "description": "提供出生信息，隐含算命需求"
            },
            {
                "message": "今天天气真不错啊",
                "expected_category": "普通聊天",
                "description": "明显的日常对话"
            },
            {
                "message": "你能做什么？",
                "expected_category": "普通聊天",
                "description": "询问功能，非算命需求"
            },
            {
                "message": "我想知道我和我男朋友合不合适",
                "expected_category": "算命相关",
                "description": "感情咨询，隐含合婚算命需求"
            }
        ]
        
        print("🔍 开始语义理解测试")
        print("-" * 60)
        
        success_count = 0
        total_count = len(semantic_test_cases)
        
        for i, test_case in enumerate(semantic_test_cases, 1):
            message = test_case["message"]
            expected_category = test_case["expected_category"]
            description = test_case["description"]
            
            print(f"\n测试 {i}/{total_count}:")
            print(f"消息: {message}")
            print(f"期望: {expected_category}")
            print(f"说明: {description}")
            
            # 调用LLM进行语义理解
            result = client.intent_recognition(message)
            
            if result and result.get("intent") != "error":
                intent = result.get("intent", "unknown")
                confidence = result.get("confidence", 0.0)
                reasoning = result.get("reasoning", "无")
                
                print(f"识别结果: {intent} (置信度: {confidence:.2f})")
                print(f"LLM推理: {reasoning}")
                
                # 检查实体提取
                entities = result.get("entities", {})
                if entities and any(v for v in entities.values() if v):
                    print(f"提取实体: {entities}")
                
                # 验证语义理解是否正确
                is_correct = False
                if expected_category == "算命相关":
                    is_correct = intent in ["ziwei", "bazi", "liuyao", "general"]
                elif expected_category == "紫薇相关":
                    is_correct = intent == "ziwei"
                elif expected_category == "六爻相关":
                    is_correct = intent == "liuyao"
                elif expected_category == "普通聊天":
                    is_correct = intent == "chat"
                
                if is_correct:
                    print("✅ 语义理解正确")
                    success_count += 1
                else:
                    print("❌ 语义理解错误")
                    
            else:
                print("❌ LLM服务不可用或识别失败")
                if result and result.get("error"):
                    print(f"错误: {result['error']}")
        
        print(f"\n📊 语义理解测试结果")
        print("=" * 60)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"准确率: {success_count/total_count*100:.1f}%")
        
        if success_count >= total_count * 0.7:  # 70%以上算成功
            print("🎉 语义理解测试通过！系统具备真正的智能理解能力")
            return True
        else:
            print("💥 语义理解测试失败，需要改进LLM提示词或模型")
            return False
            
    except Exception as e:
        print(f"❌ 语义理解测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_awareness():
    """测试上下文感知能力"""
    print("\n🔗 测试上下文感知能力")
    print("=" * 60)
    
    try:
        from core.nlu.llm_client import LLMClient
        from core.chat.session_manager import SessionManager
        
        client = LLMClient()
        session_manager = SessionManager()
        session_id = "context_test_session"
        
        # 模拟多轮对话，测试上下文理解
        conversation_flow = [
            {
                "message": "你好",
                "expected": "chat",
                "description": "初始问候"
            },
            {
                "message": "我想了解一下自己",
                "expected": "general",
                "description": "模糊表达，应理解为算命需求"
            },
            {
                "message": "我是1985年出生的",
                "expected": "general", 
                "description": "提供部分信息，结合上下文应理解为算命"
            },
            {
                "message": "用那种传统的方法",
                "expected": "general",
                "description": "指代不明，需要结合上下文理解"
            },
            {
                "message": "换个角度看看",
                "expected": "general",
                "description": "延续性请求，需要上下文理解"
            }
        ]
        
        print("开始上下文感知测试...")
        
        for i, turn in enumerate(conversation_flow, 1):
            message = turn["message"]
            expected = turn["expected"]
            description = turn["description"]
            
            print(f"\n轮次 {i}: {message}")
            print(f"说明: {description}")
            
            # 获取当前上下文
            context = session_manager.get_conversation_context(session_id)
            
            # 进行意图识别
            result = client.intent_recognition(message, context)
            
            if result and result.get("intent") != "error":
                intent = result.get("intent")
                confidence = result.get("confidence", 0.0)
                reasoning = result.get("reasoning", "")
                
                print(f"识别: {intent} (置信度: {confidence:.2f})")
                print(f"推理: {reasoning}")
                
                # 更新会话上下文
                message_record = {
                    "user_message": message,
                    "intent": intent,
                    "confidence": confidence,
                    "timestamp": "now"
                }
                session_manager.update_session(session_id, {}, message_record)
                
            else:
                print("❌ 识别失败")
        
        print("\n✅ 上下文感知测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 上下文感知测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 真正的LLM语义理解验证测试")
    print("=" * 80)
    print("重要说明：此测试验证系统是否具备真正的语义理解能力")
    print("而不是简单的关键词匹配或正则表达式匹配")
    print("=" * 80)
    
    # 执行测试
    results = []
    
    # 1. 语义理解测试
    results.append(("语义理解能力", test_semantic_understanding()))
    
    # 2. 上下文感知测试  
    results.append(("上下文感知", test_context_awareness()))
    
    # 汇总结果
    print("\n📊 最终测试结果")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 真正的LLM语义理解验证成功！")
        print("✅ 系统具备智能语义理解能力，不依赖关键词匹配")
        print("✅ 能够理解用户的真实意图和隐含需求")
        print("✅ 具备上下文感知和多轮对话理解能力")
    else:
        print("💥 语义理解验证失败")
        print("❌ 系统可能仍在使用关键词匹配或规则匹配")
        print("❌ 需要改进LLM提示词设计和响应处理")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
