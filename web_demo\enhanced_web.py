#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版算命Web界面 - 集成高级提示词管理器的专业体验
"""

import streamlit as st
import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 设置页面配置
st.set_page_config(
    page_title="智能算命AI - 专业增强版",
    page_icon="🔮",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 导入增强版组件
try:
    from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
    from core.nlu.enhanced_llm_client import EnhancedLLMClient
    from core.prompts.advanced_prompt_manager import AdvancedPromptManager, PromptContext, AnalysisDepth, PromptQuality
    from core.storage.session_manager import SessionManager, UserSettings, ChatMessage
    ENGINE_AVAILABLE = True
except ImportError as e:
    st.error(f"增强版组件导入失败: {e}")
    ENGINE_AVAILABLE = False

# 自定义CSS样式
def load_custom_css():
    """加载自定义CSS样式"""
    st.markdown("""
    <style>
    /* 主题适配 - 支持深色和浅色模式 */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .main-header h1 {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .main-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    /* 响应卡片 - 深色主题友好 */
    .response-card {
        background: rgba(255, 255, 255, 0.05);
        border-left: 4px solid #667eea;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 8px;
        backdrop-filter: blur(10px);
    }

    /* 分析区域 - 改善对比度 */
    .analysis-section {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        color: inherit;
    }

    /* 深色模式下的文本颜色 */
    .analysis-section p, .analysis-section div {
        color: inherit !important;
    }

    /* 质量徽章 - 增强对比度 */
    .quality-badge {
        display: inline-block;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        margin: 0.25rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .quality-basic {
        background: linear-gradient(45deg, #ffc107, #ffb300);
        color: #000;
        border: 1px solid #e0a800;
    }
    .quality-professional {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: #fff;
        border: 1px solid #1e7e34;
    }
    .quality-premium {
        background: linear-gradient(45deg, #6f42c1, #8e44ad);
        color: #fff;
        border: 1px solid #5a2d91;
    }

    /* 深度指示器 */
    .depth-indicator {
        background: linear-gradient(45deg, #17a2b8, #20c997);
        color: white;
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-size: 0.85rem;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
    }

    /* 统计容器 */
    .stats-container {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 1rem;
        margin: 1rem 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    /* 欢迎信息样式 */
    .welcome-section {
        background: rgba(102, 126, 234, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    /* 功能特性列表 */
    .feature-list {
        background: rgba(255, 255, 255, 0.03);
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }

    /* 响应类型标题 */
    .response-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.3);
    }

    .response-title h3 {
        margin: 0;
        color: inherit;
    }

    /* 互动提示 */
    .interaction-hint {
        background: rgba(23, 162, 184, 0.1);
        border: 1px solid rgba(23, 162, 184, 0.3);
        border-radius: 8px;
        padding: 0.8rem;
        margin: 1rem 0;
        color: inherit;
    }

    /* 分隔线样式 */
    .custom-divider {
        border: none;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.5), transparent);
        margin: 1.5rem 0;
    }

    /* 侧边栏样式优化 */
    .sidebar-section {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    </style>
    """, unsafe_allow_html=True)

# 初始化增强版引擎
@st.cache_resource
def init_enhanced_engine():
    """初始化增强版算命引擎"""
    if not ENGINE_AVAILABLE:
        return None, None, None, None

    try:
        # 初始化各个组件
        humanized_engine = HumanizedFortuneEngine()
        enhanced_llm = EnhancedLLMClient()
        prompt_manager = AdvancedPromptManager()
        session_manager = SessionManager()

        st.success("✅ 增强版算命引擎初始化成功")
        return humanized_engine, enhanced_llm, prompt_manager, session_manager
    except Exception as e:
        st.error(f"引擎初始化失败: {e}")
        return None, None, None, None

def display_quality_settings(session_manager, session_id):
    """显示质量设置面板"""
    st.sidebar.markdown("## ⚙️ 分析设置")

    # 加载当前会话设置
    current_settings = None
    if session_id:
        session_data = session_manager.load_session(session_id)
        if session_data:
            current_settings = session_data.settings

    # 分析深度设置
    depth_options = {
        "简要分析 (300-500字)": "brief",
        "标准分析 (800-1200字)": "standard",
        "详细分析 (2000-3000字)": "detailed",
        "全面分析 (3000-5000字)": "comprehensive"
    }

    # 获取当前深度设置的索引
    current_depth = current_settings.analysis_depth if current_settings else "standard"
    depth_index = list(depth_options.values()).index(current_depth) if current_depth in depth_options.values() else 1

    selected_depth = st.sidebar.selectbox(
        "📊 分析深度",
        options=list(depth_options.keys()),
        index=depth_index,
        help="选择分析的详细程度",
        key="analysis_depth_select"
    )

    # 质量等级设置
    quality_options = {
        "基础版 (通俗易懂)": "basic",
        "专业版 (理论扎实)": "professional",
        "高级版 (大师水准)": "premium"
    }

    # 获取当前质量设置的索引
    current_quality = current_settings.quality_level if current_settings else "professional"
    quality_index = list(quality_options.values()).index(current_quality) if current_quality in quality_options.values() else 1

    selected_quality = st.sidebar.selectbox(
        "🎯 质量等级",
        options=list(quality_options.keys()),
        index=quality_index,
        help="选择分析的专业程度",
        key="quality_level_select"
    )

    # 个性化设置
    st.sidebar.markdown("### 🎨 个性化偏好")

    focus_positive = st.sidebar.checkbox(
        "重点强调积极方面",
        value=current_settings.focus_positive if current_settings else True,
        key="focus_positive_check"
    )
    include_timing = st.sidebar.checkbox(
        "包含时间节点",
        value=current_settings.include_timing if current_settings else True,
        key="include_timing_check"
    )
    practical_advice = st.sidebar.checkbox(
        "提供实用建议",
        value=current_settings.practical_advice if current_settings else True,
        key="practical_advice_check"
    )

    # 构建设置字典
    settings = {
        "analysis_depth": depth_options[selected_depth],
        "quality_level": quality_options[selected_quality],
        "preferences": {
            "focus_positive": focus_positive,
            "include_timing": include_timing,
            "practical_advice": practical_advice
        }
    }

    # 如果设置有变化，保存到会话
    if session_id and current_settings:
        new_settings = UserSettings(
            analysis_depth=settings["analysis_depth"],
            quality_level=settings["quality_level"],
            focus_positive=settings["preferences"]["focus_positive"],
            include_timing=settings["preferences"]["include_timing"],
            practical_advice=settings["preferences"]["practical_advice"]
        )

        # 检查是否有变化
        if (new_settings.analysis_depth != current_settings.analysis_depth or
            new_settings.quality_level != current_settings.quality_level or
            new_settings.focus_positive != current_settings.focus_positive or
            new_settings.include_timing != current_settings.include_timing or
            new_settings.practical_advice != current_settings.practical_advice):

            # 更新设置
            session_manager.update_settings(session_id, new_settings)

    return settings

def display_enhanced_response(response_content: str, response_type: str,
                            quality_level: str = "professional",
                            analysis_depth: str = "standard"):
    """显示增强版响应"""
    # 响应类型图标和标题
    type_config = {
        "chat": {"icon": "💬", "title": "对话交流", "color": "#17a2b8"},
        "info_collection": {"icon": "📝", "title": "信息收集", "color": "#ffc107"},
        "chart_presentation": {"icon": "🔮", "title": "排盘展示", "color": "#6f42c1"},
        "analysis_intro": {"icon": "📋", "title": "分析引导", "color": "#28a745"},
        "aspect_analysis": {"icon": "🎯", "title": "方面分析", "color": "#fd7e14"},
        "detailed_analysis": {"icon": "📊", "title": "详细解读", "color": "#dc3545"},
        "interaction_check": {"icon": "❓", "title": "互动检查", "color": "#6c757d"},
        "synthesis": {"icon": "🔄", "title": "综合分析", "color": "#20c997"},
        "conclusion": {"icon": "✨", "title": "分析总结", "color": "#6f42c1"},
        "error": {"icon": "❌", "title": "错误信息", "color": "#dc3545"}
    }

    config = type_config.get(response_type, {"icon": "🤖", "title": "AI回复", "color": "#6c757d"})

    # 创建响应卡片
    with st.container():
        # 标题栏 - 使用自定义样式
        quality_text = {"basic": "基础", "professional": "专业", "premium": "高级"}[quality_level]
        depth_text = {"brief": "简要", "standard": "标准", "detailed": "详细", "comprehensive": "全面"}[analysis_depth]

        st.markdown(f"""
        <div class="response-title">
            <h3>{config['icon']} {config['title']}</h3>
            <div style="margin-left: auto; display: flex; gap: 0.5rem;">
                <span class="quality-badge quality-{quality_level}">
                    {quality_text}
                </span>
                <span class="depth-indicator">
                    {depth_text}
                </span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # 内容区域 - 改善文本显示
        # 处理换行和格式
        formatted_content = response_content.replace('\n', '<br>')

        st.markdown(f"""
        <div class="analysis-section">
            <div style="line-height: 1.6; font-size: 1rem;">
                {formatted_content}
            </div>
        </div>
        """, unsafe_allow_html=True)

        # 互动提示
        if response_type == "interaction_check":
            st.markdown("""
            <div class="interaction-hint">
                💡 您可以随时提问，或者让我继续分析
            </div>
            """, unsafe_allow_html=True)

        # 自定义分隔线
        if response_type not in ["conclusion", "error"]:
            st.markdown('<hr class="custom-divider">', unsafe_allow_html=True)

def process_enhanced_user_input(user_input: str, engines: tuple, session_id: str, settings: Dict[str, Any]):
    """处理用户输入并显示增强版响应"""
    humanized_engine, enhanced_llm, prompt_manager, session_manager = engines

    if not user_input.strip():
        return

    # 显示用户消息
    with st.chat_message("user"):
        st.markdown(user_input)

    # 添加到消息历史
    st.session_state.messages.append({"role": "user", "content": user_input})

    # 保存用户消息到持久化存储
    session_manager.add_message(
        session_id=session_id,
        role="user",
        content=user_input,
        message_type="text",
        metadata={"timestamp": datetime.now().isoformat()}
    )

    # 更新用户画像
    if "user_id" in st.session_state:
        prompt_manager.update_user_profile(st.session_state.user_id, {
            "preferences": settings["preferences"]
        })

    # 处理用户消息
    with st.chat_message("assistant"):
        with st.spinner("🤔 正在使用增强版AI分析..."):
            responses = humanized_engine.process_user_message(user_input, session_id)

        if responses:
            # 显示响应统计
            st.info(f"📊 本次分析包含 {len(responses)} 个分段，使用{settings['quality_level']}级别分析")

            # 逐个显示增强版响应
            for i, response in enumerate(responses):
                response_type = response.get("type", "general")
                response_content = response.get("content", "")

                # 添加短暂延迟
                if i > 0:
                    time.sleep(0.3)

                # 显示增强版响应
                display_enhanced_response(
                    response_content,
                    response_type,
                    settings["quality_level"],
                    settings["analysis_depth"]
                )

            # 添加到消息历史
            combined_response = "\n\n".join([r.get("content", "") for r in responses])
            st.session_state.messages.append({
                "role": "assistant",
                "content": combined_response,
                "response_count": len(responses),
                "settings": settings
            })

            # 保存助手回复到持久化存储
            session_manager.add_message(
                session_id=session_id,
                role="assistant",
                content=combined_response,
                message_type="analysis",
                metadata={
                    "response_count": len(responses),
                    "settings": settings,
                    "timestamp": datetime.now().isoformat()
                }
            )

            # 更新统计信息
            if "stats" not in st.session_state:
                st.session_state.stats = {"total_responses": 0, "total_segments": 0}

            st.session_state.stats["total_responses"] += 1
            st.session_state.stats["total_segments"] += len(responses)

        else:
            st.error("抱歉，处理您的消息时出现了问题。")

def display_session_history_panel(session_manager, current_user_id):
    """显示会话历史面板"""
    st.sidebar.markdown("## 📚 会话历史")

    if current_user_id:
        # 获取用户的会话列表
        user_sessions = session_manager.get_user_sessions(current_user_id, limit=5)

        if user_sessions:
            st.sidebar.markdown("**最近会话**")
            for i, session_info in enumerate(user_sessions):
                session_id = session_info["session_id"]
                message_count = session_info["message_count"]
                updated_time = session_info["updated_at"][:16].replace("T", " ")

                # 创建会话按钮
                if st.sidebar.button(
                    f"💬 会话 {i+1} ({message_count}条消息)",
                    help=f"更新时间: {updated_time}",
                    key=f"load_session_{session_id}"
                ):
                    # 加载选中的会话
                    st.session_state.session_id = session_id
                    st.session_state.user_id = current_user_id

                    # 加载会话数据
                    session_data = session_manager.load_session(session_id)
                    if session_data:
                        # 恢复消息历史
                        st.session_state.messages = []
                        for msg in session_data.messages:
                            st.session_state.messages.append({
                                "role": msg.role,
                                "content": msg.content,
                                "timestamp": msg.timestamp,
                                "metadata": msg.metadata
                            })

                        # 恢复统计信息
                        st.session_state.stats = session_data.stats

                        st.success(f"已加载会话: {session_id[:8]}...")
                        st.rerun()
        else:
            st.sidebar.markdown("*暂无历史会话*")

    # 导出当前会话
    if st.sidebar.button("📤 导出当前会话", help="导出为JSON格式"):
        if "session_id" in st.session_state:
            export_data = session_manager.export_session(st.session_state.session_id)
            if export_data:
                import json
                json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
                st.sidebar.download_button(
                    label="⬇️ 下载会话数据",
                    data=json_str,
                    file_name=f"session_{st.session_state.session_id[:8]}.json",
                    mime="application/json"
                )

def display_statistics_panel(prompt_manager, session_manager, session_id):
    """显示统计面板"""
    st.sidebar.markdown("## 📈 使用统计")

    # 当前会话统计
    if session_id:
        session_data = session_manager.load_session(session_id)
        if session_data:
            stats = session_data.stats
            st.sidebar.markdown(f"**当前会话**")
            st.sidebar.markdown(f"- 总消息数: {stats.get('total_messages', 0)}")
            st.sidebar.markdown(f"- 分析次数: {stats.get('total_analyses', 0)}")

    # 系统统计
    if "stats" in st.session_state:
        stats = st.session_state.stats
        st.sidebar.markdown(f"**本次统计**")
        st.sidebar.markdown(f"- 总回复数: {stats.get('total_responses', 0)}")
        st.sidebar.markdown(f"- 总分段数: {stats.get('total_segments', 0)}")

    # 提示词统计
    try:
        prompt_stats = prompt_manager.get_prompt_statistics()
        st.sidebar.markdown(f"**系统统计**")
        st.sidebar.markdown(f"- 提示词类型: {prompt_stats['total_prompts']}")
        st.sidebar.markdown(f"- 总使用次数: {prompt_stats['total_usage']}")
    except:
        pass

def main():
    """主界面"""
    # 加载自定义样式
    load_custom_css()

    # 主标题
    st.markdown("""
    <div class="main-header">
        <h1>🔮 智能算命AI - 专业增强版</h1>
        <p>集成高级提示词管理器的专业算命体验</p>
    </div>
    """, unsafe_allow_html=True)

    # 初始化引擎
    engines = init_enhanced_engine()

    if not all(engines):
        st.error("❌ 系统初始化失败，无法提供服务")
        st.info("请检查系统配置和依赖")
        return

    humanized_engine, enhanced_llm, prompt_manager, session_manager = engines

    # 初始化会话
    if "session_id" not in st.session_state or "user_id" not in st.session_state:
        # 创建新会话
        user_id = f"user_{int(datetime.now().timestamp())}"
        user_settings = UserSettings()
        session_data = session_manager.create_session(user_id, user_settings)
        st.session_state.session_id = session_data.session_id
        st.session_state.user_id = session_data.user_id
        st.session_state.messages = []
        st.session_state.stats = {"total_responses": 0, "total_segments": 0}

    # 侧边栏设置
    settings = display_quality_settings(session_manager, st.session_state.session_id)

    # 显示会话历史面板
    display_session_history_panel(session_manager, st.session_state.user_id)

    # 显示统计面板
    display_statistics_panel(prompt_manager, session_manager, st.session_state.session_id)

    # 侧边栏功能说明
    with st.sidebar:
        st.markdown("---")
        st.markdown("""
        <div class="sidebar-section">
            <h3>🌟 增强版特性</h3>
            <ul style="list-style: none; padding: 0;">
                <li>✅ 智能提示词管理</li>
                <li>✅ 多层次质量控制</li>
                <li>✅ 个性化分析偏好</li>
                <li>✅ 用户画像学习</li>
                <li>✅ 实时统计监控</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

        # 会话管理按钮
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🗑️ 新会话", type="primary"):
                # 创建新会话
                user_id = st.session_state.user_id
                user_settings = UserSettings()
                session_data = session_manager.create_session(user_id, user_settings)
                st.session_state.session_id = session_data.session_id
                st.session_state.messages = []
                st.session_state.stats = {"total_responses": 0, "total_segments": 0}
                st.rerun()

        with col2:
            if st.button("💾 保存会话"):
                if "session_id" in st.session_state:
                    st.success("会话已自动保存")
                else:
                    st.warning("无活动会话")

    # 初始化会话
    if "session_id" not in st.session_state:
        st.session_state.session_id = f"enhanced_web_{int(datetime.now().timestamp())}"

    if "user_id" not in st.session_state:
        st.session_state.user_id = f"user_{int(datetime.now().timestamp())}"

    if "messages" not in st.session_state:
        st.session_state.messages = []

    # 显示欢迎信息
    if not st.session_state.messages:
        with st.chat_message("assistant"):
            st.markdown("""
            <div class="welcome-section">
                <h3>🔮 欢迎来到智能算命AI增强版</h3>
                <p>我是您的专属AI算命师，配备了最先进的提示词管理系统。</p>
            </div>
            """, unsafe_allow_html=True)

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("""
                <div class="feature-list">
                    <h4>🎯 专业特性</h4>
                    <ul>
                        <li>🧠 智能提示词优化</li>
                        <li>📊 多层次分析深度</li>
                        <li>🎨 个性化表达风格</li>
                        <li>📈 持续学习改进</li>
                    </ul>
                </div>
                """, unsafe_allow_html=True)

            with col2:
                st.markdown("""
                <div class="feature-list">
                    <h4>💬 交互体验</h4>
                    <ul>
                        <li>🗣️ 分段式自然对话</li>
                        <li>❓ 随时互动提问</li>
                        <li>🔄 智能上下文理解</li>
                        <li>✨ 专业话术融入</li>
                    </ul>
                </div>
                """, unsafe_allow_html=True)

            st.markdown('<hr class="custom-divider">', unsafe_allow_html=True)

            st.markdown("""
            <div style="text-align: center; padding: 1rem;">
                <p><strong>请在右侧设置您的偏好，然后开始对话：</strong></p>
                <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap; margin-top: 1rem;">
                    <span style="background: rgba(102, 126, 234, 0.2); padding: 0.5rem 1rem; border-radius: 20px;">💬 你好</span>
                    <span style="background: rgba(102, 126, 234, 0.2); padding: 0.5rem 1rem; border-radius: 20px;">🔮 我想算命</span>
                    <span style="background: rgba(102, 126, 234, 0.2); padding: 0.5rem 1rem; border-radius: 20px;">⭐ 紫薇斗数分析</span>
                </div>
            </div>
            """, unsafe_allow_html=True)

    # 显示对话历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

            # 显示响应统计和设置
            if message["role"] == "assistant" and "response_count" in message:
                col1, col2 = st.columns(2)
                with col1:
                    st.caption(f"📊 包含 {message['response_count']} 个分段")
                with col2:
                    if "settings" in message:
                        settings_info = message["settings"]
                        st.caption(f"⚙️ {settings_info['quality_level']} | {settings_info['analysis_depth']}")

    # 用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        process_enhanced_user_input(prompt, engines, st.session_state.session_id, settings)

if __name__ == "__main__":
    main()
