#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试增强版八字算法
"""

def debug_enhanced_bazi():
    """调试增强版八字算法"""
    print("🔍 调试增强版八字算法")
    print("=" * 50)
    
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        
        calc = EnhancedBaziCalculator()
        print("✅ 算法实例创建成功")
        
        # 测试基础功能
        print("\n📊 测试基础功能:")
        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")
        
        print(f"结果类型: {type(result)}")
        print(f"结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if result.get("success"):
            print("✅ 基础功能正常")
            
            analysis = result.get("analysis", {})
            print(f"分析键: {list(analysis.keys())}")
            
            # 检查新功能
            if "dayun" in analysis:
                print("✅ 大运分析已添加")
                dayun = analysis["dayun"]
                print(f"  大运键: {list(dayun.keys())}")
            else:
                print("❌ 大运分析缺失")
            
            if "nayin" in analysis:
                print("✅ 纳音分析已添加")
            else:
                print("❌ 纳音分析缺失")
        else:
            print(f"❌ 基础功能失败: {result.get('error')}")
        
        # 测试传统分析
        print("\n📜 测试传统分析:")
        try:
            result2 = calc.calculate_with_traditional_analysis(1988, 6, 1, 11, "男")
            if result2.get("success"):
                print("✅ 传统分析正常")
                if "traditional_analysis" in result2:
                    print("✅ 传统分析数据已添加")
                else:
                    print("❌ 传统分析数据缺失")
            else:
                print(f"❌ 传统分析失败: {result2.get('error')}")
        except Exception as e:
            print(f"❌ 传统分析异常: {e}")
        
        # 测试量化分析
        print("\n📊 测试量化分析:")
        try:
            result3 = calc.calculate_with_quantitative_analysis(1988, 6, 1, 11, "男")
            if result3.get("success"):
                print("✅ 量化分析正常")
                if "quantitative_analysis" in result3:
                    print("✅ 量化分析数据已添加")
                else:
                    print("❌ 量化分析数据缺失")
            else:
                print(f"❌ 量化分析失败: {result3.get('error')}")
        except Exception as e:
            print(f"❌ 量化分析异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 增强版八字算法调试")
    print("=" * 60)
    
    success = debug_enhanced_bazi()
    
    if success:
        print("\n✅ 调试完成")
    else:
        print("\n❌ 调试失败")

if __name__ == "__main__":
    main()
