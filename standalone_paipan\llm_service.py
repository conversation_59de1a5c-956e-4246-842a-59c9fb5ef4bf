#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM分析服务模块
用于12角度分析和知识库互动
"""

import requests
import json
import time
from typing import Dict, Any, List, Optional
from database import PaipanDatabase

class LLMService:
    """LLM分析服务"""

    def __init__(self, api_key: str = None, base_url: str = None, model_name: str = None):
        """
        初始化LLM服务

        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 模型名称
        """
        # 使用用户提供的API配置
        self.api_key = api_key or "sk-trklwkxjmgnrgbuxhwcanaxkzwtuqevslzhoikwgwajnkqjz"
        self.base_url = base_url or "https://api.siliconflow.cn/v1/chat/completions"
        # 使用固定的模型名称
        self.model_name = model_name or "deepseek-ai/DeepSeek-V3"

        self.timeout = 300  # 5分钟超时
        self.max_retries = 3  # 重试3次

        print(f"✅ LLM服务初始化完成: {self.model_name}")
        print(f"🔄 重试设置: 最多重试 {self.max_retries} 次")

    def chat_completion(self, messages: List[Dict[str, str]], temperature: float = 0.3,
                       max_tokens: int = 8000) -> Optional[str]:
        """
        调用LLM进行对话，支持重试机制

        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数

        Returns:
            LLM回复内容
        """
        # 对同一模型重试最多3次
        for attempt in range(1, self.max_retries + 1):
            try:
                print(f"🔄 第 {attempt} 次尝试调用模型: {self.model_name}")

                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                payload = {
                    "model": self.model_name,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "stream": False
                }

                response = requests.post(
                    self.base_url,
                    headers=headers,
                    json=payload,
                    timeout=self.timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    print(f"✅ 第 {attempt} 次尝试成功")
                    return content
                else:
                    print(f"❌ 第 {attempt} 次尝试失败: {response.status_code} - {response.text}")
                    if attempt == self.max_retries:
                        print(f"❌ 已达到最大重试次数 ({self.max_retries})")
                        return None
                    else:
                        print(f"⏳ 等待 2 秒后重试...")
                        import time
                        time.sleep(2)
                        continue

            except Exception as e:
                print(f"❌ 第 {attempt} 次尝试异常: {e}")
                if attempt == self.max_retries:
                    print(f"❌ 已达到最大重试次数 ({self.max_retries})")
                    return None
                else:
                    print(f"⏳ 等待 2 秒后重试...")
                    import time
                    time.sleep(2)
                    continue

        return None

    def generate_response(self, prompt: str, temperature: float = 0.3, max_tokens: int = 8000) -> Optional[str]:
        """
        生成LLM回复（简化版本，用于六爻分析等场景）

        Args:
            prompt: 提示词
            temperature: 温度参数
            max_tokens: 最大token数

        Returns:
            LLM回复内容
        """
        messages = [
            {"role": "user", "content": prompt}
        ]
        return self.chat_completion(messages, temperature, max_tokens)

    def analyze_single_angle(self, record_data: Dict[str, Any], angle_key: str,
                           angle_name: str, description: str) -> Dict[str, Any]:
        """
        分析单个角度

        Args:
            record_data: 排盘数据
            angle_key: 角度标识符
            angle_name: 角度中文名称
            description: 角度描述

        Returns:
            分析结果
        """
        try:
            print(f"🔍 开始分析: {angle_name}")
            start_time = time.time()

            # 构建分析提示词
            system_prompt = self._build_system_prompt()
            user_prompt = self._build_analysis_prompt(record_data, angle_name, description)

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # 调用LLM分析
            content = self.chat_completion(messages, temperature=0.3, max_tokens=8000)

            analysis_time = time.time() - start_time

            if content and len(content) > 500:
                word_count = len(content)
                print(f"✅ {angle_name}分析完成: {word_count}字, 耗时{analysis_time:.1f}秒")

                return {
                    'success': True,
                    'content': content,
                    'word_count': word_count,
                    'analysis_time': analysis_time,
                    'angle_key': angle_key,
                    'angle_name': angle_name
                }
            else:
                print(f"❌ {angle_name}分析失败: 内容过短或为空")
                return {
                    'success': False,
                    'error': '分析内容过短或为空',
                    'content': content or '',
                    'word_count': len(content) if content else 0,
                    'angle_key': angle_key,
                    'angle_name': angle_name
                }

        except Exception as e:
            print(f"❌ {angle_name}分析异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '',
                'word_count': 0,
                'angle_key': angle_key,
                'angle_name': angle_name
            }

    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一位资深的命理分析师，精通紫薇斗数和八字命理。

【分析要求】
1. 基于提供的紫薇斗数和八字数据进行专业分析
2. 分析要详细深入，字数控制在4000-5000字
3. 语言通俗易懂，避免过于专业的术语
4. 提供具体的建议和指导
5. 分析要有逻辑性和条理性
6. 必须引用具体的星曜配置和八字信息作为依据

【分析结构】
1. 核心特征分析（基于主要星曜和八字配置）
2. 详细解读（结合宫位和五行分析）
3. 现实表现（具体的生活体现）
4. 发展趋势（未来的变化和机遇）
5. 实用建议（具体的改善方法）

请确保分析内容完整、专业、实用。"""

    def _build_analysis_prompt(self, record_data: Dict[str, Any], angle_name: str, description: str) -> str:
        """构建分析提示词"""
        birth_info = record_data.get('birth_info', {})
        ziwei_data = record_data.get('ziwei_analysis', {})
        bazi_data = record_data.get('bazi_analysis', {})

        prompt = f"""请分析以下命盘的【{angle_name}】方面：

【基本信息】
- 出生时间：{birth_info.get('datetime', '未知')}
- 性别：{birth_info.get('gender', '未知')}
- 生肖：{birth_info.get('zodiac', '未知')}
- 星座：{birth_info.get('sign', '未知')}

【紫薇斗数】
"""

        # 添加紫薇宫位信息
        if 'palaces' in ziwei_data:
            prompt += "十二宫配置：\n"
            for palace_name, palace_data in ziwei_data['palaces'].items():
                if isinstance(palace_data, dict):
                    major_stars = palace_data.get('major_stars', [])
                    minor_stars = palace_data.get('minor_stars', [])
                    prompt += f"- {palace_name}: 主星{major_stars}, 辅星{minor_stars}\n"

        # 添加八字信息
        if 'bazi_info' in bazi_data:
            bazi_info = bazi_data['bazi_info']
            prompt += f"\n【八字命理】\n"
            prompt += f"年柱：{bazi_info.get('year_pillar', ['', ''])}\n"
            prompt += f"月柱：{bazi_info.get('month_pillar', ['', ''])}\n"
            prompt += f"日柱：{bazi_info.get('day_pillar', ['', ''])}\n"
            prompt += f"时柱：{bazi_info.get('hour_pillar', ['', ''])}\n"

            if 'analysis' in bazi_data:
                analysis = bazi_data['analysis']
                day_master = analysis.get('day_master', {})
                prompt += f"日主：{day_master.get('gan', '')}({day_master.get('element', '')})\n"
                prompt += f"强弱：{day_master.get('strength', '')}\n"

        prompt += f"\n【分析重点】\n请重点分析【{angle_name}】方面：{description}\n"
        prompt += "\n请提供4000-5000字的详细分析，包含具体的星曜依据和实用建议。"

        return prompt

    def chat_with_knowledge(self, record_data: Dict[str, Any], user_question: str,
                           chat_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        基于知识库进行聊天

        Args:
            record_data: 排盘数据
            user_question: 用户问题
            chat_history: 聊天历史

        Returns:
            聊天回复结果
        """
        try:
            print(f"💬 处理用户问题: {user_question[:50]}...")
            start_time = time.time()

            # 构建知识库内容
            knowledge_text = self._build_knowledge_base(record_data)

            # 构建系统提示词
            system_prompt = self._build_chat_system_prompt(record_data, knowledge_text)

            # 构建消息历史
            messages = [{"role": "system", "content": system_prompt}]

            # 添加聊天历史（最近5轮）
            if chat_history:
                for chat in chat_history[-5:]:
                    messages.append({"role": "user", "content": chat.get('user_message', '')})
                    messages.append({"role": "assistant", "content": chat.get('assistant_response', '')})

            # 添加当前问题
            messages.append({"role": "user", "content": user_question})

            # 调用LLM
            response = self.chat_completion(messages, temperature=0.7, max_tokens=4000)

            response_time = time.time() - start_time

            if response:
                print(f"✅ 聊天回复完成: {len(response)}字, 耗时{response_time:.1f}秒")
                return {
                    'success': True,
                    'response': response,
                    'response_time_ms': int(response_time * 1000),
                    'knowledge_used': {'knowledge_length': len(knowledge_text)}
                }
            else:
                return {
                    'success': False,
                    'error': 'LLM回复为空',
                    'response': '抱歉，我暂时无法回答您的问题，请稍后重试。'
                }

        except Exception as e:
            print(f"❌ 聊天处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': '抱歉，处理您的问题时出现了错误。'
            }

    def _build_knowledge_base(self, record_data: Dict[str, Any]) -> str:
        """构建知识库文本"""
        birth_info = record_data.get('birth_info', {})
        ziwei_data = record_data.get('ziwei_analysis', {})
        bazi_data = record_data.get('bazi_analysis', {})

        knowledge = f"""【专属知识库】

【基本信息】
- 出生时间：{birth_info.get('datetime', '未知')}
- 性别：{birth_info.get('gender', '未知')}
- 阳历：{birth_info.get('solar', '未知')}
- 阴历：{birth_info.get('lunar', '未知')}
- 生肖：{birth_info.get('zodiac', '未知')}
- 星座：{birth_info.get('sign', '未知')}

【紫薇斗数宫位】
"""

        # 添加宫位信息
        if 'palaces' in ziwei_data:
            for palace_name, palace_data in ziwei_data['palaces'].items():
                if isinstance(palace_data, dict):
                    knowledge += f"★ {palace_name}: "
                    major_stars = palace_data.get('major_stars', [])
                    minor_stars = palace_data.get('minor_stars', [])
                    if major_stars:
                        knowledge += f"主星{major_stars} "
                    if minor_stars:
                        knowledge += f"辅星{minor_stars}"
                    knowledge += "\n"

        # 添加八字信息
        if 'bazi_info' in bazi_data:
            bazi_info = bazi_data['bazi_info']
            knowledge += f"\n【八字四柱】\n"
            knowledge += f"★ 年柱：{' '.join(bazi_info.get('year_pillar', ['', '']))}\n"
            knowledge += f"★ 月柱：{' '.join(bazi_info.get('month_pillar', ['', '']))}\n"
            knowledge += f"★ 日柱：{' '.join(bazi_info.get('day_pillar', ['', '']))}\n"
            knowledge += f"★ 时柱：{' '.join(bazi_info.get('hour_pillar', ['', '']))}\n"

            if 'analysis' in bazi_data:
                analysis = bazi_data['analysis']
                day_master = analysis.get('day_master', {})
                knowledge += f"★ 日主：{day_master.get('gan', '')}({day_master.get('element', '')})\n"
                knowledge += f"★ 强弱：{day_master.get('strength', '')}\n"

        knowledge += "\n【重要提示】请在回答中引用上述★标记的具体信息"

        return knowledge

    def _build_chat_system_prompt(self, record_data: Dict[str, Any], knowledge_text: str) -> str:
        """构建聊天系统提示词"""
        birth_info = record_data.get('birth_info', {})

        return f"""你是一位专业的命理分析师，现在要基于用户的专属知识库回答问题。

【用户基本信息】
- 出生时间：{birth_info.get('datetime', '未知')}
- 性别：{birth_info.get('gender', '未知')}

{knowledge_text}

【回答要求】
1. 优先使用知识库中的信息回答
2. 结合紫薇斗数和八字命理提供具体依据
3. 语言通俗易懂，像朋友聊天一样
4. 回答要具体实用，避免空话套话
5. 回答要完整详细，控制在600-1000字之间
6. 确保回答有完整的结尾，不要突然截断
7. 回答结构要完整：开头-分析-建议-总结

请根据用户的专属知识库，专业而亲切地回答问题。回答必须完整，有始有终。"""
