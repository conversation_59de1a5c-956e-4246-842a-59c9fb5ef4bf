#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础接口类 - 统一所有接入方式的基础抽象类
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class BaseInterface(ABC):
    """基础接口类 - 定义所有接入方式的标准接口"""
    
    def __init__(self, conversation_engine):
        """
        初始化基础接口
        
        Args:
            conversation_engine: 对话引擎实例
        """
        self.conversation_engine = conversation_engine
        self.created_at = datetime.now()
        
        # 接口统计
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0
        }
        
        logger.info(f"{self.__class__.__name__} 初始化完成")
    
    @abstractmethod
    def handle_message(self, session_id: str, message: str) -> str:
        """
        处理消息的抽象方法 - 子类必须实现
        
        Args:
            session_id: 会话ID
            message: 用户消息
            
        Returns:
            处理结果字符串
        """
        pass
    
    def process_message(self, session_id: str, message: str, 
                       user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        通用消息处理逻辑
        
        Args:
            session_id: 会话ID
            message: 用户消息
            user_id: 用户ID
            
        Returns:
            处理结果字典
        """
        start_time = datetime.now()
        self.stats["total_requests"] += 1
        
        try:
            # 调用对话引擎处理消息
            result = self.conversation_engine.process_message(
                session_id=session_id,
                message=message,
                user_id=user_id
            )
            
            # 更新统计
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_stats(True, processing_time)
            
            logger.debug(f"消息处理成功 - 会话: {session_id}, 耗时: {processing_time:.2f}秒")
            
            return result
            
        except Exception as e:
            # 更新统计
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_stats(False, processing_time)
            
            logger.error(f"消息处理失败 - 会话: {session_id}, 错误: {e}")
            
            return {
                "success": False,
                "session_id": session_id,
                "error": str(e),
                "message": "抱歉，处理您的消息时出现了问题，请稍后重试。",
                "timestamp": datetime.now()
            }
    
    def _update_stats(self, success: bool, response_time: float) -> None:
        """
        更新接口统计信息
        
        Args:
            success: 是否成功
            response_time: 响应时间（秒）
        """
        if success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
        
        # 更新平均响应时间
        total_time = self.stats["average_response_time"] * (self.stats["total_requests"] - 1)
        self.stats["average_response_time"] = (total_time + response_time) / self.stats["total_requests"]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取接口统计信息
        
        Returns:
            统计信息字典
        """
        success_rate = 0
        if self.stats["total_requests"] > 0:
            success_rate = self.stats["successful_requests"] / self.stats["total_requests"] * 100
        
        return {
            "interface_name": self.__class__.__name__,
            "created_at": self.created_at.isoformat(),
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{success_rate:.2f}%",
            "average_response_time": f"{self.stats['average_response_time']:.2f}s"
        }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0
        }
        logger.info(f"{self.__class__.__name__} 统计信息已重置")
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        try:
            # 检查对话引擎是否正常
            if not self.conversation_engine:
                return {
                    "status": "unhealthy",
                    "error": "对话引擎未初始化",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 简单的功能测试
            test_result = self.conversation_engine.process_message("health_check", "测试")
            
            if test_result.get("success"):
                return {
                    "status": "healthy",
                    "interface": self.__class__.__name__,
                    "stats": self.get_stats(),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "degraded",
                    "error": "对话引擎响应异常",
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def format_response_for_platform(self, result: Dict[str, Any], 
                                   platform: str = "generic") -> str:
        """
        根据平台格式化响应
        
        Args:
            result: 处理结果
            platform: 平台类型 (web, wechat, api, etc.)
            
        Returns:
            格式化后的响应字符串
        """
        if not result.get("success"):
            return result.get("message", "处理失败")
        
        message = result.get("message", "")
        
        # 根据平台调整格式
        if platform == "wechat":
            # 微信平台：限制长度，添加表情符号
            if len(message) > 2000:
                message = message[:1900] + "...\n\n💬 回复'继续'查看更多"
            
            # 添加图片提示
            if result.get("tool_result", {}).get("chart_image"):
                message += "\n\n📊 已生成专业图表，请访问网页版查看"
                
        elif platform == "api":
            # API平台：保持原始格式
            pass
            
        elif platform == "web":
            # Web平台：可以包含更多格式化
            pass
        
        return message
    
    def log_interaction(self, session_id: str, user_message: str, 
                       response: str, success: bool) -> None:
        """
        记录交互日志
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            response: 系统响应
            success: 是否成功
        """
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "interface": self.__class__.__name__,
            "session_id": session_id,
            "user_message": user_message[:100] + "..." if len(user_message) > 100 else user_message,
            "response_length": len(response),
            "success": success
        }
        
        if success:
            logger.info(f"交互成功: {log_data}")
        else:
            logger.warning(f"交互失败: {log_data}")
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(created_at={self.created_at})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(requests={self.stats['total_requests']})>"
