#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新架构智能算命Web界面 - 集成智能对话系统
"""

import streamlit as st
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 设置页面配置
st.set_page_config(
    page_title="智能算命AI - 新架构",
    page_icon="🔮",
    layout="wide"
)

# 导入新架构组件
try:
    from core.chat.session_manager import SessionManager
    from core.nlu.llm_client import LLMClient
    from core.tools.tool_selector import ToolSelector
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    st.error(f"新架构组件导入失败: {e}")
    COMPONENTS_AVAILABLE = False

# 初始化组件
@st.cache_resource
def init_components():
    """初始化智能对话组件"""
    if not COMPONENTS_AVAILABLE:
        return None, None, None
    
    try:
        session_manager = SessionManager()
        llm_client = LLMClient()
        tool_selector = ToolSelector()
        
        st.success("✅ 智能对话系统初始化成功")
        return session_manager, llm_client, tool_selector
    except Exception as e:
        st.error(f"组件初始化失败: {e}")
        return None, None, None

# 处理用户消息
def process_user_message(user_message: str, session_id: str, 
                        session_manager, llm_client, tool_selector):
    """处理用户消息的完整流程"""
    try:
        # 1. 获取会话上下文
        with st.spinner("🔍 分析上下文..."):
            context = session_manager.get_conversation_context(session_id)
        
        # 2. 智能意图识别
        with st.spinner("🧠 理解您的需求..."):
            intent_result = llm_client.intent_recognition(user_message, context)
        
        if not intent_result or intent_result.get("intent") == "error":
            return {
                "success": False,
                "message": "抱歉，我无法理解您的需求，请重新描述。",
                "error": "意图识别失败"
            }
        
        intent = intent_result["intent"]
        confidence = intent_result["confidence"]
        entities = intent_result.get("entities", {})
        
        # 显示意图识别结果
        with st.expander("🔍 智能分析结果", expanded=False):
            st.write(f"**识别意图**: {intent}")
            st.write(f"**置信度**: {confidence:.2%}")
            if entities:
                st.write(f"**提取信息**: {entities}")
        
        # 3. 工具选择和执行
        with st.spinner("🛠️ 选择合适的工具..."):
            tool_result = tool_selector.select_tool(intent_result, context)
        
        if not tool_result.get("success"):
            return {
                "success": False,
                "message": f"工具执行失败: {tool_result.get('error')}",
                "error": tool_result.get('error')
            }
        
        # 4. 处理结果
        result_data = tool_result.get("result", {})
        result_type = result_data.get("type", "unknown")
        
        # 5. 更新会话状态
        message_record = {
            "user_message": user_message,
            "intent": intent_result,
            "tool_result": tool_result,
            "timestamp": datetime.now().isoformat()
        }
        
        context_updates = {}
        if entities:
            birth_info = {}
            for key, value in entities.items():
                if key.startswith("birth_") and value:
                    birth_info[key.replace("birth_", "")] = value
                elif key == "gender" and value:
                    birth_info["gender"] = value
            
            if birth_info:
                context_updates["birth_info"] = birth_info
        
        session_manager.update_session(session_id, context_updates, message_record)
        
        # 6. 格式化响应
        response = format_response(result_data, intent_result)
        
        return {
            "success": True,
            "message": response,
            "intent": intent,
            "confidence": confidence,
            "result_type": result_type,
            "tool_result": tool_result
        }
        
    except Exception as e:
        st.error(f"处理过程出错: {e}")
        return {
            "success": False,
            "message": f"系统处理出错: {str(e)}",
            "error": str(e)
        }

def format_response(result_data, intent_result):
    """格式化响应消息"""
    result_type = result_data.get("type", "unknown")
    
    if result_type == "chat_response":
        return result_data.get("message", "您好！")
    
    elif result_type == "entity_collection":
        return result_data.get("message", "请提供更多信息。")
    
    elif result_type == "clarification":
        message = result_data.get("message", "请明确您的需求。")
        suggestions = result_data.get("suggestions", [])
        if suggestions:
            message += "\n\n**可选服务**：\n" + "\n".join([f"• {s}" for s in suggestions])
        return message
    
    elif result_type == "ziwei_analysis":
        return format_ziwei_response(result_data)
    
    elif result_type == "bazi_analysis":
        return format_bazi_response(result_data)
    
    elif result_type == "liuyao_analysis":
        return "🎯 六爻占卜分析功能正在完善中..."
    
    elif result_type == "error":
        return f"❌ {result_data.get('message', '分析出现问题')}"
    
    else:
        return result_data.get("message", "分析完成。")

def format_ziwei_response(result_data):
    """格式化紫薇斗数响应"""
    calc_result = result_data.get("calculation_result", {})
    birth_info = result_data.get("birth_info", {})
    
    if "error" in calc_result:
        return f"❌ 紫薇斗数计算失败: {calc_result['error']}"
    
    response = "# 🔮 紫薇斗数命盘分析\n\n"
    
    # 出生信息
    if birth_info:
        response += "## 📅 出生信息\n"
        response += f"- **出生时间**: {birth_info.get('birth_year')}年{birth_info.get('birth_month')}月{birth_info.get('birth_day')}日 {birth_info.get('birth_hour')}\n"
        response += f"- **性别**: {birth_info.get('gender')}\n\n"
    
    # 命盘信息
    if "palaces" in calc_result:
        palaces = calc_result["palaces"]
        response += f"## 🏰 十二宫位排盘\n"
        response += f"成功排出 **{len(palaces)}** 个宫位的完整命盘\n\n"
        
        # 主要宫位
        important_palaces = ["命宫", "财帛宫", "事业宫", "夫妻宫"]
        for palace_name in important_palaces:
            if palace_name in palaces:
                palace_info = palaces[palace_name]
                response += f"### {palace_name}\n"
                if "主星" in palace_info:
                    response += f"- **主星**: {palace_info['主星']}\n"
                if "地支" in palace_info:
                    response += f"- **地支**: {palace_info['地支']}\n"
                response += "\n"
    
    response += "## 🤖 AI智能分析\n"
    response += "基于真实的紫薇斗数排盘结果，为您提供专业的命理分析。\n\n"
    
    return response

def format_bazi_response(result_data):
    """格式化八字算命响应"""
    calc_result = result_data.get("calculation_result", {})
    birth_info = result_data.get("birth_info", {})
    
    if not calc_result.get("success"):
        return f"❌ 八字计算失败: {calc_result.get('error', '未知错误')}"
    
    response = "# 🎋 八字命理分析\n\n"
    
    # 出生信息
    if birth_info:
        response += "## 📅 出生信息\n"
        response += f"- **出生时间**: {birth_info.get('birth_year')}年{birth_info.get('birth_month')}月{birth_info.get('birth_day')}日 {birth_info.get('birth_hour')}\n"
        response += f"- **性别**: {birth_info.get('gender')}\n\n"
    
    # 八字信息
    if "bazi" in calc_result:
        bazi_info = calc_result["bazi"]
        response += "## 🎋 四柱八字\n"
        response += f"- **年柱**: {bazi_info.get('年柱', 'N/A')}\n"
        response += f"- **月柱**: {bazi_info.get('月柱', 'N/A')}\n"
        response += f"- **日柱**: {bazi_info.get('日柱', 'N/A')}\n"
        response += f"- **时柱**: {bazi_info.get('时柱', 'N/A')}\n\n"
    
    response += "## 🤖 AI智能分析\n"
    response += "基于真实的八字排盘结果，为您提供专业的命理分析。\n\n"
    
    return response

# 主界面
def main():
    st.title("🔮 智能算命AI - 新架构演示")
    st.markdown("---")
    
    # 初始化组件
    session_manager, llm_client, tool_selector = init_components()
    
    if not all([session_manager, llm_client, tool_selector]):
        st.error("❌ 系统组件初始化失败，无法提供服务")
        st.info("请检查系统配置和依赖")
        return
    
    # 侧边栏信息
    with st.sidebar:
        st.markdown("## 🎯 新架构特性")
        st.markdown("✅ 真正的LLM语义理解")
        st.markdown("✅ 智能意图识别")
        st.markdown("✅ 自动工具选择")
        st.markdown("✅ 多轮对话支持")
        st.markdown("✅ 上下文记忆")
        
        st.markdown("---")
        st.markdown("## 📊 系统状态")
        
        # 显示组件状态
        if session_manager:
            st.success("会话管理器: 正常")
        if llm_client:
            st.success("LLM客户端: 正常")
        if tool_selector:
            st.success("工具选择器: 正常")
        
        # 清除会话按钮
        if st.button("🗑️ 清除会话"):
            if "session_id" in st.session_state:
                session_manager.clear_session(st.session_state.session_id)
            if "messages" in st.session_state:
                st.session_state.messages = []
            st.rerun()
    
    # 初始化会话
    if "session_id" not in st.session_state:
        st.session_state.session_id = f"web_session_{int(datetime.now().timestamp())}"
    
    if "messages" not in st.session_state:
        st.session_state.messages = []
    
    # 显示对话历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # 用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        # 显示用户消息
        with st.chat_message("user"):
            st.markdown(prompt)
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # 处理用户消息
        with st.chat_message("assistant"):
            result = process_user_message(
                prompt, 
                st.session_state.session_id,
                session_manager, 
                llm_client, 
                tool_selector
            )
            
            if result["success"]:
                st.markdown(result["message"])
                
                # 显示额外信息
                if result.get("tool_result", {}).get("result", {}).get("type") == "ziwei_analysis":
                    calc_result = result["tool_result"]["result"].get("calculation_result", {})
                    if "chart_image" in calc_result:
                        st.info(f"命盘图表: {calc_result['chart_image']}")
                
            else:
                st.error(result["message"])
            
            # 添加到对话历史
            st.session_state.messages.append({
                "role": "assistant", 
                "content": result["message"]
            })

if __name__ == "__main__":
    main()
