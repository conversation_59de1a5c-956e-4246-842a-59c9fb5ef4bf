#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强负面分析后的效果
"""

import asyncio

async def test_negative_analysis():
    """测试增强负面分析后的效果"""
    print("🔍 测试增强负面分析后的效果")
    print("=" * 60)
    
    try:
        # 1. 清理缓存
        print("1️⃣ 清理缓存")
        import os
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.endswith('.json'):
                    os.remove(os.path.join(cache_dir, file))
        print("✅ 缓存已清理")
        
        # 2. 测试财富分析的负面分析
        print("\n2️⃣ 测试财富分析的负面分析")
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        calculator_agent = FortuneCalculatorAgent()
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        # 获取融合分析数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if not raw_data.get("success"):
            print(f"❌ 融合分析失败: {raw_data.get('error')}")
            return False
        
        print("✅ 融合分析数据获取成功")
        
        # 测试财富分析
        print(f"\n🔍 测试财富分析的负面分析")
        
        try:
            wealth_analysis = await calculator_agent._analyze_single_angle(
                "财富分析", "wealth_fortune", "财运状况、理财投资与财富积累", 
                raw_data, birth_info, "紫薇+八字融合分析"
            )
            
            if wealth_analysis and len(wealth_analysis) > 100:
                word_count = len(wealth_analysis)
                print(f"  ✅ 财富分析成功: {word_count} 字符")
                
                # 检查负面分析内容
                negative_keywords = [
                    "破财", "失败", "风险", "困难", "危机", "陷阱", "不足", "障碍",
                    "缺陷", "问题", "挫折", "损失", "亏损", "贫困", "经济困难",
                    "投资失败", "财务危机", "破产", "负债", "穷困", "财运不佳"
                ]
                
                found_negative = [keyword for keyword in negative_keywords if keyword in wealth_analysis]
                negative_ratio = len(found_negative) / len(negative_keywords) * 100
                
                print(f"    负面关键词覆盖率: {negative_ratio:.1f}% ({len(found_negative)}/{len(negative_keywords)})")
                print(f"    发现的负面词汇: {found_negative[:10]}")  # 显示前10个
                
                # 检查具体的负面分析内容
                negative_checks = {
                    "破财年份预测": any(keyword in wealth_analysis for keyword in ["破财", "损失", "亏损"]),
                    "投资风险分析": any(keyword in wealth_analysis for keyword in ["投资失败", "风险", "陷阱"]),
                    "财务危机预警": any(keyword in wealth_analysis for keyword in ["财务危机", "经济困难", "危机"]),
                    "财运缺陷分析": any(keyword in wealth_analysis for keyword in ["缺陷", "不足", "障碍"]),
                    "解决方案提供": any(keyword in wealth_analysis for keyword in ["解决", "化解", "改善", "规避"])
                }
                
                passed_checks = sum(negative_checks.values())
                total_checks = len(negative_checks)
                
                print(f"    负面分析质量: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
                for check_name, result in negative_checks.items():
                    print(f"      {check_name}: {'✅' if result else '❌'}")
                
                # 保存测试内容
                with open("test_negative_wealth_analysis.txt", "w", encoding="utf-8") as f:
                    f.write("增强负面分析的财富分析测试结果:\n")
                    f.write("=" * 60 + "\n\n")
                    f.write(f"字符数: {word_count}\n")
                    f.write(f"负面关键词覆盖率: {negative_ratio:.1f}%\n")
                    f.write(f"负面分析质量: {passed_checks}/{total_checks}\n")
                    f.write(f"发现的负面词汇: {found_negative}\n")
                    f.write("\n" + "="*60 + "\n")
                    f.write("分析内容:\n")
                    f.write("="*60 + "\n\n")
                    f.write(wealth_analysis)
                
                print(f"    💾 内容已保存到 test_negative_wealth_analysis.txt")
                
                # 分析内容结构
                sections = wealth_analysis.split("##")
                print(f"    内容结构: {len(sections)}个主要部分")
                
                for i, section in enumerate(sections[1:], 1):  # 跳过第一个空部分
                    section_title = section.split("\n")[0].strip()
                    section_length = len(section)
                    has_negative = any(keyword in section for keyword in negative_keywords[:10])
                    print(f"      第{i}部分: {section_title[:30]}... ({section_length}字符) {'🚨' if has_negative else '📝'}")
                
                return True
                
            else:
                print(f"  ❌ 财富分析失败: 内容为空或过短")
                return False
                
        except Exception as e:
            print(f"  ❌ 财富分析异常: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_negative_analysis())
