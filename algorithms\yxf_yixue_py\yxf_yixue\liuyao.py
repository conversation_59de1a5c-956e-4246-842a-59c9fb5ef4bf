#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六爻算卦算法实现
"""

import datetime
import random
from typing import Dict, Any, List

class LiuyaoApi:
    """六爻算卦API"""

    def __init__(self):
        self.liushen = ["青龙", "朱雀", "勾陈", "螣蛇", "白虎", "玄武"]
        self.liuqin = ["子孙", "妻财", "官鬼", "兄弟", "父母"]
        self.wuxing = ["金", "木", "水", "火", "土"]
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

        # 64卦名称（简化版）
        self.gua_names = {
            "111111": "乾为天", "000000": "坤为地", "100010": "水雷屯", "010001": "山水蒙",
            "111010": "水天需", "010111": "天水讼", "010000": "地水师", "000010": "水地比",
            "111011": "风天小畜", "110111": "天泽履", "111000": "地天泰", "000111": "天地否",
            "101111": "天火同人", "111101": "火天大有", "001000": "地山谦", "000100": "雷地豫",
            "100110": "泽雷随", "011001": "山风蛊", "110000": "地泽临", "000011": "风地观",
            "100101": "火雷噬嗑", "101001": "山火贲", "000001": "山地剥", "100000": "地雷复",
            "100111": "天雷无妄", "111001": "山天大畜", "100001": "山雷颐", "011110": "泽风大过",
            "010010": "坎为水", "101101": "离为火", "001110": "泽山咸", "011100": "雷风恒",
            "001111": "天山遁", "111100": "雷天大壮", "000101": "火地晋", "101000": "地火明夷"
        }

    def paipan_by_time(self, year: int, month: int, day: int, hour: int, minute: int = 0) -> Dict[str, Any]:
        """时间起卦"""
        try:
            # 简化的时间起卦算法
            time_sum = year + month + day + hour + minute
            shang_gua = (time_sum % 8) or 8
            xia_gua = ((time_sum + hour) % 8) or 8
            dong_yao = (time_sum % 6) or 6

            # 生成卦象（从下到上：初爻到上爻）
            gua_yao = []
            for i in range(6):
                if i < 3:  # 下卦
                    yao = (xia_gua >> i) & 1
                else:  # 上卦
                    yao = (shang_gua >> (i-3)) & 1
                gua_yao.append(yao)

            # 构建卦象数据
            result = self._build_gua_result(gua_yao, [dong_yao], year, month, day, hour, minute, "时间起卦")
            return result

        except Exception as e:
            return {
                "success": False,
                "error": f"时间起卦失败: {e}"
            }

    def paipan_by_numbers(self, year: int, month: int, day: int, hour: int, num1: int, num2: int) -> Dict[str, Any]:
        """数字起卦"""
        try:
            shang_gua = (num1 % 8) or 8
            xia_gua = (num2 % 8) or 8
            dong_yao = ((num1 + num2) % 6) or 6

            # 生成卦象
            gua_yao = []
            for i in range(6):
                if i < 3:  # 下卦
                    yao = (xia_gua >> i) & 1
                else:  # 上卦
                    yao = (shang_gua >> (i-3)) & 1
                gua_yao.append(yao)

            result = self._build_gua_result(gua_yao, [dong_yao], year, month, day, hour, 0, "数字起卦")
            return result

        except Exception as e:
            return {
                "success": False,
                "error": f"数字起卦失败: {e}"
            }

    def _build_gua_result(self, gua_yao: List[int], dong_yao_list: List[int],
                         year: int, month: int, day: int, hour: int, minute: int, method: str) -> Dict[str, Any]:
        """构建卦象结果"""
        try:
            # 主卦（从下到上）
            zhu_gua_str = ''.join(str(gua_yao[5-i]) for i in range(6))  # 反转顺序
            zhu_gua_name = self.gua_names.get(zhu_gua_str, "未知卦")

            # 变卦（动爻变化后）
            bian_gua_yao = gua_yao.copy()
            for dong_yao in dong_yao_list:
                if 1 <= dong_yao <= 6:
                    bian_gua_yao[dong_yao-1] = 1 - bian_gua_yao[dong_yao-1]

            bian_gua_str = ''.join(str(bian_gua_yao[5-i]) for i in range(6))  # 反转顺序
            bian_gua_name = self.gua_names.get(bian_gua_str, "未知卦")

            # 构建盘面数据（从上到下：上爻到初爻）
            pan_data = {}
            for i in range(6):
                yao_num = 16 - i  # 从上到下：16,15,14,13,12,11
                yao_index = 5 - i  # 对应gua_yao的索引

                pan_data[str(yao_num)] = {
                    "六神": self.liushen[i % 6],
                    "卦爻": "━━━" if gua_yao[yao_index] == 1 else "━ ━",
                    "动爻": "○" if (yao_index + 1) in dong_yao_list else "　",  # 使用全角空格占位
                    "六亲": self.liuqin[i % 5],
                    "纳干": self.tiangan[i % 10],
                    "纳支": self.dizhi[i % 12],
                    "五行": self.wuxing[i % 5],
                    "世应": "世" if i == 2 else ("应" if i == 5 else "　")  # 使用全角空格占位
                }

            return {
                "success": True,
                "method": method,
                "datetime": f"{year}年{month}月{day}日{hour}时{minute}分",
                "raw_result": {
                    "盘": pan_data,
                    "动爻": [str(17-d) for d in dong_yao_list],  # 转换为盘面位置
                    "主卦": zhu_gua_name,
                    "变卦": bian_gua_name
                },
                "formatted_output": self._format_output(pan_data, zhu_gua_name, bian_gua_name, dong_yao_list, year, month, day, hour, minute, method)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"构建卦象失败: {e}"
            }

    def _format_output(self, pan_data: Dict, zhu_gua: str, bian_gua: str, dong_yao_list: List[int],
                      year: int, month: int, day: int, hour: int, minute: int, method: str) -> str:
        """格式化输出"""
        output = f"""
六爻卦象 ({method})
起卦时间: {year}年{month}月{day}日{hour}时{minute}分

主卦: {zhu_gua}    变卦: {bian_gua}

六神  卦爻  动  六亲  纳支  五行  世应
"""

        # 从上到下显示六爻
        for yao_num in ["16", "15", "14", "13", "12", "11"]:
            yao_data = pan_data[yao_num]
            # 确保动爻和世应字段有占位符
            dong_yao = yao_data['动爻'] if yao_data['动爻'] else "　"
            shi_ying = yao_data['世应'] if yao_data['世应'] else "　"
            output += f"{yao_data['六神']}  {yao_data['卦爻']}  {dong_yao:2}  {yao_data['六亲']}  {yao_data['纳干']}{yao_data['纳支']}  {yao_data['五行']}    {shi_ying}\n"

        # 动爻说明
        if dong_yao_list:
            dong_yao_desc = []
            for dong_yao in dong_yao_list:
                yao_pos = 17 - dong_yao  # 转换为盘面位置
                if str(yao_pos) in pan_data:
                    yao_data = pan_data[str(yao_pos)]
                    yao_names = ["初", "二", "三", "四", "五", "上"]
                    yao_name = yao_names[dong_yao-1] if 1 <= dong_yao <= 6 else "未知"
                    dong_yao_desc.append(f"{yao_name}爻{yao_data['六亲']}{yao_data['纳支']}{yao_data['五行']}")

            if dong_yao_desc:
                output += f"\n动爻: {', '.join(dong_yao_desc)}"

        return output

    def paipan(self, datetime_obj, qiguafangfa='标准时间起卦', qiguashuru=None) -> Dict[str, Any]:
        """通用排盘方法（兼容性接口）"""
        try:
            year = datetime_obj.year
            month = datetime_obj.month
            day = datetime_obj.day
            hour = datetime_obj.hour
            minute = datetime_obj.minute

            if qiguafangfa == '两数字起卦' and qiguashuru and len(qiguashuru) >= 2:
                # 数字起卦
                result = self.paipan_by_numbers(year, month, day, hour, qiguashuru[0], qiguashuru[1])
            else:
                # 时间起卦
                result = self.paipan_by_time(year, month, day, hour, minute)

            # 返回raw_result部分，保持兼容性
            if result.get("success"):
                self.current_result = result
                self.P = LiuyaoPan(result)
                return result["raw_result"]
            else:
                raise Exception(result.get("error", "起卦失败"))

        except Exception as e:
            raise Exception(f"排盘失败: {e}")

    def print_pan(self) -> str:
        """打印排盘结果（兼容性接口）"""
        if hasattr(self, 'current_result') and self.current_result:
            return self.current_result.get("formatted_output", "无排盘结果")
        else:
            return "请先进行排盘"

    def get_chuantongfenxi(self) -> Dict[str, Any]:
        """获取传统分析（兼容性接口）"""
        return {
            "传统分析": "基于传统六爻理论的分析结果",
            "用神": "根据问题确定用神",
            "世应": "分析世爻应爻关系",
            "动静": "分析动爻静爻变化",
            "生克": "分析五行生克关系"
        }

class LiuyaoPan:
    """六爻排盘对象（兼容性类）"""

    def __init__(self, result: Dict[str, Any]):
        self.result = result

    def output(self) -> str:
        """输出排盘结果"""
        return self.result.get("formatted_output", "排盘输出失败")

def test_liuyao_api():
    """测试六爻API"""
    print("测试六爻算法...")

    api = LiuyaoApi()

    try:
        # 测试时间起卦
        result1 = api.paipan_by_time(2024, 6, 19, 14, 30)
        if result1.get("success"):
            print("✅ 时间起卦成功")
            print(result1["formatted_output"])
        else:
            print(f"❌ 时间起卦失败: {result1.get('error')}")

        # 测试数字起卦
        result2 = api.paipan_by_numbers(2024, 6, 19, 14, 123, 456)
        if result2.get("success"):
            print("✅ 数字起卦成功")
            print(result2["formatted_output"])
        else:
            print(f"❌ 数字起卦失败: {result2.get('error')}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_liuyao_api()
