#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的紫薇工具迁移测试
避免Unicode和API问题
"""

import sys
import os
sys.path.append('.')

def test_ziwei_core_functions():
    """测试紫薇核心功能"""
    print("阶段3.5：紫薇工具迁移测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试人性化紫薇工具
    print("\n1. 测试人性化紫薇工具...")
    try:
        from core.tools.humanized_ziwei_tool import HumanizedZiweiTool
        
        tool = HumanizedZiweiTool()
        intent = {
            'intent': 'ziwei',
            'entities': {
                'birth_year': '1988',
                'birth_month': '6',
                'birth_day': '1',
                'birth_hour': '午时',
                'gender': '男'
            }
        }
        
        result = tool.execute(intent, {})
        success = result.get('success', False)
        
        if success:
            print("   成功: 人性化紫薇工具正常工作")
            calc_result = result.get('calculation_result', {})
            if calc_result.get('success'):
                raw_result = calc_result.get('raw_result', {})
                palaces_count = len(raw_result.get('palaces', {}))
                print(f"   详情: 生成了{palaces_count}个宫位的命盘")
        else:
            print(f"   失败: {result.get('error', '未知错误')}")
        
        test_results.append(('人性化紫薇工具', success))
        
    except Exception as e:
        print(f"   异常: {e}")
        test_results.append(('人性化紫薇工具', False))
    
    # 2. 测试工具选择器
    print("\n2. 测试工具选择器...")
    try:
        from core.tools.tool_selector import ToolSelector
        
        selector = ToolSelector()
        intent_result = {
            'intent': 'ziwei',
            'confidence': 0.9,
            'entities': {
                'birth_year': '1988',
                'birth_month': '6',
                'birth_day': '1',
                'birth_hour': '午时',
                'gender': '男'
            }
        }
        
        tool_result = selector.select_tool(intent_result, {})
        success = (
            tool_result.get('success', False) and 
            tool_result.get('result', {}).get('success', False)
        )
        
        if success:
            print("   成功: 工具选择器正确路由到紫薇分析")
            print(f"   详情: 工具名称 {tool_result.get('tool_name')}")
        else:
            print(f"   失败: {tool_result.get('error', '未知错误')}")
        
        test_results.append(('工具选择器', success))
        
    except Exception as e:
        print(f"   异常: {e}")
        test_results.append(('工具选择器', False))
    
    # 3. 测试人性化引擎（简化版，避免API调用）
    print("\n3. 测试人性化引擎...")
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        
        # 使用简单的测试消息，避免复杂的API调用
        message = '我想看紫薇斗数命盘，1988年6月1日午时男'
        
        # 只测试意图识别和工具选择部分
        intent_result = engine.llm_client.intent_recognition(message)
        intent_result["original_message"] = message
        
        tool_result = engine.tool_selector.select_tool(intent_result, {})
        
        success = (
            intent_result.get('intent') == 'ziwei' and
            tool_result.get('success', False)
        )
        
        if success:
            print("   成功: 人性化引擎正确识别紫薇意图并执行")
            print(f"   详情: 意图识别为 {intent_result.get('intent')}")
        else:
            print(f"   失败: 意图识别或工具执行失败")
        
        test_results.append(('人性化引擎核心', success))
        
    except Exception as e:
        print(f"   异常: {e}")
        test_results.append(('人性化引擎核心', False))
    
    # 4. 测试紫薇算法可用性
    print("\n4. 测试紫薇算法可用性...")
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator, IZTRO_AVAILABLE
        
        if IZTRO_AVAILABLE:
            calc = RealZiweiCalculator()
            result = calc.calculate_chart(1988, 6, 1, 12, "男")
            
            success = "error" not in result
            
            if success:
                print("   成功: py-iztro算法正常工作")
                print(f"   详情: 生成了{len(result.get('palaces', {}))}个宫位")
            else:
                print(f"   失败: {result.get('error', '计算错误')}")
        else:
            print("   失败: py-iztro模块不可用")
            success = False
        
        test_results.append(('紫薇算法', success))
        
    except Exception as e:
        print(f"   异常: {e}")
        test_results.append(('紫薇算法', False))
    
    # 汇总结果
    print(f"\n阶段3.5紫薇工具迁移测试结果")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "通过" if passed else "失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("成功！阶段3.5紫薇工具迁移完成！")
        print("\n实现的核心功能:")
        print("  - 人性化紫薇工具：统一接口设计")
        print("  - 工具选择器集成：智能路由到紫薇分析")
        print("  - 人性化交互体验：分段式紫薇分析")
        print("  - 真实算法支持：py-iztro算法库")
        print("\nPRD阶段3.5目标达成！")
    else:
        print("部分功能存在问题，需要修复后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = test_ziwei_core_functions()
    print(f"\n测试结果: {'全部通过' if success else '部分失败'}")
    sys.exit(0 if success else 1)
