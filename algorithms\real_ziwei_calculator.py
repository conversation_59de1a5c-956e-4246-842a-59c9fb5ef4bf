#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的紫薇斗数算法 - 基于py-iztro
"""

try:
    import py_iztro
    IZTRO_AVAILABLE = True
    print("✅ py-iztro导入成功")
except ImportError as e:
    IZTRO_AVAILABLE = False
    print(f"❌ py-iztro导入失败: {e}")

class RealZiweiCalculator:
    """真正的紫薇斗数计算器 - 使用iztro算法"""

    def __init__(self):
        if not IZTRO_AVAILABLE:
            raise ImportError("py-iztro未安装")

    def calculate_chart(self, year: int, month: int, day: int, hour: int, gender: str = "男") -> dict:
        """计算真正的紫薇斗数命盘"""
        try:
            # 使用iztro计算
            astro = py_iztro.Astro()

            # 正确的时辰转换 (0-12对应子丑寅卯辰巳午未申酉戌亥)
            time_mapping = {
                (23, 1): 0,   # 子时
                (1, 3): 1,    # 丑时
                (3, 5): 2,    # 寅时
                (5, 7): 3,    # 卯时
                (7, 9): 4,    # 辰时
                (9, 11): 5,   # 巳时
                (11, 13): 6,  # 午时
                (13, 15): 7,  # 未时
                (15, 17): 8,  # 申时
                (17, 19): 9,  # 酉时
                (19, 21): 10, # 戌时
                (21, 23): 11  # 亥时
            }

            time_index = 6  # 默认午时
            for (start, end), index in time_mapping.items():
                if start <= hour < end or (start == 23 and hour >= 23):
                    time_index = index
                    break

            # 格式化日期字符串
            solar_date_str = f"{year}-{month}-{day}"

            # 转换性别格式
            gender_code = "男" if gender == "男" else "女"

            astrolabe = astro.by_solar(
                solar_date_str=solar_date_str,
                time_index=time_index,
                gender=gender_code,
                language="zh-CN"
            )

            # 获取命盘信息
            result = {
                "birth_info": {
                    "solar": f"{year}年{month}月{day}日{hour}时",
                    "lunar": astrolabe.lunar_date,
                    "chinese_date": astrolabe.chinese_date
                },
                "palaces": {},
                "zodiac": astrolabe.zodiac,
                "sign": astrolabe.sign
            }

            # 获取十二宫信息 - 修复宫位映射逻辑
            # 地支顺序：子丑寅卯辰巳午未申酉戌亥
            earthly_branches = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

            # 创建地支到宫位的映射
            branch_to_palace = {}
            for palace in astrolabe.palaces:
                branch_to_palace[palace.earthly_branch] = palace

            # 找到命宫的地支位置
            soul_palace_branch = astrolabe.earthly_branch_of_soul_palace
            soul_palace_index = earthly_branches.index(soul_palace_branch)

            # 十二宫名称（从命宫开始逆时针排列）
            palace_names = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                           "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]

            # 按正确的宫位顺序填充数据
            for i, palace_name in enumerate(palace_names):
                # 计算当前宫位对应的地支索引（逆时针）
                branch_index = (soul_palace_index - i) % 12
                branch = earthly_branches[branch_index]

                if branch in branch_to_palace:
                    palace = branch_to_palace[branch]

                    # 获取该宫的星曜
                    major_stars = []
                    minor_stars = []
                    adjective_stars = []

                    if hasattr(palace, 'major_stars'):
                        major_stars = [star.name for star in palace.major_stars]
                    if hasattr(palace, 'minor_stars'):
                        minor_stars = [star.name for star in palace.minor_stars]
                    if hasattr(palace, 'adjective_stars'):
                        adjective_stars = [star.name for star in palace.adjective_stars]

                    result["palaces"][palace_name] = {
                        "position": branch,
                        "major_stars": major_stars,
                        "minor_stars": minor_stars,
                        "adjective_stars": adjective_stars,
                        "palace_name": palace_name,
                        "is_body_palace": getattr(palace, 'is_body_palace', False)
                    }

            return result

        except Exception as e:
            return {"error": f"iztro计算错误: {str(e)}"}

    def get_palace_analysis(self, chart_data: dict, palace_name: str) -> dict:
        """获取特定宫位的详细分析"""
        if "error" in chart_data:
            return chart_data

        if palace_name not in chart_data["palaces"]:
            return {"error": f"宫位 {palace_name} 不存在"}

        palace = chart_data["palaces"][palace_name]

        analysis = {
            "宫位": palace_name,
            "地支": palace["position"],
            "主星": palace["main_stars"],
            "辅星": palace["aux_stars"],
            "星曜组合": "、".join(palace["main_stars"]) if palace["main_stars"] else "无主星"
        }

        return analysis

def test_real_ziwei():
    """测试真正的紫薇斗数算法"""
    if not IZTRO_AVAILABLE:
        print("❌ py-iztro未安装，无法测试")
        return

    try:
        calc = RealZiweiCalculator()

        # 测试1988年6月1日午时
        result = calc.calculate_chart(1988, 6, 1, 12, "男")

        if "error" in result:
            print(f"❌ 计算错误: {result['error']}")
            return

        print("=== 真正的紫薇斗数排盘结果 ===")
        print(f"出生信息: {result['birth_info']}")
        print("\n十二宫星曜分布:")

        for palace_name, palace_info in result["palaces"].items():
            major = "、".join(palace_info["major_stars"]) if palace_info["major_stars"] else ""
            minor = "、".join(palace_info["minor_stars"]) if palace_info["minor_stars"] else ""
            adjective = "、".join(palace_info["adjective_stars"]) if palace_info["adjective_stars"] else ""

            all_stars = []
            if major: all_stars.append(f"主星:{major}")
            if minor: all_stars.append(f"辅星:{minor}")
            if adjective: all_stars.append(f"煞星:{adjective}")

            stars_display = " | ".join(all_stars) if all_stars else "无星曜"
            body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""

            print(f"{palace_name}({palace_info['position']}){body_mark}: {stars_display}")

        # 重点分析命宫
        if "命宫" in result["palaces"]:
            ming_gong = result["palaces"]["命宫"]
            print(f"\n命宫详细分析: {ming_gong}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_real_ziwei()
