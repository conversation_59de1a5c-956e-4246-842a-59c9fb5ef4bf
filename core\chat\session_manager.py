#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理器 - 管理用户会话状态和上下文记忆
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class SessionManager:
    """会话管理器 - 支持多轮对话和上下文记忆"""
    
    def __init__(self, max_history: int = 20, session_timeout: int = 3600):
        """
        初始化会话管理器
        
        Args:
            max_history: 最大历史记录数量
            session_timeout: 会话超时时间（秒）
        """
        self.sessions = {}  # 内存存储，后期可扩展为Redis
        self.max_history = max_history
        self.session_timeout = session_timeout
        
        logger.info(f"会话管理器初始化完成 - 最大历史: {max_history}, 超时: {session_timeout}秒")
    
    def get_session(self, session_id: str) -> Dict[str, Any]:
        """
        获取会话信息，如果不存在则创建新会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息字典
        """
        # 清理过期会话
        self._cleanup_expired_sessions()
        
        if session_id not in self.sessions:
            self.sessions[session_id] = self._create_new_session(session_id)
            logger.info(f"创建新会话: {session_id}")
        else:
            # 更新最后活跃时间
            self.sessions[session_id]["last_active"] = datetime.now()
            
        return self.sessions[session_id]
    
    def _create_new_session(self, session_id: str) -> Dict[str, Any]:
        """创建新会话"""
        return {
            "id": session_id,
            "created_at": datetime.now(),
            "last_active": datetime.now(),
            "context": {
                "user_info": {},           # 用户基本信息
                "current_topic": None,     # 当前话题
                "birth_info": None,        # 出生信息
                "preferences": {},         # 用户偏好
                "last_analysis_type": None # 上次分析类型
            },
            "history": [],                 # 对话历史
            "metadata": {
                "total_messages": 0,
                "tools_used": [],
                "session_type": "chat"
            }
        }
    
    def update_session(self, session_id: str, context_updates: Dict[str, Any], 
                      message_record: Dict[str, Any]) -> None:
        """
        更新会话信息
        
        Args:
            session_id: 会话ID
            context_updates: 上下文更新
            message_record: 消息记录
        """
        session = self.get_session(session_id)
        
        # 更新上下文
        if context_updates:
            session["context"].update(context_updates)
        
        # 添加消息记录
        if message_record:
            session["history"].append(message_record)
            session["metadata"]["total_messages"] += 1
            
            # 记录使用的工具
            if "tool_used" in message_record:
                tool_name = message_record["tool_used"]
                if tool_name not in session["metadata"]["tools_used"]:
                    session["metadata"]["tools_used"].append(tool_name)
        
        # 保持历史记录在合理范围内
        if len(session["history"]) > self.max_history:
            session["history"] = session["history"][-self.max_history:]
        
        # 更新最后活跃时间
        session["last_active"] = datetime.now()
        
        logger.debug(f"会话 {session_id} 已更新")
    
    def get_conversation_context(self, session_id: str) -> Dict[str, Any]:
        """
        获取对话上下文，用于意图识别
        
        Args:
            session_id: 会话ID
            
        Returns:
            对话上下文信息
        """
        session = self.get_session(session_id)
        
        # 获取最近的对话历史
        recent_history = session["history"][-5:] if session["history"] else []
        
        return {
            "session_id": session_id,
            "user_info": session["context"]["user_info"],
            "current_topic": session["context"]["current_topic"],
            "birth_info": session["context"]["birth_info"],
            "last_analysis_type": session["context"]["last_analysis_type"],
            "recent_history": recent_history,
            "total_messages": session["metadata"]["total_messages"],
            "tools_used": session["metadata"]["tools_used"]
        }
    
    def set_user_birth_info(self, session_id: str, birth_info: Dict[str, Any]) -> None:
        """
        设置用户出生信息
        
        Args:
            session_id: 会话ID
            birth_info: 出生信息
        """
        session = self.get_session(session_id)
        session["context"]["birth_info"] = birth_info
        logger.info(f"会话 {session_id} 设置出生信息: {birth_info}")
    
    def get_user_birth_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户出生信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            出生信息或None
        """
        session = self.get_session(session_id)
        return session["context"]["birth_info"]
    
    def set_current_topic(self, session_id: str, topic: str) -> None:
        """
        设置当前话题
        
        Args:
            session_id: 会话ID
            topic: 话题名称
        """
        session = self.get_session(session_id)
        session["context"]["current_topic"] = topic
        logger.debug(f"会话 {session_id} 设置话题: {topic}")
    
    def clear_session(self, session_id: str) -> bool:
        """
        清除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功清除
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"会话 {session_id} 已清除")
            return True
        return False
    
    def _cleanup_expired_sessions(self) -> None:
        """清理过期会话"""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            if (current_time - session["last_active"]).seconds > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
            logger.info(f"清理过期会话: {session_id}")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Returns:
            统计信息
        """
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": len([s for s in self.sessions.values() 
                                  if (datetime.now() - s["last_active"]).seconds < 300]),
            "total_messages": sum(s["metadata"]["total_messages"] for s in self.sessions.values()),
            "tools_usage": self._get_tools_usage_stats()
        }
    
    def _get_tools_usage_stats(self) -> Dict[str, int]:
        """获取工具使用统计"""
        tools_count = {}
        for session in self.sessions.values():
            for tool in session["metadata"]["tools_used"]:
                tools_count[tool] = tools_count.get(tool, 0) + 1
        return tools_count
    
    def export_session(self, session_id: str) -> Optional[str]:
        """
        导出会话数据（JSON格式）
        
        Args:
            session_id: 会话ID
            
        Returns:
            JSON字符串或None
        """
        if session_id in self.sessions:
            session_data = self.sessions[session_id].copy()
            # 转换datetime为字符串
            session_data["created_at"] = session_data["created_at"].isoformat()
            session_data["last_active"] = session_data["last_active"].isoformat()
            
            for record in session_data["history"]:
                if "timestamp" in record and isinstance(record["timestamp"], datetime):
                    record["timestamp"] = record["timestamp"].isoformat()
            
            return json.dumps(session_data, ensure_ascii=False, indent=2)
        return None
