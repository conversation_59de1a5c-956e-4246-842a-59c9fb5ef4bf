#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的排盘数据格式化功能
"""

def test_chart_format_fix():
    """测试修复后的排盘数据格式化功能"""
    print("🔧 测试修复后的排盘数据格式化功能")
    print("=" * 60)
    
    try:
        # 1. 生成测试排盘数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=1988,
            month=6,
            day=1,
            hour=11,
            gender="男"
        )
        
        if raw_data.get("success"):
            print("✅ 测试排盘数据生成成功")
            
            # 2. 测试修复后的格式化函数
            from backend_agent_web import format_chart_data_for_chat
            
            formatted_data = format_chart_data_for_chat(raw_data)
            
            if formatted_data and len(formatted_data) > 20:
                print("✅ 排盘数据格式化成功")
                print(f"📊 格式化数据长度: {len(formatted_data)}字符")
                print(f"\n📝 格式化数据内容:")
                print("-" * 50)
                print(formatted_data)
                print("-" * 50)
                
                # 检查关键信息是否存在
                has_ziwei = "紫薇" in formatted_data or "命宫" in formatted_data
                has_bazi = "八字" in formatted_data
                
                print(f"\n📊 内容检查:")
                print(f"  包含紫薇信息: {'✅' if has_ziwei else '❌'}")
                print(f"  包含八字信息: {'✅' if has_bazi else '❌'}")
                
                if has_ziwei or has_bazi:
                    print("✅ 排盘数据格式化功能正常")
                    return True
                else:
                    print("⚠️ 排盘数据格式化内容不完整")
                    return False
            else:
                print("❌ 排盘数据格式化失败或内容过短")
                print(f"返回内容: {formatted_data}")
                return False
        else:
            print(f"❌ 测试排盘数据生成失败: {raw_data.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_with_formatted_data():
    """测试使用格式化数据的聊天功能"""
    print(f"\n💬 测试使用格式化数据的聊天功能")
    print("=" * 40)
    
    try:
        # 1. 创建测试缓存结果
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        calculator_agent = FortuneCalculatorAgent("chat_test")
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            # 保存到缓存
            result_id = calculator_agent.cache.save_result(
                user_id="chat_test_user",
                session_id="chat_test_session",
                calculation_type="ziwei",
                birth_info=birth_info,
                raw_calculation=raw_data,
                detailed_analysis={"angle_analyses": {}},
                summary="聊天测试排盘",
                keywords=["紫薇", "八字", "聊天测试"],
                confidence=0.9
            )
            
            cached_result = calculator_agent.cache.get_result(result_id)
            
            if cached_result:
                # 2. 测试聊天回复生成
                from backend_agent_web import generate_chat_response
                
                test_questions = [
                    "我的性格特点是什么？",
                    "我适合什么职业？"
                ]
                
                success_count = 0
                
                for question in test_questions:
                    print(f"\n👤 测试问题: {question}")
                    
                    response = generate_chat_response(cached_result, question)
                    
                    if response and len(response) > 50:
                        print(f"✅ 回复生成成功: {len(response)}字")
                        print(f"📝 回复预览: {response[:100]}...")
                        success_count += 1
                    else:
                        print(f"❌ 回复生成失败: {response}")
                
                print(f"\n📊 聊天测试结果: {success_count}/{len(test_questions)} 成功")
                return success_count == len(test_questions)
            else:
                print("❌ 无法获取缓存结果")
                return False
        else:
            print("❌ 排盘数据生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 聊天测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 排盘数据格式化修复验证")
    print("=" * 70)
    
    # 1. 测试排盘数据格式化
    format_success = test_chart_format_fix()
    
    # 2. 测试聊天功能
    chat_success = test_chat_with_formatted_data()
    
    print("\n" + "=" * 70)
    print("🎯 修复验证结果总结:")
    
    if format_success:
        print("✅ 排盘数据格式化修复成功")
    else:
        print("❌ 排盘数据格式化仍有问题")
    
    if chat_success:
        print("✅ 聊天功能正常工作")
    else:
        print("❌ 聊天功能仍有问题")
    
    if format_success and chat_success:
        print("\n🎉 所有问题修复完成！Web界面功能完全正常！")
        print("💡 现在可以启动Web界面体验完整功能:")
        print("  1. ✅ 排盘数据生成和显示")
        print("  2. ✅ 12个分析按需生成")
        print("  3. ✅ 每个分析可以重试")
        print("  4. ✅ 即时聊天功能完整")
        print("  5. ✅ 排盘数据格式化正常")
    else:
        print("\n⚠️ 还有功能需要进一步修复")

if __name__ == "__main__":
    main()
