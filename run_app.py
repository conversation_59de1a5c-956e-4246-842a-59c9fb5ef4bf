#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构项目启动脚本
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 确保在正确的目录中
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def init_app():
    """初始化应用"""
    st.set_page_config(
        page_title="智能算命AI系统 v4.0",
        page_icon="🔮",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 设置自定义CSS
    st.markdown("""
    <style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
        border-left: 4px solid #667eea;
    }

    .status-success {
        color: #28a745;
        font-weight: bold;
    }

    .status-error {
        color: #dc3545;
        font-weight: bold;
    }

    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)

    # 初始化会话状态
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'home'

    # 初始化服务
    try:
        if 'chart_service' not in st.session_state:
            from services.chart_service import ChartService
            st.session_state.chart_service = ChartService()

        if 'analysis_service' not in st.session_state:
            from services.analysis_service import AnalysisService
            st.session_state.analysis_service = AnalysisService()
    except Exception as e:
        st.error(f"服务初始化失败: {e}")

def show_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.markdown("""
        <div style="text-align: center; padding: 1rem;">
            <h2>🔮 智能算命AI</h2>
            <p style="color: #666;">v4.0 重构版</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

        # 导航菜单
        st.markdown("### 📋 功能导航")

        if st.button("🏠 首页", use_container_width=True):
            st.session_state.current_page = 'home'
            st.rerun()

        if st.button("📊 生成排盘", use_container_width=True):
            st.session_state.current_page = 'chart'
            st.rerun()

        if st.button("🔍 深度分析", use_container_width=True):
            st.session_state.current_page = 'analysis'
            st.rerun()

        if st.button("🎲 六爻占卜", use_container_width=True):
            st.session_state.current_page = 'liuyao'
            st.rerun()

        if st.button("💑 合婚分析", use_container_width=True):
            st.session_state.current_page = 'compatibility'
            st.rerun()

        st.markdown("---")

        # 系统状态
        st.markdown("### ⚙️ 系统状态")

        # 检查配置
        try:
            from config import config
            if config.llm.api_key:
                st.markdown('<p class="status-success">✅ API已配置</p>', unsafe_allow_html=True)
            else:
                st.markdown('<p class="status-error">❌ API未配置</p>', unsafe_allow_html=True)
        except:
            st.markdown('<p class="status-error">❌ 配置异常</p>', unsafe_allow_html=True)

        # 检查服务状态
        if 'chart_service' in st.session_state:
            st.markdown('<p class="status-success">✅ 服务已就绪</p>', unsafe_allow_html=True)
        else:
            st.markdown('<p class="status-error">❌ 服务未就绪</p>', unsafe_allow_html=True)

def show_home_page():
    """显示首页"""
    st.markdown("""
    <div class="main-header">
        <h1>🔮 智能算命AI系统 v4.0</h1>
        <p>基于简洁稳定架构的专业命理分析系统</p>
    </div>
    """, unsafe_allow_html=True)

    # 功能介绍
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>📊 真实排盘</h3>
            <p>基于py-iztro算法的紫薇斗数和八字排盘，确保计算准确性。</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="feature-card">
            <h3>🔍 深度分析</h3>
            <p>12角度专业分析，每个角度4000-5000字深度解读。</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="feature-card">
            <h3>🎲 六爻占卜</h3>
            <p>传统六爻占卜系统，支持时间起卦和数字起卦。</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="feature-card">
            <h3>💑 合婚分析</h3>
            <p>双人配对分析，多维度兼容性评估。</p>
        </div>
        """, unsafe_allow_html=True)

    # 快速测试
    st.markdown("---")
    st.markdown("## 🧪 快速功能测试")

    if st.button("🔮 测试排盘功能", use_container_width=True, type="primary"):
        with st.spinner("测试中..."):
            try:
                from models.birth_info import BirthInfo
                birth = BirthInfo(1988, 6, 1, 11, "男")

                chart_service = st.session_state.chart_service
                chart_data = chart_service.generate_chart(birth)

                if chart_data.success:
                    st.success("🎉 排盘功能测试成功！")
                    st.info(f"测试数据: {birth.to_display_string()}")
                    st.info(f"紫薇: {'✅' if chart_data.ziwei_chart else '❌'} | 八字: {'✅' if chart_data.bazi_chart else '❌'}")
                else:
                    st.error(f"❌ 排盘测试失败: {chart_data.error_message}")
            except Exception as e:
                st.error(f"❌ 测试异常: {e}")

def show_chart_page():
    """显示排盘页面"""
    st.markdown("""
    <div class="main-header">
        <h1>📊 生成排盘</h1>
        <p>输入生辰信息，生成紫薇斗数和八字排盘</p>
    </div>
    """, unsafe_allow_html=True)

    # 输入表单
    with st.form("birth_info_form"):
        st.markdown("### 📝 请输入生辰信息")

        col1, col2 = st.columns(2)

        with col1:
            year = st.number_input("出生年份", min_value=1900, max_value=2100, value=1990)
            month = st.number_input("出生月份", min_value=1, max_value=12, value=1)
            day = st.number_input("出生日期", min_value=1, max_value=31, value=1)

        with col2:
            hour = st.number_input("出生小时", min_value=0, max_value=23, value=12)
            gender = st.selectbox("性别", ["男", "女"])

            # 显示时辰信息
            hour_names = {
                23: "子时", 0: "子时", 1: "丑时", 2: "丑时",
                3: "寅时", 4: "寅时", 5: "卯时", 6: "卯时",
                7: "辰时", 8: "辰时", 9: "巳时", 10: "巳时",
                11: "午时", 12: "午时", 13: "未时", 14: "未时",
                15: "申时", 16: "申时", 17: "酉时", 18: "酉时",
                19: "戌时", 20: "戌时", 21: "亥时", 22: "亥时"
            }
            hour_name = hour_names.get(hour, "未知时辰")
            st.info(f"时辰: {hour_name}")

        # 提交按钮
        submitted = st.form_submit_button("🔮 生成排盘", use_container_width=True, type="primary")

        if submitted:
            try:
                from models.birth_info import BirthInfo

                # 创建生辰信息对象
                birth_info = BirthInfo(year, month, day, hour, gender)

                # 显示输入信息确认
                st.success(f"✅ 生辰信息: {birth_info.to_display_string()}")

                # 生成排盘
                with st.spinner("🔄 正在生成排盘，请稍候..."):
                    chart_service = st.session_state.chart_service
                    chart_data = chart_service.generate_chart(birth_info)

                if chart_data.success:
                    st.success("🎉 排盘生成成功！")

                    # 保存到会话状态
                    st.session_state.current_chart_data = chart_data

                    # 显示排盘结果
                    show_chart_result(chart_data)

                    # 操作按钮
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        if st.button("🔍 开始分析", use_container_width=True, type="primary"):
                            st.session_state.current_page = 'analysis'
                            st.rerun()

                    with col2:
                        if st.button("🎲 六爻占卜", use_container_width=True):
                            st.session_state.current_page = 'liuyao'
                            st.rerun()

                    with col3:
                        if st.button("💑 合婚分析", use_container_width=True):
                            st.session_state.current_page = 'compatibility'
                            st.rerun()

                else:
                    st.error(f"❌ 排盘生成失败: {chart_data.error_message}")

            except ValueError as e:
                st.error(f"❌ 输入错误: {e}")
            except Exception as e:
                st.error(f"❌ 系统异常: {e}")
                st.exception(e)

    # 显示历史记录
    show_chart_history()

def show_chart_result(chart_data):
    """显示排盘结果"""
    st.markdown("---")
    st.markdown("## 📊 排盘结果")

    # 基本信息
    with st.expander("📋 基本信息", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown(f"""
            **出生时间**: {chart_data.birth_info.to_display_string()}
            **计算时间**: {chart_data.calculation_time.strftime('%Y-%m-%d %H:%M:%S')}
            **缓存键**: `{chart_data.get_cache_key()}`
            """)

        with col2:
            if chart_data.ziwei_chart and chart_data.bazi_chart:
                st.success("✅ 紫薇斗数和八字排盘均已生成")
            elif chart_data.ziwei_chart:
                st.warning("⚠️ 仅紫薇斗数排盘生成成功")
            elif chart_data.bazi_chart:
                st.warning("⚠️ 仅八字排盘生成成功")
            else:
                st.error("❌ 排盘生成失败")

    # 紫薇斗数结果
    if chart_data.ziwei_chart:
        with st.expander("🔮 紫薇斗数排盘"):
            show_ziwei_chart(chart_data.ziwei_chart)

    # 八字结果
    if chart_data.bazi_chart:
        with st.expander("📜 八字命理排盘"):
            show_bazi_chart(chart_data.bazi_chart)

def show_ziwei_chart(ziwei_chart):
    """显示紫薇斗数排盘"""
    # 命宫信息
    main_palace = ziwei_chart.get_main_palace()
    if main_palace:
        st.markdown("### 🏛️ 命宫信息")
        st.json(main_palace)

    # 十二宫信息
    if ziwei_chart.palaces:
        st.markdown("### 🏯 十二宫详情")

        # 创建3x4网格显示十二宫
        palace_names = list(ziwei_chart.palaces.keys())

        for i in range(0, len(palace_names), 3):
            cols = st.columns(3)
            for j, col in enumerate(cols):
                if i + j < len(palace_names):
                    palace_name = palace_names[i + j]
                    palace_data = ziwei_chart.palaces[palace_name]

                    with col:
                        st.markdown(f"**{palace_name}**")
                        if isinstance(palace_data, dict):
                            for key, value in palace_data.items():
                                st.text(f"{key}: {value}")
                        else:
                            st.text(str(palace_data))

def show_bazi_chart(bazi_chart):
    """显示八字排盘"""
    # 四柱信息
    if bazi_chart.four_pillars:
        st.markdown("### 📜 四柱信息")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.markdown(f"**年柱**  \n{bazi_chart.get_year_pillar()}")
        with col2:
            st.markdown(f"**月柱**  \n{bazi_chart.get_month_pillar()}")
        with col3:
            st.markdown(f"**日柱**  \n{bazi_chart.get_day_pillar()}")
        with col4:
            st.markdown(f"**时柱**  \n{bazi_chart.get_hour_pillar()}")

    # 五行分布
    if bazi_chart.wuxing:
        st.markdown("### 🌟 五行分布")

        col1, col2 = st.columns(2)

        with col1:
            # 五行数值
            for element, value in bazi_chart.wuxing.items():
                st.metric(element, f"{value:.1f}")

        with col2:
            # 五行总结
            wuxing_summary = bazi_chart.get_wuxing_summary()
            st.info(f"五行特点: {wuxing_summary}")

    # 十神信息
    if bazi_chart.ten_gods:
        st.markdown("### 🎭 十神信息")
        st.json(bazi_chart.ten_gods)

def show_chart_history():
    """显示排盘历史"""
    st.markdown("---")
    st.markdown("## 📚 最近排盘")

    # 这里可以从缓存中获取最近的排盘记录
    # 暂时显示提示信息
    st.info("💡 排盘记录会自动保存，您可以随时查看历史记录")

    if 'current_chart_data' in st.session_state:
        chart_data = st.session_state.current_chart_data
        st.markdown(f"**当前排盘**: {chart_data.birth_info.to_display_string()}")

        if st.button("🔄 重新生成排盘"):
            del st.session_state.current_chart_data
            st.rerun()

def show_analysis_page():
    """显示分析页面"""
    st.markdown("""
    <div class="main-header">
        <h1>🔍 深度分析</h1>
        <p>基于排盘数据进行12角度专业分析</p>
    </div>
    """, unsafe_allow_html=True)

    # 检查是否有排盘数据
    if 'current_chart_data' not in st.session_state:
        st.warning("⚠️ 请先生成排盘数据")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("📊 去生成排盘", use_container_width=True, type="primary"):
                st.session_state.current_page = 'chart'
                st.rerun()

        with col2:
            st.info("💡 需要先在排盘页面生成命盘数据，才能进行深度分析")

        return

    chart_data = st.session_state.current_chart_data

    # 显示当前排盘信息
    with st.expander("📋 当前排盘信息", expanded=False):
        st.markdown(f"""
        **生辰信息**: {chart_data.birth_info.to_display_string()}
        **排盘状态**: {'✅ 成功' if chart_data.success else '❌ 失败'}
        **生成时间**: {chart_data.calculation_time.strftime('%Y-%m-%d %H:%M:%S')}
        """)

        if chart_data.ziwei_chart and chart_data.bazi_chart:
            st.success("✅ 紫薇斗数和八字排盘数据完整，可以进行分析")
        else:
            st.error("❌ 排盘数据不完整，请重新生成")
            return

    # 分析角度选择
    st.markdown("### 🎯 选择分析角度")

    analysis_angles = {
        "命宫性格命运": "personality_destiny",
        "财富运势": "wealth_fortune",
        "婚姻感情": "marriage_love",
        "健康养生": "health_wellness",
        "子女创造": "children_creativity",
        "事业成就": "career_achievement",
        "学业智慧": "education_wisdom",
        "家庭亲情": "family_kinship",
        "社交友谊": "social_friendship",
        "迁移出行": "travel_migration",
        "精神信仰": "spiritual_belief",
        "整体运势": "overall_fortune"
    }

    # 创建3x4网格选择
    selected_angles = []
    for i in range(0, len(analysis_angles), 3):
        cols = st.columns(3)
        angle_items = list(analysis_angles.items())[i:i+3]

        for j, (angle_name, angle_key) in enumerate(angle_items):
            with cols[j]:
                if st.checkbox(f"{angle_name}", key=f"select_{angle_key}"):
                    selected_angles.append(angle_name)

    # 分析操作按钮
    st.markdown("---")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🎯 分析选中角度", use_container_width=True, type="primary"):
            if selected_angles:
                analyze_selected_angles(chart_data, selected_angles)
            else:
                st.warning("请先选择要分析的角度")

    with col2:
        if st.button("🚀 分析所有角度", use_container_width=True):
            analyze_all_angles(chart_data, list(analysis_angles.keys()))

    with col3:
        if st.button("⭐ 分析重点角度", use_container_width=True):
            # 分析前5个重点角度
            priority_angles = list(analysis_angles.keys())[:5]
            analyze_selected_angles(chart_data, priority_angles)

    # 显示分析结果
    show_analysis_results()

def analyze_selected_angles(chart_data, selected_angles):
    """分析选中的角度"""
    with st.spinner(f"🔍 正在分析 {len(selected_angles)} 个角度，请稍候..."):
        try:
            # 模拟分析过程
            import time
            time.sleep(2)  # 模拟分析时间

            # 保存分析结果到会话状态
            if 'analysis_results' not in st.session_state:
                st.session_state.analysis_results = {}

            for angle in selected_angles:
                # 生成模拟分析结果
                analysis_content = generate_mock_analysis(chart_data, angle)
                st.session_state.analysis_results[angle] = {
                    'content': analysis_content,
                    'word_count': len(analysis_content),
                    'timestamp': time.time()
                }

            st.success(f"✅ 分析完成！已完成 {len(selected_angles)} 个角度的分析")
            st.rerun()

        except Exception as e:
            st.error(f"❌ 分析失败: {e}")

def analyze_all_angles(chart_data, all_angles):
    """分析所有角度"""
    with st.spinner("🚀 正在进行全角度分析，这可能需要几分钟时间..."):
        try:
            # 模拟全角度分析
            import time
            time.sleep(3)  # 模拟分析时间

            # 保存分析结果到会话状态
            if 'analysis_results' not in st.session_state:
                st.session_state.analysis_results = {}

            for angle in all_angles:
                analysis_content = generate_mock_analysis(chart_data, angle)
                st.session_state.analysis_results[angle] = {
                    'content': analysis_content,
                    'word_count': len(analysis_content),
                    'timestamp': time.time()
                }

            st.success("🎉 所有角度分析完成！")
            st.rerun()

        except Exception as e:
            st.error(f"❌ 全角度分析失败: {e}")

def generate_mock_analysis(chart_data, angle):
    """生成模拟分析内容"""
    birth_info = chart_data.birth_info

    analysis_templates = {
        "命宫性格命运": f"""
## 命宫性格命运分析

根据您的生辰信息（{birth_info.to_display_string()}），从命宫角度进行深度分析：

### 性格特征
您的命宫显示出独特的性格特质。作为{birth_info.gender}命，您在性格上展现出...

### 命运走向
从整体命运来看，您的人生轨迹呈现出...

### 发展建议
建议您在人生发展中注意以下几个方面...

（这是模拟分析内容，实际系统会调用LLM生成专业分析）
        """,

        "财富运势": f"""
## 财富运势分析

基于您的八字和紫薇排盘（{birth_info.to_display_string()}），财富运势分析如下：

### 财运基础
您的财帛宫显示...

### 财富机遇
在财富积累方面，您具有...

### 理财建议
建议您在理财方面...

（这是模拟分析内容，实际系统会调用LLM生成专业分析）
        """,

        "婚姻感情": f"""
## 婚姻感情分析

根据您的命理信息（{birth_info.to_display_string()}），感情婚姻方面分析：

### 感情特质
您在感情方面的特点是...

### 婚姻运势
从夫妻宫来看，您的婚姻...

### 感情建议
在感情经营中，建议您...

（这是模拟分析内容，实际系统会调用LLM生成专业分析）
        """
    }

    return analysis_templates.get(angle, f"""
## {angle}分析

根据您的生辰信息（{birth_info.to_display_string()}），{angle}方面的分析如下：

### 基本情况
从命理角度来看，您在{angle}方面...

### 发展趋势
未来在{angle}方面的发展...

### 建议指导
针对{angle}，建议您...

（这是模拟分析内容，实际系统会调用LLM生成专业分析）
    """)

def show_analysis_results():
    """显示分析结果"""
    if 'analysis_results' not in st.session_state or not st.session_state.analysis_results:
        st.info("💡 请先选择角度进行分析")
        return

    st.markdown("---")
    st.markdown("## 📖 分析结果")

    results = st.session_state.analysis_results

    # 显示分析进度
    total_angles = 12
    completed = len(results)
    progress = completed / total_angles

    st.progress(progress)
    st.markdown(f"**分析进度**: {completed}/{total_angles} 完成 ({progress*100:.1f}%)")

    # 显示已完成的分析
    for angle, result in results.items():
        with st.expander(f"📋 {angle} ({result['word_count']}字)", expanded=False):
            st.markdown(result['content'])

            col1, col2 = st.columns(2)
            with col1:
                st.info(f"字数: {result['word_count']}")
            with col2:
                import datetime
                timestamp = datetime.datetime.fromtimestamp(result['timestamp'])
                st.info(f"完成时间: {timestamp.strftime('%H:%M:%S')}")

    # 导出功能
    if len(results) >= 3:  # 至少完成3个分析才显示导出
        st.markdown("---")
        if st.button("📄 导出分析报告", use_container_width=True, type="primary"):
            export_analysis_report(results)

def export_analysis_report(results):
    """导出分析报告"""
    st.success("📄 导出功能开发中...")
    st.info("将来会支持导出为HTML格式的完整分析报告")

def show_liuyao_page():
    """显示六爻占卜页面"""
    st.markdown("""
    <div class="main-header">
        <h1>🎲 六爻占卜</h1>
        <p>传统六爻占卜，洞察事物发展趋势</p>
    </div>
    """, unsafe_allow_html=True)

    # 占卜方式选择
    st.markdown("### 🎯 选择起卦方式")

    tab1, tab2 = st.tabs(["⏰ 时间起卦", "🔢 数字起卦"])

    with tab1:
        st.markdown("#### 时间起卦")
        st.info("基于当前时间自动生成卦象，适合即时占卜")

        question = st.text_area("请输入您要占卜的问题：",
                               placeholder="例如：我最近的工作运势如何？",
                               height=100)

        if st.button("🎲 时间起卦", use_container_width=True, type="primary"):
            if question.strip():
                perform_time_divination(question)
            else:
                st.warning("请先输入占卜问题")

    with tab2:
        st.markdown("#### 数字起卦")
        st.info("请输入3个数字（1-999），基于数字生成卦象")

        question2 = st.text_area("请输入您要占卜的问题：",
                                placeholder="例如：我和某人的关系发展如何？",
                                height=100,
                                key="question2")

        col1, col2, col3 = st.columns(3)
        with col1:
            num1 = st.number_input("第一个数字", min_value=1, max_value=999, value=1)
        with col2:
            num2 = st.number_input("第二个数字", min_value=1, max_value=999, value=1)
        with col3:
            num3 = st.number_input("第三个数字", min_value=1, max_value=999, value=1)

        if st.button("🎲 数字起卦", use_container_width=True, type="primary"):
            if question2.strip():
                perform_number_divination(question2, [num1, num2, num3])
            else:
                st.warning("请先输入占卜问题")

    # 显示占卜结果
    show_liuyao_results()

def perform_time_divination(question):
    """执行时间起卦"""
    with st.spinner("🔮 正在进行时间起卦..."):
        try:
            import time
            import datetime

            # 模拟时间起卦
            now = datetime.datetime.now()

            # 简化的起卦算法
            year = now.year % 12
            month = now.month
            day = now.day
            hour = now.hour

            upper = (year + month + day) % 8
            lower = (year + month + day + hour) % 8
            moving_line = (year + month + day + hour) % 6 + 1

            trigrams = ["坤", "震", "坎", "兑", "艮", "离", "巽", "乾"]

            hexagram_data = {
                "卦名": f"第{upper * 8 + lower + 1}卦",
                "上卦": trigrams[upper],
                "下卦": trigrams[lower],
                "动爻": f"第{moving_line}爻",
                "起卦时间": now.strftime("%Y年%m月%d日%H时"),
                "起卦方式": "时间起卦"
            }

            # 生成解卦内容
            interpretation = generate_liuyao_interpretation(question, hexagram_data)

            # 保存结果
            if 'liuyao_results' not in st.session_state:
                st.session_state.liuyao_results = []

            result = {
                'question': question,
                'method': '时间起卦',
                'hexagram_data': hexagram_data,
                'interpretation': interpretation,
                'timestamp': time.time()
            }

            st.session_state.liuyao_results.append(result)
            st.success("✅ 时间起卦完成！")
            st.rerun()

        except Exception as e:
            st.error(f"❌ 起卦失败: {e}")

def perform_number_divination(question, numbers):
    """执行数字起卦"""
    with st.spinner("🔮 正在进行数字起卦..."):
        try:
            import time

            # 简化的数字起卦算法
            upper = sum(numbers[:2]) % 8
            lower = sum(numbers[1:3]) % 8
            moving_line = sum(numbers) % 6 + 1

            trigrams = ["坤", "震", "坎", "兑", "艮", "离", "巽", "乾"]

            hexagram_data = {
                "卦名": f"第{upper * 8 + lower + 1}卦",
                "上卦": trigrams[upper],
                "下卦": trigrams[lower],
                "动爻": f"第{moving_line}爻",
                "起卦数字": numbers,
                "起卦方式": "数字起卦"
            }

            # 生成解卦内容
            interpretation = generate_liuyao_interpretation(question, hexagram_data)

            # 保存结果
            if 'liuyao_results' not in st.session_state:
                st.session_state.liuyao_results = []

            result = {
                'question': question,
                'method': '数字起卦',
                'hexagram_data': hexagram_data,
                'interpretation': interpretation,
                'timestamp': time.time()
            }

            st.session_state.liuyao_results.append(result)
            st.success("✅ 数字起卦完成！")
            st.rerun()

        except Exception as e:
            st.error(f"❌ 起卦失败: {e}")

def generate_liuyao_interpretation(question, hexagram_data):
    """生成六爻解卦内容"""
    return f"""
## 六爻解卦

**问题**: {question}

**卦象信息**:
- 卦名: {hexagram_data['卦名']}
- 上卦: {hexagram_data['上卦']}
- 下卦: {hexagram_data['下卦']}
- 动爻: {hexagram_data['动爻']}

**解卦分析**:

根据您所得卦象，此卦象征着...

从卦象来看，您所问之事的发展趋势是...

**建议指导**:
1. 近期宜...
2. 需要注意...
3. 长远来看...

**总结**: 此卦整体来说是一个...的卦象，建议您...

（这是模拟解卦内容，实际系统会调用LLM生成专业解读）
    """

def show_liuyao_results():
    """显示六爻占卜结果"""
    if 'liuyao_results' not in st.session_state or not st.session_state.liuyao_results:
        st.info("💡 请先进行占卜")
        return

    st.markdown("---")
    st.markdown("## 📖 占卜结果")

    results = st.session_state.liuyao_results

    for i, result in enumerate(reversed(results)):  # 最新的在前
        with st.expander(f"🎲 {result['method']} - {result['question'][:20]}...", expanded=(i==0)):
            st.markdown(f"**问题**: {result['question']}")

            # 显示卦象信息
            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**卦象信息**:")
                for key, value in result['hexagram_data'].items():
                    st.text(f"{key}: {value}")

            with col2:
                import datetime
                timestamp = datetime.datetime.fromtimestamp(result['timestamp'])
                st.info(f"占卜时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

            # 显示解卦内容
            st.markdown("**解卦内容**:")
            st.markdown(result['interpretation'])

def show_compatibility_page():
    """显示合婚分析页面"""
    st.markdown("""
    <div class="main-header">
        <h1>💑 合婚分析</h1>
        <p>双人命理配对分析，评估感情兼容性</p>
    </div>
    """, unsafe_allow_html=True)

    st.info("🚧 合婚分析功能开发中...")
    st.markdown("""
    ### 🎯 功能规划

    合婚分析将包含以下功能：

    1. **双人排盘** - 分别生成两人的紫薇和八字排盘
    2. **多维度分析** - 性格、事业、财运、家庭等方面的兼容性
    3. **兼容性评分** - 量化的匹配度评估
    4. **建议指导** - 具体的相处建议和注意事项

    ### 📋 使用流程

    1. 输入第一人的生辰信息
    2. 输入第二人的生辰信息
    3. 系统自动生成双人排盘
    4. 进行多维度兼容性分析
    5. 生成详细的合婚报告

    敬请期待！
    """)

def main():
    """主函数"""
    try:
        # 初始化应用
        init_app()

        # 显示侧边栏
        show_sidebar()

        # 根据当前页面显示内容
        current_page = st.session_state.current_page

        if current_page == 'home':
            show_home_page()
        elif current_page == 'chart':
            show_chart_page()
        elif current_page == 'analysis':
            show_analysis_page()
        elif current_page == 'liuyao':
            show_liuyao_page()
        elif current_page == 'compatibility':
            show_compatibility_page()
        else:
            show_home_page()

    except Exception as e:
        st.error(f"应用运行异常: {e}")
        st.exception(e)

if __name__ == "__main__":
    main()
