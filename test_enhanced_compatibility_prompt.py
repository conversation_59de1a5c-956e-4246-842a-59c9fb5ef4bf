#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版合盘提示词
"""

import asyncio
import time
from datetime import datetime

def test_enhanced_compatibility_prompt():
    """测试增强版合盘提示词"""
    print("🔮 测试增强版合盘提示词")

    try:
        # 1. 导入必要模块
        from core.compatibility_analysis import CompatibilityAnalysisEngine

        # 2. 测试数据
        person_a_info = {
            "name": "张三",
            "year": "1985",
            "month": "8",
            "day": "15",
            "hour": "申时",
            "gender": "男"
        }

        person_b_info = {
            "name": "李四",
            "year": "1987",
            "month": "11",
            "day": "22",
            "hour": "午时",
            "gender": "女"
        }

        analysis_dimension = "personality_compatibility"

        print(f"📊 测试数据:")
        print(f"   A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"   B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        print(f"   维度: {analysis_dimension}")

        # 3. 初始化引擎
        print("🔧 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()

        # 4. 计算合盘数据
        print("📊 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)

        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False

        print(f"✅ 合盘数据计算成功")

        # 5. 测试提示词构建
        print(f"🔨 测试增强版提示词构建...")
        from core.analysis.compatibility_prompt_builder import CompatibilityPromptBuilder

        prompt_builder = CompatibilityPromptBuilder()
        prompt = prompt_builder.build_compatibility_prompt(compatibility_data, analysis_dimension)

        print(f"✅ 提示词构建成功")
        print(f"   提示词长度: {len(prompt)}字符")
        lines_count = len(prompt.split('\n'))
        print(f"   提示词行数: {lines_count}行")

        # 6. 显示提示词摘要
        print(f"\n📋 提示词内容摘要:")
        lines = prompt.split('\n')

        # 显示关键部分
        key_sections = []
        current_section = ""

        for line in lines:
            if line.strip().startswith('【') and line.strip().endswith('】'):
                if current_section:
                    key_sections.append(current_section)
                current_section = line.strip()
            elif line.strip().startswith('🎯') or line.strip().startswith('📊') or line.strip().startswith('🚨'):
                if current_section:
                    current_section += f" -> {line.strip()[:50]}..."

        if current_section:
            key_sections.append(current_section)

        for i, section in enumerate(key_sections[:10], 1):  # 显示前10个关键部分
            print(f"   {i}. {section}")

        # 7. 检查提示词质量
        print(f"\n🔍 提示词质量检查:")

        # 检查关键词
        required_keywords = [
            "紫薇斗数", "八字", "融合分析", "相互印证",
            "负面分析", "具体建议", "时间预测", "风险防范"
        ]

        missing_keywords = []
        for keyword in required_keywords:
            if keyword not in prompt:
                missing_keywords.append(keyword)

        if missing_keywords:
            print(f"   ⚠️ 缺少关键词: {', '.join(missing_keywords)}")
        else:
            print(f"   ✅ 所有关键词都包含")

        # 检查结构
        required_sections = [
            "【严格约束】", "【基本信息】", "【计算数据】",
            "【合盘分析要求】", "【输出格式要求】"
        ]

        missing_sections = []
        for section in required_sections:
            if section not in prompt:
                missing_sections.append(section)

        if missing_sections:
            print(f"   ⚠️ 缺少章节: {', '.join(missing_sections)}")
        else:
            print(f"   ✅ 所有必要章节都包含")

        # 检查长度
        if len(prompt) < 3000:
            print(f"   ⚠️ 提示词过短: {len(prompt)}字符")
        elif len(prompt) > 10000:
            print(f"   ⚠️ 提示词过长: {len(prompt)}字符")
        else:
            print(f"   ✅ 提示词长度适中: {len(prompt)}字符")

        # 8. 保存提示词样本
        print(f"\n💾 保存提示词样本...")

        sample_filename = f"enhanced_compatibility_prompt_sample_{analysis_dimension}_{int(time.time())}.txt"

        with open(sample_filename, 'w', encoding='utf-8') as f:
            f.write(f"增强版合盘分析提示词样本\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析维度: {analysis_dimension}\n")
            f.write(f"提示词长度: {len(prompt)}字符\n")
            f.write(f"{'='*60}\n\n")
            f.write(prompt)

        print(f"   ✅ 提示词样本已保存: {sample_filename}")

        print("\n🎉 增强版合盘提示词测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_compatibility_prompt()
    if success:
        print("\n✅ 增强版合盘提示词测试通过！")
        print("🌟 主要改进:")
        print("   1. 参考紫薇+八字融合分析方案")
        print("   2. 增加详细的数据格式化")
        print("   3. 强化分析要求和约束条件")
        print("   4. 提供具体的实用建议要求")
        print("   5. 增加时间预测和风险防范要求")
        print("\n🔄 下一步可以:")
        print("   1. 在Web界面中创建新的合盘分析")
        print("   2. 对比分析质量的提升")
        print("   3. 验证提示词的实际效果")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
