#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合盘分析Web功能
"""

import asyncio
import time
from datetime import datetime
from core.compatibility_analysis import CompatibilityAnalysisEngine
from core.storage.calculation_cache import CalculationCache

def test_compatibility_analysis():
    """测试合盘分析功能"""
    print("🔮 开始测试合盘分析功能")

    # 测试数据
    person_a_info = {
        "name": "张三",
        "year": "1988",
        "month": "6",
        "day": "1",
        "hour": "午时",
        "gender": "男"
    }

    person_b_info = {
        "name": "李四",
        "year": "1990",
        "month": "8",
        "day": "15",
        "hour": "申时",
        "gender": "女"
    }

    analysis_dimension = "personality_compatibility"

    try:
        # 1. 初始化合盘分析引擎
        print("📊 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()

        # 2. 计算合盘数据
        print("🔄 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)

        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False

        print(f"✅ 合盘数据计算成功")
        print(f"   关系类型: {compatibility_data.get('relationship_type')}")

        # 3. 执行合盘分析
        print(f"🤖 执行{analysis_dimension}分析...")

        async def run_analysis():
            result = await compatibility_engine.execute_compatibility_analysis(
                compatibility_data, analysis_dimension
            )
            return result

        # 运行异步分析
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        result = loop.run_until_complete(run_analysis())
        loop.close()

        if not result.get("success"):
            print(f"❌ 合盘分析失败: {result.get('error')}")
            return False

        print(f"✅ 合盘分析成功")
        print(f"   分析内容长度: {len(result.get('content', ''))}字符")

        # 4. 保存到缓存
        print("💾 保存合盘结果到缓存...")
        cache = CalculationCache()

        compatibility_birth_info = {
            "person_a": f"{person_a_info['name']}({person_a_info['year']}-{person_a_info['month']}-{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']})",
            "person_b": f"{person_b_info['name']}({person_b_info['year']}-{person_b_info['month']}-{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']})",
            "analysis_dimension": analysis_dimension,
            "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
            "timestamp": datetime.now().isoformat()
        }

        result_id = cache.save_result(
            user_id=f"test_compatibility_{int(time.time())}",
            session_id=f"test_{person_a_info['name']}_{person_b_info['name']}_{int(time.time())}",
            calculation_type="compatibility",
            birth_info=compatibility_birth_info,
            raw_calculation=compatibility_data,
            detailed_analysis={
                "compatibility_analysis": result.get("content", ""),
                "analysis_dimension": analysis_dimension,
                "person_a_info": person_a_info,
                "person_b_info": person_b_info,
                "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
                "compatibility_result": result
            },
            summary=f"合盘分析: {person_a_info['name']} & {person_b_info['name']} - {analysis_dimension}",
            keywords=[analysis_dimension, "合盘分析", person_a_info['name'], person_b_info['name']],
            confidence=0.85
        )

        print(f"✅ 合盘结果保存成功")
        print(f"   结果ID: {result_id}")

        # 5. 验证缓存读取
        print("🔍 验证缓存读取...")
        cached_result = cache.get_result(result_id)

        if cached_result:
            print(f"✅ 缓存读取成功")
            print(f"   计算类型: {cached_result.calculation_type}")
            print(f"   分析维度: {cached_result.detailed_analysis.get('analysis_dimension')}")
            print(f"   分析内容: {len(cached_result.detailed_analysis.get('compatibility_analysis', ''))}字符")
        else:
            print(f"❌ 缓存读取失败")
            return False

        print("🎉 合盘分析功能测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_compatibility_analysis()
    if success:
        print("\n✅ 所有测试通过！合盘分析功能正常工作。")
        print("🌐 现在可以在Web界面中测试合盘功能了：http://localhost:8502")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
