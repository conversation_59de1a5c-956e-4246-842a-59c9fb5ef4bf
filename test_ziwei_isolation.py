#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紫薇斗数功能隔离问题
"""

def test_ziwei_direct():
    """直接测试紫薇斗数算法"""
    print("🔧 直接测试紫薇斗数算法")
    print("=" * 60)

    try:
        # 1. 测试导入
        print("1. 测试紫薇斗数算法导入...")
        import sys
        sys.path.append('algorithms')

        from real_ziwei_calculator import RealZiweiCalculator as ZiweiCalculator
        print("✅ 紫薇斗数算法导入成功")

        # 2. 测试初始化
        print("2. 测试紫薇斗数算法初始化...")
        calc = ZiweiCalculator()
        print("✅ 紫薇斗数算法初始化成功")

        # 3. 测试功能
        print("3. 测试紫薇斗数算法功能...")
        result = calc.calculate_chart(1988, 6, 1, 12, "男")
        print(f"功能测试结果类型: {type(result)}")
        print(f"功能测试结果长度: {len(str(result))}")

        if "error" not in result:
            print("✅ 紫薇斗数算法功能正常")
            return calc
        else:
            print(f"❌ 紫薇斗数算法功能异常: {result.get('error')}")
            return None

    except Exception as e:
        print(f"❌ 紫薇斗数算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_fortune_engine_ziwei():
    """测试FortuneEngine中的紫薇斗数"""
    print("\n🔮 测试FortuneEngine中的紫薇斗数")
    print("=" * 60)

    try:
        # 1. 获取紫薇斗数算法实例
        calc = test_ziwei_direct()
        if not calc:
            print("❌ 无法获取紫薇斗数算法实例")
            return False

        # 2. 测试FortuneEngine
        print("4. 测试FortuneEngine导入...")
        import sys
        sys.path.append('core')

        from fortune_engine import FortuneEngine
        print("✅ FortuneEngine导入成功")

        # 3. 创建FortuneEngine实例
        print("5. 创建FortuneEngine实例...")

        def mock_api(prompt):
            return "ziwei"

        engine = FortuneEngine(
            ziwei_calc=calc,  # 传入紫薇斗数算法实例
            bazi_calc=None,
            liuyao_calc=None,
            chat_api_func=mock_api
        )
        print("✅ FortuneEngine创建成功")

        # 4. 检查紫薇斗数算法是否正确传入
        print("6. 检查紫薇斗数算法是否正确传入...")
        print(f"engine.ziwei_calc存在: {engine.ziwei_calc is not None}")
        print(f"engine.ziwei_calc类型: {type(engine.ziwei_calc)}")

        if not engine.ziwei_calc:
            print("❌ 紫薇斗数算法未正确传入FortuneEngine")
            return False

        # 5. 测试紫薇斗数算法调用
        print("7. 测试FortuneEngine中的紫薇斗数算法调用...")
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 12,
            "gender": "男"
        }

        result = engine._call_ziwei_api(birth_info)
        print(f"紫薇斗数算法调用结果: {result}")

        if result.get("success"):
            print("✅ FortuneEngine中的紫薇斗数算法调用成功")
            return True
        else:
            print(f"❌ FortuneEngine中的紫薇斗数算法调用失败: {result.get('error')}")
            return False

    except Exception as e:
        print(f"❌ FortuneEngine测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_request_ziwei():
    """测试完整的紫薇斗数用户请求流程"""
    print("\n🚀 测试完整的紫薇斗数用户请求流程")
    print("=" * 60)

    try:
        # 1. 获取紫薇斗数算法实例
        calc = test_ziwei_direct()
        if not calc:
            return False

        # 2. 创建FortuneEngine
        from fortune_engine import FortuneEngine

        def mock_api(prompt):
            if "算命类型" in prompt:
                return "ziwei"
            elif "问题类型" in prompt:
                return "general"
            elif "出生信息" in prompt and "JSON" in prompt:
                # 模拟LLM解析出生信息
                return '''```json
{
  "year": 1988,
  "month": 6,
  "day": 1,
  "hour": 12,
  "gender": "男"
}
```'''
            else:
                return "null"

        engine = FortuneEngine(
            ziwei_calc=calc,
            bazi_calc=None,
            liuyao_calc=None,
            chat_api_func=mock_api
        )

        # 3. 测试用户请求
        print("8. 测试完整用户请求...")
        user_question = "1988年6月1日公历 午时 男命 紫薇排盘下"

        result = engine.process_user_request(user_question)
        print(f"用户请求处理结果: {result}")

        if result.get("success"):
            print("✅ 完整用户请求处理成功")
            print(f"分析内容长度: {len(result.get('message', ''))}")
            return True
        else:
            print(f"❌ 完整用户请求处理失败: {result.get('message')}")
            return False

    except Exception as e:
        print(f"❌ 用户请求测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 紫薇斗数功能隔离测试")
    print("=" * 80)

    # 测试1: 直接测试紫薇斗数算法
    ziwei_success = test_ziwei_direct()

    # 测试2: 测试FortuneEngine
    if ziwei_success:
        engine_success = test_fortune_engine_ziwei()
    else:
        engine_success = False

    # 测试3: 测试完整流程
    if engine_success:
        request_success = test_user_request_ziwei()
    else:
        request_success = False

    # 总结
    print("\n" + "=" * 80)
    print("🎉 紫薇斗数功能隔离测试结果:")
    print(f"  紫薇斗数直接测试: {'✅ 成功' if ziwei_success else '❌ 失败'}")
    print(f"  FortuneEngine集成: {'✅ 成功' if engine_success else '❌ 失败'}")
    print(f"  完整用户请求: {'✅ 成功' if request_success else '❌ 失败'}")

    if all([ziwei_success, engine_success, request_success]):
        print("\n🎊 所有测试通过！")
        print("\n💡 **问题分析:**")
        print("  如果这里的测试都通过了，但API服务器仍然报错，")
        print("  那么问题在于API服务器启动时的紫薇斗数算法初始化。")
        print()
        print("🔧 **解决方案:**")
        print("  1. 重启API服务器")
        print("  2. 查看启动日志中的紫薇斗数算法初始化信息")
        print("  3. 确认紫薇斗数算法实例正确传递给FortuneEngine")
    else:
        print("\n⚠️ 部分测试失败")
        print("\n🔧 **需要检查:**")
        if not ziwei_success:
            print("  - 紫薇斗数算法模块安装和导入")
        if not engine_success:
            print("  - FortuneEngine与紫薇斗数算法的集成")
        if not request_success:
            print("  - 完整流程的处理逻辑")

if __name__ == "__main__":
    main()
