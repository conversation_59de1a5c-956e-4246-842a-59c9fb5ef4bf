#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人性化紫薇斗数工具
集成到人性化交互系统中的紫薇斗数功能
"""

import logging
import time
from typing import Dict, Any, List, Optional
from core.tools.base_tool import BaseTool

logger = logging.getLogger(__name__)

class HumanizedZiweiTool(BaseTool):
    """人性化紫薇斗数工具"""

    def __init__(self):
        """初始化紫薇工具"""
        super().__init__(
            name="humanized_ziwei",
            description="人性化紫薇斗数命盘分析工具",
            version="1.0.0"
        )

        # 初始化紫薇计算器 - 只使用真实算法
        try:
            from algorithms.real_ziwei_calculator import RealZiweiCalculator
            self.ziwei_calculator = RealZiweiCalculator()
            logger.info("真实紫薇斗数计算器初始化成功")
        except Exception as e:
            logger.error(f"真实紫薇斗数计算器初始化失败: {e}")
            logger.error("紫薇斗数功能不可用 - 需要安装真实算法模块")
            self.ziwei_calculator = None

    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行紫薇斗数分析

        Args:
            intent: 意图识别结果
            context: 会话上下文

        Returns:
            紫薇分析结果
        """
        try:
            logger.info("开始执行人性化紫薇斗数分析")

            # 1. 检查紫薇计算器 - 必须使用真实算法
            if not self.ziwei_calculator:
                return {
                    "success": False,
                    "error": "紫薇斗数计算器未初始化",
                    "message": "紫薇斗数功能暂时不可用，需要安装真实算法模块"
                }

            # 2. 提取出生信息
            birth_info = self._extract_birth_info(intent, context)
            if not birth_info:
                return {
                    "success": False,
                    "error": "缺少出生信息",
                    "message": "请告诉我您的出生年月日时和性别，例如：1988年6月1日午时男"
                }

            # 3. 调用紫薇算法 - 只使用真实算法
            calculation_result = self._calculate_ziwei(birth_info)

            if not calculation_result.get("success"):
                return {
                    "success": False,
                    "error": calculation_result.get("error", "算法调用失败"),
                    "message": "紫薇斗数排盘失败，请检查出生信息是否正确"
                }

            # 4. 返回成功结果（供人性化引擎处理）
            return {
                "success": True,
                "type": "ziwei_analysis",
                "calculation_result": calculation_result,
                "birth_info": birth_info,
                "message": "紫薇斗数命盘分析完成"
            }

        except Exception as e:
            logger.error(f"紫薇斗数分析执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "紫薇斗数分析过程中出现错误，请稍后重试"
            }

    def _extract_birth_info(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """提取出生信息"""
        try:
            entities = intent.get("entities", {})

            # 提取出生信息
            birth_year = entities.get("birth_year")
            birth_month = entities.get("birth_month")
            birth_day = entities.get("birth_day")
            birth_hour = entities.get("birth_hour")
            gender = entities.get("gender")

            # 检查必需信息
            if not all([birth_year, birth_month, birth_day, birth_hour, gender]):
                # 从上下文中查找缺失信息
                context_birth = context.get("birth_info", {})
                birth_year = birth_year or context_birth.get("birth_year")
                birth_month = birth_month or context_birth.get("birth_month")
                birth_day = birth_day or context_birth.get("birth_day")
                birth_hour = birth_hour or context_birth.get("birth_hour")
                gender = gender or context_birth.get("gender")

                # 再次检查
                if not all([birth_year, birth_month, birth_day, birth_hour, gender]):
                    logger.warning("出生信息不完整")
                    return None

            birth_info = {
                "birth_year": str(birth_year),
                "birth_month": str(birth_month),
                "birth_day": str(birth_day),
                "birth_hour": str(birth_hour),
                "gender": str(gender)
            }

            logger.info(f"成功提取出生信息: {birth_info}")
            return birth_info

        except Exception as e:
            logger.error(f"提取出生信息失败: {e}")
            return None

    def _calculate_ziwei(self, birth_info: Dict[str, str]) -> Dict[str, Any]:
        """调用紫薇算法进行计算"""
        try:
            logger.info(f"开始紫薇斗数计算: {birth_info}")

            # 转换时辰
            hour_int = self._convert_hour_to_int(birth_info["birth_hour"])

            # 调用真实算法
            result = self.ziwei_calculator.calculate_chart(
                year=int(birth_info["birth_year"]),
                month=int(birth_info["birth_month"]),
                day=int(birth_info["birth_day"]),
                hour=hour_int,
                gender=birth_info["gender"]
            )

            if "error" in result:
                logger.error(f"紫薇斗数计算失败: {result['error']}")
                return {
                    "success": False,
                    "error": result["error"]
                }
            else:
                logger.info("紫薇斗数计算成功")
                return {
                    "success": True,
                    "raw_result": result,
                    "birth_info": {
                        "datetime": f"{birth_info['birth_year']}年{birth_info['birth_month']}月{birth_info['birth_day']}日{birth_info['birth_hour']}",
                        "gender": birth_info["gender"]
                    },
                    "formatted_output": self._format_ziwei_output(result),
                    "calculation_type": "真实紫薇斗数算法"
                }

        except Exception as e:
            logger.error(f"紫薇斗数计算异常: {e}")
            return {
                "success": False,
                "error": f"紫薇斗数计算异常: {str(e)}"
            }

    def _convert_hour_to_int(self, hour_str: str) -> int:
        """转换时辰字符串为整数"""
        # 统一时辰映射，与八字工具保持一致
        hour_mapping = {
            "子时": 0, "丑时": 1, "寅时": 3, "卯时": 5,
            "辰时": 7, "巳时": 9, "午时": 11, "未时": 13,
            "申时": 15, "酉时": 17, "戌时": 19, "亥时": 21
        }

        # 如果是数字，直接返回
        if hour_str.isdigit():
            return int(hour_str)

        # 如果是时辰名称，转换
        return hour_mapping.get(hour_str, 11)  # 默认午时

    def _format_ziwei_output(self, result: Dict[str, Any]) -> str:
        """格式化紫薇斗数输出"""
        try:
            birth_info = result.get("birth_info", {})
            palaces = result.get("palaces", {})

            output = f"""
紫薇斗数命盘
出生信息: {birth_info.get('solar', '未知')} ({birth_info.get('lunar', '未知')})
性别: {birth_info.get('gender', '未知')}

十二宫星曜分布:
"""

            # 按宫位顺序显示
            palace_order = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                           "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]

            for palace_name in palace_order:
                if palace_name in palaces:
                    palace = palaces[palace_name]
                    major_stars = "、".join(palace.get("major_stars", []))
                    minor_stars = "、".join(palace.get("minor_stars", []))

                    stars_display = []
                    if major_stars:
                        stars_display.append(f"主星:{major_stars}")
                    if minor_stars:
                        stars_display.append(f"辅星:{minor_stars}")

                    stars_text = " | ".join(stars_display) if stars_display else "无星曜"
                    body_mark = " [身宫]" if palace.get("is_body_palace") else ""

                    output += f"{palace_name}({palace.get('position', '未知')}){body_mark}: {stars_text}\n"

            return output

        except Exception as e:
            logger.error(f"格式化紫薇输出失败: {e}")
            return "紫薇斗数命盘格式化失败"

    def can_handle(self, intent: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查是否能处理该意图"""
        return self.validate_input(intent, context)

    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """验证输入参数"""
        try:
            # 检查是否是紫薇相关意图
            intent_type = intent.get("intent", "").lower()
            if intent_type not in self.get_supported_intents():
                return False

            # 检查是否有出生信息（可以从上下文获取）
            entities = intent.get("entities", {})
            context_birth = context.get("birth_info", {})

            # 检查必需的出生信息
            required_fields = ["birth_year", "birth_month", "birth_day", "birth_hour", "gender"]

            for field in required_fields:
                if not (entities.get(field) or context_birth.get(field)):
                    return False

            return True

        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False

    def get_supported_intents(self) -> List[str]:
        """获取支持的意图类型"""
        return ["ziwei", "ziwei_analysis", "紫薇", "紫薇斗数", "排盘", "命盘"]

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.name,
            "description": self.description,
            "supported_intents": self.get_supported_intents(),
            "required_entities": ["birth_year", "birth_month", "birth_day", "birth_hour", "gender"],
            "optional_entities": [],
            "output_type": "ziwei_analysis",
            "features": [
                "真实紫薇斗数算法",
                "十二宫星曜分析",
                "命盘排盘",
                "人性化交互"
            ]
        }

def test_humanized_ziwei_tool():
    """测试人性化紫薇工具"""
    print("🔮 测试人性化紫薇工具")
    print("-" * 50)

    try:
        # 创建工具实例
        tool = HumanizedZiweiTool()

        # 测试数据
        intent = {
            "intent": "ziwei",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }

        context = {}

        # 执行测试
        result = tool.execute(intent, context)

        if result.get("success"):
            print("✅ 紫薇工具执行成功")
            print(f"   类型: {result.get('type')}")
            print(f"   消息: {result.get('message')}")

            # 检查计算结果
            calc_result = result.get("calculation_result", {})
            if calc_result.get("success"):
                print("✅ 紫薇计算成功")
                raw_result = calc_result.get("raw_result", {})
                if "palaces" in raw_result:
                    print(f"   宫位数量: {len(raw_result['palaces'])}")
                    print(f"   出生信息: {raw_result.get('birth_info', {})}")
            else:
                print(f"❌ 紫薇计算失败: {calc_result.get('error')}")
        else:
            print(f"❌ 紫薇工具执行失败: {result.get('error')}")

        # 测试工具信息
        tool_info = tool.get_tool_info()
        print(f"\n工具信息:")
        print(f"   名称: {tool_info['name']}")
        print(f"   描述: {tool_info['description']}")
        print(f"   支持意图: {tool_info['supported_intents']}")

        return result.get("success", False)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_humanized_ziwei_tool()
