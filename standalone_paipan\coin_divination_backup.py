#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬币起卦模块
实现传统的六爻硬币起卦方法
"""

import random
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple


class CoinDivination:
    """硬币起卦类"""
    
    def __init__(self):
        """初始化硬币起卦器"""
        self.trigram_names = {
            0: "坤", 1: "震", 2: "坎", 3: "兑",
            4: "艮", 5: "离", 6: "巽", 7: "乾"
        }

        # 完整的64卦对照表
        self.hexagram_names = {
            (7, 7): "乾为天", (0, 0): "坤为地", (2, 7): "水天需", (7, 2): "天水讼",
            (0, 2): "地水师", (2, 0): "水地比", (7, 6): "天风小畜", (6, 7): "风天大畜",
            (0, 5): "地火明夷", (5, 0): "火地晋", (1, 0): "雷地豫", (0, 1): "地雷复",
            (7, 1): "天雷无妄", (1, 7): "雷天大壮", (5, 4): "火山旅", (4, 5): "山火贲",
            (2, 2): "坎为水", (5, 5): "离为火", (1, 1): "震为雷", (3, 3): "兑为泽",
            (6, 6): "巽为风", (4, 4): "艮为山", (7, 0): "天地否", (0, 7): "地天泰",
            (5, 2): "火水未济", (2, 5): "水火既济", (1, 4): "雷山小过", (4, 1): "山雷颐",
            (6, 3): "风泽中孚", (3, 6): "泽风大过", (2, 4): "水山蹇", (4, 2): "山水蒙",
            (3, 5): "泽火革", (5, 3): "火泽睽", (1, 2): "雷水解", (2, 1): "水雷屯",
            (6, 4): "风山渐", (4, 6): "山风蛊", (3, 7): "泽天夬", (7, 3): "天泽履",
            (0, 6): "地风升", (6, 0): "风地观", (5, 1): "火雷噬嗑", (1, 5): "雷火丰",
            (2, 3): "水泽节", (3, 2): "泽水困", (4, 7): "山天大畜", (7, 4): "天山遁",
            (0, 1): "地雷复", (1, 0): "雷地豫", (5, 6): "火风鼎", (6, 5): "风火家人",
            (2, 6): "水风井", (6, 2): "风水涣", (3, 4): "泽山咸", (4, 3): "山泽损",
            (7, 5): "天火同人", (5, 7): "火天大有", (0, 3): "地泽临", (3, 0): "泽地萃",
            (1, 6): "雷风恒", (6, 1): "风雷益", (2, 7): "水天需", (7, 2): "天水讼",
            (4, 0): "山地剥", (0, 4): "地山谦", (5, 2): "火水未济", (2, 5): "水火既济"
        }

        # 六神对应表（按甲子日起青龙）
        self.six_spirits = ["青龙", "朱雀", "勾陈", "腾蛇", "白虎", "玄武"]

        # 天干对应的六神起始位置（甲乙起青龙）
        self.spirit_start = {
            "甲": 0, "乙": 0,  # 甲乙日起青龙
            "丙": 1, "丁": 1,  # 丙丁日起朱雀
            "戊": 2, "己": 2,  # 戊己日起勾陈
            "庚": 3, "辛": 3,  # 庚辛日起腾蛇
            "壬": 4, "癸": 4   # 壬癸日起白虎
        }

        # 地支
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

        # 天干
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]

        # 地支对应的五行
        self.dizhi_wuxing = {
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", "巳": "火",
            "午": "火", "未": "土", "申": "金", "酉": "金", "戌": "土", "亥": "水"
        }

        # 八卦对应的五行
        self.bagua_wuxing = {
            "乾": "金", "兑": "金", "离": "火", "震": "木",
            "巽": "木", "坎": "水", "艮": "土", "坤": "土"
        }



    def throw_coins(self, manual_results: List[str] = None) -> List[Dict[str, Any]]:
        """
        投掷硬币6次，生成六爻
        
        Args:
            manual_results: 手动指定的硬币结果，格式如 ["正正反", "反正正", ...]
            
        Returns:
            六次投掷的结果列表
        """
        coin_throws = []
        
        for i in range(6):
            if manual_results and i < len(manual_results):
                # 使用手动指定的结果
                throw_result = manual_results[i]
                coins = list(throw_result)
            else:
                # 随机投掷3枚硬币
                coins = [random.choice(["正", "反"]) for _ in range(3)]
                throw_result = "".join(coins)
            
            # 计算爻的性质
            zheng_count = coins.count("正")  # 正面数量
            fan_count = coins.count("反")    # 反面数量
            
            # 根据传统规则确定爻的性质
            if zheng_count == 3:
                # 三个正面 = 老阳 = 动爻 = 阳爻变阴爻
                yao_type = "老阳"
                yao_symbol = "▅▅▅▅▅"
                is_moving = True
                changed_symbol = "▅▅  ▅▅"
            elif zheng_count == 2:
                # 两正一反 = 少阴 = 静爻 = 阴爻
                yao_type = "少阴"
                yao_symbol = "▅▅  ▅▅"
                is_moving = False
                changed_symbol = "▅▅  ▅▅"
            elif zheng_count == 1:
                # 一正两反 = 少阳 = 静爻 = 阳爻
                yao_type = "少阳"
                yao_symbol = "▅▅▅▅▅"
                is_moving = False
                changed_symbol = "▅▅▅▅▅"
            else:
                # 三个反面 = 老阴 = 动爻 = 阴爻变阳爻
                yao_type = "老阴"
                yao_symbol = "▅▅  ▅▅"
                is_moving = True
                changed_symbol = "▅▅▅▅▅"
            
            coin_throws.append({
                "throw_number": i + 1,
                "coins": coins,
                "throw_result": throw_result,
                "zheng_count": zheng_count,
                "fan_count": fan_count,
                "yao_type": yao_type,
                "yao_symbol": yao_symbol,
                "is_moving": is_moving,
                "changed_symbol": changed_symbol
            })
        
        return coin_throws

    def build_hexagram(self, coin_throws: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        根据硬币投掷结果构建卦象
        
        Args:
            coin_throws: 硬币投掷结果
            
        Returns:
            完整的卦象数据
        """
        # 构建主卦（从下到上：初爻到上爻）
        main_hexagram_lines = []
        changed_hexagram_lines = []
        moving_lines = []
        
        for i, throw in enumerate(coin_throws):
            main_hexagram_lines.append(throw["yao_symbol"])
            changed_hexagram_lines.append(throw["changed_symbol"])
            
            if throw["is_moving"]:
                moving_lines.append(i + 1)  # 爻位从1开始
        
        # 计算上卦和下卦
        upper_trigram_value = self._calculate_trigram_value(main_hexagram_lines[3:6])
        lower_trigram_value = self._calculate_trigram_value(main_hexagram_lines[0:3])
        
        upper_trigram_name = self.trigram_names.get(upper_trigram_value, "未知")
        lower_trigram_name = self.trigram_names.get(lower_trigram_value, "未知")
        
        # 获取卦名
        main_hexagram_name = self.hexagram_names.get(
            (upper_trigram_value, lower_trigram_value),
            f"{upper_trigram_name}{lower_trigram_name}"
        )

        # 如果有动爻，计算变卦
        changed_hexagram_name = main_hexagram_name
        if moving_lines:
            changed_upper_value = self._calculate_trigram_value(changed_hexagram_lines[3:6])
            changed_lower_value = self._calculate_trigram_value(changed_hexagram_lines[0:3])
            changed_hexagram_name = self.hexagram_names.get(
                (changed_upper_value, changed_lower_value),
                f"{self.trigram_names.get(changed_upper_value, '未知')}{self.trigram_names.get(changed_lower_value, '未知')}"
            )

        # 配置六亲和六神
        hexagram_details = self._configure_liuqin_liushen(
            main_hexagram_name, upper_trigram_value, lower_trigram_value, main_hexagram_lines
        )
        
        return {
            "main_hexagram": {
                "name": main_hexagram_name,
                "upper_trigram": upper_trigram_name,
                "lower_trigram": lower_trigram_name,
                "lines": main_hexagram_lines
            },
            "changed_hexagram": {
                "name": changed_hexagram_name,
                "lines": changed_hexagram_lines
            },
            "moving_lines": moving_lines,
            "coin_throws": coin_throws,
            "divination_time": datetime.now().isoformat(),
            "hexagram_details": hexagram_details
        }

    def _calculate_trigram_value(self, lines: List[str]) -> int:
        """
        计算三爻的八卦数值

        Args:
            lines: 三个爻的符号列表

        Returns:
            八卦数值 (0-7)
        """
        value = 0
        for i, line in enumerate(lines):
            if "▅▅▅▅▅" in line:  # 阳爻
                value += 2 ** i
        return value

    def _get_current_ganzhi_day(self) -> tuple:
        """
        获取当前日期的干支

        Returns:
            (天干, 地支, 干支日)
        """
        from datetime import datetime, date

        # 使用更准确的基准日期：1900年1月31日为庚子日
        # 但我们使用一个已知准确的日期作为基准
        # 2025年6月25日 = 乙丑日（根据万年历验证）
        base_date = date(2025, 6, 25)
        base_tiangan = "乙"
        base_dizhi = "丑"

        current_date = date.today()
        days_diff = (current_date - base_date).days

        # 计算基准日期的干支索引
        base_tiangan_index = self.tiangan.index(base_tiangan)
        base_dizhi_index = self.dizhi.index(base_dizhi)

        # 计算当前日期的干支索引
        current_tiangan_index = (base_tiangan_index + days_diff) % 10
        current_dizhi_index = (base_dizhi_index + days_diff) % 12

        tiangan = self.tiangan[current_tiangan_index]
        dizhi = self.dizhi[current_dizhi_index]

        return tiangan, dizhi, f"{tiangan}{dizhi}"

    def _calculate_xunkong(self, ganzhi_day: str) -> tuple:
        """
        计算旬空

        Args:
            ganzhi_day: 干支日

        Returns:
            (旬空地支1, 旬空地支2)
        """
        # 找到当前日干支在60甲子中的位置
        ganzhi_60 = []
        for i in range(60):
            tg = self.tiangan[i % 10]
            dz = self.dizhi[i % 12]
            ganzhi_60.append(f"{tg}{dz}")

        try:
            current_index = ganzhi_60.index(ganzhi_day)
        except ValueError:
            return "戌", "亥"  # 默认值

        # 找到当前旬的起始位置（甲子、甲戌、甲申、甲午、甲辰、甲寅）
        xun_start = (current_index // 10) * 10

        # 每旬的旬空地支
        xunkong_map = {
            0: ("戌", "亥"),   # 甲子旬
            10: ("申", "酉"),  # 甲戌旬
            20: ("午", "未"),  # 甲申旬
            30: ("辰", "巳"),  # 甲午旬
            40: ("寅", "卯"),  # 甲辰旬
            50: ("子", "丑")   # 甲寅旬
        }

        return xunkong_map.get(xun_start, ("戌", "亥"))

    def _calculate_shensha(self, day_tiangan: str, day_dizhi: str, yao_dizhi_list: list) -> dict:
        """
        计算神煞

        Args:
            day_tiangan: 日干
            day_dizhi: 日支
            yao_dizhi_list: 各爻地支列表

        Returns:
            神煞信息字典
        """
        shensha_info = {}

        # 驿马（以日支为准）
        yima_map = {
            "申": "寅", "子": "寅", "辰": "寅",  # 申子辰见寅
            "寅": "申", "午": "申", "戌": "申",  # 寅午戌见申
            "巳": "亥", "酉": "亥", "丑": "亥",  # 巳酉丑见亥
            "亥": "巳", "卯": "巳", "未": "巳"   # 亥卯未见巳
        }
        yima = yima_map.get(day_dizhi, "")

        # 桃花（以日支为准）
        taohua_map = {
            "申": "酉", "子": "酉", "辰": "酉",  # 申子辰见酉
            "寅": "卯", "午": "卯", "戌": "卯",  # 寅午戌见卯
            "巳": "午", "酉": "午", "丑": "午",  # 巳酉丑见午
            "亥": "子", "卯": "子", "未": "子"   # 亥卯未见子
        }
        taohua = taohua_map.get(day_dizhi, "")

        # 贵人（以日干为准）
        tianyi_map = {
            "甲": ["丑", "未"], "乙": ["子", "申"],
            "丙": ["亥", "酉"], "丁": ["亥", "酉"],
            "戊": ["丑", "未"], "己": ["子", "申"],
            "庚": ["丑", "未"], "辛": ["寅", "午"],
            "壬": ["卯", "巳"], "癸": ["卯", "巳"]
        }
        tianyi = tianyi_map.get(day_tiangan, [])

        # 检查各爻是否有神煞
        yao_shensha = []
        for i, dizhi in enumerate(yao_dizhi_list):
            yao_sha = []
            if dizhi == yima:
                yao_sha.append("驿马")
            if dizhi == taohua:
                yao_sha.append("桃花")
            if dizhi in tianyi:
                yao_sha.append("天乙贵人")
            yao_shensha.append(yao_sha)

        shensha_info = {
            "yima": yima,
            "taohua": taohua,
            "tianyi": tianyi,
            "yao_shensha": yao_shensha
        }

        return shensha_info

    def _configure_liuqin_liushen(self, hexagram_name: str, upper_trigram: int, lower_trigram: int, lines: List[str]) -> dict:
        """
        配置六亲和六神

        Args:
            hexagram_name: 卦名
            upper_trigram: 上卦数值
            lower_trigram: 下卦数值
            lines: 爻线列表

        Returns:
            包含六亲六神信息的字典
        """
        # 获取当前日期的干支
        day_tiangan, day_dizhi, ganzhi_day = self._get_current_ganzhi_day()

        # 计算旬空
        xunkong1, xunkong2 = self._calculate_xunkong(ganzhi_day)

        # 配置地支（使用传统纳甲方法）
        six_dizhi = self._calculate_najia_dizhi(upper_trigram, lower_trigram)

        # 计算神煞
        shensha_info = self._calculate_shensha(day_tiangan, day_dizhi, six_dizhi)

        # 配置六神（从初爻到上爻，根据日干起六神）
        spirit_start_index = self.spirit_start.get(day_tiangan, 0)
        six_spirits_config = []
        for i in range(6):
            spirit_index = (spirit_start_index + i) % 6
            six_spirits_config.append(self.six_spirits[spirit_index])



        # 获取卦宫五行（以下卦为准）
        lower_trigram_name = self.trigram_names[lower_trigram]
        gua_gong_wuxing = self.bagua_wuxing.get(lower_trigram_name, "土")

        # 配置六亲（根据地支五行与卦宫五行的关系）
        six_relatives_config = []
        for dizhi in six_dizhi:
            dizhi_wuxing = self.dizhi_wuxing[dizhi]
            relative = self._get_liuqin_by_wuxing(gua_gong_wuxing, dizhi_wuxing)
            six_relatives_config.append(relative)

        # 构建详细信息
        yao_details = []
        for i in range(6):
            yao_info = {
                "position": f"第{i+1}爻",
                "symbol": lines[i],
                "dizhi": six_dizhi[i],
                "wuxing": self.dizhi_wuxing[six_dizhi[i]],
                "liuqin": six_relatives_config[i],
                "liushen": six_spirits_config[i],
                "is_yang": "▅▅▅▅▅" in lines[i]
            }
            yao_details.append(yao_info)

        return {
            "gua_gong": lower_trigram_name,
            "gua_gong_wuxing": gua_gong_wuxing,
            "yao_details": yao_details,
            "six_spirits": six_spirits_config,
            "six_relatives": six_relatives_config,
            "six_dizhi": six_dizhi,
            "day_ganzhi": ganzhi_day,
            "day_tiangan": day_tiangan,
            "day_dizhi": day_dizhi,
            "xunkong": [xunkong1, xunkong2],
            "shensha": shensha_info
        }

    def _calculate_najia_dizhi(self, upper_trigram, lower_trigram):
        """
        根据传统纳甲理论计算六爻地支

        Args:
            upper_trigram: 上卦三爻卦值
            lower_trigram: 下卦三爻卦值

        Returns:
            六爻地支列表（从初爻到上爻）
        """
        # 传统纳甲地支配置表
        # 乾在内子寅辰，乾在外午申戌
        # 坎在内寅辰午，坎在外申戌子
        # 艮在内辰午申，艮在外戌子寅
        # 震在内子寅辰，震在外午申戌
        # 巽在内丑亥酉，巽在外未巳卯
        # 离在内卯丑亥，离在外酉未巳
        # 兑在内巳卯丑，兑在外亥酉未
        # 坤在内未巳卯，坤在外丑亥酉

        najia_map = {
            # 乾卦(7)
            7: {"inner": ["子", "寅", "辰"], "outer": ["午", "申", "戌"]},
            # 兑卦(6)
            6: {"inner": ["巳", "卯", "丑"], "outer": ["亥", "酉", "未"]},
            # 离卦(5)
            5: {"inner": ["卯", "丑", "亥"], "outer": ["酉", "未", "巳"]},
            # 震卦(4)
            4: {"inner": ["子", "寅", "辰"], "outer": ["午", "申", "戌"]},
            # 巽卦(3)
            3: {"inner": ["丑", "亥", "酉"], "outer": ["未", "巳", "卯"]},
            # 坎卦(2)
            2: {"inner": ["寅", "辰", "午"], "outer": ["申", "戌", "子"]},
            # 艮卦(1)
            1: {"inner": ["辰", "午", "申"], "outer": ["戌", "子", "寅"]},
            # 坤卦(0)
            0: {"inner": ["未", "巳", "卯"], "outer": ["丑", "亥", "酉"]}
        }

        # 获取下卦和上卦的地支
        lower_dizhi = najia_map.get(lower_trigram, {"inner": ["子", "寅", "辰"]})["inner"]
        upper_dizhi = najia_map.get(upper_trigram, {"outer": ["午", "申", "戌"]})["outer"]

        # 组合成六爻地支（从初爻到上爻）
        six_dizhi = lower_dizhi + upper_dizhi

        return six_dizhi

    def _calculate_changed_hexagram_najia(self, changed_hexagram_lines, main_gua_gong_wuxing, main_six_spirits):
        """
        计算变卦的纳甲信息

        Args:
            changed_hexagram_lines: 变卦爻线
            main_gua_gong_wuxing: 本卦宫五行（用于计算变卦六亲）
            main_six_spirits: 本卦六神（变卦六神不变）

        Returns:
            变卦的详细信息
        """
        # 计算变卦的三爻卦值
        changed_upper_trigram = self._calculate_trigram_value(changed_hexagram_lines[3:6])
        changed_lower_trigram = self._calculate_trigram_value(changed_hexagram_lines[0:3])

        # 配置变卦地支（重新计算）
        changed_six_dizhi = self._calculate_najia_dizhi(changed_upper_trigram, changed_lower_trigram)

        # 配置变卦六亲（仍以本卦宫属性为准）
        changed_six_relatives = []
        for dizhi in changed_six_dizhi:
            dizhi_wuxing = self.dizhi_wuxing[dizhi]
            relative = self._get_liuqin_by_wuxing(main_gua_gong_wuxing, dizhi_wuxing)  # 注意：仍用本卦宫属性
            changed_six_relatives.append(relative)

        # 构建变卦爻的详细信息
        changed_yao_details = []
        for i in range(6):
            yao_info = {
                "position": f"第{i+1}爻",
                "symbol": changed_hexagram_lines[i],
                "dizhi": changed_six_dizhi[i],
                "wuxing": self.dizhi_wuxing[changed_six_dizhi[i]],
                "liuqin": changed_six_relatives[i],
                "liushen": main_six_spirits[i],  # 六神不变
                "is_yang": "▅▅▅▅▅" in changed_hexagram_lines[i]
            }
            changed_yao_details.append(yao_info)

        return changed_yao_details

    def _get_liuqin_by_wuxing(self, gua_gong_wuxing: str, yao_wuxing: str) -> str:
        """
        根据五行关系确定六亲

        Args:
            gua_gong_wuxing: 卦宫五行
            yao_wuxing: 爻的五行

        Returns:
            六亲名称
        """
        # 五行生克关系
        wuxing_sheng = {
            "木": "火", "火": "土", "土": "金", "金": "水", "水": "木"
        }
        wuxing_ke = {
            "木": "土", "火": "金", "土": "水", "金": "木", "水": "火"
        }

        if yao_wuxing == gua_gong_wuxing:
            return "兄弟"
        elif wuxing_sheng.get(gua_gong_wuxing) == yao_wuxing:
            return "子孙"
        elif wuxing_ke.get(gua_gong_wuxing) == yao_wuxing:
            return "妻财"
        elif wuxing_ke.get(yao_wuxing) == gua_gong_wuxing:
            return "官鬼"
        elif wuxing_sheng.get(yao_wuxing) == gua_gong_wuxing:
            return "父母"
        else:
            return "兄弟"  # 默认

    def format_hexagram_display(self, hexagram_data: Dict[str, Any]) -> str:
        """
        格式化卦象显示

        Args:
            hexagram_data: 卦象数据

        Returns:
            格式化的卦象字符串
        """
        main_hex = hexagram_data["main_hexagram"]
        changed_hex = hexagram_data["changed_hexagram"]
        moving_lines = hexagram_data["moving_lines"]
        hexagram_details = hexagram_data.get("hexagram_details", {})

        output = f"""
═══════════════════════════════════════
            硬币起卦结果
═══════════════════════════════════════
起卦时间: {hexagram_data['divination_time'][:19].replace('T', ' ')}

【卦象信息】
主卦: {main_hex['name']} ({main_hex['upper_trigram']}上{main_hex['lower_trigram']}下)
"""

        if moving_lines:
            output += f"变卦: {changed_hex['name']}\n"
            output += f"动爻: 第{', '.join(map(str, moving_lines))}爻\n"

        if hexagram_details:
            output += f"卦宫: {hexagram_details.get('gua_gong', '未知')}宫 ({hexagram_details.get('gua_gong_wuxing', '未知')})\n"
            output += f"日期: {hexagram_details.get('day_ganzhi', '未知')}日\n"
            xunkong = hexagram_details.get('xunkong', [])
            if xunkong:
                output += f"旬空: {xunkong[0]}, {xunkong[1]}\n"

            # 添加神煞信息
            shensha = hexagram_details.get('shensha', {})
            if shensha:
                shensha_list = []
                if shensha.get('yima'):
                    shensha_list.append(f"驿马({shensha['yima']})")
                if shensha.get('taohua'):
                    shensha_list.append(f"桃花({shensha['taohua']})")
                if shensha.get('tianyi'):
                    tianyi_str = "、".join(shensha['tianyi'])
                    shensha_list.append(f"天乙贵人({tianyi_str})")
                if shensha_list:
                    output += f"神煞: {', '.join(shensha_list)}\n"

        # 添加卦象图形显示
        output += "\n【卦象图形】\n"

        if moving_lines:
            # 有动爻时，显示本卦和变卦
            output += "┌─────────────────┬─────────────────┐\n"
            output += "│      本卦       │      变卦       │\n"
            output += "├─────────────────┼─────────────────┤\n"

            yao_details = hexagram_details.get("yao_details", [])
            for i in range(5, -1, -1):  # 从上爻到初爻显示
                line_num = i + 1
                main_line = main_hex["lines"][i]
                changed_line = changed_hex["lines"][i]

                # 标准化本卦阴阳爻显示
                if main_line == "▅▅▅▅▅":
                    main_yao_symbol = "━━━━━━━━━"  # 阳爻
                else:
                    main_yao_symbol = "━━━　━━━"  # 阴爻，使用全角空格

                # 标准化变卦阴阳爻显示
                if changed_line == "▅▅▅▅▅":
                    changed_yao_symbol = "━━━━━━━━━"  # 阳爻
                else:
                    changed_yao_symbol = "━━━　━━━"  # 阴爻，使用全角空格

                # 动静标记
                if line_num in moving_lines:
                    dong_mark = "○"
                else:
                    dong_mark = "　"

                # 获取六亲和六神
                if i < len(yao_details):
                    yao_info = yao_details[i]
                    liuqin = yao_info.get("liuqin", "　")  # 使用全角空格作为占位符
                    liushen = yao_info.get("liushen", "　")  # 使用全角空格作为占位符
                else:
                    liuqin = liushen = "　"  # 使用全角空格作为占位符

                # 格式化显示，确保对齐
                output += f"│ {main_yao_symbol} {dong_mark} │ {changed_yao_symbol}   │\n"

            output += "└─────────────────┴─────────────────┘\n"
        else:
            # 无动爻时，只显示本卦
            output += "┌─────────────────────────────────────┐\n"
            yao_details = hexagram_details.get("yao_details", [])
            for i in range(5, -1, -1):  # 从上爻到初爻显示
                line_num = i + 1
                main_line = main_hex["lines"][i]

                # 标准化阴阳爻显示
                if main_line == "▅▅▅▅▅":
                    yao_symbol = "━━━━━━━━━"  # 阳爻
                else:
                    yao_symbol = "━━━　━━━"  # 阴爻，使用全角空格

                # 获取六亲和六神
                if i < len(yao_details):
                    yao_info = yao_details[i]
                    liuqin = yao_info.get("liuqin", "　")  # 使用全角空格作为占位符
                    liushen = yao_info.get("liushen", "　")  # 使用全角空格作为占位符
                else:
                    liuqin = liushen = "　"  # 使用全角空格作为占位符

                # 格式化显示，确保对齐
                output += f"│ {yao_symbol}   {liuqin:>2} {liushen:>2} │\n"

            output += "└─────────────────────────────────────┘\n"

        # 显示本卦详细信息
        output += "\n【本卦详细】\n"
        output += "┌──────┬─────────────┬────┬────┬────┬────┬────┐\n"
        output += "│ 爻位 │    卦象     │地支│五行│六亲│六神│动静│\n"
        output += "├──────┼─────────────┼────┼────┼────┼────┼────┤\n"

        # 显示本卦（从上到下）
        yao_details = hexagram_details.get("yao_details", [])
        for i in range(5, -1, -1):  # 从上爻到初爻
            line_num = i + 1
            main_line = main_hex["lines"][i]

            # 标准化阴阳爻显示
            if main_line == "▅▅▅▅▅":
                yao_display = "━━━━━━━━━"  # 阳爻
            else:
                yao_display = "━━━　━━━"  # 阴爻，使用全角空格

            # 获取详细信息
            if i < len(yao_details):
                yao_info = yao_details[i]
                dizhi = yao_info.get("dizhi", "　")  # 使用全角空格作为占位符
                wuxing = yao_info.get("wuxing", "　")  # 使用全角空格作为占位符
                liuqin = yao_info.get("liuqin", "　")  # 使用全角空格作为占位符
                liushen = yao_info.get("liushen", "　")  # 使用全角空格作为占位符
            else:
                dizhi = wuxing = liuqin = liushen = "　"  # 使用全角空格作为占位符

            # 动静标记
            if line_num in moving_lines:
                dong_jing = "动○"
            else:
                dong_jing = "静"

            output += f"│第{line_num}爻│ {yao_display} │ {dizhi} │ {wuxing} │ {liuqin} │ {liushen} │ {dong_jing} │\n"

        output += "└──────┴─────────────┴────┴────┴────┴────┴────┘\n"

        # 如果有动爻，显示变卦详细信息
        if moving_lines:
            output += "\n【变卦详细】\n"
            output += "┌──────┬─────────────┬────┬────┬────┬────┬────┐\n"
            output += "│ 爻位 │    卦象     │地支│五行│六亲│六神│动静│\n"
            output += "├──────┼─────────────┼────┼────┼────┼────┼────┤\n"

            # 计算变卦的纳甲信息
            main_gua_gong_wuxing = hexagram_details.get("gua_gong_wuxing", "土")
            main_six_spirits = [yao.get("liushen", "　") for yao in yao_details]
            changed_yao_details = self._calculate_changed_hexagram_najia(
                changed_hex["lines"], main_gua_gong_wuxing, main_six_spirits
            )

            # 显示变卦（从上到下）
            for i in range(5, -1, -1):  # 从上爻到初爻
                line_num = i + 1
                changed_line = changed_hex["lines"][i]

                # 标准化变卦阴阳爻显示
                if changed_line == "▅▅▅▅▅":
                    changed_yao_display = "━━━━━━━━━"  # 阳爻
                else:
                    changed_yao_display = "━━━　━━━"  # 阴爻，使用全角空格

                # 使用重新计算的变卦纳甲信息
                if i < len(changed_yao_details):
                    yao_info = changed_yao_details[i]
                    dizhi = yao_info.get("dizhi", "　")
                    wuxing = yao_info.get("wuxing", "　")
                    liuqin = yao_info.get("liuqin", "　")
                    liushen = yao_info.get("liushen", "　")
                else:
                    dizhi = wuxing = liuqin = liushen = "　"

                # 变卦中所有爻都是静爻
                dong_jing = "静"

                output += f"│第{line_num}爻│ {changed_yao_display} │ {dizhi} │ {wuxing} │ {liuqin} │ {liushen} │ {dong_jing} │\n"

            output += "└──────┴─────────────┴────┴────┴────┴────┴────┘\n"

        # 显示硬币投掷详情
        output += "\n【硬币投掷详情】\n"
        for throw in hexagram_data["coin_throws"]:
            output += f"第{throw['throw_number']}次: {throw['throw_result']} → {throw['yao_type']}\n"

        output += "\n═══════════════════════════════════════\n"

        return output

    def divine_with_coins(self, question: str, manual_results: List[str] = None) -> Dict[str, Any]:
        """
        完整的硬币起卦流程
        
        Args:
            question: 占卜问题
            manual_results: 手动指定的硬币结果
            
        Returns:
            完整的占卜结果
        """
        try:
            # 投掷硬币
            coin_throws = self.throw_coins(manual_results)
            
            # 构建卦象
            hexagram_data = self.build_hexagram(coin_throws)
            
            # 格式化显示
            formatted_output = self.format_hexagram_display(hexagram_data)
            
            return {
                "success": True,
                "method": "硬币起卦",
                "question": question,
                "divination_data": {
                    "coin_throws": coin_throws,
                    "manual_results": manual_results
                },
                "hexagram_data": hexagram_data,
                "formatted_output": formatted_output,
                "divination_type": "六爻算卦"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"硬币起卦失败: {str(e)}"
            }


def test_coin_divination():
    """测试硬币起卦功能"""
    print("=== 硬币起卦测试 ===")
    
    divination = CoinDivination()
    
    # 测试1: 随机硬币起卦
    print("\n1. 随机硬币起卦测试:")
    result1 = divination.divine_with_coins("我的事业发展如何？")
    if result1["success"]:
        print("起卦成功!")
        print(result1["formatted_output"])
    else:
        print(f"起卦失败: {result1['error']}")
    
    # 测试2: 手动指定硬币结果
    print("\n2. 手动硬币结果测试:")
    manual_coins = ["正正反", "反正正", "正反反", "反反反", "正正正", "反正反"]
    result2 = divination.divine_with_coins("感情运势如何？", manual_coins)
    if result2["success"]:
        print("起卦成功!")
        print(result2["formatted_output"])
    else:
        print(f"起卦失败: {result2['error']}")


if __name__ == "__main__":
    test_coin_divination()
