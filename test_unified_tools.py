#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一工具接口测试 - 阶段3.1测试
"""

import sys
import os
sys.path.append('.')

def test_tool_interface_unification():
    """测试工具接口统一"""
    print("🔧 测试工具接口统一")
    print("-" * 50)
    
    try:
        # 测试现有工具的接口
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        print("✅ 算法模块导入成功")
        
        # 检查紫薇算法接口
        ziwei_calc = RealZiweiCalculator()
        ziwei_methods = [method for method in dir(ziwei_calc) if not method.startswith('_')]
        print(f"紫薇算法方法: {ziwei_methods}")
        
        # 检查八字算法接口
        bazi_calc = RealBaziCalculator()
        bazi_methods = [method for method in dir(bazi_calc) if not method.startswith('_')]
        print(f"八字算法方法: {bazi_methods}")
        
        return True
        
    except Exception as e:
        print(f"❌ 接口检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_unified_wrapper():
    """测试创建统一包装器"""
    print("\n🎯 测试创建统一包装器")
    print("-" * 50)
    
    try:
        # 创建统一的算命工具包装器
        class UnifiedFortuneCalculator:
            """统一的算命计算器包装器"""
            
            def __init__(self):
                self.ziwei_calc = None
                self.bazi_calc = None
                self._init_calculators()
            
            def _init_calculators(self):
                """初始化算法"""
                try:
                    from algorithms.real_ziwei_calculator import RealZiweiCalculator
                    self.ziwei_calc = RealZiweiCalculator()
                    print("✅ 紫薇算法初始化成功")
                except Exception as e:
                    print(f"⚠️ 紫薇算法初始化失败: {e}")
                
                try:
                    from algorithms.real_bazi_calculator import RealBaziCalculator
                    self.bazi_calc = RealBaziCalculator()
                    print("✅ 八字算法初始化成功")
                except Exception as e:
                    print(f"⚠️ 八字算法初始化失败: {e}")
            
            def calculate(self, method: str, year: int, month: int, day: int, 
                         hour: int, gender: str = "男") -> dict:
                """统一的计算接口"""
                try:
                    if method == "ziwei":
                        if not self.ziwei_calc:
                            return {"error": "紫薇算法不可用"}
                        return self.ziwei_calc.calculate_chart(year, month, day, hour, gender)
                    
                    elif method == "bazi":
                        if not self.bazi_calc:
                            return {"error": "八字算法不可用"}
                        return self.bazi_calc.calculate_bazi(year, month, day, hour, 0, gender)
                    
                    else:
                        return {"error": f"不支持的算命方法: {method}"}
                        
                except Exception as e:
                    return {"error": f"计算失败: {str(e)}"}
            
            def get_supported_methods(self) -> list:
                """获取支持的算命方法"""
                methods = []
                if self.ziwei_calc:
                    methods.append("ziwei")
                if self.bazi_calc:
                    methods.append("bazi")
                return methods
        
        # 测试统一包装器
        calculator = UnifiedFortuneCalculator()
        
        supported_methods = calculator.get_supported_methods()
        print(f"✅ 支持的算命方法: {supported_methods}")
        
        # 测试紫薇计算
        if "ziwei" in supported_methods:
            print("\n测试紫薇斗数计算...")
            ziwei_result = calculator.calculate("ziwei", 1988, 6, 1, 12, "男")
            if "error" in ziwei_result:
                print(f"❌ 紫薇计算失败: {ziwei_result['error']}")
            else:
                print("✅ 紫薇计算成功")
                print(f"   出生信息: {ziwei_result.get('birth_info', {})}")
                print(f"   宫位数量: {len(ziwei_result.get('palaces', {}))}")
        
        # 测试八字计算
        if "bazi" in supported_methods:
            print("\n测试八字算命计算...")
            bazi_result = calculator.calculate("bazi", 1988, 6, 1, 12, "男")
            if "error" in bazi_result:
                print(f"❌ 八字计算失败: {bazi_result['error']}")
            else:
                print("✅ 八字计算成功")
                print(f"   计算类型: {bazi_result.get('calculation_type', 'N/A')}")
                print(f"   成功状态: {bazi_result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一包装器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selector_integration():
    """测试与工具选择器的集成"""
    print("\n🔗 测试与工具选择器集成")
    print("-" * 50)
    
    try:
        # 修复工具选择器中的方法调用
        from core.tools.tool_selector import ToolSelector
        
        # 创建修复版本的工具选择器
        class FixedToolSelector(ToolSelector):
            """修复的工具选择器"""
            
            def _ziwei_handler(self, intent_result, context):
                """修复的紫薇斗数处理器"""
                try:
                    from algorithms.real_ziwei_calculator import RealZiweiCalculator
                    
                    entities = intent_result.get("entities", {})
                    birth_info = self._extract_birth_info(entities, context)
                    
                    calculator = RealZiweiCalculator()
                    
                    # 使用正确的方法名
                    result = calculator.calculate_chart(
                        year=int(birth_info["birth_year"]),
                        month=int(birth_info["birth_month"]),
                        day=int(birth_info["birth_day"]),
                        hour=self._convert_hour_to_int(birth_info["birth_hour"]),
                        gender=birth_info["gender"]
                    )
                    
                    return {
                        "type": "ziwei_analysis",
                        "calculation_result": result,
                        "message": "紫薇斗数命盘分析完成",
                        "birth_info": birth_info
                    }
                    
                except Exception as e:
                    return {
                        "type": "error",
                        "message": f"紫薇斗数计算出现问题: {str(e)}"
                    }
            
            def _bazi_handler(self, intent_result, context):
                """修复的八字算命处理器"""
                try:
                    from algorithms.real_bazi_calculator import RealBaziCalculator
                    
                    entities = intent_result.get("entities", {})
                    birth_info = self._extract_birth_info(entities, context)
                    
                    calculator = RealBaziCalculator()
                    
                    # 使用正确的方法名
                    result = calculator.calculate_bazi(
                        year=int(birth_info["birth_year"]),
                        month=int(birth_info["birth_month"]),
                        day=int(birth_info["birth_day"]),
                        hour=self._convert_hour_to_int(birth_info["birth_hour"]),
                        minute=0,
                        gender=birth_info["gender"]
                    )
                    
                    return {
                        "type": "bazi_analysis",
                        "calculation_result": result,
                        "message": "八字命理分析完成",
                        "birth_info": birth_info
                    }
                    
                except Exception as e:
                    return {
                        "type": "error",
                        "message": f"八字计算出现问题: {str(e)}"
                    }
            
            def _convert_hour_to_int(self, hour_input):
                """转换时辰为整数小时"""
                if isinstance(hour_input, int):
                    return hour_input
                
                if isinstance(hour_input, str):
                    try:
                        return int(hour_input)
                    except ValueError:
                        pass
                    
                    time_mapping = {
                        "子时": 0, "丑时": 2, "寅时": 4, "卯时": 6,
                        "辰时": 8, "巳时": 10, "午时": 12, "未时": 14,
                        "申时": 16, "酉时": 18, "戌时": 20, "亥时": 22
                    }
                    
                    return time_mapping.get(hour_input, 12)
                
                return 12
        
        # 测试修复后的工具选择器
        selector = FixedToolSelector()
        print("✅ 修复版工具选择器创建成功")
        
        # 测试紫薇斗数
        ziwei_intent = {
            "intent": "ziwei",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        print("\n测试紫薇斗数工具选择...")
        ziwei_result = selector.select_tool(ziwei_intent, {})
        
        if ziwei_result.get("success"):
            result_data = ziwei_result.get("result", {})
            if result_data.get("type") == "ziwei_analysis":
                print("✅ 紫薇斗数工具选择和执行成功")
                calc_result = result_data.get("calculation_result", {})
                if "error" not in calc_result:
                    print(f"✅ 紫薇计算成功，包含 {len(calc_result.get('palaces', {}))} 个宫位")
                else:
                    print(f"⚠️ 紫薇计算有问题: {calc_result.get('error')}")
            else:
                print(f"⚠️ 结果类型: {result_data.get('type')}")
                print(f"消息: {result_data.get('message', 'N/A')}")
        else:
            print(f"❌ 紫薇工具选择失败: {ziwei_result.get('error')}")
        
        # 测试八字算命
        bazi_intent = {
            "intent": "bazi",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        print("\n测试八字算命工具选择...")
        bazi_result = selector.select_tool(bazi_intent, {})
        
        if bazi_result.get("success"):
            result_data = bazi_result.get("result", {})
            if result_data.get("type") == "bazi_analysis":
                print("✅ 八字算命工具选择和执行成功")
                calc_result = result_data.get("calculation_result", {})
                if calc_result.get("success"):
                    print(f"✅ 八字计算成功: {calc_result.get('calculation_type', 'N/A')}")
                else:
                    print(f"⚠️ 八字计算有问题: {calc_result.get('error')}")
            else:
                print(f"⚠️ 结果类型: {result_data.get('type')}")
                print(f"消息: {result_data.get('message', 'N/A')}")
        else:
            print(f"❌ 八字工具选择失败: {bazi_result.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具选择器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 阶段3.1：工具基类设计和接口统一测试")
    print("=" * 70)
    
    # 测试结果
    results = []
    
    # 1. 工具接口统一测试
    results.append(("工具接口检查", test_tool_interface_unification()))
    
    # 2. 统一包装器测试
    results.append(("统一包装器", test_create_unified_wrapper()))
    
    # 3. 工具选择器集成测试
    results.append(("工具选择器集成", test_tool_selector_integration()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 阶段3.1测试全部通过！工具接口统一成功！")
        print("\n🎯 完成功能:")
        print("  ✅ 算法接口检查和分析")
        print("  ✅ 统一包装器创建")
        print("  ✅ 工具选择器接口修复")
        print("  ✅ 端到端算法调用验证")
        print("\n📋 下一步：开始阶段3.2 - 紫薇工具迁移")
    else:
        print("💥 部分测试失败，需要修复问题后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
