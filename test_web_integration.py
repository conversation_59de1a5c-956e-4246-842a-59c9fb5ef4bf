#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web集成效果
验证紫薇+八字融合分析在Web端的表现
"""

import asyncio
import sys
import time
import requests
import json
from datetime import datetime

def test_api_integration():
    """测试API集成"""
    try:
        print("🚀 测试API集成")
        print("=" * 60)
        
        # API配置
        api_url = "http://localhost:8002"
        
        # 测试健康检查
        print("1. 测试API健康状态...")
        try:
            response = requests.get(f"{api_url}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API健康状态: {health_data.get('status')}")
                print(f"📅 版本: {health_data.get('version')}")
            else:
                print(f"❌ API健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接API: {e}")
            print("💡 请先启动API服务器: python main.py --api-only")
            return False
        
        # 测试融合分析
        print("\n2. 测试紫薇+八字融合分析...")
        
        test_message = "我是1988年6月1日午时出生的男性，请帮我分析命运"
        
        chat_data = {
            "message": test_message,
            "session_id": "test_session_001"
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{api_url}/v2/chat",
                json=chat_data,
                timeout=60
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ 融合分析请求成功")
                print(f"⏱️ 响应时间: {response_time:.2f}秒")
                print(f"🎯 成功状态: {result.get('success')}")
                print(f"🔧 使用工具: {result.get('tool_used', '无')}")
                
                # 检查HTML图表
                if result.get('chart_html'):
                    html_length = len(result['chart_html'])
                    print(f"🌐 HTML图表: {html_length}字符")
                    
                    # 保存HTML文件用于测试
                    with open("test_chart_output.html", "w", encoding="utf-8") as f:
                        f.write(result['chart_html'])
                    print(f"💾 HTML图表已保存: test_chart_output.html")
                else:
                    print(f"⚠️ 未生成HTML图表")
                
                # 检查分析内容
                message_length = len(result.get('message', ''))
                print(f"📝 分析内容: {message_length}字符")
                
                if message_length > 500:
                    print(f"✅ 分析内容充实")
                else:
                    print(f"⚠️ 分析内容较少")
                
                return True
                
            else:
                print(f"❌ 融合分析请求失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 融合分析请求异常: {e}")
            return False
        
    except Exception as e:
        print(f"❌ API集成测试失败: {e}")
        return False

def test_web_interface():
    """测试Web界面"""
    try:
        print("\n🌐 测试Web界面")
        print("=" * 60)
        
        # 检查Web界面文件
        import os
        web_file = "interfaces/web_interface.py"
        
        if os.path.exists(web_file):
            print(f"✅ Web界面文件存在: {web_file}")
            
            # 检查关键修改
            with open(web_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ("紫薇+八字融合分析", "功能描述更新"),
                ("chart_html", "HTML图表支持"),
                ("components.v1.html", "HTML组件渲染"),
                ("双重印证", "融合分析说明")
            ]
            
            for keyword, description in checks:
                if keyword in content:
                    print(f"✅ {description}: 已更新")
                else:
                    print(f"⚠️ {description}: 未找到关键词 '{keyword}'")
            
            return True
        else:
            print(f"❌ Web界面文件不存在: {web_file}")
            return False
            
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False

def test_fusion_tool():
    """测试融合工具"""
    try:
        print("\n🔧 测试融合工具")
        print("=" * 60)
        
        # 检查融合工具文件
        import os
        tool_file = "core/tools/fusion_tool.py"
        
        if os.path.exists(tool_file):
            print(f"✅ 融合工具文件存在: {tool_file}")
            
            # 尝试导入融合工具
            try:
                from core.tools.fusion_tool import FusionTool
                print(f"✅ 融合工具导入成功")
                
                # 检查工具信息
                # 创建模拟实例进行测试
                class MockEngine:
                    pass
                
                class MockLLM:
                    def chat_completion(self, messages, max_tokens=None):
                        return '{"year": "1988", "month": "6", "day": "1", "hour": "12", "gender": "男"}'
                
                class MockPrompt:
                    pass
                
                tool = FusionTool(MockEngine(), MockLLM(), MockPrompt())
                tool_info = tool.get_tool_info()
                
                print(f"🔧 工具名称: {tool_info.get('name')}")
                print(f"📋 工具描述: {tool_info.get('description')}")
                print(f"🎯 支持意图: {len(tool_info.get('supported_intents', []))}")
                print(f"📝 必需信息: {len(tool_info.get('required_info', []))}")
                print(f"⭐ 功能特性: {len(tool_info.get('features', []))}")
                
                return True
                
            except Exception as e:
                print(f"❌ 融合工具导入失败: {e}")
                return False
        else:
            print(f"❌ 融合工具文件不存在: {tool_file}")
            return False
            
    except Exception as e:
        print(f"❌ 融合工具测试失败: {e}")
        return False

def test_html_generation():
    """测试HTML生成"""
    try:
        print("\n🎨 测试HTML生成")
        print("=" * 60)
        
        # 检查HTML生成器
        import os
        html_file = "generate_html_ziwei.py"
        
        if os.path.exists(html_file):
            print(f"✅ HTML生成器存在: {html_file}")
            
            # 尝试生成HTML
            try:
                from generate_html_ziwei import generate_html_ziwei
                
                print(f"🎨 开始生成测试HTML...")
                success = generate_html_ziwei()
                
                if success:
                    print(f"✅ HTML生成成功")
                    
                    # 检查生成的文件
                    if os.path.exists("ziwei_chart.html"):
                        file_size = os.path.getsize("ziwei_chart.html")
                        print(f"📄 HTML文件大小: {file_size} 字节")
                        
                        if file_size > 10000:  # 至少10KB
                            print(f"✅ HTML文件内容充实")
                        else:
                            print(f"⚠️ HTML文件内容较少")
                    
                    return True
                else:
                    print(f"❌ HTML生成失败")
                    return False
                    
            except Exception as e:
                print(f"❌ HTML生成异常: {e}")
                return False
        else:
            print(f"❌ HTML生成器不存在: {html_file}")
            return False
            
    except Exception as e:
        print(f"❌ HTML生成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Web集成测试")
    print("=" * 80)
    print("测试内容:")
    print("1. API集成 - 验证融合分析API")
    print("2. Web界面 - 验证界面修改")
    print("3. 融合工具 - 验证工具实现")
    print("4. HTML生成 - 验证图表生成")
    print("=" * 80)
    
    # 执行测试
    results = []
    
    # 测试HTML生成（不依赖API）
    results.append(("HTML生成", test_html_generation()))
    
    # 测试融合工具
    results.append(("融合工具", test_fusion_tool()))
    
    # 测试Web界面
    results.append(("Web界面", test_web_interface()))
    
    # 测试API集成
    results.append(("API集成", test_api_integration()))
    
    # 总结结果
    print(f"\n🎯 Web集成测试结果")
    print("=" * 80)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📊 总体结果: {success_count}/{len(results)} 通过")
    
    if success_count >= 3:
        print(f"\n🎉 Web集成基本成功！")
        print(f"\n🚀 启动方式:")
        print(f"  1. 启动API: python main.py --api-only")
        print(f"  2. 启动Web: python main.py --web-only")
        print(f"  3. 访问: http://localhost:8501")
        
        print(f"\n🌟 集成特点:")
        print(f"  ✅ 只保留紫薇+八字融合分析")
        print(f"  ✅ 移除独立的紫薇、八字、六爻功能")
        print(f"  ✅ 集成HTML图表生成")
        print(f"  ✅ 现代化Web界面")
        
        return True
    else:
        print(f"\n💥 Web集成需要进一步完善")
        print(f"  请检查失败的测试项目")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
