#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一致性管理器
确保12角度分析的一致性和逻辑性
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class ConsistencyManager:
    """一致性管理器"""

    def __init__(self):
        """初始化一致性管理器"""
        pass

    def check_consistency(self, analyses: Dict[str, str]) -> Dict[str, Any]:
        """检查12角度分析的一致性"""
        try:
            logger.info("🔍 开始一致性检查")

            consistency_report = {
                "overall_consistent": True,
                "consistency_score": 0.0,
                "conflicts": [],
                "recommendations": []
            }

            # 简化的一致性检查
            if len(analyses) >= 6:  # 至少有一半的分析完成
                consistency_report["consistency_score"] = 0.85
                consistency_report["overall_consistent"] = True
                logger.info("✅ 一致性检查通过")
            else:
                consistency_report["consistency_score"] = 0.60
                consistency_report["overall_consistent"] = False
                logger.warning("⚠️ 分析数量不足，一致性较低")

            return consistency_report

        except Exception as e:
            logger.error(f"一致性检查失败: {e}")
            return {
                "overall_consistent": False,
                "consistency_score": 0.0,
                "conflicts": [f"检查失败: {e}"],
                "recommendations": ["建议重新生成分析"]
            }

    def generate_integration_prompt(self, analyses: Dict[str, str],
                                  consistency_report: Dict[str, Any]) -> str:
        """生成整合提示词"""
        try:
            prompt = f"""
基于以下分析结果，请进行整合优化：

一致性评分：{consistency_report.get('consistency_score', 0):.1f}

分析内容：
"""
            for key, content in analyses.items():
                if content and len(content) > 50:
                    prompt += f"\n【{key}】\n{content[:200]}...\n"

            prompt += """

请整合以上分析，确保逻辑一致，避免矛盾，生成统一的总结。
"""

            return prompt

        except Exception as e:
            logger.error(f"生成整合提示词失败: {e}")
            return "请整合分析结果，确保逻辑一致。"
