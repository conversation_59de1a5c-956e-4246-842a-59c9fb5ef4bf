<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六爻占卜 - 独立排盘系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .divination-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .method-tabs {
            display: flex;
            margin-bottom: 20px;
            background: #e9ecef;
            border-radius: 10px;
            padding: 5px;
        }

        .method-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            color: #666;
        }

        .method-tab.active {
            background: #667eea;
            color: white;
        }

        .method-content {
            display: none;
        }

        .method-content.active {
            display: block;
        }

        .coin-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .coin-throw {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .coin-throw h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .coin-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .coin-btn {
            padding: 8px 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .coin-btn.active {
            background: #667eea;
            color: white;
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            display: none;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .hexagram-display {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .hexagram-lines {
            font-family: 'Courier New', monospace;
            font-size: 1.5em;
            line-height: 1.2;
            margin: 20px 0;
        }

        .hexagram-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid #667eea;
        }

        .info-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
            display: none;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            border: 2px solid #667eea;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: #667eea;
            color: white;
        }

        @media (max-width: 768px) {
            .coin-grid {
                grid-template-columns: 1fr;
            }
            
            .method-tabs {
                flex-direction: column;
            }
            
            .hexagram-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="back-btn">← 返回首页</a>
    
    <div class="container">
        <div class="header">
            <h1>六爻占卜</h1>
            <p>传统易学占卜，洞察事物发展趋势</p>
        </div>

        <div class="content">
            <div class="divination-form">
                <div class="form-group">
                    <label for="question">占卜问题</label>
                    <textarea id="question" placeholder="请输入您要占卜的问题，如：工作发展如何？感情运势怎样？等"></textarea>
                </div>

                <div class="form-group">
                    <label>起卦方式</label>
                    <div class="method-tabs">
                        <div class="method-tab active" data-method="time">时间起卦</div>
                        <div class="method-tab" data-method="coins">硬币起卦</div>
                        <div class="method-tab" data-method="numbers">数字起卦</div>
                    </div>
                </div>

                <!-- 时间起卦 -->
                <div class="method-content active" id="time-method">
                    <p style="color: #666; margin-bottom: 15px;">
                        系统将根据当前时间自动起卦，无需额外操作。
                    </p>
                </div>

                <!-- 硬币起卦 -->
                <div class="method-content" id="coins-method">
                    <p style="color: #666; margin-bottom: 15px;">
                        请连续投掷硬币6次，每次投掷3枚硬币，记录正反面结果：
                    </p>
                    <div class="coin-grid" id="coin-throws">
                        <!-- 动态生成硬币投掷界面 -->
                    </div>
                </div>

                <!-- 数字起卦 -->
                <div class="method-content" id="numbers-method">
                    <p style="color: #666; margin-bottom: 15px;">
                        请输入两个数字（1-999），系统将根据数字起卦：
                    </p>
                    <div style="display: flex; gap: 15px;">
                        <input type="number" id="number1" placeholder="第一个数字" min="1" max="999">
                        <input type="number" id="number2" placeholder="第二个数字" min="1" max="999">
                    </div>
                </div>

                <button class="btn" id="startDivination">开始占卜</button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在进行占卜分析，请稍候...</p>
            </div>

            <div class="error" id="error"></div>

            <div class="result-section" id="resultSection">
                <h3 style="color: #667eea; margin-bottom: 20px;">占卜结果</h3>
                <div id="divinationResult"></div>
            </div>
        </div>
    </div>

    <script>
        // 初始化硬币投掷界面
        function initCoinThrows() {
            const container = document.getElementById('coin-throws');
            container.innerHTML = '';
            
            for (let i = 1; i <= 6; i++) {
                const throwDiv = document.createElement('div');
                throwDiv.className = 'coin-throw';
                throwDiv.innerHTML = `
                    <h4>第${i}次投掷</h4>
                    <div class="coin-buttons">
                        <button class="coin-btn" data-throw="${i}" data-result="正正正">正正正</button>
                        <button class="coin-btn" data-throw="${i}" data-result="正正反">正正反</button>
                        <button class="coin-btn" data-throw="${i}" data-result="正反正">正反正</button>
                        <button class="coin-btn" data-throw="${i}" data-result="正反反">正反反</button>
                        <button class="coin-btn" data-throw="${i}" data-result="反正正">反正正</button>
                        <button class="coin-btn" data-throw="${i}" data-result="反正反">反正反</button>
                        <button class="coin-btn" data-throw="${i}" data-result="反反正">反反正</button>
                        <button class="coin-btn" data-throw="${i}" data-result="反反反">反反反</button>
                    </div>
                `;
                container.appendChild(throwDiv);
            }
        }

        // 初始化
        initCoinThrows();

        // 方法切换
        document.querySelectorAll('.method-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 切换标签状态
                document.querySelectorAll('.method-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 切换内容
                document.querySelectorAll('.method-content').forEach(c => c.classList.remove('active'));
                document.getElementById(this.dataset.method + '-method').classList.add('active');
            });
        });

        // 硬币按钮点击
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('coin-btn')) {
                const throwNum = e.target.dataset.throw;
                // 清除同一次投掷的其他选择
                document.querySelectorAll(`[data-throw="${throwNum}"]`).forEach(btn => {
                    btn.classList.remove('active');
                });
                // 激活当前选择
                e.target.classList.add('active');
            }
        });

        // 开始占卜
        document.getElementById('startDivination').addEventListener('click', function() {
            const question = document.getElementById('question').value.trim();
            const activeMethod = document.querySelector('.method-tab.active').dataset.method;
            
            if (!question) {
                showError('请输入占卜问题');
                return;
            }
            
            let requestData = {
                question: question,
                method: activeMethod
            };
            
            // 根据不同方法收集数据
            if (activeMethod === 'coins') {
                const coinResults = [];
                for (let i = 1; i <= 6; i++) {
                    const activeBtn = document.querySelector(`[data-throw="${i}"].active`);
                    if (!activeBtn) {
                        showError(`请完成第${i}次硬币投掷`);
                        return;
                    }
                    coinResults.push(activeBtn.dataset.result.split(''));
                }
                requestData.coin_results = coinResults;
            } else if (activeMethod === 'numbers') {
                const num1 = document.getElementById('number1').value;
                const num2 = document.getElementById('number2').value;
                if (!num1 || !num2) {
                    showError('请输入两个数字');
                    return;
                }
                requestData.numbers = [parseInt(num1), parseInt(num2)];
            }
            
            // 开始占卜
            startDivination(requestData);
        });

        function startDivination(data) {
            showLoading(true);
            hideError();
            
            fetch('/api/liuyao/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 轮询获取结果
                    pollResult(result.liuyao_id);
                } else {
                    showLoading(false);
                    showError(result.error || '占卜创建失败');
                }
            })
            .catch(error => {
                showLoading(false);
                showError('网络错误：' + error.message);
                console.error('占卜错误:', error);
            });
        }

        function pollResult(liuyaoId) {
            const poll = () => {
                fetch(`/api/liuyao/${liuyaoId}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        if (result.status === 'completed') {
                            showLoading(false);
                            displayResult(result);
                        } else if (result.status === 'failed') {
                            showLoading(false);
                            showError('占卜分析失败：' + result.analysis_content);
                        } else {
                            // 继续轮询
                            setTimeout(poll, 2000);
                        }
                    } else {
                        showLoading(false);
                        showError(result.error || '获取结果失败');
                    }
                })
                .catch(error => {
                    showLoading(false);
                    showError('网络错误：' + error.message);
                    console.error('轮询错误:', error);
                });
            };
            
            poll();
        }

        function displayResult(result) {
            const resultSection = document.getElementById('resultSection');
            const resultDiv = document.getElementById('divinationResult');

            // 格式化显示结果，保持原有的格式
            const content = result.analysis_content
                .replace(/\n/g, '<br>')
                .replace(/═+/g, '<hr style="border: 2px solid #667eea; margin: 15px 0;">')
                .replace(/【([^】]+)】/g, '<h4 style="color: #667eea; margin: 15px 0 10px 0;">【$1】</h4>')
                .replace(/┌[─┬┐]+/g, '<div style="font-family: monospace; font-size: 0.9em; color: #333;">')
                .replace(/└[─┴┘]+/g, '</div>')
                .replace(/├[─┼┤]+/g, '<div style="border-top: 1px solid #ddd; margin: 5px 0;"></div>')
                .replace(/│([^│]+)│/g, '<div style="padding: 3px 0; border-left: 2px solid #667eea; padding-left: 10px;">$1</div>');

            resultDiv.innerHTML = `
                <div class="hexagram-display">
                    <h4>问题：${result.question}</h4>
                    <p style="margin-top: 10px; color: #666;">起卦方式：${result.divination_method}</p>
                    <p style="margin-top: 5px; color: #999; font-size: 0.9em;">
                        创建时间：${new Date(result.created_time).toLocaleString()}
                    </p>
                    ${result.completed_time ? `<p style="margin-top: 5px; color: #999; font-size: 0.9em;">
                        完成时间：${new Date(result.completed_time).toLocaleString()}
                    </p>` : ''}
                    <p style="margin-top: 5px; color: #999; font-size: 0.9em;">
                        分析字数：${result.word_count || 0} 字
                    </p>
                </div>
                <div style="background: white; border-radius: 10px; padding: 20px; line-height: 1.8; font-size: 1.05em;">
                    ${content}
                </div>
            `;

            resultSection.style.display = 'block';
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
    </script>
</body>
</html>
