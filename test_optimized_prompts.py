#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的12角度专项提示词
"""

import asyncio

async def test_optimized_prompts():
    """测试优化后的12角度专项提示词"""
    print("🎯 测试优化后的12角度专项提示词系统")
    print("=" * 70)
    
    try:
        # 1. 获取融合分析数据
        print("1️⃣ 获取测试数据")
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        print("✅ 融合分析数据获取成功")
        
        # 2. 测试所有12个角度的提示词
        print("\n2️⃣ 测试12个角度的专项提示词")
        from core.analysis.prompt_builder import PromptBuilder
        from core.analysis.data_processor import DataProcessor
        
        # 定义12个角度
        analysis_angles = [
            ("命宫分析", "personality_destiny", "性格命运核心特征与天赋潜能"),
            ("财富分析", "wealth_fortune", "财运状况、理财投资与财富积累"),
            ("婚姻分析", "marriage_love", "感情婚姻、桃花运势与配偶关系"),
            ("健康分析", "health_wellness", "健康状况、疾病预防与养生指导"),
            ("事业分析", "career_achievement", "事业发展、成就运势与职业规划"),
            ("子女分析", "children_creativity", "子女运势、创造力与生育指导"),
            ("人际分析", "interpersonal_relationship", "人际关系、贵人运势与社交能力"),
            ("学业分析", "education_learning", "学习教育、智慧发展与知识积累"),
            ("家庭分析", "family_environment", "家庭环境、房产田宅与居住运势"),
            ("迁移分析", "travel_relocation", "迁移变动、外出运势与环境适应"),
            ("精神分析", "spiritual_blessing", "精神世界、福德修养与心灵成长"),
            ("权威分析", "authority_parents", "父母长辈、权威关系与传承运势")
        ]
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        processor = DataProcessor()
        prompt_builder = PromptBuilder()
        
        prompt_stats = {}
        
        for i, (angle_name, analysis_key, description) in enumerate(analysis_angles, 1):
            print(f"\n🔍 测试第{i}/12个角度: {angle_name}")
            
            # 提取分析数据
            analysis_data = processor.extract_analysis_data(result, analysis_key)
            
            # 构建提示词
            prompt = prompt_builder.build_analysis_prompt(
                analysis_data, birth_info, analysis_key
            )
            
            # 分析提示词特征
            prompt_length = len(prompt)
            
            # 检查关键要求
            checks = {
                "角度隔离": f"仅分析{angle_name.replace('分析', '')}" in prompt or "严禁涉及" in prompt,
                "字数要求": "5000字以上" in prompt,
                "紫薇数据": "紫薇斗数" in prompt,
                "八字数据": "八字" in prompt or "四柱" in prompt,
                "相互印证": "印证" in prompt or "融合" in prompt,
                "具体预测": "年份" in prompt and "具体" in prompt,
                "性别差异": "男性重点" in prompt or "女性重点" in prompt or "儿童重点" in prompt
            }
            
            passed_checks = sum(checks.values())
            total_checks = len(checks)
            
            prompt_stats[analysis_key] = {
                "angle_name": angle_name,
                "length": prompt_length,
                "checks_passed": passed_checks,
                "total_checks": total_checks,
                "score": (passed_checks / total_checks) * 100
            }
            
            print(f"  提示词长度: {prompt_length} 字符")
            print(f"  检查通过: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
            
            # 显示检查详情
            for check_name, result in checks.items():
                print(f"    {check_name}: {'✅' if result else '❌'}")
        
        # 3. 统计总体结果
        print(f"\n3️⃣ 12角度提示词优化统计")
        print("=" * 70)
        
        total_length = sum(stats["length"] for stats in prompt_stats.values())
        total_score = sum(stats["score"] for stats in prompt_stats.values()) / len(prompt_stats)
        
        print(f"📊 总体统计:")
        print(f"  总提示词长度: {total_length:,} 字符")
        print(f"  平均提示词长度: {total_length//12:,} 字符")
        print(f"  总体优化评分: {total_score:.1f}/100")
        
        # 显示各角度详情
        print(f"\n📋 各角度详细统计:")
        for analysis_key, stats in prompt_stats.items():
            angle_name = stats["angle_name"]
            length = stats["length"]
            score = stats["score"]
            status = "✅" if score >= 85 else "⚠️" if score >= 70 else "❌"
            
            print(f"  {status} {angle_name}: {length:,}字符, {score:.1f}分")
        
        # 4. 测试一个具体角度的分析
        print(f"\n4️⃣ 测试财富分析的实际执行")
        from core.analysis.analysis_controller import AnalysisController
        
        controller = AnalysisController()
        analysis_result = await controller.execute_single_analysis(
            raw_data=result,
            birth_info=birth_info,
            analysis_type="wealth_fortune"
        )
        
        success = analysis_result.get("success", False)
        print(f"  财富分析执行: {'✅' if success else '❌'}")
        
        if success:
            content = analysis_result.get("content", "")
            content_length = len(content)
            print(f"  生成内容长度: {content_length} 字符")
            
            # 检查内容质量
            content_checks = {
                "包含财富信息": any(keyword in content for keyword in ["财运", "财富", "赚钱", "发财"]),
                "包含具体预测": any(keyword in content for keyword in ["年", "时候", "什么时候"]),
                "包含性别针对性": "男性" in content,
                "包含紫薇分析": any(keyword in content for keyword in ["财帛宫", "天府", "紫薇"]),
                "包含八字分析": any(keyword in content for keyword in ["八字", "四柱", "五行"]),
                "内容充实": content_length >= 3000
            }
            
            content_score = sum(content_checks.values()) / len(content_checks) * 100
            print(f"  内容质量评分: {content_score:.1f}/100")
            
            for check_name, result in content_checks.items():
                print(f"    {check_name}: {'✅' if result else '❌'}")
            
            # 保存测试内容
            with open("test_optimized_wealth_analysis.txt", "w", encoding="utf-8") as f:
                f.write("优化后的财富分析测试结果:\n")
                f.write("=" * 70 + "\n\n")
                f.write(f"分析成功: {success}\n")
                f.write(f"内容长度: {content_length} 字符\n")
                f.write(f"内容评分: {content_score:.1f}/100\n")
                f.write("\n" + "="*70 + "\n")
                f.write("分析内容:\n")
                f.write("="*70 + "\n\n")
                f.write(content)
            
            print(f"  💾 测试内容已保存到 test_optimized_wealth_analysis.txt")
        
        # 5. 总结
        print(f"\n5️⃣ 优化效果总结")
        print("=" * 70)
        
        if total_score >= 90:
            print("🎉 提示词优化效果优秀！")
            print("✅ 12个角度完全隔离")
            print("✅ 专项要求明确详细")
            print("✅ 性别差异化分析")
            print("✅ 紫薇+八字融合分析")
            print("✅ 5000字详细要求")
            return True
        elif total_score >= 75:
            print("⚠️ 提示词优化效果良好，但需要进一步完善")
            return True
        else:
            print("❌ 提示词优化效果不佳，需要重新设计")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_optimized_prompts())
