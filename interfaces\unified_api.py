#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一API接口 - 基于新架构的智能聊天API，支持多端接入
"""

import logging
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import Flask, request, jsonify, Response
from flask_cors import CORS

# 导入新架构组件
from core.chat.session_manager import SessionManager
from core.chat.conversation_engine import ConversationEngine
from core.nlu.intent_recognizer import IntentRecognizer
from core.tools.tool_registry import ToolRegistry
# 只使用融合分析工具
# from core.tools.ziwei_tool import ZiweiTool
# from core.tools.bazi_tool import BaziTool
# from core.tools.liuyao_tool import LiuyaoTool
from config.prompts.prompt_manager import PromptManager
from utils.llm_client import LLMClient, LLMClientFactory

logger = logging.getLogger(__name__)

class UnifiedAPI:
    """统一API接口 - 智能聊天算命系统"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化统一API

        Args:
            config: 配置信息
        """
        self.config = config
        self.app = Flask(__name__)
        CORS(self.app)  # 支持跨域

        # 初始化核心组件
        self._init_components()

        # 注册路由
        self._register_routes()

        logger.info("统一API接口初始化完成")

    def _init_components(self):
        """初始化核心组件"""

        # 1. 创建LLM客户端
        self.llm_client = LLMClientFactory.create_siliconflow_client(
            api_key=self.config.get("api_key", ""),
            model_name=self.config.get("model_name", "deepseek-ai/DeepSeek-V3")
        )

        # 2. 创建提示词管理器
        self.prompt_manager = PromptManager()

        # 3. 创建会话管理器
        self.session_manager = SessionManager(
            max_history=self.config.get("max_history", 20),
            session_timeout=self.config.get("session_timeout", 3600)
        )

        # 4. 创建意图识别器
        self.intent_recognizer = IntentRecognizer(
            self.llm_client, self.prompt_manager
        )

        # 5. 创建工具注册中心
        self.tool_registry = ToolRegistry()

        # 6. 注册算命工具
        self._register_tools()

        # 7. 创建对话引擎
        self.conversation_engine = ConversationEngine(
            self.session_manager,
            self.intent_recognizer,
            self.tool_registry
        )

        logger.info("核心组件初始化完成")

    def _register_tools(self):
        """注册算命工具 - 只使用紫薇+八字融合分析"""

        try:
            # 导入融合分析引擎
            from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
            from core.tools.fusion_tool import FusionTool

            # 创建融合分析引擎
            fusion_engine = ZiweiBaziFusionEngine()

            # 创建融合工具实例
            fusion_tool = FusionTool(fusion_engine, self.llm_client, self.prompt_manager)

            # 注册融合工具
            self.tool_registry.register_tool(fusion_tool, "fortune")

            logger.info("紫薇+八字融合工具注册完成")

        except Exception as e:
            logger.error(f"融合工具注册失败: {e}")
            # 使用模拟工具作为备用
            self._register_mock_tools()

    def _register_mock_tools(self):
        """注册模拟融合工具（用于测试）"""

        class MockFusionEngine:
            def calculate_fusion_analysis(self, year, month, day, hour, gender):
                return {
                    "success": True,
                    "birth_info": {"year": year, "month": month, "day": day, "hour": hour, "gender": gender},
                    "ziwei_analysis": {"palaces": {"命宫": {"major_stars": ["紫微"], "minor_stars": []}}},
                    "bazi_analysis": {"bazi_info": {"chinese_date": "戊辰 丁巳 丁亥 丙午"}},
                    "chart_html": "<div>模拟HTML图表</div>"
                }

        mock_engine = MockFusionEngine()

        from core.tools.fusion_tool import FusionTool
        fusion_tool = FusionTool(mock_engine, self.llm_client, self.prompt_manager)

        self.tool_registry.register_tool(fusion_tool, "fortune")

        logger.warning("使用模拟融合工具进行测试")

    def _register_routes(self):
        """注册API路由"""

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "2.0.0"
            })

        @self.app.route('/v2/chat', methods=['POST'])
        def chat():
            """智能聊天接口"""
            return self._handle_chat_request()

        @self.app.route('/v2/session/<session_id>', methods=['GET'])
        def get_session(session_id):
            """获取会话信息"""
            return self._handle_get_session(session_id)

        @self.app.route('/v2/session/<session_id>', methods=['DELETE'])
        def clear_session(session_id):
            """清除会话"""
            return self._handle_clear_session(session_id)

        @self.app.route('/v2/tools', methods=['GET'])
        def list_tools():
            """列出可用工具"""
            return self._handle_list_tools()

        @self.app.route('/v2/stats', methods=['GET'])
        def get_stats():
            """获取系统统计"""
            return self._handle_get_stats()

        # 兼容旧API
        @self.app.route('/v1/chat/completions', methods=['POST'])
        def legacy_chat():
            """兼容旧版API"""
            return self._handle_legacy_chat()

    def _handle_chat_request(self) -> Response:
        """处理聊天请求"""

        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "无效的请求数据"}), 400

            # 提取参数
            message = data.get("message", "")
            session_id = data.get("session_id") or str(uuid.uuid4())
            user_id = data.get("user_id")

            if not message.strip():
                return jsonify({"error": "消息不能为空"}), 400

            # 处理消息
            result = self.conversation_engine.process_message(
                session_id=session_id,
                message=message,
                user_id=user_id
            )

            # 格式化响应
            response = {
                "success": result.get("success", False),
                "session_id": session_id,
                "message": result.get("message", ""),
                "timestamp": datetime.now().isoformat()
            }

            # 添加额外信息
            if result.get("intent"):
                response["intent"] = result["intent"]

            if result.get("tool_result"):
                tool_result = result["tool_result"]
                # HTML图表优先
                if tool_result.get("chart_html"):
                    response["chart_html"] = tool_result["chart_html"]
                # 兼容传统图片
                elif tool_result.get("chart_image"):
                    response["chart_image"] = tool_result["chart_image"]
                if tool_result.get("tool_name"):
                    response["tool_used"] = tool_result["tool_name"]

            return jsonify(response)

        except Exception as e:
            logger.error(f"聊天请求处理失败: {e}")
            return jsonify({
                "success": False,
                "error": str(e),
                "message": "处理请求时出现错误，请稍后重试"
            }), 500

    def _handle_get_session(self, session_id: str) -> Response:
        """获取会话信息"""

        try:
            session = self.session_manager.get_session(session_id)
            context = self.session_manager.get_conversation_context(session_id)

            return jsonify({
                "session_id": session_id,
                "created_at": session["created_at"].isoformat(),
                "last_active": session["last_active"].isoformat(),
                "message_count": len(session["history"]),
                "context": context
            })

        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            return jsonify({"error": str(e)}), 500

    def _handle_clear_session(self, session_id: str) -> Response:
        """清除会话"""

        try:
            success = self.session_manager.clear_session(session_id)

            return jsonify({
                "success": success,
                "message": "会话已清除" if success else "会话不存在"
            })

        except Exception as e:
            logger.error(f"清除会话失败: {e}")
            return jsonify({"error": str(e)}), 500

    def _handle_list_tools(self) -> Response:
        """列出可用工具"""

        try:
            tools_info = self.tool_registry.get_all_tools_info()

            return jsonify({
                "tools": tools_info,
                "total_count": len(tools_info)
            })

        except Exception as e:
            logger.error(f"获取工具列表失败: {e}")
            return jsonify({"error": str(e)}), 500

    def _handle_get_stats(self) -> Response:
        """获取系统统计"""

        try:
            session_stats = self.session_manager.get_session_stats()
            tool_stats = self.tool_registry.get_global_stats()
            conversation_stats = self.conversation_engine.get_stats()
            llm_stats = self.llm_client.get_stats()

            return jsonify({
                "session_stats": session_stats,
                "tool_stats": tool_stats,
                "conversation_stats": conversation_stats,
                "llm_stats": llm_stats,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return jsonify({"error": str(e)}), 500

    def _handle_legacy_chat(self) -> Response:
        """处理旧版API兼容"""

        try:
            data = request.get_json()
            messages = data.get("messages", [])

            if not messages:
                return jsonify({"error": "无效的消息格式"}), 400

            # 提取最后一条用户消息
            user_message = ""
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break

            if not user_message:
                return jsonify({"error": "未找到用户消息"}), 400

            # 生成会话ID
            session_id = str(uuid.uuid4())

            # 处理消息
            result = self.conversation_engine.process_message(session_id, user_message)

            # 格式化为旧版API响应
            response = {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": result.get("message", "")
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "total_tokens": len(user_message) + len(result.get("message", ""))
                }
            }

            return jsonify(response)

        except Exception as e:
            logger.error(f"旧版API处理失败: {e}")
            return jsonify({"error": str(e)}), 500

    def run(self, host: str = "0.0.0.0", port: int = 8002, debug: bool = False):
        """启动API服务器"""

        logger.info(f"启动统一API服务器 - {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)

def create_app(config: Dict[str, Any]) -> Flask:
    """创建Flask应用"""

    api = UnifiedAPI(config)
    return api.app

if __name__ == "__main__":
    # 默认配置
    config = {
        "api_key": "sk-your-api-key",
        "model_name": "deepseek-ai/DeepSeek-V3",
        "max_history": 20,
        "session_timeout": 3600
    }

    # 创建并启动API
    api = UnifiedAPI(config)
    api.run(port=8002, debug=True)
