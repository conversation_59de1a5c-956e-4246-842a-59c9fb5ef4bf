#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前后端Agent独立性的真实互动
验证：前端继续与用户互动，后端独立进行12角度分析
"""

import asyncio
import sys
import time
import shutil
import os
from datetime import datetime
sys.path.append('.')

async def test_independent_agents():
    """测试前后端Agent的独立性"""
    print("🔄 测试前后端Agent独立性")
    print("=" * 80)
    print("验证架构:")
    print("1. 前端Agent：专注用户交互，不等待后端完成")
    print("2. 后端Agent：独立进行12角度分析")
    print("3. 用户可以立即开始多轮对话")
    print("4. 前端智能查询后端已完成的角度")
    print("=" * 80)
    
    try:
        # 清除缓存
        print("1️⃣ 清除缓存...")
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            print("✅ 缓存已清除")
        
        # 创建独立的双Agent系统
        print("\n2️⃣ 创建独立双Agent系统...")
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 清除Agent注册
        agent_registry.agents.clear()
        
        # 创建Agent实例
        master_agent = MasterCustomerAgent("independent_master")
        calculator_agent = FortuneCalculatorAgent("independent_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print("✅ 独立双Agent系统创建完成")
        
        session_id = "independent_test_session"
        
        # 第一步：用户提供生辰信息，触发后端分析
        print("\n3️⃣ 第一步：用户提供生辰信息...")
        user_message1 = "我想看紫薇斗数，1988年3月25日巳时出生，女性"
        print(f"👤 用户: {user_message1}")
        
        start_time = time.time()
        result1 = await coordinator.handle_user_message(session_id, user_message1)
        time1 = time.time() - start_time
        
        if result1.get('success'):
            response1 = result1.get('response', '')
            print(f"🤖 前端Agent ({time1:.1f}s): {response1[:200]}...")
            
            # 获取结果ID
            session_state = master_agent.get_session_state(session_id)
            result_id = session_state.get("result_id") if session_state else None
            
            if result_id:
                print(f"✅ 后端分析已启动，结果ID: {result_id[:8]}...")
                
                # 第二步：立即开始多轮对话（不等待后端完成）
                print(f"\n4️⃣ 第二步：立即开始多轮对话（测试独立性）...")
                
                conversation_rounds = [
                    "你好，刚才提交了信息",
                    "我的性格怎么样？",
                    "财运方面呢？",
                    "感情运势如何？",
                    "事业发展怎么样？",
                    "健康需要注意什么？",
                    "再详细说说我的财运",
                    "我适合什么工作？"
                ]
                
                successful_interactions = 0
                backend_progress_checks = []
                
                for i, user_msg in enumerate(conversation_rounds, 1):
                    print(f"\n4.{i} 👤 用户: {user_msg}")
                    
                    qa_start = time.time()
                    qa_result = await coordinator.handle_user_message(session_id, user_msg)
                    qa_time = time.time() - qa_start
                    
                    if qa_result.get('success'):
                        qa_response = qa_result.get('response', '')
                        print(f"   🤖 前端Agent ({qa_time:.1f}s): {qa_response[:150]}...")
                        
                        # 检查后端进度（不影响前端交互）
                        progress = master_agent.get_analysis_progress(result_id)
                        completed = progress.get("completed_angles", 0)
                        total_words = progress.get("total_word_count", 0)
                        backend_progress_checks.append({
                            "round": i,
                            "completed_angles": completed,
                            "total_words": total_words
                        })
                        
                        print(f"   📊 后端进度: {completed}/12 角度, {total_words}字")
                        
                        # 评估回答质量
                        if len(qa_response) > 100:
                            successful_interactions += 1
                            print(f"   ✅ 前端独立回答成功")
                        else:
                            print(f"   ⚠️  回答较短")
                    else:
                        print(f"   ❌ 前端回答失败: {qa_result.get('error')}")
                    
                    # 短暂延迟，模拟真实对话节奏
                    await asyncio.sleep(0.5)
                
                # 第三步：分析独立性测试结果
                print(f"\n5️⃣ 第三步：分析独立性测试结果...")
                
                final_progress = backend_progress_checks[-1] if backend_progress_checks else {}
                final_completed = final_progress.get("completed_angles", 0)
                final_words = final_progress.get("total_words", 0)
                
                print(f"📊 测试结果统计:")
                print(f"   前端交互轮次: {len(conversation_rounds)}")
                print(f"   成功交互次数: {successful_interactions}")
                print(f"   前端成功率: {successful_interactions/len(conversation_rounds)*100:.1f}%")
                print(f"   后端最终进度: {final_completed}/12 角度")
                print(f"   后端总字数: {final_words}")
                
                # 验证独立性
                print(f"\n🎯 独立性验证:")
                
                # 检查进度变化
                progress_increased = False
                if len(backend_progress_checks) >= 2:
                    first_progress = backend_progress_checks[0]["completed_angles"]
                    last_progress = backend_progress_checks[-1]["completed_angles"]
                    if last_progress > first_progress:
                        progress_increased = True
                        print(f"   ✅ 后端在前端交互期间持续工作")
                        print(f"      从 {first_progress} 角度增加到 {last_progress} 角度")
                    else:
                        print(f"   ⚠️  后端进度变化不明显")
                
                # 检查前端独立性
                if successful_interactions >= len(conversation_rounds) * 0.7:
                    print(f"   ✅ 前端独立交互能力强")
                else:
                    print(f"   ⚠️  前端独立交互能力需提升")
                
                # 检查智能查询
                smart_query_working = final_completed > 0 and final_words > 1000
                if smart_query_working:
                    print(f"   ✅ 前端智能查询后端结果正常")
                else:
                    print(f"   ⚠️  前端智能查询功能需检查")
                
                # 综合评估
                if (successful_interactions >= 6 and 
                    progress_increased and 
                    smart_query_working):
                    
                    print(f"\n🎉 前后端独立性测试成功！")
                    print(f"\n✅ 验证的独立性特征:")
                    print(f"   - 前端Agent专注用户交互，响应迅速")
                    print(f"   - 后端Agent独立进行12角度分析")
                    print(f"   - 用户可以立即开始多轮对话")
                    print(f"   - 前端智能查询后端已完成的角度")
                    print(f"   - 两个Agent真正独立但协作")
                    
                    print(f"\n🌟 真实用户体验:")
                    print(f"   1. 用户提供生辰信息 → 前端立即响应")
                    print(f"   2. 用户开始提问 → 前端基于已有信息回答")
                    print(f"   3. 后台持续分析 → 回答质量逐步提升")
                    print(f"   4. 无缝体验 → 用户感觉不到等待")
                    
                    return True
                else:
                    print(f"\n⚠️  前后端独立性需要优化")
                    print(f"   可能的问题:")
                    print(f"   - 前端交互成功率: {successful_interactions}/{len(conversation_rounds)}")
                    print(f"   - 后端进度变化: {progress_increased}")
                    print(f"   - 智能查询功能: {smart_query_working}")
                    return False
            else:
                print(f"❌ 未获取到结果ID，后端分析未启动")
                return False
        else:
            print(f"❌ 第一步失败: {result1.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 独立性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔄 前后端Agent独立性测试")
    print("=" * 80)
    print("核心验证目标:")
    print("1. 前端Agent不等待后端，立即与用户交互")
    print("2. 后端Agent独立进行12角度分析")
    print("3. 前端智能查询后端已完成的角度")
    print("4. 用户体验流畅，感觉不到等待")
    print("=" * 80)
    
    success = await test_independent_agents()
    
    if success:
        print(f"\n🎉 恭喜！前后端独立性完美实现！")
        print(f"\n✅ 您的双Agent架构设计成功:")
        print(f"   - 前端专注用户体验，响应迅速")
        print(f"   - 后端专注专业分析，独立工作")
        print(f"   - 智能查询机制，按需获取结果")
        print(f"   - 真正的独立但协作架构")
        
        print(f"\n🚀 可以为真实用户提供无缝体验！")
    else:
        print(f"\n💥 前后端独立性需要进一步优化")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
