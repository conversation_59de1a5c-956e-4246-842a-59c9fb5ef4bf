#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试六爻卦象显示
"""

from coin_divination import CoinDivination

def test_hexagram_display():
    """测试卦象显示功能"""
    divination = CoinDivination()
    
    # 模拟一个有动爻的投掷结果
    test_throws = [
        {"throw_number": 1, "throw_result": "背背正", "yao_type": "老阳", "yao_symbol": "▅▅▅▅▅", "changed_symbol": "▅▅ ▅▅", "is_moving": True},
        {"throw_number": 2, "throw_result": "正正正", "yao_type": "少阴", "yao_symbol": "▅▅ ▅▅", "changed_symbol": "▅▅ ▅▅", "is_moving": False},
        {"throw_number": 3, "throw_result": "正正背", "yao_type": "少阳", "yao_symbol": "▅▅▅▅▅", "changed_symbol": "▅▅▅▅▅", "is_moving": False},
        {"throw_number": 4, "throw_result": "背背背", "yao_type": "老阴", "yao_symbol": "▅▅ ▅▅", "changed_symbol": "▅▅▅▅▅", "is_moving": True},
        {"throw_number": 5, "throw_result": "正背正", "yao_type": "少阳", "yao_symbol": "▅▅▅▅▅", "changed_symbol": "▅▅▅▅▅", "is_moving": False},
        {"throw_number": 6, "throw_result": "正正背", "yao_type": "少阳", "yao_symbol": "▅▅▅▅▅", "changed_symbol": "▅▅▅▅▅", "is_moving": False}
    ]
    
    # 生成卦象数据
    hexagram_data = divination.build_hexagram(test_throws)

    # 格式化显示
    formatted_result = divination.format_hexagram_display(hexagram_data)
    
    print("=== 测试卦象显示 ===")
    print(formatted_result)
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_hexagram_display()
