#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紫薇工具迁移到人性化交互系统
"""

import sys
import os
sys.path.append('.')

def test_humanized_ziwei_tool():
    """测试人性化紫薇工具"""
    print("测试人性化紫薇工具")
    print("-" * 50)
    
    try:
        from core.tools.humanized_ziwei_tool import HumanizedZiweiTool
        
        # 创建工具实例
        tool = HumanizedZiweiTool()
        
        # 测试数据
        intent = {
            "intent": "ziwei",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        context = {}
        
        # 执行测试
        result = tool.execute(intent, context)
        
        if result.get("success"):
            print("✅ 人性化紫薇工具执行成功")
            print(f"   类型: {result.get('type')}")
            print(f"   消息: {result.get('message')}")
            
            # 检查计算结果
            calc_result = result.get("calculation_result", {})
            if calc_result.get("success"):
                print("✅ 紫薇计算成功")
                raw_result = calc_result.get("raw_result", {})
                if "palaces" in raw_result:
                    palaces = raw_result["palaces"]
                    print(f"   宫位数量: {len(palaces)}")
                    
                    # 检查关键宫位
                    key_palaces = ["命宫", "财帛宫", "官禄宫", "夫妻宫"]
                    found_palaces = [p for p in key_palaces if p in palaces]
                    print(f"   关键宫位: {len(found_palaces)}/{len(key_palaces)} ({found_palaces})")
                    
                if "birth_info" in raw_result:
                    birth_info = raw_result["birth_info"]
                    print(f"   出生信息: {birth_info.get('solar', '未知')}")
            else:
                print(f"❌ 紫薇计算失败: {calc_result.get('error')}")
        else:
            print(f"❌ 人性化紫薇工具执行失败: {result.get('error')}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selector_ziwei():
    """测试工具选择器的紫薇处理"""
    print("\n测试工具选择器的紫薇处理")
    print("-" * 50)
    
    try:
        from core.tools.tool_selector import ToolSelector
        
        selector = ToolSelector()
        
        # 测试意图
        intent_result = {
            "intent": "ziwei",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1", 
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        context = {}
        
        # 选择工具
        tool_result = selector.select_tool(intent_result, context)
        
        if tool_result.get("success"):
            print("✅ 工具选择成功")
            print(f"   工具名称: {tool_result.get('tool_name')}")
            print(f"   工具描述: {tool_result.get('tool_description')}")
            
            result = tool_result.get("result", {})
            if result.get("success"):
                print("✅ 紫薇分析执行成功")
                print(f"   分析类型: {result.get('type')}")
                print(f"   消息: {result.get('message')}")
            else:
                print(f"❌ 紫薇分析执行失败: {result.get('error')}")
        else:
            print(f"❌ 工具选择失败: {tool_result.get('error')}")
        
        return tool_result.get("success", False)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_humanized_engine_ziwei():
    """测试人性化引擎的紫薇分析"""
    print("\n测试人性化引擎的紫薇分析")
    print("-" * 50)
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        session_id = "test_ziwei_migration"
        
        # 测试紫薇斗数请求（使用明确的紫薇关键词）
        message = "我想看紫薇斗数命盘，1988年6月1日午时男"
        
        print(f"用户消息: {message}")
        
        responses = engine.process_user_message(message, session_id)
        
        print(f"总响应数: {len(responses)}")
        
        # 分析响应类型
        response_types = {}
        ziwei_analysis_count = 0
        
        for response in responses:
            response_type = response.get("type", "unknown")
            response_types[response_type] = response_types.get(response_type, 0) + 1
            
            if response_type == "detailed_analysis":
                ziwei_analysis_count += 1
                content = response.get("content", "")
                print(f"\n紫薇分析 {ziwei_analysis_count}:")
                print(f"  长度: {len(content)} 字符")
                print(f"  预览: {content[:100]}...")
                
                # 检查是否包含紫薇特征
                ziwei_features = ["命盘", "紫薇", "宫位", "星曜", "主星"]
                found_features = [f for f in ziwei_features if f in content]
                print(f"  紫薇特征: {len(found_features)}/{len(ziwei_features)} ({found_features})")
        
        print(f"\n响应类型分布:")
        for response_type, count in response_types.items():
            print(f"  {response_type}: {count} 个")
        
        # 检查关键响应类型
        required_types = ["chart_presentation", "analysis_intro", "detailed_analysis", "synthesis"]
        missing_types = [t for t in required_types if t not in response_types]
        
        if missing_types:
            print(f"\n⚠️ 缺少关键响应类型: {missing_types}")
        else:
            print(f"\n✅ 所有关键响应类型都存在")
        
        success = (
            len(responses) >= 10 and  # 至少10个响应
            ziwei_analysis_count >= 4 and  # 至少4个详细分析
            len(missing_types) == 0  # 没有缺少的类型
        )
        
        print(f"\n人性化紫薇分析: {'✅ 成功' if success else '⚠️ 需要改进'}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ziwei_fewshot_integration():
    """测试紫薇Few-shot Learning集成"""
    print("\n测试紫薇Few-shot Learning集成")
    print("-" * 50)
    
    try:
        from core.nlu.llm_client import LLMClient
        
        client = LLMClient()
        
        # 测试紫薇相关的Few-shot调用
        test_cases = [
            ("请帮我分析紫薇斗数命盘", "general"),
            ("紫薇斗数看我的事业运势如何？", "career"),
            ("用紫薇斗数分析我的财运", "wealth"),
            ("紫薇斗数看感情运势", "love")
        ]
        
        success_count = 0
        
        for message, category in test_cases:
            print(f"\n测试: {message}")
            
            response = client.fewshot_chat(
                user_message=message,
                category=category,
                temperature=0.7,
                max_tokens=500
            )
            
            if response and len(response) > 100:
                print(f"✅ 响应成功: {len(response)} 字符")
                
                # 检查紫薇专业术语
                ziwei_terms = ["根据", "紫薇", "命盘", "星曜", "建议", "通过", "将会"]
                found_terms = [term for term in ziwei_terms if term in response]
                
                print(f"   专业术语: {len(found_terms)}/{len(ziwei_terms)}")
                print(f"   内容预览: {response[:80]}...")
                
                if len(found_terms) >= 5:
                    success_count += 1
                    print(f"   质量: ✅ 优秀")
                else:
                    print(f"   质量: ⚠️ 一般")
            else:
                print(f"❌ 响应失败或过短")
        
        print(f"\nFew-shot紫薇集成: {success_count}/{len(test_cases)} 优秀")
        
        return success_count >= len(test_cases) * 0.75
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ziwei_algorithm_availability():
    """测试紫薇算法可用性"""
    print("\n测试紫薇算法可用性")
    print("-" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator, IZTRO_AVAILABLE
        
        print(f"py-iztro可用性: {IZTRO_AVAILABLE}")
        
        if IZTRO_AVAILABLE:
            print("✅ py-iztro模块可用")
            
            # 测试计算器
            calc = RealZiweiCalculator()
            result = calc.calculate_chart(1988, 6, 1, 12, "男")
            
            if "error" in result:
                print(f"❌ 紫薇计算失败: {result['error']}")
                return False
            else:
                print("✅ 紫薇计算成功")
                print(f"   宫位数量: {len(result.get('palaces', {}))}")
                print(f"   出生信息: {result.get('birth_info', {}).get('solar', '未知')}")
                return True
        else:
            print("❌ py-iztro模块不可用")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("阶段3.5：紫薇工具迁移测试")
    print("=" * 80)
    print("目标: 将紫薇斗数功能迁移到人性化交互系统")
    print("=" * 80)
    
    # 执行测试
    test_results = []
    
    # 1. 紫薇算法可用性测试
    test_results.append(("紫薇算法可用性", test_ziwei_algorithm_availability()))
    
    # 2. 人性化紫薇工具测试
    test_results.append(("人性化紫薇工具", test_humanized_ziwei_tool()))
    
    # 3. 工具选择器紫薇处理测试
    test_results.append(("工具选择器紫薇处理", test_tool_selector_ziwei()))
    
    # 4. 人性化引擎紫薇分析测试
    test_results.append(("人性化引擎紫薇分析", test_humanized_engine_ziwei()))
    
    # 5. 紫薇Few-shot集成测试
    test_results.append(("紫薇Few-shot集成", test_ziwei_fewshot_integration()))
    
    # 汇总结果
    print(f"\n阶段3.5紫薇工具迁移测试结果")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 阶段3.5紫薇工具迁移成功！")
        print("\n🎯 实现的核心功能:")
        print("  ✅ 人性化紫薇工具 - 统一接口设计")
        print("  ✅ 工具选择器集成 - 智能路由到紫薇分析")
        print("  ✅ 人性化交互体验 - 15段式紫薇分析")
        print("  ✅ Few-shot Learning - 专业紫薇话术")
        print("  ✅ 真实算法支持 - py-iztro算法库")
        print("\n🌟 紫薇斗数特色:")
        print("  🔮 真实紫薇算法 - 十二宫星曜排盘")
        print("  💬 专业话术融入 - 基于微调模型训练数据")
        print("  🔄 分段式交互 - 命盘展示、详细分析")
        print("  🎯 智能分析 - 结合真实命盘数据的专业解读")
        print("  📊 完整宫位分析 - 命宫、财帛宫、官禄宫、夫妻宫")
        print("\n📋 PRD阶段3.5目标达成！")
    else:
        print("💥 部分功能存在问题，需要修复后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
