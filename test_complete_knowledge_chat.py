#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整知识库的聊天效果
"""

import sys
sys.path.append('.')

from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
from core.chat.knowledge_base import ChatKnowledgeBase
from core.nlu.llm_client import LLMClient

def test_complete_knowledge_chat():
    """测试完整知识库的聊天效果"""
    print("🎯 测试完整知识库聊天效果")
    print("=" * 60)
    
    try:
        # 获取缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        cache_file = cache_files[0]
        result_id = cache_file.stem
        cached_result = cache.get_result(result_id)
        
        # 创建完整知识库
        knowledge_base = ChatKnowledgeBase()
        knowledge_base.load_from_cache_result(cached_result)
        
        # 显示知识库统计
        stats = knowledge_base.get_stats()
        print(f"📊 知识库统计: 总计{stats['total_items']}项")
        
        # 测试复杂问题
        complex_questions = [
            "我是1988年6月1日午时出生的男性，请详细分析我的整体命运如何？",
            "根据我的八字和紫薇斗数，我的财运和事业发展如何？什么时候是最佳时机？",
            "我现在37岁了，作为丁火日主的龙年生人，今年的运势如何？需要注意什么？",
            "我的命宫天相星，财帛宫天府星，这样的配置对我的人生有什么影响？",
            "从五行分析来看，我土偏旺、木偏弱，这对我的性格和运势有什么影响？"
        ]
        
        success_count = 0
        total_questions = len(complex_questions)
        
        for i, question in enumerate(complex_questions, 1):
            print(f"\n📝 复杂问题 {i}/{total_questions}:")
            print(f"❓ {question}")
            print("-" * 50)
            
            # 生成回复
            response = generate_enhanced_response(cached_result, question, knowledge_base)
            
            if response:
                print(f"🤖 回复长度: {len(response)} 字符")
                
                # 检查知识使用情况
                knowledge_usage = check_knowledge_usage(response, knowledge_base)
                print(f"📊 知识使用: {knowledge_usage['used_count']}/{knowledge_usage['total_count']} 项")
                print(f"📊 使用类别: {', '.join(knowledge_usage['used_categories'])}")
                
                # 质量评估
                quality_score = evaluate_response_quality(response, question, knowledge_base)
                print(f"🎯 质量评分: {quality_score}/10")
                
                # 显示回复预览
                print(f"📄 回复预览:")
                print(f"   {response[:150]}...")
                
                if quality_score >= 7:
                    success_count += 1
                    print("✅ 回复质量优秀")
                elif quality_score >= 5:
                    print("⚠️  回复质量良好")
                else:
                    print("❌ 回复质量需要改进")
            else:
                print("❌ 回复生成失败")
                
        # 总体评估
        success_rate = success_count / total_questions
        print(f"\n🎯 完整知识库聊天测试结果:")
        print(f"  优秀回复: {success_count}/{total_questions}")
        print(f"  成功率: {success_rate*100:.1f}%")
        print(f"  知识库项目: {stats['total_items']}项")
        print(f"  覆盖类别: {len([k for k, v in stats.items() if k.endswith('_count') and v > 0])}个")
        
        if success_rate >= 0.8:
            print("🎉 完整知识库聊天效果优秀！")
            return True
        elif success_rate >= 0.6:
            print("✅ 完整知识库聊天效果良好")
            return True
        else:
            print("❌ 需要进一步优化")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_enhanced_response(cached_result, question, knowledge_base):
    """生成增强的回复"""
    try:
        # 格式化知识库
        knowledge_text = knowledge_base.format_for_llm(max_length=2000)  # 增加长度限制
        
        birth_info = cached_result.birth_info
        
        # 构建增强的系统提示词
        system_prompt = f"""
你是一位资深的命理分析师，现在要基于用户的完整专属知识库回答问题。

【用户基本信息】
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}
- 性别：{birth_info.get('gender')}
- 当前年龄：37岁

{knowledge_text}

【回答要求】
1. 充分利用知识库中的所有相关信息
2. 结合紫薇斗数和八字命理提供专业分析
3. 引用具体的星曜配置和五行分析
4. 语言专业但通俗易懂
5. 回答要详细完整，控制在800-1200字之间
6. 结构清晰：现状分析-深层解读-实用建议-总结展望
7. 必须基于知识库数据，不能编造信息

请根据用户的完整专属知识库，提供专业、详细、实用的命理分析。
"""
        
        # 调用LLM
        llm_client = LLMClient()
        response = llm_client.chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question}
            ],
            temperature=0.7,
            max_tokens=4096
        )
        
        return response
        
    except Exception as e:
        print(f"❌ 回复生成失败: {e}")
        return None

def check_knowledge_usage(response, knowledge_base):
    """检查知识使用情况"""
    used_count = 0
    used_categories = set()
    total_count = len(knowledge_base.knowledge_items)
    
    for item in knowledge_base.knowledge_items:
        if item.value and len(item.value) > 2:
            if item.value in response:
                used_count += 1
                used_categories.add(knowledge_base.categories.get(item.category, item.category))
    
    return {
        'used_count': used_count,
        'total_count': total_count,
        'used_categories': list(used_categories),
        'usage_rate': used_count / total_count if total_count > 0 else 0
    }

def evaluate_response_quality(response, question, knowledge_base):
    """评估回复质量"""
    score = 0
    
    # 1. 长度检查 (2分)
    if len(response) >= 800:
        score += 2
    elif len(response) >= 500:
        score += 1
        
    # 2. 知识使用检查 (2分)
    knowledge_usage = check_knowledge_usage(response, knowledge_base)
    if knowledge_usage['usage_rate'] >= 0.1:
        score += 2
    elif knowledge_usage['usage_rate'] >= 0.05:
        score += 1
        
    # 3. 专业术语检查 (2分)
    professional_terms = ['天相', '天府', '紫微', '丁火', '日主', '命宫', '财帛宫', '五行', '八字']
    used_terms = sum(1 for term in professional_terms if term in response)
    if used_terms >= 5:
        score += 2
    elif used_terms >= 3:
        score += 1
        
    # 4. 结构完整性检查 (2分)
    structure_indicators = ['分析', '建议', '总结', '综合', '整体']
    structure_score = sum(1 for indicator in structure_indicators if indicator in response)
    if structure_score >= 3:
        score += 2
    elif structure_score >= 2:
        score += 1
        
    # 5. 相关性检查 (2分)
    question_keywords = question.split()
    relevance_score = sum(1 for keyword in question_keywords if keyword in response and len(keyword) > 1)
    if relevance_score >= len(question_keywords) * 0.5:
        score += 2
    elif relevance_score >= len(question_keywords) * 0.3:
        score += 1
    
    return min(score, 10)  # 最高10分

if __name__ == "__main__":
    print("🎯 完整知识库聊天效果测试")
    print("=" * 70)
    
    success = test_complete_knowledge_chat()
    
    print(f"\n🎯 最终结论:")
    if success:
        print("🎉 完整知识库聊天功能已达到优秀水平！")
        print("💡 知识库数据全面完整，聊天效果显著提升")
        print("🚀 可以正式部署使用")
    else:
        print("⚠️  聊天功能基本可用，但仍有优化空间")
        print("💡 建议继续完善知识库数据和提示词")
