#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器
负责从排盘数据中提取和整理分析所需的信息
"""

import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器"""

    def __init__(self):
        """初始化数据处理器"""
        pass

    def extract_analysis_data(self, raw_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """提取分析数据"""
        try:
            logger.info(f"🔍 开始提取{analysis_type}分析数据")

            # 检查是否为合盘分析
            if raw_data.get("analysis_type") == "compatibility_analysis":
                return self._extract_compatibility_data(raw_data, analysis_type)

            # 基础数据结构
            analysis_data = {
                "analysis_type": analysis_type,
                "data_source": "fusion_analysis",
                "confidence": {
                    "ziwei": raw_data.get("ziwei_confidence", 0.0),
                    "bazi": raw_data.get("bazi_confidence", 0.0)
                }
            }

            # 提取紫薇斗数数据
            ziwei_data = self._extract_ziwei_data(raw_data)
            if ziwei_data:
                analysis_data["ziwei"] = ziwei_data
                # 同时保存原始数据以备用
                ziwei_raw = raw_data.get("ziwei", {})
                if isinstance(ziwei_raw, dict):
                    analysis_data["raw_ziwei_data"] = ziwei_raw
                else:
                    analysis_data["raw_ziwei_data"] = {}

            # 提取八字数据
            bazi_data = self._extract_bazi_data(raw_data)
            if bazi_data:
                analysis_data["bazi"] = bazi_data

            # 根据分析类型提取特定数据
            specific_data = self._extract_specific_data(raw_data, analysis_type)
            if specific_data:
                analysis_data["specific"] = specific_data

            logger.info(f"✅ {analysis_type}数据提取完成")
            return analysis_data

        except Exception as e:
            logger.error(f"提取{analysis_type}数据失败: {e}")
            return {}

    def _extract_ziwei_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取紫薇斗数数据"""
        try:
            # 尝试多种数据路径
            ziwei_analysis = raw_data.get("ziwei_analysis", {})
            if not ziwei_analysis:
                # 尝试从raw_calculation中获取
                raw_calculation = raw_data.get("raw_calculation", {})
                ziwei_analysis = raw_calculation.get("ziwei_analysis", {})

            if not ziwei_analysis:
                # 尝试从data字段获取
                data = raw_data.get("data", {})
                ziwei_analysis = data.get("ziwei_analysis", {})

            if not ziwei_analysis:
                # 尝试直接从ziwei字段获取
                ziwei_analysis = raw_data.get("ziwei", {})

            # 🔧 新增：处理process_message保存的原始紫薇数据格式
            if not ziwei_analysis:
                # 检查是否是原始紫薇数据格式（直接包含palaces等字段）
                if "palaces" in raw_data and isinstance(raw_data["palaces"], dict):
                    logger.info("检测到原始紫薇数据格式，进行格式转换")
                    ziwei_analysis = {
                        "birth_info": raw_data.get("birth_info", {}),
                        "palaces": raw_data.get("palaces", {}),
                        "zodiac": raw_data.get("zodiac", ""),
                        "sign": raw_data.get("sign", "")
                    }

            if not ziwei_analysis:
                logger.warning("未找到紫薇斗数数据")
                logger.debug(f"原始数据键: {list(raw_data.keys())}")
                return {}

            ziwei_data = {}

            # 提取宫位信息
            palaces = ziwei_analysis.get("palaces", {})
            if palaces:
                ziwei_data["palaces"] = {}

                # 重要宫位
                important_palaces = [
                    "命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                    "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"
                ]

                for palace_name in important_palaces:
                    if palace_name in palaces:
                        palace_info = palaces[palace_name]
                        # 兼容多种字段名格式
                        position = (palace_info.get("position") or
                                  palace_info.get("位置") or
                                  palace_info.get("palace_position", ""))
                        major_stars = (palace_info.get("major_stars") or
                                     palace_info.get("主星") or
                                     palace_info.get("stars", []))
                        minor_stars = (palace_info.get("minor_stars") or
                                     palace_info.get("辅星") or
                                     palace_info.get("auxiliary_stars", []))
                        description = (palace_info.get("description") or
                                     palace_info.get("描述") or "")

                        ziwei_data["palaces"][palace_name] = {
                            "位置": position,
                            "主星": major_stars,
                            "辅星": minor_stars,
                            "描述": description
                        }

            # 提取星曜信息
            stars_info = ziwei_analysis.get("stars_info", {})
            if stars_info:
                ziwei_data["stars"] = stars_info

            return ziwei_data

        except Exception as e:
            logger.error(f"提取紫薇斗数数据失败: {e}")
            return {}

    def _extract_bazi_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取八字数据"""
        try:
            # 尝试多种数据路径
            bazi_analysis = raw_data.get("bazi_analysis", {})
            if not bazi_analysis:
                # 尝试从raw_calculation中获取
                raw_calculation = raw_data.get("raw_calculation", {})
                bazi_analysis = raw_calculation.get("bazi_analysis", {})

            if not bazi_analysis:
                # 尝试从data字段获取
                data = raw_data.get("data", {})
                bazi_analysis = data.get("bazi_analysis", {})

            if not bazi_analysis:
                # 尝试直接从bazi字段获取
                bazi_analysis = raw_data.get("bazi", {})

            # 🔧 新增：对于原始紫薇数据格式，八字数据可能不存在
            # 这种情况下返回空字典，但不报错
            if not bazi_analysis:
                logger.info("未找到八字数据，可能是纯紫薇数据格式")
                return {}

            bazi_data = {}

            # 提取四柱信息
            pillars = ["年柱", "月柱", "日柱", "时柱"]
            for pillar in pillars:
                if pillar in bazi_analysis:
                    bazi_data[pillar] = bazi_analysis[pillar]

            # 如果没有直接的四柱信息，尝试从bazi_info中获取
            if not any(pillar in bazi_data for pillar in pillars):
                bazi_info = bazi_analysis.get("bazi_info", {})
                if bazi_info:
                    bazi_data["年柱"] = bazi_info.get("year_pillar", "")
                    bazi_data["月柱"] = bazi_info.get("month_pillar", "")
                    bazi_data["日柱"] = bazi_info.get("day_pillar", "")
                    bazi_data["时柱"] = bazi_info.get("hour_pillar", "")

            # 提取五行分析
            wuxing = bazi_analysis.get("五行分析", {})
            if not wuxing:
                # 尝试从analysis.wuxing中获取
                analysis = bazi_analysis.get("analysis", {})
                wuxing = analysis.get("wuxing", {})

            if wuxing:
                # 转换数据格式以便提示词使用
                wuxing_formatted = {}
                if "count" in wuxing:
                    for element, count in wuxing["count"].items():
                        wuxing_formatted[element] = {"数量": count}
                else:
                    wuxing_formatted = wuxing
                bazi_data["五行"] = wuxing_formatted

            # 提取十神分析
            shishen = bazi_analysis.get("十神分析", {})
            if not shishen:
                # 尝试从analysis.shishen中获取
                analysis = bazi_analysis.get("analysis", {})
                shishen = analysis.get("shishen", {})

            if shishen:
                # 转换数据格式
                shishen_formatted = {}
                for key, value in shishen.items():
                    shishen_formatted[key] = {"数量": 1, "描述": value}
                bazi_data["十神"] = shishen_formatted

            # 提取用神信息
            yongshen = bazi_analysis.get("用神", {})
            if yongshen:
                bazi_data["用神"] = yongshen

            # 提取日主信息
            analysis = bazi_analysis.get("analysis", {})
            if analysis and "day_master" in analysis:
                day_master = analysis["day_master"]
                bazi_data["日主"] = {
                    "天干": day_master.get("gan", ""),
                    "五行": day_master.get("element", ""),
                    "强弱": day_master.get("strength", "")
                }

            return bazi_data

        except Exception as e:
            logger.error(f"提取八字数据失败: {e}")
            return {}

    def _extract_specific_data(self, raw_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """根据分析类型提取特定数据"""
        try:
            specific_data = {}

            if analysis_type == "personality_destiny":
                # 命宫分析特定数据
                specific_data = self._extract_personality_data(raw_data)
            elif analysis_type == "wealth_fortune":
                # 财富分析特定数据
                specific_data = self._extract_wealth_data(raw_data)
            elif analysis_type == "marriage_love":
                # 婚姻分析特定数据
                specific_data = self._extract_marriage_data(raw_data)
            elif analysis_type == "health_wellness":
                # 健康分析特定数据
                specific_data = self._extract_health_data(raw_data)

            return specific_data

        except Exception as e:
            logger.error(f"提取{analysis_type}特定数据失败: {e}")
            return {}

    def _extract_personality_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取性格命运数据"""
        try:
            data = {}

            # 命宫信息
            ziwei_analysis = raw_data.get("ziwei_analysis", {})
            palaces = ziwei_analysis.get("palaces", {})
            mingong = palaces.get("命宫", {})

            if mingong:
                data["命宫"] = {
                    "位置": mingong.get("position", ""),
                    "主星": mingong.get("major_stars", []),
                    "辅星": mingong.get("minor_stars", [])
                }

            # 福德宫信息
            fude = palaces.get("福德宫", {})
            if fude:
                data["福德宫"] = {
                    "位置": fude.get("position", ""),
                    "主星": fude.get("major_stars", []),
                    "辅星": fude.get("minor_stars", [])
                }

            return data

        except Exception as e:
            logger.error(f"提取性格命运数据失败: {e}")
            return {}

    def _extract_wealth_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取财富数据"""
        try:
            data = {}

            # 财帛宫信息
            ziwei_analysis = raw_data.get("ziwei_analysis", {})
            palaces = ziwei_analysis.get("palaces", {})
            caibo = palaces.get("财帛宫", {})

            if caibo:
                data["财帛宫"] = {
                    "位置": caibo.get("position", ""),
                    "主星": caibo.get("major_stars", []),
                    "辅星": caibo.get("minor_stars", [])
                }

            # 田宅宫信息
            tianzhai = palaces.get("田宅宫", {})
            if tianzhai:
                data["田宅宫"] = {
                    "位置": tianzhai.get("position", ""),
                    "主星": tianzhai.get("major_stars", []),
                    "辅星": tianzhai.get("minor_stars", [])
                }

            return data

        except Exception as e:
            logger.error(f"提取财富数据失败: {e}")
            return {}

    def _extract_marriage_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取婚姻数据"""
        try:
            data = {}

            # 夫妻宫信息
            ziwei_analysis = raw_data.get("ziwei_analysis", {})
            palaces = ziwei_analysis.get("palaces", {})
            fuqi = palaces.get("夫妻宫", {})

            if fuqi:
                data["夫妻宫"] = {
                    "位置": fuqi.get("position", ""),
                    "主星": fuqi.get("major_stars", []),
                    "辅星": fuqi.get("minor_stars", [])
                }

            return data

        except Exception as e:
            logger.error(f"提取婚姻数据失败: {e}")
            return {}

    def _extract_health_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取健康数据"""
        try:
            data = {}

            # 疾厄宫信息
            ziwei_analysis = raw_data.get("ziwei_analysis", {})
            palaces = ziwei_analysis.get("palaces", {})
            jie = palaces.get("疾厄宫", {})

            if jie:
                data["疾厄宫"] = {
                    "位置": jie.get("position", ""),
                    "主星": jie.get("major_stars", []),
                    "辅星": jie.get("minor_stars", [])
                }

            return data

        except Exception as e:
            logger.error(f"提取健康数据失败: {e}")
            return {}

    def validate_data_integrity(self, analysis_data: Dict[str, Any]) -> bool:
        """验证数据完整性"""
        try:
            # 检查基础结构
            if not analysis_data.get("analysis_type"):
                logger.warning("缺少分析类型")
                return False

            # 检查是否为合盘分析
            if analysis_data.get("data_source") == "compatibility_analysis":
                return self._validate_compatibility_data_integrity(analysis_data)

            # 检查紫薇数据
            ziwei_data = analysis_data.get("ziwei", {})
            palaces = ziwei_data.get("palaces", {})

            # 如果没有宫位数据，尝试从多个来源获取
            if not palaces:
                # 尝试从原始数据中获取
                raw_ziwei = analysis_data.get("raw_ziwei_data", {})
                if isinstance(raw_ziwei, dict):
                    palaces = raw_ziwei.get("palaces", {})

                # 如果还是没有，尝试从其他路径
                if not palaces:
                    # 尝试从analysis_data的其他字段获取
                    for key in ["ziwei_analysis", "raw_calculation"]:
                        if key in analysis_data:
                            data_source = analysis_data[key]
                            if isinstance(data_source, dict):
                                palaces = data_source.get("palaces", {})
                                if palaces:
                                    break

                if palaces:
                    logger.info("从备用数据源中找到宫位数据")
                    # 更新ziwei_data
                    ziwei_data["palaces"] = palaces
                else:
                    logger.warning("缺少紫薇宫位数据")
                    # 不直接返回False，继续检查八字数据
                    has_ziwei = False
            else:
                has_ziwei = True

            # 检查关键宫位（如果有紫薇数据）
            has_mingong = False
            if palaces and "命宫" in palaces:
                mingong = palaces["命宫"]
                # 检查命宫数据的不同字段名
                position = (mingong.get("位置") or mingong.get("position") or
                          mingong.get("palace_position"))
                major_stars = (mingong.get("主星") or mingong.get("major_stars") or
                             mingong.get("stars"))

                if position or major_stars:
                    has_mingong = True
                    logger.info("命宫数据验证通过")
                else:
                    logger.warning(f"命宫数据不完整: position={position}, major_stars={major_stars}")

            # 检查八字数据
            bazi_data = analysis_data.get("bazi", {})
            has_bazi = bool(bazi_data and (
                bazi_data.get("年柱") or bazi_data.get("月柱") or
                bazi_data.get("日柱") or bazi_data.get("时柱")
            ))

            # 至少要有紫薇或八字数据之一
            if not (has_ziwei or has_bazi):
                logger.warning("既没有紫薇数据也没有八字数据")
                return False

            if has_ziwei and not has_mingong:
                logger.warning("有紫薇数据但命宫数据不完整")
                # 如果有八字数据，仍然可以继续
                if not has_bazi:
                    return False

            logger.info("✅ 数据完整性验证通过")
            return True

        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            return False

    def _validate_compatibility_data_integrity(self, analysis_data: Dict[str, Any]) -> bool:
        """验证合盘分析数据完整性"""
        try:
            # 检查合盘分析基础结构
            if not analysis_data.get("compatibility_dimension"):
                logger.warning("缺少合盘分析维度")
                return False

            # 检查双方数据
            person_a_data = analysis_data.get("person_a", {})
            person_b_data = analysis_data.get("person_b", {})

            if not person_a_data or not person_b_data:
                logger.warning("缺少双方数据")
                return False

            # 检查双方的基本信息
            person_a_info = person_a_data.get("info", {})
            person_b_info = person_b_data.get("info", {})

            if not person_a_info or not person_b_info:
                logger.warning("缺少双方基本信息")
                return False

            # 检查双方的紫薇数据（至少要有基本结构）
            person_a_ziwei = person_a_data.get("ziwei", {})
            person_b_ziwei = person_b_data.get("ziwei", {})

            # 对于合盘分析，只要有基本的紫薇或八字数据就可以
            person_a_has_data = bool(person_a_ziwei.get("palaces")) or bool(person_a_data.get("bazi"))
            person_b_has_data = bool(person_b_ziwei.get("palaces")) or bool(person_b_data.get("bazi"))

            if not person_a_has_data:
                logger.warning("A的命盘数据不完整")
                return False

            if not person_b_has_data:
                logger.warning("B的命盘数据不完整")
                return False

            logger.info("✅ 合盘数据完整性验证通过")
            return True

        except Exception as e:
            logger.error(f"合盘数据完整性验证失败: {e}")
            return False

    def _extract_compatibility_data(self, raw_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """提取合盘分析数据"""
        try:
            logger.info(f"🔍 开始提取合盘{analysis_type}分析数据")

            # 基础数据结构
            analysis_data = {
                "analysis_type": analysis_type,
                "data_source": "compatibility_analysis",
                "compatibility_dimension": raw_data.get("compatibility_dimension", ""),
                "relationship_type": raw_data.get("relationship_type", "")
            }

            # 提取A的数据
            person_a_data = raw_data.get("person_a", {})
            if person_a_data:
                analysis_data["person_a"] = {
                    "info": person_a_data.get("info", {}),
                    "ziwei": self._extract_person_ziwei_data(person_a_data.get("ziwei_analysis", {})),
                    "bazi": self._extract_person_bazi_data(person_a_data.get("bazi_analysis", {}))
                }

            # 提取B的数据
            person_b_data = raw_data.get("person_b", {})
            if person_b_data:
                analysis_data["person_b"] = {
                    "info": person_b_data.get("info", {}),
                    "ziwei": self._extract_person_ziwei_data(person_b_data.get("ziwei_analysis", {})),
                    "bazi": self._extract_person_bazi_data(person_b_data.get("bazi_analysis", {}))
                }

            logger.info(f"✅ 合盘{analysis_type}数据提取完成")
            return analysis_data

        except Exception as e:
            logger.error(f"提取合盘{analysis_type}数据失败: {e}")
            return {}

    def _extract_person_ziwei_data(self, ziwei_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """提取个人紫薇斗数数据（用于合盘）"""
        try:
            if not ziwei_analysis:
                return {}

            ziwei_data = {}

            # 提取宫位信息
            palaces = ziwei_analysis.get("palaces", {})
            if palaces:
                ziwei_data["palaces"] = {}

                # 重要宫位
                important_palaces = [
                    "命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                    "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"
                ]

                for palace_name in important_palaces:
                    if palace_name in palaces:
                        palace_info = palaces[palace_name]
                        ziwei_data["palaces"][palace_name] = {
                            "位置": palace_info.get("position", ""),
                            "主星": palace_info.get("major_stars", []),
                            "辅星": palace_info.get("minor_stars", [])
                        }

            return ziwei_data

        except Exception as e:
            logger.error(f"提取个人紫薇数据失败: {e}")
            return {}

    def _extract_person_bazi_data(self, bazi_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """提取个人八字数据（用于合盘）"""
        try:
            if not bazi_analysis or not bazi_analysis.get("success"):
                return {}

            bazi_data = {}

            # 提取基本信息
            bazi_info = bazi_analysis.get("bazi_info", {})
            if bazi_info:
                bazi_data["四柱"] = bazi_info.get("chinese_date", "")
                bazi_data["年柱"] = bazi_info.get("year_pillar", "")
                bazi_data["月柱"] = bazi_info.get("month_pillar", "")
                bazi_data["日柱"] = bazi_info.get("day_pillar", "")
                bazi_data["时柱"] = bazi_info.get("hour_pillar", "")

            # 提取分析信息
            analysis = bazi_analysis.get("analysis", {})
            if analysis:
                # 五行信息
                wuxing = analysis.get("wuxing", {})
                if wuxing and "count" in wuxing:
                    bazi_data["五行"] = wuxing["count"]

                # 日主信息
                day_master = analysis.get("day_master", {})
                if day_master:
                    bazi_data["日主"] = {
                        "天干": day_master.get("gan", ""),
                        "五行": day_master.get("element", ""),
                        "强弱": day_master.get("strength", "")
                    }

            return bazi_data

        except Exception as e:
            logger.error(f"提取个人八字数据失败: {e}")
            return {}
