#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正常通俗易懂的分析风格
确保不会过度戏剧化
"""

import asyncio

async def test_normal_style():
    """测试正常通俗易懂的分析风格"""
    print("📝 测试正常通俗易懂的分析风格")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("test_calc")
        
        # 测试生辰信息
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "11",
            "gender": "男"
        }
        
        print(f"📅 测试生辰: {birth_info}")
        
        # 获取融合分析数据
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=int(birth_info["hour"]),
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            print("✅ 融合分析数据获取成功")
            
            # 执行性格分析
            print(f"\n🎯 执行正常风格的性格分析...")
            
            analysis_result = await calculator_agent._analyze_single_angle(
                "性格分析", "personality_destiny", "性格特点和命运格局",
                raw_data, birth_info, "紫薇+八字融合分析"
            )
            
            if analysis_result:
                print(f"✅ 分析完成，字数: {len(analysis_result)}")
                
                # 检查语言风格
                dramatic_terms = ["掏出", "烟袋", "师傅", "小伙子", "敲敲", "慢悠悠"]
                professional_terms = ["专业分析", "详细解析", "深度剖析", "理论依据"]
                normal_terms = ["从", "来看", "显示", "说明", "分析", "建议"]
                
                dramatic_count = sum(1 for term in dramatic_terms if term in analysis_result)
                professional_count = sum(1 for term in professional_terms if term in analysis_result)
                normal_count = sum(1 for term in normal_terms if term in analysis_result)
                
                print(f"📊 语言风格检查:")
                print(f"  戏剧化表达: {dramatic_count}个")
                print(f"  过度专业: {professional_count}个")
                print(f"  正常表达: {normal_count}个")
                
                if dramatic_count == 0 and normal_count > professional_count:
                    print("✅ 语言风格正常：通俗易懂，不过度戏剧化")
                elif dramatic_count > 0:
                    print("❌ 语言风格过度戏剧化")
                elif professional_count > normal_count:
                    print("⚠️ 语言风格过度专业化")
                else:
                    print("✅ 语言风格适中")
                
                # 显示分析内容的前500字
                print(f"\n📝 正常风格分析内容预览:")
                print("-" * 60)
                print(analysis_result[:500] + "..." if len(analysis_result) > 500 else analysis_result)
                print("-" * 60)
                
                return True
            else:
                print("❌ 分析失败")
                return False
        else:
            print(f"❌ 融合分析失败: {raw_data.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_liuyao_normal_style():
    """测试六爻的正常风格"""
    print("\n🔮 测试六爻正常风格")
    print("=" * 40)
    
    try:
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool
        
        # 创建六爻工具
        liuyao_tool = HumanizedLiuyaoTool()
        
        # 模拟用户问题
        intent = {
            "tool": "liuyao",
            "question_type": "career",
            "confidence": 0.9
        }
        
        context = {
            "user_message": "我想问问工作运势怎么样，最近想换工作",
            "session_id": "test_session"
        }
        
        print(f"👤 用户问题: {context['user_message']}")
        
        # 执行六爻占卜
        result = liuyao_tool.execute(intent, context)
        
        if result.get("success"):
            print("✅ 六爻占卜成功")
            
            analysis = result.get("analysis", "")
            if analysis:
                print(f"📊 分析字数: {len(analysis)}")
                
                # 检查语言风格
                dramatic_terms = ["掏出", "烟袋", "师傅", "小伙子", "敲敲", "慢悠悠"]
                normal_terms = ["卦象", "显示", "分析", "建议", "情况", "注意"]
                
                dramatic_count = sum(1 for term in dramatic_terms if term in analysis)
                normal_count = sum(1 for term in normal_terms if term in analysis)
                
                print(f"📊 语言风格检查:")
                print(f"  戏剧化表达: {dramatic_count}个")
                print(f"  正常表达: {normal_count}个")
                
                if dramatic_count == 0:
                    print("✅ 六爻语言风格正常：通俗易懂，不过度戏剧化")
                else:
                    print("❌ 六爻语言风格过度戏剧化")
                
                # 显示分析内容的前400字
                print(f"\n📝 正常风格六爻分析预览:")
                print("-" * 60)
                print(analysis[:400] + "..." if len(analysis) > 400 else analysis)
                print("-" * 60)
                
                return dramatic_count == 0
            else:
                print("❌ 没有分析内容")
                return False
        else:
            print(f"❌ 六爻占卜失败: {result.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 六爻测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("📝 正常通俗易懂风格测试")
    print("=" * 70)
    
    # 1. 测试紫薇+八字正常风格
    success1 = await test_normal_style()
    
    # 2. 测试六爻正常风格
    success2 = await test_liuyao_normal_style()
    
    print("\n" + "=" * 70)
    print("🎯 正常风格测试结果总结:")
    
    if success1:
        print("✅ 紫薇+八字风格正常：通俗易懂，不过度戏剧化")
    else:
        print("❌ 紫薇+八字风格有问题")
    
    if success2:
        print("✅ 六爻风格正常：通俗易懂，不过度戏剧化")
    else:
        print("❌ 六爻风格有问题")
    
    if success1 and success2:
        print("\n🎉 风格调整成功！现在是正常的通俗易懂风格！")
        print("💡 特点:")
        print("  - 语言通俗易懂，避免专业术语")
        print("  - 自然流畅，像正常交流")
        print("  - 紫薇+八字相互印证清楚")
        print("  - 六爻解卦简洁明了")
        print("  - 没有过度戏剧化的表演")
    else:
        print("\n⚠️ 还需要进一步调整风格")

if __name__ == "__main__":
    asyncio.run(main())
