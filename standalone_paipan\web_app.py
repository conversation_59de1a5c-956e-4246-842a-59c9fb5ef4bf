#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立排盘Web应用
使用Flask创建简单的Web界面
"""

import sys
import os
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from simple_interface import SimplePaipanInterface
from database import PaipanDatabase
from llm_service import LLMService

app = Flask(__name__)
app.secret_key = 'ziwei_paipan_2025'

# 全局接口实例
paipan_interface = None
db = PaipanDatabase()
llm_service = LLMService()

def get_paipan_interface():
    """延迟初始化排盘接口"""
    global paipan_interface
    if paipan_interface is None:
        try:
            print("🔄 初始化排盘接口...")
            paipan_interface = SimplePaipanInterface()
            print("✅ 排盘接口初始化成功")
        except Exception as e:
            print(f"❌ 排盘接口初始化失败: {e}")
            raise e
    return paipan_interface

@app.route('/')
def index():
    """首页 - 输入页面"""
    return render_template('index.html')

@app.route('/calculate', methods=['POST'])
def calculate():
    """计算排盘"""
    try:
        # 获取表单数据
        year = int(request.form.get('year'))
        month = int(request.form.get('month'))
        day = int(request.form.get('day'))
        hour = int(request.form.get('hour'))
        gender = request.form.get('gender', '男')

        # 验证输入
        if not (1900 <= year <= 2100):
            return jsonify({'success': False, 'error': '年份必须在1900-2100之间'})
        if not (1 <= month <= 12):
            return jsonify({'success': False, 'error': '月份必须在1-12之间'})
        if not (1 <= day <= 31):
            return jsonify({'success': False, 'error': '日期必须在1-31之间'})
        if not (0 <= hour <= 23):
            return jsonify({'success': False, 'error': '小时必须在0-23之间'})

        print(f"🔮 Web请求: {year}年{month}月{day}日{hour}时 {gender}")

        # 使用增强的错误处理
        try:
            print("🔄 开始排盘计算...")

            # 分步骤执行，便于定位问题
            print("📊 步骤1: 获取计算接口")
            interface = get_paipan_interface()
            print("✅ 步骤1完成")

            print("📊 步骤2: 执行排盘计算")
            result = interface.calculate_and_save(year, month, day, hour, gender)
            print("✅ 步骤2完成")

            print("📊 步骤3: 检查计算结果")
            if not isinstance(result, dict):
                raise ValueError(f"计算返回类型错误: {type(result)}")
            print("✅ 步骤3完成")

            print(f"📊 步骤4: 验证结果状态: {result.get('success', False)}")

        except ImportError as import_error:
            print(f"❌ 导入错误: {import_error}")
            return jsonify({
                'success': False,
                'error': f'模块导入失败: {str(import_error)}。请检查依赖包安装'
            })
        except AttributeError as attr_error:
            print(f"❌ 属性错误: {attr_error}")
            return jsonify({
                'success': False,
                'error': f'算法模块错误: {str(attr_error)}。请检查算法模块'
            })
        except ValueError as value_error:
            print(f"❌ 数值错误: {value_error}")
            return jsonify({
                'success': False,
                'error': f'输入数据错误: {str(value_error)}'
            })
        except Exception as calc_error:
            print(f"❌ 计算异常: {calc_error}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': f'计算异常: {str(calc_error)}。请重试或联系技术支持'
            })

        print(f"✅ 计算完成: {result.get('success', False)}")

        if result['success']:
            # 保存到数据库
            try:
                print("💾 保存数据到数据库...")
                record_id = db.save_paipan_result(result['calculation_result'])
                print(f"✅ 数据库保存成功，记录ID: {record_id}")

                # 直接返回记录ID，前端通过ID从数据库获取数据
                return jsonify({
                    'success': True,
                    'record_id': record_id,
                    'message': '排盘计算完成，数据已保存'
                })

            except Exception as db_error:
                print(f"❌ 数据库保存失败: {db_error}")
                # 如果数据库保存失败，返回临时数据
                return jsonify({
                    'success': True,
                    'data': result['calculation_result'],
                    'formatted_output': result['formatted_output'],
                    'temp_data': True,
                    'message': '排盘计算完成，但数据库保存失败'
                })
        else:
            error_msg = result.get('error', '计算失败')
            print(f"❌ 计算失败: {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            })

    except ValueError as e:
        error_msg = f'输入格式错误: {str(e)}'
        print(f"❌ 输入错误: {error_msg}")
        return jsonify({'success': False, 'error': error_msg})
    except Exception as e:
        error_msg = f'计算异常: {str(e)}'
        print(f"❌ 系统异常: {error_msg}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': error_msg})

@app.route('/result')
def result():
    """结果展示页面（兼容旧版本）"""
    return render_template('result.html')

@app.route('/result/<int:record_id>')
def result_by_id(record_id):
    """通过记录ID展示结果页面"""
    return render_template('result.html', record_id=record_id)

@app.route('/liuyao')
def liuyao():
    """显示六爻占卜页面"""
    return render_template('liuyao.html')

@app.route('/debug')
def debug():
    """数据调试页面"""
    return render_template('debug_data.html')

@app.route('/database')
def database():
    """数据库管理页面"""
    return render_template('database.html')

@app.route('/admin')
def admin():
    """后台管理系统"""
    return render_template('admin.html')

@app.route('/api/records')
def get_records():
    """获取排盘记录列表"""
    try:
        # 获取查询参数
        gender = request.args.get('gender')
        birth_year = request.args.get('birth_year', type=int)
        success_only = request.args.get('success_only', 'true').lower() == 'true'
        limit = request.args.get('limit', type=int)

        # 构建查询条件
        search_params = {}
        if gender:
            search_params['gender'] = gender
        if birth_year:
            search_params['birth_year'] = birth_year
        if success_only:
            search_params['success'] = True
        if limit:
            search_params['limit'] = limit

        # 查询记录
        records = db.search_records(**search_params)

        return jsonify({
            'success': True,
            'records': records,
            'count': len(records)
        })

    except Exception as e:
        print(f"❌ 获取记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/record/<int:record_id>')
def get_record(record_id):
    """根据ID获取具体排盘记录"""
    try:
        record_data = db.get_paipan_record(record_id)

        if record_data:
            return jsonify({
                'success': True,
                'data': record_data
            })
        else:
            return jsonify({
                'success': False,
                'error': '记录不存在'
            })

    except Exception as e:
        print(f"❌ 获取记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/statistics')
def get_statistics():
    """获取数据库统计信息"""
    try:
        stats = db.get_statistics()
        return jsonify({
            'success': True,
            'statistics': stats
        })

    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/analysis/status/<int:record_id>')
def get_analysis_status(record_id):
    """获取记录的分析状态"""
    try:
        status = db.get_angle_analysis_status(record_id)
        return jsonify({
            'success': True,
            'status': status
        })

    except Exception as e:
        print(f"❌ 获取分析状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/analysis/analyze', methods=['POST'])
def analyze_angle():
    """分析单个角度"""
    try:
        data = request.get_json()
        record_id = data.get('record_id')
        angle_key = data.get('angle_key')
        angle_name = data.get('angle_name')

        if not all([record_id, angle_key, angle_name]):
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            })

        # 获取排盘数据
        record_data = db.get_paipan_record(record_id)
        if not record_data:
            return jsonify({
                'success': False,
                'error': '记录不存在'
            })

        # 定义角度描述
        angle_descriptions = {
            'personality_destiny': '性格命运核心特征与天赋潜能',
            'wealth_fortune': '财运状况、理财投资与财富积累',
            'marriage_love': '感情婚姻、桃花运势与配偶关系',
            'health_wellness': '健康状况、疾病预防与养生指导',
            'career_achievement': '事业发展、成就运势与职业规划',
            'children_creativity': '子女运势、创造力与生育指导',
            'interpersonal_relationship': '人际关系、贵人运势与社交能力',
            'education_learning': '学习教育、智慧发展与知识积累',
            'family_environment': '家庭环境、房产田宅与居住运势',
            'travel_relocation': '迁移变动、外出运势与环境适应',
            'spiritual_blessing': '精神世界、福德修养与心灵成长',
            'authority_parents': '父母长辈、权威关系与传承运势'
        }

        description = angle_descriptions.get(angle_key, angle_name)

        # 调用LLM分析
        result = llm_service.analyze_single_angle(record_data, angle_key, angle_name, description)

        if result['success']:
            # 保存分析结果到数据库
            db.save_angle_analysis(
                record_id=record_id,
                angle_key=angle_key,
                angle_name=angle_name,
                analysis_content=result['content'],
                word_count=result['word_count'],
                success=True
            )

            return jsonify({
                'success': True,
                'word_count': result['word_count'],
                'analysis_time': result.get('analysis_time', 0)
            })
        else:
            # 保存失败记录
            db.save_angle_analysis(
                record_id=record_id,
                angle_key=angle_key,
                angle_name=angle_name,
                analysis_content='',
                word_count=0,
                success=False,
                error_message=result['error']
            )

            return jsonify({
                'success': False,
                'error': result['error']
            })

    except Exception as e:
        print(f"❌ 角度分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/analysis/view/<int:record_id>/<angle_key>')
def view_angle_analysis(record_id, angle_key):
    """查看角度分析内容"""
    try:
        analyses = db.get_angle_analyses(record_id)
        analysis = next((a for a in analyses if a['angle_key'] == angle_key), None)

        if analysis:
            return jsonify({
                'success': True,
                'analysis': analysis
            })
        else:
            return jsonify({
                'success': False,
                'error': '分析不存在'
            })

    except Exception as e:
        print(f"❌ 获取分析内容失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/chat/history/<int:record_id>')
def get_chat_history(record_id):
    """获取聊天历史"""
    try:
        history = db.get_chat_history(record_id, limit=20)
        return jsonify({
            'success': True,
            'history': history
        })

    except Exception as e:
        print(f"❌ 获取聊天历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """发送聊天消息"""
    try:
        data = request.get_json()
        record_id = data.get('record_id')
        session_id = data.get('session_id')
        message = data.get('message')

        print(f"💬 聊天请求参数: record_id={record_id}, session_id={session_id}, message={message[:50] if message else None}...")

        if not all([record_id, session_id, message]):
            missing_params = []
            if not record_id:
                missing_params.append('record_id')
            if not session_id:
                missing_params.append('session_id')
            if not message:
                missing_params.append('message')

            error_msg = f'缺少必要参数: {", ".join(missing_params)}'
            print(f"❌ {error_msg}")

            return jsonify({
                'success': False,
                'error': error_msg
            })

        # 获取排盘数据
        record_data = db.get_paipan_record(record_id)
        if not record_data:
            return jsonify({
                'success': False,
                'error': '记录不存在'
            })

        # 获取聊天历史
        chat_history = db.get_chat_history(record_id, limit=10)

        # 调用LLM聊天
        result = llm_service.chat_with_knowledge(record_data, message, chat_history)

        if result['success']:
            # 保存聊天记录
            db.save_chat_message(
                record_id=record_id,
                session_id=session_id,
                user_message=message,
                assistant_response=result['response'],
                response_time_ms=result.get('response_time_ms', 0),
                knowledge_used=result.get('knowledge_used', {})
            )

            return jsonify({
                'success': True,
                'response': result['response']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error'],
                'response': result.get('response', '抱歉，我暂时无法回答您的问题。')
            })

    except Exception as e:
        print(f"❌ 聊天处理失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'response': '抱歉，处理您的问题时出现了错误。'
        })

@app.route('/api/quick_calculate', methods=['POST'])
def quick_calculate():
    """快速计算API"""
    try:
        data = request.get_json()
        birth_string = data.get('birth_string', '')
        gender = data.get('gender', '男')

        print(f"🚀 快速计算请求: '{birth_string}' {gender}")

        # 使用增强的错误处理
        try:
            interface = get_paipan_interface()
            result = interface.quick_calculate(birth_string, gender)
        except Exception as calc_error:
            print(f"❌ 快速计算异常: {calc_error}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'error': f'快速计算异常: {str(calc_error)}'
            })

        print(f"✅ 快速计算完成: {result.get('success', False)}")

        if result['success']:
            return jsonify({
                'success': True,
                'data': result['calculation_result'],
                'formatted_output': result['formatted_output'],
                'saved_files': result['saved_files']
            })
        else:
            error_msg = result.get('error', '计算失败')
            print(f"❌ 快速计算失败: {error_msg}")
            return jsonify({
                'success': False,
                'error': error_msg
            })

    except Exception as e:
        error_msg = f'计算异常: {str(e)}'
        print(f"❌ 快速计算异常: {error_msg}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': error_msg})

@app.route('/api/list_results')
def list_results():
    """列出已保存的结果"""
    try:
        files = paipan_interface.saver.list_saved_results()
        return jsonify({'success': True, 'files': files})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/compatibility/create', methods=['POST'])
def create_compatibility_analysis():
    """创建合盘分析"""
    try:
        data = request.get_json()
        person_a = data.get('person_a', {})
        person_b = data.get('person_b', {})
        analysis_dimension = data.get('analysis_dimension', 'overall_compatibility')

        print(f"💕 合盘分析请求: {person_a.get('name', 'A')} & {person_b.get('name', 'B')}")
        print(f"📊 分析维度: {analysis_dimension}")

        # 验证输入数据
        required_fields = ['name', 'gender', 'year', 'month', 'day', 'hour']
        for person, label in [(person_a, '甲方'), (person_b, '乙方')]:
            for field in required_fields:
                if not person.get(field):
                    return jsonify({
                        'success': False,
                        'error': f'{label}的{field}信息不能为空'
                    })

        # 构建合盘数据
        compatibility_data = {
            'person_a': person_a,
            'person_b': person_b,
            'analysis_dimension': analysis_dimension,
            'created_time': datetime.now().isoformat()
        }

        # 保存到数据库
        compatibility_id = db.save_compatibility_record(compatibility_data)

        if compatibility_id:
            # 启动后台合盘分析任务
            print(f"🔄 启动后台合盘分析任务: {compatibility_id}")

            # 使用线程池执行后台分析，避免阻塞请求
            import threading
            analysis_thread = threading.Thread(
                target=run_compatibility_analysis_background,
                args=(compatibility_id, person_a, person_b, analysis_dimension)
            )
            analysis_thread.daemon = True
            analysis_thread.start()

            result = {
                'success': True,
                'compatibility_id': compatibility_id,
                'person_a': person_a,
                'person_b': person_b,
                'analysis_dimension': analysis_dimension,
                'status': 'processing',
                'created_time': datetime.now().isoformat(),
                'message': '合盘分析已创建并保存到数据库，后台分析正在进行中...'
            }

            print(f"✅ 合盘分析创建成功，后台任务已启动: {compatibility_id}")
            return jsonify(result)
        else:
            return jsonify({
                'success': False,
                'error': '保存合盘记录失败'
            })

    except Exception as e:
        print(f"❌ 创建合盘分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/compatibility/list')
def list_compatibility_records():
    """获取合盘记录列表"""
    try:
        # 从数据库获取合盘记录
        limit = request.args.get('limit', 50, type=int)
        records = db.get_compatibility_records(limit)

        print(f"📋 获取到 {len(records)} 条合盘记录")

        return jsonify({
            'success': True,
            'records': records,
            'total': len(records)
        })

    except Exception as e:
        print(f"❌ 获取合盘记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/compatibility/<compatibility_id>')
def get_compatibility_result(compatibility_id):
    """获取合盘分析结果"""
    try:
        # 从数据库获取合盘记录
        record = db.get_compatibility_record(compatibility_id)

        if record:
            result = {
                'success': True,
                'compatibility_id': compatibility_id,
                'status': record.get('status', 'processing'),
                'analysis_content': record.get('analysis_content', '分析正在进行中...'),
                'created_time': record.get('created_time', ''),
                'completed_time': record.get('completed_time', ''),
                'person_a': record.get('person_a', {}),
                'person_b': record.get('person_b', {}),
                'analysis_dimension': record.get('analysis_dimension', ''),
                'word_count': record.get('word_count', 0)
            }

            print(f"✅ 获取合盘结果成功: {compatibility_id}")
            return jsonify(result)
        else:
            return jsonify({
                'success': False,
                'error': '合盘记录不存在'
            })

    except Exception as e:
        print(f"❌ 获取合盘结果失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/compatibility/<compatibility_id>', methods=['DELETE'])
def delete_compatibility_record(compatibility_id):
    """删除合盘记录"""
    try:
        success = db.delete_compatibility_record(compatibility_id)

        if success:
            return jsonify({
                'success': True,
                'message': f'合盘记录 {compatibility_id} 删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除合盘记录失败'
            })

    except Exception as e:
        print(f"❌ 删除合盘记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/compatibility/<compatibility_id>/status')
def get_compatibility_status(compatibility_id):
    """获取合盘分析状态"""
    try:
        record = db.get_compatibility_record(compatibility_id)

        if record:
            return jsonify({
                'success': True,
                'compatibility_id': compatibility_id,
                'status': record.get('status', 'processing'),
                'word_count': record.get('word_count', 0),
                'created_time': record.get('created_time', ''),
                'completed_time': record.get('completed_time', ''),
                'has_content': bool(record.get('analysis_content'))
            })
        else:
            return jsonify({
                'success': False,
                'error': '合盘记录不存在'
            })

    except Exception as e:
        print(f"❌ 获取合盘状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def run_compatibility_analysis_background(compatibility_id, person_a, person_b, analysis_dimension):
    """后台运行真实合盘分析"""
    try:
        print(f"🔄 开始后台合盘分析: {compatibility_id}")
        print(f"👥 分析对象: {person_a.get('name', 'A')} & {person_b.get('name', 'B')}")
        print(f"📊 分析维度: {analysis_dimension}")

        # 添加项目根目录到路径
        import sys
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        print(f"📂 项目根目录: {project_root}")

        # 导入真实的合盘分析引擎
        print(f"📦 导入合盘分析引擎...")
        from core.compatibility_analysis import CompatibilityAnalysisEngine

        # 初始化合盘分析引擎
        print(f"🔧 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()

        # 计算合盘数据
        print(f"📊 正在计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a, person_b)

        if not compatibility_data.get("success"):
            error_msg = f"合盘数据计算失败: {compatibility_data.get('error', '未知错误')}"
            print(f"❌ {error_msg}")

            # 更新数据库状态为失败
            db.update_compatibility_result(compatibility_id, error_msg, 'failed')
            return

        print(f"✅ 合盘数据计算完成")

        # 执行具体维度分析
        print(f"🔍 开始执行分析维度: {analysis_dimension}")

        # 创建事件循环来运行异步分析
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            analysis_result = loop.run_until_complete(
                compatibility_engine.execute_compatibility_analysis(compatibility_data, analysis_dimension)
            )
        finally:
            loop.close()

        if not analysis_result.get("success"):
            error_msg = f"合盘分析失败: {analysis_result.get('error', '未知错误')}"
            print(f"❌ {error_msg}")

            # 更新数据库状态为失败
            db.update_compatibility_result(compatibility_id, error_msg, 'failed')
            return

        # 获取分析内容
        analysis_content = analysis_result.get("content", "")
        word_count = len(analysis_content)

        print(f"✅ 合盘分析完成，内容长度: {word_count}字")

        # 更新数据库结果
        success = db.update_compatibility_result(compatibility_id, analysis_content, 'completed')

        if success:
            print(f"✅ 合盘分析结果已保存到数据库: {compatibility_id}")
        else:
            print(f"❌ 保存合盘分析结果失败: {compatibility_id}")

    except ImportError as e:
        error_msg = f"导入合盘分析模块失败: {str(e)}"
        print(f"❌ {error_msg}")
        print(f"📂 当前Python路径: {sys.path}")

        # 更新数据库状态为失败
        try:
            db.update_compatibility_result(compatibility_id, error_msg, 'failed')
        except Exception as db_error:
            print(f"❌ 更新数据库状态失败: {db_error}")

    except Exception as e:
        error_msg = f"后台合盘分析异常: {str(e)}"
        print(f"❌ {error_msg}")
        import traceback
        print(f"❌ 详细错误信息: {traceback.format_exc()}")

        # 更新数据库状态为失败
        try:
            db.update_compatibility_result(compatibility_id, error_msg, 'failed')
        except Exception as db_error:
            print(f"❌ 更新数据库状态失败: {db_error}")

@app.route('/api/liuyao/create', methods=['POST'])
def create_liuyao_divination():
    """创建六爻占卜"""
    try:
        data = request.get_json()
        question = data.get('question', '')
        method = data.get('method', 'time')

        print(f"🔮 六爻占卜请求: {question}")
        print(f"📊 起卦方式: {method}")

        # 验证输入数据
        if not question.strip():
            return jsonify({
                'success': False,
                'error': '占卜问题不能为空'
            })

        # 构建六爻数据
        liuyao_data = {
            'question': question,
            'method': method,
            'created_time': datetime.now().isoformat()
        }

        # 根据起卦方式添加额外数据
        if method == 'numbers':
            numbers = data.get('numbers', [])
            if len(numbers) < 2:
                return jsonify({
                    'success': False,
                    'error': '数字起卦需要两个数字'
                })
            liuyao_data['divination_data'] = {'numbers': numbers}
        elif method == 'coins':
            coin_results = data.get('coin_results', [])
            if len(coin_results) != 6:
                return jsonify({
                    'success': False,
                    'error': '硬币起卦需要6次投掷结果'
                })
            liuyao_data['divination_data'] = {'coin_results': coin_results}
        else:
            # 时间起卦 - 使用简化的时间起卦算法
            now = datetime.now()

            # 简化的时间起卦：根据时间生成6个硬币结果
            import random
            random.seed(now.year + now.month + now.day + now.hour + now.minute)

            coin_results = []
            for i in range(6):
                # 每次投掷3枚硬币
                throw_result = []
                for j in range(3):
                    throw_result.append(random.choice(['正', '反']))
                coin_results.append(throw_result)

            liuyao_data['divination_data'] = {
                'year': now.year,
                'month': now.month,
                'day': now.day,
                'hour': now.hour,
                'minute': now.minute,
                'coin_results': coin_results  # 添加硬币结果用于生成卦象
            }

        # 保存到数据库
        liuyao_id = db.save_liuyao_record(liuyao_data)

        if liuyao_id:
            # 启动后台六爻分析任务
            print(f"🔄 启动后台六爻分析任务: {liuyao_id}")

            # 使用线程池执行后台分析，避免阻塞请求
            import threading
            analysis_thread = threading.Thread(
                target=run_liuyao_analysis_background,
                args=(liuyao_id, question, method, liuyao_data['divination_data'])
            )
            analysis_thread.daemon = True
            analysis_thread.start()

            result = {
                'success': True,
                'liuyao_id': liuyao_id,
                'question': question,
                'method': method,
                'status': 'processing',
                'created_time': datetime.now().isoformat(),
                'message': '六爻占卜已创建并保存到数据库，后台分析正在进行中...'
            }

            print(f"✅ 六爻占卜创建成功，后台任务已启动: {liuyao_id}")
            return jsonify(result)
        else:
            return jsonify({
                'success': False,
                'error': '保存六爻记录失败'
            })

    except Exception as e:
        print(f"❌ 创建六爻占卜失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/liuyao/list')
def list_liuyao_records():
    """获取六爻记录列表"""
    try:
        # 从数据库获取六爻记录
        limit = request.args.get('limit', 50, type=int)
        records = db.get_liuyao_records(limit)

        print(f"📋 获取到 {len(records)} 条六爻记录")

        return jsonify({
            'success': True,
            'records': records,
            'total': len(records)
        })

    except Exception as e:
        print(f"❌ 获取六爻记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/liuyao/<liuyao_id>')
def get_liuyao_result(liuyao_id):
    """获取六爻占卜结果"""
    try:
        # 从数据库获取六爻记录
        record = db.get_liuyao_record(liuyao_id)

        if record:
            result = {
                'success': True,
                'liuyao_id': liuyao_id,
                'question': record.get('question', ''),
                'divination_method': record.get('divination_method', ''),
                'status': record.get('status', 'processing'),
                'analysis_content': record.get('analysis_content', '分析正在进行中...'),
                'created_time': record.get('created_time', ''),
                'completed_time': record.get('completed_time', ''),
                'word_count': record.get('word_count', 0)
            }

            print(f"✅ 获取六爻结果成功: {liuyao_id}")
            return jsonify(result)
        else:
            return jsonify({
                'success': False,
                'error': '六爻记录不存在'
            })

    except Exception as e:
        print(f"❌ 获取六爻结果失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/liuyao/<liuyao_id>', methods=['DELETE'])
def delete_liuyao_record(liuyao_id):
    """删除六爻记录"""
    try:
        success = db.delete_liuyao_record(liuyao_id)

        if success:
            return jsonify({
                'success': True,
                'message': f'六爻记录 {liuyao_id} 删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除六爻记录失败'
            })

    except Exception as e:
        print(f"❌ 删除六爻记录失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/liuyao/<liuyao_id>/status')
def get_liuyao_status(liuyao_id):
    """获取六爻分析状态"""
    try:
        record = db.get_liuyao_record(liuyao_id)

        if record:
            return jsonify({
                'success': True,
                'liuyao_id': liuyao_id,
                'status': record.get('status', 'processing'),
                'word_count': record.get('word_count', 0),
                'created_time': record.get('created_time', ''),
                'completed_time': record.get('completed_time', ''),
                'has_content': bool(record.get('analysis_content'))
            })
        else:
            return jsonify({
                'success': False,
                'error': '六爻记录不存在'
            })

    except Exception as e:
        print(f"❌ 获取六爻状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def run_liuyao_analysis_background(liuyao_id, question, method, divination_data):
    """后台运行六爻分析"""
    try:
        print(f"🔄 开始后台六爻分析: {liuyao_id}")
        print(f"🔮 占卜问题: {question}")
        print(f"📊 起卦方式: {method}")

        # 导入硬币起卦模块
        from coin_divination import CoinDivination

        # 根据起卦方式进行占卜
        if method == 'coins':
            # 硬币起卦
            coin_divination = CoinDivination()
            coin_results = divination_data.get('coin_results', [])

            # 转换前端格式为后端格式
            manual_results = []
            for throw in coin_results:
                if isinstance(throw, list):
                    manual_results.append(''.join(throw))
                else:
                    manual_results.append(throw)

            result = coin_divination.divine_with_coins(question, manual_results)
        elif method == 'numbers':
            # 数字起卦 - 转换为硬币起卦
            numbers = divination_data.get('numbers', [])
            coin_divination = CoinDivination()

            # 根据数字生成硬币结果
            import random
            random.seed(sum(numbers))
            coin_results = []
            for i in range(6):
                throw_result = []
                for j in range(3):
                    throw_result.append(random.choice(['正', '反']))
                coin_results.append(throw_result)

            result = coin_divination.divine_with_coins(question, coin_results)
            result["method"] = "数字起卦"
        else:
            # 时间起卦 - 转换为硬币起卦
            coin_divination = CoinDivination()
            coin_results = divination_data.get('coin_results', [])
            result = coin_divination.divine_with_coins(question, coin_results)
            result["method"] = "时间起卦"

        if not result.get("success"):
            error_msg = f"六爻起卦失败: {result.get('error', '未知错误')}"
            print(f"❌ {error_msg}")
            db.update_liuyao_result(liuyao_id, error_msg, 'failed')
            return

        print(f"✅ 六爻起卦完成")

        # 生成LLM分析
        print(f"🤖 开始生成LLM分析...")
        analysis_content = generate_liuyao_llm_analysis(question, result)

        if not analysis_content:
            error_msg = "LLM分析生成失败"
            print(f"❌ {error_msg}")
            db.update_liuyao_result(liuyao_id, error_msg, 'failed')
            return

        word_count = len(analysis_content)
        print(f"✅ 六爻分析完成，内容长度: {word_count}字")

        # 更新数据库结果
        success = db.update_liuyao_result(liuyao_id, analysis_content, 'completed')

        if success:
            print(f"✅ 六爻分析结果已保存到数据库: {liuyao_id}")
        else:
            print(f"❌ 保存六爻分析结果失败: {liuyao_id}")

    except Exception as e:
        error_msg = f"后台六爻分析异常: {str(e)}"
        print(f"❌ {error_msg}")
        import traceback
        print(f"❌ 详细错误信息: {traceback.format_exc()}")

        # 更新数据库状态为失败
        try:
            db.update_liuyao_result(liuyao_id, error_msg, 'failed')
        except Exception as db_error:
            print(f"❌ 更新数据库状态失败: {db_error}")

def generate_liuyao_llm_analysis(question, divination_result):
    """生成六爻占卜的LLM分析"""
    try:
        # 导入《增删卜易》知识库
        from zengshan_buyi_knowledge import ZengshanBuyiKnowledge
        knowledge_base = ZengshanBuyiKnowledge()

        # 获取传统理论指导
        traditional_knowledge = knowledge_base.format_knowledge_for_llm(question, divination_result.get('hexagram_data', {}))

        # 构建分析提示词
        formatted_output = divination_result.get('formatted_output', '')
        method = divination_result.get('method', '')

        prompt = f"""你是一位经验丰富的六爻占卜师，擅长用通俗易懂的语言为普通人解读卦象。

【用户问题】
{question}

【卦象数据】
{formatted_output}

【分析要求】
请用通俗易懂的语言进行分析，避免过多专业术语，让普通人能够理解：

## 1. 卦象概述
- 简单说明得到了什么卦
- 这个卦的基本含义是什么
- 对您的问题有什么指示

## 2. 详细解读
- 针对您的具体问题进行分析
- 从卦象中看到的信息
- 事情的发展趋势如何

## 3. 时机分析
- 什么时候是好时机
- 需要注意什么时间点
- 耐心等待还是积极行动

## 4. 实用建议
- 具体应该怎么做
- 需要注意什么
- 如何趋吉避凶

## 5. 总结
- 简要总结占卜结果
- 给出明确的建议

【写作要求】
- 语言通俗易懂，像朋友聊天一样
- 避免"用神"、"原神"、"忌神"等专业术语
- 多用"表示"、"显示"、"预示"等词汇
- 给出具体可行的建议
- 保持积极正面的态度
- 总字数1500-2000字

请开始分析："""

        # 调用LLM服务
        from llm_service import LLMService
        llm_service = LLMService()

        print(f"🤖 调用LLM服务生成六爻分析...")
        response = llm_service.generate_response(prompt)

        if response and response.strip():
            # 添加标题和格式化
            analysis_content = f"""# 🔮 六爻占卜分析报告

## 占卜问题
{question}

## 起卦信息
**起卦方式：** {method}

{formatted_output}

---

## 专业分析

{response}

---

*本分析基于《增删卜易》传统六爻理论，仅供参考。具体决策请结合实际情况谨慎考虑。*
"""
            print(f"✅ LLM分析生成成功，长度: {len(analysis_content)}字")
            return analysis_content
        else:
            print(f"❌ LLM服务返回空内容")
            return None

    except Exception as e:
        print(f"❌ LLM分析生成失败: {e}")
        import traceback
        print(f"❌ 详细错误: {traceback.format_exc()}")

        # 返回基础分析作为备用
        return f"""# 🔮 六爻占卜分析报告

## 占卜问题
{question}

## 起卦信息
**起卦方式：** {divination_result.get('method', '')}

{divination_result.get('formatted_output', '')}

## 分析结果

根据您的问题和卦象，现在为您进行基本分析：

### 卦象概述
此卦象反映了当前情况的基本态势和可能的发展方向。

### 问题分析
针对您提出的问题，卦象显示需要仔细观察当前形势，把握时机。

### 建议
1. 保持冷静，理性分析当前情况
2. 注意观察周围环境的变化
3. 在适当的时机采取行动
4. 保持积极的心态面对挑战

### 总结
此次占卜提醒您要谨慎行事，顺应自然规律，相信自己的判断力。

---

*本分析仅供参考，具体决策请结合实际情况。*
"""

if __name__ == '__main__':
    # 确保模板目录存在
    template_dir = os.path.join(current_dir, 'templates')
    static_dir = os.path.join(current_dir, 'static')

    if not os.path.exists(template_dir):
        os.makedirs(template_dir)
    if not os.path.exists(static_dir):
        os.makedirs(static_dir)

    print("🌐 启动独立排盘Web应用")
    print("📍 访问地址: http://localhost:5000")
    print("🔮 功能: 紫薇斗数+八字排盘")

    app.run(debug=True, host='0.0.0.0', port=5000)
