#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的UI交互流程
验证：
1. 不再自动生成12个角度
2. 按需点击生成
3. 异步处理不阻塞界面
4. 生成后自动更新状态
"""

import asyncio

async def test_no_auto_generation():
    """测试不再自动生成12个角度"""
    print("1️⃣ 测试不再自动生成12个角度")
    print("=" * 60)

    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent

        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("no_auto_test")

        # 测试生辰信息
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }

        print(f"📅 创建分析记录: {birth_info}")

        # 调用process_message方法（这应该只生成排盘，不生成12个角度）
        from core.agents.base_agent import AgentMessage, MessageType

        content = {
            "calculation_type": "ziwei",
            "birth_info": birth_info,
            "user_message": "测试排盘生成",
            "session_id": "test_session"
        }

        message = AgentMessage(
            message_id="test_message_001",
            message_type=MessageType.CALCULATION_REQUEST,
            sender_id="test_user",
            receiver_id=calculator_agent.agent_id,
            content=content,
            timestamp=""
        )

        result = await calculator_agent.process_message(message)

        if result.success:
            result_id = result.data.get("result_id")
            print(f"✅ 分析记录创建成功: {result_id}")

            # 检查是否有详细分析
            cached_result = calculator_agent.cache.get_result(result_id)
            if cached_result:
                detailed_analysis = cached_result.detailed_analysis

                if isinstance(detailed_analysis, dict):
                    angle_analyses = detailed_analysis.get("angle_analyses", {})
                    completed_count = len([v for v in angle_analyses.values() if v and len(v) > 100])

                    print(f"📊 已完成的分析角度: {completed_count}/12")

                    if completed_count == 0:
                        print("✅ 成功！没有自动生成12个角度分析")
                        return True, result_id, calculator_agent
                    else:
                        print(f"❌ 失败！自动生成了{completed_count}个角度分析")
                        return False, result_id, calculator_agent
                else:
                    print("✅ 成功！没有详细分析数据")
                    return True, result_id, calculator_agent
            else:
                print("❌ 无法获取缓存结果")
                return False, None, None
        else:
            print(f"❌ 分析记录创建失败: {result.error}")
            return False, None, None

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

async def test_on_demand_generation(result_id, calculator_agent):
    """测试按需生成功能"""
    print(f"\n2️⃣ 测试按需生成功能")
    print("=" * 40)

    try:
        # 使用传入的相同代理实例
        cached_result = calculator_agent.cache.get_result(result_id)

        if not cached_result:
            print("❌ 无法获取缓存结果")
            return False

        # 测试生成单个角度
        print("🎯 测试生成命宫分析...")

        raw_data = cached_result.raw_calculation
        birth_info = cached_result.birth_info

        # 生成单个角度分析
        analysis_result = await calculator_agent._analyze_single_angle(
            "命宫分析",
            "personality_destiny",
            "性格命运核心特征",
            raw_data,
            birth_info,
            "紫薇+八字融合分析"
        )

        if analysis_result and len(analysis_result) > 100:
            print(f"✅ 按需生成成功: {len(analysis_result)}字")

            # 模拟更新缓存
            if not hasattr(cached_result, 'detailed_analysis') or not cached_result.detailed_analysis:
                cached_result.detailed_analysis = {"angle_analyses": {}}
            elif not isinstance(cached_result.detailed_analysis, dict):
                cached_result.detailed_analysis = {"angle_analyses": {}}

            if "angle_analyses" not in cached_result.detailed_analysis:
                cached_result.detailed_analysis["angle_analyses"] = {}

            cached_result.detailed_analysis["angle_analyses"]["personality_destiny"] = analysis_result

            print("✅ 缓存更新成功")
            return True
        else:
            print("❌ 按需生成失败")
            return False

    except Exception as e:
        print(f"❌ 按需生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_functions():
    """测试UI函数是否存在"""
    print(f"\n3️⃣ 测试UI函数")
    print("=" * 40)

    try:
        # 检查关键函数是否存在
        from backend_agent_web import (
            start_async_analysis_generation,
            check_generation_status,
            show_analysis_content_simple
        )

        print("✅ 所有关键UI函数都存在")

        # 检查函数是否可调用
        import inspect

        functions_to_check = [
            ("start_async_analysis_generation", start_async_analysis_generation),
            ("check_generation_status", check_generation_status),
            ("show_analysis_content_simple", show_analysis_content_simple)
        ]

        for func_name, func in functions_to_check:
            if callable(func):
                sig = inspect.signature(func)
                print(f"✅ {func_name}: 可调用，参数: {list(sig.parameters.keys())}")
            else:
                print(f"❌ {func_name}: 不可调用")
                return False

        return True

    except ImportError as e:
        print(f"❌ UI函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ UI函数测试失败: {e}")
        return False

def test_async_processing():
    """测试异步处理机制"""
    print(f"\n4️⃣ 测试异步处理机制")
    print("=" * 40)

    try:
        import threading
        import time

        # 模拟异步任务
        result_container = {"status": "pending"}

        def background_task():
            time.sleep(2)  # 模拟耗时操作
            result_container["status"] = "completed"
            result_container["result"] = "测试结果"

        # 启动后台线程
        thread = threading.Thread(target=background_task, daemon=True)
        start_time = time.time()
        thread.start()

        print("🔄 后台任务已启动...")

        # 检查主线程是否被阻塞
        time.sleep(0.5)  # 短暂等待

        if result_container["status"] == "pending":
            print("✅ 主线程未被阻塞，异步处理正常")

            # 等待任务完成
            thread.join(timeout=5)
            end_time = time.time()

            if result_container["status"] == "completed":
                print(f"✅ 后台任务完成，耗时: {end_time - start_time:.1f}秒")
                return True
            else:
                print("❌ 后台任务未完成")
                return False
        else:
            print("❌ 主线程被阻塞")
            return False

    except Exception as e:
        print(f"❌ 异步处理测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔧 修复后的UI交互流程完整测试")
    print("=" * 70)

    # 1. 测试不再自动生成12个角度
    no_auto_success, result_id, calculator_agent = await test_no_auto_generation()

    # 2. 测试按需生成功能
    on_demand_success = False
    if result_id and calculator_agent:
        on_demand_success = await test_on_demand_generation(result_id, calculator_agent)

    # 3. 测试UI函数
    ui_functions_success = test_ui_functions()

    # 4. 测试异步处理机制
    async_success = test_async_processing()

    print("\n" + "=" * 70)
    print("🎯 修复后的UI流程测试结果总结:")

    if no_auto_success:
        print("✅ 不再自动生成12个角度分析")
    else:
        print("❌ 仍然自动生成12个角度分析")

    if on_demand_success:
        print("✅ 按需生成功能正常")
    else:
        print("❌ 按需生成功能异常")

    if ui_functions_success:
        print("✅ UI函数完整可用")
    else:
        print("❌ UI函数有问题")

    if async_success:
        print("✅ 异步处理机制正常")
    else:
        print("❌ 异步处理机制异常")

    if no_auto_success and on_demand_success and ui_functions_success and async_success:
        print("\n🎉 所有问题修复完成！新的UI交互流程完全正常！")
        print("💡 新功能特点:")
        print("  1. ✅ 排盘完成后不自动生成分析")
        print("  2. ✅ 12个分析按钮按需点击生成")
        print("  3. ✅ 异步处理不阻塞界面")
        print("  4. ✅ 生成状态实时更新")
        print("  5. ✅ 用户体验流畅高效")
        print("\n🚀 现在可以启动Web界面体验完美的交互流程！")
    else:
        print("\n⚠️ 还有功能需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
