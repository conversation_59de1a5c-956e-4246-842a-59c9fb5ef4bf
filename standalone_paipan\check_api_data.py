#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过API检查六爻数据
"""

import requests
import json

def check_api_data():
    """通过API检查数据"""
    
    base_url = "http://localhost:5000"
    
    try:
        # 获取六爻记录列表
        response = requests.get(f"{base_url}/api/liuyao/list", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {result}")
            
            if result.get('success') and result.get('records'):
                record = result['records'][0]
                liuyao_id = record['liuyao_id']
                print(f"找到六爻记录: {liuyao_id}")
                
                # 获取详细结果
                detail_response = requests.get(f"{base_url}/api/liuyao/{liuyao_id}")
                if detail_response.status_code == 200:
                    detail_result = detail_response.json()
                    print(f"详细结果: {detail_result.get('success')}")
                    
                    if detail_result.get('success'):
                        data = detail_result.get('data', {})
                        formatted_output = data.get('formatted_output', '')
                        
                        print(f"格式化输出长度: {len(formatted_output)}")
                        
                        if formatted_output:
                            print(f"\n=== 格式化输出前500字符 ===")
                            print(repr(formatted_output[:500]))
                            
                            print(f"\n=== 查找卦象图形 ===")
                            lines = formatted_output.split('\n')
                            for i, line in enumerate(lines):
                                if '【卦象图形】' in line:
                                    print(f"找到卦象图形在第{i+1}行")
                                    # 显示接下来的10行
                                    for j in range(i, min(i+15, len(lines))):
                                        if lines[j].strip():
                                            print(f"第{j+1}行: {repr(lines[j])}")
                                    break
                            else:
                                print("未找到【卦象图形】")
                        else:
                            print("格式化输出为空")
                    else:
                        print(f"获取详细结果失败: {detail_result}")
                else:
                    print(f"API请求失败: {detail_response.status_code}")
            else:
                print("没有找到六爻记录")
        else:
            print(f"API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    check_api_data()
