#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复后的Web界面集成功能
"""

import asyncio

async def test_web_integration_final():
    """测试修复后的Web界面集成"""
    print("🔧 测试修复后的Web界面集成功能")
    print("=" * 60)
    
    try:
        from backend_agent_web import generate_single_analysis
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建测试缓存结果
        calculator_agent = FortuneCalculatorAgent("final_test")
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1", 
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if not raw_data.get("success"):
            print("❌ 排盘数据生成失败")
            return False
        
        print("✅ 排盘数据生成成功")
        
        # 保存到缓存
        result_id = calculator_agent.cache.save_result(
            user_id="final_test_user",
            session_id="final_test_session",
            calculation_type="ziwei",
            birth_info=birth_info,
            raw_calculation=raw_data,
            detailed_analysis={"angle_analyses": {}},
            summary="最终测试排盘",
            keywords=["紫薇", "八字", "最终测试"],
            confidence=0.9
        )
        
        print(f"📊 缓存结果ID: {result_id}")
        
        # 验证初始状态
        cached_result = calculator_agent.cache.get_result(result_id)
        if not cached_result:
            print("❌ 无法获取缓存结果")
            return False
        
        initial_analyses = cached_result.detailed_analysis.get("angle_analyses", {})
        print(f"📋 初始分析数量: {len(initial_analyses)}")
        
        # 测试Web界面的单个分析生成
        print("\n🎯 测试生成命宫分析...")
        success = generate_single_analysis(result_id, "personality_destiny", "命宫分析 - 性格命运核心特征")
        
        if success:
            print("✅ Web界面单个分析生成成功")
            
            # 验证分析是否保存
            updated_result = calculator_agent.cache.get_result(result_id)
            if updated_result and updated_result.detailed_analysis:
                angle_analyses = updated_result.detailed_analysis.get("angle_analyses", {})
                if "personality_destiny" in angle_analyses:
                    analysis_content = angle_analyses["personality_destiny"]
                    if analysis_content and len(analysis_content) > 100:
                        print(f"✅ 分析内容已保存: {len(analysis_content)}字")
                        print(f"📝 内容预览: {analysis_content[:100]}...")
                        
                        # 测试生成第二个分析
                        print("\n🎯 测试生成财富分析...")
                        success2 = generate_single_analysis(result_id, "wealth_fortune", "财富分析 - 财运状况与理财投资")
                        
                        if success2:
                            print("✅ 第二个分析生成成功")
                            
                            # 验证两个分析都存在
                            final_result = calculator_agent.cache.get_result(result_id)
                            final_analyses = final_result.detailed_analysis.get("angle_analyses", {})
                            
                            if len(final_analyses) >= 2:
                                print(f"✅ 多个分析保存成功: {len(final_analyses)}个分析")
                                for key, content in final_analyses.items():
                                    print(f"  - {key}: {len(content)}字")
                                return True
                            else:
                                print(f"❌ 分析保存不完整: 只有{len(final_analyses)}个分析")
                                return False
                        else:
                            print("❌ 第二个分析生成失败")
                            return False
                    else:
                        print("❌ 分析内容为空或过短")
                        return False
                else:
                    print("❌ 分析结果未保存到缓存")
                    return False
            else:
                print("❌ 无法获取更新后的缓存结果")
                return False
        else:
            print("❌ Web界面单个分析生成失败")
            return False
            
    except Exception as e:
        print(f"❌ Web界面集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ui_flow_complete():
    """测试完整的UI交互流程"""
    print(f"\n🌟 测试完整的UI交互流程")
    print("=" * 40)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 1. 模拟用户输入生辰信息
        print("1️⃣ 模拟用户输入生辰信息...")
        birth_info = {
            "year": "1990",
            "month": "8",
            "day": "15", 
            "hour": "申时",
            "gender": "女"
        }
        print(f"📅 生辰信息: {birth_info}")
        
        # 2. 生成排盘数据（不自动生成12个角度）
        print("\n2️⃣ 生成排盘数据...")
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=15,  # 申时
            gender=birth_info["gender"]
        )
        
        if not raw_data.get("success"):
            print("❌ 排盘生成失败")
            return False
        
        print("✅ 排盘生成成功，未自动生成12个角度分析")
        
        # 3. 保存基础排盘数据
        print("\n3️⃣ 保存基础排盘数据...")
        calculator_agent = FortuneCalculatorAgent("ui_flow_test")
        
        result_id = calculator_agent.cache.save_result(
            user_id="ui_flow_user",
            session_id="ui_flow_session",
            calculation_type="ziwei",
            birth_info=birth_info,
            raw_calculation=raw_data,
            detailed_analysis={"angle_analyses": {}},
            summary="UI流程测试排盘",
            keywords=["紫薇", "八字", "UI测试"],
            confidence=0.9
        )
        
        print(f"✅ 基础数据保存成功: {result_id}")
        
        # 4. 模拟用户按需点击生成分析
        print("\n4️⃣ 模拟用户按需点击生成分析...")
        
        test_angles = [
            ("personality_destiny", "命宫分析 - 性格命运核心特征"),
            ("marriage_love", "婚姻分析 - 感情婚姻与桃花运势"),
            ("career_achievement", "事业分析 - 职业发展与成就潜力")
        ]
        
        generated_count = 0
        for angle_key, angle_name in test_angles:
            print(f"\n🎯 生成 {angle_name.split(' - ')[0]}...")
            
            from backend_agent_web import generate_single_analysis
            success = generate_single_analysis(result_id, angle_key, angle_name)
            
            if success:
                print(f"✅ {angle_name.split(' - ')[0]} 生成成功")
                generated_count += 1
            else:
                print(f"❌ {angle_name.split(' - ')[0]} 生成失败")
        
        # 5. 验证最终结果
        print(f"\n5️⃣ 验证最终结果...")
        final_result = calculator_agent.cache.get_result(result_id)
        if final_result:
            final_analyses = final_result.detailed_analysis.get("angle_analyses", {})
            print(f"📊 最终生成的分析数量: {len(final_analyses)}/{len(test_angles)}")
            
            total_words = sum(len(content) for content in final_analyses.values() if content)
            print(f"📝 总字数: {total_words:,} 字")
            
            if len(final_analyses) == generated_count and generated_count > 0:
                print("✅ UI交互流程完全正常！")
                return True
            else:
                print("❌ UI交互流程有问题")
                return False
        else:
            print("❌ 无法获取最终结果")
            return False
            
    except Exception as e:
        print(f"❌ UI流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🎉 最终修复验证 - Web界面按需生成功能")
    print("=" * 70)
    
    # 1. 测试Web界面集成
    web_success = await test_web_integration_final()
    
    # 2. 测试完整UI流程
    ui_success = await test_ui_flow_complete()
    
    print("\n" + "=" * 70)
    print("🎯 最终修复验证结果:")
    
    if web_success:
        print("✅ Web界面集成功能完全正常")
    else:
        print("❌ Web界面集成功能仍有问题")
    
    if ui_success:
        print("✅ 完整UI交互流程正常")
    else:
        print("❌ 完整UI交互流程异常")
    
    if web_success and ui_success:
        print("\n🎉 🎉 🎉 所有问题修复完成！🎉 🎉 🎉")
        print("💡 新的Web界面功能特点:")
        print("  1. ✅ 排盘完成后不自动生成12个角度分析")
        print("  2. ✅ 12个分析按钮支持按需点击生成")
        print("  3. ✅ 异步处理不阻塞界面操作")
        print("  4. ✅ 生成状态实时更新显示")
        print("  5. ✅ 分析结果正确保存到缓存")
        print("  6. ✅ 支持多个分析同时存在")
        print("  7. ✅ 即时聊天功能基于真实排盘数据")
        print("\n🚀 现在可以启动Web界面享受完美的用户体验！")
        print("   启动命令: streamlit run backend_agent_web.py")
    else:
        print("\n⚠️ 还有功能需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
