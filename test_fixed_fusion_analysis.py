#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的紫薇+八字融合分析
"""

import asyncio
import json

async def test_fixed_fusion_analysis():
    """测试修复后的紫薇+八字融合分析"""
    print("🔮 测试修复后的紫薇+八字融合分析")
    print("=" * 70)
    
    try:
        # 1. 清理缓存
        print("1️⃣ 清理缓存")
        import os
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.endswith('.json'):
                    os.remove(os.path.join(cache_dir, file))
        print("✅ 缓存已清理")
        
        # 2. 测试融合引擎
        print("\n2️⃣ 测试融合引擎")
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        print("✅ 融合分析成功")
        
        # 3. 检查数据完整性
        print("\n3️⃣ 检查数据完整性")
        
        # 检查紫薇数据
        ziwei_analysis = result.get("ziwei_analysis", {})
        has_ziwei = bool(ziwei_analysis and ziwei_analysis.get("palaces"))
        print(f"  紫薇斗数数据: {'✅' if has_ziwei else '❌'}")
        
        if has_ziwei:
            palaces = ziwei_analysis["palaces"]
            print(f"    宫位数量: {len(palaces)}")
            mingong = palaces.get("命宫", {})
            if mingong:
                print(f"    命宫: {mingong.get('position', '')}宫, 主星{mingong.get('major_stars', [])}")
        
        # 检查八字数据
        bazi_analysis = result.get("bazi_analysis", {})
        has_bazi = bool(bazi_analysis and bazi_analysis.get("success"))
        print(f"  八字分析数据: {'✅' if has_bazi else '❌'}")
        
        if has_bazi:
            bazi_info = bazi_analysis.get("bazi_info", {})
            if bazi_info:
                print(f"    八字: {bazi_info.get('chinese_date', '')}")
                print(f"    年柱: {bazi_info.get('year_pillar', '')}")
                print(f"    月柱: {bazi_info.get('month_pillar', '')}")
                print(f"    日柱: {bazi_info.get('day_pillar', '')}")
                print(f"    时柱: {bazi_info.get('hour_pillar', '')}")
            
            analysis = bazi_analysis.get("analysis", {})
            if analysis:
                wuxing = analysis.get("wuxing", {})
                if wuxing and "count" in wuxing:
                    print(f"    五行: {wuxing['count']}")
                
                day_master = analysis.get("day_master", {})
                if day_master:
                    print(f"    日主: {day_master.get('gan', '')}({day_master.get('element', '')})")
        
        # 4. 测试数据处理器
        print("\n4️⃣ 测试数据处理器")
        from core.analysis.data_processor import DataProcessor
        
        processor = DataProcessor()
        analysis_data = processor.extract_analysis_data(result, "personality_destiny")
        
        # 检查提取的数据
        ziwei_extracted = analysis_data.get("ziwei", {})
        bazi_extracted = analysis_data.get("bazi", {})
        
        print(f"  提取的紫薇数据: {'✅' if ziwei_extracted else '❌'}")
        print(f"  提取的八字数据: {'✅' if bazi_extracted else '❌'}")
        
        if bazi_extracted:
            print(f"    八字内容: {list(bazi_extracted.keys())}")
            
            # 检查四柱
            pillars = ["年柱", "月柱", "日柱", "时柱"]
            pillar_count = sum(1 for pillar in pillars if pillar in bazi_extracted and bazi_extracted[pillar])
            print(f"    四柱完整性: {pillar_count}/4")
            
            # 检查五行
            wuxing_extracted = bazi_extracted.get("五行", {})
            print(f"    五行数据: {'✅' if wuxing_extracted else '❌'}")
            
            # 检查日主
            richu_extracted = bazi_extracted.get("日主", {})
            print(f"    日主数据: {'✅' if richu_extracted else '❌'}")
        
        # 5. 测试提示词构建
        print("\n5️⃣ 测试提示词构建")
        from core.analysis.prompt_builder import PromptBuilder
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        prompt_builder = PromptBuilder()
        prompt = prompt_builder.build_analysis_prompt(
            analysis_data, birth_info, "personality_destiny"
        )
        
        print(f"  提示词长度: {len(prompt)} 字符")
        
        # 检查关键内容
        checks = {
            "紫薇斗数": "紫薇斗数" in prompt,
            "八字": "八字" in prompt or "四柱" in prompt,
            "五行": "五行" in prompt,
            "命宫": "命宫" in prompt,
            "日主": "日主" in prompt,
            "相互印证": "印证" in prompt or "融合" in prompt,
            "年柱": any(pillar in prompt for pillar in ["年柱", "月柱", "日柱", "时柱"])
        }
        
        for check_name, result in checks.items():
            print(f"    {check_name}: {'✅' if result else '❌'}")
        
        # 6. 测试完整分析
        print("\n6️⃣ 测试完整分析")
        from core.analysis.analysis_controller import AnalysisController
        
        controller = AnalysisController()
        analysis_result = await controller.execute_single_analysis(
            raw_data=result,
            birth_info=birth_info,
            analysis_type="personality_destiny"
        )
        
        success = analysis_result.get("success", False)
        print(f"  分析执行: {'✅' if success else '❌'}")
        
        if success:
            content = analysis_result.get("content", "")
            print(f"  内容长度: {len(content)} 字符")
            
            # 检查内容质量
            content_checks = {
                "包含紫薇信息": "紫薇" in content or "命宫" in content,
                "包含八字信息": "八字" in content or "日主" in content or "五行" in content,
                "包含融合分析": "印证" in content or "融合" in content or "综合" in content,
                "包含具体数据": "天相" in content or "亥" in content,  # 具体的星曜和宫位
                "包含四柱信息": any(pillar in content for pillar in ["年柱", "月柱", "日柱", "时柱"])
            }
            
            for check_name, result in content_checks.items():
                print(f"    {check_name}: {'✅' if result else '❌'}")
            
            # 保存分析内容
            with open("test_fusion_analysis_content.txt", "w", encoding="utf-8") as f:
                f.write("紫薇+八字融合分析测试结果:\n")
                f.write("=" * 70 + "\n\n")
                f.write(content)
            
            print("  💾 分析内容已保存到 test_fusion_analysis_content.txt")
        
        # 7. 总结
        print("\n7️⃣ 融合分析状态总结")
        
        all_checks = [
            has_ziwei,
            has_bazi,
            bool(ziwei_extracted),
            bool(bazi_extracted),
            checks["紫薇斗数"],
            checks["八字"],
            checks["相互印证"],
            success
        ]
        
        passed_checks = sum(all_checks)
        total_checks = len(all_checks)
        
        print(f"  总体状态: {passed_checks}/{total_checks} 项检查通过")
        
        if passed_checks >= 6:
            print("🎉 紫薇+八字融合分析系统运行良好！")
            print("✅ 数据提取完整")
            print("✅ 提示词包含双重数据")
            print("✅ 分析内容包含相互印证")
            return True
        else:
            print("⚠️ 融合分析系统需要进一步优化")
            print(f"❌ 有{total_checks - passed_checks}项检查未通过")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_fixed_fusion_analysis())
