#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然对话风格测试
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_natural_conversation():
    print('💬 自然对话风格测试...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = 'test_natural_001'
        
        print('\n🎭 测试场景：完整的自然对话流程')
        
        # 场景1：打招呼
        print('\n👋 场景1：用户打招呼')
        result1 = await coordinator.handle_user_message(session_id, '你好')
        print(f'回复: {result1.get("response", "")}')
        
        # 场景2：询问算命但没提供信息
        print('\n🔮 场景2：想算命但没提供信息')
        result2 = await coordinator.handle_user_message(session_id, '我想算命')
        print(f'回复: {result2.get("response", "")}')
        
        # 场景3：提供完整信息
        print('\n📝 场景3：提供完整生辰信息')
        result3 = await coordinator.handle_user_message(
            session_id, 
            '我是1988年6月1日午时出生的男命'
        )
        print(f'回复: {result3.get("response", "")}')
        
        # 等待后台分析启动
        print('\n⏳ 等待5秒让后台分析启动...')
        time.sleep(5)
        
        # 场景4：立即询问财运
        print('\n💰 场景4：立即询问财运')
        result4 = await coordinator.handle_user_message(session_id, '我的财运怎么样？')
        print(f'回复: {result4.get("response", "")}')
        
        # 场景5：询问感情
        print('\n💕 场景5：询问感情')
        result5 = await coordinator.handle_user_message(session_id, '感情方面呢？')
        print(f'回复: {result5.get("response", "")}')
        
        # 场景6：闲聊
        print('\n😊 场景6：闲聊')
        result6 = await coordinator.handle_user_message(session_id, '谢谢你')
        print(f'回复: {result6.get("response", "")}')
        
        print('\n📊 对话风格评估:')
        
        # 检查是否还有AI化表达
        all_responses = [
            result1.get("response", ""),
            result2.get("response", ""),
            result3.get("response", ""),
            result4.get("response", ""),
            result5.get("response", ""),
            result6.get("response", "")
        ]
        
        ai_indicators = ["您", "✅", "❌", "🔮", "💡", "🎯", "📊", "请稍后", "分析中", "小贴士"]
        found_ai_indicators = []
        
        for response in all_responses:
            for indicator in ai_indicators:
                if indicator in response:
                    found_ai_indicators.append(indicator)
        
        if found_ai_indicators:
            print(f'❌ 仍有AI化表达: {set(found_ai_indicators)}')
        else:
            print('✅ 成功去除AI化表达')
        
        # 检查自然度
        natural_indicators = ["你", "我", "挺", "比较", "还", "就是", "这个"]
        found_natural = []
        
        for response in all_responses:
            for indicator in natural_indicators:
                if indicator in response:
                    found_natural.append(indicator)
        
        print(f'✅ 自然表达: {len(set(found_natural))}/7 种口语化词汇')
        
        print('\n🎉 自然对话风格测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_natural_conversation())
