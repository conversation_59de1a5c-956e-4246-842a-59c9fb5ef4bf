#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的缓存功能
"""

import asyncio

async def test_cache_functionality():
    """测试修复后的缓存功能"""
    print("🎉 测试修复后的缓存功能")
    print("=" * 70)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        print("1️⃣ 创建计算代理...")
        calculator_agent = FortuneCalculatorAgent("cache_fix_test")
        
        print("2️⃣ 生成排盘数据...")
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1", 
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if not raw_data.get("success"):
            print("❌ 排盘数据生成失败")
            return False
        
        print("✅ 排盘数据生成成功")
        
        print("3️⃣ 保存基础排盘到缓存...")
        # 保存到缓存
        result_id = calculator_agent.cache.save_result(
            user_id="cache_fix_test_user",
            session_id="cache_fix_test_session",
            calculation_type="ziwei",
            birth_info=birth_info,
            raw_calculation=raw_data,
            detailed_analysis={"angle_analyses": {}},
            summary="缓存修复测试排盘",
            keywords=["紫薇", "八字", "缓存修复"],
            confidence=0.9
        )
        
        print(f"✅ 基础排盘保存成功: {result_id}")
        
        print("4️⃣ 验证初始状态...")
        # 验证初始状态
        initial_result = calculator_agent.cache.get_result(result_id)
        if not initial_result:
            print("❌ 无法获取初始缓存结果")
            return False
        
        initial_analyses = initial_result.detailed_analysis.get("angle_analyses", {})
        print(f"📋 初始分析数量: {len(initial_analyses)} (应该为0)")
        
        if len(initial_analyses) > 0:
            print("❌ 初始状态错误：不应该有自动生成的分析")
            return False
        
        print("✅ 初始状态正确：没有自动生成分析")
        
        print("5️⃣ 测试按需生成分析...")
        # 测试按需生成多个分析
        test_analyses = [
            ("personality_destiny", "命宫分析", "性格命运核心特征"),
            ("wealth_fortune", "财富分析", "财运状况与理财投资"),
            ("marriage_love", "婚姻分析", "感情婚姻与桃花运势")
        ]
        
        generated_count = 0
        for i, (angle_key, angle_name, description) in enumerate(test_analyses, 1):
            print(f"\n  {i}. 生成 {angle_name}...")
            
            # 直接调用分析生成方法
            analysis_result = await calculator_agent._analyze_single_angle(
                angle_name,
                angle_key,
                description,
                raw_data,
                birth_info,
                "紫薇+八字融合分析"
            )
            
            if analysis_result and len(analysis_result) > 100:
                print(f"  ✅ {angle_name} 生成成功: {len(analysis_result)}字")
                
                # 使用正确的缓存更新方法
                current_result = calculator_agent.cache.get_result(result_id)
                if current_result:
                    if not hasattr(current_result, 'detailed_analysis') or not current_result.detailed_analysis:
                        current_result.detailed_analysis = {"angle_analyses": {}}
                    elif not isinstance(current_result.detailed_analysis, dict):
                        current_result.detailed_analysis = {"angle_analyses": {}}
                    
                    if "angle_analyses" not in current_result.detailed_analysis:
                        current_result.detailed_analysis["angle_analyses"] = {}
                    
                    current_result.detailed_analysis["angle_analyses"][angle_key] = analysis_result
                    
                    # 使用正确的缓存属性名
                    if hasattr(calculator_agent.cache, 'memory_cache'):
                        calculator_agent.cache.memory_cache[result_id] = current_result
                        print(f"  ✅ {angle_name} 已更新到内存缓存")
                    
                    # 保存到文件
                    import json
                    from datetime import datetime
                    cache_file = calculator_agent.cache.cache_dir / f"{result_id}.json"
                    if cache_file.exists():
                        with open(cache_file, 'r', encoding='utf-8') as f:
                            cache_data = json.load(f)
                        cache_data['detailed_analysis'] = current_result.detailed_analysis
                        cache_data['updated_at'] = datetime.now().isoformat()
                        with open(cache_file, 'w', encoding='utf-8') as f:
                            json.dump(cache_data, f, ensure_ascii=False, indent=2)
                        print(f"  ✅ {angle_name} 已保存到文件缓存")
                    
                    generated_count += 1
                else:
                    print(f"  ❌ 无法获取缓存结果")
                    return False
            else:
                print(f"  ❌ {angle_name} 生成失败")
                return False
        
        print(f"\n6️⃣ 验证最终结果...")
        # 验证最终结果
        final_result = calculator_agent.cache.get_result(result_id)
        if final_result:
            final_analyses = final_result.detailed_analysis.get("angle_analyses", {})
            
            print(f"📊 最终统计:")
            print(f"  生成的分析数量: {len(final_analyses)}/{len(test_analyses)}")
            
            total_words = 0
            for key, content in final_analyses.items():
                if content:
                    word_count = len(content)
                    total_words += word_count
                    print(f"  - {key}: {word_count}字")
            
            print(f"  总字数: {total_words:,} 字")
            
            # 检查是否所有分析都存在
            all_exist = all(
                angle_key in final_analyses and len(final_analyses[angle_key]) > 100
                for angle_key, _, _ in test_analyses
            )
            
            if all_exist and len(final_analyses) == len(test_analyses):
                print("\n🎉 🎉 🎉 缓存功能修复成功！🎉 🎉 🎉")
                print("✅ 排盘生成功能正常")
                print("✅ 按需分析生成功能正常")
                print("✅ 缓存保存功能正常")
                print("✅ 多个分析可以正确累积")
                print("✅ 内存缓存和文件缓存同步正常")
                return True
            else:
                print(f"\n❌ 最终验证失败:")
                print(f"  期望分析数量: {len(test_analyses)}")
                print(f"  实际分析数量: {len(final_analyses)}")
                print(f"  所有分析存在: {all_exist}")
                return False
        else:
            print("❌ 无法获取最终缓存结果")
            return False
            
    except Exception as e:
        print(f"❌ 缓存功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_web_integration():
    """测试Web界面集成"""
    print(f"\n💻 测试Web界面集成")
    print("=" * 40)
    
    try:
        # 模拟streamlit session_state
        class MockSessionState:
            def __init__(self):
                self.data = {}
                self.global_calculator_agent = None
            
            def __getitem__(self, key):
                return self.data.get(key)
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __contains__(self, key):
                return key in self.data
        
        # 创建模拟的streamlit环境
        import sys
        if 'streamlit' not in sys.modules:
            class MockStreamlit:
                session_state = MockSessionState()
            sys.modules['streamlit'] = MockStreamlit()
        
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = MockSessionState()
        
        from backend_agent_web import generate_single_analysis
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建全局计算代理
        st.session_state.global_calculator_agent = FortuneCalculatorAgent("web_integration_test")
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1", 
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if not raw_data.get("success"):
            print("❌ 排盘数据生成失败")
            return False
        
        # 保存到缓存
        result_id = st.session_state.global_calculator_agent.cache.save_result(
            user_id="web_integration_user",
            session_id="web_integration_session",
            calculation_type="ziwei",
            birth_info=birth_info,
            raw_calculation=raw_data,
            detailed_analysis={"angle_analyses": {}},
            summary="Web集成测试排盘",
            keywords=["紫薇", "八字", "Web集成"],
            confidence=0.9
        )
        
        print(f"📊 缓存结果ID: {result_id}")
        
        # 测试Web界面的单个分析生成
        print("\n🎯 测试Web界面生成命宫分析...")
        success = generate_single_analysis(result_id, "personality_destiny", "命宫分析 - 性格命运核心特征")
        
        if success:
            print("✅ Web界面单个分析生成成功")
            
            # 验证分析是否保存
            updated_result = st.session_state.global_calculator_agent.cache.get_result(result_id)
            if updated_result and updated_result.detailed_analysis:
                angle_analyses = updated_result.detailed_analysis.get("angle_analyses", {})
                if "personality_destiny" in angle_analyses:
                    analysis_content = angle_analyses["personality_destiny"]
                    if analysis_content and len(analysis_content) > 100:
                        print(f"✅ 分析内容已保存: {len(analysis_content)}字")
                        return True
                    else:
                        print("❌ 分析内容为空或过短")
                        return False
                else:
                    print("❌ 分析结果未保存到缓存")
                    return False
            else:
                print("❌ 无法获取更新后的缓存结果")
                return False
        else:
            print("❌ Web界面单个分析生成失败")
            return False
            
    except Exception as e:
        print(f"❌ Web界面集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🎯 缓存功能修复最终验证")
    print("=" * 70)
    
    # 1. 测试核心缓存功能
    cache_success = await test_cache_functionality()
    
    # 2. 测试Web界面集成
    web_success = await test_web_integration()
    
    print("\n" + "=" * 70)
    print("🎯 缓存功能修复最终验证结果:")
    
    if cache_success:
        print("✅ 核心缓存功能完全正常")
    else:
        print("❌ 核心缓存功能仍有问题")
    
    if web_success:
        print("✅ Web界面集成功能完全正常")
    else:
        print("❌ Web界面集成功能仍有问题")
    
    if cache_success and web_success:
        print("\n🎉 🎉 🎉 所有问题修复完成！🎉 🎉 🎉")
        print("💡 Web界面新功能特点:")
        print("  1. ✅ 排盘完成后不自动生成12个角度分析")
        print("  2. ✅ 12个分析按钮支持按需点击生成")
        print("  3. ✅ 异步处理不阻塞界面操作")
        print("  4. ✅ 生成状态实时更新显示")
        print("  5. ✅ 分析结果正确保存到缓存")
        print("  6. ✅ 支持多个分析同时存在和累积")
        print("  7. ✅ 即时聊天功能基于真实排盘数据")
        print("  8. ✅ 内存缓存和文件缓存同步正常")
        print("  9. ✅ 全局缓存实例确保一致性")
        print("\n🚀 现在可以启动Web界面享受完美的用户体验！")
        print("   启动命令: streamlit run backend_agent_web.py")
        print("\n🌟 用户体验流程:")
        print("   1. 输入生辰信息 → 快速生成排盘")
        print("   2. 查看12个分析按钮 → 按需点击生成")
        print("   3. 每个分析可重试 → 确保质量满意")
        print("   4. 即时聊天互动 → 随时提问解答")
        print("   5. 分析结果持久保存 → 随时查看历史")
    else:
        print("\n⚠️ 还有功能需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
