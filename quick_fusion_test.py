#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试融合分析的数据一致性
"""

def test_fusion_bazi_consistency():
    """测试融合分析中紫薇和八字的数据一致性"""
    print("🔮 测试融合分析数据一致性")
    print("=" * 50)
    
    try:
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if result["success"]:
            print("✅ 融合分析成功")
            
            # 获取紫薇数据
            ziwei_analysis = result.get("ziwei_analysis", {})
            ziwei_bazi = ""
            if "birth_info" in ziwei_analysis:
                ziwei_bazi = ziwei_analysis["birth_info"].get("chinese_date", "")
            
            # 获取八字数据
            bazi_analysis = result.get("bazi_analysis", {})
            bazi_bazi = ""
            if "bazi_info" in bazi_analysis:
                bazi_bazi = bazi_analysis["bazi_info"].get("chinese_date", "")
            
            print(f"紫薇中的八字: {ziwei_bazi}")
            print(f"八字算法结果: {bazi_bazi}")
            
            # 检查一致性
            if ziwei_bazi and bazi_bazi:
                if ziwei_bazi == bazi_bazi:
                    print("✅ 数据完全一致！")
                    
                    # 显示其他信息
                    birth_info = result.get("birth_info", {})
                    print(f"\n📊 统一信息:")
                    print(f"  八字: {ziwei_bazi}")
                    print(f"  农历: {birth_info.get('lunar', '')}")
                    print(f"  生肖: {birth_info.get('zodiac', '')}")
                    
                    # 检查交叉验证结果
                    fusion_analysis = result.get("fusion_analysis", {})
                    if "cross_validation" in fusion_analysis:
                        validation = fusion_analysis["cross_validation"]
                        confidence = validation.get("confidence_level", 0)
                        print(f"  一致性得分: {confidence:.2f}")
                    
                    return True
                else:
                    print("❌ 数据不一致")
                    return False
            else:
                print("⚠️ 数据不完整")
                print(f"  紫薇数据: {'有' if ziwei_bazi else '无'}")
                print(f"  八字数据: {'有' if bazi_bazi else '无'}")
                return False
        else:
            print(f"❌ 融合分析失败: {result.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 快速融合分析一致性测试")
    print("=" * 60)
    
    success = test_fusion_bazi_consistency()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 融合分析数据一致性验证成功！")
        print("✅ 紫薇斗数和八字使用统一数据源")
        print("✅ 修复后的八字算法工作正常")
    else:
        print("⚠️ 仍需进一步调整")

if __name__ == "__main__":
    main()
