#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试聊天功能修复效果
"""

import sys
sys.path.append('.')

from core.nlu.llm_client import LLMClient
import json

def test_chat_completion():
    """测试聊天完整性"""
    print("🧪 测试聊天功能修复效果")
    print("=" * 50)
    
    try:
        # 初始化LLM客户端
        llm_client = LLMClient()
        print("✅ LLM客户端初始化成功")
        
        # 测试系统提示词
        system_prompt = """
你是一位专业的命理分析师，现在要基于用户的排盘信息和已生成的专业分析回答问题。

【用户基本信息】
- 出生时间：1988年6月1日 午时
- 性别：女

【排盘信息】
【完整排盘信息】
【八字命理】
八字四柱：戊辰 戊午 甲子 庚午
五行分布：{'木': 1, '火': 3, '土': 3, '金': 1, '水': 1}
日主：甲木
当前大运：己未 (34-44岁)
近期流年：甲辰

【紫薇斗数】
命宫：紫微、天府，辅星：左辅、右弼、天魁
财帛宫：武曲、七杀，辅星：擎羊、陀罗
夫妻宫：天同、太阴，辅星：文昌、文曲
事业宫：廉贞、贪狼，辅星：火星、铃星

【回答要求】
1. 优先参考已生成的专业分析内容回答
2. 结合排盘数据提供具体的命理依据
3. 语言通俗易懂，像朋友聊天一样
4. 回答要具体实用，避免空话套话
5. 如果问题超出现有分析范围，基于排盘数据回答
6. 回答要完整详细，控制在600-1000字之间
7. 确保回答有完整的结尾，不要突然截断
8. 必须提供完整的回答，不能因为长度限制而截断
9. 回答结构要完整：开头-分析-建议-总结

请根据用户的排盘信息和已有分析，专业而亲切地回答问题。回答必须完整，有始有终，不能突然停止。
"""
        
        # 测试问题
        test_question = "你好，我的本命金会怎么样？系统解析到了吗？"
        
        print(f"📝 测试问题: {test_question}")
        print("\n🤖 LLM回复:")
        print("-" * 30)
        
        # 调用LLM
        response = llm_client.chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": test_question}
            ],
            temperature=0.7,
            max_tokens=4096  # 使用修复后的max_tokens
        )
        
        if response:
            print(response)
            print("-" * 30)
            
            # 分析回复质量
            print("\n📊 回复质量分析:")
            print(f"✅ 回复长度: {len(response)} 字符")
            
            # 检查是否完整
            truncation_indicators = [
                response.endswith("..."),
                response.endswith("。。。"),
                response.endswith("，"),
                response.endswith("、"),
                response.endswith("和"),
                response.endswith("或"),
                response.endswith("但"),
                response.endswith("而"),
                len(response) < 200,
            ]
            
            if any(truncation_indicators):
                print("⚠️  可能存在截断")
            else:
                print("✅ 回复看起来完整")
                
            # 检查结构完整性
            has_greeting = any(word in response for word in ["你好", "您好", "朋友"])
            has_analysis = any(word in response for word in ["分析", "看出", "显示"])
            has_suggestion = any(word in response for word in ["建议", "推荐", "可以"])
            has_conclusion = any(word in response for word in ["总的来说", "综合", "总结"])
            
            print(f"✅ 包含问候: {has_greeting}")
            print(f"✅ 包含分析: {has_analysis}")
            print(f"✅ 包含建议: {has_suggestion}")
            print(f"✅ 包含总结: {has_conclusion}")
            
            # 整体评分
            completeness_score = sum([
                not any(truncation_indicators),
                has_greeting,
                has_analysis,
                has_suggestion,
                len(response) >= 400
            ])
            
            print(f"\n🎯 完整性评分: {completeness_score}/5")
            
            if completeness_score >= 4:
                print("🎉 修复效果良好！")
            elif completeness_score >= 3:
                print("⚠️  修复效果一般，可能需要进一步优化")
            else:
                print("❌ 修复效果不佳，需要检查配置")
                
        else:
            print("❌ LLM回复为空")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chat_completion()
