#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇+八字融合分析工具
集成HTML图表生成和增强版分析系统
"""

import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_tool import BaseTool

logger = logging.getLogger(__name__)

class FusionTool(BaseTool):
    """紫薇+八字融合分析工具"""

    def __init__(self, fusion_engine, llm_client, prompt_manager):
        """
        初始化融合工具

        Args:
            fusion_engine: 融合分析引擎
            llm_client: LLM客户端
            prompt_manager: 提示词管理器
        """
        super().__init__("fusion_analysis", "紫薇+八字融合分析")
        self.display_name = "紫薇+八字融合分析"
        self.fusion_engine = fusion_engine
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager

    def can_handle(self, intent: Dict[str, Any]) -> bool:
        """判断是否可以处理该意图"""

        intent_name = intent.get("name", "")
        message = intent.get("message", "")

        # 支持的意图类型
        supported_intents = [
            "fortune_telling",
            "ziwei_analysis",
            "bazi_analysis",
            "chart_analysis",
            "life_analysis"
        ]

        if intent_name in supported_intents:
            return True

        # 关键词匹配
        keywords = [
            "算命", "命盘", "紫薇", "八字", "分析", "运势", "命运",
            "财运", "事业", "婚姻", "健康", "性格", "未来"
        ]

        return any(keyword in message for keyword in keywords)

    def extract_birth_info(self, message: str) -> Optional[Dict[str, Any]]:
        """从消息中提取出生信息"""

        try:
            # 使用LLM提取出生信息
            extraction_prompt = f"""
请从以下用户消息中提取出生信息，如果信息不完整，请返回null。

用户消息：{message}

请以JSON格式返回：
{{
    "year": "年份(4位数字)",
    "month": "月份(1-12)",
    "day": "日期(1-31)",
    "hour": "时辰(0-23或中文时辰)",
    "gender": "性别(男/女)"
}}

如果信息不完整，返回：null
"""

            response = self.llm_client.chat_completion(
                [{"role": "user", "content": extraction_prompt}],
                max_tokens=200
            )

            if response and response.strip() != "null":
                import json
                birth_info = json.loads(response.strip())

                # 验证必要字段
                required_fields = ["year", "month", "day", "hour", "gender"]
                if all(field in birth_info for field in required_fields):
                    # 转换时辰
                    birth_info["hour"] = self._convert_hour(birth_info["hour"])
                    return birth_info

            return None

        except Exception as e:
            logger.error(f"提取出生信息失败: {e}")
            return None

    def _convert_hour(self, hour_input: str) -> int:
        """转换时辰为24小时制"""

        if isinstance(hour_input, int):
            return hour_input

        if hour_input.isdigit():
            return int(hour_input)

        # 中文时辰转换
        time_mapping = {
            "子时": 0, "丑时": 2, "寅时": 4, "卯时": 6,
            "辰时": 8, "巳时": 10, "午时": 12, "未时": 14,
            "申时": 16, "酉时": 18, "戌时": 20, "亥时": 22
        }

        for chinese_time, hour in time_mapping.items():
            if chinese_time in hour_input:
                return hour

        # 默认返回12点（午时）
        return 12

    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行融合分析"""

        try:
            intent_name = intent.get("name", "")
            message = intent.get("message", "")

            logger.info(f"执行紫薇+八字融合分析: {intent_name}")

            # 提取出生信息
            birth_info = self.extract_birth_info(message)

            if not birth_info:
                return {
                    "success": False,
                    "message": "请提供完整的出生信息：年、月、日、时辰、性别。\n\n例如：1988年6月1日午时男",
                    "need_birth_info": True
                }

            # 执行融合分析
            result = self.fusion_engine.calculate_fusion_analysis(
                year=int(birth_info["year"]),
                month=int(birth_info["month"]),
                day=int(birth_info["day"]),
                hour=int(birth_info["hour"]),
                gender=birth_info["gender"]
            )

            if not result.get("success"):
                return {
                    "success": False,
                    "message": f"分析失败：{result.get('error', '未知错误')}",
                    "error": result.get("error")
                }

            # 生成HTML图表
            chart_html = self._generate_html_chart(result)

            # 生成分析文本
            analysis_text = self._generate_analysis_text(result, message)

            return {
                "success": True,
                "message": analysis_text,
                "chart_html": chart_html,
                "birth_info": birth_info,
                "calculation_result": result,
                "tool_name": self.name
            }

        except Exception as e:
            logger.error(f"融合分析执行失败: {e}")
            return {
                "success": False,
                "message": f"分析过程中出现错误：{str(e)}",
                "error": str(e)
            }

    def _generate_html_chart(self, result: Dict[str, Any]) -> str:
        """生成HTML图表"""

        try:
            # 导入HTML图表生成器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

            from generate_html_ziwei import create_html_chart

            # 生成HTML内容
            html_content = create_html_chart(result)

            return html_content

        except Exception as e:
            logger.error(f"HTML图表生成失败: {e}")
            return f"<div style='text-align: center; padding: 20px;'>图表生成失败: {str(e)}</div>"

    def _generate_analysis_text(self, result: Dict[str, Any], user_message: str) -> str:
        """生成分析文本"""

        try:
            birth_info = result.get("birth_info", {})
            ziwei_data = result.get("ziwei_analysis", {})
            bazi_data = result.get("bazi_analysis", {})

            # 构建分析提示词
            analysis_prompt = f"""
作为专业算命大师，请基于紫薇斗数和八字分析结果，为用户提供综合分析。

用户信息：
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}时
- 性别：{birth_info.get('gender')}

紫薇斗数分析：
{ziwei_data}

八字分析：
{bazi_data}

用户问题：{user_message}

请提供：
1. 命盘概述（200字）
2. 性格特质分析（300字）
3. 运势分析（300字）
4. 具体建议（200字）

要求：
- 基于真实计算结果
- 语言通俗易懂
- 包含正面和负面分析
- 提供实用建议
"""

            response = self.llm_client.chat_completion(
                [{"role": "user", "content": analysis_prompt}],
                max_tokens=2000
            )

            if response:
                return f"🔮 **紫薇+八字融合分析**\n\n{response}\n\n---\n💡 *基于紫薇斗数和八字算法双重印证*"
            else:
                return "分析生成失败，请稍后重试。"

        except Exception as e:
            logger.error(f"分析文本生成失败: {e}")
            return f"分析文本生成失败：{str(e)}"

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""

        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": "紫薇斗数+八字算命融合分析，双重算法相互印证",
            "supported_intents": [
                "fortune_telling",
                "ziwei_analysis",
                "bazi_analysis",
                "chart_analysis",
                "life_analysis"
            ],
            "required_info": [
                "出生年份",
                "出生月份",
                "出生日期",
                "出生时辰",
                "性别"
            ],
            "features": [
                "紫薇斗数排盘",
                "八字四柱分析",
                "HTML可视化图表",
                "12角度详细分析",
                "性别差异化分析"
            ]
        }
