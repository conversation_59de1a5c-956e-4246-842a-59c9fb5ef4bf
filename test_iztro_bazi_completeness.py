#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试py-iztro库的八字功能完整性
检查是否能完全替代专门的八字算法
"""

def test_iztro_bazi_features():
    """测试py-iztro的八字相关功能"""
    print("🔍 测试py-iztro的八字功能完整性")
    print("=" * 60)
    
    try:
        import py_iztro
        
        # 创建实例
        astro = py_iztro.Astro()
        
        # 测试数据：1988年6月1日午时男
        astrolabe = astro.by_solar(
            solar_date_str="1988-6-1",
            time_index=6,  # 午时
            gender="男",
            language="zh-CN"
        )
        
        print("✅ py-iztro基础功能正常")
        
        # 1. 基础八字信息
        print(f"\n📊 基础八字信息:")
        print(f"  八字: {astrolabe.chinese_date}")
        print(f"  农历: {astrolabe.lunar_date}")
        print(f"  生肖: {astrolabe.zodiac}")
        print(f"  星座: {astrolabe.sign}")
        
        # 2. 检查是否有详细的八字分析功能
        print(f"\n🔍 检查py-iztro的八字分析能力:")
        
        # 检查可用的属性和方法
        available_attrs = []
        bazi_related_attrs = []
        
        for attr in dir(astrolabe):
            if not attr.startswith('_'):
                available_attrs.append(attr)
                if any(keyword in attr.lower() for keyword in ['bazi', '八字', 'ganzhi', '干支', 'wuxing', '五行']):
                    bazi_related_attrs.append(attr)
        
        print(f"  总可用属性: {len(available_attrs)}个")
        print(f"  八字相关属性: {bazi_related_attrs}")
        
        # 3. 尝试获取更多八字信息
        print(f"\n🎯 尝试获取详细八字信息:")
        
        # 检查是否有五行信息
        try:
            # 尝试访问可能的五行属性
            test_attrs = ['wuxing', 'five_elements', 'elements', 'ganzhi_info', 'bazi_info']
            for attr in test_attrs:
                if hasattr(astrolabe, attr):
                    value = getattr(astrolabe, attr)
                    print(f"  {attr}: {value}")
        except Exception as e:
            print(f"  五行信息获取失败: {e}")
        
        # 4. 检查是否有大运信息
        try:
            if hasattr(astrolabe, 'horoscope') or hasattr(astrolabe, 'fortune'):
                print(f"  可能包含运势信息")
            else:
                print(f"  ⚠️ 未发现运势相关属性")
        except Exception as e:
            print(f"  运势信息检查失败: {e}")
        
        # 5. 检查是否有十神信息
        try:
            if hasattr(astrolabe, 'shishen') or hasattr(astrolabe, 'ten_gods'):
                print(f"  可能包含十神信息")
            else:
                print(f"  ⚠️ 未发现十神相关属性")
        except Exception as e:
            print(f"  十神信息检查失败: {e}")
        
        return astrolabe, available_attrs
        
    except Exception as e:
        print(f"❌ py-iztro测试失败: {e}")
        return None, []

def compare_with_dedicated_bazi():
    """对比专门的八字算法功能"""
    print(f"\n📜 对比专门八字算法的功能")
    print("=" * 60)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        calc = RealBaziCalculator()
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        if not result.get("success"):
            print(f"❌ 专门八字算法失败: {result.get('error')}")
            return
        
        raw_result = result.get("raw_result", {})
        
        print(f"✅ 专门八字算法功能:")
        
        # 检查专门算法提供的功能
        features = []
        
        if "干支" in raw_result:
            features.append("✅ 四柱干支")
            ganzhi = raw_result["干支"]
            print(f"  四柱: {ganzhi.get('文本', '')}")
        
        if "五行" in raw_result:
            features.append("✅ 五行分析")
            wuxing = raw_result["五行"]
            print(f"  五行统计: {len(wuxing)}种")
        
        if "大运" in raw_result:
            features.append("✅ 大运推算")
            dayun = raw_result["大运"]
            print(f"  大运信息: {len(dayun)}步")
        
        if "十神" in raw_result:
            features.append("✅ 十神分析")
        
        if "纳音" in raw_result:
            features.append("✅ 纳音五行")
        
        if "神煞" in raw_result:
            features.append("✅ 神煞推算")
        
        print(f"\n📋 专门算法功能列表:")
        for feature in features:
            print(f"  {feature}")
        
        return features
        
    except Exception as e:
        print(f"❌ 专门八字算法测试失败: {e}")
        return []

def analyze_functionality_gap():
    """分析功能差距"""
    print(f"\n🔍 分析py-iztro与专门八字算法的功能差距")
    print("=" * 60)
    
    # py-iztro提供的功能
    iztro_features = [
        "✅ 准确的四柱八字",
        "✅ 农历转换",
        "✅ 生肖星座",
        "❓ 五行分析（需验证）",
        "❌ 大运推算",
        "❌ 十神分析", 
        "❌ 神煞推算",
        "❌ 纳音五行",
        "❌ 流年分析"
    ]
    
    # 专门八字算法提供的功能
    bazi_features = [
        "❌ 四柱八字（计算错误）",
        "✅ 五行详细分析",
        "✅ 大运推算",
        "✅ 十神分析",
        "✅ 神煞推算", 
        "✅ 纳音五行",
        "✅ 流年分析",
        "✅ 量化分析"
    ]
    
    print("📊 py-iztro功能:")
    for feature in iztro_features:
        print(f"  {feature}")
    
    print("\n📊 专门八字算法功能:")
    for feature in bazi_features:
        print(f"  {feature}")
    
    print(f"\n🎯 结论:")
    print("  py-iztro: 八字计算准确，但分析功能有限")
    print("  专门算法: 分析功能丰富，但八字计算错误")
    print("  最佳方案: 结合两者优势")

def recommend_solution():
    """推荐解决方案"""
    print(f"\n💡 推荐解决方案")
    print("=" * 60)
    
    print("🔧 方案1: 混合使用（推荐）")
    print("  - 使用py-iztro获取准确的四柱八字")
    print("  - 手动实现五行、十神、大运等分析算法")
    print("  - 确保计算准确性的同时提供完整功能")
    
    print("\n🔧 方案2: 修复专门算法")
    print("  - 找到yxf_yixue_py的计算错误并修复")
    print("  - 保持丰富的分析功能")
    print("  - 风险：可能有其他隐藏bug")
    
    print("\n🔧 方案3: 寻找其他库")
    print("  - 寻找其他准确的八字算法库")
    print("  - 可能需要时间调研和测试")
    
    print("\n🎯 建议采用方案1:")
    print("  1. 保留py-iztro作为八字计算核心")
    print("  2. 基于正确八字实现传统分析算法")
    print("  3. 逐步完善八字分析功能")

def main():
    """主函数"""
    print("🧪 py-iztro八字功能完整性评估")
    print("=" * 80)
    
    # 测试py-iztro功能
    astrolabe, attrs = test_iztro_bazi_features()
    
    # 对比专门算法
    bazi_features = compare_with_dedicated_bazi()
    
    # 分析差距
    analyze_functionality_gap()
    
    # 推荐方案
    recommend_solution()
    
    print("\n" + "=" * 80)
    print("🎉 评估完成")
    print("建议：使用py-iztro获取准确八字，手动实现分析算法")

if __name__ == "__main__":
    main()
