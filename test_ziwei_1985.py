#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1985年4月23日亥时女命的紫薇斗数排盘
"""

def test_simple():
    """简单测试 - 不考虑地理位置，单纯用时间"""
    try:
        print("🔍 测试步骤1: 导入py-iztro...")
        import py_iztro
        print("✅ py-iztro导入成功")

        print("🔍 测试步骤2: 创建Astro对象...")
        astro = py_iztro.Astro()
        print("✅ Astro对象创建成功")

        print("🔍 测试步骤3: 排盘计算...")
        print("📅 出生信息: 1985年4月23日亥时女命")
        print("⏰ 亥时对应: 21-23点，时辰序号11")

        # 1985年4月23日亥时女命 - 单纯用时间，不考虑地理位置
        astrolabe = astro.by_solar('1985-4-23', 11, '女')
        print("✅ 排盘成功")

        print("\n📊 基本信息:")
        print(f"📅 农历: {astrolabe.lunar_date}")
        print(f"🎯 八字: {astrolabe.chinese_date}")
        print(f"🐂 生肖: {astrolabe.zodiac}")
        print(f"⭐ 星座: {astrolabe.sign}")
        print(f"🏰 命宫地支: {astrolabe.earthly_branch_of_soul_palace}")
        print(f"🏛️ 身宫地支: {astrolabe.earthly_branch_of_body_palace}")

        print("\n🏰 十二宫详细配置:")
        palace_names = ['命宫', '兄弟宫', '夫妻宫', '子女宫', '财帛宫', '疾厄宫',
                       '迁移宫', '奴仆宫', '官禄宫', '田宅宫', '福德宫', '父母宫']

        # 先按宫位名称显示
        for i, palace in enumerate(astrolabe.palaces):
            if i < len(palace_names):
                palace_name = palace_names[i]
                major_stars = [star.name for star in palace.major_stars] if hasattr(palace, 'major_stars') else []
                body_mark = ' [身宫]' if palace.is_body_palace else ''

                print(f"{palace_name}({palace.earthly_branch}){body_mark}: {major_stars}")

        # 重点检查命宫位置
        print(f"\n🎯 重点检查:")
        print(f"算法结果 - 命宫位置: {astrolabe.earthly_branch_of_soul_palace}")
        print(f"您的排盘图显示命宫应该在: 巳宫")
        print(f"是否匹配: {'✅' if astrolabe.earthly_branch_of_soul_palace == '巳' else '❌'}")

        # 找到真正的命宫
        print(f"\n🔍 查找命宫星曜:")
        for i, palace in enumerate(astrolabe.palaces):
            if palace.earthly_branch == astrolabe.earthly_branch_of_soul_palace:
                palace_name = palace_names[i] if i < len(palace_names) else f"宫位{i}"
                major_stars = [star.name for star in palace.major_stars] if hasattr(palace, 'major_stars') else []
                print(f"真正的命宫: {palace_name}({palace.earthly_branch}) = {major_stars}")

                # 对比排盘图
                print(f"排盘图显示巳宫应该有: 巨门")
                print(f"算法结果巳宫实际有: {major_stars}")
                print(f"星曜匹配: {'✅' if '巨门' in major_stars else '❌'}")
                break

        return True

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔮 测试1985年4月23日亥时女命紫薇斗数排盘")
    print("=" * 50)
    success = test_simple()
    if success:
        print("\n✅ 测试成功！")
    else:
        print("\n❌ 测试失败！")
