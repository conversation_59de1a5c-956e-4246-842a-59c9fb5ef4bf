#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万年历算法实现
"""

import datetime
from typing import Dict, Any

class WannianliApi:
    """万年历API"""
    
    def __init__(self):
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        self.lunar_months = ["正月", "二月", "三月", "四月", "五月", "六月", 
                           "七月", "八月", "九月", "十月", "冬月", "腊月"]
    
    def get_Calendar(self, datetime_obj: datetime.datetime) -> Dict[str, Any]:
        """获取万年历信息"""
        try:
            year = datetime_obj.year
            month = datetime_obj.month
            day = datetime_obj.day
            
            # 简化的农历计算（实际应该使用复杂的农历算法）
            lunar_year = year
            lunar_month = month
            lunar_day = day - 10 if day > 10 else day + 20
            
            # 计算年干支
            year_gan_index = (year - 4) % 10
            year_zhi_index = (year - 4) % 12
            year_ganzhi = self.tiangan[year_gan_index] + self.dizhi[year_zhi_index]
            
            # 计算月干支
            month_gan_index = (year_gan_index * 2 + month) % 10
            month_zhi_index = (month + 1) % 12
            month_ganzhi = self.tiangan[month_gan_index] + self.dizhi[month_zhi_index]
            
            # 计算日干支
            day_gan_index = (year * 365 + month * 30 + day) % 10
            day_zhi_index = (year * 365 + month * 30 + day) % 12
            day_ganzhi = self.tiangan[day_gan_index] + self.dizhi[day_zhi_index]
            
            return {
                "公历": {
                    "年": year,
                    "月": month,
                    "日": day
                },
                "农历": {
                    "年": lunar_year,
                    "月": self.lunar_months[lunar_month - 1],
                    "日": lunar_day
                },
                "干支": {
                    "年": year_ganzhi,
                    "月": month_ganzhi,
                    "日": day_ganzhi
                },
                "节气": self._get_jieqi(month, day),
                "星期": datetime_obj.strftime("%A")
            }
            
        except Exception as e:
            raise Exception(f"万年历计算失败: {e}")
    
    def _get_jieqi(self, month: int, day: int) -> str:
        """获取节气（简化版）"""
        jieqi_map = {
            (2, 4): "立春", (2, 19): "雨水",
            (3, 6): "惊蛰", (3, 21): "春分",
            (4, 5): "清明", (4, 20): "谷雨",
            (5, 6): "立夏", (5, 21): "小满",
            (6, 6): "芒种", (6, 22): "夏至",
            (7, 7): "小暑", (7, 23): "大暑",
            (8, 8): "立秋", (8, 23): "处暑",
            (9, 8): "白露", (9, 23): "秋分",
            (10, 8): "寒露", (10, 24): "霜降",
            (11, 8): "立冬", (11, 22): "小雪",
            (12, 7): "大雪", (12, 22): "冬至",
            (1, 6): "小寒", (1, 20): "大寒"
        }
        
        for (m, d), jieqi in jieqi_map.items():
            if month == m and abs(day - d) <= 2:
                return jieqi
        
        return ""
    
    def print_Calendar(self, datetime_obj: datetime.datetime) -> str:
        """打印万年历信息"""
        calendar_info = self.get_Calendar(datetime_obj)
        
        output = f"""
万年历信息
公历: {calendar_info['公历']['年']}年{calendar_info['公历']['月']}月{calendar_info['公历']['日']}日
农历: {calendar_info['农历']['年']}年{calendar_info['农历']['月']}{calendar_info['农历']['日']}日
干支: {calendar_info['干支']['年']}年 {calendar_info['干支']['月']}月 {calendar_info['干支']['日']}日
节气: {calendar_info['节气'] or '无'}
星期: {calendar_info['星期']}
"""
        return output

def test_wannianli_api():
    """测试万年历API"""
    print("测试万年历算法...")
    
    api = WannianliApi()
    dt = datetime.datetime(1988, 6, 1, 12, 0)
    
    try:
        result = api.get_Calendar(dt)
        print("✅ 万年历计算成功")
        print(api.print_Calendar(dt))
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_wannianli_api()
