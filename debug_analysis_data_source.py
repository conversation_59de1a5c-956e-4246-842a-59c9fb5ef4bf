#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分析内容的数据源问题
"""

def debug_analysis_data_flow():
    """调试分析数据流"""
    print("🔍 调试分析数据流")
    print("=" * 60)
    
    try:
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 1. 测试融合分析
        fusion_engine = ZiweiBaziFusionEngine()
        result = fusion_engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if result["success"]:
            print("✅ 融合分析成功")
            
            # 检查紫薇数据中的八字
            ziwei_analysis = result.get("ziwei_analysis", {})
            if "birth_info" in ziwei_analysis:
                ziwei_bazi = ziwei_analysis["birth_info"].get("chinese_date", "")
                print(f"紫薇数据中的八字: {ziwei_bazi}")
            
            # 检查八字数据
            bazi_analysis = result.get("bazi_analysis", {})
            if "bazi_info" in bazi_analysis:
                bazi_bazi = bazi_analysis["bazi_info"].get("chinese_date", "")
                print(f"八字数据中的八字: {bazi_bazi}")
            
            # 检查原始计算数据
            raw_calculation = result.get("raw_calculation", {})
            print(f"\n📊 原始计算数据结构:")
            print(f"  keys: {list(raw_calculation.keys())}")
            
            if "ziwei" in raw_calculation:
                ziwei_raw = raw_calculation["ziwei"]
                if "data" in ziwei_raw and "birth_info" in ziwei_raw["data"]:
                    raw_ziwei_bazi = ziwei_raw["data"]["birth_info"].get("chinese_date", "")
                    print(f"  原始紫薇八字: {raw_ziwei_bazi}")
            
            if "bazi" in raw_calculation:
                bazi_raw = raw_calculation["bazi"]
                if "data" in bazi_raw and "raw_result" in bazi_raw["data"]:
                    raw_bazi_result = bazi_raw["data"]["raw_result"].get("干支", {}).get("文本", "")
                    print(f"  原始八字结果: {raw_bazi_result}")
            
            return result
        else:
            print(f"❌ 融合分析失败: {result.get('error', '')}")
            return None
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_llm_prompt_data():
    """调试LLM提示词中使用的数据"""
    print("\n🔍 调试LLM提示词数据")
    print("=" * 40)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("debug_calc")
        
        # 模拟分析数据
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        # 获取算法结果
        algorithm_result = calculator_agent._call_comprehensive_api(birth_info)
        
        print(f"算法结果keys: {list(algorithm_result.keys())}")
        
        # 提取算法数据（这是传递给LLM的数据）
        base_data = calculator_agent._extract_algorithm_data(algorithm_result)
        
        print(f"\n📊 传递给LLM的基础数据:")
        print(base_data[:1000] + "..." if len(base_data) > 1000 else base_data)
        
        # 检查数据中的八字信息
        if "戊辰" in base_data:
            print(f"\n✅ 数据中包含正确八字: 戊辰")
        else:
            print(f"\n❌ 数据中不包含正确八字")
            
        if "戊午" in base_data:
            print(f"⚠️ 数据中包含错误八字: 戊午")
        
        return base_data
        
    except Exception as e:
        print(f"❌ LLM数据调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_data_extraction():
    """调试数据提取过程"""
    print("\n🔍 调试数据提取过程")
    print("=" * 40)
    
    try:
        from core.analysis.data_processor import DataProcessor
        
        # 创建数据处理器
        processor = DataProcessor()
        
        # 模拟原始数据
        raw_data = {
            "ziwei": {
                "success": True,
                "data": {
                    "birth_info": {
                        "chinese_date": "戊辰 丁巳 丁亥 丙午"
                    }
                }
            },
            "bazi": {
                "success": True,
                "data": {
                    "raw_result": {
                        "干支": {
                            "文本": "戊辰 丁巳 丁亥 丙午"
                        }
                    }
                }
            }
        }
        
        # 提取八字数据
        bazi_data = processor._extract_bazi_data(raw_data)
        print(f"提取的八字数据: {bazi_data}")
        
        # 提取紫薇数据
        ziwei_data = processor._extract_ziwei_data(raw_data)
        print(f"提取的紫薇数据keys: {list(ziwei_data.keys()) if ziwei_data else 'None'}")
        
        return bazi_data, ziwei_data
        
    except Exception as e:
        print(f"❌ 数据提取调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    print("🔍 分析数据源问题调试")
    print("=" * 70)
    
    # 1. 调试融合分析数据流
    fusion_result = debug_analysis_data_flow()
    
    # 2. 调试LLM提示词数据
    llm_data = debug_llm_prompt_data()
    
    # 3. 调试数据提取过程
    bazi_data, ziwei_data = debug_data_extraction()
    
    print("\n" + "=" * 70)
    print("🎯 问题诊断:")
    
    if fusion_result:
        print("✅ 融合分析数据正常")
    else:
        print("❌ 融合分析数据异常")
    
    if llm_data and "戊辰 丁巳 丁亥 丙午" in llm_data:
        print("✅ LLM接收到正确八字数据")
    elif llm_data and "戊午" in llm_data:
        print("❌ LLM接收到错误八字数据")
    else:
        print("⚠️ LLM数据状态不明")
    
    print("\n💡 可能的问题:")
    print("1. 数据提取过程中使用了错误的数据源")
    print("2. LLM分析时使用了缓存的错误数据")
    print("3. 数据传递过程中发生了数据混淆")
    print("4. 不同模块使用了不同的八字计算结果")

if __name__ == "__main__":
    main()
