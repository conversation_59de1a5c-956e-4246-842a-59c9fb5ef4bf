#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段二：后台Agent独立测试
测试后台Agent的完整工作流程：算法计算 -> 排盘图片 -> 12角度分析 -> 缓存管理
"""

import asyncio
import sys
import time
import os
sys.path.append('.')

async def test_backend_agent_complete_workflow():
    """测试后台Agent完整工作流程"""
    print('🧮 阶段二：后台Agent独立测试')
    print('=' * 50)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import AgentMessage, MessageType
        from datetime import datetime
        
        # 初始化后台Agent
        calculator_agent = FortuneCalculatorAgent()
        
        print('\n🎯 测试场景：后台Agent完整工作流程')
        print('流程：算法计算 -> 排盘图片 -> 12角度分析 -> 缓存管理')
        
        # 准备测试数据
        test_birth_info = {
            'year': '1988',
            'month': '6', 
            'day': '1',
            'hour': '午时',
            'gender': '男'
        }
        
        test_session_id = 'backend_test_001'
        calculation_type = 'ziwei'
        
        print(f'\n📝 测试数据:')
        print(f'  生辰信息: {test_birth_info}')
        print(f'  算命类型: {calculation_type}')
        print(f'  会话ID: {test_session_id}')
        
        # 构建计算请求
        calculation_request = AgentMessage(
            message_id=f"test_{datetime.now().timestamp()}",
            message_type=MessageType.CALCULATION_REQUEST,
            sender_id="test_master",
            receiver_id=calculator_agent.agent_id,
            content={
                "user_message": f"测试计算请求：{test_birth_info}",
                "birth_info": test_birth_info,
                "calculation_type": calculation_type,
                "session_id": test_session_id
            },
            timestamp=datetime.now().isoformat()
        )
        
        print(f'\n🚀 第一步：发送计算请求到后台Agent')
        start_time = time.time()
        
        # 发送请求给后台Agent
        response = await calculator_agent.process_message(calculation_request)
        
        initial_time = time.time() - start_time
        print(f'✅ 后台Agent响应: success={response.success}')
        print(f'⏱️ 初始响应时间: {initial_time:.2f}秒')
        
        if response.success:
            result_id = response.data.get("result_id")
            summary = response.data.get("summary", "")
            from_cache = response.data.get("from_cache", False)
            
            print(f'📊 响应数据:')
            print(f'  result_id: {result_id[:8] if result_id else "None"}...')
            print(f'  from_cache: {from_cache}')
            print(f'  summary长度: {len(summary)}字')
            
            if result_id:
                print(f'\n🔍 第二步：检查缓存结果详情')
                
                # 检查缓存结果
                cached_result = calculator_agent.cache.get_result(result_id)
                if cached_result:
                    print(f'✅ 缓存结果存在')
                    print(f'  算法数据: {"✅" if cached_result.raw_calculation else "❌"}')
                    print(f'  图片路径: {cached_result.chart_image_path or "❌ 无"}')
                    print(f'  详细分析: {"✅" if cached_result.detailed_analysis else "❌"}')
                    
                    # 检查图片文件
                    if cached_result.chart_image_path:
                        if os.path.exists(cached_result.chart_image_path):
                            file_size = os.path.getsize(cached_result.chart_image_path)
                            print(f'  图片文件: ✅ ({file_size} bytes)')
                        else:
                            print(f'  图片文件: ❌ 文件不存在')
                    
                    # 检查12角度分析进度
                    print(f'\n📈 第三步：监控12角度分析进度')
                    
                    # 监控分析进度（最多等待5分钟）
                    max_wait_time = 300  # 5分钟
                    check_interval = 10   # 每10秒检查一次
                    elapsed_time = 0
                    
                    while elapsed_time < max_wait_time:
                        try:
                            # 获取分析进度
                            progress = calculator_agent.get_angle_progress(result_id)
                            completed = progress.get("completed_angles", 0)
                            total = progress.get("total_angles", 12)
                            
                            print(f'⏳ 分析进度: {completed}/{total} ({completed/total*100:.1f}%)')
                            
                            # 检查是否完成
                            if completed >= total:
                                print(f'🎉 12角度分析全部完成！')
                                break
                            elif completed > 0:
                                print(f'🔄 已完成{completed}个角度，继续等待...')
                            
                            # 等待下次检查
                            time.sleep(check_interval)
                            elapsed_time += check_interval
                            
                        except Exception as e:
                            print(f'⚠️ 进度检查异常: {e}')
                            break
                    
                    # 最终状态检查
                    print(f'\n📊 第四步：最终状态检查')
                    
                    # 重新获取缓存结果
                    final_cached_result = calculator_agent.cache.get_result(result_id)
                    if final_cached_result:
                        detailed_analysis = final_cached_result.detailed_analysis
                        
                        if isinstance(detailed_analysis, dict):
                            angle_analyses = detailed_analysis.get("angle_analyses", {})
                            completed_angles = len(angle_analyses)
                            total_word_count = sum(len(content) for content in angle_analyses.values() if content)
                            
                            print(f'✅ 最终分析结果:')
                            print(f'  完成角度: {completed_angles}/12')
                            print(f'  总字数: {total_word_count}')
                            print(f'  图片路径: {final_cached_result.chart_image_path or "无"}')
                            print(f'  最终总结: {len(final_cached_result.summary)}字')
                            
                            # 显示各角度完成情况
                            angle_names = {
                                "personality_destiny": "命宫分析",
                                "wealth_fortune": "财富分析", 
                                "marriage_love": "婚姻分析",
                                "health_wellness": "健康分析",
                                "career_achievement": "事业分析",
                                "children_creativity": "子女分析",
                                "interpersonal_relationship": "人际分析",
                                "education_learning": "学业分析",
                                "family_environment": "家庭分析",
                                "travel_relocation": "迁移分析",
                                "spiritual_blessing": "精神分析",
                                "authority_parents": "权威分析"
                            }
                            
                            print(f'\n📋 各角度完成情况:')
                            for angle_key, angle_name in angle_names.items():
                                if angle_key in angle_analyses and angle_analyses[angle_key]:
                                    word_count = len(angle_analyses[angle_key])
                                    status = "✅" if word_count > 500 else "⚠️"
                                    print(f'  {status} {angle_name}: {word_count}字')
                                else:
                                    print(f'  ❌ {angle_name}: 未完成')
                        
                        # 功能完成度评估
                        print(f'\n🎯 后台Agent功能评估:')
                        
                        checks = [
                            ("算法计算", bool(final_cached_result.raw_calculation)),
                            ("排盘图片", bool(final_cached_result.chart_image_path and os.path.exists(final_cached_result.chart_image_path))),
                            ("12角度分析", completed_angles >= 12),
                            ("缓存管理", bool(final_cached_result)),
                            ("总结生成", len(final_cached_result.summary) > 100),
                            ("字数达标", total_word_count > 10000)
                        ]
                        
                        for check_name, check_result in checks:
                            status = "✅" if check_result else "❌"
                            print(f'  {status} {check_name}')
                        
                        # 计算成功率
                        success_count = sum(1 for _, result in checks if result)
                        total_count = len(checks)
                        success_rate = success_count / total_count * 100
                        
                        print(f'\n🎯 后台Agent完成度: {success_count}/{total_count} ({success_rate:.1f}%)')
                        
                        if success_rate >= 90:
                            print('🎉 后台Agent功能优秀！')
                        elif success_rate >= 70:
                            print('✅ 后台Agent功能良好！')
                        elif success_rate >= 50:
                            print('⚠️ 后台Agent功能一般，需要优化')
                        else:
                            print('❌ 后台Agent功能存在问题，需要修复')
                    
                else:
                    print('❌ 缓存结果不存在')
            else:
                print('❌ 未获取到result_id')
        else:
            print(f'❌ 后台Agent处理失败: {response.error}')
        
        print(f'\n🎉 后台Agent独立测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_backend_agent_complete_workflow())
