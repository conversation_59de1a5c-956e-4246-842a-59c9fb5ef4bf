#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理器 - 统一管理所有工具的提示词，支持自动切换
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class PromptManager:
    """提示词管理器 - 根据工具和任务类型自动选择最佳提示词"""
    
    def __init__(self):
        """初始化提示词管理器"""
        self.prompts = {}
        self._load_all_prompts()
        
        logger.info("提示词管理器初始化完成")
    
    def _load_all_prompts(self):
        """加载所有提示词"""
        
        # 加载紫薇斗数提示词
        self._load_ziwei_prompts()
        
        # 加载八字算命提示词
        self._load_bazi_prompts()
        
        # 加载六爻算卦提示词
        self._load_liuyao_prompts()
        
        # 加载意图识别提示词
        self._load_intent_prompts()
    
    def _load_ziwei_prompts(self):
        """加载紫薇斗数提示词"""
        
        self.prompts["ziwei"] = {
            "analysis": {
                "base": """你是专业的紫薇斗数大师，请基于以下真实排盘数据进行专业分析。

【排盘数据】
{chart_data}

【出生信息】
{birth_info}

【分析要求】
1. 必须基于真实排盘数据，不得编造信息
2. 分析要深入专业，体现紫薇斗数的精髓
3. 包含命宫、事业、财运、感情四个角度
4. 提供具体的建议和时间节点
5. 字数要求：详细版2000-3000字，简洁版300-500字

【输出格式】
请直接输出流畅的中文分析，不要使用JSON格式。

请开始专业的紫薇斗数分析：""",
                
                "career": """请重点分析事业宫、官禄宫的星曜配置，包括：
- 主星特质对职业选择的影响
- 辅星组合对事业发展的助力
- 化权、化科对升职的影响
- 流年事业运势的变化趋势""",
                
                "love": """请重点分析夫妻宫、桃花星的情况，包括：
- 配偶星的特质和外貌特征
- 感情发展的时间节点
- 婚姻关系的和谐程度
- 桃花运势的起伏变化""",
                
                "wealth": """请重点分析财帛宫、禄存星的配置，包括：
- 财星的强弱和财运基础
- 正财偏财的获得方式
- 投资理财的适宜方向
- 财运的流年变化趋势""",
                
                "health": """请重点分析疾厄宫、化忌星的影响，包括：
- 先天体质的强弱特点
- 容易出现的健康问题
- 养生保健的注意事项
- 健康运势的周期变化"""
            }
        }
    
    def _load_bazi_prompts(self):
        """加载八字算命提示词"""
        
        self.prompts["bazi"] = {
            "analysis": {
                "base": """你是专业的八字命理大师，请基于以下真实八字数据进行专业分析。

【八字数据】
{chart_data}

【出生信息】
{birth_info}

【分析要求】
1. 必须基于真实八字数据，不得编造信息
2. 分析要深入专业，体现八字命理的精髓
3. 包含日主强弱、十神关系、大运流年分析
4. 提供具体的建议和时间节点
5. 字数要求：详细版2000-3000字，简洁版300-500字

【输出格式】
请直接输出流畅的中文分析，不要使用JSON格式。

请开始专业的八字命理分析：""",
                
                "career": """请重点分析官杀星、印星对事业的影响，包括：
- 官星的有无和强弱
- 印星对学历和权威的影响
- 食伤生财的事业发展模式
- 大运对事业的推动作用""",
                
                "love": """请重点分析配偶星、桃花的情况，包括：
- 配偶星的特质和配偶条件
- 桃花的旺衰和感情机遇
- 婚姻宫的稳定程度
- 感情发展的大运流年""",
                
                "wealth": """请重点分析财星、食伤生财的配置，包括：
- 正财偏财的强弱配置
- 食伤生财的能力强弱
- 财运的大运流年变化
- 投资理财的适宜时机""",
                
                "health": """请重点分析日主强弱、五行平衡，包括：
- 日主的旺衰程度
- 五行的平衡状况
- 容易出现的健康问题
- 养生调理的五行方法"""
            }
        }
    
    def _load_liuyao_prompts(self):
        """加载六爻算卦提示词"""
        
        self.prompts["liuyao"] = {
            "analysis": {
                "base": """你是专业的六爻算卦大师，请基于以下真实卦象数据进行专业分析。

【卦象数据】
{chart_data}

【用户问题】
{user_question}

【分析要求】
1. 必须基于真实卦象数据，不得编造信息
2. 分析要深入专业，体现六爻算卦的精髓
3. 包含卦象解读、动爻分析、世应关系
4. 提供具体的建议和时间节点
5. 字数要求：详细版1500-2500字，简洁版300-500字

【输出格式】
请直接输出流畅的中文分析，不要使用JSON格式。

请开始专业的六爻卦象分析：""",
                
                "career": """请重点分析官鬼爻、父母爻对事业的影响，包括：
- 官鬼爻的旺衰和职位运势
- 父母爻对工作环境的影响
- 动爻对事业变化的预示
- 世应关系对人际的影响""",
                
                "love": """请重点分析世应关系、桃花爻的情况，包括：
- 世应的生克关系
- 妻财爻的旺衰情况
- 桃花爻的动静变化
- 感情发展的时间预测""",
                
                "wealth": """请重点分析妻财爻、生克关系，包括：
- 妻财爻的强弱配置
- 子孙爻生财的情况
- 动爻对财运的影响
- 求财的最佳时机""",
                
                "general": """请进行全面的六爻卦象分析，包括：
- 卦象的基本含义
- 动爻的变化趋势
- 世应的关系分析
- 整体的吉凶判断"""
            }
        }
    
    def _load_intent_prompts(self):
        """加载意图识别提示词"""
        
        self.prompts["intent"] = {
            "recognition": """请分析用户消息并识别意图，返回JSON格式结果。

【用户消息】
{message}

【对话上下文】
{context}

【工具类型说明】
- ziwei: 紫薇斗数 (关键词: 紫薇、斗数、命宫、星曜、排盘)
- bazi: 八字算命 (关键词: 八字、四柱、天干、地支、合婚)
- liuyao: 六爻算卦 (关键词: 算卦、占卜、起卦、卦象、六爻)
- comprehensive: 综合分析 (关键词: 运势、命运、算命、综合)
- general: 一般对话 (问候、闲聊、咨询等)

【问题类型说明】
- career: 事业工作 (关键词: 事业、工作、职业、升职)
- love: 感情婚姻 (关键词: 感情、婚姻、恋爱、配偶)
- wealth: 财运投资 (关键词: 财运、财富、投资、赚钱)
- health: 健康身体 (关键词: 健康、身体、疾病)
- fortune: 运势流年 (关键词: 运势、今年、明年、流年)
- general: 综合分析 (其他或不明确)

请返回JSON格式：
{{
  "tool_name": "工具名称",
  "question_type": "问题类型", 
  "confidence": 0.95,
  "reasoning": "识别理由"
}}"""
        }
    
    def get_ziwei_analysis_prompt(self, algorithm_result: Dict[str, Any], 
                                 question_type: str, birth_info: Dict[str, Any],
                                 context: Dict[str, Any]) -> str:
        """获取紫薇斗数分析提示词"""
        
        base_prompt = self.prompts["ziwei"]["analysis"]["base"]
        specific_prompt = self.prompts["ziwei"]["analysis"].get(question_type, "")
        
        # 格式化提示词
        chart_data = self._format_chart_data(algorithm_result)
        birth_info_str = self._format_birth_info(birth_info)
        
        prompt = base_prompt.format(
            chart_data=chart_data,
            birth_info=birth_info_str
        )
        
        if specific_prompt:
            prompt += f"\n\n【重点分析方向】\n{specific_prompt}"
        
        return prompt
    
    def get_bazi_analysis_prompt(self, algorithm_result: Dict[str, Any], 
                                question_type: str, birth_info: Dict[str, Any],
                                context: Dict[str, Any]) -> str:
        """获取八字算命分析提示词"""
        
        base_prompt = self.prompts["bazi"]["analysis"]["base"]
        specific_prompt = self.prompts["bazi"]["analysis"].get(question_type, "")
        
        # 格式化提示词
        chart_data = self._format_chart_data(algorithm_result)
        birth_info_str = self._format_birth_info(birth_info)
        
        prompt = base_prompt.format(
            chart_data=chart_data,
            birth_info=birth_info_str
        )
        
        if specific_prompt:
            prompt += f"\n\n【重点分析方向】\n{specific_prompt}"
        
        return prompt
    
    def get_liuyao_analysis_prompt(self, algorithm_result: Dict[str, Any], 
                                  question_type: str, user_question: str,
                                  context: Dict[str, Any]) -> str:
        """获取六爻算卦分析提示词"""
        
        base_prompt = self.prompts["liuyao"]["analysis"]["base"]
        specific_prompt = self.prompts["liuyao"]["analysis"].get(question_type, "")
        
        # 格式化提示词
        chart_data = self._format_chart_data(algorithm_result)
        
        prompt = base_prompt.format(
            chart_data=chart_data,
            user_question=user_question
        )
        
        if specific_prompt:
            prompt += f"\n\n【重点分析方向】\n{specific_prompt}"
        
        return prompt
    
    def get_intent_recognition_prompt(self, message: str, context: Dict[str, Any]) -> str:
        """获取意图识别提示词"""
        
        prompt_template = self.prompts["intent"]["recognition"]
        
        # 格式化上下文
        context_str = self._format_context(context)
        
        return prompt_template.format(
            message=message,
            context=context_str
        )
    
    def _format_chart_data(self, algorithm_result: Dict[str, Any]) -> str:
        """格式化排盘数据"""
        
        data = algorithm_result.get("data", {})
        if not data:
            return "排盘数据为空"
        
        # 简化数据展示，只显示关键信息
        formatted_data = []
        
        for key, value in data.items():
            if isinstance(value, dict):
                formatted_data.append(f"{key}: {str(value)[:200]}...")
            else:
                formatted_data.append(f"{key}: {value}")
        
        return "\n".join(formatted_data[:10])  # 限制长度
    
    def _format_birth_info(self, birth_info: Dict[str, Any]) -> str:
        """格式化出生信息"""
        
        return f"""出生时间: {birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日{birth_info.get('hour')}时
性别: {birth_info.get('gender', '未知')}"""
    
    def _format_context(self, context: Dict[str, Any]) -> str:
        """格式化上下文信息"""
        
        context_parts = []
        
        if context.get("recent_history"):
            context_parts.append(f"历史消息数: {len(context['recent_history'])}")
        
        if context.get("last_analysis_type"):
            context_parts.append(f"上次分析类型: {context['last_analysis_type']}")
        
        if context.get("tools_used"):
            context_parts.append(f"已使用工具: {', '.join(context['tools_used'])}")
        
        return "\n".join(context_parts) if context_parts else "无特殊上下文"
