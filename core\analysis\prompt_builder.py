#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词构建器
负责构建严格、准确的LLM提示词
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class PromptBuilder:
    """提示词构建器"""

    def __init__(self):
        """初始化提示词构建器"""
        self.base_constraints = [
            "严格按照提供的计算数据进行分析",
            "禁止编造任何不在数据中的星曜配置",
            "禁止使用不在数据中的宫位信息",
            "必须准确引用计算结果中的具体数据",
            "如果数据不足，明确说明而不是编造"
        ]

    def build_analysis_prompt(self, analysis_data: Dict[str, Any],
                            birth_info: Dict[str, Any],
                            analysis_type: str) -> str:
        """构建分析提示词"""
        try:
            logger.info(f"🔨 构建{analysis_type}分析提示词")

            # 检查是否为合盘分析
            if analysis_data.get("data_source") == "compatibility_analysis":
                return self._build_compatibility_prompt(analysis_data, birth_info, analysis_type)

            # 构建提示词各部分
            header = self._build_header(analysis_type)
            constraints = self._build_constraints()
            user_info = self._build_user_info(birth_info)
            data_section = self._build_data_section(analysis_data)
            requirements = self._build_requirements(analysis_type)

            # 组合完整提示词
            prompt = f"""
{header}

{constraints}

{user_info}

{data_section}

{requirements}
"""

            logger.info(f"✅ {analysis_type}提示词构建完成，长度: {len(prompt)}字符")
            return prompt.strip()

        except Exception as e:
            logger.error(f"构建{analysis_type}提示词失败: {e}")
            return self._build_fallback_prompt(analysis_type)

    def _build_header(self, analysis_type: str) -> str:
        """构建提示词头部"""
        type_names = {
            "personality_destiny": "性格命运",
            "wealth_fortune": "财运分析",
            "marriage_love": "感情婚姻",
            "health_wellness": "健康状况",
            "career_development": "事业发展",
            "family_relationship": "家庭关系",
            "children_education": "子女运势",
            "social_network": "人际关系",
            "living_environment": "居住环境",
            "spiritual_growth": "精神修养",
            "travel_migration": "出行迁移",
            "sibling_friendship": "兄弟朋友"
        }

        analysis_name = type_names.get(analysis_type, analysis_type)

        return f"""
你是一位专业的命理分析师，请为用户分析【{analysis_name}】方面的情况。

【分析要求】
- 用通俗易懂的语言，避免过多专业术语
- 语言自然流畅，像跟朋友交流一样
- 紫薇斗数和八字要相互印证，说明原因
- 客观分析，既说优点也说需要注意的地方
"""

    def _build_constraints(self) -> str:
        """构建约束条件"""
        constraints_text = "【分析要求】\n"
        constraints_text += "1. 严格基于真实的排盘数据进行分析，不得编造\n"
        constraints_text += "2. 用通俗易懂的语言，避免过多专业术语\n"
        constraints_text += "3. 紫薇斗数和八字要相互印证，说明依据\n"
        constraints_text += "4. 客观分析，既说优势也说不足\n"
        constraints_text += "5. 提供实用的建议和指导\n"

        return constraints_text

    def _build_user_info(self, birth_info: Dict[str, Any]) -> str:
        """构建用户信息"""
        # 安全地计算年龄
        try:
            birth_year = birth_info.get('year', 2000)
            if isinstance(birth_year, str):
                birth_year = int(birth_year)
            current_age = 2024 - birth_year
        except (ValueError, TypeError):
            current_age = "未知"

        return f"""
【这个人的基本情况】
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}点
- 性别：{birth_info.get('gender')}
- 现在年龄：{current_age}岁左右
"""

    def _build_data_section(self, analysis_data: Dict[str, Any]) -> str:
        """构建数据部分"""
        data_text = "【排盘结果】\n"

        # 添加紫薇斗数数据
        ziwei_data = analysis_data.get("ziwei", {})
        if ziwei_data:
            data_text += self._build_ziwei_section(ziwei_data)

        # 添加八字数据
        bazi_data = analysis_data.get("bazi", {})
        if bazi_data:
            data_text += self._build_bazi_section(bazi_data)

        return data_text

    def _build_ziwei_section(self, ziwei_data: Dict[str, Any]) -> str:
        """构建紫薇斗数数据部分"""
        ziwei_text = "\n🌟 紫薇斗数排盘：\n"

        palaces = ziwei_data.get("palaces", {})
        # 重点显示几个主要宫位
        important_palaces = ["命宫", "财帛宫", "夫妻宫", "事业宫", "健康宫"]

        for palace_name in important_palaces:
            if palace_name in palaces:
                palace_info = palaces[palace_name]
                position = palace_info.get("位置", "")
                major_stars = palace_info.get("主星", [])

                if major_stars:
                    stars_str = "、".join(major_stars[:2])  # 只显示前2个主星
                    ziwei_text += f"  {palace_name}：{stars_str}星坐守\n"

        return ziwei_text

    def _build_bazi_section(self, bazi_data: Dict[str, Any]) -> str:
        """构建八字数据部分"""
        bazi_text = "\n🔮 八字排盘：\n"

        # 四柱信息
        pillars = ["年柱", "月柱", "日柱", "时柱"]
        pillar_values = []
        for pillar in pillars:
            if pillar in bazi_data:
                pillar_values.append(bazi_data[pillar])

        # 添加完整八字
        if len(pillar_values) == 4:
            complete_bazi = " ".join(pillar_values)
            bazi_text += f"  八字：{complete_bazi}\n"

        # 五行分析（简化版）
        wuxing = bazi_data.get("五行", {})
        if wuxing:
            bazi_text += "  五行情况：\n"
            strong_elements = []
            weak_elements = []

            for element, info in wuxing.items():
                if isinstance(info, dict) and "数量" in info:
                    count = info['数量']
                    if count >= 3:
                        strong_elements.append(f"{element}很旺")
                    elif count <= 1:
                        weak_elements.append(f"{element}偏弱")

            if strong_elements:
                bazi_text += f"    - {', '.join(strong_elements)}\n"
            if weak_elements:
                bazi_text += f"    - {', '.join(weak_elements)}\n"

        # 日主信息（简化）
        rizhu = bazi_data.get("日主", {})
        if rizhu:
            wuxing_attr = rizhu.get("五行", "")
            strength = rizhu.get("强弱", "")
            if wuxing_attr:
                bazi_text += f"  命主属{wuxing_attr}，整体{strength}\n"

        return bazi_text

    def _build_requirements(self, analysis_type: str) -> str:
        """构建分析要求"""
        base_requirements = """
【分析方法】
请用通俗易懂的语言，为用户提供专业的命理分析：

📝 【分析方式】
- 语言通俗易懂，避免过多专业术语
- 紫薇斗数和八字相互印证，说明分析依据
- 客观分析，既说优势也说不足
- 提供实用的建议和指导

🔮 【分析步骤】
1. 紫薇斗数分析
   - 分析命盘中主要星曜的配置和特点
   - 说明这些星曜组合的含义
   - 分析大运流年的影响

2. 八字命理分析
   - 分析八字的五行配置和强弱
   - 说明对性格和运势的影响
   - 分析大运流年的变化

3. 融合印证
   - 对比两种方法的分析结果
   - 找出一致性和互补性
   - 综合得出更准确的结论

4. 问题分析
   - 客观指出可能存在的问题
   - 分析可能面临的挑战
   - 说明问题的原因和影响

5. 建议指导
   - 提供针对性的改善建议
   - 给出具体的实施方法
   - 提供预防措施和注意事项

【输出要求】
- 内容详细，至少3000字
- 语言自然流畅，通俗易懂
- 分析客观平衡，有理有据
- 建议实用可行，具体明确
- 预测具体到年份和情况
- 专注当前分析角度

请开始分析：
"""

        # 根据分析类型添加特定要求
        specific_requirements = self._get_specific_requirements(analysis_type)
        if specific_requirements:
            base_requirements = base_requirements.replace("请开始分析：",
                                                        f"{specific_requirements}\n\n请开始分析：")

        return base_requirements

    def _get_specific_requirements(self, analysis_type: str) -> str:
        """获取特定分析类型的要求"""
        specific_reqs = {
            "personality_destiny": """
【性格命运分析重点】
- 重点说说这个人的性格特点、天赋能力、人生格局
- 要客观地说，既说优点也说缺点，不要只说好听的
- 分析一下性格上可能存在的问题和盲点
- 看看命格有什么特殊的地方，是好格局还是有缺陷
- 说说性格缺点可能带来什么麻烦，怎么改善
- 预测一下人生的关键转折点，什么时候要特别注意
- 内容要详细，至少3000字，好坏都要说到
""",
            "wealth_fortune": """
【财运分析重点】
- 重点分析财运、赚钱能力、投资理财方面
- 男性：什么时候能发财？赚钱能力如何？但也要说破财风险
- 女性：有没有富贵命？能不能嫁得好？但也要说经济困难的可能
- 客观分析财运的优势和劣势，不要只报喜不报忧
- 说说可能的破财年份和投资风险
- 分析经济困难期怎么应对
- 给出具体的理财建议和注意事项
- 内容要详细，至少3000字，好坏财运都要分析
""",
            "marriage_love": """
【感情婚姻分析重点】
- 重点分析感情、婚姻、配偶方面的情况
- 女性：能不能嫁得好？但也要说感情风险和婚姻问题
- 男性：什么时候结婚？感情运如何？但也要说单身和婚姻失败的可能
- 客观分析感情优势和劣势，不要只说好的
- 说说可能的感情挫折、桃花劫、第三者问题
- 分析婚姻中可能遇到的困难和解决办法
- 预测感情的关键时期和需要注意的年份
- 内容要详细，至少3000字，好坏感情都要分析
""",
            "health_wellness": """
【健康状况分析重点】
- 重点分析健康状况、体质特点、养生方法
- 客观分析身体的强项和弱点，不要回避健康问题
- 说说容易得什么病，什么时候要特别注意身体
- 分析体质缺陷和先天的健康隐患
- 预测可能的健康危机年份和疾病风险
- 给出具体的养生建议和预防措施
- 包括饮食、运动、作息等方面的指导
- 内容要详细，至少3000字，健康风险要重点分析
""",
            "career_achievement": """
【事业成就专项要求】
- 仅分析事业发展、职业成就、升职加薪，严禁涉及财运、婚姻、健康等其他角度
- 男性重点：能不能当官？当多大的官？什么时候提拔？仕途发展如何？
- 女性重点：事业成就如何？能不能做女强人？职场发展前景？
- 儿童重点：长大后事业成就如何？适合什么职业？有没有当官命？
- 详细分析官禄宫配置，预测职业类型、升职年份、事业高峰期
- 分析权力运势、领导能力、事业转折点
- 字数要求：5000字以上，要有具体的升职年份和职位预测
""",
            "children_creativity": """
【子女创造专项要求】
- 仅分析子女运势、生育能力、创造才华，严禁涉及财运、婚姻、事业等其他角度
- 成人重点：什么时候生孩子？能生几个？子女是否优秀？子女对自己的帮助？
- 儿童重点：是不是学霸？聪明程度如何？有什么特殊才华？创造力如何？
- 详细分析子女宫配置，预测生育年龄、子女数量、子女成就
- 分析创造才华、艺术天赋、学习能力、智商水平
- 字数要求：5000字以上，要有具体的生育时间和子女特征预测
""",
            "interpersonal_relationship": """
【人际关系专项要求】
- 仅分析人际交往、朋友关系、社交能力，严禁涉及财运、婚姻、事业等其他角度
- 深度分析奴仆宫配置，预测朋友质量、贵人运势、小人风险
- 分析社交能力、人缘好坏、领导魅力、团队合作能力
- 预测重要贵人出现年份、小人陷害时期、人际关系转折点
- 分析下属运势、合作伙伴关系、社会声望
- 字数要求：5000字以上，要有具体的贵人和小人预测
""",
            "education_learning": """
【学业教育专项要求】
- 仅分析学习能力、教育成就、知识发展，严禁涉及财运、婚姻、事业等其他角度
- 儿童重点：智商高不高？学习成绩好不好？有没有学霸潜质？记忆力如何？考试运如何？
- 成人重点：学习新知识的能力？继续教育机会？专业技能提升？学历发展？
- 详细分析文昌文曲配置，预测学习天赋、考试运势、教育成就
- 分析记忆力、理解力、创新思维、学术研究能力
- 字数要求：5000字以上，要有具体的学习能力评估和教育建议
""",
            "family_environment": """
【家庭环境专项要求】
- 仅分析家庭环境、房产田宅、居住运势，严禁涉及财运、婚姻、事业等其他角度
- 深度分析田宅宫配置，预测房产运势、居住环境、家庭和睦程度
- 分析购房时机、房产投资、搬家运势、家居风水
- 预测家庭变故、房产纠纷、居住环境变化
- 分析家族运势、祖业传承、家庭责任
- 字数要求：5000字以上，要有具体的房产和家庭环境预测
""",
            "travel_relocation": """
【迁移出行专项要求】
- 仅分析外出发展、搬迁运势、旅行机遇，严禁涉及财运、婚姻、事业等其他角度
- 深度分析迁移宫配置，预测适合发展的地区、搬家时机、外出运势
- 分析出国机会、异地发展、旅行运势、居住环境变化
- 预测重大搬迁年份、出国时机、旅行中的机遇和风险
- 分析地理风水对命运的影响、最佳居住方位
- 字数要求：5000字以上，要有具体的搬迁时间和地点建议
""",
            "spiritual_blessing": """
【精神福德专项要求】
- 仅分析精神境界、福德修养、心灵成长，严禁涉及财运、婚姻、事业等其他角度
- 深度分析福德宫配置，预测精神状态、心理健康、修养水平
- 分析宗教缘分、哲学思辨、精神追求、心灵成长
- 预测精神危机、心理调节、修行机缘、福德积累
- 分析慈善公益、道德品质、精神财富、内心平静
- 字数要求：5000字以上，要有具体的精神发展和修养建议
""",
            "authority_parents": """
【权威父母专项要求】
- 仅分析父母关系、权威地位、长辈缘分，严禁涉及财运、婚姻、事业等其他角度
- 儿童重点：孝顺程度如何？与父母关系好不好？父母对自己的影响？
- 成人重点：父母健康如何？权威地位？领导能力？与长辈的关系？
- 详细分析父母宫配置，预测父母健康、亲情深浅、孝顺表现
- 分析权威运势、领导魅力、上级关系、传承运势
- 字数要求：5000字以上，要有具体的父母关系和权威地位预测
"""
        }

        return specific_reqs.get(analysis_type, "")

    def _build_compatibility_prompt(self, analysis_data: Dict[str, Any], birth_info: Dict[str, Any], analysis_type: str) -> str:
        """构建合盘分析提示词"""
        try:
            logger.info(f"🔨 构建合盘{analysis_type}分析提示词")

            # 获取基础信息
            person_a_info = analysis_data["person_a"]["info"]
            person_b_info = analysis_data["person_b"]["info"]
            relationship_type = analysis_data.get("relationship_type", "未知关系")
            compatibility_dimension = analysis_data.get("compatibility_dimension", "")

            # 获取分析数据
            person_a_ziwei = analysis_data["person_a"]["ziwei"]
            person_a_bazi = analysis_data["person_a"]["bazi"]
            person_b_ziwei = analysis_data["person_b"]["ziwei"]
            person_b_bazi = analysis_data["person_b"]["bazi"]

            # 构建合盘提示词
            prompt = f"""
作为专业的紫薇斗数+八字命理合盘分析师，请对以下两人进行{self._get_compatibility_dimension_name(analysis_type)}的深度合盘分析。

【基本信息】
👤 {person_a_info.get('name', 'A')}：{person_a_info.get('year')}年{person_a_info.get('month')}月{person_a_info.get('day')}日 {person_a_info.get('hour')} {person_a_info.get('gender')}
👤 {person_b_info.get('name', 'B')}：{person_b_info.get('year')}年{person_b_info.get('month')}月{person_b_info.get('day')}日 {person_b_info.get('hour')} {person_b_info.get('gender')}
🔗 关系类型：{relationship_type}

【{person_a_info.get('name', 'A')}的紫薇斗数数据】
{self._format_compatibility_ziwei_data(person_a_ziwei)}

【{person_a_info.get('name', 'A')}的八字数据】
{self._format_compatibility_bazi_data(person_a_bazi)}

【{person_b_info.get('name', 'B')}的紫薇斗数数据】
{self._format_compatibility_ziwei_data(person_b_ziwei)}

【{person_b_info.get('name', 'B')}的八字数据】
{self._format_compatibility_bazi_data(person_b_bazi)}

【合盘分析要求】
{self._get_compatibility_requirements(analysis_type, relationship_type)}

【输出要求】
- 分析内容：5000字以上（必须达到）
- 🚨 必须包含大量负面分析，客观指出两人关系中的问题和风险
- 🚨 不得回避不利因素，要深度分析冲突和挑战
- 🚨 必须提供具体的关系改善方案和问题解决方法
- 每个结论都要有数据支撑（基于两人的真实命盘数据）
- 预测要具体到年份、事件、影响程度
- 建议要实用可操作，有具体步骤
- 严格限定在{self._get_compatibility_dimension_name(analysis_type)}分析，不得涉及其他维度

请开始分析：
"""

            logger.info(f"✅ 合盘{analysis_type}提示词构建完成，长度: {len(prompt)}字符")
            return prompt

        except Exception as e:
            logger.error(f"❌ 构建合盘提示词失败: {e}")
            return f"构建合盘提示词失败: {str(e)}"

    def _get_compatibility_dimension_name(self, analysis_type: str) -> str:
        """获取合盘分析维度的中文名称"""
        dimension_names = {
            "personality_compatibility": "性格互补性",
            "wealth_cooperation": "财运配合度",
            "emotional_harmony": "感情和谐度",
            "career_partnership": "事业合作潜力",
            "health_influence": "健康相互影响",
            "family_harmony": "家庭和睦度",
            "children_fortune": "子女缘分",
            "overall_compatibility": "综合匹配度"
        }
        return dimension_names.get(analysis_type, analysis_type)

    def _format_compatibility_ziwei_data(self, ziwei_data: Dict[str, Any]) -> str:
        """格式化合盘用的紫薇斗数数据"""
        if not ziwei_data:
            return "紫薇斗数数据缺失"

        palaces = ziwei_data.get("palaces", {})
        if not palaces:
            return "宫位数据缺失"

        formatted_data = []
        for palace_name, palace_info in palaces.items():
            position = palace_info.get("位置", "未知")
            major_stars = palace_info.get("主星", [])
            minor_stars = palace_info.get("辅星", [])

            stars_info = f"主星: {', '.join(major_stars) if major_stars else '无'}"
            if minor_stars:
                stars_info += f", 辅星: {', '.join(minor_stars[:3])}{'...' if len(minor_stars) > 3 else ''}"

            formatted_data.append(f"  {palace_name}({position}): {stars_info}")

        return "\n".join(formatted_data)

    def _format_compatibility_bazi_data(self, bazi_data: Dict[str, Any]) -> str:
        """格式化合盘用的八字数据"""
        if not bazi_data:
            return "八字数据缺失"

        formatted_data = []

        # 四柱信息
        if "四柱" in bazi_data:
            formatted_data.append(f"  四柱: {bazi_data['四柱']}")

        pillars = ["年柱", "月柱", "日柱", "时柱"]
        for pillar in pillars:
            if pillar in bazi_data:
                formatted_data.append(f"  {pillar}: {bazi_data[pillar]}")

        # 五行分析
        if "五行" in bazi_data:
            wuxing = bazi_data["五行"]
            if isinstance(wuxing, dict):
                wuxing_str = ", ".join([f"{element}: {count}" for element, count in wuxing.items()])
                formatted_data.append(f"  五行: {wuxing_str}")

        # 日主信息
        if "日主" in bazi_data:
            day_master = bazi_data["日主"]
            if isinstance(day_master, dict):
                formatted_data.append(f"  日主: {day_master.get('天干', '未知')}({day_master.get('五行', '未知')})")

        return "\n".join(formatted_data) if formatted_data else "八字分析数据缺失"

    def _get_compatibility_requirements(self, analysis_type: str, relationship_type: str) -> str:
        """获取合盘分析的特定要求"""
        requirements = {
            "personality_compatibility": f"""
【性格互补性专项要求】
- 仅分析两人性格的匹配度和互补性，严禁涉及财运、事业、健康等其他维度
- 🚨 深度分析性格冲突：价值观差异、行为模式冲突、沟通障碍
- 🚨 客观指出性格不合的具体表现和可能导致的关系问题
- 🚨 详细分析两人相处中的摩擦点和矛盾根源
- 分析性格互补的积极面和如何发挥优势
- 提供性格磨合的具体方法和沟通技巧
- 预测性格冲突的高发期和化解时机
- 字数要求：5000字以上，负面分析不少于40%
""",
            "emotional_harmony": f"""
【感情和谐度专项要求】
- 仅分析两人感情关系的和谐程度，严禁涉及财运、事业、健康等其他维度
- 🚨 深度分析感情危机：感情淡化、第三者风险、分手离婚可能性
- 🚨 客观指出感情中的重大问题和关系破裂风险
- 🚨 详细分析感情波动期和关系不稳定因素
- 分析感情和谐的积极面和深化感情的方法
- 提供感情维护的具体策略和危机化解方案
- 预测感情低潮期和关系转折点
- 字数要求：5000字以上，负面分析不少于40%
"""
        }

        return requirements.get(analysis_type, f"""
【{self._get_compatibility_dimension_name(analysis_type)}专项要求】
- 仅分析{self._get_compatibility_dimension_name(analysis_type)}相关内容，严禁涉及其他维度
- 🚨 必须包含大量负面分析，客观指出问题和风险
- 🚨 不得回避不利因素，要深度分析冲突和挑战
- 🚨 必须提供具体的改善方案和问题解决方法
- 字数要求：5000字以上，负面分析不少于40%
""")

    def _build_fallback_prompt(self, analysis_type: str) -> str:
        """构建备用提示词"""
        return f"""
请进行{analysis_type}相关的专业分析。

要求：
1. 基于提供的数据进行分析
2. 内容专业详细
3. 包含正面和负面分析
4. 提供实用建议

请开始分析。
"""

    def validate_prompt(self, prompt: str) -> bool:
        """验证提示词质量"""
        try:
            # 检查长度
            if len(prompt) < 500:
                logger.warning("提示词过短")
                return False

            # 检查是否为合盘分析提示词
            is_compatibility = "合盘" in prompt or "👤" in prompt

            if is_compatibility:
                # 合盘分析的关键部分
                required_sections = ["👤", "🔗", "分析要求", "输出要求"]
                for section in required_sections:
                    if section not in prompt:
                        logger.warning(f"合盘提示词缺少{section}部分")
                        return False

                # 检查是否包含双方信息
                if prompt.count("👤") < 2:
                    logger.warning("合盘提示词缺少双方信息")
                    return False

                logger.info("✅ 合盘提示词质量验证通过")
                return True
            else:
                # 单人分析的关键部分
                required_sections = ["分析要求", "这个人的基本情况", "排盘结果", "分析方法"]
                for section in required_sections:
                    if section not in prompt:
                        logger.warning(f"提示词缺少{section}部分")
                        return False

                # 检查通俗易懂要求
                normal_requirements = ["通俗易懂", "自然流畅", "避免", "专业术语"]
                normal_count = sum(1 for req in normal_requirements if req in prompt)
                if normal_count < 2:
                    logger.warning(f"提示词通俗化程度不足，只包含{normal_count}个通俗化要求")
                    return False

                logger.info("✅ 单人分析提示词质量验证通过")
                return True

        except Exception as e:
            logger.error(f"提示词验证失败: {e}")
            return False
