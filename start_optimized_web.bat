@echo off
chcp 65001 >nul
title 紫薇+八字融合分析后台系统 v3.0 - 优化版

echo.
echo 🔮 紫薇+八字融合分析后台系统 v3.0 - 优化版
echo ============================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.8+并添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 启动优化版系统
echo 🚀 启动优化版后台系统...
echo.
python start_optimized_web.py

REM 如果出错，提供备用选项
if errorlevel 1 (
    echo.
    echo ❌ 优化版启动失败，尝试启动原版本...
    echo.
    streamlit run backend_agent_web.py
)

pause
