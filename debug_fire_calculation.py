#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试火元素计算错误
"""

def debug_fire_calculation():
    """调试火元素计算"""
    print("🔍 调试火元素计算错误")
    print("=" * 50)
    
    bazi = "戊辰 丁巳 丁亥 丙午"
    print(f"八字: {bazi}")
    
    # 手动逐步计算
    print(f"\n📊 逐步计算火元素:")
    
    fire_count = 0
    fire_sources = []
    
    # 年柱：戊辰
    print("年柱 戊辰:")
    print("  戊(天干) = 土")
    print("  辰(地支) = 土")
    print("  辰藏干: 戊(土)、乙(木)、癸(水)")
    print("  → 火元素: 0个")
    
    # 月柱：丁巳
    print("月柱 丁巳:")
    print("  丁(天干) = 火 ✓")
    fire_count += 1
    fire_sources.append("月干丁")
    print("  巳(地支) = 火 ✓")
    fire_count += 1
    fire_sources.append("月支巳")
    print("  巳藏干: 丙(火)、戊(土)、庚(金)")
    print("    丙(火) ✓")
    fire_count += 0.5
    fire_sources.append("月支巳藏丙")
    print("  → 火元素: 2.5个")
    
    # 日柱：丁亥
    print("日柱 丁亥:")
    print("  丁(天干) = 火 ✓")
    fire_count += 1
    fire_sources.append("日干丁")
    print("  亥(地支) = 水")
    print("  亥藏干: 壬(水)、甲(木)")
    print("  → 火元素: 1个")
    
    # 时柱：丙午
    print("时柱 丙午:")
    print("  丙(天干) = 火 ✓")
    fire_count += 1
    fire_sources.append("时干丙")
    print("  午(地支) = 火 ✓")
    fire_count += 1
    fire_sources.append("时支午")
    print("  午藏干: 丁(火)、己(土)")
    print("    丁(火) ✓")
    fire_count += 0.5
    fire_sources.append("时支午藏丁")
    print("  → 火元素: 2.5个")
    
    print(f"\n🔥 火元素总计:")
    print(f"  总数: {fire_count}个")
    print(f"  来源: {fire_sources}")
    
    # 检查我的算法
    print(f"\n🔧 检查我的算法:")
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()
        
        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")
        
        if result["success"]:
            analysis = result["analysis"]
            wuxing = analysis["wuxing"]
            my_fire_count = wuxing["count"]["火"]
            my_fire_details = wuxing["details"]["火"]
            
            print(f"  我的算法火元素: {my_fire_count}个")
            print(f"  我的算法来源: {my_fire_details}")
            
            # 对比
            print(f"\n🔍 对比分析:")
            print(f"  标准计算: {fire_count}个")
            print(f"  我的算法: {my_fire_count}个")
            print(f"  差异: {my_fire_count - fire_count}个")
            
            if abs(my_fire_count - fire_count) > 0.1:
                print("❌ 发现计算错误！")
                
                # 找出多算的部分
                print(f"\n🔍 分析多算的原因:")
                standard_sources = set(fire_sources)
                my_sources = set(my_fire_details)
                
                extra_sources = my_sources - standard_sources
                if extra_sources:
                    print(f"  多算的来源: {extra_sources}")
                
                missing_sources = standard_sources - my_sources
                if missing_sources:
                    print(f"  漏算的来源: {missing_sources}")
                
                return False
            else:
                print("✅ 计算正确！")
                return True
        else:
            print(f"❌ 算法执行失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_algorithm_logic():
    """检查算法逻辑"""
    print(f"\n🔍 检查算法逻辑")
    print("=" * 50)
    
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()
        
        # 模拟算法执行过程
        year_pillar, month_pillar, day_pillar, hour_pillar = "戊辰", "丁巳", "丁亥", "丙午"
        
        wuxing_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
        wuxing_details = {'木': [], '火': [], '土': [], '金': [], '水': []}
        
        pillars = [year_pillar, month_pillar, day_pillar, hour_pillar]
        pillar_names = ['年', '月', '日', '时']
        
        print("🔧 模拟算法执行:")
        
        for i, pillar in enumerate(pillars):
            print(f"\n处理{pillar_names[i]}柱 {pillar}:")
            
            # 天干
            tiangan = pillar[0]
            if tiangan in calc.wuxing_map:
                element = calc.wuxing_map[tiangan]
                wuxing_count[element] += 1
                wuxing_details[element].append(f"{pillar_names[i]}干{tiangan}")
                print(f"  天干{tiangan} = {element}")
            
            # 地支
            dizhi = pillar[1]
            if dizhi in calc.wuxing_map:
                element = calc.wuxing_map[dizhi]
                wuxing_count[element] += 1
                wuxing_details[element].append(f"{pillar_names[i]}支{dizhi}")
                print(f"  地支{dizhi} = {element}")
            
            # 地支藏干
            if dizhi in calc.dizhi_canggan:
                print(f"  {dizhi}藏干:")
                for canggan in calc.dizhi_canggan[dizhi]:
                    if canggan in calc.wuxing_map:
                        element = calc.wuxing_map[canggan]
                        wuxing_count[element] += 0.5  # 藏干权重减半
                        wuxing_details[element].append(f"{pillar_names[i]}支{dizhi}藏{canggan}")
                        print(f"    {canggan} = {element} (权重0.5)")
        
        print(f"\n📊 模拟结果:")
        for element, count in wuxing_count.items():
            print(f"  {element}: {count}个")
            if element == '火':
                print(f"    来源: {wuxing_details[element]}")
        
        return wuxing_count['火']
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return 0

def main():
    """主函数"""
    print("🧪 火元素计算错误调试")
    print("=" * 80)
    
    # 调试计算
    debug_result = debug_fire_calculation()
    
    # 检查算法逻辑
    simulated_fire = check_algorithm_logic()
    
    print("\n" + "=" * 80)
    print("🎯 调试结论:")
    if debug_result:
        print("✅ 算法计算正确")
    else:
        print("❌ 算法需要修正")
        print(f"模拟计算火元素: {simulated_fire}个")

if __name__ == "__main__":
    main()
