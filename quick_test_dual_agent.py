#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试双Agent修复效果
"""

import asyncio
import sys
sys.path.append('.')

async def quick_test():
    """快速测试双Agent系统"""
    try:
        from core.agents.customer_service_agent import CustomerServiceAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.agent_coordinator import AgentCoordinator
        from core.agents.base_agent import agent_registry
        
        print("快速测试双Agent修复效果")
        print("=" * 50)
        
        # 创建Agent
        customer_agent = CustomerServiceAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = AgentCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(customer_agent)
        agent_registry.register_agent(calculator_agent)
        
        print("Agent创建和注册成功")
        
        # 测试算命请求
        test_message = "我想看紫薇斗数，1988年6月1日午时出生，男性"
        print(f"测试消息: {test_message}")
        
        result = await coordinator.handle_user_message("test_session", test_message)
        
        print(f"处理结果: {result.get('success')}")
        if result.get("success"):
            print("双Agent协作成功！")
            response = result.get("response", "")
            print(f"响应: {response[:100]}...")
            if "calculation_result" in result:
                print("包含算命计算结果")
        else:
            print(f"失败原因: {result.get('error')}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = asyncio.run(quick_test())
    print(f"\n测试结果: {'通过' if success else '失败'}")
    return success

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
