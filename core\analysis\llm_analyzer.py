#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM分析器
负责调用LLM进行分析，确保结果质量
"""

import logging
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class LLMAnalyzer:
    """LLM分析器"""

    def __init__(self):
        """初始化LLM分析器"""
        self.llm_client = None
        self.max_retries = 3
        self.timeout_seconds = 180
        self._init_llm_client()

    def _init_llm_client(self):
        """初始化LLM客户端"""
        try:
            from core.nlu.llm_client import LLMClient
            self.llm_client = LLMClient()
            logger.info("✅ LLM客户端初始化成功")
        except Exception as e:
            logger.error(f"❌ LLM客户端初始化失败: {e}")

    async def analyze(self, prompt: str, analysis_type: str) -> Optional[str]:
        """执行LLM分析"""
        try:
            if not self.llm_client:
                logger.error("LLM客户端未初始化")
                return None

            logger.info(f"🤖 开始LLM分析: {analysis_type}")

            # 多次重试机制
            for attempt in range(self.max_retries):
                try:
                    logger.info(f"🔄 第{attempt + 1}次尝试分析")

                    # 调用LLM
                    result = await self._call_llm_with_timeout(prompt)

                    if result and len(result) > 500:  # 降低要求到500字符
                        logger.info(f"✅ 分析成功: {len(result)}字符")
                        return result
                    else:
                        logger.warning(f"⚠️ 第{attempt + 1}次尝试结果过短: {len(result) if result else 0}字符")

                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ 第{attempt + 1}次尝试超时")
                except Exception as e:
                    logger.warning(f"⚠️ 第{attempt + 1}次尝试异常: {e}")

                # 如果不是最后一次尝试，等待一下再重试
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2)

            logger.error(f"❌ {analysis_type}分析失败，已重试{self.max_retries}次")
            return None

        except Exception as e:
            logger.error(f"❌ LLM分析异常: {e}")
            return None

    async def _call_llm_with_timeout(self, prompt: str) -> Optional[str]:
        """带超时的LLM调用"""
        try:
            messages = [{"role": "user", "content": prompt}]

            # 使用asyncio.wait_for添加超时控制
            result = await asyncio.wait_for(
                asyncio.to_thread(
                    self.llm_client.chat_completion,
                    messages,
                    temperature=0.2,  # 低温度确保准确性
                    max_tokens=4000   # 增加token限制
                ),
                timeout=self.timeout_seconds
            )

            return result

        except asyncio.TimeoutError:
            logger.error(f"LLM调用超时({self.timeout_seconds}秒)")
            raise
        except Exception as e:
            logger.error(f"LLM调用异常: {e}")
            raise

    def validate_analysis_result(self, result: str, analysis_data: Dict[str, Any]) -> bool:
        """验证分析结果"""
        try:
            if not result or len(result) < 500:  # 降低要求到500字符
                logger.warning("分析结果过短")
                return False

            # 检查是否包含禁止的错误信息
            forbidden_phrases = [
                "天机星坐命",
                "太阴化科同宫",
                "太阳天梁同宫于午",
                "紫微七杀同宫",
                "廉贞贪狼同宫"
            ]

            for phrase in forbidden_phrases:
                if phrase in result:
                    logger.warning(f"分析结果包含错误信息: {phrase}")
                    return False

            # 检查是否包含正确的宫位信息
            ziwei_data = analysis_data.get("ziwei", {})
            palaces = ziwei_data.get("palaces", {})

            if palaces:
                # 验证命宫信息
                mingong = palaces.get("命宫", {})
                if mingong:
                    position = mingong.get("位置", "")
                    major_stars = mingong.get("主星", [])

                    if position and major_stars:
                        # 检查是否包含正确的命宫信息
                        position_mentioned = f"命宫({position})" in result or f"命宫{position}" in result
                        stars_mentioned = any(star in result for star in major_stars)

                        if not position_mentioned:
                            logger.warning(f"分析结果缺少命宫位置信息: {position}")
                            return False

                        if not stars_mentioned:
                            logger.warning(f"分析结果缺少命宫主星信息: {major_stars}")
                            return False

            # 检查分析结构
            required_sections = ["基础", "发展", "建议", "注意"]
            section_count = sum(1 for section in required_sections if section in result)

            if section_count < 2:
                logger.warning(f"分析结果结构不完整，只包含{section_count}个必要部分")
                return False

            logger.info("✅ 分析结果验证通过")
            return True

        except Exception as e:
            logger.error(f"验证分析结果失败: {e}")
            return False

    def extract_analysis_summary(self, result: str) -> Dict[str, str]:
        """提取分析摘要"""
        try:
            summary = {}

            # 提取关键信息
            lines = result.split('\n')
            current_section = ""
            section_content = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检测章节标题
                if any(keyword in line for keyword in ["##", "基础", "发展", "建议", "注意"]):
                    if current_section and section_content:
                        summary[current_section] = '\n'.join(section_content[:3])  # 只取前3行作为摘要

                    current_section = line.replace("#", "").strip()
                    section_content = []
                else:
                    if current_section:
                        section_content.append(line)

            # 处理最后一个章节
            if current_section and section_content:
                summary[current_section] = '\n'.join(section_content[:3])

            return summary

        except Exception as e:
            logger.error(f"提取分析摘要失败: {e}")
            return {}

    def calculate_analysis_score(self, result: str, analysis_data: Dict[str, Any]) -> float:
        """计算分析质量评分"""
        try:
            score = 0.0

            # 长度评分 (0-20分)
            length = len(result)
            if length >= 3000:
                score += 20
            elif length >= 2000:
                score += 15
            elif length >= 1000:
                score += 10
            else:
                score += 5

            # 结构评分 (0-20分)
            sections = ["基础", "发展", "建议", "注意"]
            section_count = sum(1 for section in sections if section in result)
            score += section_count * 5

            # 数据准确性评分 (0-30分)
            if self.validate_analysis_result(result, analysis_data):
                score += 30
            else:
                score += 10  # 部分准确

            # 专业性评分 (0-20分)
            professional_terms = ["宫位", "星曜", "五行", "生克", "大运", "流年"]
            term_count = sum(1 for term in professional_terms if term in result)
            score += min(term_count * 3, 20)

            # 实用性评分 (0-10分)
            practical_keywords = ["建议", "注意", "方法", "策略", "时间"]
            practical_count = sum(1 for keyword in practical_keywords if keyword in result)
            score += min(practical_count * 2, 10)

            # 总分100分
            final_score = min(score, 100.0)

            logger.info(f"📊 分析质量评分: {final_score:.1f}/100")
            return final_score

        except Exception as e:
            logger.error(f"计算分析评分失败: {e}")
            return 0.0
