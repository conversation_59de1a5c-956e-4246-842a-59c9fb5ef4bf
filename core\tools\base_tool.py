#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具基类 - 所有功能工具的基础抽象类
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class BaseTool(ABC):
    """工具基类 - 定义所有功能工具的标准接口"""
    
    def __init__(self, name: str, description: str, version: str = "1.0.0"):
        """
        初始化工具
        
        Args:
            name: 工具名称
            description: 工具描述
            version: 工具版本
        """
        self.name = name
        self.description = description
        self.version = version
        self.created_at = datetime.now()
        
        # 工具统计信息
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "average_response_time": 0.0
        }
        
        logger.info(f"工具 {name} 初始化完成 - 版本: {version}")
    
    @abstractmethod
    def can_handle(self, intent: Dict[str, Any]) -> bool:
        """
        判断是否能处理该意图
        
        Args:
            intent: 意图信息
            
        Returns:
            是否能处理
        """
        pass
    
    @abstractmethod
    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行工具功能
        
        Args:
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            执行结果
        """
        pass
    
    def get_required_entities(self) -> List[str]:
        """
        获取工具所需的实体列表
        
        Returns:
            实体名称列表
        """
        return []
    
    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证输入参数
        
        Args:
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            验证结果 {"valid": bool, "message": str, "missing_entities": list}
        """
        required_entities = self.get_required_entities()
        entities = intent.get("entities", {})
        missing_entities = []
        
        for entity in required_entities:
            if entity not in entities and entity not in context:
                missing_entities.append(entity)
        
        if missing_entities:
            return {
                "valid": False,
                "message": f"缺少必要信息: {', '.join(missing_entities)}",
                "missing_entities": missing_entities
            }
        
        return {
            "valid": True,
            "message": "输入验证通过",
            "missing_entities": []
        }
    
    def get_prompt_template(self, task_type: str) -> Optional[str]:
        """
        获取工具专用的提示词模板
        
        Args:
            task_type: 任务类型
            
        Returns:
            提示词模板或None
        """
        return None
    
    def format_response(self, raw_result: Dict[str, Any], intent: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化响应结果
        
        Args:
            raw_result: 原始结果
            intent: 意图信息
            
        Returns:
            格式化后的结果
        """
        return {
            "success": raw_result.get("success", True),
            "tool_name": self.name,
            "tool_version": self.version,
            "timestamp": datetime.now(),
            "data": raw_result,
            "intent": intent
        }
    
    def handle_error(self, error: Exception, intent: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理错误
        
        Args:
            error: 异常对象
            intent: 意图信息
            
        Returns:
            错误响应
        """
        logger.error(f"工具 {self.name} 执行失败: {error}")
        
        return {
            "success": False,
            "tool_name": self.name,
            "error": str(error),
            "error_type": type(error).__name__,
            "timestamp": datetime.now(),
            "intent": intent
        }
    
    def update_stats(self, success: bool, response_time: float) -> None:
        """
        更新统计信息
        
        Args:
            success: 是否成功
            response_time: 响应时间（秒）
        """
        self.stats["total_calls"] += 1
        
        if success:
            self.stats["successful_calls"] += 1
        else:
            self.stats["failed_calls"] += 1
        
        # 更新平均响应时间
        total_time = self.stats["average_response_time"] * (self.stats["total_calls"] - 1)
        self.stats["average_response_time"] = (total_time + response_time) / self.stats["total_calls"]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取工具统计信息
        
        Returns:
            统计信息
        """
        success_rate = 0
        if self.stats["total_calls"] > 0:
            success_rate = self.stats["successful_calls"] / self.stats["total_calls"] * 100
        
        return {
            "name": self.name,
            "version": self.version,
            "total_calls": self.stats["total_calls"],
            "successful_calls": self.stats["successful_calls"],
            "failed_calls": self.stats["failed_calls"],
            "success_rate": f"{success_rate:.2f}%",
            "average_response_time": f"{self.stats['average_response_time']:.2f}s",
            "created_at": self.created_at.isoformat()
        }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "average_response_time": 0.0
        }
        logger.info(f"工具 {self.name} 统计信息已重置")
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取工具基本信息
        
        Returns:
            工具信息
        """
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "created_at": self.created_at.isoformat(),
            "required_entities": self.get_required_entities()
        }
    
    def __str__(self) -> str:
        return f"Tool({self.name}, v{self.version})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}', version='{self.version}')>"

class ToolExecutionContext:
    """工具执行上下文 - 提供工具执行时的环境信息"""
    
    def __init__(self, session_id: str, user_id: Optional[str] = None):
        """
        初始化执行上下文
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
        """
        self.session_id = session_id
        self.user_id = user_id
        self.start_time = datetime.now()
        self.metadata = {}
    
    def add_metadata(self, key: str, value: Any) -> None:
        """添加元数据"""
        self.metadata[key] = value
    
    def get_execution_time(self) -> float:
        """获取执行时间（秒）"""
        return (datetime.now() - self.start_time).total_seconds()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "start_time": self.start_time.isoformat(),
            "execution_time": self.get_execution_time(),
            "metadata": self.metadata
        }
