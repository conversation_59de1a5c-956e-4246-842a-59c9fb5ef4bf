#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两个案例：
1. 1988年6月1日午时
2. 1985年4月23日亥时
"""

def test_ziwei_case(year, month, day, hour, time_name, gender="男"):
    """测试单个紫薇斗数案例"""
    try:
        import py_iztro
        astro = py_iztro.Astro()
        
        print(f"📅 测试: {year}年{month}月{day}日{time_name} {gender}命")
        print("-" * 50)
        
        # 时辰映射
        time_mapping = {
            "子时": 0, "丑时": 1, "寅时": 2, "卯时": 3, "辰时": 4, "巳时": 5,
            "午时": 6, "未时": 7, "申时": 8, "酉时": 9, "戌时": 10, "亥时": 11
        }
        
        time_index = time_mapping.get(time_name, 6)  # 默认午时
        
        astrolabe = astro.by_solar(f'{year}-{month}-{day}', time_index, gender)
        
        print("✅ 排盘成功")
        print(f"📅 农历: {astrolabe.lunar_date}")
        print(f"🎯 八字: {astrolabe.chinese_date}")
        print(f"🐂 生肖: {astrolabe.zodiac}")
        print(f"⭐ 星座: {astrolabe.sign}")
        print(f"🏰 命宫地支: {astrolabe.earthly_branch_of_soul_palace}")
        print(f"🏛️ 身宫地支: {astrolabe.earthly_branch_of_body_palace}")
        
        # 修复后的宫位映射逻辑
        earthly_branches = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        
        # 创建地支到宫位的映射
        branch_to_palace = {}
        for palace in astrolabe.palaces:
            branch_to_palace[palace.earthly_branch] = palace
        
        # 找到命宫的地支位置
        soul_palace_branch = astrolabe.earthly_branch_of_soul_palace
        soul_palace_index = earthly_branches.index(soul_palace_branch)
        
        # 十二宫名称（从命宫开始逆时针排列）
        palace_names = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                       "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]
        
        print("\n🏰 十二宫详细配置:")
        print("-" * 40)
        
        result_palaces = {}
        
        # 按正确的宫位顺序填充数据
        for i, palace_name in enumerate(palace_names):
            # 计算当前宫位对应的地支索引（逆时针）
            branch_index = (soul_palace_index - i) % 12
            branch = earthly_branches[branch_index]
            
            if branch in branch_to_palace:
                palace = branch_to_palace[branch]
                
                # 获取该宫的星曜
                major_stars = [star.name for star in palace.major_stars] if hasattr(palace, 'major_stars') else []
                minor_stars = [star.name for star in palace.minor_stars] if hasattr(palace, 'minor_stars') else []
                adjective_stars = [star.name for star in palace.adjective_stars] if hasattr(palace, 'adjective_stars') else []
                is_body_palace = getattr(palace, 'is_body_palace', False)
                
                # 组合星曜显示
                all_stars = []
                if major_stars:
                    all_stars.append(f"主星: {' '.join(major_stars)}")
                if minor_stars:
                    all_stars.append(f"辅星: {' '.join(minor_stars[:3])}")  # 只显示前3个
                if adjective_stars:
                    all_stars.append(f"杂曜: {' '.join(adjective_stars[:3])}")  # 只显示前3个
                
                stars_display = " | ".join(all_stars) if all_stars else "无星曜"
                body_mark = " [身宫]" if is_body_palace else ""
                
                print(f"{palace_name}({branch}){body_mark}: {stars_display}")
                
                # 保存结果用于返回
                result_palaces[palace_name] = {
                    "position": branch,
                    "major_stars": major_stars,
                    "minor_stars": minor_stars,
                    "adjective_stars": adjective_stars,
                    "is_body_palace": is_body_palace
                }
        
        print("\n🎯 关键信息总结:")
        print("-" * 25)
        print(f"命宫: {astrolabe.earthly_branch_of_soul_palace}宫 {[star.name for star in branch_to_palace[astrolabe.earthly_branch_of_soul_palace].major_stars]}")
        print(f"身宫: {astrolabe.earthly_branch_of_body_palace}宫 {[star.name for star in branch_to_palace[astrolabe.earthly_branch_of_body_palace].major_stars]}")
        
        return {
            "success": True,
            "birth_info": {
                "solar": f"{year}年{month}月{day}日{time_name}",
                "lunar": astrolabe.lunar_date,
                "chinese_date": astrolabe.chinese_date
            },
            "zodiac": astrolabe.zodiac,
            "sign": astrolabe.sign,
            "soul_palace": astrolabe.earthly_branch_of_soul_palace,
            "body_palace": astrolabe.earthly_branch_of_body_palace,
            "palaces": result_palaces
        }
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

def main():
    """主测试函数"""
    print("🔮 紫薇斗数算法修复验证测试")
    print("=" * 60)
    
    # 测试案例1: 1988年6月1日午时
    print("\n【案例1】")
    result1 = test_ziwei_case(1988, 6, 1, 12, "午时", "男")
    
    print("\n" + "=" * 60)
    
    # 测试案例2: 1985年4月23日亥时
    print("\n【案例2】")
    result2 = test_ziwei_case(1985, 4, 23, 22, "亥时", "女")
    
    print("\n" + "=" * 60)
    print("\n🎉 测试完成！")
    
    # 简单验证
    if result1.get("success") and result2.get("success"):
        print("✅ 两个案例都计算成功")
        
        # 验证案例2（我们知道的标准答案）
        case2_ming_gong = result2.get("soul_palace")
        case2_palaces = result2.get("palaces", {})
        ming_gong_stars = case2_palaces.get("命宫", {}).get("major_stars", [])
        
        print(f"\n🎯 案例2验证 (1985年4月23日亥时女命):")
        print(f"命宫位置: {case2_ming_gong} (期望: 巳)")
        print(f"命宫主星: {ming_gong_stars} (期望: ['巨门'])")
        print(f"验证结果: {'✅ 正确' if case2_ming_gong == '巳' and '巨门' in ming_gong_stars else '❌ 错误'}")
    else:
        print("❌ 部分案例计算失败")

if __name__ == "__main__":
    main()
