#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全新重构的分析系统
验证数据准确性和分析质量
"""

import asyncio
import json
from datetime import datetime

async def test_new_analysis_system():
    """测试新的分析系统"""
    print("🧪 测试全新重构的分析系统")
    print("=" * 70)
    
    try:
        # 1. 清理旧缓存
        print("1️⃣ 清理旧缓存")
        print("-" * 50)
        
        import os
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.endswith('.json'):
                    os.remove(os.path.join(cache_dir, file))
            print("✅ 旧缓存已清理")
        else:
            print("✅ 缓存目录不存在，无需清理")
        
        # 2. 初始化新的分析控制器
        print(f"\n2️⃣ 初始化新的分析控制器")
        print("-" * 50)
        
        from core.analysis.analysis_controller import AnalysisController
        
        analysis_controller = AnalysisController()
        print("✅ 分析控制器初始化完成")
        
        # 3. 准备测试数据
        print(f"\n3️⃣ 准备测试数据")
        print("-" * 50)
        
        test_birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        print(f"📅 测试出生信息: {test_birth_info}")
        
        # 4. 获取融合分析数据
        print(f"\n4️⃣ 获取融合分析数据")
        print("-" * 50)
        
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        fusion_engine = ZiweiBaziFusionEngine()
        calculator_agent = FortuneCalculatorAgent()
        
        hour_number = calculator_agent._convert_hour_to_number("午时")
        
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=1988,
            month=6,
            day=1,
            hour=hour_number,
            gender="男"
        )
        
        if not raw_data.get("success"):
            print(f"❌ 融合分析失败: {raw_data.get('error')}")
            return
        
        print("✅ 融合分析数据获取成功")
        
        # 验证关键数据
        ziwei_data = raw_data.get("ziwei_analysis", {})
        palaces = ziwei_data.get("palaces", {})
        
        print(f"\n🏠 关键宫位验证:")
        key_palaces = ["命宫", "疾厄宫", "父母宫"]
        for palace_name in key_palaces:
            palace_data = palaces.get(palace_name, {})
            position = palace_data.get("position", "")
            major_stars = palace_data.get("major_stars", [])
            print(f"  {palace_name}({position}): {major_stars}")
        
        # 5. 测试单个角度分析
        print(f"\n5️⃣ 测试单个角度分析")
        print("-" * 50)
        
        test_analysis_type = "personality_destiny"
        
        print(f"🎯 测试分析类型: {test_analysis_type}")
        
        analysis_result = await analysis_controller.execute_single_analysis(
            raw_data=raw_data,
            birth_info=test_birth_info,
            analysis_type=test_analysis_type
        )
        
        print(f"📊 分析结果:")
        print(f"  成功: {analysis_result.get('success')}")
        print(f"  类型: {analysis_result.get('analysis_type')}")
        print(f"  时间: {analysis_result.get('timestamp')}")
        
        if analysis_result.get("error"):
            print(f"  错误: {analysis_result.get('error')}")
        
        content = analysis_result.get("content", "")
        if content:
            print(f"  内容长度: {len(content)} 字符")
            print(f"  内容预览: {content[:200]}...")
        
        # 6. 验证分析内容
        print(f"\n6️⃣ 验证分析内容")
        print("-" * 50)
        
        validation_report = analysis_result.get("validation_report", {})
        
        if validation_report:
            print(f"📋 验证报告:")
            print(f"  总体有效: {validation_report.get('overall_valid')}")
            print(f"  评分: {validation_report.get('score', 0):.1f}/100")
            print(f"  长度检查: {'✅' if validation_report.get('length_check') else '❌'}")
            print(f"  结构检查: {'✅' if validation_report.get('structure_check') else '❌'}")
            print(f"  准确性检查: {'✅' if validation_report.get('accuracy_check') else '❌'}")
            print(f"  专业性检查: {'✅' if validation_report.get('professional_check') else '❌'}")
            print(f"  数据一致性检查: {'✅' if validation_report.get('data_consistency_check') else '❌'}")
            
            errors = validation_report.get('errors', [])
            if errors:
                print(f"\n❌ 发现的错误:")
                for i, error in enumerate(errors[:5], 1):
                    print(f"    {i}. {error}")
            
            warnings = validation_report.get('warnings', [])
            if warnings:
                print(f"\n⚠️ 警告信息:")
                for i, warning in enumerate(warnings[:3], 1):
                    print(f"    {i}. {warning}")
        
        # 7. 检查关键信息
        print(f"\n7️⃣ 检查关键信息")
        print("-" * 50)
        
        if content:
            # 检查正确信息
            correct_checks = {
                "命宫": "命宫" in content,
                "亥宫": "亥" in content,
                "天相": "天相" in content,
                "疾厄宫": "疾厄宫" in content,
                "太阳": "太阳" in content,
                "父母宫": "父母宫" in content,
                "天梁": "天梁" in content
            }
            
            # 检查错误信息
            wrong_checks = {
                "天机星坐命": "天机星坐命" in content,
                "太阴化科": "太阴化科" in content,
                "太阳天梁同宫": "太阳天梁同宫" in content,
                "太阳天梁坐午": "太阳天梁坐午" in content
            }
            
            print(f"✅ 正确信息检查:")
            correct_count = 0
            for check_item, result in correct_checks.items():
                status = "✅" if result else "❌"
                print(f"  {status} {check_item}: {'存在' if result else '缺失'}")
                if result:
                    correct_count += 1
            
            print(f"\n🚨 错误信息检查:")
            wrong_count = 0
            for check_item, result in wrong_checks.items():
                status = "🚨" if result else "✅"
                print(f"  {status} {check_item}: {'存在(错误!)' if result else '不存在(正确)'}")
                if result:
                    wrong_count += 1
            
            print(f"\n📊 检查结果统计:")
            print(f"  正确信息: {correct_count}/{len(correct_checks)}")
            print(f"  错误信息: {wrong_count}/{len(wrong_checks)}")
        
        # 8. 保存测试结果
        print(f"\n8️⃣ 保存测试结果")
        print("-" * 50)
        
        test_result = {
            "test_time": datetime.now().isoformat(),
            "test_type": "new_analysis_system",
            "birth_info": test_birth_info,
            "analysis_type": test_analysis_type,
            "analysis_result": analysis_result,
            "content_length": len(content) if content else 0,
            "validation_passed": validation_report.get('overall_valid', False) if validation_report else False,
            "validation_score": validation_report.get('score', 0) if validation_report else 0,
            "correct_info_count": correct_count if 'correct_count' in locals() else 0,
            "wrong_info_count": wrong_count if 'wrong_count' in locals() else 0
        }
        
        with open("test_new_analysis_result.json", "w", encoding="utf-8") as f:
            json.dump(test_result, f, ensure_ascii=False, indent=2)
        
        if content:
            with open("test_new_analysis_content.txt", "w", encoding="utf-8") as f:
                f.write("新分析系统测试结果:\n")
                f.write("=" * 70 + "\n")
                f.write(f"测试时间: {test_result['test_time']}\n")
                f.write(f"分析类型: {test_analysis_type}\n")
                f.write("=" * 70 + "\n\n")
                f.write(content)
        
        print("💾 测试结果已保存:")
        print("  - test_new_analysis_result.json (测试摘要)")
        print("  - test_new_analysis_content.txt (完整内容)")
        
        # 9. 测试结论
        print(f"\n9️⃣ 测试结论")
        print("-" * 50)
        
        if analysis_result.get("success"):
            if validation_report.get('overall_valid', False):
                print("🎉 新分析系统测试完全成功!")
                print("✅ 分析生成成功")
                print("✅ 验证通过")
                print("✅ 数据准确性良好")
            else:
                print("⚠️ 新分析系统部分成功")
                print("✅ 分析生成成功")
                print("❌ 验证未完全通过")
                print("💡 需要进一步优化")
        else:
            print("❌ 新分析系统测试失败")
            print("❌ 分析生成失败")
            print("💡 需要检查系统配置")
        
        print(f"\n🏁 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_new_analysis_system())
