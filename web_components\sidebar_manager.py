#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
侧边栏管理器 - 优化的侧边栏组件
"""

import streamlit as st
import psutil
from datetime import datetime
from typing import Dict, List
from .ui_components import UIComponents, NavigationManager, DataManager

class SidebarManager:
    """侧边栏管理器"""
    
    def __init__(self):
        self.ui = UIComponents()
        self.nav = NavigationManager()
        self.data = DataManager()

    def render_sidebar(self):
        """渲染优化的侧边栏"""
        with st.container():
            self._render_header()
            self._render_navigation()
            self._render_quick_stats()
            self._render_recent_records()
            self._render_system_tools()
            self._render_quick_actions()
            self._render_system_status()
            self._render_help_section()
            self._render_version_info()

    def _render_header(self):
        """渲染标题"""
        st.markdown("""
        <div class="sidebar-container">
            <h3 style="color: #4CAF50; margin-bottom: 1rem; text-align: center;">
                🛠️ 智能管理中心
            </h3>
        </div>
        """, unsafe_allow_html=True)

    def _render_navigation(self):
        """渲染导航菜单"""
        st.markdown("#### 📋 功能导航")

        nav_options = {
            "overview": ("📊", "系统概览"),
            "records": ("📋", "分析记录"),
            "create": ("🆕", "创建分析"),
            "compatibility": ("💕", "合盘分析"),
            "liuyao": ("🔮", "六爻占卜"),
            "monitor": ("📈", "实时监控"),
            "export": ("📊", "数据导出"),
            "settings": ("⚙️", "系统设置")
        }

        current_view = self.nav.get_current_view()
        
        for key, (icon, label) in nav_options.items():
            # 高亮当前页面
            button_style = "primary" if key == current_view else "secondary"
            
            if st.button(f"{icon} {label}", 
                        key=f"nav_{key}", 
                        use_container_width=True,
                        type=button_style):
                self.nav.navigate_to(key)

        st.markdown("---")

    def _render_quick_stats(self):
        """渲染快速统计"""
        st.markdown("#### 📊 数据统计")

        cache_records = self.data.get_cache_records()
        
        if cache_records:
            stats = self._calculate_stats(cache_records)
            
            # 总记录数
            self.ui.render_metric_card(
                "总记录数", 
                str(stats['total']), 
                "📁", 
                "#4CAF50"
            )
            
            # 完成状态
            col1, col2 = st.columns(2)
            with col1:
                st.metric("已完成", stats['completed'], delta=None)
            with col2:
                st.metric("进行中", stats['in_progress'], delta=None)
        else:
            self.ui.render_alert("暂无数据", "info", "📝")

        st.markdown("---")

    def _render_recent_records(self):
        """渲染最近记录"""
        st.markdown("#### 🕒 最近记录")

        cache_records = self.data.get_cache_records()
        
        if cache_records:
            for i, record in enumerate(cache_records[:3]):
                self._render_record_card(record, i)
        else:
            self.ui.render_alert("暂无记录", "info", "📝")

        st.markdown("---")

    def _render_record_card(self, record: Dict, index: int):
        """渲染记录卡片"""
        # 确定状态和颜色
        is_completed = self._is_record_completed(record)
        status_color = "#4CAF50" if is_completed else "#FF9800"
        status_text = "完成" if is_completed else f"{record['completed_angles']}/12"
        
        st.markdown(f"""
        <div class="modern-card" style="border-left: 4px solid {status_color}; margin: 0.5rem 0;">
            <div style="font-size: 0.85rem; color: #fff; margin-bottom: 0.5rem;">
                <strong>{record['birth_info'][:20]}...</strong>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span class="status-badge status-{'success' if is_completed else 'warning'}">
                    {status_text}
                </span>
                <span style="font-size: 0.7rem; color: #ccc;">
                    {record['created_at'][11:16]}
                </span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("查看详情", key=f"quick_view_{record['result_id']}", use_container_width=True):
            self.nav.navigate_to('detail', record['result_id'])

    def _render_system_tools(self):
        """渲染系统工具"""
        st.markdown("#### ⚙️ 系统工具")

        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🧹 清理缓存", key="sidebar_clean", use_container_width=True):
                self.nav.navigate_to('clean_cache')
        
        with col2:
            if st.button("📊 导出数据", key="sidebar_export", use_container_width=True):
                self.nav.navigate_to('export')

        if st.button("🔄 刷新数据", key="sidebar_refresh", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

        st.markdown("---")

    def _render_quick_actions(self):
        """渲染快速操作"""
        st.markdown("#### ⚡ 快速操作")

        actions = [
            ("🔮", "六爻卜卦", "liuyao"),
            ("💕", "合盘分析", "compatibility"),
            ("📈", "性能监控", "monitor"),
            ("📋", "任务队列", "queue")
        ]

        for icon, label, view in actions:
            if st.button(f"{icon} {label}", 
                        key=f"sidebar_{view}", 
                        use_container_width=True):
                self.nav.navigate_to(view)

        st.markdown("---")

    def _render_system_status(self):
        """渲染系统状态"""
        st.markdown("#### 🔍 系统状态")

        # CPU状态
        cpu_percent = psutil.cpu_percent()
        cpu_color = self._get_status_color(cpu_percent, 50, 80)
        
        st.markdown(f"""
        <div class="modern-card" style="border-left: 4px solid {cpu_color}; padding: 0.8rem;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 0.8rem; color: #fff;">🖥️ CPU使用率</span>
                <span style="font-size: 0.8rem; color: {cpu_color}; font-weight: bold;">{cpu_percent:.1f}%</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # 内存状态
        memory = psutil.virtual_memory()
        mem_color = self._get_status_color(memory.percent, 60, 80)
        
        st.markdown(f"""
        <div class="modern-card" style="border-left: 4px solid {mem_color}; padding: 0.8rem;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 0.8rem; color: #fff;">💾 内存使用率</span>
                <span style="font-size: 0.8rem; color: {mem_color}; font-weight: bold;">{memory.percent:.1f}%</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # 活跃任务
        cache_records = self.data.get_cache_records()
        active_tasks = len([r for r in cache_records if not self._is_record_completed(r)])
        task_color = self._get_status_color(active_tasks, 0, 3, reverse=True)
        
        st.markdown(f"""
        <div class="modern-card" style="border-left: 4px solid {task_color}; padding: 0.8rem;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="font-size: 0.8rem; color: #fff;">📋 活跃任务</span>
                <span style="font-size: 0.8rem; color: {task_color}; font-weight: bold;">{active_tasks}</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

    def _render_help_section(self):
        """渲染帮助部分"""
        st.markdown("#### 📚 帮助中心")

        help_items = [
            ("📖", "使用手册", "help"),
            ("🔧", "调试模式", "debug"),
            ("❓", "常见问题", "faq")
        ]

        for icon, label, view in help_items:
            if st.button(f"{icon} {label}", 
                        key=f"sidebar_help_{view}", 
                        use_container_width=True):
                self.nav.navigate_to(view)

        st.markdown("---")

    def _render_version_info(self):
        """渲染版本信息"""
        st.markdown("#### ℹ️ 版本信息")
        
        st.markdown(f"""
        <div class="modern-card" style="text-align: center;">
            <div style="font-size: 0.75rem; color: #ccc; line-height: 1.4;">
                <div style="color: #4CAF50; font-weight: bold; margin-bottom: 0.5rem;">
                    🔮 紫薇+八字融合分析系统
                </div>
                <div>Version 3.0.0 (优化版)</div>
                <div>Build {datetime.now().strftime('%Y%m%d')}</div>
                <div style="margin-top: 0.5rem;">
                    <span style="color: #4CAF50;">●</span> 运行正常
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

    def _calculate_stats(self, records: List[Dict]) -> Dict:
        """计算统计数据"""
        total = len(records)
        completed = 0
        in_progress = 0

        for record in records:
            if self._is_record_completed(record):
                completed += 1
            elif record['has_analysis']:
                in_progress += 1

        return {
            'total': total,
            'completed': completed,
            'in_progress': in_progress,
            'pending': total - completed - in_progress
        }

    def _is_record_completed(self, record: Dict) -> bool:
        """判断记录是否完成"""
        calculation_type = record.get('calculation_type', 'ziwei')
        
        if calculation_type in ['compatibility', 'liuyao']:
            return record['completed_angles'] > 0 and record['total_words'] > 0
        else:
            return record['completed_angles'] >= 12

    def _get_status_color(self, value: float, warning_threshold: float, 
                         danger_threshold: float, reverse: bool = False) -> str:
        """获取状态颜色"""
        if reverse:
            if value <= warning_threshold:
                return "#4CAF50"
            elif value <= danger_threshold:
                return "#FF9800"
            else:
                return "#f44336"
        else:
            if value < warning_threshold:
                return "#4CAF50"
            elif value < danger_threshold:
                return "#FF9800"
            else:
                return "#f44336"
