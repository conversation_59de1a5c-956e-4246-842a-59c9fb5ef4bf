#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的紫薇斗数算法
"""

def test_fixed_algorithm():
    """测试修复后的算法"""
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        
        print("🔮 测试修复后的紫薇斗数算法")
        print("=" * 50)
        
        calc = RealZiweiCalculator()
        
        # 测试1985年4月23日亥时女命
        print("📅 测试数据: 1985年4月23日亥时女命")
        result = calc.calculate_chart(1985, 4, 23, 22, "女")  # 亥时用22点
        
        if "error" in result:
            print(f"❌ 计算错误: {result['error']}")
            return False
        
        print("✅ 算法计算成功！")
        print()
        
        # 显示基本信息
        birth_info = result.get("birth_info", {})
        print("📊 基本信息:")
        print(f"📅 阳历: {birth_info.get('solar', '')}")
        print(f"🌙 农历: {birth_info.get('lunar', '')}")
        print(f"🎯 八字: {birth_info.get('chinese_date', '')}")
        print(f"🐂 生肖: {result.get('zodiac', '')}")
        print(f"⭐ 星座: {result.get('sign', '')}")
        print()
        
        # 显示十二宫配置
        palaces = result.get("palaces", {})
        print("🏰 十二宫配置 (修复后):")
        print("-" * 40)
        
        for palace_name, palace_info in palaces.items():
            position = palace_info.get("position", "")
            major_stars = palace_info.get("major_stars", [])
            minor_stars = palace_info.get("minor_stars", [])
            adjective_stars = palace_info.get("adjective_stars", [])
            is_body_palace = palace_info.get("is_body_palace", False)
            
            # 组合星曜显示
            all_stars = []
            if major_stars:
                all_stars.append(f"主星: {' '.join(major_stars)}")
            if minor_stars:
                all_stars.append(f"辅星: {' '.join(minor_stars)}")
            if adjective_stars:
                all_stars.append(f"杂曜: {' '.join(adjective_stars[:3])}")  # 只显示前3个杂曜
            
            stars_display = " | ".join(all_stars) if all_stars else "无星曜"
            body_mark = " [身宫]" if is_body_palace else ""
            
            print(f"{palace_name}({position}){body_mark}: {stars_display}")
        
        print()
        print("🎯 关键验证:")
        print("-" * 20)
        
        # 验证命宫
        ming_gong = palaces.get("命宫", {})
        ming_gong_stars = ming_gong.get("major_stars", [])
        ming_gong_position = ming_gong.get("position", "")
        
        print(f"命宫位置: {ming_gong_position} (期望: 巳)")
        print(f"命宫主星: {ming_gong_stars} (期望: ['巨门'])")
        print(f"命宫匹配: {'✅' if ming_gong_position == '巳' and '巨门' in ming_gong_stars else '❌'}")
        
        # 验证身宫
        for palace_name, palace_info in palaces.items():
            if palace_info.get("is_body_palace"):
                body_position = palace_info.get("position", "")
                body_stars = palace_info.get("major_stars", [])
                print(f"身宫位置: {palace_name}({body_position}) (期望: 兄弟宫(卯))")
                print(f"身宫主星: {body_stars} (期望: ['太阴'])")
                print(f"身宫匹配: {'✅' if palace_name == '兄弟宫' and body_position == '卯' and '太阴' in body_stars else '❌'}")
                break
        
        # 验证其他关键宫位
        key_palaces = {
            "夫妻宫": {"position": "辰", "stars": ["贪狼"]},
            "财帛宫": {"position": "午", "stars": ["廉贞", "天相"]},
            "疾厄宫": {"position": "未", "stars": ["天梁"]},
        }
        
        print()
        print("🔍 其他关键宫位验证:")
        for palace_name, expected in key_palaces.items():
            if palace_name in palaces:
                actual_position = palaces[palace_name].get("position", "")
                actual_stars = palaces[palace_name].get("major_stars", [])
                expected_position = expected["position"]
                expected_stars = expected["stars"]
                
                position_match = actual_position == expected_position
                stars_match = all(star in actual_stars for star in expected_stars)
                
                print(f"{palace_name}: {actual_position} {actual_stars} {'✅' if position_match and stars_match else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_algorithm()
    print()
    if success:
        print("🎉 修复测试完成！")
    else:
        print("💥 修复测试失败！")
