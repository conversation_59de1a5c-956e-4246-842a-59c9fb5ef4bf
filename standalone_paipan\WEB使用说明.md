# 独立排盘Web应用使用说明

## 🌐 Web应用概述

这是一个基于Flask的Web应用，提供了美观易用的界面来进行紫薇斗数+八字命理排盘。

## 🚀 启动应用

### 1. 安装依赖
```bash
pip install flask
```

### 2. 启动Web服务
```bash
cd standalone_paipan
python web_app.py
```

### 3. 访问地址
- 本地访问: http://localhost:5000
- 局域网访问: http://192.168.x.x:5000

## 📱 功能特点

### 🎨 界面设计
- ✅ 现代化渐变背景设计
- ✅ 响应式布局，支持手机和电脑
- ✅ 毛玻璃效果和阴影
- ✅ 流畅的动画过渡

### 📝 输入方式
1. **快速输入**: 支持多种格式
   - `1990-3-15-8`
   - `1990年3月15日8时`
   - 回车键快速提交

2. **详细输入**: 分别输入年月日时
   - 年份: 1900-2100
   - 月份: 1-12
   - 日期: 1-31
   - 小时: 0-23 (24小时制)
   - 性别: 男/女

### 📊 结果展示

#### 四个标签页
1. **📊 概览**: 基本信息和计算状态
2. **🌟 紫薇斗数**: 十二宫星曜分布
3. **🎯 八字命理**: 四柱八字和五行分析
4. **📄 完整报告**: 格式化的文本输出

#### 特色功能
- ✅ 宫位卡片式展示
- ✅ 身宫特殊标识
- ✅ 星曜分类显示（主星/辅星/煞星）
- ✅ 五行数量和强弱分析
- ✅ 日主分析

### 💾 操作功能
- **🔄 重新排盘**: 返回输入页面
- **💾 下载结果**: 下载为TXT文件
- **📋 复制结果**: 复制到剪贴板

## 🔧 技术特点

### 后端 (Flask)
- **路由设计**:
  - `/` - 首页输入
  - `/calculate` - 详细计算
  - `/result` - 结果展示
  - `/api/quick_calculate` - 快速计算API
  - `/api/list_results` - 结果列表API

- **数据处理**:
  - 表单验证
  - JSON API响应
  - 错误处理
  - 文件保存

### 前端 (HTML/CSS/JS)
- **响应式设计**: 支持各种屏幕尺寸
- **现代CSS**: 渐变、阴影、动画
- **JavaScript交互**: 
  - 表单提交
  - 标签页切换
  - 数据展示
  - 文件下载

## 📱 使用流程

### 1. 输入出生信息
- 打开 http://localhost:5000
- 选择快速输入或详细输入
- 输入出生年月日时和性别
- 点击"开始排盘"或"快速排盘"

### 2. 查看计算结果
- 自动跳转到结果页面
- 浏览四个标签页的内容
- 查看紫薇斗数十二宫
- 分析八字命理信息

### 3. 保存或分享
- 下载完整报告
- 复制结果文本
- 重新排盘计算

## 🎯 界面截图说明

### 首页界面
- 渐变紫色背景
- 居中的输入表单
- 快速输入和详细输入两种方式
- 性别选择按钮
- 加载动画效果

### 结果页面
- 顶部显示出生信息
- 四个功能标签页
- 卡片式信息展示
- 十二宫网格布局
- 底部操作按钮

## ⚠️ 注意事项

### 浏览器兼容性
- 推荐使用现代浏览器 (Chrome, Firefox, Safari, Edge)
- 支持移动端浏览器
- 需要JavaScript支持

### 网络要求
- 本地运行无需网络
- 局域网访问需要防火墙允许

### 数据安全
- 计算结果保存在本地
- 不会上传到外部服务器
- 支持离线使用

## 🔍 故障排除

### 常见问题

1. **无法启动Web应用**
   - 检查Flask是否安装: `pip install flask`
   - 检查端口5000是否被占用
   - 查看终端错误信息

2. **计算失败**
   - 检查输入格式是否正确
   - 确认主项目算法模块正常
   - 查看浏览器控制台错误

3. **页面显示异常**
   - 刷新页面
   - 清除浏览器缓存
   - 检查JavaScript是否启用

4. **无法下载文件**
   - 检查浏览器下载设置
   - 确认有写入权限
   - 尝试复制功能

## 🎉 总结

这个Web应用成功地将独立排盘功能包装成了一个美观易用的网页界面，具有以下优势：

- ✅ **界面美观**: 现代化设计，用户体验良好
- ✅ **功能完整**: 支持完整的排盘计算和结果展示
- ✅ **操作简单**: 多种输入方式，一键计算
- ✅ **结果详细**: 分类展示紫薇斗数和八字信息
- ✅ **移动友好**: 响应式设计，支持手机访问
- ✅ **本地运行**: 无需网络，数据安全

现在您可以通过浏览器轻松进行紫薇斗数+八字命理排盘了！
