#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紫薇斗数排盘计算
"""

def test_ziwei_calculation():
    """测试紫薇斗数排盘"""
    print("🔮 测试紫薇斗数排盘")
    print("=" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        
        # 创建算法实例
        calc = RealZiweiCalculator()
        
        # 测试数据：1988年6月1日午时男
        print("📅 测试数据：1988年6月1日午时男")
        result = calc.calculate_chart(1988, 6, 1, 11, "男")  # 午时=11点
        
        if "error" in result:
            print(f"❌ 排盘失败: {result['error']}")
            return False
        
        print("✅ 排盘成功！")
        
        # 显示基本信息
        birth_info = result.get("birth_info", {})
        print(f"\n📊 出生信息:")
        print(f"  公历: {birth_info.get('solar', '')}")
        print(f"  农历: {birth_info.get('lunar', '')}")
        print(f"  八字: {birth_info.get('chinese_date', '')}")
        print(f"  生肖: {result.get('zodiac', '')}")
        print(f"  星座: {result.get('sign', '')}")
        
        # 显示十二宫信息
        palaces = result.get("palaces", {})
        print(f"\n🏛️ 十二宫配置:")
        
        palace_order = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                       "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]
        
        for palace_name in palace_order:
            if palace_name in palaces:
                palace_info = palaces[palace_name]
                position = palace_info.get("position", "")
                major_stars = palace_info.get("major_stars", [])
                minor_stars = palace_info.get("minor_stars", [])
                body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""
                
                print(f"  • {palace_name}({position}){body_mark}:")
                if major_stars:
                    print(f"    主星: {' '.join(major_stars)}")
                if minor_stars:
                    print(f"    辅星: {' '.join(minor_stars)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_conversion():
    """测试时辰转换"""
    print("\n⏰ 测试时辰转换")
    print("=" * 30)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        engine = FortuneEngine()
        
        # 测试各个时辰
        time_tests = [
            ("子时", 0),
            ("丑时", 1),
            ("寅时", 3),
            ("卯时", 5),
            ("辰时", 7),
            ("巳时", 9),
            ("午时", 11),
            ("未时", 13),
            ("申时", 15),
            ("酉时", 17),
            ("戌时", 19),
            ("亥时", 21)
        ]
        
        print("时辰转换测试:")
        for time_name, expected_hour in time_tests:
            actual_hour = engine._convert_traditional_hour(time_name)
            status = "✅" if actual_hour == expected_hour else "❌"
            print(f"  {status} {time_name} -> {actual_hour} (期望: {expected_hour})")
        
        return True
        
    except Exception as e:
        print(f"❌ 时辰转换测试失败: {e}")
        return False

def test_image_generation():
    """测试图片生成"""
    print("\n🎨 测试图片生成")
    print("=" * 30)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        calc = RealZiweiCalculator()
        result = calc.calculate_chart(1988, 6, 1, 11, "男")
        
        if "error" in result:
            print(f"❌ 排盘失败: {result['error']}")
            return False
        
        # 创建引擎实例
        engine = FortuneEngine()
        
        # 测试图片生成
        algorithm_result = {"success": True, "data": result}
        chart_display = engine._generate_chart_display(algorithm_result)
        
        print("✅ 图片生成测试完成")
        
        # 检查是否包含图片路径
        if "图片已生成:" in chart_display:
            print("✅ 包含图片路径信息")
            # 提取图片路径
            lines = chart_display.split('\n')
            for line in lines:
                if "图片已生成:" in line:
                    image_path = line.split("图片已生成:")[-1].strip()
                    print(f"📸 图片路径: {image_path}")
        else:
            print("⚠️ 未生成图片，使用文本排盘")
        
        return True
        
    except Exception as e:
        print(f"❌ 图片生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 紫薇斗数排盘测试")
    print("=" * 60)
    
    # 测试1: 排盘计算
    calc_success = test_ziwei_calculation()
    
    # 测试2: 时辰转换
    time_success = test_time_conversion()
    
    # 测试3: 图片生成
    image_success = test_image_generation()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 测试总结:")
    print(f"  排盘计算: {'✅' if calc_success else '❌'}")
    print(f"  时辰转换: {'✅' if time_success else '❌'}")
    print(f"  图片生成: {'✅' if image_success else '❌'}")
    
    if calc_success and time_success and image_success:
        print("\n🎊 所有测试通过！紫薇斗数功能正常")
    else:
        print("\n⚠️ 部分测试失败，需要检查相关功能")

if __name__ == "__main__":
    main()
