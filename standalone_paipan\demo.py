#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立排盘模块演示脚本
展示如何使用独立排盘功能
"""

import sys
import os

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from simple_interface import SimplePaipanInterface

def demo_basic_usage():
    """演示基本使用方法"""
    print("🔮 独立排盘模块演示")
    print("=" * 60)
    
    # 创建接口
    interface = SimplePaipanInterface()
    
    print("\n📋 演示案例:")
    demo_cases = [
        {
            "name": "案例1: 1990年3月15日8时 女命",
            "year": 1990, "month": 3, "day": 15, "hour": 8, "gender": "女"
        },
        {
            "name": "案例2: 1985年10月15日14时 男命", 
            "year": 1985, "month": 10, "day": 15, "hour": 14, "gender": "男"
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n{'-'*60}")
        print(f"🎯 {case['name']}")
        print(f"{'-'*60}")
        
        try:
            # 计算排盘
            result = interface.calculate_and_save(
                case["year"], case["month"], case["day"], 
                case["hour"], case["gender"]
            )
            
            # 显示结果
            if result["success"]:
                print("✅ 排盘计算成功！")
                print(f"状态: {result['status']}")
                
                # 显示保存的文件
                saved_files = result.get("saved_files", {})
                print(f"\n📁 已保存 {len(saved_files)} 个文件:")
                for file_type, filepath in saved_files.items():
                    filename = os.path.basename(filepath)
                    print(f"  {file_type.upper()}: {filename}")
                
                # 显示部分输出内容
                formatted_output = result["formatted_output"]
                lines = formatted_output.split('\n')
                print(f"\n📄 输出内容预览 (前20行):")
                for line in lines[:20]:
                    print(line)
                if len(lines) > 20:
                    print("  ... (更多内容请查看保存的文件)")
                    
            else:
                print("❌ 排盘计算失败")
                print(f"错误: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 演示失败: {e}")

def demo_quick_calculation():
    """演示快速计算功能"""
    print(f"\n{'-'*60}")
    print("🚀 快速计算功能演示")
    print(f"{'-'*60}")
    
    interface = SimplePaipanInterface()
    
    # 测试不同的输入格式
    test_inputs = [
        ("1992-8-20-16", "女"),
        ("1988年6月1日12时", "男"),
        ("2000-1-1-0", "男")
    ]
    
    for birth_string, gender in test_inputs:
        print(f"\n输入: '{birth_string}' {gender}")
        try:
            result = interface.quick_calculate(birth_string, gender)
            if result["success"]:
                print(f"✅ 解析成功: {result['status']}")
                saved_files = result.get("saved_files", {})
                print(f"📁 保存了 {len(saved_files)} 个文件")
            else:
                print(f"❌ 失败: {result.get('error', '未知错误')}")
        except Exception as e:
            print(f"❌ 异常: {e}")

def demo_file_management():
    """演示文件管理功能"""
    print(f"\n{'-'*60}")
    print("📂 文件管理功能演示")
    print(f"{'-'*60}")
    
    interface = SimplePaipanInterface()
    
    # 显示已保存的文件
    print("查看已保存的排盘结果:")
    interface.list_saved_results()

def demo_error_handling():
    """演示错误处理"""
    print(f"\n{'-'*60}")
    print("⚠️ 错误处理演示")
    print(f"{'-'*60}")
    
    interface = SimplePaipanInterface()
    
    # 测试无效输入
    invalid_inputs = [
        ("1990-13-1-12", "男", "无效月份"),
        ("1990-1-32-12", "男", "无效日期"),
        ("invalid-date", "男", "无效格式")
    ]
    
    for birth_string, gender, description in invalid_inputs:
        print(f"\n测试 {description}: '{birth_string}'")
        try:
            result = interface.quick_calculate(birth_string, gender)
            if not result["success"]:
                print(f"✅ 正确识别错误: {result.get('error', '未知错误')}")
            else:
                print("⚠️ 意外成功")
        except Exception as e:
            print(f"✅ 正确抛出异常: {e}")

def main():
    """主演示函数"""
    try:
        demo_basic_usage()
        demo_quick_calculation()
        demo_file_management()
        demo_error_handling()
        
        print(f"\n{'='*60}")
        print("🎉 演示完成！")
        print("💡 提示:")
        print("  - 所有结果已保存到 paipan_outputs/ 目录")
        print("  - 可以直接运行 python simple_interface.py 进入交互模式")
        print("  - 查看 README.md 了解更多使用方法")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
