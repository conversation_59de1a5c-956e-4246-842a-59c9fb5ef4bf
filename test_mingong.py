#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命宫问题专项测试
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_mingong_question():
    print('🏛️ 命宫问题专项测试...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = 'test_mingong_001'
        
        # 第一步：提供完整信息
        print('\n📝 第一步：提供完整生辰信息')
        result1 = await coordinator.handle_user_message(
            session_id, 
            '我是1988年6月1日午时出生的男命，想算命'
        )
        
        print(f'✅ 第一步响应: {result1.get("success")}')
        
        # 等待后台分析启动
        print('\n⏳ 等待5秒让后台分析启动...')
        time.sleep(5)
        
        # 检查状态
        session_state = master_agent.get_session_state(session_id)
        result_id = session_state.get("result_id")
        print(f'📊 状态: 阶段={session_state.get("stage")}, result_id={result_id is not None}')
        
        if result_id:
            progress = master_agent.get_analysis_progress(result_id)
            print(f'📈 分析进度: {progress}')
        
        # 第二步：测试命宫问题
        print('\n🏛️ 第二步：询问命宫特点')
        result2 = await coordinator.handle_user_message(
            session_id, 
            '我的命宫有什么特点？'
        )
        
        print(f'✅ 第二步响应: {result2.get("success")}')
        response2 = result2.get("response", "")
        print(f'响应长度: {len(response2)}字')
        print(f'响应预览: {response2[:300]}...')
        
        # 检查是否基于真实分析
        if "命盘" in response2 or "紫薇" in response2 or "天相" in response2:
            print('🎯 ✅ 基于真实分析回答')
        elif "需要结合您的具体生辰信息" in response2:
            print('🎯 ❌ 仍然是通用回答，修复失败')
        else:
            print('🎯 ❓ 回答类型不确定')
        
        # 检查紫薇术语
        ziwei_terms = ["命宫", "紫薇", "天府", "天相", "太阳", "太阴", "贪狼", "巨门", "天同", "廉贞", "天梁", "七杀", "破军"]
        found_terms = [term for term in ziwei_terms if term in response2]
        print(f'🔍 包含紫薇术语: {found_terms}')
        
        print('\n🎉 命宫问题测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mingong_question())
