#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合婚服务 - 双人合婚分析
"""

import sys
import os
from typing import Optional

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.birth_info import BirthInfo
from models.compatibility_result import CompatibilityResult
from services.chart_service import ChartService
from utils.simple_logger import get_logger
from utils.cache_manager import get_cache

try:
    from utils.simple_llm_client import get_llm_client
except ImportError:
    get_llm_client = None

logger = get_logger()
cache = get_cache()

class CompatibilityService:
    """合婚服务"""
    
    def __init__(self):
        """初始化合婚服务"""
        self.chart_service = ChartService()
        try:
            if get_llm_client:
                self.llm_client = get_llm_client()
            else:
                self.llm_client = None
            logger.info("合婚服务初始化完成")
        except Exception as e:
            logger.error(f"合婚服务初始化失败: {e}")
            self.llm_client = None
    
    def analyze_compatibility(self, person1_info: BirthInfo, person2_info: BirthInfo) -> CompatibilityResult:
        """
        分析两人合婚兼容性
        
        Args:
            person1_info: 第一人生辰信息
            person2_info: 第二人生辰信息
            
        Returns:
            合婚分析结果
        """
        logger.info(f"💑 开始合婚分析: {person1_info.to_display_string()} & {person2_info.to_display_string()}")
        
        # 创建合婚结果对象
        compatibility_result = CompatibilityResult(
            person1_info=person1_info,
            person2_info=person2_info
        )
        
        try:
            # 1. 生成两人的排盘
            person1_chart = self.chart_service.generate_chart(person1_info)
            person2_chart = self.chart_service.generate_chart(person2_info)
            
            if not person1_chart.success:
                compatibility_result.success = False
                compatibility_result.error_message = f"第一人排盘失败: {person1_chart.error_message}"
                return compatibility_result
            
            if not person2_chart.success:
                compatibility_result.success = False
                compatibility_result.error_message = f"第二人排盘失败: {person2_chart.error_message}"
                return compatibility_result
            
            compatibility_result.person1_chart = person1_chart
            compatibility_result.person2_chart = person2_chart
            
            logger.info("✅ 双人排盘生成成功")
            
            # 2. 多维度兼容性分析
            self._analyze_personality_compatibility(compatibility_result)
            self._analyze_career_compatibility(compatibility_result)
            self._analyze_wealth_compatibility(compatibility_result)
            self._analyze_family_compatibility(compatibility_result)
            
            # 3. 计算综合兼容性评分
            self._calculate_overall_score(compatibility_result)
            
            # 4. 生成综合分析报告
            if self.llm_client:
                compatibility_result.analysis_content = self._generate_analysis_report(compatibility_result)
            else:
                compatibility_result.analysis_content = "LLM客户端不可用，无法生成详细分析"
            
            logger.info(f"🎉 合婚分析完成，兼容性评分: {compatibility_result.compatibility_score:.1f}")
            return compatibility_result
            
        except Exception as e:
            logger.error(f"❌ 合婚分析异常: {e}")
            compatibility_result.success = False
            compatibility_result.error_message = f"合婚分析异常: {str(e)}"
            return compatibility_result
    
    def _analyze_personality_compatibility(self, result: CompatibilityResult):
        """分析性格兼容性"""
        try:
            # 基于紫薇命宫分析性格匹配度
            person1_palace = result.person1_chart.ziwei_chart.get_main_palace() if result.person1_chart.ziwei_chart else {}
            person2_palace = result.person2_chart.ziwei_chart.get_main_palace() if result.person2_chart.ziwei_chart else {}
            
            # 简化的性格匹配算法
            score = 75.0  # 基础分数
            
            # 这里可以添加更复杂的性格匹配逻辑
            
            result.add_dimension_analysis("性格兼容", score, "性格方面基本匹配，需要相互理解和包容")
            
        except Exception as e:
            logger.error(f"性格兼容性分析失败: {e}")
            result.add_dimension_analysis("性格兼容", 60.0, "性格兼容性分析失败")
    
    def _analyze_career_compatibility(self, result: CompatibilityResult):
        """分析事业兼容性"""
        try:
            # 基于八字和紫薇分析事业匹配度
            score = 70.0  # 基础分数
            
            result.add_dimension_analysis("事业兼容", score, "事业发展方向基本一致，可以相互支持")
            
        except Exception as e:
            logger.error(f"事业兼容性分析失败: {e}")
            result.add_dimension_analysis("事业兼容", 60.0, "事业兼容性分析失败")
    
    def _analyze_wealth_compatibility(self, result: CompatibilityResult):
        """分析财运兼容性"""
        try:
            # 基于财帛宫分析财运匹配度
            score = 80.0  # 基础分数
            
            result.add_dimension_analysis("财运兼容", score, "财运方面比较匹配，有利于共同发展")
            
        except Exception as e:
            logger.error(f"财运兼容性分析失败: {e}")
            result.add_dimension_analysis("财运兼容", 60.0, "财运兼容性分析失败")
    
    def _analyze_family_compatibility(self, result: CompatibilityResult):
        """分析家庭兼容性"""
        try:
            # 基于夫妻宫和子女宫分析家庭匹配度
            score = 85.0  # 基础分数
            
            result.add_dimension_analysis("家庭兼容", score, "家庭观念比较一致，有利于建立和谐家庭")
            
        except Exception as e:
            logger.error(f"家庭兼容性分析失败: {e}")
            result.add_dimension_analysis("家庭兼容", 60.0, "家庭兼容性分析失败")
    
    def _calculate_overall_score(self, result: CompatibilityResult):
        """计算综合兼容性评分"""
        dimension_scores = result.get_dimension_scores()
        
        if dimension_scores:
            # 加权平均计算
            weights = {
                "性格兼容": 0.3,
                "事业兼容": 0.2,
                "财运兼容": 0.2,
                "家庭兼容": 0.3
            }
            
            total_score = 0.0
            total_weight = 0.0
            
            for dimension, score in dimension_scores.items():
                weight = weights.get(dimension, 0.25)
                total_score += score * weight
                total_weight += weight
            
            if total_weight > 0:
                result.compatibility_score = total_score / total_weight
            else:
                result.compatibility_score = 60.0
        else:
            result.compatibility_score = 60.0
    
    def _generate_analysis_report(self, result: CompatibilityResult) -> str:
        """生成综合分析报告"""
        if not self.llm_client:
            return "LLM客户端不可用，无法生成详细分析报告"
        
        try:
            system_prompt = """你是一位专业的命理合婚分析师。请根据两人的命理信息进行专业的合婚分析。

要求：
1. 分析内容要专业准确，基于传统命理理论
2. 分析长度控制在2000-3000字
3. 既要分析优势，也要指出需要注意的方面
4. 提供具体的相处建议和指导
5. 语言通俗易懂，避免过于晦涩的术语"""
            
            person1_str = result.person1_info.to_display_string()
            person2_str = result.person2_info.to_display_string()
            
            user_prompt = f"""请分析以下两人的合婚兼容性：

【第一人】: {person1_str}
【第二人】: {person2_str}
【兼容性评分】: {result.compatibility_score:.1f}分

【各维度评分】:
"""
            
            for dimension, data in result.dimensions.items():
                user_prompt += f"- {dimension}: {data['score']:.1f}分\n"
            
            user_prompt += "\n请提供详细的合婚分析和建议。"
            
            analysis = self.llm_client.chat(user_prompt, system_prompt)
            
            if analysis:
                return analysis
            else:
                return "合婚分析生成失败，请重试"
                
        except Exception as e:
            logger.error(f"LLM合婚分析失败: {e}")
            return f"合婚分析失败: {str(e)}"
