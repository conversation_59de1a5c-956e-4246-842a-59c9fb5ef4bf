#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成专业的紫薇斗数+八字融合图表
参考网上专业排盘样式
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Circle, Rectangle, FancyBboxPatch
import numpy as np
from matplotlib import font_manager
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_professional_chart():
    """生成专业的紫薇斗数图表"""
    print("🎨 生成专业紫薇斗数+八字融合图表")
    print("=" * 50)
    
    try:
        # 获取融合数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        # 创建更大的画布
        fig, ax = plt.subplots(1, 1, figsize=(20, 16))
        ax.set_xlim(-12, 12)
        ax.set_ylim(-10, 10)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 设置专业背景
        fig.patch.set_facecolor('#1a1a2e')
        
        # 1. 绘制标题区域
        draw_title_section(ax, result)
        
        # 2. 绘制专业的12宫格布局
        draw_professional_palaces(ax, result)
        
        # 3. 在宫格中融入八字和其他信息
        draw_integrated_info(ax, result)
        
        # 4. 添加专业装饰元素
        draw_decorative_elements(ax)
        
        # 保存图片
        output_file = "professional_chart.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                   facecolor='#1a1a2e', edgecolor='none')
        
        print(f"✅ 专业图表生成成功: {output_file}")
        plt.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def draw_title_section(ax, result):
    """绘制标题区域"""
    birth_info = result.get("birth_info", {})
    
    # 主标题
    ax.text(0, 9, "紫薇斗数命盘", ha='center', va='center', 
           fontsize=24, fontweight='bold', color='#ffd700')
    
    # 副标题 - 出生信息
    datetime_str = birth_info.get('datetime', '')
    lunar_str = birth_info.get('lunar', '')
    zodiac = birth_info.get('zodiac', '')
    
    ax.text(0, 8.2, f"{datetime_str} ({zodiac}年)", ha='center', va='center', 
           fontsize=16, color='#e6e6fa')
    ax.text(0, 7.7, f"农历: {lunar_str}", ha='center', va='center', 
           fontsize=14, color='#e6e6fa')

def draw_professional_palaces(ax, result):
    """绘制专业的12宫格布局"""
    ziwei_data = result.get("ziwei_analysis", {})
    palaces = ziwei_data.get("palaces", {})
    
    # 12宫的专业布局（3x4网格，中间空出）
    palace_positions = {
        "命宫": (1, 1), "兄弟宫": (2, 1), "夫妻宫": (3, 1), "子女宫": (4, 1),
        "父母宫": (1, 2), "福德宫": (4, 2),
        "田宅宫": (1, 3), "官禄宫": (4, 3),
        "奴仆宫": (1, 4), "迁移宫": (2, 4), "疾厄宫": (3, 4), "财帛宫": (4, 4)
    }
    
    # 宫格大小和位置
    cell_width = 4.5
    cell_height = 3.5
    start_x = -9
    start_y = 5
    
    for palace_name, (col, row) in palace_positions.items():
        x = start_x + (col - 1) * cell_width
        y = start_y - (row - 1) * cell_height
        
        # 绘制宫格
        draw_palace_cell(ax, x, y, cell_width, cell_height, palace_name, palaces.get(palace_name, {}))

def draw_palace_cell(ax, x, y, width, height, palace_name, palace_data):
    """绘制单个宫格"""
    # 宫格边框 - 使用渐变色效果
    rect = FancyBboxPatch((x, y), width, height,
                         boxstyle="round,pad=0.1",
                         linewidth=2, 
                         edgecolor='#4169e1',
                         facecolor='#191970',
                         alpha=0.8)
    ax.add_patch(rect)
    
    # 宫位名称（左上角）
    ax.text(x + 0.3, y + height - 0.4, palace_name, 
           ha='left', va='top', fontsize=12, 
           fontweight='bold', color='#ffd700')
    
    # 地支（右上角）
    position = palace_data.get("position", "")
    if position:
        ax.text(x + width - 0.3, y + height - 0.4, position, 
               ha='right', va='top', fontsize=11, 
               fontweight='bold', color='#87ceeb')
    
    # 主星（中央偏上）
    major_stars = palace_data.get("major_stars", [])
    if major_stars:
        # 主星用较大字体显示
        main_star = major_stars[0]
        ax.text(x + width/2, y + height*0.7, main_star, 
               ha='center', va='center', fontsize=14, 
               fontweight='bold', color='#ff6347')
        
        # 其他主星
        if len(major_stars) > 1:
            other_stars = "、".join(major_stars[1:3])  # 最多再显示2个
            ax.text(x + width/2, y + height*0.5, other_stars, 
                   ha='center', va='center', fontsize=10, 
                   color='#ff6347')
    
    # 副星（下方）
    minor_stars = palace_data.get("minor_stars", [])
    if minor_stars:
        minor_text = "、".join(minor_stars[:4])  # 最多显示4个副星
        ax.text(x + width/2, y + height*0.3, minor_text, 
               ha='center', va='center', fontsize=8, 
               color='#98fb98')
    
    # 四化（左下角）
    transformations = palace_data.get("transformations", [])
    if transformations:
        trans_text = "".join(transformations[:2])  # 最多显示2个四化
        ax.text(x + 0.3, y + 0.3, trans_text, 
               ha='left', va='bottom', fontsize=8, 
               color='#ffa500')

def draw_integrated_info(ax, result):
    """在中央区域绘制八字和融合信息"""
    # 中央区域坐标
    center_x, center_y = 0, 0
    center_width = 9
    center_height = 7
    
    # 中央背景
    center_rect = FancyBboxPatch((center_x - center_width/2, center_y - center_height/2), 
                                center_width, center_height,
                                boxstyle="round,pad=0.2",
                                linewidth=3, 
                                edgecolor='#8a2be2',
                                facecolor='#2f2f4f',
                                alpha=0.9)
    ax.add_patch(center_rect)
    
    # 八字四柱（上半部分）
    draw_bazi_in_center(ax, result, center_y + 1.5)
    
    # 五行分析（下半部分）
    draw_wuxing_in_center(ax, result, center_y - 1.5)
    
    # 融合验证（底部）
    draw_validation_in_center(ax, result, center_y - 3)

def draw_bazi_in_center(ax, result, y_pos):
    """在中央绘制八字信息"""
    bazi_data = result.get("bazi_analysis", {})
    
    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")
        
        # 八字标题
        ax.text(0, y_pos + 1, "八字命理", ha='center', va='center', 
               fontsize=16, fontweight='bold', color='#ffd700')
        
        # 分割四柱
        pillars = chinese_date.split()
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        
        for i, (name, pillar) in enumerate(zip(pillar_names, pillars)):
            x = -3 + i * 2
            
            # 柱名
            ax.text(x, y_pos + 0.3, name, ha='center', va='center', 
                   fontsize=10, color='#e6e6fa')
            
            # 天干
            if len(pillar) >= 1:
                ax.text(x, y_pos, pillar[0], ha='center', va='center', 
                       fontsize=14, fontweight='bold', color='#ff6347')
            
            # 地支
            if len(pillar) >= 2:
                ax.text(x, y_pos - 0.4, pillar[1], ha='center', va='center', 
                       fontsize=14, fontweight='bold', color='#87ceeb')

def draw_wuxing_in_center(ax, result, y_pos):
    """在中央绘制五行信息"""
    bazi_data = result.get("bazi_analysis", {})
    
    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})
        
        # 五行标题
        ax.text(0, y_pos + 0.8, "五行分析", ha='center', va='center', 
               fontsize=14, fontweight='bold', color='#ffd700')
        
        elements = ["木", "火", "土", "金", "水"]
        colors = ["#32cd32", "#ff4500", "#daa520", "#c0c0c0", "#4169e1"]
        
        for i, (element, color) in enumerate(zip(elements, colors)):
            x = -4 + i * 2
            count = wuxing_count.get(element, 0)
            
            # 五行圆圈
            circle = Circle((x, y_pos), 0.3, 
                          facecolor=color, edgecolor='white', 
                          linewidth=2, alpha=0.8)
            ax.add_patch(circle)
            
            # 五行名称
            ax.text(x, y_pos, element, ha='center', va='center', 
                   fontsize=12, fontweight='bold', color='white')
            
            # 数量
            ax.text(x, y_pos - 0.6, f"{count:.1f}", ha='center', va='center', 
                   fontsize=10, color=color)

def draw_validation_in_center(ax, result, y_pos):
    """在中央绘制验证信息"""
    fusion = result.get("fusion_analysis", {})
    
    if "cross_validation" in fusion:
        validation = fusion["cross_validation"]
        confidence = validation.get("confidence_level", 0)
        
        # 验证标题
        ax.text(0, y_pos + 0.3, f"交叉验证: {confidence:.0%}", 
               ha='center', va='center', fontsize=12, 
               fontweight='bold', color='#32cd32')

def draw_decorative_elements(ax):
    """绘制装饰元素"""
    # 四个角的装饰
    corners = [(-11, 8), (11, 8), (-11, -8), (11, -8)]
    
    for x, y in corners:
        # 装饰性星星
        star_x = [x, x+0.3, x+0.6, x+0.3, x, x-0.3, x-0.6, x-0.3, x]
        star_y = [y+0.6, y+0.3, y, y-0.3, y-0.6, y-0.3, y, y+0.3, y+0.6]
        ax.plot(star_x, star_y, color='#ffd700', linewidth=2, alpha=0.7)

def main():
    """主函数"""
    print("🎨 专业紫薇斗数图表生成器")
    print("=" * 60)
    
    success = generate_professional_chart()
    
    if success:
        print("\n✅ 专业图表生成成功！")
        print("📁 文件保存为: professional_chart.png")
        print("🖼️ 专业特点:")
        print("  - 深色专业背景")
        print("  - 传统12宫格布局")
        print("  - 主星、副星、四化完整显示")
        print("  - 中央融合八字和五行信息")
        print("  - 专业的颜色搭配和字体")
        print("  - 装饰性元素增强视觉效果")
    else:
        print("\n❌ 专业图表生成失败")

if __name__ == "__main__":
    main()
