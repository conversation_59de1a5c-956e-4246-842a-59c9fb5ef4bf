#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后台Agent修复效果
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_backend_fix():
    """测试后台Agent的修复效果"""
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        print('🔧 测试后台Agent修复效果')
        print('=' * 50)
        
        agent = FortuneCalculatorAgent()
        
        # 模拟数据
        raw_data = {
            'palaces': {
                '命宫': {'position': '亥', 'major_stars': ['天相'], 'minor_stars': []},
                '财帛宫': {'position': '未', 'major_stars': ['天府'], 'minor_stars': ['左辅', '右弼']},
                '夫妻宫': {'position': '酉', 'major_stars': ['紫微', '贪狼'], 'minor_stars': []}
            }
        }
        
        birth_info = {'year': '1990', 'month': '3', 'day': '15', 'hour': '辰时', 'gender': '女'}
        
        print(f'📝 测试数据: {birth_info}')
        
        # 测试单个角度分析（财富分析）
        print(f'\n💰 测试财富分析（修复后）:')
        start_time = time.time()
        
        result = await agent._analyze_single_angle(
            '财富分析', 'wealth_fortune', '财运状况、理财投资与财富积累',
            raw_data, birth_info, '紫薇斗数'
        )
        
        end_time = time.time()
        
        print(f'✅ 财富分析完成')
        print(f'📊 字数: {len(result)}')
        print(f'⏱️ 耗时: {end_time - start_time:.1f}秒')
        print(f'📝 内容预览: {result[:500]}...')
        
        # 检查是否包含4个段落
        sections = ['基础分析', '详细解读', '实用指导', '注意事项']
        found_sections = []
        for section in sections:
            if section in result:
                found_sections.append(section)
        
        print(f'\n📋 段落检查:')
        for section in sections:
            status = "✅" if section in found_sections else "❌"
            print(f'  {status} {section}')
        
        print(f'\n🎯 修复效果评估:')
        if len(result) >= 3000:
            print(f'  ✅ 字数达标: {len(result)}字 >= 3000字')
        else:
            print(f'  ❌ 字数不足: {len(result)}字 < 3000字')
        
        if len(found_sections) >= 3:
            print(f'  ✅ 结构完整: {len(found_sections)}/4个段落')
        else:
            print(f'  ❌ 结构不完整: {len(found_sections)}/4个段落')
        
        if end_time - start_time < 300:  # 5分钟内
            print(f'  ✅ 速度合理: {end_time - start_time:.1f}秒 < 300秒')
        else:
            print(f'  ⚠️ 速度较慢: {end_time - start_time:.1f}秒 >= 300秒')
        
        # 保存结果
        with open('test_wealth_fix_result.txt', 'w', encoding='utf-8') as f:
            f.write(f"修复后财富分析结果 ({len(result)}字):\n")
            f.write("=" * 50 + "\n")
            f.write(result)
        
        print(f'\n📁 结果已保存到 test_wealth_fix_result.txt')
        
        # 总结
        success_count = 0
        if len(result) >= 3000:
            success_count += 1
        if len(found_sections) >= 3:
            success_count += 1
        if end_time - start_time < 300:
            success_count += 1
        
        success_rate = success_count / 3 * 100
        
        print(f'\n🎯 修复成功率: {success_count}/3 ({success_rate:.1f}%)')
        
        if success_rate >= 100:
            print('🎉 修复完全成功！')
        elif success_rate >= 66:
            print('✅ 修复基本成功！')
        else:
            print('⚠️ 修复效果有限，需要进一步优化')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_backend_fix())
