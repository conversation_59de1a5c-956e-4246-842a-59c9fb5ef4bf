#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立排盘核心计算器
直接使用项目现有的融合引擎
"""

import sys
import os
import json
import datetime
from typing import Dict, Any, Optional

# 添加主项目路径以使用现有算法
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

class StandalonePaipanCalculator:
    """独立排盘计算器 - 基于现有融合引擎"""

    def __init__(self):
        """初始化计算器"""
        self.fusion_engine = None
        self._initialize_fusion_engine()

    def _initialize_fusion_engine(self):
        """初始化融合引擎"""
        try:
            # 直接使用项目现有的融合引擎
            from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
            self.fusion_engine = ZiweiBaziFusionEngine()
            print("✅ 融合引擎初始化成功")
        except Exception as e:
            print(f"❌ 融合引擎初始化失败: {e}")
            self.fusion_engine = None

    def calculate_complete_paipan(self, year: int, month: int, day: int,
                                 hour: int, gender: str = "男") -> Dict[str, Any]:
        """
        计算完整的排盘（紫薇+八字）

        Args:
            year: 出生年份
            month: 出生月份
            day: 出生日期
            hour: 出生小时
            gender: 性别（"男"或"女"）

        Returns:
            完整的排盘结果
        """
        print(f"🔮 开始计算排盘: {year}年{month}月{day}日{hour}时 {gender}")

        if not self.fusion_engine:
            return {
                "success": False,
                "error": "融合引擎未初始化",
                "calculation_time": datetime.datetime.now().isoformat(),
                "input_info": {
                    "year": year, "month": month, "day": day,
                    "hour": hour, "gender": gender,
                    "datetime_str": f"{year}年{month}月{day}日{hour}时"
                }
            }

        try:
            # 直接使用融合引擎计算
            result = self.fusion_engine.calculate_fusion_analysis(year, month, day, hour, gender)

            if result.get("success"):
                print("✅ 完整排盘计算成功")
                # 添加输入信息以保持兼容性
                result["input_info"] = {
                    "year": year, "month": month, "day": day,
                    "hour": hour, "gender": gender,
                    "datetime_str": f"{year}年{month}月{day}日{hour}时"
                }
                result["status"] = "完整排盘成功"
            else:
                print(f"❌ 排盘计算失败: {result.get('error', '未知错误')}")

            return result

        except Exception as e:
            error_msg = f"排盘计算异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "calculation_time": datetime.datetime.now().isoformat(),
                "input_info": {
                    "year": year, "month": month, "day": day,
                    "hour": hour, "gender": gender,
                    "datetime_str": f"{year}年{month}月{day}日{hour}时"
                }
            }

    def format_output(self, paipan_result: Dict[str, Any]) -> str:
        """
        格式化输出排盘结果（适配融合引擎数据结构）

        Args:
            paipan_result: 排盘计算结果

        Returns:
            格式化的文本输出
        """
        if not paipan_result.get("success"):
            error_msg = paipan_result.get("error", "未知错误")
            return f"排盘失败: {error_msg}"

        output_lines = []
        input_info = paipan_result.get("input_info", {})
        birth_info = paipan_result.get("birth_info", {})

        # 基本信息
        output_lines.append("=" * 60)
        output_lines.append("紫薇斗数+八字命理 完整排盘")
        output_lines.append("=" * 60)
        output_lines.append(f"出生时间: {birth_info.get('datetime', input_info.get('datetime_str', ''))}")
        output_lines.append(f"性别: {birth_info.get('gender', input_info.get('gender', ''))}")
        output_lines.append(f"计算时间: {paipan_result.get('calculation_time', '')}")
        output_lines.append("")

        # 紫薇斗数部分
        ziwei_analysis = paipan_result.get("ziwei_analysis", {})
        if ziwei_analysis and "error" not in ziwei_analysis:
            output_lines.append("【紫薇斗数排盘】")
            output_lines.append("-" * 40)

            # 基本信息
            output_lines.append(f"阳历: {birth_info.get('solar', '')}")
            output_lines.append(f"农历: {birth_info.get('lunar', '')}")

            # 从紫薇分析中获取八字信息
            ziwei_birth_info = ziwei_analysis.get("birth_info", {})
            if ziwei_birth_info:
                output_lines.append(f"干支: {ziwei_birth_info.get('chinese_date', '')}")

            output_lines.append(f"生肖: {birth_info.get('zodiac', '')}")
            output_lines.append(f"星座: {birth_info.get('sign', '')}")
            output_lines.append("")

            # 十二宫信息
            palaces = ziwei_analysis.get("palaces", {})
            if palaces:
                output_lines.append("十二宫星曜分布:")
                for palace_name, palace_info in palaces.items():
                    major_stars = palace_info.get("major_stars", [])
                    minor_stars = palace_info.get("minor_stars", [])
                    adjective_stars = palace_info.get("adjective_stars", [])

                    stars_display = []
                    if major_stars:
                        stars_display.append(f"主星: {', '.join(major_stars)}")
                    if minor_stars:
                        stars_display.append(f"辅星: {', '.join(minor_stars)}")
                    if adjective_stars:
                        stars_display.append(f"煞星: {', '.join(adjective_stars)}")

                    stars_text = " | ".join(stars_display) if stars_display else "无星曜"
                    body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""
                    position = palace_info.get("position", "")

                    output_lines.append(f"  {palace_name}({position}){body_mark}: {stars_text}")
            output_lines.append("")

        # 八字部分
        bazi_analysis = paipan_result.get("bazi_analysis", {})
        if bazi_analysis and bazi_analysis.get("success"):
            output_lines.append("【八字命理排盘】")
            output_lines.append("-" * 40)

            # 基本信息
            bazi_birth_info = bazi_analysis.get("birth_info", {})
            if bazi_birth_info:
                output_lines.append(f"出生时间: {bazi_birth_info.get('datetime', '')}")
                output_lines.append(f"农历: {bazi_birth_info.get('lunar', '')}")
                output_lines.append(f"生肖: {bazi_birth_info.get('zodiac', '')}")
                output_lines.append(f"星座: {bazi_birth_info.get('sign', '')}")

            # 八字信息 - 修复数据源
            bazi_info = bazi_analysis.get("bazi_info", {})
            if bazi_info:
                output_lines.append(f"八字: {bazi_info.get('chinese_date', '')}")
                output_lines.append(f"年柱: {bazi_info.get('year_pillar', '')}")
                output_lines.append(f"月柱: {bazi_info.get('month_pillar', '')}")
                output_lines.append(f"日柱: {bazi_info.get('day_pillar', '')}")
                output_lines.append(f"时柱: {bazi_info.get('hour_pillar', '')}")

            # 五行信息 - 修复数据源
            analysis = bazi_analysis.get("analysis", {})
            wuxing_info = analysis.get("wuxing", {})
            if wuxing_info:
                output_lines.append("")
                output_lines.append("五行分析:")

                # 五行数量统计
                count = wuxing_info.get("count", {})
                if count:
                    output_lines.append("  五行数量:")
                    for element, num in count.items():
                        output_lines.append(f"    {element}: {num}")

                # 五行强弱
                strength = wuxing_info.get("strength", {})
                if strength:
                    output_lines.append("  五行强弱:")
                    for element, level in strength.items():
                        output_lines.append(f"    {element}: {level}")

            # 日主信息
            day_master = analysis.get("day_master", {})
            if day_master:
                output_lines.append("")
                output_lines.append("日主分析:")
                output_lines.append(f"  日主: {day_master.get('gan', '')} ({day_master.get('element', '')})")
                output_lines.append(f"  身强弱: {day_master.get('strength', '')}")

            # 大运信息
            dayun = analysis.get("dayun", {})
            if dayun and dayun.get("dayun_list"):
                output_lines.append("")
                output_lines.append("大运信息:")
                dayun_list = dayun["dayun_list"][:3]  # 显示前3个大运
                for dayun_item in dayun_list:
                    ganzhi = dayun_item.get("ganzhi", "")
                    age_range = dayun_item.get("age_range", "")
                    shishen = dayun_item.get("shishen", "")
                    output_lines.append(f"  {ganzhi} ({age_range}): {shishen}")

            output_lines.append("")

        output_lines.append("=" * 60)
        output_lines.append("排盘完成")
        output_lines.append("=" * 60)

        return "\n".join(output_lines)
