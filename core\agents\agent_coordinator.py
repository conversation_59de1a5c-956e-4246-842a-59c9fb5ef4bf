#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent协调器 - 管理双Agent协作流程
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime

from .base_agent import BaseAgent, AgentMessage, AgentResponse, MessageType, AgentStatus, agent_registry

logger = logging.getLogger(__name__)

@dataclass
class CoordinationTask:
    """协调任务"""
    task_id: str
    session_id: str
    user_message: str
    task_type: str  # "fortune_telling", "chat", "question"
    status: str  # "pending", "processing", "completed", "failed"
    created_at: str = ""
    customer_agent_id: Optional[str] = None
    calculator_agent_id: Optional[str] = None
    customer_response: Optional[str] = None
    calculation_result: Optional[Dict[str, Any]] = None
    final_response: Optional[str] = None
    error: Optional[str] = None

    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

class AgentCoordinator:
    """Agent协调器 - 管理双Agent协作"""

    def __init__(self):
        """初始化协调器"""
        self.coordinator_id = "coordinator_001"
        self.active_tasks: Dict[str, CoordinationTask] = {}
        self.agent_assignments: Dict[str, str] = {}  # session_id -> agent_id

        # 性能统计
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_response_time": 0.0,
            "total_response_time": 0.0
        }

        # 配置
        self.config = {
            "max_concurrent_tasks": 20,
            "task_timeout": 60.0,
            "customer_agent_timeout": 10.0,
            "calculator_agent_timeout": 30.0,
            "enable_fallback": True
        }

        logger.info("Agent协调器初始化完成")

    async def handle_user_message(self, session_id: str, user_message: str,
                                 user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理用户消息的主入口"""
        start_time = time.time()
        task = None

        try:
            # 创建协调任务
            task = CoordinationTask(
                task_id=str(uuid.uuid4()),
                session_id=session_id,
                user_message=user_message,
                task_type=self._determine_task_type(user_message),
                status="pending"
            )

            self.active_tasks[task.task_id] = task
            self.stats["total_tasks"] += 1

            logger.info(f"开始处理用户消息，任务ID: {task.task_id}")

            # 根据任务类型选择处理策略
            if task.task_type == "fortune_telling":
                result = await self._handle_fortune_telling_task(task, user_context or {})
            elif task.task_type == "chat":
                result = await self._handle_chat_task(task, user_context or {})
            else:
                result = await self._handle_question_task(task, user_context or {})

            # 更新统计信息
            processing_time = time.time() - start_time
            self.stats["total_response_time"] += processing_time

            if result.get("success"):
                self.stats["completed_tasks"] += 1
                task.status = "completed"
            else:
                self.stats["failed_tasks"] += 1
                task.status = "failed"
                task.error = result.get("error")

            self.stats["average_response_time"] = (
                self.stats["total_response_time"] / self.stats["total_tasks"]
            )

            # 添加处理时间到结果
            result["processing_time"] = processing_time
            result["task_id"] = task.task_id

            return result

        except Exception as e:
            logger.error(f"处理用户消息失败: {e}")
            self.stats["failed_tasks"] += 1
            if task:
                task.status = "failed"
                task.error = str(e)
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，系统处理您的请求时遇到了问题，请稍后再试。"
            }
        finally:
            # 清理完成的任务
            if task and task.task_id in self.active_tasks:
                if task.status in ["completed", "failed"]:
                    del self.active_tasks[task.task_id]

    def _determine_task_type(self, user_message: str) -> str:
        """确定任务类型"""
        message_lower = user_message.lower()

        # 算命相关关键词
        fortune_keywords = [
            "算命", "紫薇", "斗数", "八字", "六爻", "占卜", "命理", "运势",
            "命盘", "生辰", "出生", "年月日", "时辰", "男", "女"
        ]

        # 问答相关关键词
        question_keywords = [
            "什么", "怎么", "如何", "为什么", "哪里", "谁", "何时", "多少"
        ]

        # 检查是否包含算命关键词
        if any(keyword in message_lower for keyword in fortune_keywords):
            return "fortune_telling"

        # 检查是否是问题
        if any(keyword in message_lower for keyword in question_keywords) or "?" in user_message or "？" in user_message:
            return "question"

        # 默认为聊天
        return "chat"

    async def _handle_fortune_telling_task(self, task: CoordinationTask,
                                         user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理算命任务 - 双Agent协作"""
        logger.info(f"处理算命任务: {task.task_id}")

        try:
            # 获取可用的Agent
            customer_agent = self._get_customer_agent()
            calculator_agent = self._get_calculator_agent()

            if not customer_agent or not calculator_agent:
                return {
                    "success": False,
                    "error": "Agent不可用",
                    "response": "抱歉，系统暂时无法提供服务，请稍后再试。"
                }

            task.customer_agent_id = customer_agent.agent_id
            task.calculator_agent_id = calculator_agent.agent_id
            task.status = "processing"

            # 并行执行两个Agent的任务
            customer_task = self._execute_customer_agent_task(
                customer_agent, task, user_context
            )
            calculator_task = self._execute_calculator_agent_task(
                calculator_agent, task, user_context
            )

            # 等待两个任务完成
            customer_result, calculator_result = await asyncio.gather(
                customer_task, calculator_task, return_exceptions=True
            )

            # 处理结果
            if isinstance(customer_result, Exception):
                logger.error(f"沟通Agent执行失败: {customer_result}")
                customer_result = {
                    "success": False,
                    "error": str(customer_result),
                    "response": "抱歉，处理您的请求时遇到了问题。"
                }

            if isinstance(calculator_result, Exception):
                logger.error(f"计算Agent执行失败: {calculator_result}")
                calculator_result = {
                    "success": False,
                    "error": str(calculator_result)
                }

            # 合并结果
            if customer_result.get("success") and calculator_result.get("success"):
                # 让沟通Agent基于计算结果生成最终回复
                final_response = await self._generate_final_response(
                    customer_agent, calculator_result.get("data", {}), task
                )

                return {
                    "success": True,
                    "response": final_response,
                    "calculation_result": calculator_result.get("data"),
                    "customer_response": customer_result.get("response"),
                    "agent_info": {
                        "customer_agent": customer_agent.agent_id,
                        "calculator_agent": calculator_agent.agent_id
                    }
                }
            else:
                # 处理失败情况
                error_msg = customer_result.get("error") or calculator_result.get("error")
                fallback_response = customer_result.get("response", "抱歉，无法完成您的算命请求。")

                return {
                    "success": False,
                    "error": error_msg,
                    "response": fallback_response
                }

        except Exception as e:
            logger.error(f"算命任务处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，算命服务暂时不可用，请稍后再试。"
            }

    async def _handle_chat_task(self, task: CoordinationTask,
                               user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理聊天任务 - 仅使用沟通Agent"""
        logger.info(f"处理聊天任务: {task.task_id}")

        try:
            customer_agent = self._get_customer_agent()
            if not customer_agent:
                return {
                    "success": False,
                    "error": "沟通Agent不可用",
                    "response": "抱歉，系统暂时无法提供服务。"
                }

            task.customer_agent_id = customer_agent.agent_id
            task.status = "processing"

            # 执行沟通Agent任务
            result = await self._execute_customer_agent_task(
                customer_agent, task, user_context
            )

            return result

        except Exception as e:
            logger.error(f"聊天任务处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，无法处理您的消息。"
            }

    async def _handle_question_task(self, task: CoordinationTask,
                                   user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理问答任务 - 智能选择Agent"""
        logger.info(f"处理问答任务: {task.task_id}")

        # 问答任务目前使用沟通Agent处理
        # 未来可以根据问题类型智能选择Agent
        return await self._handle_chat_task(task, user_context)

    async def _execute_customer_agent_task(self, agent: BaseAgent, task: CoordinationTask,
                                         user_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行沟通Agent任务"""
        try:
            message = AgentMessage(
                message_id=str(uuid.uuid4()),
                message_type=MessageType.COMMUNICATION_REQUEST,
                sender_id=self.coordinator_id,
                receiver_id=agent.agent_id,
                content={
                    "user_message": task.user_message,
                    "session_id": task.session_id,
                    "task_type": task.task_type,
                    "user_context": user_context
                },
                timestamp=datetime.now().isoformat(),
                timeout=self.config["customer_agent_timeout"]
            )

            response = await agent.process_message(message)

            if response.success:
                return {
                    "success": True,
                    "response": response.data.get("response", ""),
                    "data": response.data
                }
            else:
                return {
                    "success": False,
                    "error": response.error,
                    "response": "抱歉，无法处理您的请求。"
                }

        except Exception as e:
            logger.error(f"沟通Agent任务执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，处理您的请求时遇到了问题。"
            }

    async def _execute_calculator_agent_task(self, agent: BaseAgent, task: CoordinationTask,
                                           user_context: Dict[str, Any]) -> Dict[str, Any]:
        """执行计算Agent任务"""
        try:
            message = AgentMessage(
                message_id=str(uuid.uuid4()),
                message_type=MessageType.CALCULATION_REQUEST,
                sender_id=self.coordinator_id,
                receiver_id=agent.agent_id,
                content={
                    "user_message": task.user_message,
                    "session_id": task.session_id,
                    "user_context": user_context
                },
                timestamp=datetime.now().isoformat(),
                timeout=self.config["calculator_agent_timeout"]
            )

            response = await agent.process_message(message)

            if response.success:
                return {
                    "success": True,
                    "data": response.data
                }
            else:
                return {
                    "success": False,
                    "error": response.error
                }

        except Exception as e:
            logger.error(f"计算Agent任务执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _generate_final_response(self, customer_agent: BaseAgent,
                                     calculation_data: Dict[str, Any],
                                     task: CoordinationTask) -> str:
        """生成最终回复"""
        try:
            message = AgentMessage(
                message_id=str(uuid.uuid4()),
                message_type=MessageType.COMMUNICATION_REQUEST,
                sender_id=self.coordinator_id,
                receiver_id=customer_agent.agent_id,
                content={
                    "action": "explain_calculation_result",
                    "calculation_data": calculation_data,
                    "user_message": task.user_message,
                    "session_id": task.session_id
                },
                timestamp=datetime.now().isoformat()
            )

            response = await customer_agent.process_message(message)

            if response.success:
                return response.data.get("response", "算命分析已完成。")
            else:
                return "算命分析已完成，但解释生成失败。"

        except Exception as e:
            logger.error(f"生成最终回复失败: {e}")
            return "算命分析已完成。"

    def _get_customer_agent(self) -> Optional[BaseAgent]:
        """获取可用的沟通Agent"""
        agents = agent_registry.get_agents_by_type("CustomerServiceAgent")
        return agents[0] if agents else None

    def _get_calculator_agent(self) -> Optional[BaseAgent]:
        """获取可用的计算Agent"""
        agents = agent_registry.get_agents_by_type("FortuneCalculatorAgent")
        return agents[0] if agents else None

    def get_stats(self) -> Dict[str, Any]:
        """获取协调器统计信息"""
        return {
            **self.stats,
            "active_tasks": len(self.active_tasks),
            "agent_assignments": len(self.agent_assignments)
        }

    def get_active_tasks(self) -> List[CoordinationTask]:
        """获取活动任务列表"""
        return list(self.active_tasks.values())

    async def shutdown(self):
        """关闭协调器"""
        logger.info("Agent协调器开始关闭")

        # 等待所有活动任务完成
        if self.active_tasks:
            logger.info(f"等待 {len(self.active_tasks)} 个活动任务完成")
            # 这里可以添加等待逻辑

        # 清理资源
        self.active_tasks.clear()
        self.agent_assignments.clear()

        logger.info("Agent协调器已关闭")
