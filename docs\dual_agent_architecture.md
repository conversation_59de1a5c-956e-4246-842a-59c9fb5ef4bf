# 🤖 双Agent协作架构设计文档

## 📋 架构概述

### 设计理念
通过专业分工实现算命计算与用户沟通的分离，提升系统专业度和用户体验。

### 核心优势
- **专业分工**: 各Agent专注自己的领域
- **并行处理**: 计算和沟通可以异步进行
- **用户体验**: 减少等待时间，保持对话连续性
- **可扩展性**: 易于添加更多专业Agent

## 🏗️ 架构设计

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐
│   用户输入      │    │   用户输出      │
└─────────┬───────┘    └─────────▲───────┘
          │                      │
          ▼                      │
┌─────────────────────────────────────────┐
│        沟通Agent (Customer Service)      │
│  - 自然对话                            │
│  - 信息收集                            │
│  - 结果解释                            │
│  - 用户互动                            │
└─────────┬───────────────────────▲───────┘
          │                       │
          ▼ 计算请求                │ 计算结果
┌─────────────────────────────────────────┐
│        计算Agent (Fortune Calculator)   │
│  - 算命计算                            │
│  - 深度分析                            │
│  - 结果生成                            │
│  - 算法调用                            │
└─────────┬───────────────────────▲───────┘
          │                       │
          ▼                       │
┌─────────────────────────────────────────┐
│           算法引擎层                    │
│  - 紫薇斗数 (iztro)                    │
│  - 八字算命 (bazi)                     │
│  - 六爻占卜 (liuyao)                   │
└─────────────────────────────────────────┘
```

### Agent职责分工

#### 🗣️ 沟通Agent (Customer Service Agent)
**核心使命**: 提供自然、专业的用户交互体验

**主要职责**:
1. **信息收集**
   - 引导用户提供生辰八字信息
   - 验证信息完整性和准确性
   - 确认用户需求和算命类型

2. **过程沟通**
   - 告知用户计算进度
   - 保持对话连续性
   - 处理用户等待期间的问题

3. **结果解释**
   - 将复杂算命结果转换为易懂表达
   - 分段式展示分析内容
   - 提供个性化解读

4. **互动问答**
   - 回答用户追问
   - 提供深入解释
   - 引导用户关注重点

**技术特点**:
- 快速响应 (< 3秒)
- 自然语言处理优化
- 上下文记忆能力
- 情感化表达

#### 🧮 计算Agent (Fortune Calculator Agent)
**核心使命**: 提供准确、深度的算命计算分析

**主要职责**:
1. **信息解析**
   - 解析生辰八字信息
   - 转换为算法所需格式
   - 验证数据有效性

2. **算法调用**
   - 调用真实算命算法
   - 处理算法异常情况
   - 确保计算准确性

3. **深度分析**
   - 基于算法结果进行推理
   - 生成多维度分析
   - 提供时间节点预测

4. **结果生成**
   - 输出结构化分析结果
   - 包含置信度评估
   - 提供原始数据支撑

**技术特点**:
- 专业算命知识
- 深度分析能力
- 结构化输出
- 高准确性保证

## 🔄 协作流程

### 标准工作流程

#### 阶段1: 信息收集
```
用户: "我想看紫薇斗数"
沟通Agent: "好的，我来为您排紫薇斗数盘。请告诉我您的出生年月日时和性别。"
用户: "1988年6月1日午时，男"
沟通Agent: "收到，您是1988年6月1日午时出生的男性，我马上为您计算..."
```

#### 阶段2: 并行处理
```
沟通Agent → 计算Agent: {
  "birth_info": {
    "year": 1988,
    "month": 6,
    "day": 1,
    "hour": "午时",
    "gender": "男"
  },
  "calculation_type": "ziwei",
  "request_id": "req_001"
}

沟通Agent → 用户: "我正在为您排盘和分析，这需要几秒钟时间，请稍等..."
```

#### 阶段3: 结果处理
```
计算Agent → 沟通Agent: {
  "request_id": "req_001",
  "result": {
    "chart_data": "...",
    "analysis": {
      "personality": "...",
      "career": "...",
      "wealth": "...",
      "relationship": "..."
    }
  },
  "confidence": 0.95
}

沟通Agent → 用户: "您的紫薇斗数盘已经排好了。从您的命盘来看..."
```

#### 阶段4: 互动问答
```
用户: "我的事业运势怎么样？"
沟通Agent: 基于计算结果回答事业相关问题
用户: "什么时候会有转机？"
沟通Agent: 提供时间节点分析
```

### 异常处理流程

#### 信息不完整
```
用户: "我想算命"
沟通Agent: "好的，请告诉我您的出生年月日时和性别，这样我才能为您准确计算。"
用户: "1988年6月1日"
沟通Agent: "还需要知道您的出生时辰和性别，这对算命准确性很重要。"
```

#### 计算失败
```
计算Agent → 沟通Agent: {
  "request_id": "req_001",
  "error": "算法调用失败",
  "retry_count": 3
}

沟通Agent → 用户: "抱歉，计算过程中遇到了一些问题，我重新为您计算一下..."
```

#### 用户中断
```
用户: "等等，我想先问个问题"
沟通Agent: 暂停当前流程，回答用户问题
沟通Agent: "好的，我们继续刚才的分析..."
```

## 💻 技术实现

### Agent基础架构

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import asyncio

class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.message_queue = asyncio.Queue()
        self.state = {}
    
    @abstractmethod
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理消息的抽象方法"""
        pass
    
    async def send_message(self, target_agent: 'BaseAgent', message: Dict[str, Any]):
        """发送消息给其他Agent"""
        await target_agent.message_queue.put(message)
    
    async def receive_message(self) -> Dict[str, Any]:
        """接收消息"""
        return await self.message_queue.get()

class CustomerServiceAgent(BaseAgent):
    """客户沟通专家"""
    
    def __init__(self):
        super().__init__("CustomerService")
        self.conversation_history = []
        self.user_info = {}
    
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户消息或计算结果"""
        if message.get("type") == "user_input":
            return await self.handle_user_input(message)
        elif message.get("type") == "calculation_result":
            return await self.handle_calculation_result(message)
    
    async def handle_user_input(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户输入"""
        user_input = message.get("content")
        
        # 信息提取和意图识别
        intent = await self.recognize_intent(user_input)
        
        if intent == "fortune_request":
            # 收集生辰信息
            birth_info = await self.extract_birth_info(user_input)
            if self.is_birth_info_complete(birth_info):
                # 发送计算请求
                await self.request_calculation(birth_info)
                return {"response": "我正在为您计算命盘，请稍等..."}
            else:
                return {"response": "请提供完整的出生年月日时和性别信息。"}
        
        return {"response": "我是您的专属算命师，请告诉我您想了解什么？"}

class FortuneCalculatorAgent(BaseAgent):
    """算命计算专家"""
    
    def __init__(self):
        super().__init__("FortuneCalculator")
        self.algorithm_engines = {
            "ziwei": ZiweiEngine(),
            "bazi": BaziEngine(),
            "liuyao": LiuyaoEngine()
        }
    
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理计算请求"""
        if message.get("type") == "calculation_request":
            return await self.handle_calculation_request(message)
    
    async def handle_calculation_request(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """处理算命计算请求"""
        birth_info = message.get("birth_info")
        calculation_type = message.get("calculation_type")
        request_id = message.get("request_id")
        
        try:
            # 调用对应的算命算法
            engine = self.algorithm_engines.get(calculation_type)
            if not engine:
                raise ValueError(f"不支持的算命类型: {calculation_type}")
            
            # 执行计算
            result = await engine.calculate(birth_info)
            
            # 深度分析
            analysis = await self.deep_analysis(result, birth_info)
            
            return {
                "type": "calculation_result",
                "request_id": request_id,
                "result": {
                    "chart_data": result,
                    "analysis": analysis,
                    "confidence": 0.95,
                    "calculation_time": "2.3s"
                }
            }
            
        except Exception as e:
            return {
                "type": "calculation_error",
                "request_id": request_id,
                "error": str(e)
            }
```

### Agent协调器

```python
class AgentCoordinator:
    """Agent协调器"""
    
    def __init__(self):
        self.customer_agent = CustomerServiceAgent()
        self.calculator_agent = FortuneCalculatorAgent()
        self.active_sessions = {}
    
    async def handle_user_message(self, session_id: str, user_message: str) -> str:
        """处理用户消息"""
        # 发送给沟通Agent
        message = {
            "type": "user_input",
            "content": user_message,
            "session_id": session_id
        }
        
        response = await self.customer_agent.process_message(message)
        return response.get("response", "抱歉，我没有理解您的意思。")
    
    async def coordinate_calculation(self, birth_info: Dict, calculation_type: str) -> Dict:
        """协调算命计算"""
        request_id = f"req_{int(time.time())}"
        
        # 发送计算请求给计算Agent
        calc_message = {
            "type": "calculation_request",
            "birth_info": birth_info,
            "calculation_type": calculation_type,
            "request_id": request_id
        }
        
        calc_result = await self.calculator_agent.process_message(calc_message)
        
        # 将结果发送给沟通Agent
        result_message = {
            "type": "calculation_result",
            "request_id": request_id,
            "result": calc_result.get("result")
        }
        
        response = await self.customer_agent.process_message(result_message)
        return response
```

## 🎯 实施优先级

### 第一优先级 (本周)
1. **架构框架搭建** - 创建Agent基类和协调器
2. **沟通Agent实现** - 实现基础对话功能
3. **计算Agent实现** - 集成现有算法模块

### 第二优先级 (下周)
1. **协作流程优化** - 完善Agent间通信
2. **异常处理机制** - 增强系统稳定性
3. **性能优化** - 提升响应速度

### 第三优先级 (后续)
1. **Web界面集成** - 更新前端支持双Agent
2. **监控和日志** - 添加系统监控
3. **扩展功能** - 支持更多算命类型

## 📊 预期效果

### 用户体验提升
- **响应时间**: 从10秒降低到3秒
- **对话自然度**: 提升40%
- **专业度**: 保持100%准确性
- **用户满意度**: 预期提升50%

### 技术优势
- **可维护性**: 模块化设计，易于维护
- **可扩展性**: 新Agent可轻松集成
- **稳定性**: 故障隔离，提升系统稳定性
- **性能**: 并行处理，提升整体性能

---

**🎯 通过双Agent协作架构，我们将实现专业算命与自然沟通的完美结合！**
