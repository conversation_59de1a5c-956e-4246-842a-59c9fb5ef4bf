#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Few-shot Learning集成效果
"""

import sys
import os
sys.path.append('.')

def test_fewshot_samples():
    """测试Few-shot样本管理器"""
    print("📚 测试Few-shot样本管理器")
    print("-" * 50)
    
    try:
        from core.conversation.fewshot_samples import fewshot_manager, format_fewshot_prompt
        
        # 测试样本获取
        test_messages = [
            ("我想知道我的工作运势", "career"),
            ("帮我看看财运怎么样", "wealth"),
            ("我的感情运势如何", "love"),
            ("请分析我的整体运势", "general")
        ]
        
        for message, expected_category in test_messages:
            print(f"\n用户消息: {message}")
            
            # 获取相关样本
            samples = fewshot_manager.get_relevant_samples(message, expected_category, max_samples=2)
            print(f"匹配样本数: {len(samples)}")
            
            for i, sample in enumerate(samples, 1):
                print(f"  样本{i}: [{sample['category']}] {sample['user'][:30]}...")
            
            # 生成完整prompt
            prompt = format_fewshot_prompt(message, expected_category, max_samples=2)
            print(f"Prompt长度: {len(prompt)} 字符")
            
            # 检查prompt中是否包含关键元素
            has_examples = "示例" in prompt
            has_requirements = "要求" in prompt
            has_professional_terms = "根据你的生辰八字" in prompt
            
            print(f"  包含示例: {'✅' if has_examples else '❌'}")
            print(f"  包含要求: {'✅' if has_requirements else '❌'}")
            print(f"  包含专业术语: {'✅' if has_professional_terms else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Few-shot样本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fewshot_llm_client():
    """测试Few-shot LLM客户端"""
    print("\n🤖 测试Few-shot LLM客户端")
    print("-" * 50)
    
    try:
        from core.nlu.llm_client import LLMClient
        
        client = LLMClient()
        
        # 测试不同类别的Few-shot调用
        test_cases = [
            ("我最近工作不顺，请帮我分析一下", "career"),
            ("我想知道我的财运如何", "wealth"),
            ("我的感情生活怎么样", "love"),
            ("请帮我看看整体运势", "general")
        ]
        
        success_count = 0
        
        for message, category in test_cases:
            print(f"\n测试消息: {message}")
            print(f"类别: {category}")
            
            try:
                response = client.fewshot_chat(
                    user_message=message,
                    category=category,
                    temperature=0.7,
                    max_tokens=500
                )
                
                if response:
                    print(f"✅ 响应成功: {len(response)} 字符")
                    print(f"   内容预览: {response[:100]}...")
                    
                    # 检查专业话术特征
                    professional_indicators = [
                        "根据", "建议", "保持", "通过", "将会"
                    ]
                    
                    found_indicators = [ind for ind in professional_indicators if ind in response]
                    print(f"   专业话术: {len(found_indicators)}/{len(professional_indicators)} 个指标")
                    
                    if len(found_indicators) >= 3:
                        success_count += 1
                        print(f"   质量评估: ✅ 优秀")
                    else:
                        print(f"   质量评估: ⚠️ 一般")
                else:
                    print(f"❌ 响应失败")
                    
            except Exception as e:
                print(f"❌ 调用失败: {e}")
        
        print(f"\nFew-shot LLM测试结果: {success_count}/{len(test_cases)} 优秀")
        return success_count >= len(test_cases) * 0.75
        
    except Exception as e:
        print(f"❌ Few-shot LLM测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_humanized_engine_with_fewshot():
    """测试集成Few-shot的人性化引擎"""
    print("\n🔮 测试集成Few-shot的人性化引擎")
    print("-" * 50)
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        session_id = "test_fewshot_integration"
        
        # 测试完整算命流程
        message = "我1988年6月1日午时出生，男，想看紫薇斗数"
        
        print(f"用户消息: {message}")
        
        responses = engine.process_user_message(message, session_id)
        
        print(f"总响应数: {len(responses)}")
        
        # 分析响应质量
        detailed_analysis_count = 0
        professional_response_count = 0
        
        for i, response in enumerate(responses, 1):
            response_type = response.get("type", "unknown")
            content = response.get("content", "")
            
            print(f"\n响应 {i}: [{response_type}]")
            print(f"  长度: {len(content)} 字符")
            print(f"  预览: {content[:80]}...")
            
            # 检查是否是详细分析
            if response_type == "detailed_analysis":
                detailed_analysis_count += 1
                
                # 检查专业话术
                professional_terms = ["根据", "建议", "保持", "通过", "将会"]
                found_terms = [term for term in professional_terms if term in content]
                
                if len(found_terms) >= 3:
                    professional_response_count += 1
                    print(f"  质量: ✅ 专业 (包含 {len(found_terms)} 个专业术语)")
                else:
                    print(f"  质量: ⚠️ 一般 (包含 {len(found_terms)} 个专业术语)")
        
        print(f"\n分析结果:")
        print(f"  详细分析数: {detailed_analysis_count}")
        print(f"  专业响应数: {professional_response_count}")
        print(f"  专业率: {professional_response_count/max(1, detailed_analysis_count)*100:.1f}%")
        
        # 判断集成效果
        integration_success = (
            detailed_analysis_count >= 4 and  # 至少4个详细分析
            professional_response_count >= detailed_analysis_count * 0.75  # 75%专业率
        )
        
        print(f"Few-shot集成效果: {'✅ 成功' if integration_success else '⚠️ 需要改进'}")
        
        return integration_success
        
    except Exception as e:
        print(f"❌ 人性化引擎Few-shot测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison_with_without_fewshot():
    """对比有无Few-shot的效果"""
    print("\n📊 对比有无Few-shot的效果")
    print("-" * 50)
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        
        # 模拟分析请求
        test_aspects = ["personality", "career", "wealth", "love"]
        
        print("对比分析结果:")
        
        for aspect in test_aspects:
            print(f"\n方面: {aspect}")
            
            # 模拟算命结果数据
            result_data = {
                "calculation_result": {
                    "palaces": {
                        "命宫": {"主星": "紫微", "地支": "子"},
                        "事业宫": {"主星": "天府", "地支": "午"},
                        "财帛宫": {"主星": "太阳", "地支": "寅"},
                        "夫妻宫": {"主星": "天机", "地支": "申"}
                    }
                }
            }
            
            # 生成分析
            analysis = engine._generate_detailed_analysis(result_data, aspect, "ziwei_analysis")
            
            if analysis:
                print(f"  长度: {len(analysis)} 字符")
                print(f"  预览: {analysis[:100]}...")
                
                # 检查专业特征
                professional_features = [
                    ("专业开场", ["根据您的", "从", "根据"]),
                    ("具体建议", ["建议您", "建议你", "注意"]),
                    ("积极结尾", ["将会", "帮助", "实现"])
                ]
                
                feature_score = 0
                for feature_name, keywords in professional_features:
                    has_feature = any(keyword in analysis for keyword in keywords)
                    print(f"    {feature_name}: {'✅' if has_feature else '❌'}")
                    if has_feature:
                        feature_score += 1
                
                print(f"  专业度: {feature_score}/{len(professional_features)} ({feature_score/len(professional_features)*100:.0f}%)")
            else:
                print(f"  ❌ 分析生成失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Few-shot Learning集成测试")
    print("=" * 80)
    print("目标: 验证微调模型话术的Few-shot Learning集成效果")
    print("=" * 80)
    
    # 执行测试
    test_results = []
    
    # 1. Few-shot样本管理器测试
    test_results.append(("Few-shot样本管理器", test_fewshot_samples()))
    
    # 2. Few-shot LLM客户端测试
    test_results.append(("Few-shot LLM客户端", test_fewshot_llm_client()))
    
    # 3. 人性化引擎集成测试
    test_results.append(("人性化引擎集成", test_humanized_engine_with_fewshot()))
    
    # 4. 效果对比测试
    test_results.append(("效果对比", test_comparison_with_without_fewshot()))
    
    # 汇总结果
    print(f"\n📊 Few-shot Learning集成测试结果")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 Few-shot Learning集成成功！")
        print("\n🎯 实现的核心功能:")
        print("  ✅ 训练样本智能匹配")
        print("  ✅ 专业话术自动生成")
        print("  ✅ 微调模型风格复现")
        print("  ✅ 人性化引擎无缝集成")
        print("\n🌟 Few-shot Learning优势:")
        print("  💡 无需本地模型部署")
        print("  🎯 基于真实训练数据")
        print("  🔄 动态样本选择")
        print("  📈 专业话术质量提升")
        print("\n📋 微调模型话术成功融入！")
    else:
        print("💥 部分功能存在问题，需要进一步优化")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
