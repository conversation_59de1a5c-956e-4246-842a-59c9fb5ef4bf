#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证八字算法准确性
对比多个来源的八字排盘结果
"""

def manual_calculate_bazi():
    """手动计算1988年6月1日午时的八字"""
    print("🔍 手动验证1988年6月1日午时的八字")
    print("=" * 50)
    
    # 1988年6月1日 = 农历四月十七
    print("📅 基本信息:")
    print("  公历: 1988年6月1日11时")
    print("  农历: 戊辰年四月十七日午时")
    
    # 手动推算八字
    print("\n🔮 手动推算八字:")
    print("  年柱: 戊辰 (1988年是戊辰年)")
    print("  月柱: 丁巳 (农历四月是丁巳月)")
    print("  日柱: 需要查万年历确定")
    print("  时柱: 丙午 (午时对应丙午)")
    
    # 五行分析
    print("\n🌟 理论五行分析:")
    print("  戊(土) 辰(土) - 年柱: 土土")
    print("  丁(火) 巳(火) - 月柱: 火火") 
    print("  日柱: 待确定")
    print("  丙(火) 午(火) - 时柱: 火火")
    
    print("\n⚠️ 需要准确的万年历数据来确定日柱")

def test_different_algorithms():
    """测试不同算法的结果"""
    print("\n🧪 测试不同算法结果")
    print("=" * 50)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        calc = RealBaziCalculator()
        
        # 测试同一个时间
        print("📅 测试数据: 1988年6月1日11时(午时)")
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        if "error" in result:
            print(f"❌ 算法失败: {result['error']}")
            return
        
        print("✅ 算法结果:")
        raw_result = result.get("raw_result", {})
        ganzhi = raw_result.get("干支", {})
        
        print(f"  四柱: {ganzhi.get('文本', '')}")
        print(f"  年柱: {ganzhi.get('年柱', '')}")
        print(f"  月柱: {ganzhi.get('月柱', '')}")
        print(f"  日柱: {ganzhi.get('日柱', '')}")
        print(f"  时柱: {ganzhi.get('时柱', '')}")
        
        # 详细分析每柱的五行
        print("\n🔍 详细五行分析:")
        
        # 分析年柱 戊辰
        print("  年柱 戊辰:")
        print("    戊 = 土")
        print("    辰 = 土 (辰中藏: 戊土、乙木、癸水)")
        
        # 分析月柱
        month_pillar = ganzhi.get('月柱', '')
        print(f"  月柱 {month_pillar}:")
        if month_pillar == "戊未":
            print("    戊 = 土")
            print("    未 = 土 (未中藏: 己土、丁火、乙木)")
        elif month_pillar == "丁巳":
            print("    丁 = 火")
            print("    巳 = 火 (巳中藏: 丙火、戊土、庚金)")
        
        # 分析日柱
        day_pillar = ganzhi.get('日柱', '')
        print(f"  日柱 {day_pillar}:")
        if day_pillar == "乙巳":
            print("    乙 = 木")
            print("    巳 = 火 (巳中藏: 丙火、戊土、庚金)")
        elif day_pillar == "丁亥":
            print("    丁 = 火") 
            print("    亥 = 水 (亥中藏: 壬水、甲木)")
        
        # 分析时柱
        hour_pillar = ganzhi.get('时柱', '')
        print(f"  时柱 {hour_pillar}:")
        if hour_pillar == "壬午":
            print("    壬 = 水")
            print("    午 = 火 (午中藏: 丁火、己土)")
        elif hour_pillar == "丙午":
            print("    丙 = 火")
            print("    午 = 火 (午中藏: 丁火、己土)")
        
        # 统计五行
        print("\n📊 五行统计 (含地支藏干):")
        wuxing = raw_result.get("五行", {})
        for element, info in wuxing.items():
            if isinstance(info, dict):
                count = info.get("五行数", "")
                strength = info.get("旺衰", "")
                print(f"  {element}: {count}个 ({strength})")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def compare_with_standard():
    """与标准八字对比"""
    print("\n📚 与标准八字资料对比")
    print("=" * 50)
    
    print("🔍 1988年6月1日的标准八字应该是:")
    print("  年柱: 戊辰 (1988年)")
    print("  月柱: 丁巳 (农历四月)")
    print("  日柱: 需要查万年历")
    print("  时柱: 丙午 (午时11-13点)")
    
    print("\n⚠️ 关键问题:")
    print("1. 月柱是丁巳还是戊未？")
    print("2. 日柱的天干地支是什么？")
    print("3. 时柱是丙午还是壬午？")
    
    print("\n💡 建议:")
    print("1. 对比权威万年历")
    print("2. 检查节气交替时间")
    print("3. 验证时辰干支推算")

def test_ziwei_comparison():
    """对比紫薇斗数的八字"""
    print("\n🔮 对比紫薇斗数中的八字")
    print("=" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        
        calc = RealZiweiCalculator()
        result = calc.calculate_chart(1988, 6, 1, 11, "男")
        
        if "error" in result:
            print(f"❌ 紫薇算法失败: {result['error']}")
            return
        
        birth_info = result.get("birth_info", {})
        chinese_date = birth_info.get("chinese_date", "")
        
        print(f"✅ 紫薇斗数中的八字: {chinese_date}")
        
        # 分析差异
        print("\n🔍 与八字算法对比:")
        print(f"  紫薇斗数: {chinese_date}")
        print(f"  八字算法: 戊辰 戊未 乙巳 壬午")
        
        if chinese_date != "戊辰 戊未 乙巳 壬午":
            print("⚠️ 发现差异！需要进一步验证")
        else:
            print("✅ 两个算法结果一致")
            
    except Exception as e:
        print(f"❌ 紫薇对比失败: {e}")

def main():
    """主函数"""
    print("🔍 八字算法准确性验证")
    print("=" * 60)
    
    # 1. 手动计算
    manual_calculate_bazi()
    
    # 2. 测试算法
    bazi_result = test_different_algorithms()
    
    # 3. 标准对比
    compare_with_standard()
    
    # 4. 紫薇对比
    test_ziwei_comparison()
    
    print("\n" + "=" * 60)
    print("🎯 验证结论:")
    print("需要进一步确认:")
    print("1. 1988年6月1日的准确日柱")
    print("2. 农历四月的月柱干支")
    print("3. 午时的时柱干支")
    print("4. 地支藏干的五行统计方法")

if __name__ == "__main__":
    main()
