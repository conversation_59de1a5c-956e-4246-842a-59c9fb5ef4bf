#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后台Agent Web界面集成
验证紫薇+八字融合分析和六爻占卜功能
"""

import os
import sys
import time
from datetime import datetime

def test_backend_agent_file():
    """测试后台Agent文件修改"""
    try:
        print("🔧 测试后台Agent文件修改")
        print("=" * 60)
        
        # 检查文件存在
        backend_file = "backend_agent_web.py"
        if not os.path.exists(backend_file):
            print(f"❌ 后台Agent文件不存在: {backend_file}")
            return False
        
        print(f"✅ 后台Agent文件存在: {backend_file}")
        
        # 读取文件内容
        with open(backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("紫薇+八字融合分析后台系统", "页面标题更新"),
            ("紫薇+八字融合分析系统", "系统名称更新"),
            ("⚡ 紫薇+八字 - 相互印证综合分析", "分析类型简化"),
            ("🔮 六爻卜卦", "六爻功能保留"),
            ("双重算法相互印证", "融合分析说明"),
            ("HTML可视化图表", "HTML图表支持"),
            ("selected_type = \"combined\"", "固定融合分析类型")
        ]
        
        success_count = 0
        for keyword, description in checks:
            if keyword in content:
                print(f"✅ {description}: 已更新")
                success_count += 1
            else:
                print(f"⚠️ {description}: 未找到关键词 '{keyword}'")
        
        print(f"\n📊 修改检查结果: {success_count}/{len(checks)} 通过")
        
        # 检查移除的功能
        removed_checks = [
            ("🏛️ 紫薇斗数 - 专业命盘分析", "独立紫薇斗数"),
            ("🔮 八字命理 - 生辰八字分析", "独立八字分析")
        ]
        
        removed_count = 0
        for keyword, description in removed_checks:
            if keyword not in content:
                print(f"✅ {description}: 已移除")
                removed_count += 1
            else:
                print(f"⚠️ {description}: 仍然存在")
        
        print(f"📊 移除检查结果: {removed_count}/{len(removed_checks)} 通过")
        
        return success_count >= 5 and removed_count >= 1
        
    except Exception as e:
        print(f"❌ 后台Agent文件测试失败: {e}")
        return False

def test_functionality_structure():
    """测试功能结构"""
    try:
        print("\n🏗️ 测试功能结构")
        print("=" * 60)
        
        backend_file = "backend_agent_web.py"
        with open(backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查保留的功能
        preserved_functions = [
            ("render_liuyao_divination", "六爻占卜渲染函数"),
            ("show_liuyao_analysis_content", "六爻分析内容显示"),
            ("render_create_simple", "创建分析页面"),
            ("render_progress_monitor", "进度监控页面"),
            ("render_completed_detail", "详情页面")
        ]
        
        preserved_count = 0
        for func_name, description in preserved_functions:
            if func_name in content:
                print(f"✅ {description}: 功能保留")
                preserved_count += 1
            else:
                print(f"❌ {description}: 功能缺失")
        
        print(f"\n📊 功能保留检查: {preserved_count}/{len(preserved_functions)} 通过")
        
        # 检查六爻相关代码
        liuyao_checks = [
            ("calculation_type == 'liuyao'", "六爻类型判断"),
            ("六爻占卜分析", "六爻分析标识"),
            ("占卜完成", "六爻完成状态"),
            ("解卦中", "六爻进行状态")
        ]
        
        liuyao_count = 0
        for keyword, description in liuyao_checks:
            if keyword in content:
                print(f"✅ {description}: 代码存在")
                liuyao_count += 1
            else:
                print(f"⚠️ {description}: 代码缺失")
        
        print(f"📊 六爻功能检查: {liuyao_count}/{len(liuyao_checks)} 通过")
        
        return preserved_count >= 4 and liuyao_count >= 3
        
    except Exception as e:
        print(f"❌ 功能结构测试失败: {e}")
        return False

def test_ui_elements():
    """测试UI元素"""
    try:
        print("\n🎨 测试UI元素")
        print("=" * 60)
        
        backend_file = "backend_agent_web.py"
        with open(backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查UI元素
        ui_checks = [
            ("🆕 创建分析", "创建分析按钮"),
            ("📈 分析进度监控", "进度监控标题"),
            ("🔮 六爻卜卦", "六爻占卜按钮"),
            ("📊 系统概览", "系统概览标题"),
            ("📋 分析记录", "记录列表标题")
        ]
        
        ui_count = 0
        for keyword, description in ui_checks:
            if keyword in content:
                print(f"✅ {description}: UI元素存在")
                ui_count += 1
            else:
                print(f"⚠️ {description}: UI元素缺失")
        
        print(f"\n📊 UI元素检查: {ui_count}/{len(ui_checks)} 通过")
        
        # 检查样式和布局
        style_checks = [
            ("left-panel", "左侧面板样式"),
            ("right-panel", "右侧面板样式"),
            ("record-card-left", "记录卡片样式"),
            ("detail-section", "详情区域样式")
        ]
        
        style_count = 0
        for keyword, description in style_checks:
            if keyword in content:
                print(f"✅ {description}: 样式存在")
                style_count += 1
            else:
                print(f"⚠️ {description}: 样式缺失")
        
        print(f"📊 样式检查: {style_count}/{len(style_checks)} 通过")
        
        return ui_count >= 4 and style_count >= 3
        
    except Exception as e:
        print(f"❌ UI元素测试失败: {e}")
        return False

def test_integration_compatibility():
    """测试集成兼容性"""
    try:
        print("\n🔗 测试集成兼容性")
        print("=" * 60)
        
        # 检查依赖导入
        try:
            import streamlit as st
            print("✅ Streamlit导入成功")
        except ImportError:
            print("❌ Streamlit导入失败")
            return False
        
        # 检查核心模块
        core_modules = [
            ("core.agents.fortune_calculator_agent", "后台Agent模块"),
            ("core.ziwei_bazi_fusion", "融合分析引擎")
        ]
        
        module_count = 0
        for module_name, description in core_modules:
            try:
                # 检查模块文件是否存在
                module_path = module_name.replace('.', '/') + '.py'
                if os.path.exists(module_path):
                    print(f"✅ {description}: 模块文件存在")
                    module_count += 1
                else:
                    print(f"⚠️ {description}: 模块文件不存在")
            except Exception as e:
                print(f"⚠️ {description}: 检查失败 - {e}")
        
        print(f"\n📊 模块兼容性: {module_count}/{len(core_modules)} 通过")
        
        # 检查数据目录
        data_dirs = [
            ("data/calculation_cache", "计算缓存目录"),
            ("charts", "图表目录")
        ]
        
        dir_count = 0
        for dir_path, description in data_dirs:
            if os.path.exists(dir_path):
                print(f"✅ {description}: 目录存在")
                dir_count += 1
            else:
                print(f"⚠️ {description}: 目录不存在")
        
        print(f"📊 目录结构: {dir_count}/{len(data_dirs)} 通过")
        
        return module_count >= 1 and dir_count >= 1
        
    except Exception as e:
        print(f"❌ 集成兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 后台Agent Web界面集成测试")
    print("=" * 80)
    print("测试内容:")
    print("1. 后台Agent文件修改")
    print("2. 功能结构检查")
    print("3. UI元素验证")
    print("4. 集成兼容性")
    print("=" * 80)
    
    # 执行测试
    results = []
    
    # 测试文件修改
    results.append(("文件修改", test_backend_agent_file()))
    
    # 测试功能结构
    results.append(("功能结构", test_functionality_structure()))
    
    # 测试UI元素
    results.append(("UI元素", test_ui_elements()))
    
    # 测试集成兼容性
    results.append(("集成兼容性", test_integration_compatibility()))
    
    # 总结结果
    print(f"\n🎯 后台Agent集成测试结果")
    print("=" * 80)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📊 总体结果: {success_count}/{len(results)} 通过")
    
    if success_count >= 3:
        print(f"\n🎉 后台Agent集成基本成功！")
        print(f"\n🚀 启动方式:")
        print(f"  streamlit run backend_agent_web.py")
        print(f"\n🌟 集成特点:")
        print(f"  ✅ 只保留紫薇+八字融合分析")
        print(f"  ✅ 保留六爻占卜功能")
        print(f"  ✅ 移除独立的紫薇、八字分析")
        print(f"  ✅ 现代化管理界面")
        
        return True
    else:
        print(f"\n💥 后台Agent集成需要进一步完善")
        print(f"  请检查失败的测试项目")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
