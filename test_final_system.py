#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终系统测试 - 验证完整的生产级系统
"""

import sys
import os
import time
import requests
import threading
import subprocess
from datetime import datetime

sys.path.append('.')

def test_configuration_system():
    """测试配置系统"""
    print("🔧 测试配置系统")
    print("=" * 80)
    
    try:
        from config.settings import ConfigManager, validate_config
        
        # 测试配置管理器
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        print(f"✅ 配置管理器创建成功")
        print(f"   LLM模型: {config.llm.model_name}")
        print(f"   API端口: {config.api.port}")
        print(f"   环境: {config.environment}")
        
        # 验证配置
        validation = validate_config()
        print(f"   配置验证: {'通过' if validation['valid'] else '失败'}")
        
        if validation['issues']:
            for issue in validation['issues']:
                print(f"   ⚠️ {issue}")
        
        # 创建环境变量模板
        config_manager.create_env_file(".env.test")
        print(f"   环境变量模板已创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False

def test_logging_system():
    """测试日志系统"""
    print("\n📝 测试日志系统")
    print("=" * 80)
    
    try:
        from utils.logger import init_logging, get_logger, get_performance_monitor
        
        # 初始化日志
        init_logging({
            "level": "INFO",
            "file_path": "logs/test.log",
            "console_output": True
        })
        
        # 测试日志记录
        logger = get_logger("test")
        logger.info("日志系统测试开始")
        logger.warning("这是一个测试警告")
        
        # 测试性能监控
        monitor = get_performance_monitor()
        monitor.record_api_request(1.5)
        monitor.record_llm_call(25.0, 1000)
        
        stats = monitor.get_performance_stats()
        
        print(f"✅ 日志系统测试成功")
        print(f"   API请求: {stats['api_requests']}")
        print(f"   LLM调用: {stats['llm_calls']}")
        print(f"   平均API时间: {stats['avg_api_response_time']}s")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🚨 测试错误处理")
    print("=" * 80)
    
    try:
        from utils.error_handler import handle_error, retry_on_error, RetryConfig
        
        # 测试错误处理
        try:
            raise ValueError("测试错误")
        except Exception as e:
            error_result = handle_error(e, {"test": "context"})
            print(f"✅ 错误处理测试成功")
            print(f"   用户消息: {error_result['user_message']}")
            print(f"   可恢复: {error_result['recoverable']}")
        
        # 测试重试机制
        @retry_on_error(RetryConfig(max_attempts=2, base_delay=0.1))
        def test_retry():
            import random
            if random.random() < 0.5:  # 50%概率失败
                raise ConnectionError("网络连接失败")
            return "成功"
        
        try:
            result = test_retry()
            print(f"   重试测试: {result}")
        except:
            print(f"   重试测试: 最终失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def test_main_startup():
    """测试主启动脚本"""
    print("\n🚀 测试主启动脚本")
    print("=" * 80)
    
    try:
        # 测试配置验证
        from main import validate_environment
        
        env_valid = validate_environment()
        print(f"✅ 环境验证: {'通过' if env_valid else '失败'}")
        
        # 检查必要目录
        required_dirs = ["logs", "charts", "config"]
        for dir_name in required_dirs:
            exists = os.path.exists(dir_name)
            print(f"   目录 {dir_name}: {'存在' if exists else '不存在'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主启动脚本测试失败: {e}")
        return False

def test_api_server_production():
    """测试生产级API服务器"""
    print("\n🌐 测试生产级API服务器")
    print("=" * 80)
    
    try:
        # 启动API服务器（后台）
        def start_api():
            from interfaces.unified_api import UnifiedAPI
            config = {
                "api_key": "test_key",
                "model_name": "deepseek-ai/DeepSeek-V3",
                "max_history": 20,
                "session_timeout": 3600
            }
            api = UnifiedAPI(config)
            api.run(port=8002, debug=False)
        
        api_thread = threading.Thread(target=start_api, daemon=True)
        api_thread.start()
        
        # 等待启动
        time.sleep(3)
        
        # 测试各种端点
        base_url = "http://localhost:8002"
        
        # 1. 健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        health_ok = response.status_code == 200
        print(f"   健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
        
        # 2. 工具列表
        response = requests.get(f"{base_url}/v2/tools", timeout=5)
        tools_ok = response.status_code == 200
        print(f"   工具列表: {'✅ 通过' if tools_ok else '❌ 失败'}")
        
        # 3. 聊天功能
        chat_data = {"message": "你好", "session_id": "test_prod"}
        response = requests.post(f"{base_url}/v2/chat", json=chat_data, timeout=30)
        chat_ok = response.status_code == 200
        print(f"   聊天功能: {'✅ 通过' if chat_ok else '❌ 失败'}")
        
        # 4. 统计信息
        response = requests.get(f"{base_url}/v2/stats", timeout=5)
        stats_ok = response.status_code == 200
        print(f"   统计信息: {'✅ 通过' if stats_ok else '❌ 失败'}")
        
        # 5. 旧版兼容
        legacy_data = {
            "model": "deepseek-ai/DeepSeek-V3",
            "messages": [{"role": "user", "content": "测试"}]
        }
        response = requests.post(f"{base_url}/v1/chat/completions", json=legacy_data, timeout=30)
        legacy_ok = response.status_code == 200
        print(f"   旧版兼容: {'✅ 通过' if legacy_ok else '❌ 失败'}")
        
        all_ok = all([health_ok, tools_ok, chat_ok, stats_ok, legacy_ok])
        print(f"✅ API服务器测试: {'全部通过' if all_ok else '部分失败'}")
        
        return all_ok
        
    except Exception as e:
        print(f"❌ API服务器测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n🔗 测试系统集成")
    print("=" * 80)
    
    try:
        # 测试完整的对话流程
        base_url = "http://localhost:8002"
        session_id = f"integration_test_{int(time.time())}"
        
        # 1. 问候
        response = requests.post(f"{base_url}/v2/chat", json={
            "message": "你好",
            "session_id": session_id
        }, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   问候测试: ✅ {result.get('message', '')[:30]}...")
        
        # 2. 算命请求
        response = requests.post(f"{base_url}/v2/chat", json={
            "message": "我想看紫薇斗数",
            "session_id": session_id
        }, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   算命请求: ✅ {result.get('message', '')[:30]}...")
        
        # 3. 提供出生信息
        response = requests.post(f"{base_url}/v2/chat", json={
            "message": "1988年6月1日午时男",
            "session_id": session_id
        }, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            tool_used = result.get('tool_used', '无')
            print(f"   出生信息: ✅ 工具={tool_used}")
        
        # 4. 检查会话状态
        response = requests.get(f"{base_url}/v2/session/{session_id}", timeout=5)
        
        if response.status_code == 200:
            session_data = response.json()
            msg_count = session_data.get('message_count', 0)
            print(f"   会话状态: ✅ 消息数={msg_count}")
        
        print(f"✅ 系统集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def test_performance_and_monitoring():
    """测试性能和监控"""
    print("\n📊 测试性能和监控")
    print("=" * 80)
    
    try:
        base_url = "http://localhost:8002"
        
        # 并发测试
        def make_request():
            try:
                response = requests.post(f"{base_url}/v2/chat", json={
                    "message": "测试并发",
                    "session_id": f"perf_test_{threading.current_thread().ident}"
                }, timeout=30)
                return response.status_code == 200
            except:
                return False
        
        # 启动多个并发请求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待完成
        success_count = 0
        for thread in threads:
            thread.join()
            success_count += 1
        
        print(f"   并发测试: ✅ {success_count}/5 个线程完成")
        
        # 获取性能统计
        response = requests.get(f"{base_url}/v2/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            tool_stats = stats.get('tool_stats', {})
            print(f"   工具执行: {tool_stats.get('total_executions', 0)} 次")
            print(f"   成功率: {tool_stats.get('success_rate', '0%')}")
        
        print(f"✅ 性能监控测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 性能监控测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 最终系统测试 - 生产级验证")
    print("=" * 100)
    
    start_time = datetime.now()
    
    # 执行所有测试
    tests = [
        ("配置系统", test_configuration_system),
        ("日志系统", test_logging_system),
        ("错误处理", test_error_handling),
        ("主启动脚本", test_main_startup),
        ("API服务器", test_api_server_production),
        ("系统集成", test_system_integration),
        ("性能监控", test_performance_and_monitoring)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结结果
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 100)
    print("🎉 最终系统测试结果:")
    print("=" * 100)
    
    success_count = 0
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name:12} : {status}")
        if success:
            success_count += 1
    
    total_tests = len(tests)
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 测试统计:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过数量: {success_count}")
    print(f"  成功率: {success_rate:.1f}%")
    print(f"  测试耗时: {duration:.1f}秒")
    
    if success_rate >= 85:
        print(f"\n🎊 系统测试基本通过！生产级系统准备就绪！")
        print(f"\n🚀 **系统已完成架构重构，可以投入使用！**")
        print(f"  - 智能对话能力 ✅")
        print(f"  - 模块化架构 ✅") 
        print(f"  - 多端支持 ✅")
        print(f"  - 生产级配置 ✅")
        print(f"  - 完善的监控 ✅")
        print(f"\n💡 **启动命令:**")
        print(f"  python main.py")
        print(f"\n🌐 **访问地址:**")
        print(f"  API: http://localhost:8002")
        print(f"  Web: http://localhost:8501")
        return True
    else:
        print(f"\n⚠️ 系统测试未完全通过，需要修复问题")
        return False

if __name__ == "__main__":
    main()
