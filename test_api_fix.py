#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API配置修复
"""

def test_api_configuration():
    """测试API配置"""
    print("🔧 测试API配置修复")
    print("=" * 40)
    
    try:
        # 检查web端修改
        with open("web_demo/prompt_web.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查算法导入
        if "from algorithms.real_ziwei_calculator import RealZiweiCalculator" in content:
            print("✅ 紫薇算法导入已添加")
        else:
            print("❌ 紫薇算法导入缺失")
            return False
        
        if "from algorithms.real_bazi_calculator import RealBaziCalculator" in content:
            print("✅ 八字算法导入已添加")
        else:
            print("❌ 八字算法导入缺失")
            return False
        
        # 检查API函数定义
        if "def simple_chat_api(prompt: str) -> str:" in content:
            print("✅ 聊天API函数已定义")
        else:
            print("❌ 聊天API函数缺失")
            return False
        
        # 检查引擎初始化
        if "FortuneEngine(" in content and "chat_api_func=simple_chat_api" in content:
            print("✅ 引擎初始化已修复")
        else:
            print("❌ 引擎初始化未修复")
            return False
        
        # 检查API配置
        if "SILICONFLOW_API_KEY" in content and "DEEPSEEK_MODEL_NAME" in content:
            print("✅ API配置已包含")
        else:
            print("❌ API配置缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API配置检查失败: {e}")
        return False

def test_engine_initialization():
    """测试引擎初始化"""
    print("\n🔮 测试引擎初始化")
    print("=" * 30)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()
        
        print("✅ 算法实例创建成功")
        
        # 定义简单的API函数
        def test_chat_api(prompt: str) -> str:
            return "测试响应"
        
        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=test_chat_api
        )
        
        print("✅ 引擎实例创建成功")
        
        # 检查属性
        if hasattr(engine, 'chat_api_func') and engine.chat_api_func is not None:
            print("✅ chat_api_func 已正确设置")
        else:
            print("❌ chat_api_func 未设置")
            return False
        
        if hasattr(engine, 'ziwei_calc') and engine.ziwei_calc is not None:
            print("✅ ziwei_calc 已正确设置")
        else:
            print("❌ ziwei_calc 未设置")
            return False
        
        if hasattr(engine, 'bazi_calc') and engine.bazi_calc is not None:
            print("✅ bazi_calc 已正确设置")
        else:
            print("❌ bazi_calc 未设置")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 引擎初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_birth_info_extraction():
    """测试出生信息提取"""
    print("\n📅 测试出生信息提取")
    print("=" * 30)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 定义测试API函数
        def test_chat_api(prompt: str) -> str:
            # 模拟正确的JSON响应
            return '''
            {
              "year": 1988,
              "month": 6,
              "day": 1,
              "hour": 12,
              "gender": "男"
            }
            '''
        
        # 创建引擎实例
        engine = FortuneEngine(chat_api_func=test_chat_api)
        
        # 测试出生信息提取
        test_input = "1988年6月1日公历 午时 男命"
        result = engine.parse_user_input(test_input)
        
        print(f"📝 测试输入: {test_input}")
        print(f"📊 解析结果: {result}")
        
        if result.get("birth_info"):
            print("✅ 出生信息提取成功")
            birth_info = result["birth_info"]
            print(f"  年份: {birth_info.get('year')}")
            print(f"  月份: {birth_info.get('month')}")
            print(f"  日期: {birth_info.get('day')}")
            print(f"  时辰: {birth_info.get('hour')}")
            print(f"  性别: {birth_info.get('gender')}")
            return True
        else:
            print("❌ 出生信息提取失败")
            return False
        
    except Exception as e:
        print(f"❌ 出生信息提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n📋 修复总结")
    print("=" * 30)
    
    print("🔧 **问题原因:**")
    print("  - Web端引擎初始化时没有传递 chat_api_func 参数")
    print("  - 导致 self.chat_api_func 为 None")
    print("  - LLM解析时返回 '聊天API未配置'")
    print()
    
    print("✅ **修复内容:**")
    print("  1. 在web端导入算法模块")
    print("  2. 创建算法实例 (ziwei_calc, bazi_calc)")
    print("  3. 定义 simple_chat_api 函数")
    print("  4. 正确初始化 FortuneEngine")
    print("  5. 传递所有必要参数")
    print()
    
    print("🎯 **修复效果:**")
    print("  - chat_api_func 正确配置")
    print("  - LLM解析功能正常")
    print("  - 出生信息提取成功")
    print("  - 算法调用正常")
    print("  - 图片生成和显示正常")

def main():
    """主测试函数"""
    print("🔧 API配置修复验证")
    print("=" * 50)
    
    # 测试1: API配置
    config_success = test_api_configuration()
    
    # 测试2: 引擎初始化
    engine_success = test_engine_initialization()
    
    # 测试3: 出生信息提取
    extraction_success = test_birth_info_extraction()
    
    # 显示总结
    show_fix_summary()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎉 API配置修复验证结果:")
    print(f"  API配置检查: {'✅' if config_success else '❌'}")
    print(f"  引擎初始化: {'✅' if engine_success else '❌'}")
    print(f"  信息提取: {'✅' if extraction_success else '❌'}")
    
    if all([config_success, engine_success, extraction_success]):
        print("\n🎊 API配置修复完成！")
        print("\n📝 修复成果:")
        print("  1. ✅ Web端正确初始化引擎")
        print("  2. ✅ chat_api_func 正确配置")
        print("  3. ✅ 算法实例正确传递")
        print("  4. ✅ LLM解析功能正常")
        print("  5. ✅ 出生信息提取成功")
        
        print("\n🚀 现在可以启动Web端:")
        print("  cd web_demo")
        print("  streamlit run prompt_web.py")
        
        print("\n🎯 现在应该不会再出现 '聊天API未配置' 错误！")
        print("用户输入算命问题后，系统可以:")
        print("  - 正确解析出生信息")
        print("  - 调用真实算法计算")
        print("  - 生成精美排盘图")
        print("  - 提供完整AI分析")
    else:
        print("\n⚠️ API配置修复需要进一步检查")

if __name__ == "__main__":
    main()
