#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字算命工具 - 将现有八字算命功能包装为新架构工具
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from .base_tool import BaseTool

logger = logging.getLogger(__name__)

class BaziTool(BaseTool):
    """八字算命工具 - 基于现有FortuneEngine的八字算命功能"""
    
    def __init__(self, bazi_calc, llm_client, prompt_manager):
        """
        初始化八字算命工具
        
        Args:
            bazi_calc: 八字算法实例
            llm_client: LLM客户端
            prompt_manager: 提示词管理器
        """
        super().__init__(
            name="bazi",
            description="八字算命 - 基于出生信息进行传统四柱分析",
            version="2.0.0"
        )
        
        self.bazi_calc = bazi_calc
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
        
        logger.info("八字算命工具初始化完成")
    
    def can_handle(self, intent: Dict[str, Any]) -> bool:
        """
        判断是否能处理八字算命相关请求
        
        Args:
            intent: 意图信息
            
        Returns:
            是否能处理
        """
        tool_name = intent.get("tool_name", "").lower()
        
        # 直接指定八字算命
        if tool_name == "bazi":
            return True
        
        # 综合分析也可以使用八字算命
        if tool_name == "comprehensive":
            return True
        
        # 检查关键词
        raw_response = intent.get("raw_response", "").lower()
        bazi_keywords = ["八字", "四柱", "天干", "地支", "合婚"]
        
        return any(keyword in raw_response for keyword in bazi_keywords)
    
    def get_required_entities(self) -> List[str]:
        """获取八字算命所需的实体"""
        return ["birth_info"]
    
    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行八字算命分析
        
        Args:
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            分析结果
        """
        start_time = time.time()
        
        try:
            logger.info("开始执行八字算命分析")
            
            # 1. 获取出生信息
            birth_info = self._extract_birth_info(intent, context)
            if not birth_info:
                return {
                    "success": False,
                    "error": "缺少出生信息",
                    "message": "请提供您的出生信息（年月日时和性别），例如：1988年6月1日午时男"
                }
            
            # 2. 调用八字算法
            algorithm_result = self._call_bazi_algorithm(birth_info)
            if not algorithm_result.get("success"):
                return {
                    "success": False,
                    "error": algorithm_result.get("error", "算法调用失败"),
                    "message": "八字排盘失败，请检查出生信息是否正确"
                }
            
            # 3. 生成AI分析
            analysis_result = self._generate_ai_analysis(
                algorithm_result, intent, context, birth_info
            )
            
            # 4. 生成图片
            chart_image = self._generate_chart_image(algorithm_result)
            
            execution_time = time.time() - start_time
            logger.info(f"八字算命分析完成 - 耗时: {execution_time:.2f}秒")
            
            return {
                "success": True,
                "tool_name": "bazi",
                "analysis": analysis_result,
                "chart_data": algorithm_result,
                "chart_image": chart_image,
                "birth_info": birth_info,
                "execution_time": execution_time,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"八字算命分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "八字算命分析过程中出现错误，请稍后重试"
            }
    
    def _extract_birth_info(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取出生信息"""
        
        # 从意图中获取
        birth_info = intent.get("entities", {}).get("birth_info")
        if birth_info:
            return birth_info
        
        # 从上下文中获取
        birth_info = context.get("birth_info")
        if birth_info:
            return birth_info
        
        return None
    
    def _call_bazi_algorithm(self, birth_info: Dict[str, Any]) -> Dict[str, Any]:
        """调用八字算法"""
        
        try:
            if not self.bazi_calc:
                return {"success": False, "error": "八字算法未初始化"}
            
            result = self.bazi_calc.calculate_bazi(
                year=birth_info["year"],
                month=birth_info["month"],
                day=birth_info["day"],
                hour=birth_info["hour"],
                gender=birth_info.get("gender", "男")
            )
            
            if result.get("success"):
                return result
            else:
                return {
                    "success": False,
                    "error": result.get("error", "算法计算失败")
                }
                
        except Exception as e:
            logger.error(f"八字算法调用失败: {e}")
            return {
                "success": False,
                "error": f"算法调用异常: {str(e)}"
            }
    
    def _generate_ai_analysis(self, algorithm_result: Dict[str, Any], 
                            intent: Dict[str, Any], context: Dict[str, Any],
                            birth_info: Dict[str, Any]) -> str:
        """生成AI分析"""
        
        try:
            # 获取问题类型
            question_type = intent.get("question_type", "general")
            
            # 构建分析提示词
            prompt = self.prompt_manager.get_bazi_analysis_prompt(
                algorithm_result=algorithm_result,
                question_type=question_type,
                birth_info=birth_info,
                context=context
            )
            
            # 调用LLM生成分析
            analysis = self.llm_client.chat(
                prompt=prompt,
                system_prompt="你是专业的八字命理大师，必须基于提供的真实八字数据进行分析，不得编造任何信息。",
                temperature=0.7
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"AI分析生成失败: {e}")
            return f"分析生成失败: {str(e)}"
    
    def _generate_chart_image(self, algorithm_result: Dict[str, Any]) -> Optional[str]:
        """生成八字图片"""
        
        try:
            # 导入图片生成器
            import sys
            sys.path.append('utils')
            from image_generator import generate_bazi_chart
            
            chart_data = algorithm_result.get("data", {})
            if not chart_data:
                return None
            
            # 生成图片
            image_path = generate_bazi_chart(chart_data)
            return image_path
            
        except Exception as e:
            logger.warning(f"八字图片生成失败: {e}")
            return None
    
    def get_prompt_template(self, task_type: str) -> Optional[str]:
        """获取八字算命专用提示词模板"""
        
        templates = {
            "career": "请重点分析官杀星、印星对事业的影响...",
            "love": "请重点分析配偶星、桃花的情况...",
            "wealth": "请重点分析财星、食伤生财的配置...",
            "health": "请重点分析日主强弱、五行平衡...",
            "fortune": "请重点分析大运、流年的吉凶...",
            "general": "请进行全面的八字命理分析..."
        }
        
        return templates.get(task_type, templates["general"])
    
    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """验证八字算命输入"""
        
        # 基础验证
        base_validation = super().validate_input(intent, context)
        if not base_validation["valid"]:
            return base_validation
        
        # 出生信息验证
        birth_info = self._extract_birth_info(intent, context)
        if not birth_info:
            return {
                "valid": False,
                "message": "八字算命需要完整的出生信息",
                "missing_entities": ["birth_info"]
            }
        
        # 验证出生信息完整性
        required_fields = ["year", "month", "day", "hour", "gender"]
        missing_fields = []
        
        for field in required_fields:
            if field not in birth_info:
                missing_fields.append(field)
        
        if missing_fields:
            return {
                "valid": False,
                "message": f"出生信息不完整，缺少: {', '.join(missing_fields)}",
                "missing_entities": missing_fields
            }
        
        return {
            "valid": True,
            "message": "八字算命输入验证通过",
            "missing_entities": []
        }
    
    def get_info(self) -> Dict[str, Any]:
        """获取八字算命工具信息"""
        
        base_info = super().get_info()
        base_info.update({
            "supported_questions": ["career", "love", "wealth", "health", "fortune", "general"],
            "required_birth_info": ["year", "month", "day", "hour", "gender"],
            "features": [
                "传统四柱分析",
                "十神关系解读",
                "大运流年预测",
                "五行平衡分析"
            ],
            "algorithm_status": "已连接" if self.bazi_calc else "未连接"
        })
        
        return base_info
