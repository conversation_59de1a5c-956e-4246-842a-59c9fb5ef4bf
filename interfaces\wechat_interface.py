#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信接口 - 基于新架构的微信聊天机器人接口
"""

import logging
import json
import hashlib
import time
from typing import Dict, Any, Optional
from flask import Flask, request, Response
import xml.etree.ElementTree as ET

from .base_interface import BaseInterface

logger = logging.getLogger(__name__)

class WeChatInterface(BaseInterface):
    """微信接口 - 支持微信公众号和企业微信"""
    
    def __init__(self, conversation_engine, config: Dict[str, Any]):
        """
        初始化微信接口
        
        Args:
            conversation_engine: 对话引擎
            config: 微信配置
        """
        super().__init__(conversation_engine)
        
        self.config = config
        self.token = config.get("token", "")
        self.app_id = config.get("app_id", "")
        self.app_secret = config.get("app_secret", "")
        
        # 创建Flask应用
        self.app = Flask(__name__)
        self._register_routes()
        
        logger.info("微信接口初始化完成")
    
    def _register_routes(self):
        """注册微信路由"""
        
        @self.app.route('/wechat', methods=['GET', 'POST'])
        def wechat_handler():
            """微信消息处理器"""
            
            if request.method == 'GET':
                # 微信验证
                return self._verify_wechat()
            else:
                # 处理消息
                return self._handle_wechat_message()
    
    def _verify_wechat(self) -> str:
        """验证微信服务器"""
        
        try:
            signature = request.args.get('signature', '')
            timestamp = request.args.get('timestamp', '')
            nonce = request.args.get('nonce', '')
            echostr = request.args.get('echostr', '')
            
            # 验证签名
            if self._check_signature(signature, timestamp, nonce):
                logger.info("微信验证成功")
                return echostr
            else:
                logger.warning("微信验证失败")
                return "验证失败"
                
        except Exception as e:
            logger.error(f"微信验证异常: {e}")
            return "验证异常"
    
    def _check_signature(self, signature: str, timestamp: str, nonce: str) -> bool:
        """检查微信签名"""
        
        try:
            # 按字典序排序
            tmp_list = [self.token, timestamp, nonce]
            tmp_list.sort()
            
            # 拼接字符串
            tmp_str = ''.join(tmp_list)
            
            # SHA1加密
            hash_obj = hashlib.sha1(tmp_str.encode('utf-8'))
            hash_str = hash_obj.hexdigest()
            
            return hash_str == signature
            
        except Exception as e:
            logger.error(f"签名验证异常: {e}")
            return False
    
    def _handle_wechat_message(self) -> Response:
        """处理微信消息"""
        
        try:
            # 解析XML消息
            xml_data = request.get_data()
            msg_data = self._parse_xml_message(xml_data)
            
            if not msg_data:
                return Response("解析失败", content_type='text/plain')
            
            # 提取消息信息
            from_user = msg_data.get('FromUserName', '')
            to_user = msg_data.get('ToUserName', '')
            msg_type = msg_data.get('MsgType', '')
            content = msg_data.get('Content', '')
            
            logger.info(f"收到微信消息: {from_user} -> {content}")
            
            # 处理不同类型的消息
            if msg_type == 'text':
                response_content = self._handle_text_message(from_user, content)
            elif msg_type == 'event':
                response_content = self._handle_event_message(msg_data)
            else:
                response_content = "暂不支持该消息类型"
            
            # 生成回复XML
            response_xml = self._generate_response_xml(
                to_user, from_user, response_content
            )
            
            return Response(response_xml, content_type='application/xml')
            
        except Exception as e:
            logger.error(f"处理微信消息异常: {e}")
            return Response("处理异常", content_type='text/plain')
    
    def _parse_xml_message(self, xml_data: bytes) -> Optional[Dict[str, str]]:
        """解析XML消息"""
        
        try:
            root = ET.fromstring(xml_data)
            
            msg_data = {}
            for child in root:
                msg_data[child.tag] = child.text
            
            return msg_data
            
        except Exception as e:
            logger.error(f"XML解析失败: {e}")
            return None
    
    def _handle_text_message(self, user_id: str, content: str) -> str:
        """处理文本消息"""
        
        try:
            # 使用用户ID作为会话ID
            session_id = f"wechat_{user_id}"
            
            # 调用对话引擎
            result = self.process_message(session_id, content)
            
            if result.get("success"):
                response = result.get("message", "")
                
                # 微信消息长度限制处理
                if len(response) > 2000:
                    response = response[:1900] + "...\n\n回复'继续'查看更多内容"
                
                # 添加图片提示
                if result.get("tool_result", {}).get("chart_image"):
                    response += "\n\n📊 已生成专业图表，请访问网页版查看完整分析"
                
                return response
            else:
                return f"处理失败: {result.get('message', '未知错误')}"
                
        except Exception as e:
            logger.error(f"处理文本消息失败: {e}")
            return "抱歉，处理您的消息时出现了问题，请稍后重试"
    
    def _handle_event_message(self, msg_data: Dict[str, str]) -> str:
        """处理事件消息"""
        
        event = msg_data.get('Event', '')
        
        if event == 'subscribe':
            # 关注事件
            return self._get_welcome_message()
        elif event == 'unsubscribe':
            # 取消关注事件
            logger.info(f"用户取消关注: {msg_data.get('FromUserName')}")
            return ""
        elif event == 'CLICK':
            # 菜单点击事件
            event_key = msg_data.get('EventKey', '')
            return self._handle_menu_click(event_key)
        else:
            return "感谢您的操作"
    
    def _get_welcome_message(self) -> str:
        """获取欢迎消息"""
        
        return """🔮 欢迎使用智能算命AI助手！

我是专业的算命AI，可以为您提供：
• 紫薇斗数 - 详细命盘分析
• 八字算命 - 传统四柱分析
• 六爻算卦 - 占卜预测

💬 使用方法：
直接发送您的问题或出生信息即可开始分析

📝 示例：
"我想看紫薇斗数"
"1988年6月1日午时男"
"帮我算一卦，今年运势如何"

让我们开始您的命理之旅吧！"""
    
    def _handle_menu_click(self, event_key: str) -> str:
        """处理菜单点击"""
        
        menu_responses = {
            "ziwei": "请提供您的出生信息（年月日时和性别），我将为您进行紫薇斗数分析",
            "bazi": "请提供您的出生信息（年月日时和性别），我将为您进行八字分析",
            "liuyao": "请告诉我您想要占卜的具体问题，我将为您进行六爻算卦",
            "help": self._get_welcome_message()
        }
        
        return menu_responses.get(event_key, "感谢您的点击")
    
    def _generate_response_xml(self, from_user: str, to_user: str, content: str) -> str:
        """生成回复XML"""
        
        timestamp = int(time.time())
        
        xml_template = """<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""
        
        return xml_template.format(
            to_user=to_user,
            from_user=from_user,
            timestamp=timestamp,
            content=content
        )
    
    def handle_message(self, session_id: str, message: str) -> str:
        """实现基类的抽象方法"""
        
        result = self.process_message(session_id, message)
        return result.get("message", "处理失败")
    
    def run(self, host: str = "0.0.0.0", port: int = 8003, debug: bool = False):
        """启动微信接口服务器"""
        
        logger.info(f"启动微信接口服务器 - {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)

def create_wechat_app(conversation_engine, config: Dict[str, Any]) -> Flask:
    """创建微信Flask应用"""
    
    interface = WeChatInterface(conversation_engine, config)
    return interface.app

if __name__ == "__main__":
    # 这里需要传入实际的对话引擎和配置
    print("微信接口需要配合完整系统使用")
    print("请参考 unified_api.py 中的完整实现")
