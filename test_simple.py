#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os
from pathlib import Path

def test_project_structure():
    """测试项目结构"""
    print("🧪 测试项目结构...")
    
    required_dirs = [
        "algorithms",
        "models", 
        "services",
        "utils",
        "web",
        "config",
        "data",
        "tests"
    ]
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✅ {dir_name}")
        else:
            print(f"❌ {dir_name}")
            return False
    
    return True

def test_config_import():
    """测试配置导入"""
    print("\n🧪 测试配置导入...")
    
    try:
        from config import config
        print(f"✅ 配置导入成功")
        print(f"   LLM模型: {config.llm.model_name}")
        print(f"   API密钥: {'已配置' if config.llm.api_key else '未配置'}")
        return True
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        return False

def test_models_import():
    """测试模型导入"""
    print("\n🧪 测试模型导入...")
    
    try:
        from models.birth_info import BirthInfo
        birth_info = BirthInfo(1988, 6, 1, 11, "男")
        print(f"✅ BirthInfo创建成功: {birth_info.to_display_string()}")
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def test_utils_import():
    """测试工具导入"""
    print("\n🧪 测试工具导入...")
    
    try:
        from utils.simple_logger import get_logger
        logger = get_logger()
        logger.info("测试日志")
        print("✅ 日志工具导入成功")
        
        from utils.cache_manager import get_cache
        cache = get_cache()
        print("✅ 缓存工具导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 工具导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔮 智能算命AI系统 v4.0 简单测试")
    print("=" * 50)
    
    tests = [
        test_project_structure,
        test_config_import,
        test_models_import,
        test_utils_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 基础测试通过！项目结构正常")
    else:
        print("⚠️ 部分测试失败，请检查项目结构")

if __name__ == "__main__":
    main()
