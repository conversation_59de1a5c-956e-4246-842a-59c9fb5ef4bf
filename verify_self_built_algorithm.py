#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证自建算法的正确性
对比权威资料和多个来源
"""

def verify_wuxing_mapping():
    """验证五行映射是否正确"""
    print("🔍 验证五行映射")
    print("=" * 50)

    # 标准五行映射
    standard_wuxing = {
        # 天干五行
        '甲': '木', '乙': '木',
        '丙': '火', '丁': '火',
        '戊': '土', '己': '土',
        '庚': '金', '辛': '金',
        '壬': '水', '癸': '水',
        # 地支五行
        '子': '水', '丑': '土', '寅': '木', '卯': '木',
        '辰': '土', '巳': '火', '午': '火', '未': '土',
        '申': '金', '酉': '金', '戌': '土', '亥': '水'
    }

    # 我的算法映射
    from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
    calc = EnhancedBaziCalculator()
    my_wuxing = calc.wuxing_map

    print("📊 对比五行映射:")
    all_correct = True
    for char, standard_element in standard_wuxing.items():
        my_element = my_wuxing.get(char, "未定义")
        status = "✅" if my_element == standard_element else "❌"
        if my_element != standard_element:
            all_correct = False
        print(f"  {char}: 标准={standard_element}, 我的={my_element} {status}")

    if all_correct:
        print("✅ 五行映射完全正确！")
    else:
        print("❌ 五行映射有错误！")

    return all_correct

def verify_dizhi_canggan():
    """验证地支藏干是否正确"""
    print(f"\n🔍 验证地支藏干")
    print("=" * 50)

    # 标准地支藏干
    standard_canggan = {
        '子': ['癸'],
        '丑': ['己', '癸', '辛'],
        '寅': ['甲', '丙', '戊'],
        '卯': ['乙'],
        '辰': ['戊', '乙', '癸'],
        '巳': ['丙', '戊', '庚'],
        '午': ['丁', '己'],
        '未': ['己', '丁', '乙'],
        '申': ['庚', '壬', '戊'],
        '酉': ['辛'],
        '戌': ['戊', '辛', '丁'],
        '亥': ['壬', '甲']
    }

    # 我的算法
    from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
    calc = EnhancedBaziCalculator()
    my_canggan = calc.dizhi_canggan

    print("📊 对比地支藏干:")
    all_correct = True
    for dizhi, standard_list in standard_canggan.items():
        my_list = my_canggan.get(dizhi, [])
        status = "✅" if my_list == standard_list else "❌"
        if my_list != standard_list:
            all_correct = False
        print(f"  {dizhi}: 标准={standard_list}, 我的={my_list} {status}")

    if all_correct:
        print("✅ 地支藏干完全正确！")
    else:
        print("❌ 地支藏干有错误！")

    return all_correct

def verify_wuxing_calculation():
    """验证五行计算是否正确"""
    print(f"\n🔍 验证五行计算")
    print("=" * 50)

    # 使用标准八字：戊辰 丁巳 丁亥 丙午
    bazi = "戊辰 丁巳 丁亥 丙午"
    print(f"测试八字: {bazi}")

    # 手动计算标准答案
    print(f"\n📊 手动计算标准答案:")

    # 天干五行（注意：丁出现两次）
    tiangan_list = ['戊', '丁', '丁', '丙']  # 年月日时的天干
    tiangan_elements = ['土', '火', '火', '火']  # 对应的五行
    print("天干五行:")
    for i, (gan, element) in enumerate(zip(tiangan_list, tiangan_elements)):
        pillar_name = ['年', '月', '日', '时'][i]
        print(f"  {pillar_name}干{gan} = {element}")

    # 地支五行
    dizhi_wuxing = {'辰': '土', '巳': '火', '亥': '水', '午': '火'}
    print("地支五行:")
    for zhi, element in dizhi_wuxing.items():
        print(f"  {zhi} = {element}")

    # 地支藏干
    print("地支藏干:")
    canggan_elements = []
    canggan_map = {
        '辰': ['戊', '乙', '癸'],  # 土、木、水
        '巳': ['丙', '戊', '庚'],  # 火、土、金
        '亥': ['壬', '甲'],       # 水、木
        '午': ['丁', '己']        # 火、土
    }

    for zhi, ganlist in canggan_map.items():
        for gan in ganlist:
            element_map = {'戊': '土', '乙': '木', '癸': '水', '丙': '火', '庚': '金', '壬': '水', '甲': '木', '丁': '火', '己': '土'}
            element = element_map[gan]
            canggan_elements.append(element)
            print(f"  {zhi}藏{gan} = {element}")

    # 统计标准答案
    standard_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}

    # 天干
    for element in tiangan_elements:
        standard_count[element] += 1

    # 地支
    for element in dizhi_wuxing.values():
        standard_count[element] += 1

    # 藏干（权重0.5）
    for element in canggan_elements:
        standard_count[element] += 0.5

    print(f"\n📊 标准答案统计:")
    for element, count in standard_count.items():
        print(f"  {element}: {count}个")

    # 测试我的算法
    print(f"\n🔧 测试我的算法:")
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()

        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")

        if result["success"]:
            analysis = result["analysis"]
            wuxing = analysis["wuxing"]
            my_count = wuxing["count"]

            print("我的算法统计:")
            for element, count in my_count.items():
                print(f"  {element}: {count}个")

            # 对比结果
            print(f"\n🔍 对比结果:")
            all_correct = True
            for element in ['木', '火', '土', '金', '水']:
                standard = standard_count[element]
                mine = my_count[element]
                status = "✅" if abs(standard - mine) < 0.1 else "❌"
                if abs(standard - mine) >= 0.1:
                    all_correct = False
                print(f"  {element}: 标准={standard}, 我的={mine} {status}")

            if all_correct:
                print("✅ 五行计算完全正确！")
            else:
                print("❌ 五行计算有误差！")

            return all_correct
        else:
            print(f"❌ 算法执行失败: {result.get('error')}")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def verify_shishen_logic():
    """验证十神逻辑是否正确"""
    print(f"\n🔍 验证十神逻辑")
    print("=" * 50)

    # 标准十神关系（以日干为中心）
    print("标准十神关系:")
    print("  同类 = 比肩/劫财")
    print("  我生 = 食神/伤官")
    print("  我克 = 偏财/正财")
    print("  克我 = 七杀/正官")
    print("  生我 = 偏印/正印")

    # 五行相生相克
    print(f"\n五行相生相克:")
    print("  相生: 木→火→土→金→水→木")
    print("  相克: 木克土、土克水、水克火、火克金、金克木")

    # 测试日干丁火的十神
    print(f"\n测试日干丁火的十神:")
    test_cases = [
        ('木', '生我', '偏印/正印'),
        ('火', '同类', '比肩/劫财'),
        ('土', '我生', '食神/伤官'),
        ('金', '我克', '偏财/正财'),
        ('水', '克我', '七杀/正官')
    ]

    from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
    calc = EnhancedBaziCalculator()

    day_master_element = '火'
    if day_master_element in calc.shishen_map:
        my_shishen = calc.shishen_map[day_master_element]

        all_correct = True
        for element, relation, expected in test_cases:
            actual = my_shishen.get(element, "未定义")
            status = "✅" if expected in actual else "❌"
            if expected not in actual:
                all_correct = False
            print(f"  {element}({relation}): 期望={expected}, 实际={actual} {status}")

        if all_correct:
            print("✅ 十神逻辑基本正确！")
        else:
            print("❌ 十神逻辑有问题！")

        return all_correct
    else:
        print("❌ 十神映射表缺失！")
        return False

def verify_with_online_calculator():
    """与在线八字排盘对比"""
    print(f"\n🔍 与在线八字排盘对比")
    print("=" * 50)

    print("📊 1988年6月1日11时男的标准数据:")
    print("  八字: 戊辰 丁巳 丁亥 丙午")
    print("  日主: 丁火")
    print("  五行: 火旺、土次、水木弱、金极弱")

    # 测试我的算法
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()

        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")

        if result["success"]:
            bazi_info = result["bazi_info"]
            analysis = result["analysis"]

            print(f"\n🔧 我的算法结果:")
            print(f"  八字: {bazi_info['chinese_date']}")
            print(f"  日主: {analysis['day_master']['gan']} ({analysis['day_master']['element']})")
            print(f"  强弱: {analysis['day_master']['strength']}")

            wuxing = analysis["wuxing"]
            print(f"  五行统计:")
            for element, count in wuxing["count"].items():
                strength = wuxing["strength"][element]
                print(f"    {element}: {count}个 ({strength})")

            # 验证关键点
            checks = [
                (bazi_info['chinese_date'] == "戊辰 丁巳 丁亥 丙午", "八字正确"),
                (analysis['day_master']['gan'] == "丁", "日主正确"),
                (analysis['day_master']['element'] == "火", "日主五行正确"),
                (wuxing["count"]["火"] > wuxing["count"]["金"], "火旺金弱"),
                (wuxing["strength"]["火"] == "旺", "火旺判断"),
                (wuxing["strength"]["金"] == "极弱", "金弱判断")
            ]

            print(f"\n✅ 验证结果:")
            all_passed = True
            for check, description in checks:
                status = "✅" if check else "❌"
                if not check:
                    all_passed = False
                print(f"  {description}: {status}")

            return all_passed
        else:
            print(f"❌ 算法执行失败")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🧪 自建算法正确性全面验证")
    print("=" * 80)

    # 验证1: 五行映射
    wuxing_correct = verify_wuxing_mapping()

    # 验证2: 地支藏干
    canggan_correct = verify_dizhi_canggan()

    # 验证3: 五行计算
    calculation_correct = verify_wuxing_calculation()

    # 验证4: 十神逻辑
    shishen_correct = verify_shishen_logic()

    # 验证5: 在线对比
    online_correct = verify_with_online_calculator()

    # 总结
    print("\n" + "=" * 80)
    print("🎉 验证总结:")
    print(f"  五行映射: {'✅' if wuxing_correct else '❌'}")
    print(f"  地支藏干: {'✅' if canggan_correct else '❌'}")
    print(f"  五行计算: {'✅' if calculation_correct else '❌'}")
    print(f"  十神逻辑: {'✅' if shishen_correct else '❌'}")
    print(f"  在线对比: {'✅' if online_correct else '❌'}")

    all_tests = [wuxing_correct, canggan_correct, calculation_correct, shishen_correct, online_correct]

    if all(all_tests):
        print("\n🎊 所有验证通过！自建算法完全正确")
    else:
        print("\n⚠️ 部分验证失败，需要修正算法")
        failed_count = sum(1 for test in all_tests if not test)
        print(f"失败项目: {failed_count}/{len(all_tests)}")

if __name__ == "__main__":
    main()
