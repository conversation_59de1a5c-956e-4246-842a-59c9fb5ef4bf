#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首页
"""

import streamlit as st
from config import config

def show_home_page():
    """显示首页"""
    
    # 主标题
    st.markdown("""
    <div class="main-header">
        <h1>🔮 智能算命AI系统 v4.0</h1>
        <p>基于简洁稳定架构的专业命理分析系统</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 功能介绍
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>📊 真实排盘</h3>
            <p>基于py-iztro算法的紫薇斗数和八字排盘，确保计算准确性，与权威网站结果一致。</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="feature-card">
            <h3>🔍 深度分析</h3>
            <p>12角度专业分析，每个角度4000-5000字深度解读，涵盖命运、财富、婚姻、健康等。</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h3>🎲 六爻占卜</h3>
            <p>传统六爻占卜系统，支持时间起卦和数字起卦，AI结合传统理论解卦。</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="feature-card">
            <h3>💑 合婚分析</h3>
            <p>双人配对分析，多维度兼容性评估，提供量化匹配度和具体建议。</p>
        </div>
        """, unsafe_allow_html=True)
    
    # 系统特点
    st.markdown("---")
    st.markdown("## ✨ 系统特点")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        ### 🎯 简洁稳定
        - 模块独立设计
        - 架构简洁清晰
        - 运行稳定可靠
        - 易于维护扩展
        """)
    
    with col2:
        st.markdown("""
        ### 🔮 算法准确
        - 真实算法支撑
        - 数据一致性保证
        - 权威验证通过
        - 无LLM编造
        """)
    
    with col3:
        st.markdown("""
        ### 💻 现代界面
        - 清晰功能导航
        - 响应式设计
        - 实时进度反馈
        - 结果导出支持
        """)
    
    # 快速开始
    st.markdown("---")
    st.markdown("## 🚀 快速开始")
    
    st.markdown("""
    ### 使用流程：
    1. **📊 生成排盘** - 输入生辰信息，生成紫薇+八字排盘
    2. **🔍 深度分析** - 选择分析角度，获取专业解读
    3. **📋 查看结果** - 浏览分析结果，导出HTML报告
    
    ### 示例输入：
    - **出生时间**: 1988年6月1日11时
    - **性别**: 男/女
    - **分析角度**: 命宫性格、财富运势、婚姻感情等
    """)
    
    # 操作按钮
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 开始排盘", use_container_width=True, type="primary"):
            st.session_state.current_page = 'chart'
            st.rerun()
    
    with col2:
        if st.button("🔍 查看分析", use_container_width=True):
            st.session_state.current_page = 'analysis'
            st.rerun()
    
    with col3:
        if st.button("🎲 六爻占卜", use_container_width=True):
            st.session_state.current_page = 'liuyao'
            st.rerun()
    
    # 免责声明
    st.markdown("---")
    st.markdown("""
    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; border-left: 4px solid #ffc107;">
        <h4>⚠️ 免责声明</h4>
        <p>本系统仅供娱乐、学习和学术研究使用，不构成任何形式的人生建议或决策依据。
        算命结果仅供参考，请理性对待。开发者不对使用本系统产生的任何后果承担责任。</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 技术信息
    with st.expander("🔧 技术信息"):
        st.markdown(f"""
        **系统版本**: v4.0 重构版  
        **LLM模型**: {config.llm.model_name}  
        **算法库**: py-iztro v0.1.5  
        **架构**: 简洁分层架构  
        **特点**: 模块独立、稳定可靠、易于扩展
        """)
        
        # 显示配置状态
        if config.llm.api_key:
            st.success("✅ API配置正常")
        else:
            st.error("❌ API未配置，请检查环境变量")
        
        st.info("💡 如需帮助，请查看侧边栏的使用帮助")
