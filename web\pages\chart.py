#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排盘页面
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from models.birth_info import BirthInfo, get_hour_name
from utils.simple_logger import get_logger

logger = get_logger()

def show_chart_page():
    """显示排盘页面"""
    
    st.markdown("""
    <div class="main-header">
        <h1>📊 生成排盘</h1>
        <p>输入生辰信息，生成紫薇斗数和八字排盘</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 输入表单
    with st.form("birth_info_form"):
        st.markdown("### 📝 请输入生辰信息")
        
        col1, col2 = st.columns(2)
        
        with col1:
            year = st.number_input("出生年份", min_value=1900, max_value=2100, value=1990)
            month = st.number_input("出生月份", min_value=1, max_value=12, value=1)
            day = st.number_input("出生日期", min_value=1, max_value=31, value=1)
        
        with col2:
            hour = st.number_input("出生小时", min_value=0, max_value=23, value=12)
            gender = st.selectbox("性别", ["男", "女"])
            
            # 显示时辰信息
            hour_name = get_hour_name(hour)
            st.info(f"时辰: {hour_name}")
        
        # 提交按钮
        submitted = st.form_submit_button("🔮 生成排盘", use_container_width=True, type="primary")
        
        if submitted:
            try:
                # 创建生辰信息对象
                birth_info = BirthInfo(year, month, day, hour, gender)
                
                # 显示输入信息确认
                st.success(f"✅ 生辰信息: {birth_info.to_display_string()}")
                
                # 生成排盘
                with st.spinner("🔄 正在生成排盘，请稍候..."):
                    chart_service = st.session_state.chart_service
                    chart_data = chart_service.generate_chart(birth_info)
                
                if chart_data.success:
                    st.success("🎉 排盘生成成功！")
                    
                    # 保存到会话状态
                    st.session_state.current_chart_data = chart_data
                    
                    # 显示排盘结果
                    show_chart_result(chart_data)
                    
                    # 操作按钮
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        if st.button("🔍 开始分析", use_container_width=True, type="primary"):
                            st.session_state.current_page = 'analysis'
                            st.rerun()
                    
                    with col2:
                        if st.button("🎲 六爻占卜", use_container_width=True):
                            st.session_state.current_page = 'liuyao'
                            st.rerun()
                    
                    with col3:
                        if st.button("💑 合婚分析", use_container_width=True):
                            st.session_state.current_page = 'compatibility'
                            st.rerun()
                
                else:
                    st.error(f"❌ 排盘生成失败: {chart_data.error_message}")
                    
            except ValueError as e:
                st.error(f"❌ 输入错误: {e}")
            except Exception as e:
                logger.error(f"排盘生成异常: {e}")
                st.error(f"❌ 系统异常: {e}")
    
    # 显示历史记录
    show_chart_history()

def show_chart_result(chart_data):
    """显示排盘结果"""
    
    st.markdown("---")
    st.markdown("## 📊 排盘结果")
    
    # 基本信息
    with st.expander("📋 基本信息", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"""
            **出生时间**: {chart_data.birth_info.to_display_string()}  
            **计算时间**: {chart_data.calculation_time.strftime('%Y-%m-%d %H:%M:%S')}  
            **缓存键**: `{chart_data.get_cache_key()}`
            """)
        
        with col2:
            if chart_data.ziwei_chart and chart_data.bazi_chart:
                st.success("✅ 紫薇斗数和八字排盘均已生成")
            elif chart_data.ziwei_chart:
                st.warning("⚠️ 仅紫薇斗数排盘生成成功")
            elif chart_data.bazi_chart:
                st.warning("⚠️ 仅八字排盘生成成功")
            else:
                st.error("❌ 排盘生成失败")
    
    # 紫薇斗数结果
    if chart_data.ziwei_chart:
        with st.expander("🔮 紫薇斗数排盘"):
            show_ziwei_chart(chart_data.ziwei_chart)
    
    # 八字结果
    if chart_data.bazi_chart:
        with st.expander("📜 八字命理排盘"):
            show_bazi_chart(chart_data.bazi_chart)

def show_ziwei_chart(ziwei_chart):
    """显示紫薇斗数排盘"""
    
    # 命宫信息
    main_palace = ziwei_chart.get_main_palace()
    if main_palace:
        st.markdown("### 🏛️ 命宫信息")
        st.json(main_palace)
    
    # 十二宫信息
    if ziwei_chart.palaces:
        st.markdown("### 🏯 十二宫详情")
        
        # 创建3x4网格显示十二宫
        palace_names = list(ziwei_chart.palaces.keys())
        
        for i in range(0, len(palace_names), 3):
            cols = st.columns(3)
            for j, col in enumerate(cols):
                if i + j < len(palace_names):
                    palace_name = palace_names[i + j]
                    palace_data = ziwei_chart.palaces[palace_name]
                    
                    with col:
                        st.markdown(f"**{palace_name}**")
                        if isinstance(palace_data, dict):
                            for key, value in palace_data.items():
                                st.text(f"{key}: {value}")
                        else:
                            st.text(str(palace_data))

def show_bazi_chart(bazi_chart):
    """显示八字排盘"""
    
    # 四柱信息
    if bazi_chart.four_pillars:
        st.markdown("### 📜 四柱信息")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.markdown(f"**年柱**  \n{bazi_chart.get_year_pillar()}")
        with col2:
            st.markdown(f"**月柱**  \n{bazi_chart.get_month_pillar()}")
        with col3:
            st.markdown(f"**日柱**  \n{bazi_chart.get_day_pillar()}")
        with col4:
            st.markdown(f"**时柱**  \n{bazi_chart.get_hour_pillar()}")
    
    # 五行分布
    if bazi_chart.wuxing:
        st.markdown("### 🌟 五行分布")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 五行数值
            for element, value in bazi_chart.wuxing.items():
                st.metric(element, f"{value:.1f}")
        
        with col2:
            # 五行总结
            wuxing_summary = bazi_chart.get_wuxing_summary()
            st.info(f"五行特点: {wuxing_summary}")
    
    # 十神信息
    if bazi_chart.ten_gods:
        st.markdown("### 🎭 十神信息")
        st.json(bazi_chart.ten_gods)

def show_chart_history():
    """显示排盘历史"""
    
    st.markdown("---")
    st.markdown("## 📚 最近排盘")
    
    # 这里可以从缓存中获取最近的排盘记录
    # 暂时显示提示信息
    st.info("💡 排盘记录会自动保存，您可以随时查看历史记录")
    
    if 'current_chart_data' in st.session_state:
        chart_data = st.session_state.current_chart_data
        st.markdown(f"**当前排盘**: {chart_data.birth_info.to_display_string()}")
        
        if st.button("🔄 重新生成排盘"):
            del st.session_state.current_chart_data
            st.rerun()
