#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理和重试机制 - 提供健壮的错误处理和自动重试功能
"""

import time
import logging
import functools
from typing import Dict, Any, Optional, Callable, Type, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RetryConfig:
    """重试配置"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_base: float = 2.0):
        """
        初始化重试配置
        
        Args:
            max_attempts: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            exponential_base: 指数退避基数
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        """初始化错误处理器"""
        self.error_stats = {
            "total_errors": 0,
            "error_types": {},
            "recent_errors": [],
            "last_error_time": None
        }
        
        logger.info("错误处理器初始化完成")
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            
        Returns:
            错误处理结果
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # 更新统计
        self.error_stats["total_errors"] += 1
        self.error_stats["error_types"][error_type] = \
            self.error_stats["error_types"].get(error_type, 0) + 1
        self.error_stats["last_error_time"] = datetime.now()
        
        # 记录最近错误
        error_record = {
            "type": error_type,
            "message": error_message,
            "time": datetime.now().isoformat(),
            "context": context or {}
        }
        
        self.error_stats["recent_errors"].append(error_record)
        if len(self.error_stats["recent_errors"]) > 100:
            self.error_stats["recent_errors"] = self.error_stats["recent_errors"][-100:]
        
        # 记录日志
        logger.error(f"错误处理: {error_type}: {error_message}", exc_info=True)
        
        # 生成用户友好的错误消息
        user_message = self._generate_user_message(error, context)
        
        return {
            "error_type": error_type,
            "error_message": error_message,
            "user_message": user_message,
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "recoverable": self._is_recoverable_error(error)
        }
    
    def _generate_user_message(self, error: Exception, context: Dict[str, Any] = None) -> str:
        """生成用户友好的错误消息"""
        
        error_type = type(error).__name__
        
        # 网络相关错误
        if "ConnectionError" in error_type or "Timeout" in error_type:
            return "网络连接出现问题，请稍后重试"
        
        # API相关错误
        if "401" in str(error) or "Unauthorized" in str(error):
            return "API密钥验证失败，请检查配置"
        
        if "429" in str(error) or "Rate" in str(error):
            return "请求过于频繁，请稍后重试"
        
        if "500" in str(error) or "Internal" in str(error):
            return "服务器内部错误，请稍后重试"
        
        # LLM相关错误
        if "model" in str(error).lower():
            return "AI模型调用失败，请稍后重试"
        
        # 算法相关错误
        if context and context.get("tool_name"):
            tool_name = context["tool_name"]
            if tool_name == "ziwei":
                return "紫薇斗数分析失败，请检查出生信息是否正确"
            elif tool_name == "bazi":
                return "八字分析失败，请检查出生信息是否正确"
            elif tool_name == "liuyao":
                return "六爻算卦失败，请重新提问"
        
        # 通用错误
        return "处理您的请求时出现了问题，请稍后重试"
    
    def _is_recoverable_error(self, error: Exception) -> bool:
        """判断错误是否可恢复"""
        
        error_str = str(error).lower()
        
        # 可恢复的错误类型
        recoverable_patterns = [
            "timeout", "connection", "network", "429", "500", "502", "503", "504"
        ]
        
        for pattern in recoverable_patterns:
            if pattern in error_str:
                return True
        
        return False
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return self.error_stats.copy()

def retry_on_error(retry_config: RetryConfig = None, 
                  retry_on: Tuple[Type[Exception], ...] = (Exception,),
                  ignore_on: Tuple[Type[Exception], ...] = ()):
    """
    重试装饰器
    
    Args:
        retry_config: 重试配置
        retry_on: 需要重试的异常类型
        ignore_on: 不重试的异常类型
    """
    if retry_config is None:
        retry_config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(retry_config.max_attempts):
                try:
                    return func(*args, **kwargs)
                    
                except ignore_on as e:
                    # 不重试的异常直接抛出
                    logger.debug(f"不重试异常: {type(e).__name__}: {e}")
                    raise
                    
                except retry_on as e:
                    last_exception = e
                    
                    if attempt < retry_config.max_attempts - 1:
                        # 计算延迟时间
                        delay = min(
                            retry_config.base_delay * (retry_config.exponential_base ** attempt),
                            retry_config.max_delay
                        )
                        
                        logger.warning(f"第{attempt + 1}次尝试失败，{delay:.1f}秒后重试: {type(e).__name__}: {e}")
                        time.sleep(delay)
                    else:
                        logger.error(f"所有重试尝试失败: {type(e).__name__}: {e}")
            
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator

def circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
    """
    断路器装饰器
    
    Args:
        failure_threshold: 失败阈值
        recovery_timeout: 恢复超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        # 断路器状态
        state = {
            "failures": 0,
            "last_failure_time": None,
            "is_open": False
        }
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            now = datetime.now()
            
            # 检查断路器是否应该恢复
            if state["is_open"] and state["last_failure_time"]:
                if now - state["last_failure_time"] > timedelta(seconds=recovery_timeout):
                    state["is_open"] = False
                    state["failures"] = 0
                    logger.info(f"断路器恢复: {func.__name__}")
            
            # 如果断路器打开，直接抛出异常
            if state["is_open"]:
                raise Exception(f"断路器打开，服务暂时不可用: {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                # 成功时重置失败计数
                state["failures"] = 0
                return result
                
            except Exception as e:
                state["failures"] += 1
                state["last_failure_time"] = now
                
                # 检查是否达到失败阈值
                if state["failures"] >= failure_threshold:
                    state["is_open"] = True
                    logger.error(f"断路器打开: {func.__name__} 失败次数达到阈值 {failure_threshold}")
                
                raise
        
        return wrapper
    return decorator

class GracefulShutdown:
    """优雅关闭处理器"""
    
    def __init__(self):
        """初始化优雅关闭处理器"""
        self.shutdown_handlers = []
        self.is_shutting_down = False
        
        logger.info("优雅关闭处理器初始化完成")
    
    def register_handler(self, handler: Callable):
        """注册关闭处理器"""
        self.shutdown_handlers.append(handler)
        logger.debug(f"注册关闭处理器: {handler.__name__}")
    
    def shutdown(self):
        """执行优雅关闭"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        logger.info("开始优雅关闭...")
        
        for handler in self.shutdown_handlers:
            try:
                logger.info(f"执行关闭处理器: {handler.__name__}")
                handler()
            except Exception as e:
                logger.error(f"关闭处理器执行失败: {handler.__name__}: {e}")
        
        logger.info("优雅关闭完成")

# 全局实例
error_handler = ErrorHandler()
graceful_shutdown = GracefulShutdown()

def handle_error(error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """处理错误的全局函数"""
    return error_handler.handle_error(error, context)

def get_error_stats() -> Dict[str, Any]:
    """获取错误统计的全局函数"""
    return error_handler.get_error_stats()

if __name__ == "__main__":
    # 测试错误处理
    print("错误处理系统测试")
    print("=" * 50)
    
    # 测试重试装饰器
    @retry_on_error(RetryConfig(max_attempts=3, base_delay=0.1))
    def test_retry_function():
        import random
        if random.random() < 0.7:  # 70%概率失败
            raise ConnectionError("网络连接失败")
        return "成功"
    
    try:
        result = test_retry_function()
        print(f"重试测试结果: {result}")
    except Exception as e:
        print(f"重试测试失败: {e}")
    
    # 测试错误处理
    try:
        raise ValueError("测试错误")
    except Exception as e:
        error_result = handle_error(e, {"test": "context"})
        print(f"错误处理结果: {error_result['user_message']}")
    
    # 显示错误统计
    stats = get_error_stats()
    print(f"错误统计: {stats}")
