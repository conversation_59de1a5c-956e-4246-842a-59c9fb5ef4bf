#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的数据处理功能
"""

import asyncio

async def test_single_analysis():
    """测试单个分析生成"""
    print("🔧 测试修复后的单个分析生成")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 生成排盘数据
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=1988,
            month=6,
            day=1,
            hour=11,
            gender="男"
        )
        
        if not raw_data.get("success"):
            print(f"❌ 排盘数据生成失败: {raw_data.get('error', '')}")
            return False
        
        print("✅ 排盘数据生成成功")
        print(f"📊 数据结构: {list(raw_data.keys())}")
        
        # 检查数据内容
        if "ziwei_analysis" in raw_data:
            ziwei_data = raw_data["ziwei_analysis"]
            if isinstance(ziwei_data, dict) and "palaces" in ziwei_data:
                palaces = ziwei_data["palaces"]
                print(f"🏛️ 宫位数量: {len(palaces)}")
                if "命宫" in palaces:
                    mingong = palaces["命宫"]
                    print(f"📍 命宫数据: {mingong}")
                else:
                    print("⚠️ 命宫数据缺失")
            else:
                print("⚠️ 紫薇数据格式异常")
        
        if "bazi_analysis" in raw_data:
            bazi_data = raw_data["bazi_analysis"]
            if isinstance(bazi_data, dict):
                print("✅ 八字数据存在")
                bazi_info = bazi_data.get("bazi_info", {})
                if bazi_info:
                    chinese_date = bazi_info.get("chinese_date", "")
                    print(f"🔮 八字: {chinese_date}")
            else:
                print("⚠️ 八字数据格式异常")
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent()
        
        # 测试生成单个角度分析
        birth_info = {
            "year": "1988",
            "month": "6", 
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        print("\n🎯 开始生成命宫分析...")
        
        analysis_result = await calculator_agent._analyze_single_angle(
            "命宫分析",
            "personality_destiny", 
            "性格命运核心特征",
            raw_data,
            birth_info,
            "紫薇+八字融合分析"
        )
        
        if analysis_result and len(analysis_result) > 100:
            print(f"✅ 单个分析生成成功: {len(analysis_result)}字")
            print(f"📝 分析预览: {analysis_result[:200]}...")
            return True
        else:
            print(f"❌ 单个分析生成失败")
            if analysis_result:
                print(f"返回内容: {analysis_result}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_web_integration():
    """测试Web界面集成"""
    print(f"\n💻 测试Web界面集成")
    print("=" * 40)
    
    try:
        from backend_agent_web import generate_single_analysis
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建测试缓存结果
        calculator_agent = FortuneCalculatorAgent("web_test")
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1", 
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if not raw_data.get("success"):
            print("❌ 排盘数据生成失败")
            return False
        
        # 保存到缓存
        result_id = calculator_agent.cache.save_result(
            user_id="web_test_user",
            session_id="web_test_session",
            calculation_type="ziwei",
            birth_info=birth_info,
            raw_calculation=raw_data,
            detailed_analysis={"angle_analyses": {}},
            summary="Web界面测试排盘",
            keywords=["紫薇", "八字", "Web测试"],
            confidence=0.9
        )
        
        print(f"📊 缓存结果ID: {result_id}")
        
        # 测试Web界面的单个分析生成
        success = generate_single_analysis(result_id, "personality_destiny", "命宫分析 - 性格命运核心特征")
        
        if success:
            print("✅ Web界面单个分析生成成功")
            
            # 验证分析是否保存
            cached_result = calculator_agent.cache.get_result(result_id)
            if cached_result and cached_result.detailed_analysis:
                angle_analyses = cached_result.detailed_analysis.get("angle_analyses", {})
                if "personality_destiny" in angle_analyses:
                    analysis_content = angle_analyses["personality_destiny"]
                    if analysis_content and len(analysis_content) > 100:
                        print(f"✅ 分析内容已保存: {len(analysis_content)}字")
                        return True
                    else:
                        print("❌ 分析内容为空或过短")
                        return False
                else:
                    print("❌ 分析结果未保存")
                    return False
            else:
                print("❌ 无法获取缓存结果")
                return False
        else:
            print("❌ Web界面单个分析生成失败")
            return False
            
    except Exception as e:
        print(f"❌ Web界面集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 按需生成功能修复验证")
    print("=" * 70)
    
    # 1. 测试单个分析生成
    analysis_success = await test_single_analysis()
    
    # 2. 测试Web界面集成
    web_success = await test_web_integration()
    
    print("\n" + "=" * 70)
    print("🎯 按需生成功能修复验证结果:")
    
    if analysis_success:
        print("✅ 单个分析生成功能正常")
    else:
        print("❌ 单个分析生成功能异常")
    
    if web_success:
        print("✅ Web界面集成功能正常")
    else:
        print("❌ Web界面集成功能异常")
    
    if analysis_success and web_success:
        print("\n🎉 按需生成功能修复完成！")
        print("💡 现在可以启动Web界面体验:")
        print("  1. ✅ 排盘完成后不自动生成分析")
        print("  2. ✅ 12个分析按钮按需点击生成")
        print("  3. ✅ 异步处理不阻塞界面")
        print("  4. ✅ 生成状态实时更新")
        print("\n🚀 启动命令: streamlit run backend_agent_web.py")
    else:
        print("\n⚠️ 还有功能需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
