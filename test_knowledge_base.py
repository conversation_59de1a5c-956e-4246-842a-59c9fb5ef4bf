#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库方案
"""

import sys
sys.path.append('.')

from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
from core.chat.knowledge_base import ChatKnowledgeBase
from core.nlu.llm_client import LLMClient
import json

def test_knowledge_base_loading():
    """测试知识库加载"""
    print("🧪 测试知识库加载功能")
    print("=" * 50)
    
    try:
        # 获取现有的缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        # 获取缓存统计
        cache_stats = cache.get_cache_stats()
        print(f"📊 缓存统计: {cache_stats}")
        
        if cache_stats['total_results'] == 0:
            print("⚠️  没有缓存数据，无法测试")
            return False
            
        # 获取第一个缓存结果
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        if not cache_files:
            print("❌ 没有找到缓存文件")
            return False
            
        # 读取第一个缓存文件
        cache_file = cache_files[0]
        result_id = cache_file.stem
        
        print(f"📄 测试缓存文件: {cache_file.name}")
        
        # 获取缓存结果
        cached_result = cache.get_result(result_id)
        if not cached_result:
            print("❌ 无法获取缓存结果")
            return False
            
        print("✅ 缓存结果获取成功")
        
        # 创建知识库
        knowledge_base = ChatKnowledgeBase()
        print("✅ 知识库创建成功")
        
        # 加载知识
        knowledge_base.load_from_cache_result(cached_result)
        print("✅ 知识加载完成")
        
        # 获取统计
        kb_stats = knowledge_base.get_stats()
        print(f"📊 知识库统计: {kb_stats}")
        
        # 显示知识项
        print(f"\n📋 知识项详情:")
        for category, category_name in knowledge_base.categories.items():
            items = knowledge_base.get_knowledge_by_category(category)
            if items:
                print(f"  {category_name}: {len(items)}项")
                for item in items[:3]:  # 只显示前3项
                    print(f"    - {item.key}: {item.value[:50]}...")
                    
        # 格式化为LLM文本
        formatted_text = knowledge_base.format_for_llm(max_length=1000)
        print(f"\n📝 格式化文本 (前500字符):")
        print(formatted_text[:500] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_base_chat():
    """测试基于知识库的聊天"""
    print(f"\n🤖 测试知识库聊天功能")
    print("=" * 50)
    
    try:
        # 获取现有的缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        # 获取第一个缓存结果
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        if not cache_files:
            print("❌ 没有找到缓存文件")
            return False
            
        cache_file = cache_files[0]
        result_id = cache_file.stem
        cached_result = cache.get_result(result_id)
        
        # 创建知识库
        knowledge_base = ChatKnowledgeBase()
        knowledge_base.load_from_cache_result(cached_result)
        
        # 格式化知识库
        knowledge_text = knowledge_base.format_for_llm(max_length=1500)
        
        birth_info = cached_result.birth_info
        
        # 构建系统提示词
        system_prompt = f"""
你是一位专业的命理分析师，现在要基于用户的专属知识库回答问题。

【用户基本信息】
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}
- 性别：{birth_info.get('gender')}

{knowledge_text}

【回答要求】
1. 优先使用知识库中的信息回答
2. 结合紫薇斗数和八字命理提供具体依据
3. 语言通俗易懂，像朋友聊天一样
4. 回答要具体实用，避免空话套话
5. 回答要完整详细，控制在600-1000字之间
6. 确保回答有完整的结尾，不要突然截断
7. 回答结构要完整：开头-分析-建议-总结

请根据用户的专属知识库，专业而亲切地回答问题。回答必须完整，有始有终。
"""
        
        # 测试问题
        test_question = "我的财运如何？有什么需要注意的吗？"
        
        print(f"📝 测试问题: {test_question}")
        print(f"📊 系统提示词长度: {len(system_prompt)} 字符")
        print(f"📊 知识库长度: {len(knowledge_text)} 字符")
        
        # 调用LLM
        llm_client = LLMClient()
        response = llm_client.chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": test_question}
            ],
            temperature=0.7,
            max_tokens=4096
        )
        
        if response:
            print(f"\n🤖 LLM回复:")
            print("-" * 30)
            print(response)
            print("-" * 30)
            
            # 分析回复质量
            print(f"\n📊 回复质量分析:")
            print(f"✅ 回复长度: {len(response)} 字符")
            
            # 检查知识库使用情况
            knowledge_usage = 0
            for item in knowledge_base.knowledge_items:
                if item.value and len(item.value) > 3:  # 忽略太短的值
                    if item.value in response:
                        knowledge_usage += 1
                        
            print(f"✅ 知识库使用: {knowledge_usage}/{len(knowledge_base.knowledge_items)} 项")
            
            # 检查完整性
            has_greeting = any(word in response for word in ["你好", "您好", "朋友", "根据"])
            has_analysis = any(word in response for word in ["分析", "看出", "显示", "命盘"])
            has_suggestion = any(word in response for word in ["建议", "推荐", "可以", "注意"])
            has_conclusion = any(word in response for word in ["总的来说", "综合", "总结", "总体"])
            
            print(f"✅ 包含问候/开头: {has_greeting}")
            print(f"✅ 包含分析: {has_analysis}")
            print(f"✅ 包含建议: {has_suggestion}")
            print(f"✅ 包含总结: {has_conclusion}")
            
            # 检查是否截断
            truncation_indicators = [
                response.endswith("..."),
                response.endswith("。。。"),
                response.endswith("，"),
                response.endswith("、"),
                len(response) < 300,
            ]
            
            is_truncated = any(truncation_indicators)
            print(f"✅ 回复完整性: {'❌ 可能截断' if is_truncated else '✅ 完整'}")
            
            # 整体评分
            completeness_score = sum([
                not is_truncated,
                has_greeting,
                has_analysis,
                has_suggestion,
                len(response) >= 400,
                knowledge_usage > 0
            ])
            
            print(f"\n🎯 知识库聊天评分: {completeness_score}/6")
            
            if completeness_score >= 5:
                print("🎉 知识库方案效果优秀！")
            elif completeness_score >= 4:
                print("✅ 知识库方案效果良好")
            elif completeness_score >= 3:
                print("⚠️  知识库方案效果一般")
            else:
                print("❌ 知识库方案需要优化")
                
            return completeness_score >= 4
            
        else:
            print("❌ LLM回复为空")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 知识库方案测试")
    print("=" * 60)
    
    # 测试知识库加载
    loading_success = test_knowledge_base_loading()
    
    if loading_success:
        # 测试知识库聊天
        chat_success = test_knowledge_base_chat()
        
        print(f"\n🎯 总体测试结果:")
        print(f"  知识库加载: {'✅ 成功' if loading_success else '❌ 失败'}")
        print(f"  知识库聊天: {'✅ 成功' if chat_success else '❌ 失败'}")
        
        if loading_success and chat_success:
            print("🎉 知识库方案测试通过！")
        else:
            print("⚠️  知识库方案需要进一步优化")
    else:
        print("❌ 知识库加载失败，无法进行后续测试")
