#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字准确性深度分析
对比不同来源的1988年6月1日八字结果
"""

def analyze_bazi_sources():
    """分析不同来源的八字结果"""
    print("🔍 1988年6月1日八字准确性分析")
    print("=" * 60)
    
    # 收集到的不同来源结果
    sources = {
        "py-iztro (紫薇斗数)": "戊辰 丁巳 丁亥 丙午",
        "网络资料 (李佳薇)": "戊辰 丁巳 丁亥 庚子",  # 但时辰不同
        "当前八字算法": "戊辰 戊未 乙巳 壬午",
        "理论推算": "戊辰 丁巳 ? 丙午"
    }
    
    print("📊 不同来源的八字结果对比:")
    for source, bazi in sources.items():
        print(f"  {source:20}: {bazi}")
    
    print("\n🔍 逐柱分析:")
    
    # 年柱分析
    print("1. 年柱 (1988年):")
    print("   ✅ 所有来源一致: 戊辰")
    print("   📝 1988年确实是戊辰年")
    
    # 月柱分析  
    print("\n2. 月柱 (农历四月):")
    print("   ✅ py-iztro: 丁巳")
    print("   ✅ 网络资料: 丁巳") 
    print("   ❌ 当前算法: 戊未")
    print("   📝 农历四月应该是丁巳月，当前算法有误")
    
    # 日柱分析
    print("\n3. 日柱 (1988年6月1日):")
    print("   ✅ py-iztro: 丁亥")
    print("   ✅ 网络资料: 丁亥")
    print("   ❌ 当前算法: 乙巳") 
    print("   📝 1988年6月1日应该是丁亥日，当前算法有误")
    
    # 时柱分析
    print("\n4. 时柱 (11时-午时):")
    print("   ✅ py-iztro: 丙午")
    print("   ⚠️ 网络资料: 庚子 (可能是不同时辰)")
    print("   ❌ 当前算法: 壬午")
    print("   📝 午时应该是丙午，当前算法有误")

def test_current_algorithms():
    """测试当前算法的具体问题"""
    print("\n🧪 测试当前算法")
    print("=" * 40)
    
    try:
        # 测试py-iztro (正确的)
        print("1. 测试py-iztro算法:")
        import py_iztro
        astro = py_iztro.Astro()
        astrolabe = astro.by_solar("1988-6-1", 6, "男", "zh-CN")  # 6对应午时
        print(f"   结果: {astrolabe.chinese_date}")
        
        # 测试当前八字算法
        print("\n2. 测试当前八字算法:")
        from algorithms.real_bazi_calculator import RealBaziCalculator
        calc = RealBaziCalculator()
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        if result["success"]:
            ganzhi = result["raw_result"].get("干支", {})
            bazi_text = ganzhi.get("文本", "")
            print(f"   结果: {bazi_text}")
        else:
            print(f"   错误: {result['error']}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def identify_root_cause():
    """识别根本原因"""
    print("\n🎯 问题根本原因分析")
    print("=" * 40)
    
    print("📋 发现的问题:")
    print("1. 月柱错误: 丁巳 → 戊未")
    print("2. 日柱错误: 丁亥 → 乙巳") 
    print("3. 时柱错误: 丙午 → 壬午")
    
    print("\n🔍 可能的原因:")
    print("1. 使用的万年历数据不准确")
    print("2. 节气交替时间计算错误")
    print("3. 干支推算算法有误")
    print("4. 时区或真太阳时转换问题")
    
    print("\n💡 解决方案:")
    print("1. 统一使用py-iztro作为八字数据源")
    print("2. 修改八字算法调用py-iztro获取准确八字")
    print("3. 保持紫薇和八字数据的一致性")
    print("4. 验证修改后的准确性")

def recommend_fix():
    """推荐修复方案"""
    print("\n🛠️ 推荐修复方案")
    print("=" * 40)
    
    print("方案1: 统一数据源")
    print("- 修改real_bazi_calculator.py")
    print("- 使用py-iztro获取准确八字")
    print("- 保持与紫薇斗数的一致性")
    
    print("\n方案2: 验证流程")
    print("- 对比多个标准时间的八字")
    print("- 确保节气交替正确")
    print("- 验证时辰干支推算")
    
    print("\n方案3: 测试用例")
    print("- 创建标准测试用例")
    print("- 包含不同年份、月份、日期")
    print("- 确保算法稳定性")

def main():
    """主函数"""
    analyze_bazi_sources()
    test_current_algorithms()
    identify_root_cause()
    recommend_fix()
    
    print("\n" + "=" * 60)
    print("🎯 结论:")
    print("当前八字算法存在严重准确性问题")
    print("建议统一使用py-iztro作为八字数据源")
    print("确保紫薇斗数和八字的数据一致性")

if __name__ == "__main__":
    main()
