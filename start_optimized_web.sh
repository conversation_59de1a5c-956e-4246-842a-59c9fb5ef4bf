#!/bin/bash
# 紫薇+八字融合分析后台系统 v3.0 - 优化版启动脚本 (Linux/macOS)

# 设置UTF-8编码
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

echo ""
echo "🔮 紫薇+八字融合分析后台系统 v3.0 - 优化版"
echo "============================================================"
echo ""

# 检查Python版本
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python未安装，请先安装Python 3.8+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python环境检查通过"
echo ""

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
echo "Python版本: $PYTHON_VERSION"

# 启动优化版系统
echo "🚀 启动优化版后台系统..."
echo ""

$PYTHON_CMD start_optimized_web.py

# 如果出错，提供备用选项
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 优化版启动失败，尝试启动原版本..."
    echo ""
    streamlit run backend_agent_web.py
fi
