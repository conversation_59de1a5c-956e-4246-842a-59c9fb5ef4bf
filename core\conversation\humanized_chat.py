#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人性化对话管理器 - 模拟真人算命师的自然交流方式
"""

import logging
import random
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ConversationStage(Enum):
    """对话阶段枚举"""
    GREETING = "greeting"                    # 问候阶段
    INFO_COLLECTION = "info_collection"      # 信息收集阶段
    CHART_PRESENTATION = "chart_presentation" # 排盘展示阶段
    ANALYSIS_INTRO = "analysis_intro"        # 分析引导阶段
    ASPECT_ANALYSIS = "aspect_analysis"      # 分段分析阶段
    INTERACTION_CHECK = "interaction_check"  # 互动检查点
    SYNTHESIS = "synthesis"                  # 综合总结阶段
    CONCLUSION = "conclusion"                # 结论阶段

@dataclass
class ConversationContext:
    """对话上下文"""
    current_stage: ConversationStage
    analysis_progress: Dict[str, bool]  # 分析进度
    user_questions: List[str]           # 用户问题记录
    interruption_count: int             # 打断次数
    birth_info: Optional[Dict[str, Any]] = None
    chart_result: Optional[Dict[str, Any]] = None

class HumanizedChatManager:
    """人性化对话管理器"""

    def __init__(self):
        """初始化对话管理器"""
        self.conversation_contexts = {}  # 会话上下文存储
        self._init_templates()
        logger.info("人性化对话管理器初始化完成")

    def _init_templates(self):
        """初始化口语化模板库"""
        self.templates = {
            # 开场语模板（融入专业算命师风格）
            "greeting": [
                "您好！我是您的专属算命师，很高兴为您服务。根据传统命理学，我将为您提供专业的分析。",
                "欢迎来到智能算命！我会根据您的生辰八字，用专业的方法为您解读命理。",
                "您好！我来为您进行详细的命理分析，通过紫薇斗数为您指点迷津。"
            ],

            # 信息收集模板（专业化表达）
            "info_collection": [
                "为了给您准确的命理分析，我需要了解您的详细出生信息。根据传统算命学，出生时辰对命运影响极大。",
                "请告诉我您的出生年月日时和性别，这样我才能为您精确排盘。每一个时辰都关系到您的命运走向。",
                "麻烦您提供一下准确的出生时间，包括年月日时和性别。这是推算命理的基础信息。"
            ],

            # 排盘展示模板（专业算命师风格）
            "chart_presentation": [
                "好的，我已经根据您的生辰八字为您排好了{fortune_type}命盘。从您的命盘来看，有很多值得深入分析的地方。",
                "您是{birth_info}出生的{gender}性，根据传统命理学，我看到您的命盘显示出独特的格局...",
                "从{fortune_type}的角度来看，您的命盘已经排好了。接下来我将为您详细解读其中的奥秘。"
            ],

            # 分析引导模板（传统算命师风格）
            "analysis_intro": [
                "根据您的生辰八字，我准备从四个主要方面为您详细分析：性格特质、事业运势、财运状况、感情婚姻。每个方面都关系到您的人生走向。",
                "接下来我会按照传统命理学的方法，分几个方面来解读您的命运。您想先了解哪个方面呢？还是让我按顺序为您分析？",
                "我按照古法，从几个重要角度为您分析命理。这些都是影响您一生运势的关键因素。"
            ],

            # 互动检查模板
            "interaction_check": [
                "关于这个方面，您有什么想问的吗？",
                "这部分您还有疑问吗？我可以详细解释。",
                "您对刚才的分析有什么想了解的？",
                "我继续分析下一个方面，还是您先问问题？"
            ],

            # 过渡语模板
            "transition": [
                "接下来我们看看",
                "现在我们来分析",
                "关于这个方面",
                "从另一个角度来看"
            ],

            # 总结语模板
            "synthesis": [
                "综合来看，您的命理特点是...",
                "从整体上分析，您的运势呈现出...",
                "总的来说，您的命盘显示..."
            ],

            # 结论模板
            "conclusion": [
                "以上就是我为您做的详细分析，希望对您有帮助。",
                "这就是您的命理分析，有什么具体问题可以继续问我。",
                "分析完毕，您还想深入了解哪个方面吗？"
            ]
        }

        # 四个分析方面
        self.analysis_aspects = {
            "personality": {
                "name": "性格特质",
                "description": "分析您的性格特点、天赋才能和行为模式"
            },
            "career": {
                "name": "事业运势",
                "description": "解读您的职业发展、事业机遇和工作状况"
            },
            "wealth": {
                "name": "财运状况",
                "description": "分析您的财富运势、理财能力和投资方向"
            },
            "love": {
                "name": "感情婚姻",
                "description": "解读您的感情运势、婚姻状况和人际关系"
            }
        }

    def get_conversation_context(self, session_id: str) -> ConversationContext:
        """获取对话上下文"""
        if session_id not in self.conversation_contexts:
            self.conversation_contexts[session_id] = ConversationContext(
                current_stage=ConversationStage.GREETING,
                analysis_progress={aspect: False for aspect in self.analysis_aspects.keys()},
                user_questions=[],
                interruption_count=0
            )
        return self.conversation_contexts[session_id]

    def update_conversation_stage(self, session_id: str, stage: ConversationStage):
        """更新对话阶段"""
        context = self.get_conversation_context(session_id)
        context.current_stage = stage
        logger.info(f"会话 {session_id} 进入阶段: {stage.value}")

    def generate_humanized_response(self, session_id: str, response_type: str,
                                  data: Dict[str, Any] = None) -> str:
        """生成人性化响应"""
        context = self.get_conversation_context(session_id)

        if response_type == "greeting":
            return self._generate_greeting()

        elif response_type == "info_collection":
            return self._generate_info_collection(data)

        elif response_type == "chart_presentation":
            return self._generate_chart_presentation(context, data)

        elif response_type == "analysis_intro":
            return self._generate_analysis_intro()

        elif response_type == "aspect_analysis":
            return self._generate_aspect_analysis(context, data)

        elif response_type == "interaction_check":
            return self._generate_interaction_check(context)

        elif response_type == "synthesis":
            return self._generate_synthesis(data)

        elif response_type == "conclusion":
            return self._generate_conclusion()

        else:
            return "我继续为您分析..."

    def _generate_greeting(self) -> str:
        """生成问候语"""
        return random.choice(self.templates["greeting"])

    def _generate_info_collection(self, data: Dict[str, Any]) -> str:
        """生成信息收集响应"""
        base_message = random.choice(self.templates["info_collection"])

        if data and "missing_entities" in data:
            missing = data["missing_entities"]
            entity_names = {
                "birth_year": "出生年份",
                "birth_month": "出生月份",
                "birth_day": "出生日期",
                "birth_hour": "出生时辰",
                "gender": "性别"
            }
            missing_names = [entity_names.get(e, e) for e in missing]

            return f"{base_message}\n\n我还需要了解您的：{', '.join(missing_names)}。"

        return base_message

    def _generate_chart_presentation(self, context: ConversationContext,
                                   data: Dict[str, Any]) -> str:
        """生成排盘展示响应"""
        if not data or "calculation_result" not in data:
            return "排盘过程中出现了问题，请重新提供出生信息。"

        birth_info = data.get("birth_info", {})
        fortune_type = data.get("fortune_type", "命理")

        # 格式化出生信息
        birth_str = f"{birth_info.get('birth_year')}年{birth_info.get('birth_month')}月{birth_info.get('birth_day')}日{birth_info.get('birth_hour')}"
        gender = birth_info.get('gender', '未知')

        template = random.choice(self.templates["chart_presentation"])

        response = template.format(
            fortune_type=fortune_type,
            birth_info=birth_str,
            gender=gender
        )

        # 添加排盘结果描述
        calc_result = data["calculation_result"]
        if "palaces" in calc_result:
            palace_count = len(calc_result["palaces"])
            response += f"\n\n您的命盘已经排好，包含{palace_count}个宫位的完整信息。"

        # 更新上下文
        context.birth_info = birth_info
        context.chart_result = calc_result

        return response

    def _generate_analysis_intro(self) -> str:
        """生成分析引导"""
        return random.choice(self.templates["analysis_intro"])

    def _generate_aspect_analysis(self, context: ConversationContext,
                                data: Dict[str, Any]) -> str:
        """生成分段分析"""
        aspect = data.get("aspect", "personality")
        aspect_info = self.analysis_aspects.get(aspect, {})

        # 标记该方面已分析
        context.analysis_progress[aspect] = True

        transition = random.choice(self.templates["transition"])
        aspect_name = aspect_info.get("name", "这个方面")

        return f"{transition}{aspect_name}。\n\n{aspect_info.get('description', '')}"

    def _generate_interaction_check(self, context: ConversationContext) -> str:
        """生成互动检查"""
        # 根据打断次数调整语气
        if context.interruption_count == 0:
            return random.choice(self.templates["interaction_check"])
        elif context.interruption_count < 3:
            return "您还有什么想了解的吗？我很乐意为您详细解释。"
        else:
            return "您的问题很多，说明您很认真！还有什么想问的？"

    def _generate_synthesis(self, data: Dict[str, Any]) -> str:
        """生成综合总结"""
        base_message = random.choice(self.templates["synthesis"])

        # 可以根据分析结果添加具体内容
        if data and "summary_points" in data:
            points = data["summary_points"]
            summary = "\n".join([f"• {point}" for point in points])
            return f"{base_message}\n\n{summary}"

        return base_message

    def _generate_conclusion(self) -> str:
        """生成结论"""
        return random.choice(self.templates["conclusion"])

    def handle_user_interruption(self, session_id: str, user_question: str) -> str:
        """处理用户打断"""
        context = self.get_conversation_context(session_id)
        context.user_questions.append(user_question)
        context.interruption_count += 1

        # 生成回应打断的响应
        responses = [
            "好的，我先回答您这个问题。",
            "没问题，让我为您解释一下。",
            "您问得很好，我详细说明一下。",
            "这个问题很重要，我来为您分析。"
        ]

        return random.choice(responses)

    def get_next_analysis_aspect(self, session_id: str) -> Optional[str]:
        """获取下一个要分析的方面"""
        context = self.get_conversation_context(session_id)

        for aspect, completed in context.analysis_progress.items():
            if not completed:
                return aspect

        return None  # 所有方面都已分析完成

    def is_analysis_complete(self, session_id: str) -> bool:
        """检查分析是否完成"""
        context = self.get_conversation_context(session_id)
        return all(context.analysis_progress.values())

    def reset_conversation(self, session_id: str):
        """重置对话状态"""
        if session_id in self.conversation_contexts:
            del self.conversation_contexts[session_id]
        logger.info(f"会话 {session_id} 状态已重置")

def test_humanized_chat():
    """测试人性化对话管理器"""
    print("💬 测试人性化对话管理器")
    print("-" * 50)

    try:
        chat_manager = HumanizedChatManager()
        session_id = "test_humanized"

        # 测试各种响应生成
        test_cases = [
            ("greeting", {}),
            ("info_collection", {"missing_entities": ["birth_year", "gender"]}),
            ("chart_presentation", {
                "birth_info": {"birth_year": "1988", "birth_month": "6", "birth_day": "1", "birth_hour": "午时", "gender": "男"},
                "fortune_type": "紫薇斗数",
                "calculation_result": {"palaces": {"命宫": {}, "财帛宫": {}}}
            }),
            ("analysis_intro", {}),
            ("aspect_analysis", {"aspect": "personality"}),
            ("interaction_check", {}),
            ("synthesis", {"summary_points": ["性格开朗", "事业有成", "财运不错"]}),
            ("conclusion", {})
        ]

        print("测试响应生成:")
        for response_type, data in test_cases:
            response = chat_manager.generate_humanized_response(session_id, response_type, data)
            print(f"\n{response_type}: {response[:100]}...")

        # 测试打断处理
        print("\n测试打断处理:")
        interruption_response = chat_manager.handle_user_interruption(session_id, "我的财运怎么样？")
        print(f"打断响应: {interruption_response}")

        # 测试分析进度
        print(f"\n下一个分析方面: {chat_manager.get_next_analysis_aspect(session_id)}")
        print(f"分析是否完成: {chat_manager.is_analysis_complete(session_id)}")

        print("\n✅ 人性化对话管理器测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_humanized_chat()
