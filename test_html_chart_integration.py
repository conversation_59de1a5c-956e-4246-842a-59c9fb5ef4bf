#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML图表集成到后台Agent
验证HTML图表生成和显示功能
"""

import asyncio
import sys
import os
import time
from datetime import datetime

def test_html_chart_generation():
    """测试HTML图表生成"""
    try:
        print("🎨 测试HTML图表生成")
        print("=" * 60)
        
        # 导入后台Agent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        # 创建Agent实例
        agent = FortuneCalculatorAgent()
        
        # 测试数据
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": "午时",
            "gender": "男"
        }
        
        calculation_result = {
            "success": True,
            "data": {
                "palaces": {
                    "命宫": {"position": "午", "major_stars": ["紫微", "天府"], "minor_stars": ["左辅", "右弼"]},
                    "兄弟宫": {"position": "未", "major_stars": ["太阳"], "minor_stars": []},
                    "夫妻宫": {"position": "申", "major_stars": ["武曲", "七杀"], "minor_stars": []}
                }
            }
        }
        
        print("📊 开始生成HTML图表...")
        
        # 异步调用图表生成
        async def test_generation():
            chart_path = await agent._generate_chart_image(
                calculation_result, birth_info, "combined"
            )
            return chart_path
        
        # 运行异步函数
        chart_path = asyncio.run(test_generation())
        
        if chart_path:
            print(f"✅ HTML图表生成成功: {chart_path}")
            
            # 检查文件是否存在
            if os.path.exists(chart_path):
                file_size = os.path.getsize(chart_path)
                print(f"📄 文件大小: {file_size} 字节")
                
                # 检查是否是HTML文件
                if chart_path.endswith('.html'):
                    print(f"🌐 确认为HTML文件")
                    
                    # 读取部分内容验证
                    with open(chart_path, 'r', encoding='utf-8') as f:
                        content = f.read(500)  # 读取前500字符
                    
                    if '<html' in content.lower() and '<body' in content.lower():
                        print(f"✅ HTML内容验证通过")
                        return True
                    else:
                        print(f"❌ HTML内容验证失败")
                        return False
                else:
                    print(f"⚠️ 生成的不是HTML文件: {chart_path}")
                    return False
            else:
                print(f"❌ 文件不存在: {chart_path}")
                return False
        else:
            print(f"❌ HTML图表生成失败")
            return False
            
    except Exception as e:
        print(f"❌ HTML图表生成测试失败: {e}")
        return False

def test_backend_agent_modifications():
    """测试后台Agent修改"""
    try:
        print("\n🔧 测试后台Agent修改")
        print("=" * 60)
        
        # 检查后台Agent文件
        agent_file = "core/agents/fortune_calculator_agent.py"
        if not os.path.exists(agent_file):
            print(f"❌ 后台Agent文件不存在: {agent_file}")
            return False
        
        print(f"✅ 后台Agent文件存在: {agent_file}")
        
        # 读取文件内容
        with open(agent_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("_generate_html_chart", "HTML图表生成方法"),
            ("_convert_hour_to_number", "时辰转换方法"),
            ("fusion_chart_", "HTML文件命名"),
            ("ZiweiBaziFusionEngine", "融合分析引擎导入"),
            ("create_html_chart", "HTML图表创建函数调用"),
            ("calculation_type == \"combined\"", "融合分析类型判断")
        ]
        
        success_count = 0
        for keyword, description in checks:
            if keyword in content:
                print(f"✅ {description}: 已添加")
                success_count += 1
            else:
                print(f"❌ {description}: 未找到")
        
        print(f"\n📊 后台Agent修改检查: {success_count}/{len(checks)} 通过")
        
        return success_count >= 5
        
    except Exception as e:
        print(f"❌ 后台Agent修改测试失败: {e}")
        return False

def test_web_interface_modifications():
    """测试Web界面修改"""
    try:
        print("\n🌐 测试Web界面修改")
        print("=" * 60)
        
        # 检查后台Agent Web界面文件
        web_file = "backend_agent_web.py"
        if not os.path.exists(web_file):
            print(f"❌ Web界面文件不存在: {web_file}")
            return False
        
        print(f"✅ Web界面文件存在: {web_file}")
        
        # 读取文件内容
        with open(web_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("show_chart_image_simple", "简化图表显示函数"),
            ("show_chart_image_detail", "详情图表显示函数"),
            ("chart_path.endswith('.html')", "HTML文件检查"),
            ("st.components.v1.html", "HTML组件渲染"),
            ("fusion_chart_", "HTML文件查找"),
            ("🌐 HTML可视化图表", "HTML图表标题")
        ]
        
        success_count = 0
        for keyword, description in checks:
            if keyword in content:
                print(f"✅ {description}: 已修改")
                success_count += 1
            else:
                print(f"❌ {description}: 未找到")
        
        print(f"\n📊 Web界面修改检查: {success_count}/{len(checks)} 通过")
        
        return success_count >= 5
        
    except Exception as e:
        print(f"❌ Web界面修改测试失败: {e}")
        return False

def test_html_generator_availability():
    """测试HTML生成器可用性"""
    try:
        print("\n🎨 测试HTML生成器可用性")
        print("=" * 60)
        
        # 检查HTML生成器文件
        generator_file = "generate_html_ziwei.py"
        if not os.path.exists(generator_file):
            print(f"❌ HTML生成器文件不存在: {generator_file}")
            return False
        
        print(f"✅ HTML生成器文件存在: {generator_file}")
        
        # 尝试导入HTML生成器
        try:
            from generate_html_ziwei import create_html_chart
            print(f"✅ HTML生成器导入成功")
            
            # 测试生成器函数
            test_data = {
                "birth_info": {"year": 1988, "month": 6, "day": 1, "hour": 12, "gender": "男"},
                "ziwei_analysis": {"palaces": {"命宫": {"major_stars": ["紫微"], "minor_stars": []}}},
                "bazi_analysis": {"bazi_info": {"chinese_date": "戊辰 丁巳 丁亥 丙午"}}
            }
            
            html_content = create_html_chart(test_data)
            
            if html_content and len(html_content) > 1000:
                print(f"✅ HTML生成器功能正常，生成内容长度: {len(html_content)}")
                return True
            else:
                print(f"❌ HTML生成器功能异常，内容长度: {len(html_content) if html_content else 0}")
                return False
                
        except Exception as e:
            print(f"❌ HTML生成器导入失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ HTML生成器可用性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 HTML图表集成测试")
    print("=" * 80)
    print("测试内容:")
    print("1. HTML生成器可用性")
    print("2. 后台Agent修改")
    print("3. Web界面修改")
    print("4. HTML图表生成")
    print("=" * 80)
    
    # 执行测试
    results = []
    
    # 测试HTML生成器可用性
    results.append(("HTML生成器", test_html_generator_availability()))
    
    # 测试后台Agent修改
    results.append(("后台Agent修改", test_backend_agent_modifications()))
    
    # 测试Web界面修改
    results.append(("Web界面修改", test_web_interface_modifications()))
    
    # 测试HTML图表生成
    results.append(("HTML图表生成", test_html_chart_generation()))
    
    # 总结结果
    print(f"\n🎯 HTML图表集成测试结果")
    print("=" * 80)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📊 总体结果: {success_count}/{len(results)} 通过")
    
    if success_count >= 3:
        print(f"\n🎉 HTML图表集成基本成功！")
        print(f"\n🌟 集成特点:")
        print(f"  ✅ 后台Agent支持HTML图表生成")
        print(f"  ✅ Web界面支持HTML图表显示")
        print(f"  ✅ 融合分析优先使用HTML图表")
        print(f"  ✅ 兼容传统图片格式")
        
        print(f"\n🚀 使用方式:")
        print(f"  streamlit run backend_agent_web.py")
        print(f"  创建紫薇+八字融合分析")
        print(f"  查看HTML可视化图表")
        
        return True
    else:
        print(f"\n💥 HTML图表集成需要进一步完善")
        print(f"  请检查失败的测试项目")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
