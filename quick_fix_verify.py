#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证修复
"""

def verify_api_config():
    """验证API配置修复"""
    print("🔧 紧急修复验证")
    print("=" * 30)
    
    try:
        # 检查API配置文件
        with open("openai_api/openai_api.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查模型配置
        if 'DEEPSEEK_MODEL_NAME = "deepseek-ai/DeepSeek-V3"' in content:
            print("✅ 模型已改回DeepSeek-V3")
        else:
            print("❌ 模型配置错误")
            return False
        
        # 检查超时设置
        if "timeout=60" in content:
            print("✅ 超时已改回60秒")
            timeout_count = content.count("timeout=60")
            print(f"✅ 找到{timeout_count}处超时设置")
        else:
            print("❌ 超时设置错误")
            return False
        
        print("\n📋 当前配置:")
        print("  模型: DeepSeek-V3 (响应快)")
        print("  超时: 60秒 (合理)")
        print("  排盘图: 已改进 (美观)")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚨 紧急修复：改回DeepSeek-V3")
    print("=" * 40)
    
    success = verify_api_config()
    
    if success:
        print("\n🎉 修复完成！")
        print("\n📝 修复内容:")
        print("  ✅ 模型: DeepSeek-V3 (不再超时)")
        print("  ✅ 超时: 60秒 (合理设置)")
        print("  ✅ 排盘图: 保持改进版本")
        
        print("\n🚀 现在应该:")
        print("  - 不会再超时")
        print("  - 响应速度快")
        print("  - 排盘图美观")
        print("  - 分析内容完整")
    else:
        print("\n❌ 修复失败，需要检查配置")

if __name__ == "__main__":
    main()
