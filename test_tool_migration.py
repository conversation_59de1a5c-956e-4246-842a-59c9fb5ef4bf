#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试功能迁移 - 验证紫薇斗数、八字算命、六爻算卦工具的迁移效果
"""

import sys
import os
sys.path.append('.')

def test_tool_creation():
    """测试工具创建"""
    print("🔧 测试工具创建")
    print("=" * 80)
    
    try:
        # 1. 测试提示词管理器
        print("1. 测试提示词管理器...")
        from config.prompts.prompt_manager import PromptManager
        
        prompt_manager = PromptManager()
        print(f"✅ 提示词管理器创建成功")
        
        # 2. 测试LLM客户端
        print("\n2. 测试LLM客户端...")
        from utils.llm_client import LLMClient
        
        llm_client = LLMClient(
            api_key="test_key",
            base_url="http://localhost:8001/v1",
            model_name="deepseek-ai/DeepSeek-V3"
        )
        print(f"✅ LLM客户端创建成功")
        
        # 3. 测试紫薇斗数工具
        print("\n3. 测试紫薇斗数工具...")
        from core.tools.ziwei_tool import ZiweiTool
        
        # 模拟紫薇斗数算法
        class MockZiweiCalc:
            def calculate_chart(self, year, month, day, hour, gender):
                return {
                    "success": True,
                    "data": {
                        "命宫": "天机星",
                        "财帛宫": "太阴星",
                        "事业宫": "天同星"
                    }
                }
        
        ziwei_tool = ZiweiTool(MockZiweiCalc(), llm_client, prompt_manager)
        print(f"✅ 紫薇斗数工具创建成功: {ziwei_tool.name}")
        
        # 4. 测试八字算命工具
        print("\n4. 测试八字算命工具...")
        from core.tools.bazi_tool import BaziTool
        
        # 模拟八字算法
        class MockBaziCalc:
            def calculate_bazi(self, year, month, day, hour, gender):
                return {
                    "success": True,
                    "data": {
                        "年柱": "戊辰",
                        "月柱": "甲午",
                        "日柱": "辛丑",
                        "时柱": "甲午"
                    }
                }
        
        bazi_tool = BaziTool(MockBaziCalc(), llm_client, prompt_manager)
        print(f"✅ 八字算命工具创建成功: {bazi_tool.name}")
        
        # 5. 测试六爻算卦工具
        print("\n5. 测试六爻算卦工具...")
        from core.tools.liuyao_tool import LiuyaoTool
        
        # 模拟六爻算法
        class MockLiuyaoCalc:
            def divine_by_time(self, question):
                return {
                    "success": True,
                    "data": {
                        "卦名": "火天大有",
                        "动爻": [5],
                        "卦象": "上离下乾"
                    }
                }
        
        liuyao_tool = LiuyaoTool(MockLiuyaoCalc(), llm_client, prompt_manager)
        print(f"✅ 六爻算卦工具创建成功: {liuyao_tool.name}")
        
        return {
            "prompt_manager": prompt_manager,
            "llm_client": llm_client,
            "ziwei_tool": ziwei_tool,
            "bazi_tool": bazi_tool,
            "liuyao_tool": liuyao_tool
        }
        
    except Exception as e:
        print(f"❌ 工具创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_tool_functionality(tools):
    """测试工具功能"""
    print("\n🎯 测试工具功能")
    print("=" * 80)
    
    try:
        # 模拟LLM响应
        class MockLLMClient:
            def chat(self, prompt, system_prompt=None, temperature=0.7):
                if "紫薇" in prompt:
                    return "基于紫薇斗数分析，您的命宫主星为天机星，具有聪明机智的特质..."
                elif "八字" in prompt:
                    return "根据您的八字分析，日主为辛金，生于午月，身弱需要印星扶助..."
                elif "六爻" in prompt:
                    return "根据卦象显示，火天大有卦象征着光明正大、事业兴旺..."
                else:
                    return "分析完成"
        
        # 替换LLM客户端
        mock_llm = MockLLMClient()
        tools["ziwei_tool"].llm_client = mock_llm
        tools["bazi_tool"].llm_client = mock_llm
        tools["liuyao_tool"].llm_client = mock_llm
        
        # 1. 测试紫薇斗数工具功能
        print("1. 测试紫薇斗数工具功能...")
        
        ziwei_intent = {
            "tool_name": "ziwei",
            "question_type": "general",
            "entities": {
                "birth_info": {
                    "year": 1988,
                    "month": 6,
                    "day": 1,
                    "hour": 12,
                    "gender": "男"
                }
            }
        }
        
        ziwei_result = tools["ziwei_tool"].execute(ziwei_intent, {})
        print(f"紫薇斗数执行结果: {ziwei_result.get('success')}")
        if ziwei_result.get("success"):
            print(f"分析长度: {len(ziwei_result.get('analysis', ''))}")
        
        # 2. 测试八字算命工具功能
        print("\n2. 测试八字算命工具功能...")
        
        bazi_intent = {
            "tool_name": "bazi",
            "question_type": "career",
            "entities": {
                "birth_info": {
                    "year": 1988,
                    "month": 6,
                    "day": 1,
                    "hour": 12,
                    "gender": "男"
                }
            }
        }
        
        bazi_result = tools["bazi_tool"].execute(bazi_intent, {})
        print(f"八字算命执行结果: {bazi_result.get('success')}")
        if bazi_result.get("success"):
            print(f"分析长度: {len(bazi_result.get('analysis', ''))}")
        
        # 3. 测试六爻算卦工具功能
        print("\n3. 测试六爻算卦工具功能...")
        
        liuyao_intent = {
            "tool_name": "liuyao",
            "question_type": "fortune",
            "entities": {
                "question": "今年运势如何"
            },
            "raw_response": "今年运势如何"
        }
        
        liuyao_result = tools["liuyao_tool"].execute(liuyao_intent, {})
        print(f"六爻算卦执行结果: {liuyao_result.get('success')}")
        if liuyao_result.get("success"):
            print(f"分析长度: {len(liuyao_result.get('analysis', ''))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_system():
    """测试集成系统"""
    print("\n🔗 测试集成系统")
    print("=" * 80)
    
    try:
        # 创建完整的系统
        from core.chat.session_manager import SessionManager
        from core.tools.tool_registry import ToolRegistry
        from core.nlu.intent_recognizer import IntentRecognizer
        from core.chat.conversation_engine import ConversationEngine
        from config.prompts.prompt_manager import PromptManager
        from utils.llm_client import LLMClient
        
        # 创建组件
        session_manager = SessionManager()
        tool_registry = ToolRegistry()
        prompt_manager = PromptManager()
        
        # 模拟LLM客户端
        class MockLLMClient:
            def chat(self, prompt, system_prompt=None, temperature=0.7):
                if "意图" in prompt or "tool_name" in prompt:
                    if "紫薇" in prompt:
                        return '{"tool_name": "ziwei", "question_type": "general", "confidence": 0.9}'
                    elif "八字" in prompt:
                        return '{"tool_name": "bazi", "question_type": "general", "confidence": 0.9}'
                    elif "算卦" in prompt:
                        return '{"tool_name": "liuyao", "question_type": "general", "confidence": 0.9}'
                    else:
                        return '{"tool_name": "general", "question_type": "general", "confidence": 0.8}'
                elif "出生信息" in prompt:
                    return '{"year": 1988, "month": 6, "day": 1, "hour": 12, "gender": "男"}'
                else:
                    return "这是一个测试分析结果"
            
            def parse_json_response(self, response):
                import json
                try:
                    return json.loads(response)
                except:
                    return None
        
        llm_client = MockLLMClient()
        intent_recognizer = IntentRecognizer(llm_client, prompt_manager)
        
        # 创建并注册工具
        from core.tools.ziwei_tool import ZiweiTool
        from core.tools.bazi_tool import BaziTool
        from core.tools.liuyao_tool import LiuyaoTool
        
        # 模拟算法
        class MockCalc:
            def calculate_chart(self, *args, **kwargs):
                return {"success": True, "data": {"test": "data"}}
            def calculate_bazi(self, *args, **kwargs):
                return {"success": True, "data": {"test": "data"}}
            def divine_by_time(self, *args, **kwargs):
                return {"success": True, "data": {"test": "data"}}
        
        mock_calc = MockCalc()
        
        ziwei_tool = ZiweiTool(mock_calc, llm_client, prompt_manager)
        bazi_tool = BaziTool(mock_calc, llm_client, prompt_manager)
        liuyao_tool = LiuyaoTool(mock_calc, llm_client, prompt_manager)
        
        tool_registry.register_tool(ziwei_tool, "fortune")
        tool_registry.register_tool(bazi_tool, "fortune")
        tool_registry.register_tool(liuyao_tool, "divination")
        
        # 创建对话引擎
        conversation_engine = ConversationEngine(session_manager, intent_recognizer, tool_registry)
        
        # 测试完整对话流程
        print("测试完整对话流程...")
        
        # 测试1: 紫薇斗数
        result1 = conversation_engine.process_message("user1", "我想看紫薇斗数，1988年6月1日午时男")
        print(f"紫薇斗数对话: {result1.get('success')}")
        
        # 测试2: 八字算命
        result2 = conversation_engine.process_message("user2", "帮我分析八字，1988年6月1日午时男")
        print(f"八字算命对话: {result2.get('success')}")
        
        # 测试3: 六爻算卦
        result3 = conversation_engine.process_message("user3", "帮我算一卦，今年运势如何")
        print(f"六爻算卦对话: {result3.get('success')}")
        
        # 检查工具统计
        stats = tool_registry.get_global_stats()
        print(f"工具执行统计: {stats['total_executions']} 次")
        
        print("✅ 集成系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 集成系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 功能迁移测试")
    print("=" * 100)
    
    # 测试1: 工具创建
    tools = test_tool_creation()
    if not tools:
        print("\n❌ 工具创建失败，停止测试")
        return False
    
    # 测试2: 工具功能
    functionality_success = test_tool_functionality(tools)
    
    # 测试3: 集成系统
    integration_success = test_integrated_system()
    
    # 总结
    print("\n" + "=" * 100)
    print("🎉 功能迁移测试结果:")
    print(f"  工具创建: ✅ 成功")
    print(f"  工具功能: {'✅ 成功' if functionality_success else '❌ 失败'}")
    print(f"  集成系统: {'✅ 成功' if integration_success else '❌ 失败'}")
    
    if all([tools, functionality_success, integration_success]):
        print("\n🎊 所有功能迁移测试通过！")
        print("\n💡 **迁移成果验证:**")
        print("  ✅ 紫薇斗数工具 - 成功包装现有功能")
        print("  ✅ 八字算命工具 - 成功包装现有功能")
        print("  ✅ 六爻算卦工具 - 成功包装现有功能")
        print("  ✅ 提示词管理器 - 智能提示词切换")
        print("  ✅ 完整对话流程 - 从意图识别到工具执行")
        print()
        print("🚀 **架构优势体现:**")
        print("  - 🔧 功能隔离: 每个算命类型独立工具")
        print("  - 🤖 智能识别: 自动选择合适的工具")
        print("  - 💬 对话支持: 支持多轮对话和上下文")
        print("  - 🔄 易于扩展: 新功能只需实现工具接口")
        print()
        print("🎯 **下一步: 创建统一接口层，替换现有API**")
        return True
    else:
        print("\n⚠️ 部分功能迁移测试失败")
        return False

if __name__ == "__main__":
    main()
