#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立排盘简单接口
提供最简单的输入输出接口
"""

import sys
import os
from typing import Dict, Any, Tuple

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from core_calculator import StandalonePaipanCalculator
from result_saver import PaipanResultSaver

class SimplePaipanInterface:
    """简单排盘接口"""
    
    def __init__(self, output_dir: str = "paipan_outputs"):
        """
        初始化接口
        
        Args:
            output_dir: 输出目录
        """
        self.calculator = StandalonePaipanCalculator()
        self.saver = PaipanResultSaver(output_dir)
        print("🔮 独立排盘系统已初始化")
    
    def calculate_and_save(self, year: int, month: int, day: int, 
                          hour: int, gender: str = "男") -> Dict[str, Any]:
        """
        计算排盘并保存结果
        
        Args:
            year: 出生年份
            month: 出生月份
            day: 出生日期
            hour: 出生小时
            gender: 性别
            
        Returns:
            包含计算结果和保存信息的字典
        """
        print(f"\n🚀 开始排盘计算...")
        print(f"输入信息: {year}年{month}月{day}日{hour}时 {gender}")
        
        # 1. 计算排盘
        paipan_result = self.calculator.calculate_complete_paipan(
            year, month, day, hour, gender
        )
        
        # 2. 格式化输出
        formatted_text = self.calculator.format_output(paipan_result)
        
        # 3. 保存结果
        saved_files = self.saver.save_complete_result(paipan_result, formatted_text)
        
        # 4. 返回完整信息
        return {
            "calculation_result": paipan_result,
            "formatted_output": formatted_text,
            "saved_files": saved_files,
            "success": paipan_result.get("success", False),
            "status": paipan_result.get("status", "未知状态")
        }
    
    def quick_calculate(self, birth_string: str, gender: str = "男") -> Dict[str, Any]:
        """
        快速计算（通过字符串输入）
        
        Args:
            birth_string: 出生时间字符串，格式如 "1990-3-15-8" 或 "1990年3月15日8时"
            gender: 性别
            
        Returns:
            计算结果
        """
        try:
            # 解析出生时间字符串
            year, month, day, hour = self._parse_birth_string(birth_string)
            return self.calculate_and_save(year, month, day, hour, gender)
        except Exception as e:
            return {
                "success": False,
                "error": f"时间解析失败: {str(e)}",
                "formatted_output": f"错误: 无法解析时间字符串 '{birth_string}'"
            }
    
    def _parse_birth_string(self, birth_string: str) -> Tuple[int, int, int, int]:
        """
        解析出生时间字符串
        
        Args:
            birth_string: 时间字符串
            
        Returns:
            (年, 月, 日, 时)
        """
        # 清理字符串
        cleaned = birth_string.replace("年", "-").replace("月", "-").replace("日", "-").replace("时", "")
        
        # 分割并转换
        parts = [p.strip() for p in cleaned.split("-") if p.strip()]
        
        if len(parts) < 3:
            raise ValueError("时间格式不正确，至少需要年月日")
        
        year = int(parts[0])
        month = int(parts[1])
        day = int(parts[2])
        hour = int(parts[3]) if len(parts) > 3 else 12  # 默认午时
        
        # 基本验证
        if not (1900 <= year <= 2100):
            raise ValueError(f"年份 {year} 超出合理范围")
        if not (1 <= month <= 12):
            raise ValueError(f"月份 {month} 不正确")
        if not (1 <= day <= 31):
            raise ValueError(f"日期 {day} 不正确")
        if not (0 <= hour <= 23):
            raise ValueError(f"小时 {hour} 不正确")
        
        return year, month, day, hour
    
    def print_result(self, result: Dict[str, Any]):
        """
        打印计算结果
        
        Args:
            result: 计算结果
        """
        if result.get("success"):
            print("\n" + "="*60)
            print("✅ 排盘计算成功！")
            print("="*60)
            print(result["formatted_output"])
            
            saved_files = result.get("saved_files", {})
            if saved_files:
                print("\n📁 文件保存信息:")
                for file_type, filepath in saved_files.items():
                    print(f"  {file_type.upper()}: {filepath}")
        else:
            print("\n" + "="*60)
            print("❌ 排盘计算失败")
            print("="*60)
            print(result.get("formatted_output", result.get("error", "未知错误")))
    
    def list_saved_results(self):
        """列出已保存的结果"""
        files = self.saver.list_saved_results()
        if not files:
            print("📂 暂无保存的排盘结果")
            return
        
        print(f"\n📂 已保存的排盘结果 (共{len(files)}个文件):")
        print("-" * 80)
        for i, file_info in enumerate(files, 1):
            filename = file_info["filename"]
            size_kb = file_info["size"] / 1024
            modified_time = file_info["modified_time"][:19].replace("T", " ")
            print(f"{i:2d}. {filename}")
            print(f"    大小: {size_kb:.1f}KB | 修改时间: {modified_time}")
            if i < len(files):
                print()

def main():
    """主函数 - 命令行交互"""
    print("🔮 独立排盘系统")
    print("=" * 50)
    
    interface = SimplePaipanInterface()
    
    while True:
        print("\n请选择操作:")
        print("1. 输入出生信息进行排盘")
        print("2. 快速排盘（字符串输入）")
        print("3. 查看已保存的结果")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            try:
                year = int(input("出生年份: "))
                month = int(input("出生月份: "))
                day = int(input("出生日期: "))
                hour = int(input("出生小时 (0-23): "))
                gender = input("性别 (男/女, 默认男): ").strip() or "男"
                
                result = interface.calculate_and_save(year, month, day, hour, gender)
                interface.print_result(result)
                
            except ValueError as e:
                print(f"❌ 输入错误: {e}")
            except KeyboardInterrupt:
                print("\n操作已取消")
        
        elif choice == "2":
            try:
                birth_string = input("出生时间 (如: 1990-3-15-8 或 1990年3月15日8时): ").strip()
                gender = input("性别 (男/女, 默认男): ").strip() or "男"
                
                result = interface.quick_calculate(birth_string, gender)
                interface.print_result(result)
                
            except KeyboardInterrupt:
                print("\n操作已取消")
        
        elif choice == "3":
            interface.list_saved_results()
        
        elif choice == "4":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
