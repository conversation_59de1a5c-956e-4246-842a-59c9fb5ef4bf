#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试4角度分析重试机制
"""

def test_retry_mechanism():
    """测试重试机制"""
    print("🔄 测试4角度分析重试机制")
    print("=" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()
        
        # 模拟有时失败的API函数
        call_count = 0
        def mock_chat_api_with_failures(prompt: str) -> str:
            nonlocal call_count
            call_count += 1
            
            # 模拟第4角度第1、2次调用失败
            if "第4角度" in prompt and call_count <= 2:
                if call_count == 1:
                    return "短"  # 太短，质量不合格
                elif call_count == 2:
                    raise Exception("模拟网络错误")
            
            # 第3次或其他角度正常返回
            return f"""
这是一个详细的命理分析，基于紫薇斗数和八字命理双重体系。

命理基础分析显示，根据真实的排盘数据，可以看出以下特点：

1. 紫薇斗数方面：命宫主星配置体现了独特的性格特质，具有很强的领导能力和创新思维。身宫的配置进一步强化了这种特质，显示出在人生发展过程中会逐渐展现出更多的潜能。

2. 八字命理方面：日主的强弱配置和五行的平衡状况反映了天赋才能的分布。从四柱的组合来看，具备了在特定领域取得成功的基础条件。

3. 综合分析：两套体系的结论相互印证，显示出这是一个具有发展潜力的命格。但同时也要注意某些不利因素的影响，需要在人生的关键阶段做出正确的选择。

具体建议包括：在事业发展方面要注重时机的把握，在感情方面要保持理性的态度，在健康方面要注意预防某些潜在的问题。

这个分析基于真实的算法计算结果，具有很高的准确性和参考价值。通过紫薇斗数和八字命理的双重验证，可以为人生规划提供有价值的指导。

总的来说，这是一个充满机遇和挑战的命格，需要在把握机遇的同时注意规避风险，才能实现人生的最大价值。
"""
        
        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=mock_chat_api_with_failures
        )
        
        print("✅ 算命引擎创建成功")
        
        # 测试数据
        birth_info = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试数据: {birth_info}")
        
        # 获取算法结果
        algorithm_result = engine._call_comprehensive_api(birth_info)
        
        if not algorithm_result.get("success"):
            print(f"❌ 算法调用失败: {algorithm_result.get('error')}")
            return False
        
        print("✅ 算法调用成功，开始测试重试机制...")
        
        # 测试单个角度重试
        print("\n🎯 测试第4角度重试机制...")
        result = engine._generate_single_angle_with_retry(
            4, algorithm_result, "测试问题", "general", max_retries=3
        )
        
        if result:
            print(f"✅ 第4角度重试成功，最终长度: {len(result)}")
            print(f"📊 总API调用次数: {call_count}")
            return True
        else:
            print("❌ 第4角度重试失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quality_validation():
    """测试质量验证机制"""
    print("\n🔍 测试质量验证机制")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        engine = FortuneEngine()
        
        # 测试1: 空内容
        print("测试1: 空内容")
        result = engine._validate_analysis_quality("", 1)
        print(f"结果: {'✅ 正确拒绝' if not result else '❌ 错误通过'}")
        
        # 测试2: 内容太短
        print("测试2: 内容太短")
        short_content = "这是一个很短的分析"
        result = engine._validate_analysis_quality(short_content, 2)
        print(f"结果: {'✅ 正确拒绝' if not result else '❌ 错误通过'}")
        
        # 测试3: 缺少关键词
        print("测试3: 缺少关键词")
        no_keywords = "这是一个很长的分析内容，但是没有包含任何相关的关键词。" * 20
        result = engine._validate_analysis_quality(no_keywords, 3)
        print(f"结果: {'✅ 正确拒绝' if not result else '❌ 错误通过'}")
        
        # 测试4: 合格内容
        print("测试4: 合格内容")
        good_content = """
这是一个详细的紫薇斗数和八字命理分析。根据真实的排盘数据，可以进行以下专业分析：

1. 命理基础：通过紫薇斗数的命宫配置和八字的日主分析，可以看出这个人具有独特的性格特质。

2. 天赋才能：从星曜的配置和五行的平衡来看，在某些方面具有天赋优势。

3. 发展方向：结合两套体系的分析，建议在特定领域发展会有更好的前景。

4. 注意事项：同时也要注意一些不利因素的影响，需要在关键时刻做出正确选择。

这个分析基于真实的算法计算，具有很高的参考价值。通过紫薇斗数和八字命理的综合分析，可以为人生规划提供指导。
""" * 3  # 重复3次确保长度足够
        
        result = engine._validate_analysis_quality(good_content, 4)
        print(f"结果: {'✅ 正确通过' if result else '❌ 错误拒绝'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 质量验证测试失败: {e}")
        return False

def test_comprehensive_retry():
    """测试完整的重试流程"""
    print("\n🌟 测试完整的重试流程")
    print("=" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()
        
        # 模拟各种失败情况的API
        call_history = []
        def comprehensive_mock_api(prompt: str) -> str:
            call_history.append(prompt)
            
            # 分析是第几角度
            if "第1角度" in prompt:
                return "这是第1角度的详细分析，包含紫薇斗数和八字命理的综合内容。" * 20
            elif "第2角度" in prompt:
                if len([p for p in call_history if "第2角度" in p]) == 1:
                    return "短"  # 第一次太短
                else:
                    return "这是第2角度的详细分析，包含紫薇斗数和八字命理的综合内容。" * 20
            elif "第3角度" in prompt:
                return "这是第3角度的详细分析，包含紫薇斗数和八字命理的综合内容。" * 20
            elif "第4角度" in prompt:
                if len([p for p in call_history if "第4角度" in p]) <= 2:
                    raise Exception("模拟API错误")
                else:
                    return "这是第4角度的详细分析，包含紫薇斗数和八字命理的综合内容。" * 20
            else:
                # 合并分析
                return "这是合并后的完整分析报告。" * 50
        
        # 创建引擎
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=comprehensive_mock_api
        )
        
        # 测试数据
        birth_info = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        # 获取算法结果
        algorithm_result = engine._call_comprehensive_api(birth_info)
        
        if not algorithm_result.get("success"):
            print(f"❌ 算法调用失败")
            return False
        
        # 测试完整的4角度分析
        print("🔮 开始完整的4角度分析测试...")
        detailed_analysis = engine._generate_detailed_analysis_with_retry(
            algorithm_result, "测试完整流程", "general"
        )
        
        if "❌ 分析生成失败" in detailed_analysis:
            print("❌ 完整流程测试失败")
            return False
        else:
            print("✅ 完整流程测试成功")
            print(f"📊 总API调用次数: {len(call_history)}")
            print(f"📝 最终分析长度: {len(detailed_analysis)}")
            
            # 分析调用历史
            angle_calls = {}
            for call in call_history:
                for i in range(1, 5):
                    if f"第{i}角度" in call:
                        angle_calls[i] = angle_calls.get(i, 0) + 1
            
            print("📈 各角度调用次数:")
            for angle, count in angle_calls.items():
                print(f"  第{angle}角度: {count}次")
            
            return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_retry_mechanism_summary():
    """显示重试机制总结"""
    print("\n📋 重试机制总结")
    print("=" * 30)
    
    print("🔄 **重试机制特点:**")
    print("  1. 每个角度最多重试3次")
    print("  2. 质量验证：长度≥500字，包含关键词")
    print("  3. 任何角度失败3次后停止整个分析")
    print("  4. 确保分析质量和完整性")
    print()
    
    print("✅ **质量保证:**")
    print("  - 不接受过短的分析（<500字）")
    print("  - 必须包含命理相关关键词")
    print("  - 网络错误自动重试")
    print("  - API异常自动重试")
    print()
    
    print("🛡️ **客观性保证:**")
    print("  - 任何角度失败都会停止分析")
    print("  - 不会基于不完整数据给出结论")
    print("  - 确保分析的准确性和专业性")
    print("  - 避免胡编乱造的内容")
    print()
    
    print("🎯 **用户体验:**")
    print("  - 明确的错误提示")
    print("  - 详细的失败原因")
    print("  - 重试过程透明化")
    print("  - 质量优先于速度")

def main():
    """主测试函数"""
    print("🔄 4角度分析重试机制测试")
    print("=" * 60)
    
    # 测试1: 基础重试机制
    retry_success = test_retry_mechanism()
    
    # 测试2: 质量验证
    quality_success = test_quality_validation()
    
    # 测试3: 完整流程
    comprehensive_success = test_comprehensive_retry()
    
    # 显示总结
    show_retry_mechanism_summary()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 重试机制测试结果:")
    print(f"  基础重试测试: {'✅ 通过' if retry_success else '❌ 失败'}")
    print(f"  质量验证测试: {'✅ 通过' if quality_success else '❌ 失败'}")
    print(f"  完整流程测试: {'✅ 通过' if comprehensive_success else '❌ 失败'}")
    
    all_success = all([retry_success, quality_success, comprehensive_success])
    
    if all_success:
        print("\n🎊 所有测试通过！重试机制完美实现！")
        print("\n📝 重试机制已完善:")
        print("  1. ✅ 每个角度最多重试3次")
        print("  2. ✅ 严格的质量验证（≥500字）")
        print("  3. ✅ 任何角度失败都停止分析")
        print("  4. ✅ 网络错误自动重试")
        print("  5. ✅ 确保分析客观性和准确性")
        
        print("\n🎯 解决了您提到的问题:")
        print("  - ❌ 第4角度分析只有7字 → ✅ 最少500字且自动重试")
        print("  - ❌ 继续分析不完整数据 → ✅ 失败立即停止")
        print("  - ❌ 可能胡编乱造 → ✅ 严格质量验证")
        print("  - ❌ 没有重试机制 → ✅ 最多3次重试")
        
        print("\n💡 **现在的系统特点:**")
        print("  - 客观性优先：宁可不分析，也不给错误结论")
        print("  - 质量保证：每个角度都要达到标准")
        print("  - 透明化：详细的调试信息")
        print("  - 用户友好：清晰的错误提示")
    else:
        print("\n⚠️ 部分测试失败，需要进一步完善")

if __name__ == "__main__":
    main()
