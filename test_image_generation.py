#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片生成功能
"""

def test_image_dependencies():
    """测试图片生成依赖"""
    print("🔍 测试图片生成依赖...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL (Pillow) 可用")
    except ImportError as e:
        print(f"❌ PIL (Pillow) 不可用: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        from matplotlib.font_manager import FontProperties
        print("✅ matplotlib 可用")
    except ImportError as e:
        print(f"❌ matplotlib 不可用: {e}")
        return False
    
    return True

def test_simple_chart():
    """测试简单图表生成"""
    print("\n🎨 测试简单图表生成...")
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        import os
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 绘制外框
        outer_rect = patches.Rectangle((1, 1), 8, 8, linewidth=3, edgecolor='purple', facecolor='white')
        ax.add_patch(outer_rect)
        
        # 添加标题
        ax.text(5, 9.5, "测试图表", ha='center', va='center', fontsize=16, fontweight='bold', color='purple')
        
        # 添加一些测试内容
        ax.text(5, 5, "图片生成测试成功", ha='center', va='center', fontsize=14, color='black')
        
        # 保存图片
        output_dir = "charts"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        test_image_path = f"{output_dir}/test_image.png"
        plt.savefig(test_image_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 测试图片已生成: {test_image_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试图片生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_chart():
    """测试综合分析图表生成"""
    print("\n🔮 测试综合分析图表生成...")
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 创建引擎实例
        engine = FortuneEngine()
        
        # 模拟紫薇数据
        ziwei_data = {
            "birth_info": {
                "solar": "1996年8月15日19时",
                "lunar": "一九九六年七月初二",
                "chinese_date": "丙子 丙申 甲申 甲戌"
            },
            "palaces": {
                "命宫": {
                    "position": "戌",
                    "major_stars": ["太阳"],
                    "minor_stars": ["左辅"],
                    "adjective_stars": ["凤阁", "天才"],
                    "is_body_palace": False
                }
            },
            "zodiac": "鼠",
            "sign": "狮子座"
        }
        
        # 模拟八字数据
        bazi_data = {
            "birth_info": {
                "datetime": "1996年8月15日19时"
            },
            "raw_result": {
                "干支": {
                    "文本": "丙子 丙申 甲申 甲戌"
                },
                "五行": {
                    "木": {"旺衰": "弱", "五行数": "1"},
                    "火": {"旺衰": "旺", "五行数": "2"},
                    "土": {"旺衰": "中", "五行数": "1"},
                    "金": {"旺衰": "强", "五行数": "3"},
                    "水": {"旺衰": "弱", "五行数": "1"}
                },
                "大运": {
                    "乙未": {"十神": "偏印", "年份": "2001"},
                    "甲午": {"十神": "正印", "年份": "2011"}
                }
            }
        }
        
        # 测试图片生成
        image_path = engine._generate_chart_image(ziwei_data, bazi_data)
        
        if image_path:
            print(f"✅ 综合分析图片生成成功: {image_path}")
            return True
        else:
            print("❌ 综合分析图片生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 综合分析图表测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎨 图片生成功能测试")
    print("=" * 50)
    
    # 测试1: 依赖检查
    deps_ok = test_image_dependencies()
    
    if not deps_ok:
        print("\n❌ 图片依赖不完整，请安装:")
        print("  pip install pillow matplotlib")
        return
    
    # 测试2: 简单图表
    simple_ok = test_simple_chart()
    
    # 测试3: 综合分析图表
    comprehensive_ok = test_comprehensive_chart()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎉 图片生成测试总结:")
    print(f"  依赖检查: {'✅' if deps_ok else '❌'}")
    print(f"  简单图表: {'✅' if simple_ok else '❌'}")
    print(f"  综合分析: {'✅' if comprehensive_ok else '❌'}")
    
    if deps_ok and simple_ok and comprehensive_ok:
        print("\n🎊 所有测试通过！图片生成功能正常")
    else:
        print("\n⚠️ 部分测试失败，需要检查图片生成功能")

if __name__ == "__main__":
    main()
