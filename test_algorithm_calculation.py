#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法计算功能
检查紫薇斗数算法是否正常工作
"""

import sys
import json
sys.path.append('.')

def test_algorithm_calculation():
    """测试算法计算功能"""
    print('🧮 算法计算功能测试')
    print('=' * 50)

    try:
        # 测试数据
        test_birth_info = {
            'year': '1988',
            'month': '6',
            'day': '1',
            'hour': '午时',
            'gender': '男'
        }

        print(f'📝 测试数据: {test_birth_info}')

        # 第一步：测试工具选择器
        print(f'\n🔧 第一步：测试工具选择器')

        from core.tools.tool_selector import ToolSelector
        tool_selector = ToolSelector()

        # 构建意图对象
        intent = {
            "intent": "ziwei",
            "confidence": 0.9,
            "original_message": "测试紫薇斗数计算",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }

        print(f'📤 发送意图: {intent}')

        # 调用工具选择器
        result = tool_selector.select_tool(intent, {})

        print(f'📥 工具选择器结果:')
        print(f'  success: {result.get("success")}')
        print(f'  message: {result.get("message", "无消息")}')

        if result.get("success"):
            tool_result = result.get("result", {})
            print(f'  工具结果类型: {type(tool_result)}')
            print(f'  工具结果success: {tool_result.get("success")}')

            if tool_result.get("success"):
                # 🔧 修复：正确提取数据路径
                calculation_result = tool_result.get("calculation_result", {})
                raw_result = calculation_result.get("raw_result", {})
                print(f'  原始结果类型: {type(raw_result)}')
                print(f'  原始结果键: {list(raw_result.keys()) if isinstance(raw_result, dict) else "非字典"}')

                # 检查关键数据
                if isinstance(raw_result, dict):
                    palaces = raw_result.get("palaces", {})
                    birth_info = raw_result.get("birth_info", {})

                    print(f'\n📊 算法数据检查:')
                    print(f'  宫位数据: {"✅" if palaces else "❌"} ({len(palaces)}个宫位)')
                    print(f'  生辰信息: {"✅" if birth_info else "❌"}')

                    if palaces:
                        print(f'  宫位列表: {list(palaces.keys())}')

                        # 检查命宫数据
                        ming_gong = palaces.get("命宫", {})
                        if ming_gong:
                            print(f'  命宫数据: {ming_gong}')
                        else:
                            print(f'  ❌ 命宫数据缺失')

                    # 保存完整结果用于分析
                    with open('debug_algorithm_result.json', 'w', encoding='utf-8') as f:
                        json.dump(raw_result, f, ensure_ascii=False, indent=2)
                    print(f'  📁 完整结果已保存到: debug_algorithm_result.json')

                else:
                    print(f'  ❌ 原始结果不是字典格式')
            else:
                error_msg = tool_result.get("error", "未知错误")
                print(f'  ❌ 工具执行失败: {error_msg}')
        else:
            error_msg = result.get("error", "未知错误")
            print(f'  ❌ 工具选择失败: {error_msg}')

        # 第二步：直接测试紫薇算法
        print(f'\n🔮 第二步：直接测试紫薇算法')

        try:
            from algorithms.real_ziwei_calculator import RealZiweiCalculator

            calculator = RealZiweiCalculator()
            print(f'✅ 紫薇算法导入成功')

            # 直接调用算法
            direct_result = calculator.calculate_chart(1988, 6, 1, 11, "男")  # 午时=11点

            print(f'📊 直接算法结果:')
            print(f'  结果类型: {type(direct_result)}')

            if isinstance(direct_result, dict):
                if "error" in direct_result:
                    print(f'  ❌ 算法错误: {direct_result["error"]}')
                else:
                    print(f'  ✅ 算法成功')
                    print(f'  结果键: {list(direct_result.keys())}')

                    # 检查宫位数据
                    palaces = direct_result.get("palaces", {})
                    if palaces:
                        print(f'  宫位数量: {len(palaces)}')
                        print(f'  宫位名称: {list(palaces.keys())}')

                        # 检查第一个宫位的详细数据
                        first_palace_name = list(palaces.keys())[0]
                        first_palace = palaces[first_palace_name]
                        print(f'  {first_palace_name}数据: {first_palace}')
                    else:
                        print(f'  ❌ 宫位数据为空')

                    # 保存直接结果
                    with open('debug_direct_algorithm_result.json', 'w', encoding='utf-8') as f:
                        json.dump(direct_result, f, ensure_ascii=False, indent=2)
                    print(f'  📁 直接结果已保存到: debug_direct_algorithm_result.json')

        except Exception as e:
            print(f'❌ 直接测试紫薇算法失败: {e}')
            import traceback
            traceback.print_exc()

        # 第三步：测试图片生成
        print(f'\n🎨 第三步：测试图片生成')

        try:
            from core.fortune_engine import FortuneEngine

            engine = FortuneEngine()
            print(f'✅ 图片引擎导入成功')

            # 使用算法结果生成图片
            if 'direct_result' in locals() and isinstance(direct_result, dict) and "error" not in direct_result:
                algorithm_result = {
                    "success": True,
                    "data": direct_result,
                    "type": "ziwei"
                }

                chart_display = engine._generate_chart_display(algorithm_result)
                print(f'📊 图片生成结果:')
                print(f'  显示内容长度: {len(chart_display)}字')

                # 检查是否包含图片路径
                import re
                image_matches = re.findall(r'图片已生成: (.+)', chart_display)
                if image_matches:
                    image_path = image_matches[0].strip()
                    print(f'  ✅ 图片路径: {image_path}')

                    # 检查图片文件
                    import os
                    if os.path.exists(image_path):
                        file_size = os.path.getsize(image_path)
                        print(f'  ✅ 图片文件存在: {file_size} bytes')
                    else:
                        print(f'  ❌ 图片文件不存在')
                else:
                    print(f'  ❌ 未找到图片路径')
                    print(f'  显示内容预览: {chart_display[:200]}...')
            else:
                print(f'  ❌ 无有效算法结果用于图片生成')

        except Exception as e:
            print(f'❌ 测试图片生成失败: {e}')
            import traceback
            traceback.print_exc()

        # 总结
        print(f'\n📋 算法测试总结:')

        checks = [
            ("工具选择器", result.get("success", False)),
            ("算法计算", 'direct_result' in locals() and isinstance(direct_result, dict) and "error" not in direct_result),
            ("宫位数据", 'direct_result' in locals() and direct_result.get("palaces", {})),
            ("图片生成", 'image_matches' in locals() and bool(image_matches))
        ]

        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f'  {status} {check_name}')

        # 计算成功率
        success_count = sum(1 for _, result in checks if result)
        total_count = len(checks)
        success_rate = success_count / total_count * 100

        print(f'\n🎯 算法功能完成度: {success_count}/{total_count} ({success_rate:.1f}%)')

        if success_rate >= 75:
            print('🎉 算法功能基本正常！')
        elif success_rate >= 50:
            print('⚠️ 算法功能部分正常，需要检查')
        else:
            print('❌ 算法功能存在严重问题，需要修复')

        print(f'\n🎉 算法测试完成！')

    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_algorithm_calculation()
