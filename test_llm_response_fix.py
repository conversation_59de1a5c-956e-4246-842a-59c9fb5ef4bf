#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM响应解析修复
"""

def test_response_parsing():
    """测试响应解析功能"""
    print("🔧 测试LLM响应解析修复")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        engine = FortuneEngine()
        
        # 测试各种响应格式
        test_cases = [
            # 纯文本格式
            ("liuyao", ["ziwei", "bazi", "liuyao", "comprehensive"], "liuyao", "纯文本格式"),
            ("LIUYAO", ["ziwei", "bazi", "liuyao", "comprehensive"], "liuyao", "大写纯文本"),
            
            # JSON格式（用户实际遇到的）
            ('```json\n{\n  "算命类型": "liuyao"\n}\n```', ["ziwei", "bazi", "liuyao", "comprehensive"], "liuyao", "JSON代码块格式"),
            ('{"problem_type": "fortune"}', ["career", "love", "wealth", "health", "fortune", "general"], "fortune", "简单JSON格式"),
            
            # 包含关键词的响应
            ("根据分析，这是liuyao类型", ["ziwei", "bazi", "liuyao", "comprehensive"], "liuyao", "包含关键词"),
            ("用户想要进行fortune相关咨询", ["career", "love", "wealth", "health", "fortune", "general"], "fortune", "包含关键词"),
            
            # 无效响应
            ("invalid_response", ["ziwei", "bazi", "liuyao", "comprehensive"], None, "无效响应"),
            ("", ["ziwei", "bazi", "liuyao", "comprehensive"], None, "空响应"),
        ]
        
        print("📝 测试结果:")
        success_count = 0
        for i, (response, valid_options, expected, description) in enumerate(test_cases, 1):
            result = engine._parse_llm_response(response, valid_options)
            
            if result == expected:
                status = "✅ 正确"
                success_count += 1
            else:
                status = f"❌ 错误(期望:{expected}, 实际:{result})"
            
            print(f"  {i}. {description}")
            print(f"     响应: {response[:50]}{'...' if len(response) > 50 else ''}")
            print(f"     结果: {status}")
            print()
        
        print(f"解析准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        
        return success_count >= len(test_cases) * 0.8  # 80%以上
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_user_case():
    """测试真实用户案例"""
    print("\n🎯 测试真实用户案例")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 模拟真实的LLM API，返回JSON格式
        def realistic_llm_api(prompt: str) -> str:
            if "算命类型" in prompt:
                if "算一卦" in prompt:
                    return '```json\n{\n  "算命类型": "liuyao"\n}\n```'
                else:
                    return "comprehensive"
            elif "问题类型" in prompt:
                if "运势" in prompt:
                    return '{"problem_type": "fortune"}'
                else:
                    return "general"
            else:
                return "null"
        
        engine = FortuneEngine(chat_api_func=realistic_llm_api)
        
        # 用户的原问题
        user_question = "帮我算一卦，看看今年运势，现在已经6月份了"
        
        print(f"📝 用户问题: {user_question}")
        print()
        
        # 测试算命类型识别
        print("🔍 测试算命类型识别:")
        fortune_type = engine._detect_fortune_type(user_question)
        print(f"识别结果: {fortune_type}")
        
        if fortune_type == "liuyao":
            print("✅ 正确识别为六爻算卦")
        else:
            print(f"❌ 错误识别为: {fortune_type}")
            return False
        
        print()
        
        # 测试问题类型识别
        print("🔍 测试问题类型识别:")
        question_type = engine._detect_question_type(user_question)
        print(f"识别结果: {question_type}")
        
        if question_type == "fortune":
            print("✅ 正确识别为运势问题")
        else:
            print(f"❌ 错误识别为: {question_type}")
            return False
        
        print()
        
        # 测试完整解析流程
        print("🔮 测试完整解析流程:")
        parsed_info = engine.parse_user_input(user_question)
        
        print(f"解析结果:")
        print(f"  算命类型: {parsed_info['fortune_type']}")
        print(f"  问题类型: {parsed_info['question_type']}")
        print(f"  出生信息: {parsed_info['birth_info']}")
        
        # 验证结果
        success = (
            parsed_info['fortune_type'] == 'liuyao' and
            parsed_info['question_type'] == 'fortune' and
            parsed_info['birth_info'] is None
        )
        
        if success:
            print("✅ 完整解析成功")
            return True
        else:
            print("❌ 解析失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n📋 LLM响应解析修复总结")
    print("=" * 40)
    
    print("🔧 **修复内容:**")
    print("  1. 添加智能响应解析方法 _parse_llm_response")
    print("  2. 支持JSON格式响应解析")
    print("  3. 支持markdown代码块格式")
    print("  4. 支持纯文本格式")
    print("  5. 支持关键词匹配")
    print()
    
    print("📝 **支持的响应格式:**")
    print("  - 纯文本: 'liuyao'")
    print("  - JSON: '{\"算命类型\": \"liuyao\"}'")
    print("  - 代码块: '```json\\n{\"type\": \"liuyao\"}\\n```'")
    print("  - 包含关键词: '根据分析，这是liuyao类型'")
    print()
    
    print("🛡️ **容错机制:**")
    print("  - LLM返回JSON格式时自动解析")
    print("  - LLM返回无效格式时降级到关键词匹配")
    print("  - 多种键名支持（算命类型、fortune_type等）")
    print("  - 大小写不敏感")
    print()
    
    print("🎯 **解决的问题:**")
    print("  - ❌ LLM返回JSON格式导致识别失败")
    print("  - ✅ 现在能正确解析JSON响应")
    print("  - ❌ 代码块格式无法处理")
    print("  - ✅ 现在支持markdown代码块")
    print("  - ❌ 只支持纯文本格式")
    print("  - ✅ 现在支持多种格式")

def main():
    """主测试函数"""
    print("🔧 LLM响应解析修复测试")
    print("=" * 60)
    
    # 测试1: 响应解析功能
    parsing_success = test_response_parsing()
    
    # 测试2: 真实用户案例
    user_case_success = test_real_user_case()
    
    # 显示修复总结
    show_fix_summary()
    
    # 最终结果
    print("\n" + "=" * 60)
    print("🎉 LLM响应解析修复测试结果:")
    print(f"  响应解析功能: {'✅ 通过' if parsing_success else '❌ 失败'}")
    print(f"  真实用户案例: {'✅ 通过' if user_case_success else '❌ 失败'}")
    
    all_success = parsing_success and user_case_success
    
    if all_success:
        print("\n🎊 LLM响应解析修复完全成功！")
        print("\n🎯 **解决的核心问题:**")
        print("  ❌ LLM返回JSON格式: {\"算命类型\": \"liuyao\"}")
        print("  ✅ 现在能正确解析为: liuyao")
        print()
        print("  ❌ 系统降级到关键词匹配")
        print("  ✅ 现在智能解析JSON内容")
        
        print("\n🚀 **现在的智能体验:**")
        print("  1. 用户: '帮我算一卦，看看今年运势'")
        print("  2. LLM: 返回JSON格式响应")
        print("  3. 系统: 智能解析JSON内容")
        print("  4. 系统: 正确识别为liuyao + fortune")
        print("  5. 系统: 六爻算卦即问即答")
        
        print("\n💡 **技术改进:**")
        print("  - 智能响应解析，支持多种格式")
        print("  - JSON格式自动解析")
        print("  - 容错机制确保稳定性")
        print("  - 保留备用关键词匹配")
        
        print("\n🎉 **用户问题完全解决！**")
        print("**现在LLM返回任何格式都能正确解析！**")
        print("**'帮我算一卦看运势' 真正实现即问即答！**")
    else:
        print("\n⚠️ 部分测试失败，需要进一步完善")

if __name__ == "__main__":
    main()
