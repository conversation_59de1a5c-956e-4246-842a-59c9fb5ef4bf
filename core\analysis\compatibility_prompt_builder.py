#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合盘分析专用提示词构建器
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class CompatibilityPromptBuilder:
    """合盘分析专用提示词构建器"""

    def __init__(self):
        """初始化合盘提示词构建器"""
        logger.info("✅ 合盘提示词构建器初始化完成")

    def build_compatibility_prompt(self, compatibility_data: Dict[str, Any], analysis_dimension: str) -> str:
        """
        构建合盘分析提示词（参考紫薇+八字相互印证方案）

        Args:
            compatibility_data: 合盘数据
            analysis_dimension: 分析维度

        Returns:
            构建的详细提示词
        """
        try:
            logger.info(f"🔨 构建合盘{analysis_dimension}分析提示词")

            # 获取基础信息
            person_a_info = compatibility_data["person_a"]["info"]
            person_b_info = compatibility_data["person_b"]["info"]
            relationship_type = compatibility_data.get("relationship_type", "未知关系")

            # 获取分析数据（修复数据访问路径）
            person_a_analysis = compatibility_data["person_a"]["analysis"]
            person_b_analysis = compatibility_data["person_b"]["analysis"]

            person_a_ziwei = person_a_analysis.get("ziwei_analysis", {})
            person_a_bazi = person_a_analysis.get("bazi_analysis", {})
            person_b_ziwei = person_b_analysis.get("ziwei_analysis", {})
            person_b_bazi = person_b_analysis.get("bazi_analysis", {})

            # 构建详细提示词
            prompt = f"""
作为专业的紫薇斗数和八字命理合盘分析师，请基于以下精确的计算数据进行【{self._get_dimension_name(analysis_dimension)}】专业合盘分析。

【重要】本次分析必须严格基于计算数据，确保准确性和专业性。

【严格约束】
1. 严格按照提供的计算数据进行分析
2. 禁止编造任何不在数据中的星曜配置
3. 禁止使用不在数据中的宫位信息
4. 必须准确引用计算结果中的具体数据
5. 如果数据不足，明确说明而不是编造
6. 严格限定在{self._get_dimension_name(analysis_dimension)}分析，禁止涉及其他维度

【基本信息】
👤 {person_a_info.get('name', 'A')}：{person_a_info.get('year')}年{person_a_info.get('month')}月{person_a_info.get('day')}日 {person_a_info.get('hour')} {person_a_info.get('gender')}
👤 {person_b_info.get('name', 'B')}：{person_b_info.get('year')}年{person_b_info.get('month')}月{person_b_info.get('day')}日 {person_b_info.get('hour')} {person_b_info.get('gender')}
🔗 关系类型：{relationship_type}
📅 分析时间：当前

【{person_a_info.get('name', 'A')}的计算数据】

🌟 紫薇斗数宫位配置：
{self._format_detailed_ziwei_data(person_a_ziwei)}

🔮 八字四柱信息：
{self._format_detailed_bazi_data(person_a_bazi)}

【{person_b_info.get('name', 'B')}的计算数据】

🌟 紫薇斗数宫位配置：
{self._format_detailed_ziwei_data(person_b_ziwei)}

🔮 八字四柱信息：
{self._format_detailed_bazi_data(person_b_bazi)}

【合盘分析要求】
请进行专业详细的紫薇+八字融合合盘分析，严格按照以下要求：

🚨 【维度隔离要求】
- 本次分析仅针对 {self._get_dimension_name(analysis_dimension)} 维度，严禁涉及其他维度的内容
- 不得提及财运、婚姻、事业、健康、子女、人际、家庭等其他维度（除非与当前维度直接相关）
- 专注于当前维度的深度分析，做到极致专业

📊 【详细度要求】
- 字数要求：5000字以上，内容要极度详细具体
- 必须包含具体的年份预测、数值评估、等级评分
- 要有针对性的实用建议和具体操作方案
- 分析要细致入微，不能泛泛而谈

🔮 【融合合盘分析步骤】
1. 双方紫薇斗数专项对比分析
   - 基于相关宫位的详细配置分析（包含不利星曜组合）
   - 结合星曜组合的深层含义（正面+负面特征）
   - 分析双方宫位的相互影响和冲克关系

2. 双方八字命理专项对比分析
   - 基于四柱八字的专项特征（优势+劣势并存）
   - 结合五行生克的具体表现（平衡+失衡分析）
   - 分析双方八字的相互作用和影响

3. 双重算法相互印证
   - 对比两种算法在本维度的结论（好坏都要印证）
   - 找出一致性验证和互补性分析（包含负面一致性）
   - 综合得出更准确的合盘结论（客观中性）

4. 深度问题发现与分析
   - 详细分析本维度存在的核心问题和缺陷
   - 指出可能面临的重大挑战和困难
   - 分析问题产生的根本原因和影响程度

5. 具体解决方案和改善策略
   - 针对发现的问题提供具体的解决方案
   - 给出详细的改善步骤和实施计划
   - 包含预防措施和长期改善策略

{self._get_detailed_analysis_requirements(analysis_dimension, relationship_type)}

【🚨 强制执行要求 🚨】
1. 必须生成3000字以上的完整分析内容
2. 禁止生成"因篇幅限制"、"精简框架"、"示例性框架"等简化说明
3. 必须包含完整的6个章节内容，每个章节至少500字
4. 禁止说"此处为精简版"、"完整版需要..."等推脱性语言

【📏 字数硬性要求】
- 最少3000字，目标4000-5000字
- 每个章节都要充分展开，不能省略
- 必须包含具体的年份预测（2024-2030年）
- 必须包含具体的数值评估和百分比
- 必须包含可操作的实用建议

【✅ 内容完整性检查】
- 使用清晰的标题结构（一、二、三、四、五、六）
- 每个部分都要有具体的数据支撑
- 包含具体的时间预测和数值评估
- 提供可操作的实用建议
- 必须包含风险预警和防范措施

现在请立即开始生成完整的5000字以上专业合盘分析报告：
"""

            logger.info(f"✅ 合盘{analysis_dimension}提示词构建完成，长度: {len(prompt)}字符")
            return prompt

        except Exception as e:
            logger.error(f"❌ 构建合盘提示词失败: {e}")
            return f"构建合盘提示词失败: {str(e)}"

    def _get_dimension_name(self, analysis_dimension: str) -> str:
        """获取分析维度的中文名称"""
        dimension_names = {
            "personality_compatibility": "性格互补性",
            "wealth_cooperation": "财运配合度",
            "emotional_harmony": "感情和谐度",
            "career_partnership": "事业合作潜力",
            "health_influence": "健康相互影响",
            "family_harmony": "家庭和睦度",
            "children_fortune": "子女缘分",
            "overall_compatibility": "综合匹配度"
        }
        return dimension_names.get(analysis_dimension, analysis_dimension)

    def _format_ziwei_data(self, ziwei_data: Dict[str, Any]) -> str:
        """格式化紫薇斗数数据（简化版）"""
        if not ziwei_data:
            return "紫薇斗数数据缺失"

        palaces = ziwei_data.get("palaces", {})
        if not palaces:
            return "宫位数据缺失"

        formatted_data = []
        for palace_name, palace_info in palaces.items():
            position = palace_info.get("position", "未知")
            major_stars = palace_info.get("major_stars", [])
            minor_stars = palace_info.get("minor_stars", [])

            stars_info = f"主星: {', '.join(major_stars) if major_stars else '无'}"
            if minor_stars:
                stars_info += f", 辅星: {', '.join(minor_stars[:3])}{'...' if len(minor_stars) > 3 else ''}"

            formatted_data.append(f"  {palace_name}({position}): {stars_info}")

        return "\n".join(formatted_data)

    def _format_detailed_ziwei_data(self, ziwei_data: Dict[str, Any]) -> str:
        """格式化详细的紫薇斗数数据（参考融合分析方案）"""
        if not ziwei_data:
            return "紫薇斗数数据缺失"

        palaces = ziwei_data.get("palaces", {})
        if not palaces:
            return "宫位数据缺失"

        formatted_data = []

        # 按照重要性排序宫位
        palace_order = [
            "命宫", "夫妻宫", "财帛宫", "事业宫", "迁移宫", "疾厄宫",
            "子女宫", "奴仆宫", "兄弟宫", "田宅宫", "福德宫", "父母宫"
        ]

        for palace_name in palace_order:
            if palace_name in palaces:
                palace_info = palaces[palace_name]
                position = palace_info.get("位置", palace_info.get("position", "未知"))
                major_stars = palace_info.get("主星", palace_info.get("major_stars", []))
                minor_stars = palace_info.get("辅星", palace_info.get("minor_stars", []))

                # 详细格式化星曜信息
                if major_stars:
                    stars_info = f"主星{major_stars}"
                    if minor_stars:
                        # 限制辅星显示数量，避免过长
                        displayed_minor = minor_stars[:5]
                        if len(minor_stars) > 5:
                            stars_info += f" 辅星{displayed_minor}等{len(minor_stars)}颗"
                        else:
                            stars_info += f" 辅星{minor_stars}"
                else:
                    stars_info = "空宫"
                    if minor_stars:
                        displayed_minor = minor_stars[:5]
                        if len(minor_stars) > 5:
                            stars_info += f" 辅星{displayed_minor}等{len(minor_stars)}颗"
                        else:
                            stars_info += f" 辅星{minor_stars}"

                formatted_data.append(f"  {palace_name}({position}): {stars_info}")

        # 添加其他未在标准顺序中的宫位
        for palace_name, palace_info in palaces.items():
            if palace_name not in palace_order:
                position = palace_info.get("位置", palace_info.get("position", "未知"))
                major_stars = palace_info.get("主星", palace_info.get("major_stars", []))
                minor_stars = palace_info.get("辅星", palace_info.get("minor_stars", []))

                stars_info = f"主星: {', '.join(major_stars) if major_stars else '无'}"
                if minor_stars:
                    stars_info += f", 辅星: {', '.join(minor_stars[:3])}{'...' if len(minor_stars) > 3 else ''}"

                formatted_data.append(f"  {palace_name}({position}): {stars_info}")

        return "\n".join(formatted_data)

    def _format_bazi_data(self, bazi_data: Dict[str, Any]) -> str:
        """格式化八字数据（简化版）"""
        if not bazi_data or not bazi_data.get("success"):
            return "八字数据缺失"

        bazi_info = bazi_data.get("bazi_info", {})
        analysis = bazi_data.get("analysis", {})

        formatted_data = []

        # 四柱信息
        if bazi_info:
            formatted_data.append(f"  四柱: {bazi_info.get('chinese_date', '未知')}")
            formatted_data.append(f"  年柱: {bazi_info.get('year_pillar', '未知')}")
            formatted_data.append(f"  月柱: {bazi_info.get('month_pillar', '未知')}")
            formatted_data.append(f"  日柱: {bazi_info.get('day_pillar', '未知')}")
            formatted_data.append(f"  时柱: {bazi_info.get('hour_pillar', '未知')}")

        # 五行分析
        if analysis and "wuxing" in analysis:
            wuxing = analysis["wuxing"]
            if "count" in wuxing:
                wuxing_str = ", ".join([f"{element}: {count}" for element, count in wuxing["count"].items()])
                formatted_data.append(f"  五行: {wuxing_str}")

        # 日主信息
        if analysis and "day_master" in analysis:
            day_master = analysis["day_master"]
            formatted_data.append(f"  日主: {day_master.get('gan', '未知')}({day_master.get('element', '未知')})")

        return "\n".join(formatted_data) if formatted_data else "八字分析数据缺失"

    def _format_detailed_bazi_data(self, bazi_data: Dict[str, Any]) -> str:
        """格式化详细的八字数据（参考融合分析方案）"""
        if not bazi_data or not bazi_data.get("success"):
            return "八字数据缺失"

        bazi_info = bazi_data.get("bazi_info", {})
        analysis = bazi_data.get("analysis", {})

        formatted_data = []

        # 四柱信息（详细版）
        if bazi_info:
            formatted_data.append(f"  四柱：{bazi_info.get('chinese_date', '未知')}")
            formatted_data.append(f"  年柱：{bazi_info.get('year_pillar', '未知')}")
            formatted_data.append(f"  月柱：{bazi_info.get('month_pillar', '未知')}")
            formatted_data.append(f"  日柱：{bazi_info.get('day_pillar', '未知')}")
            formatted_data.append(f"  时柱：{bazi_info.get('hour_pillar', '未知')}")

        # 五行分析（详细版）
        if analysis and "wuxing" in analysis:
            wuxing = analysis["wuxing"]
            formatted_data.append("\n⚖️ 五行分析：")

            if "count" in wuxing:
                for element, count in wuxing["count"].items():
                    formatted_data.append(f"  {element}：{count}个")

            # 五行强弱分析
            if "strength" in wuxing:
                formatted_data.append("  五行强弱：")
                for element, strength in wuxing["strength"].items():
                    formatted_data.append(f"    {element}：{strength}")

        # 十神分析
        if analysis and "shishen" in analysis:
            shishen = analysis["shishen"]
            formatted_data.append("\n🎭 十神分析：")

            if "count" in shishen:
                for god, count in shishen["count"].items():
                    formatted_data.append(f"  {god}：{count}个")

        # 日主分析（详细版）
        if analysis and "day_master" in analysis:
            day_master = analysis["day_master"]
            formatted_data.append(f"\n🌟 日主分析：")
            formatted_data.append(f"  日主：{day_master.get('gan', '未知')}({day_master.get('element', '未知')})")

            if "strength" in day_master:
                formatted_data.append(f"  强弱：{day_master['strength']}")

            if "characteristics" in day_master:
                characteristics = day_master["characteristics"]
                if isinstance(characteristics, list):
                    formatted_data.append(f"  特征：{', '.join(characteristics)}")
                elif isinstance(characteristics, str):
                    formatted_data.append(f"  特征：{characteristics}")

        # 用神分析
        if analysis and "yongshen" in analysis:
            yongshen = analysis["yongshen"]
            formatted_data.append(f"\n🎯 用神分析：")

            if "primary" in yongshen:
                formatted_data.append(f"  用神：{yongshen['primary']}")

            if "secondary" in yongshen:
                formatted_data.append(f"  喜神：{yongshen['secondary']}")

            if "avoid" in yongshen:
                formatted_data.append(f"  忌神：{yongshen['avoid']}")

        return "\n".join(formatted_data) if formatted_data else "八字分析数据缺失"

    def _get_analysis_requirements(self, analysis_dimension: str, relationship_type: str) -> str:
        """获取特定维度的分析要求（简化版，用于旧版兼容）"""
        return self._get_detailed_analysis_requirements(analysis_dimension, relationship_type)

    def _get_detailed_analysis_requirements(self, analysis_dimension: str, relationship_type: str) -> str:
        """获取详细的分析要求（参考紫薇+八字融合方案）"""
        requirements = {
            "personality_compatibility": f"""
【性格互补性专项分析要求】

🎯 【核心分析目标】
- 深度剖析两人性格的匹配度、互补性和冲突点
- 基于紫薇斗数和八字的双重验证，确保分析准确性
- 提供具体可操作的性格磨合方案和沟通策略

📊 【具体分析内容】
1. **性格特质对比分析**
   - 基于命宫、福德宫星曜配置分析核心性格特征
   - 结合八字日主、月令分析性格倾向和行为模式
   - 对比分析两人性格的相似性和差异性

2. **价值观冲突深度分析**
   - 基于财帛宫、夫妻宫配置分析价值观差异
   - 结合八字用神、忌神分析价值取向冲突
   - 🚨 重点分析：消费观念、人生目标、处事原则的根本性分歧

3. **行为模式冲突分析**
   - 基于迁移宫、事业宫分析行为习惯差异
   - 结合八字十神配置分析决策方式冲突
   - 🚨 重点分析：决策速度、风险偏好、社交需求的矛盾

4. **沟通障碍深度剖析**
   - 基于兄弟宫、奴仆宫分析沟通方式差异
   - 结合八字食伤、印星分析表达习惯冲突
   - 🚨 重点分析：语言风格、情感表达、冲突处理方式的不匹配

5. **性格互补优势发掘**
   - 分析两人性格的互补性和协同效应
   - 找出性格差异中的积极因素和发挥空间
   - 提供性格优势组合的具体应用方案

6. **矛盾化解策略**
   - 针对每个冲突点提供具体的化解方法
   - 包含日常相处技巧和危机处理方案
   - 提供性格磨合的时间表和实施步骤

🚨 【负面分析要求】
- 必须深度分析性格不合可能导致的严重后果
- 客观指出两人相处中的重大风险和挑战
- 预测性格冲突的高发期和危险信号
- 分析关系破裂的可能性和预防措施
- 负面分析占比不少于40%

📅 【时间预测要求】
- 具体到年份的性格冲突高发期预测
- 基于大限流年分析性格磨合的关键时期
- 提供月份级别的沟通最佳时机建议

💡 【实用建议要求】
- 每日相处的具体技巧和注意事项
- 冲突发生时的应急处理方案
- 长期性格磨合的系统性策略
- 包含具体的话术模板和行为指导
""",

            "emotional_harmony": f"""
【感情和谐度专项分析要求】

🎯 【核心分析目标】
- 深度评估两人感情关系的和谐程度和稳定性
- 基于紫薇斗数和八字的双重验证，预测感情发展趋势
- 提供具体的感情维护策略和危机化解方案

📊 【具体分析内容】
1. **感情基础稳定性分析**
   - 基于夫妻宫、福德宫配置分析感情根基
   - 结合八字配偶星、桃花星分析感情特质
   - 评估感情的持久性和发展潜力

2. **感情冲突根源分析**
   - 基于子女宫、疾厄宫分析感情表达方式差异
   - 结合八字伤官、正官分析感情需求冲突
   - 🚨 重点分析：感情期待、亲密度需求、安全感来源的差异

3. **第三者风险评估**
   - 基于桃花星、贪狼星配置分析外遇风险
   - 结合八字桃花、咸池分析感情诱惑抵抗力
   - 🚨 重点分析：感情忠诚度、诱惑抵抗力、关系稳定性

4. **感情危机预警**
   - 基于化忌星、煞星分析感情危机触发点
   - 结合八字冲克分析感情波动周期
   - 🚨 重点分析：分手离婚风险、感情淡化可能性

5. **感情和谐促进方案**
   - 分析感情升温的有利因素和时机
   - 提供感情深化的具体方法和策略
   - 包含浪漫互动和情感沟通的实用技巧

6. **感情维护长期策略**
   - 针对感情薄弱环节提供强化方案
   - 包含感情保鲜和危机预防措施
   - 提供感情修复的系统性方法

🚨 【负面分析要求】
- 必须深度分析感情关系中的重大风险
- 客观指出可能导致分手离婚的因素
- 预测感情危机的高发期和预警信号
- 分析感情破裂的可能性和严重后果
- 负面分析占比不少于40%

📅 【时间预测要求】
- 具体到年份的感情危机期和和谐期预测
- 基于流年分析感情发展的关键转折点
- 提供月份级别的感情维护最佳时机

💡 【实用建议要求】
- 日常感情维护的具体行动指南
- 感情危机时的应急处理方案
- 长期感情经营的系统性策略
- 包含具体的浪漫计划和沟通技巧
""",

            "wealth_cooperation": f"""
【财运配合度专项分析要求】

🎯 【核心分析目标】
- 深度评估两人在财富方面的配合度和相互影响
- 基于紫薇斗数和八字的双重验证，预测财务合作前景
- 提供具体的财务协调方案和风险防范措施

📊 【具体分析内容】
1. **财务观念对比分析**
   - 基于财帛宫、田宅宫配置分析财富观念
   - 结合八字财星、比劫分析理财习惯
   - 对比分析消费观念和投资理念的差异

2. **财务冲突风险评估**
   - 基于煞星、化忌分析财务纠纷风险
   - 结合八字劫财、伤官分析财务冲突点
   - 🚨 重点分析：消费分歧、投资争议、财产分配矛盾

3. **合作投资可行性分析**
   - 基于禄存、化禄分析合作财运
   - 结合八字财星配置分析投资配合度
   - 🚨 重点分析：投资风险、合作失败可能性

4. **财务管理分工建议**
   - 基于星曜特质分析适合的财务角色
   - 提供财务决策权分配的最佳方案
   - 包含家庭理财的具体分工策略

5. **财运相互促进方案**
   - 分析两人财运的互补性和协同效应
   - 提供财富增长的合作策略
   - 包含投资理财的具体建议

6. **财务风险防范措施**
   - 针对财务冲突提供预防方案
   - 包含财产保护和风险控制策略
   - 提供财务危机的应急处理方法

🚨 【负面分析要求】
- 必须深度分析财务合作中的重大风险
- 客观指出可能导致经济纠纷的因素
- 预测财务冲突的高发期和危险信号
- 分析财务失败的可能性和严重后果
- 负面分析占比不少于40%

📅 【时间预测要求】
- 具体到年份的财运合作吉凶期预测
- 基于流年分析财务决策的最佳时机
- 提供月份级别的投资理财建议

💡 【实用建议要求】
- 日常财务管理的具体操作指南
- 财务冲突时的协调处理方案
- 长期财富规划的系统性策略
- 包含具体的投资建议和风险控制措施
"""
        }

        return requirements.get(analysis_dimension, f"""
【{self._get_dimension_name(analysis_dimension)}专项分析要求】

🎯 【核心分析目标】
- 深度分析两人在{self._get_dimension_name(analysis_dimension)}方面的配合度
- 基于紫薇斗数和八字的双重验证，确保分析准确性
- 提供具体可操作的改善方案和风险防范措施

📊 【具体分析内容】
1. **基础特征对比分析**
2. **冲突风险深度评估**
3. **协调配合可行性分析**
4. **问题解决方案制定**
5. **长期发展策略规划**

🚨 【负面分析要求】
- 必须深度分析{self._get_dimension_name(analysis_dimension)}方面的重大风险
- 客观指出可能导致问题的关键因素
- 预测冲突的高发期和危险信号
- 负面分析占比不少于40%

📅 【时间预测要求】
- 具体到年份的关键时期预测
- 基于流年分析最佳配合时机
- 提供月份级别的具体建议

💡 【实用建议要求】
- 日常配合的具体操作指南
- 冲突时的应急处理方案
- 长期改善的系统性策略
- 包含具体的实施步骤和注意事项

字数要求：5000字以上，内容详细具体，必须包含数据支撑和实用建议。
""")
