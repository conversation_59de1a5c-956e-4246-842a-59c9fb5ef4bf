#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人性化八字算命工具
集成到人性化交互系统中的八字算命功能
"""

import logging
from typing import Dict, Any, List, Optional
from core.tools.base_tool import BaseTool
from algorithms.real_bazi_calculator import RealBaziCalculator

logger = logging.getLogger(__name__)

class HumanizedBaziTool(BaseTool):
    """人性化八字算命工具"""

    def __init__(self):
        """初始化八字工具"""
        super().__init__(
            name="humanized_bazi",
            description="人性化八字算命分析工具",
            version="1.0.0"
        )

        # 初始化八字计算器 - 只使用真实算法
        try:
            self.bazi_calculator = RealBaziCalculator()
            logger.info("真实八字计算器初始化成功")
        except Exception as e:
            logger.error(f"真实八字计算器初始化失败: {e}")
            logger.error("八字算命功能不可用 - 需要安装真实算法模块")
            self.bazi_calculator = None

    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行八字算命分析

        Args:
            intent: 意图识别结果
            context: 会话上下文

        Returns:
            八字分析结果
        """
        try:
            logger.info("开始执行人性化八字算命分析")

            # 1. 检查八字计算器 - 必须使用真实算法
            if not self.bazi_calculator:
                return {
                    "success": False,
                    "error": "八字计算器未初始化",
                    "message": "八字算命功能暂时不可用，需要安装真实算法模块"
                }

            # 2. 提取出生信息
            birth_info = self._extract_birth_info(intent, context)
            if not birth_info:
                return {
                    "success": False,
                    "error": "缺少出生信息",
                    "message": "请提供您的出生信息（年月日时和性别），例如：1988年6月1日午时男"
                }

            # 3. 调用八字算法 - 只使用真实算法
            calculation_result = self._calculate_bazi(birth_info)

            if not calculation_result.get("success"):
                return {
                    "success": False,
                    "error": calculation_result.get("error", "算法调用失败"),
                    "message": "八字排盘失败，请检查出生信息是否正确"
                }

            # 4. 返回成功结果（供人性化引擎处理）
            return {
                "success": True,
                "type": "bazi_analysis",
                "calculation_result": calculation_result,
                "birth_info": birth_info,
                "message": "八字命理分析完成"
            }

        except Exception as e:
            logger.error(f"八字算命执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "八字算命过程中出现错误，请稍后重试"
            }

    def _extract_birth_info(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """提取出生信息"""
        try:
            entities = intent.get("entities", {})

            # 从意图中提取
            birth_info = {}
            required_fields = ["birth_year", "birth_month", "birth_day", "birth_hour", "gender"]

            for field in required_fields:
                value = entities.get(field)
                if value:
                    birth_info[field] = value

            # 从上下文中补充缺失信息
            context_birth = context.get("birth_info", {})
            for field in required_fields:
                if field not in birth_info and field in context_birth:
                    birth_info[field] = context_birth[field]

            # 检查必需字段
            missing_fields = [field for field in required_fields if field not in birth_info or not birth_info[field]]

            if missing_fields:
                logger.warning(f"缺少出生信息字段: {missing_fields}")
                return None

            logger.info(f"成功提取出生信息: {birth_info}")
            return birth_info

        except Exception as e:
            logger.error(f"提取出生信息失败: {e}")
            return None

    def _calculate_bazi(self, birth_info: Dict[str, str]) -> Dict[str, Any]:
        """调用八字算法进行计算"""
        try:
            logger.info(f"开始八字计算: {birth_info}")

            # 转换时辰
            hour_int = self._convert_hour_to_int(birth_info["birth_hour"])

            # 调用真实八字算法
            result = self.bazi_calculator.calculate_bazi(
                year=int(birth_info["birth_year"]),
                month=int(birth_info["birth_month"]),
                day=int(birth_info["birth_day"]),
                hour=hour_int,
                minute=0,
                gender=birth_info["gender"]
            )

            if result.get("success"):
                logger.info("八字计算成功")
                return result
            else:
                error_msg = result.get("error", "未知错误")
                logger.error(f"八字计算失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }

        except Exception as e:
            logger.error(f"八字计算异常: {e}")
            return {
                "success": False,
                "error": f"八字计算异常: {str(e)}"
            }

    def _convert_hour_to_int(self, hour_str: str) -> int:
        """将时辰字符串转换为小时数字"""
        hour_map = {
            "子时": 0, "丑时": 1, "寅时": 3, "卯时": 5,
            "辰时": 7, "巳时": 9, "午时": 11, "未时": 13,
            "申时": 15, "酉时": 17, "戌时": 19, "亥时": 21
        }

        # 如果是时辰名称
        if hour_str in hour_map:
            return hour_map[hour_str]

        # 如果是数字字符串
        try:
            hour_int = int(hour_str)
            if 0 <= hour_int <= 23:
                return hour_int
        except ValueError:
            pass

        # 默认返回12点（午时）
        logger.warning(f"无法识别的时辰: {hour_str}，使用默认值12")
        return 12

    def get_supported_intents(self) -> List[str]:
        """获取支持的意图类型"""
        return ["bazi", "bazi_analysis", "八字", "八字算命"]

    def can_handle(self, intent: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查是否能处理该意图"""
        return self.validate_input(intent, context)

    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """验证输入参数"""
        try:
            # 检查是否是八字相关意图
            intent_type = intent.get("intent", "").lower()
            if intent_type not in self.get_supported_intents():
                return False

            # 检查是否有出生信息
            entities = intent.get("entities", {})
            context_birth = context.get("birth_info", {})

            required_fields = ["birth_year", "birth_month", "birth_day", "birth_hour", "gender"]

            # 检查是否有足够的信息
            available_fields = set()
            for field in required_fields:
                if entities.get(field) or context_birth.get(field):
                    available_fields.add(field)

            return len(available_fields) >= len(required_fields)

        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.tool_name,
            "description": self.description,
            "supported_intents": self.get_supported_intents(),
            "required_entities": ["birth_year", "birth_month", "birth_day", "birth_hour", "gender"],
            "output_type": "bazi_analysis",
            "features": [
                "真实八字算法",
                "四柱排盘",
                "五行分析",
                "大运流年",
                "人性化交互"
            ]
        }

def test_humanized_bazi_tool():
    """测试人性化八字工具"""
    print("🔮 测试人性化八字工具")
    print("-" * 50)

    try:
        # 创建工具实例
        tool = HumanizedBaziTool()

        # 测试数据
        intent = {
            "intent": "bazi",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }

        context = {}

        # 执行测试
        result = tool.execute(intent, context)

        if result.get("success"):
            print("✅ 八字工具执行成功")
            print(f"   类型: {result.get('type')}")
            print(f"   消息: {result.get('message')}")

            # 检查计算结果
            calc_result = result.get("calculation_result", {})
            if calc_result.get("success"):
                print("✅ 八字计算成功")
                raw_result = calc_result.get("raw_result", {})
                if "干支" in raw_result:
                    ganzhi = raw_result["干支"]
                    print(f"   四柱: {ganzhi.get('文本', '未知')}")
            else:
                print(f"❌ 八字计算失败: {calc_result.get('error')}")
        else:
            print(f"❌ 八字工具执行失败: {result.get('error')}")

        # 测试工具信息
        tool_info = tool.get_tool_info()
        print(f"\n工具信息:")
        print(f"   名称: {tool_info['name']}")
        print(f"   描述: {tool_info['description']}")
        print(f"   支持意图: {tool_info['supported_intents']}")

        return result.get("success", False)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_humanized_bazi_tool()
