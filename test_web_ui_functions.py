#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面的新功能
验证按需生成和聊天功能是否正常工作
"""

import asyncio

async def test_web_ui_functions():
    """测试Web界面的新功能"""
    print("🌐 测试Web界面新功能")
    print("=" * 60)
    
    try:
        # 导入Web界面的函数
        import sys
        sys.path.append('.')
        
        # 测试单个分析生成函数
        print("1️⃣ 测试单个分析生成函数...")
        
        # 创建测试数据
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        calculator_agent = FortuneCalculatorAgent("web_test")
        
        # 生成测试排盘数据
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            # 保存到缓存
            result_id = calculator_agent.cache.save_result(
                user_id="web_test_user",
                session_id="web_test_session",
                calculation_type="ziwei",
                birth_info=birth_info,
                raw_calculation=raw_data,
                detailed_analysis={"angle_analyses": {}},
                summary="Web测试排盘",
                keywords=["紫薇", "八字", "测试"],
                confidence=0.9
            )
            
            print(f"✅ 测试排盘数据创建成功: {result_id}")
            
            # 测试单个分析生成
            from backend_agent_web import generate_single_analysis
            
            # 模拟Streamlit环境
            class MockStreamlit:
                def __init__(self):
                    self.messages = []
                
                def spinner(self, text):
                    print(f"🔄 {text}")
                    return self
                
                def __enter__(self):
                    return self
                
                def __exit__(self, *args):
                    pass
                
                def success(self, text):
                    print(f"✅ {text}")
                    self.messages.append(("success", text))
                
                def error(self, text):
                    print(f"❌ {text}")
                    self.messages.append(("error", text))
            
            # 替换streamlit模块
            import backend_agent_web
            original_st = getattr(backend_agent_web, 'st', None)
            backend_agent_web.st = MockStreamlit()
            
            try:
                # 测试生成命宫分析
                success = generate_single_analysis(
                    result_id, 
                    "personality_destiny", 
                    "🏛️ 命宫分析 - 性格命运核心特征"
                )
                
                if success:
                    print("✅ 单个分析生成功能正常")
                else:
                    print("❌ 单个分析生成功能异常")
                
            finally:
                # 恢复原始streamlit
                if original_st:
                    backend_agent_web.st = original_st
            
            # 测试聊天功能
            print("\n2️⃣ 测试聊天回复生成...")
            
            cached_result = calculator_agent.cache.get_result(result_id)
            if cached_result:
                from backend_agent_web import generate_chat_response
                
                test_question = "我的性格特点是什么？"
                response = generate_chat_response(cached_result, test_question)
                
                if response and len(response) > 50:
                    print(f"✅ 聊天回复生成正常: {len(response)}字")
                    print(f"📝 回复预览: {response[:100]}...")
                else:
                    print("❌ 聊天回复生成异常")
            
            # 测试排盘数据格式化
            print("\n3️⃣ 测试排盘数据格式化...")
            
            from backend_agent_web import format_chart_data_for_chat
            
            formatted_data = format_chart_data_for_chat(raw_data)
            if formatted_data and "命宫" in formatted_data:
                print("✅ 排盘数据格式化正常")
                print(f"📊 格式化数据预览:\n{formatted_data[:200]}...")
            else:
                print("❌ 排盘数据格式化异常")
            
            return True
        else:
            print("❌ 排盘数据生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_layout_structure():
    """测试UI布局结构"""
    print("\n🎨 测试UI布局结构")
    print("=" * 40)
    
    try:
        # 检查关键函数是否存在
        from backend_agent_web import (
            show_analysis_content_simple,
            generate_single_analysis,
            add_interactive_chat,
            generate_chat_response,
            format_chart_data_for_chat
        )
        
        print("✅ 所有关键函数都存在")
        
        # 检查12个分析角度的定义
        angle_names = {
            "personality_destiny": "🏛️ 命宫分析 - 性格命运核心特征",
            "wealth_fortune": "💰 财富分析 - 财运状况与理财投资",
            "marriage_love": "💕 婚姻分析 - 感情婚姻与桃花运势",
            "health_wellness": "🏥 健康分析 - 身体状况与养生建议",
            "career_achievement": "💼 事业分析 - 职业发展与成就潜力",
            "children_creativity": "👶 子女分析 - 生育状况与子女关系",
            "interpersonal_relationship": "🤝 人际分析 - 社交关系与贵人运",
            "education_learning": "📚 学业分析 - 教育学习与知识发展",
            "family_environment": "🏠 家庭分析 - 家庭环境与亲情关系",
            "travel_relocation": "✈️ 迁移分析 - 搬迁旅行与环境变化",
            "spiritual_blessing": "🙏 精神分析 - 精神状态与福德运势",
            "authority_parents": "👑 权威分析 - 领导能力与权威地位"
        }
        
        print(f"✅ 12个分析角度定义完整: {len(angle_names)}个")
        
        return True
        
    except ImportError as e:
        print(f"❌ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 布局结构测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🌐 Web界面新功能完整测试")
    print("=" * 70)
    
    # 1. 测试Web界面功能
    web_success = await test_web_ui_functions()
    
    # 2. 测试UI布局结构
    layout_success = test_ui_layout_structure()
    
    print("\n" + "=" * 70)
    print("🎯 Web界面新功能测试结果:")
    
    if web_success:
        print("✅ Web界面核心功能正常")
    else:
        print("❌ Web界面核心功能异常")
    
    if layout_success:
        print("✅ UI布局结构完整")
    else:
        print("❌ UI布局结构有问题")
    
    if web_success and layout_success:
        print("\n🎉 Web界面新功能测试成功！")
        print("💡 新功能特点:")
        print("  1. ✅ 排盘完成后显示12个分析按钮")
        print("  2. ✅ 每个分析可以按需点击生成")
        print("  3. ✅ 每个分析都可以重试提高质量")
        print("  4. ✅ 即时聊天功能基于排盘数据")
        print("  5. ✅ 用户体验更灵活高效")
        print("\n🚀 现在可以启动Web界面体验新功能了！")
    else:
        print("\n⚠️ 还有功能需要进一步完善")

if __name__ == "__main__":
    asyncio.run(main())
