#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化功能演示脚本
"""

import sys
import os
import json
import time
from datetime import datetime
sys.path.append('.')

def demo_session_persistence():
    """演示会话持久化功能"""
    print("🎯 持久化功能演示")
    print("=" * 80)
    
    print("\n📚 1. 聊天历史持久化演示")
    print("-" * 50)
    
    try:
        from core.storage.session_manager import SessionManager, UserSettings
        
        # 创建会话管理器
        session_manager = SessionManager()
        
        # 创建演示用户
        user_id = "demo_user_001"
        settings = UserSettings(
            analysis_depth="detailed",
            quality_level="premium",
            focus_positive=True,
            include_timing=True,
            practical_advice=True
        )
        
        # 创建新会话
        session_data = session_manager.create_session(user_id, settings)
        session_id = session_data.session_id
        
        print(f"✅ 创建演示会话: {session_id[:12]}...")
        print(f"   用户ID: {user_id}")
        print(f"   设置: {settings.quality_level}级别, {settings.analysis_depth}深度")
        
        # 模拟对话历史
        conversation = [
            ("user", "你好，我想算命"),
            ("assistant", "您好！欢迎来到智能算命AI。我可以为您提供紫薇斗数、八字算命等多种算命服务。请问您想了解哪方面？"),
            ("user", "我想看紫薇斗数，我是1990年5月15日上午10点出生的男性"),
            ("assistant", "好的，根据您提供的出生信息：\n- 出生日期：1990年5月15日\n- 出生时间：上午10点\n- 性别：男\n\n我来为您排紫薇斗数盘..."),
            ("user", "我的事业运势如何？"),
            ("assistant", "根据您的紫薇斗数盘分析，您的事业运势呈现以下特点：\n\n1. 事业宫有紫微星坐守，具有领导才能\n2. 适合从事管理、金融或技术类工作\n3. 30-40岁是事业发展的黄金期\n4. 建议把握机会，积极进取")
        ]
        
        # 添加对话到会话
        for role, content in conversation:
            session_manager.add_message(session_id, role, content, 
                                      "analysis" if role == "assistant" else "text")
            time.sleep(0.1)  # 模拟时间间隔
        
        print(f"✅ 添加了 {len(conversation)} 条对话记录")
        
        # 验证数据持久化
        loaded_session = session_manager.load_session(session_id)
        if loaded_session:
            print(f"✅ 会话数据已持久化，包含 {len(loaded_session.messages)} 条消息")
            print(f"   统计信息: {loaded_session.stats}")
        
        return session_id, user_id
        
    except Exception as e:
        print(f"❌ 会话持久化演示失败: {e}")
        return None, None

def demo_settings_persistence(session_id, user_id):
    """演示设置持久化功能"""
    print("\n⚙️ 2. 用户设置持久化演示")
    print("-" * 50)
    
    try:
        from core.storage.session_manager import SessionManager, UserSettings
        
        session_manager = SessionManager()
        
        # 加载当前设置
        session_data = session_manager.load_session(session_id)
        if not session_data:
            print("❌ 无法加载会话数据")
            return False
        
        print("当前设置:")
        current_settings = session_data.settings
        print(f"  分析深度: {current_settings.analysis_depth}")
        print(f"  质量等级: {current_settings.quality_level}")
        print(f"  积极导向: {current_settings.focus_positive}")
        print(f"  时间节点: {current_settings.include_timing}")
        print(f"  实用建议: {current_settings.practical_advice}")
        
        # 更新设置
        new_settings = UserSettings(
            analysis_depth="comprehensive",
            quality_level="basic",
            focus_positive=False,
            include_timing=False,
            practical_advice=True,
            theme="dark",
            language="zh"
        )
        
        session_manager.update_settings(session_id, new_settings)
        print("\n✅ 设置已更新为:")
        print(f"  分析深度: {new_settings.analysis_depth}")
        print(f"  质量等级: {new_settings.quality_level}")
        print(f"  积极导向: {new_settings.focus_positive}")
        print(f"  时间节点: {new_settings.include_timing}")
        print(f"  实用建议: {new_settings.practical_advice}")
        print(f"  主题: {new_settings.theme}")
        
        # 验证设置持久化
        reloaded_session = session_manager.load_session(session_id)
        if reloaded_session:
            updated_settings = reloaded_session.settings
            if updated_settings.analysis_depth == "comprehensive":
                print("✅ 设置持久化成功，刷新后设置保持不变")
            else:
                print("❌ 设置持久化失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 设置持久化演示失败: {e}")
        return False

def demo_session_history(user_id):
    """演示会话历史功能"""
    print("\n📋 3. 会话历史管理演示")
    print("-" * 50)
    
    try:
        from core.storage.session_manager import SessionManager, UserSettings
        
        session_manager = SessionManager()
        
        # 创建多个会话
        sessions = []
        for i in range(3):
            settings = UserSettings(
                analysis_depth=["brief", "standard", "detailed"][i],
                quality_level=["basic", "professional", "premium"][i]
            )
            
            session_data = session_manager.create_session(user_id, settings)
            sessions.append(session_data.session_id)
            
            # 添加一些消息
            session_manager.add_message(session_data.session_id, "user", f"这是第{i+1}个会话的测试消息")
            session_manager.add_message(session_data.session_id, "assistant", f"这是第{i+1}个会话的AI回复")
            
            time.sleep(0.1)
        
        print(f"✅ 创建了 {len(sessions)} 个演示会话")
        
        # 获取用户会话列表
        user_sessions = session_manager.get_user_sessions(user_id, limit=10)
        print(f"✅ 用户 {user_id} 的会话历史:")
        
        for i, session_info in enumerate(user_sessions):
            session_id = session_info["session_id"]
            message_count = session_info["message_count"]
            created_time = session_info["created_at"][:19].replace("T", " ")
            updated_time = session_info["updated_at"][:19].replace("T", " ")
            
            print(f"  {i+1}. 会话 {session_id[:12]}...")
            print(f"     消息数: {message_count}")
            print(f"     创建: {created_time}")
            print(f"     更新: {updated_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ 会话历史演示失败: {e}")
        return False

def demo_data_export(session_id):
    """演示数据导出功能"""
    print("\n📤 4. 数据导出功能演示")
    print("-" * 50)
    
    try:
        from core.storage.session_manager import SessionManager
        
        session_manager = SessionManager()
        
        # 导出JSON格式
        json_data = session_manager.export_session(session_id, "json")
        if json_data:
            print("✅ JSON格式导出成功")
            # 解析并显示部分内容
            data = json.loads(json_data)
            print(f"   会话ID: {data['session_id'][:12]}...")
            print(f"   消息数量: {len(data['messages'])}")
            print(f"   创建时间: {data['created_at'][:19]}")
            
            # 保存到文件
            export_filename = f"export_session_{session_id[:8]}.json"
            with open(export_filename, "w", encoding="utf-8") as f:
                f.write(json_data)
            print(f"   已保存到: {export_filename}")
        
        # 导出文本格式
        txt_data = session_manager.export_session(session_id, "txt")
        if txt_data:
            print("✅ 文本格式导出成功")
            lines = txt_data.split('\n')
            print(f"   文本行数: {len(lines)}")
            
            # 保存到文件
            export_filename = f"export_session_{session_id[:8]}.txt"
            with open(export_filename, "w", encoding="utf-8") as f:
                f.write(txt_data)
            print(f"   已保存到: {export_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导出演示失败: {e}")
        return False

def demo_storage_stats():
    """演示存储统计功能"""
    print("\n📊 5. 存储统计信息演示")
    print("-" * 50)
    
    try:
        from core.storage.session_manager import SessionManager
        
        session_manager = SessionManager()
        
        # 获取存储统计
        stats = session_manager.get_storage_stats()
        
        print("存储统计信息:")
        print(f"  📁 存储目录: {stats['storage_dir']}")
        print(f"  📊 总会话数: {stats['total_sessions']}")
        print(f"  💾 总大小: {stats['total_size_mb']} MB ({stats['total_size_bytes']} 字节)")
        print(f"  🚀 缓存大小: {stats['cache_size']} 个会话")
        
        # 计算平均会话大小
        if stats['total_sessions'] > 0:
            avg_size = stats['total_size_bytes'] / stats['total_sessions']
            print(f"  📈 平均会话大小: {avg_size:.0f} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 存储统计演示失败: {e}")
        return False

def demo_web_integration():
    """演示Web界面集成"""
    print("\n🌐 6. Web界面集成演示")
    print("-" * 50)
    
    print("Web界面持久化功能:")
    print("  🔄 自动会话创建 - 首次访问自动创建用户会话")
    print("  💾 实时消息保存 - 每条消息自动保存到存储")
    print("  ⚙️ 设置同步保存 - 用户偏好实时更新")
    print("  📚 历史会话面板 - 侧边栏显示历史会话列表")
    print("  🔄 会话切换功能 - 点击历史会话可快速切换")
    print("  📤 数据导出按钮 - 支持导出当前会话数据")
    print("  🗑️ 新会话创建 - 保持用户ID，创建新会话")
    print("  💾 自动保存提示 - 显示会话保存状态")
    
    print("\n🎯 用户体验提升:")
    print("  ✅ 页面刷新不丢失数据")
    print("  ✅ 设置偏好自动记忆")
    print("  ✅ 历史对话可随时查看")
    print("  ✅ 多会话并行管理")
    print("  ✅ 数据导出备份")
    
    print("\n🚀 访问地址: http://localhost:8503")
    print("   现在可以放心刷新页面测试持久化功能！")
    
    return True

def main():
    """主演示函数"""
    print("🎉 智能算命AI - 持久化功能演示")
    print("=" * 80)
    print("演示目标: 展示聊天历史和用户设置的持久化存储功能")
    print("=" * 80)
    
    # 1. 会话持久化演示
    session_id, user_id = demo_session_persistence()
    if not session_id:
        print("❌ 会话持久化演示失败，停止演示")
        return False
    
    # 2. 设置持久化演示
    if not demo_settings_persistence(session_id, user_id):
        print("❌ 设置持久化演示失败")
    
    # 3. 会话历史演示
    if not demo_session_history(user_id):
        print("❌ 会话历史演示失败")
    
    # 4. 数据导出演示
    if not demo_data_export(session_id):
        print("❌ 数据导出演示失败")
    
    # 5. 存储统计演示
    if not demo_storage_stats():
        print("❌ 存储统计演示失败")
    
    # 6. Web界面集成演示
    demo_web_integration()
    
    print("\n" + "=" * 80)
    print("🎉 持久化功能演示完成！")
    print("\n💾 核心功能总结:")
    print("  ✅ 聊天历史自动保存和恢复")
    print("  ✅ 用户设置持久化记忆")
    print("  ✅ 多会话管理和切换")
    print("  ✅ 数据导出和备份")
    print("  ✅ 存储统计和监控")
    print("  ✅ Web界面完整集成")
    
    print("\n🌟 技术亮点:")
    print("  📁 JSON文件存储，易于查看和备份")
    print("  🚀 内存缓存机制，提升访问性能")
    print("  🔄 实时同步保存，防止数据丢失")
    print("  🛡️ 完善错误处理，保证系统稳定")
    print("  📊 详细统计监控，了解使用情况")
    
    print("\n🎯 用户体验:")
    print("  🔄 页面刷新后数据完整保留")
    print("  📚 历史会话随时查看和恢复")
    print("  ⚙️ 个人偏好设置自动记忆")
    print("  📤 支持数据导出和备份")
    print("  🗑️ 灵活的会话管理功能")
    
    print("\n🚀 现在可以访问 http://localhost:8503 体验完整功能！")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
