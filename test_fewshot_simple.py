#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Few-shot Learning测试
"""

import sys
import os
sys.path.append('.')

def main():
    print("Few-shot Learning集成测试")
    print("=" * 60)
    
    # 测试1: Few-shot样本管理器
    print("测试Few-shot样本管理器")
    try:
        from core.conversation.fewshot_samples import fewshot_manager, format_fewshot_prompt
        
        samples = fewshot_manager.get_relevant_samples('我想知道我的工作运势', 'career', 2)
        print(f"匹配样本: {len(samples)} 个")
        
        prompt = format_fewshot_prompt('我想知道我的工作运势', 'career', 2)
        print(f"Prompt生成: {len(prompt)} 字符")
        print("样本管理器测试通过")
        
    except Exception as e:
        print(f"样本管理器测试失败: {e}")
        return False
    
    # 测试2: Few-shot LLM调用
    print("\n测试Few-shot LLM调用")
    try:
        from core.nlu.llm_client import LLMClient
        client = LLMClient()
        
        response = client.fewshot_chat('我最近工作不顺', 'career')
        if response and len(response) > 100:
            print(f"LLM调用成功: {len(response)} 字符")
            
            # 检查专业话术
            professional_terms = ['根据', '建议', '保持', '通过', '将会']
            found_terms = [term for term in professional_terms if term in response]
            print(f"专业术语: {len(found_terms)}/{len(professional_terms)}")
            print("LLM调用测试通过")
        else:
            print("LLM调用失败")
            return False
            
    except Exception as e:
        print(f"LLM调用测试失败: {e}")
        return False
    
    # 测试3: 人性化引擎集成
    print("\n测试人性化引擎集成")
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        responses = engine.process_user_message('我1988年6月1日午时出生，男，想看紫薇斗数', 'test_fewshot')
        
        detailed_count = sum(1 for r in responses if r.get('type') == 'detailed_analysis')
        print(f"详细分析数: {detailed_count}")
        
        # 检查分析质量
        professional_count = 0
        for response in responses:
            if response.get('type') == 'detailed_analysis':
                content = response.get('content', '')
                professional_terms = ['根据', '建议', '保持', '通过', '将会']
                found_terms = [term for term in professional_terms if term in content]
                if len(found_terms) >= 3:
                    professional_count += 1
        
        print(f"专业分析数: {professional_count}/{detailed_count}")
        if detailed_count > 0:
            print(f"专业率: {professional_count/detailed_count*100:.1f}%")
        
        print("人性化引擎集成测试通过")
        
    except Exception as e:
        print(f"人性化引擎集成测试失败: {e}")
        return False
    
    print("\nFew-shot Learning集成测试完成！")
    print("微调模型话术成功融入")
    print("专业算命师风格复现")
    print("人性化交互体验提升")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n所有测试通过！")
    else:
        print("\n部分测试失败！")
    sys.exit(0 if success else 1)
