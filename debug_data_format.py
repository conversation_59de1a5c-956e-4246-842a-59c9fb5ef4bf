#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据格式差异
"""

import asyncio
import json

async def debug_data_format():
    """调试数据格式差异"""
    print("🔍 调试数据格式差异")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import AgentMessage, MessageType
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("debug_test")
        
        # 测试生辰信息
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"📅 测试生辰信息: {birth_info}")
        
        # 使用process_message方法
        content = {
            "calculation_type": "ziwei",
            "birth_info": birth_info,
            "user_message": "调试数据格式",
            "session_id": "debug_session"
        }
        
        message = AgentMessage(
            message_id="debug_message_001",
            message_type=MessageType.CALCULATION_REQUEST,
            sender_id="debug_user",
            receiver_id=calculator_agent.agent_id,
            content=content,
            timestamp=""
        )
        
        print("\n🔄 调用process_message方法...")
        result = await calculator_agent.process_message(message)
        
        if result.success:
            result_id = result.data.get("result_id")
            print(f"✅ 分析记录创建成功: {result_id}")
            
            # 获取缓存结果
            cached_result = calculator_agent.cache.get_result(result_id)
            if cached_result:
                print(f"\n📊 缓存结果分析:")
                print(f"  result_id: {cached_result.result_id}")
                print(f"  calculation_type: {cached_result.calculation_type}")
                
                # 检查raw_calculation的结构
                raw_calc = cached_result.raw_calculation
                print(f"\n🔍 raw_calculation 结构:")
                print(f"  类型: {type(raw_calc)}")
                print(f"  键: {list(raw_calc.keys()) if isinstance(raw_calc, dict) else 'Not a dict'}")
                
                if isinstance(raw_calc, dict):
                    # 检查是否有ziwei_analysis
                    if "ziwei_analysis" in raw_calc:
                        ziwei_data = raw_calc["ziwei_analysis"]
                        print(f"  ziwei_analysis 类型: {type(ziwei_data)}")
                        if isinstance(ziwei_data, dict):
                            print(f"  ziwei_analysis 键: {list(ziwei_data.keys())}")
                            if "palaces" in ziwei_data:
                                palaces = ziwei_data["palaces"]
                                print(f"  宫位数量: {len(palaces)}")
                                if "命宫" in palaces:
                                    mingong = palaces["命宫"]
                                    print(f"  命宫数据: {mingong}")
                                else:
                                    print("  ❌ 命宫数据缺失")
                            else:
                                print("  ❌ palaces字段缺失")
                        else:
                            print("  ❌ ziwei_analysis不是字典")
                    else:
                        print("  ❌ ziwei_analysis字段缺失")
                    
                    # 检查是否有bazi_analysis
                    if "bazi_analysis" in raw_calc:
                        bazi_data = raw_calc["bazi_analysis"]
                        print(f"  bazi_analysis 类型: {type(bazi_data)}")
                        if isinstance(bazi_data, dict):
                            print(f"  bazi_analysis 键: {list(bazi_data.keys())}")
                        else:
                            print("  ❌ bazi_analysis不是字典")
                    else:
                        print("  ❌ bazi_analysis字段缺失")
                
                # 保存到文件用于详细检查
                debug_file = f"debug_data_{result_id}.json"
                with open(debug_file, 'w', encoding='utf-8') as f:
                    json.dump(raw_calc, f, ensure_ascii=False, indent=2)
                print(f"\n💾 详细数据已保存到: {debug_file}")
                
                return True
            else:
                print("❌ 无法获取缓存结果")
                return False
        else:
            print(f"❌ process_message失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def compare_with_direct_fusion():
    """与直接融合引擎对比"""
    print(f"\n🔄 与直接融合引擎对比")
    print("=" * 40)
    
    try:
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=1988,
            month=6,
            day=1,
            hour=11,
            gender="男"
        )
        
        if raw_data.get("success"):
            print("✅ 直接融合引擎成功")
            print(f"📊 数据结构: {list(raw_data.keys())}")
            
            # 检查ziwei_analysis
            if "ziwei_analysis" in raw_data:
                ziwei_data = raw_data["ziwei_analysis"]
                print(f"  ziwei_analysis 类型: {type(ziwei_data)}")
                if isinstance(ziwei_data, dict):
                    print(f"  ziwei_analysis 键: {list(ziwei_data.keys())}")
                    if "palaces" in ziwei_data:
                        palaces = ziwei_data["palaces"]
                        print(f"  宫位数量: {len(palaces)}")
                        if "命宫" in palaces:
                            mingong = palaces["命宫"]
                            print(f"  命宫数据: {mingong}")
                        else:
                            print("  ❌ 命宫数据缺失")
                    else:
                        print("  ❌ palaces字段缺失")
            
            # 保存到文件用于对比
            with open("debug_direct_fusion.json", 'w', encoding='utf-8') as f:
                json.dump(raw_data, f, ensure_ascii=False, indent=2)
            print(f"\n💾 直接融合数据已保存到: debug_direct_fusion.json")
            
            return True
        else:
            print("❌ 直接融合引擎失败")
            return False
            
    except Exception as e:
        print(f"❌ 直接融合对比失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 数据格式调试")
    print("=" * 70)
    
    # 1. 调试process_message的数据格式
    process_success = await debug_data_format()
    
    # 2. 对比直接融合引擎的数据格式
    direct_success = await compare_with_direct_fusion()
    
    print("\n" + "=" * 70)
    print("🎯 数据格式调试结果:")
    
    if process_success:
        print("✅ process_message数据格式已分析")
    else:
        print("❌ process_message数据格式分析失败")
    
    if direct_success:
        print("✅ 直接融合引擎数据格式已分析")
    else:
        print("❌ 直接融合引擎数据格式分析失败")
    
    if process_success and direct_success:
        print("\n💡 请检查生成的JSON文件来对比数据格式差异")
        print("  - debug_data_*.json: process_message保存的数据")
        print("  - debug_direct_fusion.json: 直接融合引擎的数据")
    else:
        print("\n⚠️ 数据格式调试未完成")

if __name__ == "__main__":
    asyncio.run(main())
