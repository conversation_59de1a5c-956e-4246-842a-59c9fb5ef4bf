#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证排盘计算的准确性
"""

import sys
import os
import json

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from simple_interface import SimplePaipanInterface

def verify_calculation_accuracy():
    """验证计算准确性"""
    print("🔍 验证排盘计算准确性")
    print("=" * 60)
    
    # 测试案例：1990年3月15日8时 女
    test_case = {
        "year": 1990,
        "month": 3, 
        "day": 15,
        "hour": 8,
        "gender": "女"
    }
    
    print(f"📋 测试案例: {test_case}")
    
    # 计算排盘
    interface = SimplePaipanInterface()
    result = interface.calculate_and_save(**test_case)
    
    if not result["success"]:
        print("❌ 计算失败")
        return False
    
    # 获取计算结果
    calc_result = result["calculation_result"]
    
    print("\n🔍 验证关键数据:")
    print("-" * 40)
    
    # 1. 验证基础信息
    birth_info = calc_result.get("birth_info", {})
    print(f"✅ 出生时间: {birth_info.get('datetime', '')}")
    print(f"✅ 农历: {birth_info.get('lunar', '')}")
    print(f"✅ 生肖: {birth_info.get('zodiac', '')}")
    print(f"✅ 星座: {birth_info.get('sign', '')}")
    
    # 2. 验证紫薇斗数
    ziwei_analysis = calc_result.get("ziwei_analysis", {})
    if ziwei_analysis:
        print(f"\n🌟 紫薇斗数验证:")
        
        # 验证八字一致性
        ziwei_bazi = ziwei_analysis.get("birth_info", {}).get("chinese_date", "")
        print(f"  八字: {ziwei_bazi}")
        
        # 验证命宫
        palaces = ziwei_analysis.get("palaces", {})
        ming_gong = palaces.get("命宫", {})
        if ming_gong:
            print(f"  命宫: {ming_gong.get('position', '')}宫")
            print(f"  命宫主星: {', '.join(ming_gong.get('major_stars', []))}")
        
        # 验证身宫
        body_palace = None
        for name, palace in palaces.items():
            if palace.get("is_body_palace"):
                body_palace = name
                break
        print(f"  身宫: {body_palace}")
        
        print(f"  宫位总数: {len(palaces)}")
    
    # 3. 验证八字分析
    bazi_analysis = calc_result.get("bazi_analysis", {})
    if bazi_analysis and bazi_analysis.get("success"):
        print(f"\n🎯 八字分析验证:")
        
        # 验证八字
        bazi_info = bazi_analysis.get("bazi_info", {})
        if bazi_info:
            print(f"  八字: {bazi_info.get('chinese_date', '')}")
            print(f"  年柱: {bazi_info.get('year_pillar', '')}")
            print(f"  月柱: {bazi_info.get('month_pillar', '')}")
            print(f"  日柱: {bazi_info.get('day_pillar', '')}")
            print(f"  时柱: {bazi_info.get('hour_pillar', '')}")
        
        # 验证五行分析
        analysis = bazi_analysis.get("analysis", {})
        wuxing = analysis.get("wuxing", {})
        if wuxing:
            print(f"  五行统计:")
            count = wuxing.get("count", {})
            for element, num in count.items():
                print(f"    {element}: {num}")
        
        # 验证日主
        day_master = analysis.get("day_master", {})
        if day_master:
            print(f"  日主: {day_master.get('gan', '')} ({day_master.get('element', '')})")
            print(f"  身强弱: {day_master.get('strength', '')}")
    
    # 4. 验证融合分析
    fusion_analysis = calc_result.get("fusion_analysis", {})
    if fusion_analysis:
        print(f"\n🔗 融合分析验证:")
        
        # 验证交叉验证
        cross_validation = fusion_analysis.get("cross_validation", {})
        confidence = cross_validation.get("confidence_level", 0)
        print(f"  一致性得分: {confidence:.2f}")
        
        # 验证一致性检查
        consistency = cross_validation.get("consistency_check", {})
        for item, check in consistency.items():
            if isinstance(check, dict):
                status = "✅" if check.get("consistent", False) else "❌"
                print(f"  {item}一致性: {status}")
    
    # 5. 验证关键计算逻辑
    print(f"\n🧮 关键计算验证:")
    
    # 验证时辰转换 (8时应该是辰时)
    expected_hour_branch = "辰"
    if ziwei_bazi:
        hour_pillar = ziwei_bazi.split()[-1] if ziwei_bazi else ""
        hour_branch = hour_pillar[1] if len(hour_pillar) > 1 else ""
        if hour_branch == expected_hour_branch:
            print(f"  ✅ 时辰转换正确: 8时 → {hour_branch}时")
        else:
            print(f"  ❌ 时辰转换异常: 8时 → {hour_branch}时 (期望: {expected_hour_branch})")
    
    # 验证生肖计算 (1990年应该是马年)
    expected_zodiac = "马"
    actual_zodiac = birth_info.get("zodiac", "")
    if actual_zodiac == expected_zodiac:
        print(f"  ✅ 生肖计算正确: 1990年 → {actual_zodiac}")
    else:
        print(f"  ❌ 生肖计算异常: 1990年 → {actual_zodiac} (期望: {expected_zodiac})")
    
    # 验证星座计算 (3月15日应该是双鱼座)
    expected_sign = "双鱼座"
    actual_sign = birth_info.get("sign", "")
    if actual_sign == expected_sign:
        print(f"  ✅ 星座计算正确: 3月15日 → {actual_sign}")
    else:
        print(f"  ❌ 星座计算异常: 3月15日 → {actual_sign} (期望: {expected_sign})")
    
    print(f"\n📊 数据完整性检查:")
    
    # 检查数据完整性
    checks = [
        ("基础信息", bool(birth_info)),
        ("紫薇分析", bool(ziwei_analysis)),
        ("八字分析", bool(bazi_analysis and bazi_analysis.get("success"))),
        ("融合分析", bool(fusion_analysis)),
        ("十二宫数据", len(palaces) == 12 if palaces else False),
        ("八字四柱", len(ziwei_bazi.split()) == 4 if ziwei_bazi else False)
    ]
    
    for check_name, check_result in checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}")
    
    # 总体评估
    success_count = sum(1 for _, result in checks if result)
    total_count = len(checks)
    success_rate = success_count / total_count
    
    print(f"\n📈 总体评估:")
    print(f"  完整性: {success_count}/{total_count} ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        print(f"  ✅ 计算质量: 优秀")
    elif success_rate >= 0.6:
        print(f"  ⚠️ 计算质量: 良好")
    else:
        print(f"  ❌ 计算质量: 需要改进")
    
    return success_rate >= 0.8

def main():
    """主函数"""
    try:
        success = verify_calculation_accuracy()
        
        print(f"\n{'='*60}")
        if success:
            print("🎉 验证完成：计算准确性良好！")
        else:
            print("⚠️ 验证完成：发现一些问题，需要检查")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
