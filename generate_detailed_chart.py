#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细的紫薇斗数+八字融合图表
在原有方格布局基础上添加更多细节信息
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_detailed_chart():
    """生成详细的紫薇斗数图表"""
    print("🎨 生成详细紫薇斗数+八字融合图表")
    print("=" * 50)

    try:
        # 获取融合数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")

        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False

        # 创建更大的画布以容纳更多信息
        fig, ax = plt.subplots(1, 1, figsize=(24, 18))
        ax.set_xlim(0, 24)
        ax.set_ylim(0, 18)
        ax.set_aspect('equal')
        ax.axis('off')

        # 设置背景色
        fig.patch.set_facecolor('#f5f5f5')

        # 1. 绘制标题
        draw_detailed_title(ax, result)

        # 2. 绘制详细的12宫格
        draw_detailed_palaces(ax, result)

        # 3. 在中央添加八字和其他信息
        draw_center_info(ax, result)

        # 保存图片
        output_file = "detailed_chart.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='#f5f5f5', edgecolor='none')

        print(f"✅ 详细图表生成成功: {output_file}")
        plt.show()

        return True

    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def draw_detailed_title(ax, result):
    """绘制详细标题"""
    birth_info = result.get("birth_info", {})

    # 主标题
    ax.text(12, 16.5, "紫薇斗数命盘", ha='center', va='center',
           fontsize=20, fontweight='bold', color='#8B0000')

    # 出生信息
    datetime_str = birth_info.get('datetime', '')
    lunar_str = birth_info.get('lunar', '')

    ax.text(12, 15.8, f"{datetime_str}", ha='center', va='center',
           fontsize=14, color='#000080')
    ax.text(12, 15.3, f"农历: {lunar_str}", ha='center', va='center',
           fontsize=12, color='#000080')

def draw_detailed_palaces(ax, result):
    """绘制详细的12宫格"""
    ziwei_data = result.get("ziwei_analysis", {})
    palaces = ziwei_data.get("palaces", {})

    # 12宫的布局 - 4x4网格，中间4格空出
    palace_layout = [
        ["子女宫", "财帛宫", "疾厄宫", "迁移宫"],
        ["夫妻宫", "", "", "奴仆宫"],
        ["兄弟宫", "", "", "官禄宫"],
        ["命宫", "父母宫", "福德宫", "田宅宫"]
    ]

    # 宫格大小
    cell_width = 5.5
    cell_height = 3.5
    start_x = 1
    start_y = 13

    for row in range(4):
        for col in range(4):
            palace_name = palace_layout[row][col]
            if palace_name:  # 不是空格
                x = start_x + col * cell_width
                y = start_y - row * cell_height

                palace_data = palaces.get(palace_name, {})
                draw_detailed_palace_cell(ax, x, y, cell_width, cell_height,
                                        palace_name, palace_data)

def draw_detailed_palace_cell(ax, x, y, width, height, palace_name, palace_data):
    """绘制详细的单个宫格"""
    # 根据宫位重要性选择背景色
    bg_colors = {
        "命宫": "#FFF8DC", "财帛宫": "#F0FFF0", "夫妻宫": "#FFE4E1",
        "官禄宫": "#E6E6FA", "福德宫": "#F5F5DC", "田宅宫": "#F0F8FF"
    }
    bg_color = bg_colors.get(palace_name, "#F8F8FF")

    # 宫格边框
    rect = Rectangle((x, y), width, height,
                    linewidth=2, edgecolor='#8B0000',
                    facecolor=bg_color, alpha=0.9)
    ax.add_patch(rect)

    # 宫位名称（左上角）- 重要宫位用粗体
    important_palaces = ["命宫", "财帛宫", "夫妻宫", "官禄宫"]
    font_weight = 'bold' if palace_name in important_palaces else 'normal'
    name_color = '#8B0000' if palace_name in important_palaces else '#000080'

    ax.text(x + 0.2, y + height - 0.3, palace_name,
           ha='left', va='top', fontsize=11,
           fontweight=font_weight, color=name_color)

    # 地支（右上角）
    position = palace_data.get("position", "")
    if position:
        ax.text(x + width - 0.2, y + height - 0.3, f"({position})",
               ha='right', va='top', fontsize=10,
               fontweight='bold', color='#000080')

    # 主星区域（上半部分）- 按重要性分颜色
    major_stars = palace_data.get("major_stars", [])
    if major_stars:
        y_pos = y + height - 0.8
        for i, star in enumerate(major_stars[:4]):  # 最多显示4个主星
            # 根据星曜重要性选择颜色
            star_color = get_star_color(star)

            # 计算位置 - 2列布局
            col = i % 2
            row = i // 2
            star_x = x + 0.3 + col * 2.3
            star_y = y_pos - row * 0.4

            ax.text(star_x, star_y, star,
                   ha='left', va='top', fontsize=10,
                   fontweight='bold', color=star_color)

    # 副星区域（中间部分）
    minor_stars = palace_data.get("minor_stars", [])
    if minor_stars:
        y_pos = y + height - 1.8
        for i, star in enumerate(minor_stars[:8]):  # 最多显示8个副星
            # 3列布局
            col = i % 3
            row = i // 3
            star_x = x + 0.2 + col * 1.6
            star_y = y_pos - row * 0.25

            ax.text(star_x, star_y, star,
                   ha='left', va='top', fontsize=8,
                   color='#228B22')

    # 四化（左下角）
    transformations = palace_data.get("transformations", [])
    if transformations:
        y_pos = y + 0.8
        for i, trans in enumerate(transformations[:3]):
            # 四化用特殊颜色
            trans_color = get_transformation_color(trans)
            ax.text(x + 0.2, y_pos - i * 0.25, trans,
                   ha='left', va='bottom', fontsize=8,
                   fontweight='bold', color=trans_color)

    # 添加更多细节信息
    add_palace_details(ax, x, y, width, height, palace_name, palace_data)

def get_star_color(star):
    """根据星曜重要性返回颜色"""
    # 甲级主星 - 红色
    major_stars = ["紫微", "天机", "太阳", "武曲", "天同", "廉贞", "天府",
                  "太阴", "贪狼", "巨门", "天相", "天梁", "七杀", "破军"]
    if star in major_stars:
        return '#DC143C'

    # 乙级副星 - 蓝色
    minor_stars = ["左辅", "右弼", "文昌", "文曲", "天魁", "天钺",
                  "禄存", "天马", "化禄", "化权", "化科", "化忌"]
    if star in minor_stars:
        return '#0000CD'

    # 其他星曜 - 绿色
    return '#228B22'

def get_transformation_color(trans):
    """根据四化类型返回颜色"""
    if "禄" in trans:
        return '#32CD32'  # 绿色 - 吉
    elif "权" in trans:
        return '#FF8C00'  # 橙色 - 中性偏吉
    elif "科" in trans:
        return '#4169E1'  # 蓝色 - 吉
    elif "忌" in trans:
        return '#DC143C'  # 红色 - 凶
    return '#808080'  # 灰色 - 其他

def add_palace_details(ax, x, y, width, height, palace_name, palace_data):
    """添加宫位的详细信息"""
    # 添加宫位的天干地支信息
    position = palace_data.get("position", "")
    if position:
        # 在左下角添加地支的详细信息
        ax.text(x + 0.2, y + 0.8, position,
               ha='left', va='bottom', fontsize=9,
               fontweight='bold', color='#4B0082')

    # 添加宫位的五行属性
    wuxing_attr = get_palace_wuxing(palace_name)
    if wuxing_attr:
        ax.text(x + width/2, y + 0.2, wuxing_attr,
               ha='center', va='bottom', fontsize=8,
               color='#8B4513')

    # 添加宫位强弱标记
    strength = palace_data.get("strength", "")
    if strength:
        # 用不同颜色表示强弱
        color = '#FF0000' if '旺' in strength else '#0000FF' if '弱' in strength else '#808080'
        ax.text(x + width - 0.2, y + 0.8, strength,
               ha='right', va='bottom', fontsize=8,
               color=color)

    # 添加数字标记（模拟网站上的数字）
    if palace_data.get("major_stars"):
        star_count = len(palace_data["major_stars"])
        ax.text(x + width - 0.3, y + height - 0.8, str(star_count),
               ha='center', va='center', fontsize=10,
               fontweight='bold', color='#FFFFFF',
               bbox=dict(boxstyle="circle,pad=0.1", facecolor='#FF6347', alpha=0.8))

def get_palace_wuxing(palace_name):
    """获取宫位的五行属性"""
    wuxing_map = {
        "命宫": "土", "兄弟宫": "木", "夫妻宫": "金", "子女宫": "水",
        "财帛宫": "土", "疾厄宫": "火", "迁移宫": "水", "奴仆宫": "土",
        "官禄宫": "火", "田宅宫": "土", "福德宫": "木", "父母宫": "金"
    }
    return wuxing_map.get(palace_name, "")

def draw_center_info(ax, result):
    """在中央绘制八字和融合信息"""
    # 中央区域坐标
    center_x = 6.5
    center_y = 6.5
    center_width = 11
    center_height = 7

    # 中央背景
    center_rect = Rectangle((center_x, center_y), center_width, center_height,
                           linewidth=2, edgecolor='#8B0000',
                           facecolor='#FFFACD', alpha=0.9)
    ax.add_patch(center_rect)

    # 八字信息（上半部分）
    draw_bazi_in_center_detailed(ax, result, center_x, center_y + 4)

    # 五行分析（中间）
    draw_wuxing_detailed(ax, result, center_x, center_y + 2)

    # 融合验证（下半部分）
    draw_validation_detailed(ax, result, center_x, center_y + 0.5)

def draw_bazi_in_center_detailed(ax, result, x, y):
    """绘制详细的八字信息"""
    bazi_data = result.get("bazi_analysis", {})

    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")

        # 八字标题
        ax.text(x + 5.5, y + 1.5, "八字命理", ha='center', va='center',
               fontsize=16, fontweight='bold', color='#8B0000')

        # 分割四柱
        pillars = chinese_date.split()
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]

        for i, (name, pillar) in enumerate(zip(pillar_names, pillars)):
            col_x = x + 1 + i * 2.5

            # 柱名
            ax.text(col_x, y + 0.8, name, ha='center', va='center',
                   fontsize=10, fontweight='bold', color='#000080')

            # 天干
            if len(pillar) >= 1:
                ax.text(col_x, y + 0.4, pillar[0], ha='center', va='center',
                       fontsize=14, fontweight='bold', color='#DC143C')

            # 地支
            if len(pillar) >= 2:
                ax.text(col_x, y, pillar[1], ha='center', va='center',
                       fontsize=14, fontweight='bold', color='#000080')

def draw_wuxing_detailed(ax, result, x, y):
    """绘制详细的五行分析"""
    bazi_data = result.get("bazi_analysis", {})

    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})
        wuxing_strength = wuxing.get("strength", {})

        # 五行标题
        ax.text(x + 5.5, y + 1, "五行分析", ha='center', va='center',
               fontsize=14, fontweight='bold', color='#8B0000')

        elements = ["木", "火", "土", "金", "水"]
        colors = ["#228B22", "#DC143C", "#DAA520", "#C0C0C0", "#000080"]

        for i, (element, color) in enumerate(zip(elements, colors)):
            col_x = x + 1 + i * 2
            count = wuxing_count.get(element, 0)
            strength = wuxing_strength.get(element, "")

            # 五行名称
            ax.text(col_x, y + 0.5, element, ha='center', va='center',
                   fontsize=12, fontweight='bold', color=color)

            # 数量
            ax.text(col_x, y + 0.1, f"{count:.1f}个", ha='center', va='center',
                   fontsize=10, color=color)

            # 强弱
            ax.text(col_x, y - 0.3, strength, ha='center', va='center',
                   fontsize=9, color=color)

def draw_validation_detailed(ax, result, x, y):
    """绘制详细的验证信息"""
    fusion = result.get("fusion_analysis", {})

    if "cross_validation" in fusion:
        validation = fusion["cross_validation"]
        confidence = validation.get("confidence_level", 0)

        # 验证标题
        ax.text(x + 5.5, y + 0.5, "交叉验证结果", ha='center', va='center',
               fontsize=12, fontweight='bold', color='#8B0000')

        # 一致性得分
        ax.text(x + 5.5, y, f"紫薇+八字一致性: {confidence:.0%}",
               ha='center', va='center', fontsize=11,
               color='#228B22' if confidence > 0.8 else '#DC143C')

def main():
    """主函数"""
    print("🎨 详细紫薇斗数图表生成器")
    print("=" * 60)

    success = generate_detailed_chart()

    if success:
        print("\n✅ 详细图表生成成功！")
        print("📁 文件保存为: detailed_chart.png")
        print("🖼️ 详细特点:")
        print("  - 保持原有方格布局")
        print("  - 每个宫格包含详细信息:")
        print("    * 主星（红色）")
        print("    * 副星（绿色）")
        print("    * 四化（橙色）")
        print("    * 地支位置")
        print("    * 特殊标记")
        print("  - 中央融合八字和五行分析")
        print("  - 专业的颜色编码")
        print("  - 更大的画布容纳更多信息")
    else:
        print("\n❌ 详细图表生成失败")

if __name__ == "__main__":
    main()
