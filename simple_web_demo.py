#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Flask Web演示页面
展示后台Agent的分析结果
"""

from flask import Flask, render_template_string, request, jsonify
import json
import os
import sys
sys.path.append('.')

app = Flask(__name__)

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紫薇斗数后台Agent演示</title>
    <style>
        body {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #2d2d2d;
            border-radius: 10px;
        }
        .section {
            background-color: #2d2d2d;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .debug-info {
            background-color: #3d3d3d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .analysis-card {
            background-color: #3d3d3d;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 3px solid #2196F3;
        }
        .progress-bar {
            background-color: #3d3d3d;
            border-radius: 10px;
            padding: 5px;
            margin: 10px 0;
        }
        .progress-fill {
            background-color: #4CAF50;
            height: 20px;
            border-radius: 8px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-weight: bold;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .cache-list {
            background-color: #3d3d3d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .cache-item {
            padding: 10px;
            margin: 5px 0;
            background-color: #4d4d4d;
            border-radius: 3px;
            cursor: pointer;
        }
        .cache-item:hover {
            background-color: #5d5d5d;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 紫薇斗数后台Agent演示</h1>
            <p>展示后台Agent的算法计算、图片生成和12角度分析功能</p>
        </div>

        <div class="section">
            <h2>📁 缓存文件列表</h2>
            <div class="cache-list">
                {% for cache_file in cache_files %}
                <div class="cache-item" onclick="loadCache('{{ cache_file.id }}')">
                    <strong>{{ cache_file.id[:8] }}...</strong> - 
                    {{ cache_file.birth_info }} - 
                    {{ cache_file.created_at }}
                    {% if cache_file.has_analysis %}
                    <span style="color: #4CAF50;">✅ 有分析</span>
                    {% else %}
                    <span style="color: #ff9800;">⏳ 生成中</span>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <button class="btn" onclick="refreshCacheList()">🔄 刷新列表</button>
        </div>

        <div id="result-section" style="display: none;">
            <div class="section">
                <h2>👤 用户信息</h2>
                <div id="user-info"></div>
            </div>

            <div class="section">
                <h2>🎨 排盘图片</h2>
                <div id="chart-image"></div>
            </div>

            <div class="section">
                <h2>📈 分析进度</h2>
                <div id="progress-info"></div>
            </div>

            <div class="section">
                <h2>📋 12角度详细分析</h2>
                <div id="analysis-content"></div>
            </div>
        </div>
    </div>

    <script>
        function refreshCacheList() {
            location.reload();
        }

        function loadCache(cacheId) {
            fetch('/api/cache/' + cacheId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayResult(data.result);
                        document.getElementById('result-section').style.display = 'block';
                    } else {
                        alert('加载失败: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('请求失败: ' + error);
                });
        }

        function displayResult(result) {
            // 显示用户信息
            const userInfo = result.birth_info;
            document.getElementById('user-info').innerHTML = `
                <p><strong>生辰:</strong> ${userInfo.year}年${userInfo.month}月${userInfo.day}日 ${userInfo.hour}</p>
                <p><strong>性别:</strong> ${userInfo.gender}命</p>
                <p><strong>创建时间:</strong> ${result.created_at}</p>
            `;

            // 显示排盘图片
            if (result.chart_image_path) {
                document.getElementById('chart-image').innerHTML = `
                    <div class="image-container">
                        <img src="/static/${result.chart_image_path}" alt="紫薇斗数排盘图">
                        <p>图片路径: ${result.chart_image_path}</p>
                    </div>
                `;
            } else {
                document.getElementById('chart-image').innerHTML = '<p>⚠️ 排盘图片生成中...</p>';
            }

            // 显示分析进度
            const analysis = result.detailed_analysis;
            if (analysis && analysis.angle_analyses) {
                const completed = Object.keys(analysis.angle_analyses).length;
                const total = 12;
                const progress = (completed / total) * 100;
                
                document.getElementById('progress-info').innerHTML = `
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%">
                            ${completed}/${total} (${progress.toFixed(1)}%)
                        </div>
                    </div>
                    <p>已完成 ${completed} 个角度，共 ${total} 个角度</p>
                    <p>总字数: ${analysis.total_word_count || 0}</p>
                `;

                // 显示分析内容
                let analysisHtml = '';
                const angleNames = {
                    'personality_destiny': '🏛️ 命宫分析 - 性格命运核心特征',
                    'wealth_fortune': '💰 财富分析 - 财运状况与理财投资',
                    'marriage_love': '💕 婚姻分析 - 感情婚姻与桃花运势',
                    'health_wellness': '🏥 健康分析 - 身体状况与养生建议',
                    'career_achievement': '💼 事业分析 - 职业发展与成就潜力',
                    'children_creativity': '👶 子女分析 - 生育状况与子女关系'
                };

                for (const [key, name] of Object.entries(angleNames)) {
                    if (analysis.angle_analyses[key]) {
                        const content = analysis.angle_analyses[key];
                        const wordCount = content.length;
                        analysisHtml += `
                            <div class="analysis-card">
                                <h3>${name} (${wordCount}字)</h3>
                                <div style="max-height: 300px; overflow-y: auto; white-space: pre-wrap;">
                                    ${content.substring(0, 500)}...
                                </div>
                            </div>
                        `;
                    } else {
                        analysisHtml += `
                            <div class="analysis-card" style="opacity: 0.6;">
                                <h3>${name}</h3>
                                <p>⏳ 分析生成中...</p>
                            </div>
                        `;
                    }
                }

                document.getElementById('analysis-content').innerHTML = analysisHtml;
            } else {
                document.getElementById('progress-info').innerHTML = '<p>⚠️ 分析数据格式异常</p>';
                document.getElementById('analysis-content').innerHTML = '<p>⚠️ 无分析内容</p>';
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页面"""
    # 读取缓存文件列表
    cache_dir = 'data/calculation_cache'
    cache_files = []
    
    if os.path.exists(cache_dir):
        for filename in os.listdir(cache_dir):
            if filename.endswith('.json') and filename != 'index.json':
                filepath = os.path.join(cache_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    cache_info = {
                        'id': data.get('result_id', filename[:-5]),
                        'birth_info': f"{data.get('birth_info', {}).get('year', '?')}年{data.get('birth_info', {}).get('month', '?')}月{data.get('birth_info', {}).get('day', '?')}日 {data.get('birth_info', {}).get('gender', '?')}命",
                        'created_at': data.get('created_at', '未知')[:19],
                        'has_analysis': bool(data.get('detailed_analysis', {}).get('angle_analyses'))
                    }
                    cache_files.append(cache_info)
                except Exception as e:
                    print(f"读取缓存文件失败 {filename}: {e}")
    
    # 按创建时间排序
    cache_files.sort(key=lambda x: x['created_at'], reverse=True)
    
    return render_template_string(HTML_TEMPLATE, cache_files=cache_files)

@app.route('/api/cache/<cache_id>')
def get_cache(cache_id):
    """获取缓存数据API"""
    try:
        cache_file = f'data/calculation_cache/{cache_id}.json'
        
        if not os.path.exists(cache_file):
            return jsonify({'success': False, 'error': '缓存文件不存在'})
        
        with open(cache_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return jsonify({'success': True, 'result': data})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    from flask import send_from_directory
    return send_from_directory('.', filename)

if __name__ == '__main__':
    print('🚀 启动简单Web演示服务...')
    print('📊 访问地址: http://localhost:5000')
    print('🔮 展示后台Agent分析结果')
    app.run(host='0.0.0.0', port=5000, debug=True)
