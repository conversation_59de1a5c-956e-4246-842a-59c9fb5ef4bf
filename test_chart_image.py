#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试排盘图片生成功能
"""

def test_image_dependencies():
    """测试图片依赖"""
    print("📊 测试图片生成依赖")
    print("=" * 30)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL (Pillow) 可用")
        pil_ok = True
    except ImportError:
        print("❌ PIL (Pillow) 不可用")
        pil_ok = False
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        print("✅ matplotlib 可用")
        mpl_ok = True
    except ImportError:
        print("❌ matplotlib 不可用")
        mpl_ok = False
    
    return pil_ok and mpl_ok

def test_simple_chart_generation():
    """测试简单图表生成"""
    print("\n🎨 测试简单图表生成")
    print("=" * 30)
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        import os
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建简单测试图
        fig, ax = plt.subplots(1, 1, figsize=(8, 8))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 绘制外框
        outer_rect = patches.Rectangle((1, 1), 8, 8, linewidth=3, edgecolor='black', facecolor='white')
        ax.add_patch(outer_rect)
        
        # 添加标题
        ax.text(5, 9, "紫薇斗数命盘测试", ha='center', va='center', fontsize=16, fontweight='bold')
        
        # 绘制几个宫位示例
        positions = [
            (7, 7, "命宫", "紫薇"),
            (5, 7, "兄弟宫", "天机"),
            (3, 7, "夫妻宫", "太阳"),
            (1, 7, "子女宫", "武曲")
        ]
        
        for x, y, palace, star in positions:
            rect = patches.Rectangle((x, y), 1.5, 1.5, linewidth=1, edgecolor='gray', facecolor='lightblue', alpha=0.3)
            ax.add_patch(rect)
            ax.text(x + 0.75, y + 1.2, palace, ha='center', va='center', fontsize=10, fontweight='bold')
            ax.text(x + 0.75, y + 0.5, star, ha='center', va='center', fontsize=9, color='red')
        
        # 保存测试图片
        output_dir = "charts"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        test_image_path = f"{output_dir}/test_chart.png"
        plt.savefig(test_image_path, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"✅ 测试图片已生成: {test_image_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试图片生成失败: {e}")
        return False

def test_real_chart_generation():
    """测试真实排盘图生成"""
    print("\n🔮 测试真实排盘图生成")
    print("=" * 30)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        calc = RealZiweiCalculator()
        result = calc.calculate_chart(1985, 4, 23, 22, "女")
        
        if "error" in result:
            print(f"❌ 算法失败: {result['error']}")
            return False
        
        # 创建引擎实例
        engine = FortuneEngine()
        
        # 测试图片排盘生成
        chart_display = engine._generate_chart_display({"data": result, "success": True})
        
        print("✅ 真实排盘图生成成功")
        
        # 检查是否包含图片路径
        if "图片已生成:" in chart_display:
            print("✅ 包含图片路径信息")
        else:
            print("⚠️ 未生成图片，使用文本排盘")
        
        # 显示部分内容
        lines = chart_display.split('\n')
        for i, line in enumerate(lines[:10]):
            print(line)
        
        if len(lines) > 10:
            print("... (更多内容)")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实排盘图测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_features():
    """测试排盘图功能特性"""
    print("\n🎯 排盘图功能特性")
    print("=" * 30)
    
    features = [
        "✅ 图片格式输出 (PNG)",
        "✅ 高分辨率 (300 DPI)",
        "✅ 中文字体支持",
        "✅ 十二宫位布局",
        "✅ 星曜颜色区分 (主星红色，辅星蓝色)",
        "✅ 身宫特殊标识",
        "✅ 中宫信息显示",
        "✅ 自动保存到charts目录",
        "✅ 文本+图片双重显示"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n🎨 视觉效果:")
    print("  - 专业的传统命理风格")
    print("  - 清晰的宫位分割")
    print("  - 直观的星曜配置")
    print("  - 美观的色彩搭配")
    
    return True

def main():
    """主测试函数"""
    print("🎨 排盘图片生成功能测试")
    print("=" * 50)
    
    # 测试1: 依赖检查
    deps_success = test_image_dependencies()
    
    if not deps_success:
        print("\n❌ 图片依赖不完整，请安装:")
        print("  pip install pillow matplotlib")
        return
    
    # 测试2: 简单图表
    simple_success = test_simple_chart_generation()
    
    # 测试3: 真实排盘图
    real_success = test_real_chart_generation()
    
    # 测试4: 功能特性
    features_success = test_chart_features()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎉 图片生成测试总结:")
    print(f"  依赖检查: {'✅' if deps_success else '❌'}")
    print(f"  简单图表: {'✅' if simple_success else '❌'}")
    print(f"  真实排盘: {'✅' if real_success else '❌'}")
    print(f"  功能特性: {'✅' if features_success else '❌'}")
    
    if all([deps_success, simple_success, real_success, features_success]):
        print("\n🎊 图片排盘功能完成！")
        print("\n📝 功能特点:")
        print("  1. ✅ 生成高质量PNG图片")
        print("  2. ✅ 传统十二宫布局")
        print("  3. ✅ 星曜颜色区分显示")
        print("  4. ✅ 自动保存到charts目录")
        print("  5. ✅ 文本+图片双重输出")
        
        print("\n🚀 现在用户可以:")
        print("  - 看到美观的排盘图片")
        print("  - 直观理解星曜配置")
        print("  - 保存图片作为参考")
        print("  - 获得专业的视觉体验")
    else:
        print("\n⚠️ 部分功能需要进一步完善")

if __name__ == "__main__":
    main()
