#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试八字算命排盘
"""

def test_bazi_calculation():
    """测试八字算命排盘"""
    print("📜 测试八字算命排盘")
    print("=" * 50)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        # 创建算法实例
        calc = RealBaziCalculator()
        
        # 测试数据：1988年6月1日午时男
        print("📅 测试数据：1988年6月1日午时男")
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")  # 午时=11点
        
        if "error" in result:
            print(f"❌ 排盘失败: {result['error']}")
            return False
        
        print("✅ 排盘成功！")
        
        # 显示基本信息
        birth_info = result.get("birth_info", {})
        print(f"\n📊 出生信息:")
        print(f"  公历: {birth_info.get('datetime', '')}")
        print(f"  性别: {birth_info.get('gender', '')}")
        
        # 显示八字信息
        raw_result = result.get("raw_result", {})
        ganzhi = raw_result.get("干支", {})
        print(f"\n🔮 八字信息:")
        print(f"  四柱: {ganzhi.get('文本', '')}")
        
        # 显示五行分析
        wuxing = raw_result.get("五行", {})
        if wuxing:
            print(f"\n🌟 五行分析:")
            for element, info in wuxing.items():
                if isinstance(info, dict):
                    wangshui = info.get("旺衰", "")
                    count = info.get("五行数", "")
                    print(f"  {element}: {wangshui} (数量: {count})")
        
        # 显示大运信息
        dayun = raw_result.get("大运", {})
        if dayun:
            print(f"\n🎯 大运信息:")
            for period, info in list(dayun.items())[:3]:  # 只显示前3个大运
                if isinstance(info, dict):
                    shishen = info.get("十神", "")
                    year = info.get("年份", "")
                    print(f"  {period}: {shishen} ({year}年)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_analysis():
    """测试综合分析（紫薇+八字）"""
    print("\n🌟 测试综合分析")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 创建引擎实例
        engine = FortuneEngine()
        
        # 测试数据
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,  # 午时
            "gender": "男"
        }
        
        print("📅 测试数据：1988年6月1日午时男")
        
        # 调用综合分析
        result = engine._call_comprehensive_api(birth_info)
        
        if not result.get("success"):
            print(f"❌ 综合分析失败: {result.get('error')}")
            return False
        
        print("✅ 综合分析成功！")
        
        # 检查结果
        results = result.get("results", {})
        print(f"\n📊 分析结果:")
        print(f"  包含算法: {list(results.keys())}")
        
        if "ziwei" in results:
            ziwei_data = results["ziwei"]["data"]
            print(f"  紫薇斗数: ✅ (生肖: {ziwei_data.get('zodiac', '')})")
        
        if "bazi" in results:
            bazi_data = results["bazi"]["data"]
            birth_info = bazi_data.get("birth_info", {})
            print(f"  八字命理: ✅ (时间: {birth_info.get('datetime', '')})")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 八字算命和综合分析测试")
    print("=" * 60)
    
    # 测试1: 八字排盘
    bazi_success = test_bazi_calculation()
    
    # 测试2: 综合分析
    comprehensive_success = test_comprehensive_analysis()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 测试总结:")
    print(f"  八字排盘: {'✅' if bazi_success else '❌'}")
    print(f"  综合分析: {'✅' if comprehensive_success else '❌'}")
    
    if bazi_success and comprehensive_success:
        print("\n🎊 所有测试通过！八字和综合分析功能正常")
    else:
        print("\n⚠️ 部分测试失败，需要检查相关功能")

if __name__ == "__main__":
    main()
