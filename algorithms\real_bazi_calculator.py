#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的八字算命算法实现
统一使用py-iztro获取准确八字数据，确保与紫薇斗数一致性
"""

import sys
import os
import datetime
from typing import Dict, Optional

# 添加yxf_yixue_py路径
yixue_path = os.path.join(os.path.dirname(__file__), 'yxf_yixue_py')
sys.path.append(yixue_path)

# 导入py-iztro用于获取准确八字
try:
    import py_iztro
    IZTRO_AVAILABLE = True
    print("✅ py-iztro导入成功 (用于准确八字)")
except ImportError as e:
    IZTRO_AVAILABLE = False
    print(f"❌ py-iztro导入失败: {e}")

# 导入原有八字分析模块
try:
    from yxf_yixue.bazi import BaziApi
    from yxf_yixue.wannianli import WannianliApi
    BAZI_AVAILABLE = True
    print("✅ 八字分析算法模块导入成功")
except ImportError as e:
    BAZI_AVAILABLE = False
    print(f"❌ 八字分析算法模块导入失败: {e}")

class RealBaziCalculator:
    """真正的八字算命计算器 - 统一使用py-iztro获取准确八字"""

    def __init__(self):
        if not IZTRO_AVAILABLE:
            raise ImportError("py-iztro未安装，无法获取准确八字")
        if not BAZI_AVAILABLE:
            print("⚠️ 八字分析模块不可用，仅提供基础八字排盘")
            self.api = None
            self.wannianli = None
        else:
            self.api = BaziApi()
            self.wannianli = WannianliApi()

    def _convert_hour_to_index(self, hour: int) -> int:
        """转换小时到时辰索引"""
        time_mapping = {
            (23, 1): 0,   # 子时
            (1, 3): 1,    # 丑时
            (3, 5): 2,    # 寅时
            (5, 7): 3,    # 卯时
            (7, 9): 4,    # 辰时
            (9, 11): 5,   # 巳时
            (11, 13): 6,  # 午时
            (13, 15): 7,  # 未时
            (15, 17): 8,  # 申时
            (17, 19): 9,  # 酉时
            (19, 21): 10, # 戌时
            (21, 23): 11  # 亥时
        }

        for (start, end), index in time_mapping.items():
            if start <= hour < end or (start == 23 and hour >= 23):
                return index
        return 6  # 默认午时

    def _get_accurate_bazi(self, year: int, month: int, day: int, hour: int, gender: str = "男") -> Dict:
        """使用py-iztro获取准确的八字"""
        try:
            astro = py_iztro.Astro()
            time_index = self._convert_hour_to_index(hour)

            astrolabe = astro.by_solar(
                solar_date_str=f"{year}-{month}-{day}",
                time_index=time_index,
                gender=gender,
                language="zh-CN"
            )

            chinese_date = astrolabe.chinese_date
            bazi_parts = chinese_date.split()

            if len(bazi_parts) != 4:
                return {"error": f"八字格式异常: {chinese_date}"}

            year_pillar, month_pillar, day_pillar, hour_pillar = bazi_parts

            return {
                "success": True,
                "chinese_date": chinese_date,
                "year_pillar": year_pillar,
                "month_pillar": month_pillar,
                "day_pillar": day_pillar,
                "hour_pillar": hour_pillar,
                "lunar_date": astrolabe.lunar_date,
                "zodiac": astrolabe.zodiac,
                "sign": astrolabe.sign
            }

        except Exception as e:
            return {"error": f"获取准确八字失败: {str(e)}"}

    def calculate_bazi(self, year: int, month: int, day: int, hour: int,
                      minute: int = 0, gender: str = "男") -> Dict:
        """计算真正的八字命盘 - 使用py-iztro获取准确八字"""
        try:
            # 1. 使用py-iztro获取准确八字
            accurate_bazi = self._get_accurate_bazi(year, month, day, hour, gender)
            if "error" in accurate_bazi:
                return {
                    "success": False,
                    "error": accurate_bazi["error"]
                }

            # 2. 构建标准格式的结果
            result = {
                "success": True,
                "birth_info": {
                    "datetime": f"{year}年{month}月{day}日{hour}时{minute}分",
                    "gender": gender,
                    "lunar_date": accurate_bazi.get("lunar_date", ""),
                    "zodiac": accurate_bazi.get("zodiac", ""),
                    "sign": accurate_bazi.get("sign", "")
                },
                "raw_result": {
                    "干支": {
                        "文本": accurate_bazi["chinese_date"],
                        "年柱": accurate_bazi["year_pillar"],
                        "月柱": accurate_bazi["month_pillar"],
                        "日柱": accurate_bazi["day_pillar"],
                        "时柱": accurate_bazi["hour_pillar"]
                    }
                },
                "formatted_output": f"八字: {accurate_bazi['chinese_date']}",
                "calculation_type": "准确八字算命 (基于py-iztro)",
                "data_source": "py-iztro"
            }

            # 3. 如果有八字分析模块，尝试添加五行分析
            if self.api:
                try:
                    # 使用原有模块进行五行分析
                    datetime_obj = datetime.datetime(year, month, day, hour, minute)
                    analysis_result = self.api.paipan(datetime_obj, xingbie=gender)

                    # 提取五行信息
                    if "五行" in analysis_result:
                        result["raw_result"]["五行"] = analysis_result["五行"]

                except Exception as analysis_error:
                    result["analysis_note"] = f"五行分析失败: {analysis_error}"

            return result

        except Exception as e:
            return {
                "success": False,
                "error": f"八字计算失败: {str(e)}"
            }

    def calculate_with_traditional_analysis(self, year: int, month: int, day: int, hour: int,
                                          minute: int = 0, gender: str = "男") -> Dict:
        """计算八字并进行传统分析"""
        try:
            # 创建时间对象
            datetime_obj = datetime.datetime(year, month, day, hour, minute)

            # 排盘
            result = self.api.paipan(datetime_obj, xingbie=gender)

            # 进行传统分析（注意：可能有同样的output_addition问题）
            try:
                analysis_result = self.api.get_chuantongfenxi()
                # 直接获取基础排盘输出，避免调用有问题的方法
                formatted_output = self.api.P.output() if self.api.P else "排盘输出失败"
                analysis_note = "已进行传统分析标记"
            except Exception as analysis_error:
                analysis_result = result
                formatted_output = self.api.P.output() if self.api.P else "排盘输出失败"
                analysis_note = f"传统分析遇到问题: {analysis_error}"

            return {
                "success": True,
                "birth_info": {
                    "datetime": f"{year}年{month}月{day}日{hour}时{minute}分",
                    "gender": gender
                },
                "raw_result": result,
                "analysis_result": analysis_result,
                "formatted_output": formatted_output,
                "analysis_note": analysis_note,
                "calculation_type": "真正的八字算命+传统分析"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"八字分析失败: {str(e)}"
            }

    def calculate_with_quantitative_analysis(self, year: int, month: int, day: int, hour: int,
                                           minute: int = 0, gender: str = "男") -> Dict:
        """计算八字并进行量化分析"""
        try:
            # 创建时间对象
            datetime_obj = datetime.datetime(year, month, day, hour, minute)

            # 排盘
            result = self.api.paipan(datetime_obj, xingbie=gender)

            # 进行量化分析
            try:
                analysis_result = self.api.get_lianghuafenxi()
                # 直接获取基础排盘输出
                formatted_output = self.api.P.output() if self.api.P else "排盘输出失败"
                analysis_note = "已进行量化分析"
            except Exception as analysis_error:
                analysis_result = result
                formatted_output = self.api.P.output() if self.api.P else "排盘输出失败"
                analysis_note = f"量化分析遇到问题: {analysis_error}"

            return {
                "success": True,
                "birth_info": {
                    "datetime": f"{year}年{month}月{day}日{hour}时{minute}分",
                    "gender": gender
                },
                "raw_result": result,
                "analysis_result": analysis_result,
                "formatted_output": formatted_output,
                "analysis_note": analysis_note,
                "calculation_type": "真正的八字算命+量化分析"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"八字量化分析失败: {str(e)}"
            }

    def get_calendar_info(self, year: int, month: int, day: int, hour: int) -> Dict:
        """获取万年历信息"""
        try:
            datetime_obj = datetime.datetime(year, month, day, hour, 0)
            calendar_info = self.wannianli.get_Calendar(datetime_obj)

            return {
                "success": True,
                "calendar_info": calendar_info,
                "formatted_calendar": self.wannianli.print_Calendar(datetime_obj)
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"获取万年历信息失败: {str(e)}"
            }

def test_real_bazi():
    """测试真正的八字算命功能"""
    if not BAZI_AVAILABLE:
        print("❌ 真正的八字算法模块不可用，无法测试")
        return

    try:
        calc = RealBaziCalculator()

        print("=== 真正的八字算命测试 ===")

        # 测试1: 基础八字排盘
        print("\n1. 基础八字排盘测试 (1988年6月1日午时男):")
        result1 = calc.calculate_bazi(1988, 6, 1, 12, 0, "男")
        if result1["success"]:
            print(f"出生信息: {result1['birth_info']}")
            print("八字排盘结果:")
            print(result1["formatted_output"])
        else:
            print(f"错误: {result1['error']}")

        # 测试2: 传统分析
        print("\n2. 八字传统分析测试:")
        result2 = calc.calculate_with_traditional_analysis(1988, 6, 1, 12, 0, "男")
        if result2["success"]:
            print(f"分析类型: {result2['calculation_type']}")
            print(f"分析说明: {result2['analysis_note']}")
            print("分析结果:")
            print(result2["formatted_output"])
        else:
            print(f"错误: {result2['error']}")

        # 测试3: 量化分析
        print("\n3. 八字量化分析测试:")
        result3 = calc.calculate_with_quantitative_analysis(1988, 6, 1, 12, 0, "男")
        if result3["success"]:
            print(f"分析类型: {result3['calculation_type']}")
            print(f"分析说明: {result3['analysis_note']}")
            print("量化分析结果:")
            print(result3["formatted_output"])
        else:
            print(f"错误: {result3['error']}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_real_bazi()
