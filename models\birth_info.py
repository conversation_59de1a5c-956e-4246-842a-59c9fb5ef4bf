#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生辰信息数据模型
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
import hashlib

@dataclass
class BirthInfo:
    """生辰信息"""
    year: int
    month: int
    day: int
    hour: int
    gender: str  # "男" 或 "女"
    
    def __post_init__(self):
        """数据验证"""
        self._validate()
    
    def _validate(self):
        """验证生辰信息的有效性"""
        # 验证年份
        if not (1900 <= self.year <= 2100):
            raise ValueError(f"年份无效: {self.year}，应在1900-2100之间")
        
        # 验证月份
        if not (1 <= self.month <= 12):
            raise ValueError(f"月份无效: {self.month}，应在1-12之间")
        
        # 验证日期
        if not (1 <= self.day <= 31):
            raise ValueError(f"日期无效: {self.day}，应在1-31之间")
        
        # 验证小时
        if not (0 <= self.hour <= 23):
            raise ValueError(f"小时无效: {self.hour}，应在0-23之间")
        
        # 验证性别
        if self.gender not in ["男", "女"]:
            raise ValueError(f"性别无效: {self.gender}，应为'男'或'女'")
        
        # 验证日期的合理性
        try:
            datetime(self.year, self.month, self.day, self.hour)
        except ValueError as e:
            raise ValueError(f"日期时间无效: {e}")
    
    def to_datetime(self) -> datetime:
        """转换为datetime对象"""
        return datetime(self.year, self.month, self.day, self.hour)
    
    def get_cache_key(self) -> str:
        """生成缓存键"""
        data = f"{self.year}-{self.month}-{self.day}-{self.hour}-{self.gender}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def to_display_string(self) -> str:
        """生成显示字符串"""
        return f"{self.year}年{self.month}月{self.day}日{self.hour}时 {self.gender}命"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "year": self.year,
            "month": self.month,
            "day": self.day,
            "hour": self.hour,
            "gender": self.gender
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'BirthInfo':
        """从字典创建实例"""
        return cls(
            year=data["year"],
            month=data["month"],
            day=data["day"],
            hour=data["hour"],
            gender=data["gender"]
        )
    
    @classmethod
    def from_string(cls, birth_str: str, gender: str) -> 'BirthInfo':
        """从字符串解析生辰信息
        
        支持格式:
        - "1988年6月1日11时"
        - "1988-6-1 11"
        - "1988/6/1 11:00"
        """
        import re
        
        # 中文格式: 1988年6月1日11时
        pattern1 = r'(\d{4})年(\d{1,2})月(\d{1,2})日(\d{1,2})时'
        match = re.search(pattern1, birth_str)
        if match:
            year, month, day, hour = map(int, match.groups())
            return cls(year, month, day, hour, gender)
        
        # 数字格式: 1988-6-1 11
        pattern2 = r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})\s+(\d{1,2})'
        match = re.search(pattern2, birth_str)
        if match:
            year, month, day, hour = map(int, match.groups())
            return cls(year, month, day, hour, gender)
        
        # 时间格式: 1988/6/1 11:00
        pattern3 = r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})\s+(\d{1,2}):\d{2}'
        match = re.search(pattern3, birth_str)
        if match:
            year, month, day, hour = map(int, match.groups())
            return cls(year, month, day, hour, gender)
        
        raise ValueError(f"无法解析生辰信息: {birth_str}")

# 时辰对照表
HOUR_NAMES = {
    23: "子时", 0: "子时", 1: "丑时", 2: "丑时",
    3: "寅时", 4: "寅时", 5: "卯时", 6: "卯时",
    7: "辰时", 8: "辰时", 9: "巳时", 10: "巳时",
    11: "午时", 12: "午时", 13: "未时", 14: "未时",
    15: "申时", 16: "申时", 17: "酉时", 18: "酉时",
    19: "戌时", 20: "戌时", 21: "亥时", 22: "亥时"
}

def get_hour_name(hour: int) -> str:
    """获取时辰名称"""
    return HOUR_NAMES.get(hour, "未知时辰")
