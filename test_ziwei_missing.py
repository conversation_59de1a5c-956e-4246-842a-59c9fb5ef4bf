#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紫薇斗数缺失问题
"""

def test_ziwei_algorithm():
    """测试紫薇斗数算法"""
    print("🔮 测试紫薇斗数算法")
    print("=" * 40)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        print("✅ 紫薇算法实例创建成功")
        
        # 测试数据
        test_data = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试数据: {test_data}")
        
        # 调用算法
        result = ziwei_calc.calculate_chart(
            test_data["year"],
            test_data["month"],
            test_data["day"],
            test_data["hour"],
            test_data["gender"]
        )
        
        print(f"📋 算法返回结果类型: {type(result)}")
        print(f"📋 算法返回结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        # 检查是否有错误
        if "error" in result:
            print(f"❌ 紫薇算法返回错误: {result['error']}")
            return False
        else:
            print("✅ 紫薇算法调用成功")
            
            # 检查关键数据
            if "palaces" in result:
                print(f"✅ 包含宫位数据: {len(result['palaces'])}个宫位")
            else:
                print("⚠️ 缺少宫位数据")
            
            if "birth_info" in result:
                print("✅ 包含出生信息")
            else:
                print("⚠️ 缺少出生信息")
            
            return True
        
    except Exception as e:
        print(f"❌ 紫薇算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bazi_algorithm():
    """测试八字算法"""
    print("\n📜 测试八字算法")
    print("=" * 40)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        # 创建算法实例
        bazi_calc = RealBaziCalculator()
        print("✅ 八字算法实例创建成功")
        
        # 测试数据
        test_data = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试数据: {test_data}")
        
        # 调用算法
        result = bazi_calc.calculate_bazi(
            test_data["year"],
            test_data["month"],
            test_data["day"],
            test_data["hour"],
            0,  # 分钟
            test_data["gender"]
        )
        
        print(f"📋 算法返回结果类型: {type(result)}")
        print(f"📋 算法返回结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        # 检查是否有错误
        if "error" in result:
            print(f"❌ 八字算法返回错误: {result['error']}")
            return False
        else:
            print("✅ 八字算法调用成功")
            return True
        
    except Exception as e:
        print(f"❌ 八字算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fortune_engine():
    """测试算命引擎"""
    print("\n🤖 测试算命引擎")
    print("=" * 40)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()
        
        # 定义简单的API函数
        def test_chat_api(prompt: str) -> str:
            return "测试响应"
        
        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=test_chat_api
        )
        
        print("✅ 算命引擎创建成功")
        
        # 测试数据
        birth_info = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试出生信息: {birth_info}")
        
        # 测试综合分析
        print("🌟 测试综合分析...")
        result = engine._call_comprehensive_api(birth_info)
        
        print(f"📋 综合分析结果: {result}")
        
        # 检查结果
        if result.get("success"):
            results = result.get("results", {})
            print(f"✅ 综合分析成功，包含: {list(results.keys())}")
            
            if "ziwei" in results:
                print("✅ 包含紫薇斗数结果")
            else:
                print("❌ 缺少紫薇斗数结果")
            
            if "bazi" in results:
                print("✅ 包含八字算命结果")
            else:
                print("❌ 缺少八字算命结果")
            
            return "ziwei" in results and "bazi" in results
        else:
            print(f"❌ 综合分析失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 算命引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_problem():
    """分析问题原因"""
    print("\n🔍 问题分析")
    print("=" * 30)
    
    print("📊 **从调试信息分析:**")
    print("  第一次测试: 有紫薇斗数数据")
    print("  第二次测试: 只有八字数据，缺少紫薇斗数")
    print()
    
    print("🤔 **可能的原因:**")
    print("  1. 紫薇算法调用失败但没有显示错误")
    print("  2. 算法实例初始化问题")
    print("  3. 数据格式或参数问题")
    print("  4. 算法内部异常被捕获")
    print("  5. 条件判断逻辑问题")
    print()
    
    print("🔧 **解决方案:**")
    print("  1. 添加详细的调试信息")
    print("  2. 单独测试紫薇算法")
    print("  3. 检查算法实例状态")
    print("  4. 验证参数传递")
    print("  5. 增强错误处理")

def main():
    """主测试函数"""
    print("🔮 紫薇斗数缺失问题诊断")
    print("=" * 60)
    
    # 测试1: 紫薇算法
    ziwei_success = test_ziwei_algorithm()
    
    # 测试2: 八字算法
    bazi_success = test_bazi_algorithm()
    
    # 测试3: 算命引擎
    engine_success = test_fortune_engine()
    
    # 分析问题
    analyze_problem()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 诊断结果:")
    print(f"  紫薇算法: {'✅ 正常' if ziwei_success else '❌ 异常'}")
    print(f"  八字算法: {'✅ 正常' if bazi_success else '❌ 异常'}")
    print(f"  算命引擎: {'✅ 正常' if engine_success else '❌ 异常'}")
    
    if ziwei_success and bazi_success and engine_success:
        print("\n🎊 所有组件都正常！")
        print("问题可能在于:")
        print("  - 特定输入条件下的算法失败")
        print("  - 网络或API调用问题")
        print("  - 缓存或状态问题")
        print("\n建议:")
        print("  - 重启web端服务")
        print("  - 清理缓存文件")
        print("  - 检查最新的调试信息")
    else:
        print("\n⚠️ 发现组件异常！")
        print("需要修复相应的组件")

if __name__ == "__main__":
    main()
