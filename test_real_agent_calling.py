#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真正的Agent调用
"""

import asyncio
import sys
import logging
from datetime import datetime
sys.path.append('.')

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_real_agent_calling():
    """测试真正的Agent间调用"""
    print("🔍 测试真正的Agent间调用")
    print("=" * 80)
    print("目标: 验证主控Agent确实调用了计算Agent")
    print("=" * 80)
    
    try:
        # 导入组件
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建Agent
        master_agent = MasterCustomerAgent("test_master")
        calculator_agent = FortuneCalculatorAgent("test_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print("✅ Agent系统初始化完成")
        print(f"   主控Agent: {master_agent.agent_id}")
        print(f"   计算Agent: {calculator_agent.agent_id}")
        
        # 测试完整的算命流程
        session_id = "test_real_calling"
        
        print(f"\n🧪 测试完整算命流程")
        print("-" * 60)
        
        # 第1步：提供完整信息，触发计算
        print(f"\n1️⃣ 提供完整生辰信息")
        user_message = "我想看紫薇斗数，1988年6月1日午时出生，男性"
        print(f"👤 用户: {user_message}")
        
        # 获取初始统计
        initial_master_stats = master_agent.get_stats()
        initial_calc_stats = calculator_agent.get_stats()
        
        print(f"📊 调用前统计:")
        print(f"   主控Agent处理消息: {initial_master_stats['messages_processed']}")
        print(f"   计算Agent处理消息: {initial_calc_stats['messages_processed']}")
        
        # 发送请求
        print(f"\n⏳ 发送请求...")
        result = await coordinator.handle_user_message(session_id, user_message)
        
        # 获取调用后统计
        final_master_stats = master_agent.get_stats()
        final_calc_stats = calculator_agent.get_stats()
        
        print(f"\n📊 调用后统计:")
        print(f"   主控Agent处理消息: {final_master_stats['messages_processed']}")
        print(f"   计算Agent处理消息: {final_calc_stats['messages_processed']}")
        
        # 检查是否真正调用了计算Agent
        master_increase = final_master_stats['messages_processed'] - initial_master_stats['messages_processed']
        calc_increase = final_calc_stats['messages_processed'] - initial_calc_stats['messages_processed']
        
        print(f"\n🔍 调用分析:")
        print(f"   主控Agent消息增加: {master_increase}")
        print(f"   计算Agent消息增加: {calc_increase}")
        
        # 显示响应
        print(f"\n🤖 AI响应:")
        response = result.get('response', '')
        print(f"   成功: {result.get('success', False)}")
        print(f"   阶段: {result.get('stage', 'unknown')}")
        print(f"   响应长度: {len(response)} 字符")
        print(f"   响应预览: {response[:200]}...")
        
        # 检查是否包含计算结果
        if "计算完成" in response or "分析已完成" in response:
            print(f"✅ 响应包含计算完成标识")
        else:
            print(f"❌ 响应不包含计算完成标识")
        
        # 验证Agent调用
        print(f"\n🎯 Agent调用验证:")
        if calc_increase > 0:
            print(f"✅ 计算Agent被成功调用 (消息数增加 {calc_increase})")
            print(f"✅ 主控Agent → 计算Agent 调用链正常工作")
        else:
            print(f"❌ 计算Agent未被调用 (消息数无变化)")
            print(f"❌ 主控Agent → 计算Agent 调用链存在问题")
        
        # 测试后续问答
        print(f"\n2️⃣ 测试基于结果的问答")
        followup_message = "我的事业运势如何？"
        print(f"👤 用户: {followup_message}")
        
        followup_result = await coordinator.handle_user_message(session_id, followup_message)
        followup_response = followup_result.get('response', '')
        
        print(f"🤖 AI响应: {followup_response[:150]}...")
        
        # 最终评估
        print(f"\n🏁 最终评估")
        print("=" * 60)
        
        success_indicators = []
        
        # 检查1: 计算Agent被调用
        if calc_increase > 0:
            success_indicators.append("✅ 计算Agent被调用")
        else:
            success_indicators.append("❌ 计算Agent未被调用")
        
        # 检查2: 响应包含计算结果
        if "分析" in response and len(response) > 500:
            success_indicators.append("✅ 响应包含详细分析")
        else:
            success_indicators.append("❌ 响应缺少详细分析")
        
        # 检查3: 系统整体成功
        if result.get('success'):
            success_indicators.append("✅ 系统处理成功")
        else:
            success_indicators.append("❌ 系统处理失败")
        
        # 检查4: 后续问答正常
        if followup_result.get('success'):
            success_indicators.append("✅ 后续问答正常")
        else:
            success_indicators.append("❌ 后续问答异常")
        
        print(f"评估结果:")
        for indicator in success_indicators:
            print(f"   {indicator}")
        
        success_count = sum(1 for indicator in success_indicators if "✅" in indicator)
        total_count = len(success_indicators)
        success_rate = success_count / total_count
        
        print(f"\n📊 成功率: {success_count}/{total_count} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.75:
            print(f"🎉 真正的Agent调用测试通过！")
            print(f"✅ 主控Agent成功调用计算Agent")
            print(f"✅ 双Agent协作正常工作")
            return True
        else:
            print(f"⚠️  Agent调用存在问题，需要进一步调试")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_agent_registry():
    """调试Agent注册表"""
    print(f"\n🔧 调试Agent注册表")
    print("-" * 40)
    
    try:
        from core.agents.base_agent import agent_registry
        
        # 检查注册的Agent
        all_agents = agent_registry.agents
        print(f"注册的Agent总数: {len(all_agents)}")
        
        for agent_id, agent in all_agents.items():
            print(f"   {agent_id}: {type(agent).__name__}")
        
        # 检查特定类型的Agent
        calc_agents = agent_registry.get_agents_by_type("FortuneCalculatorAgent")
        print(f"\n计算Agent数量: {len(calc_agents)}")
        for agent in calc_agents:
            print(f"   {agent.agent_id}: {agent.name}")
        
        master_agents = agent_registry.get_agents_by_type("MasterCustomerAgent")
        print(f"\n主控Agent数量: {len(master_agents)}")
        for agent in master_agents:
            print(f"   {agent.agent_id}: {agent.name}")
        
        return len(calc_agents) > 0 and len(master_agents) > 0
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 真正的Agent调用验证测试")
    print("=" * 80)
    print("目标: 确认主控Agent真正调用了计算Agent")
    print("=" * 80)
    
    # 调试Agent注册表
    registry_ok = await debug_agent_registry()
    
    if not registry_ok:
        print("❌ Agent注册表存在问题")
        return False
    
    # 测试真正的Agent调用
    calling_ok = await test_real_agent_calling()
    
    # 最终结论
    print(f"\n" + "=" * 80)
    print("🏁 最终结论")
    print("=" * 80)
    
    if calling_ok:
        print("🎉 真正的Agent调用验证成功！")
        print("\n💪 确认的功能:")
        print("   ✅ 主控Agent成功调用计算Agent")
        print("   ✅ 计算Agent正确处理请求")
        print("   ✅ 双Agent协作链路正常")
        print("   ✅ 用户获得真正的算命结果")
        
        print("\n🚀 现在您可以在Web界面体验真正的双Agent协作！")
        print("   访问: http://localhost:8504")
        
    else:
        print("💥 Agent调用存在问题")
        print("\n🔧 可能的问题:")
        print("   - Agent注册不正确")
        print("   - 消息传递机制有问题")
        print("   - 计算Agent处理逻辑错误")
        print("   - 异步调用同步问题")
    
    return calling_ok

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
