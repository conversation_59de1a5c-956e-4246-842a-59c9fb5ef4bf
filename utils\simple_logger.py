#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化日志工具
"""

import logging
import sys
from pathlib import Path
from datetime import datetime

class SimpleLogger:
    """简化的日志管理器"""
    
    def __init__(self, name: str = "ziwei_refactor"):
        self.name = name
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志"""
        if self.logger.handlers:
            return  # 已经设置过了
        
        self.logger.setLevel(logging.DEBUG)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_dir / "app.log", encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str):
        """调试日志"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """错误日志"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """严重错误日志"""
        self.logger.critical(message)

# 全局日志实例
_logger = SimpleLogger()

def get_logger() -> SimpleLogger:
    """获取日志实例"""
    return _logger

# 便捷函数
def debug(message: str):
    """调试日志"""
    _logger.debug(message)

def info(message: str):
    """信息日志"""
    _logger.info(message)

def warning(message: str):
    """警告日志"""
    _logger.warning(message)

def error(message: str):
    """错误日志"""
    _logger.error(message)

def critical(message: str):
    """严重错误日志"""
    _logger.critical(message)
