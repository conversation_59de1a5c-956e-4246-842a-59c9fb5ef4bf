#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试浏览器显示问题
"""

import requests
import json
import re

def debug_browser_display():
    """调试浏览器显示"""
    base_url = "http://localhost:5000"
    
    print("=== 调试浏览器显示问题 ===")
    
    try:
        # 获取最新的六爻记录
        response = requests.get(f"{base_url}/api/liuyao/list", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('records'):
                record = result['records'][0]
                liuyao_id = record['liuyao_id']
                
                # 获取详细结果
                detail_response = requests.get(f"{base_url}/api/liuyao/{liuyao_id}")
                if detail_response.status_code == 200:
                    detail_result = detail_response.json()
                    if detail_result.get('success'):
                        analysis_content = detail_result.get('analysis_content', '')
                        
                        # 提取详细表格
                        detail_match = re.search(r'【详细卦象】(.*?)【硬币投掷详情】', analysis_content, re.DOTALL)
                        if detail_match:
                            detail_content = detail_match.group(1).strip()
                            
                            print("原始表格内容:")
                            print("=" * 80)
                            print(detail_content)
                            print("=" * 80)
                            
                            # 检查每一行的正则匹配
                            lines = [line for line in detail_content.split('\n') if line.strip() and '─' not in line and '┌' not in line and '└' not in line]
                            
                            print("\n正则匹配测试:")
                            for i, line in enumerate(lines):
                                if '├' in line or '┼' in line:
                                    continue
                                    
                                if '爻位' in line and '卦象' in line:
                                    print(f"表头行 {i}: {repr(line)}")
                                    headerMatch = re.match(r'│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│', line)
                                    if headerMatch:
                                        headers = [headerMatch.group(j).strip() for j in range(1, 8)]
                                        print(f"  解析结果: {headers}")
                                    else:
                                        print(f"  ❌ 表头解析失败")
                                    continue
                                
                                if '│' in line and ('第' in line or '爻' in line):
                                    print(f"数据行 {i}: {repr(line)}")
                                    
                                    # 测试当前的正则表达式
                                    tableMatch = re.match(r'│([^│]+)│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│', line)
                                    
                                    if tableMatch:
                                        cells = [tableMatch.group(j).strip() for j in range(1, 8)]
                                        print(f"  ✅ 解析成功: {cells}")
                                        print(f"    爻位: '{cells[0]}'")
                                        print(f"    卦象: '{cells[1]}'")
                                        print(f"    地支: '{cells[2]}'")
                                        print(f"    五行: '{cells[3]}'")
                                        print(f"    六亲: '{cells[4]}'")
                                        print(f"    六神: '{cells[5]}'")
                                        print(f"    动静: '{cells[6]}'")
                                    else:
                                        print(f"  ❌ 解析失败")
                                        
                                        # 尝试其他正则表达式
                                        alt_match = re.search(r'│([^│]+)│([^│]+)│([^│]+)│([^│]+)│([^│]+)│([^│]+)│([^│]+)│', line)
                                        if alt_match:
                                            alt_cells = [alt_match.group(j).strip() for j in range(1, 8)]
                                            print(f"  🔄 备用解析: {alt_cells}")
                                        else:
                                            print(f"  ❌ 备用解析也失败")
                                            
                                            # 手动分割测试
                                            parts = line.split('│')
                                            print(f"  🔧 手动分割: {[p.strip() for p in parts if p.strip()]}")
                                    print()
                        else:
                            print("❌ 未找到详细卦象表格")
                    else:
                        print(f"❌ 获取详细结果失败: {detail_result.get('error')}")
                else:
                    print(f"❌ 详细结果请求失败: {detail_response.status_code}")
            else:
                print("❌ 没有找到六爻记录")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_browser_display()
