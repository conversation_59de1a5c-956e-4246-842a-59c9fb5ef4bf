#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试验证失败的原因
"""

import asyncio
import json

async def debug_validation_failure():
    """调试验证失败的原因"""
    print("🔍 调试财富分析验证失败的原因")
    print("=" * 60)
    
    try:
        # 1. 重新生成财富分析
        print("1️⃣ 重新生成财富分析")
        from core.analysis.analysis_controller import AnalysisController
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        # 获取测试数据
        birth_info = {
            "year": "1988",
            "month": "6", 
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        fusion_engine = ZiweiBaziFusionEngine()
        calculator_agent = FortuneCalculatorAgent()
        hour_number = calculator_agent._convert_hour_to_number("午时")
        
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=1988, month=6, day=1, hour=hour_number, gender="男"
        )
        
        if not raw_data.get("success"):
            print(f"❌ 融合分析失败: {raw_data.get('error')}")
            return
        
        print("✅ 融合分析数据获取成功")
        
        # 2. 执行财富分析
        print("\n2️⃣ 执行财富分析")
        controller = AnalysisController()
        
        analysis_result = await controller.execute_single_analysis(
            raw_data=raw_data,
            birth_info=birth_info,
            analysis_type="wealth_fortune"
        )
        
        print(f"📊 分析结果:")
        print(f"  成功: {analysis_result.get('success')}")
        print(f"  错误: {analysis_result.get('error')}")
        
        # 3. 详细检查验证报告
        print("\n3️⃣ 详细验证报告")
        validation_report = analysis_result.get("validation_report", {})
        
        if validation_report:
            print(f"📋 验证详情:")
            print(f"  总体有效: {validation_report.get('overall_valid')}")
            print(f"  评分: {validation_report.get('score', 0):.1f}/100")
            print(f"  长度检查: {'✅' if validation_report.get('length_check') else '❌'}")
            print(f"  结构检查: {'✅' if validation_report.get('structure_check') else '❌'}")
            print(f"  准确性检查: {'✅' if validation_report.get('accuracy_check') else '❌'}")
            print(f"  专业性检查: {'✅' if validation_report.get('professional_check') else '❌'}")
            print(f"  数据一致性检查: {'✅' if validation_report.get('data_consistency_check') else '❌'}")
            
            # 显示错误详情
            errors = validation_report.get('errors', [])
            if errors:
                print(f"\n❌ 具体错误:")
                for i, error in enumerate(errors, 1):
                    print(f"    {i}. {error}")
            
            warnings = validation_report.get('warnings', [])
            if warnings:
                print(f"\n⚠️ 警告信息:")
                for i, warning in enumerate(warnings, 1):
                    print(f"    {i}. {warning}")
        
        # 4. 检查生成的内容
        print("\n4️⃣ 检查生成的内容")
        content = analysis_result.get("content", "")
        
        if content:
            print(f"📝 内容信息:")
            print(f"  长度: {len(content)} 字符")
            print(f"  前200字符: {content[:200]}...")
            
            # 检查关键词
            key_terms = ["财帛宫", "天府", "财运", "理财", "投资"]
            found_terms = [term for term in key_terms if term in content]
            print(f"  包含关键词: {found_terms}")
            
            # 检查是否有错误信息
            forbidden_phrases = [
                "天机星坐命", "太阴化科同宫", "太阳天梁同宫于午",
                "紫微七杀同宫", "廉贞贪狼同宫"
            ]
            found_errors = [phrase for phrase in forbidden_phrases if phrase in content]
            if found_errors:
                print(f"  ❌ 发现错误信息: {found_errors}")
            else:
                print(f"  ✅ 未发现错误信息")
        
        # 5. 手动验证关键检查项
        print("\n5️⃣ 手动验证关键检查项")
        
        if content:
            # 长度检查
            length_ok = len(content) >= 500
            print(f"  长度检查: {'✅' if length_ok else '❌'} ({len(content)} >= 500)")
            
            # 结构检查
            required_sections = ["基础", "发展", "建议", "注意"]
            found_sections = [section for section in required_sections if section in content]
            structure_ok = len(found_sections) >= 3
            print(f"  结构检查: {'✅' if structure_ok else '❌'} (找到{len(found_sections)}/4个必要部分: {found_sections})")
            
            # 准确性检查
            has_forbidden = any(phrase in content for phrase in forbidden_phrases)
            accuracy_ok = not has_forbidden
            print(f"  准确性检查: {'✅' if accuracy_ok else '❌'} (无禁止短语)")
            
            # 数据一致性检查
            has_caibo = "财帛宫" in content
            has_tianfu = "天府" in content
            consistency_ok = has_caibo and has_tianfu
            print(f"  数据一致性检查: {'✅' if consistency_ok else '❌'} (财帛宫: {has_caibo}, 天府: {has_tianfu})")
            
            # 总体判断
            critical_checks = [length_ok, accuracy_ok, consistency_ok]
            overall_ok = all(critical_checks)
            print(f"  总体判断: {'✅' if overall_ok else '❌'} (关键检查项全部通过)")
        
        # 6. 保存调试信息
        print("\n6️⃣ 保存调试信息")
        debug_info = {
            "analysis_result": analysis_result,
            "content_length": len(content) if content else 0,
            "validation_details": validation_report
        }
        
        with open("debug_wealth_validation.json", "w", encoding="utf-8") as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        
        if content:
            with open("debug_wealth_content.txt", "w", encoding="utf-8") as f:
                f.write("财富分析调试内容:\n")
                f.write("=" * 50 + "\n\n")
                f.write(content)
        
        print("💾 调试信息已保存:")
        print("  - debug_wealth_validation.json")
        print("  - debug_wealth_content.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(debug_validation_failure())
