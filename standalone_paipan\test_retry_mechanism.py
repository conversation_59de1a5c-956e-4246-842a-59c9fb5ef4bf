#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM服务重试机制
"""

from llm_service import LLMService

def test_retry_mechanism():
    """测试重试机制"""
    print("🧪 测试LLM服务重试机制")
    
    # 初始化LLM服务，使用错误的模型名称来测试重试
    llm = LLMService(model_name="wrong-model-name")
    
    # 测试简单对话
    messages = [
        {"role": "system", "content": "你是一个测试助手。"},
        {"role": "user", "content": "测试重试机制"}
    ]
    
    print("🔄 发送测试消息（使用错误模型名称）...")
    response = llm.chat_completion(messages, temperature=0.3, max_tokens=100)
    
    if response:
        print(f"✅ 意外成功（不应该发生）")
        print(f"📝 回复内容: {response}")
        return False
    else:
        print("✅ 重试机制测试成功：正确处理了错误模型")
        return True

def test_normal_operation():
    """测试正常操作"""
    print("\n🧪 测试正常操作")
    
    # 使用正确的模型
    llm = LLMService()
    
    messages = [
        {"role": "system", "content": "你是一个测试助手。"},
        {"role": "user", "content": "正常测试"}
    ]
    
    print("🔄 发送正常测试消息...")
    response = llm.chat_completion(messages, temperature=0.3, max_tokens=100)
    
    if response:
        print(f"✅ 正常操作测试成功")
        print(f"📝 回复内容: {response}")
        return True
    else:
        print("❌ 正常操作测试失败")
        return False

if __name__ == "__main__":
    print("=" * 50)
    test1_result = test_retry_mechanism()
    test2_result = test_normal_operation()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"重试机制测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"正常操作测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
