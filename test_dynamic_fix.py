#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态初始化修复
"""

def test_dynamic_liuyao_fix():
    """测试动态六爻算法修复"""
    print("🔧 测试动态六爻算法修复")
    print("=" * 50)
    
    try:
        import sys
        sys.path.append('core')
        
        from fortune_engine import FortuneEngine
        
        # 模拟API服务器的情况：六爻算法为None
        def mock_api(prompt):
            if "算命类型" in prompt:
                return "liuyao"
            elif "问题类型" in prompt:
                return "fortune"
            else:
                return "null"
        
        # 创建FortuneEngine，故意不传入六爻算法
        print("1. 创建FortuneEngine（六爻算法为None）...")
        engine = FortuneEngine(
            ziwei_calc=None,
            bazi_calc=None,
            liuyao_calc=None,  # 故意设为None，模拟API服务器的情况
            chat_api_func=mock_api
        )
        print("✅ FortuneEngine创建成功")
        
        # 检查六爻算法状态
        print("2. 检查六爻算法初始状态...")
        print(f"engine.liuyao_calc: {engine.liuyao_calc}")
        
        # 测试六爻算法调用（应该触发动态初始化）
        print("3. 测试六爻算法调用（触发动态初始化）...")
        birth_info = {
            "year": 2025,
            "month": 6,
            "day": 19,
            "hour": 9
        }
        
        result = engine._call_liuyao_api(birth_info)
        print(f"六爻算法调用结果: {result}")
        
        if result.get("success"):
            print("✅ 动态初始化成功，六爻算法正常工作")
            
            # 检查六爻算法是否已经初始化
            print("4. 检查六爻算法最终状态...")
            print(f"engine.liuyao_calc: {engine.liuyao_calc}")
            print(f"类型: {type(engine.liuyao_calc)}")
            
            return True
        else:
            print(f"❌ 动态初始化失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_user_flow():
    """测试完整用户流程"""
    print("\n🚀 测试完整用户流程")
    print("=" * 50)
    
    try:
        import sys
        sys.path.append('core')
        
        from fortune_engine import FortuneEngine
        
        def mock_api(prompt):
            if "算命类型" in prompt:
                return "liuyao"
            elif "问题类型" in prompt:
                return "fortune"
            else:
                return "null"
        
        # 创建FortuneEngine，六爻算法为None
        engine = FortuneEngine(
            ziwei_calc=None,
            bazi_calc=None,
            liuyao_calc=None,  # 模拟API服务器的情况
            chat_api_func=mock_api
        )
        
        # 测试完整用户请求
        print("5. 测试完整用户请求...")
        user_question = "帮我算一卦，看看今年运势，现在已经6月份了"
        
        result = engine.process_user_request(user_question)
        print(f"用户请求处理结果: {result}")
        
        if result.get("success"):
            print("✅ 完整用户流程成功")
            print(f"分析内容长度: {len(result.get('message', ''))}")
            return True
        else:
            print(f"❌ 完整用户流程失败: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 动态六爻算法修复测试")
    print("=" * 60)
    
    # 测试1: 动态初始化修复
    dynamic_success = test_dynamic_liuyao_fix()
    
    # 测试2: 完整用户流程
    if dynamic_success:
        flow_success = test_complete_user_flow()
    else:
        flow_success = False
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 动态六爻算法修复测试结果:")
    print(f"  动态初始化修复: {'✅ 成功' if dynamic_success else '❌ 失败'}")
    print(f"  完整用户流程: {'✅ 成功' if flow_success else '❌ 失败'}")
    
    if dynamic_success and flow_success:
        print("\n🎊 动态修复完全成功！")
        print("\n🎯 **修复原理:**")
        print("  1. 检测到六爻算法未初始化")
        print("  2. 动态导入六爻算法模块")
        print("  3. 创建六爻算法实例")
        print("  4. 测试算法功能")
        print("  5. 继续正常处理")
        print()
        print("💡 **解决的问题:**")
        print("  - API服务器启动时六爻算法初始化失败")
        print("  - FortuneEngine接收到None实例")
        print("  - 用户请求时动态修复")
        print("  - 无需重启API服务器")
        print()
        print("🚀 **现在的用户体验:**")
        print("  1. 用户: '帮我算一卦，看看今年运势'")
        print("  2. 系统: 检测到六爻算法未初始化")
        print("  3. 系统: 动态初始化六爻算法")
        print("  4. 系统: 正常进行六爻算卦")
        print("  5. 用户: 获得完整的六爻分析")
        
        print("\n🎉 **用户问题完全解决！**")
        print("**现在无需重启API服务器，直接测试用户问题即可！**")
    else:
        print("\n⚠️ 部分测试失败，需要进一步排查")

if __name__ == "__main__":
    main()
