#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户沟通专家Agent - 专注于用户交互和结果解释
"""

import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_agent import BaseAgent, AgentMessage, AgentResponse, MessageType
from core.nlu.llm_client import LLMClient

logger = logging.getLogger(__name__)

class CustomerServiceAgent(BaseAgent):
    """客户沟通专家 - 负责用户交互、信息收集、结果解释"""

    def __init__(self, agent_id: str = "customer_service_001"):
        super().__init__(
            agent_id=agent_id,
            name="客户沟通专家",
            description="专注于用户交互、信息收集和算命结果解释的AI助手"
        )

        # 初始化LLM客户端
        self.llm_client = LLMClient()

        # 沟通模板
        self.templates = {
            "greeting": "您好！我是您的专属算命师，很高兴为您服务。",
            "info_collection": "为了给您准确的算命分析，请告诉我您的出生年月日时和性别。",
            "processing": "我正在为您{calculation_type}，这需要几秒钟时间，请稍等...",
            "result_intro": "您的{calculation_type}已经完成，让我为您详细解读：",
            "error_fallback": "抱歉，计算过程中遇到了一些问题，让我用其他方式为您分析。",
            "goodbye": "感谢您的咨询，祝您好运！如有其他问题，随时可以问我。"
        }

        # 算命类型映射
        self.calculation_types = {
            "ziwei": "紫薇斗数排盘",
            "bazi": "八字算命分析",
            "liuyao": "六爻占卜"
        }

        logger.info(f"客户沟通专家 {self.agent_id} 初始化完成")

    async def process_message(self, message: AgentMessage) -> AgentResponse:
        """处理消息"""
        try:
            content = message.content
            action = content.get("action", "chat")

            if action == "chat" or message.message_type == MessageType.COMMUNICATION_REQUEST:
                return await self._handle_communication_request(content)
            elif action == "explain_calculation_result":
                return await self._handle_result_explanation(content)
            elif action == "collect_birth_info":
                return await self._handle_info_collection(content)
            else:
                return await self._handle_general_chat(content)

        except Exception as e:
            logger.error(f"沟通Agent处理消息失败: {e}")
            return AgentResponse(
                success=False,
                data={},
                error=str(e)
            )

    async def _handle_communication_request(self, content: Dict[str, Any]) -> AgentResponse:
        """处理沟通请求"""
        user_message = content.get("user_message", "")
        session_id = content.get("session_id", "")
        task_type = content.get("task_type", "chat")
        user_context = content.get("user_context", {})

        # 获取会话状态
        session_state = self.get_session_state(session_id)

        try:
            if task_type == "fortune_telling":
                response = await self._handle_fortune_telling_communication(
                    user_message, session_state, user_context
                )
            elif task_type == "chat":
                response = await self._handle_general_chat_communication(
                    user_message, session_state, user_context
                )
            else:
                response = await self._handle_question_communication(
                    user_message, session_state, user_context
                )

            # 更新会话状态
            self.set_session_state(session_id, session_state)

            return AgentResponse(
                success=True,
                data={"response": response, "session_state": session_state}
            )

        except Exception as e:
            logger.error(f"处理沟通请求失败: {e}")
            return AgentResponse(
                success=False,
                data={"response": "抱歉，我现在无法处理您的请求，请稍后再试。"},
                error=str(e)
            )

    async def _handle_fortune_telling_communication(self, user_message: str,
                                                   session_state: Dict[str, Any],
                                                   user_context: Dict[str, Any]) -> str:
        """处理算命相关沟通"""
        # 分析用户意图和提取信息
        intent_result = await self._analyze_fortune_intent(user_message)

        calculation_type = intent_result.get("calculation_type", "紫薇斗数")
        birth_info = intent_result.get("birth_info", {})

        # 更新会话状态
        session_state.update({
            "calculation_type": calculation_type,
            "birth_info": birth_info,
            "last_intent": "fortune_telling"
        })

        # 检查信息完整性
        if self._is_birth_info_complete(birth_info):
            # 信息完整，告知用户正在计算
            calc_type_name = self.calculation_types.get(calculation_type, calculation_type)
            response = self.templates["processing"].format(calculation_type=calc_type_name)

            # 添加一些专业说明
            if calculation_type == "ziwei":
                response += "\n\n紫薇斗数是中国古代占星学的重要分支，通过您的出生时间排出命盘，分析您的性格特质、事业财运、感情婚姻等各个方面。"
            elif calculation_type == "bazi":
                response += "\n\n八字算命以您的出生年月日时为基础，分析五行生克制化，推断您的命运走势和人生轨迹。"
            elif calculation_type == "liuyao":
                response += "\n\n六爻占卜是古老的预测方法，通过卦象变化来解答您的疑问。"

        else:
            # 信息不完整，引导用户提供
            response = await self._generate_info_collection_response(birth_info, calculation_type)

        return response

    async def _handle_general_chat_communication(self, user_message: str,
                                               session_state: Dict[str, Any],
                                               user_context: Dict[str, Any]) -> str:
        """处理一般聊天沟通"""
        # 使用LLM生成自然回复
        prompt = f"""
你是一个专业的算命师客服，用户说："{user_message}"

请用自然、亲切的语言回复用户。要求：
1. 语言温和友善
2. 体现专业性
3. 适当引导用户了解算命服务
4. 回复简洁明了

回复："""

        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=200)
            return response.strip() if response else "您好！我是您的专属算命师，很高兴为您服务。请问您想了解什么呢？"
        except Exception as e:
            logger.error(f"LLM生成回复失败: {e}")
            return "您好！我是您的专属算命师，很高兴为您服务。请问您想了解什么呢？"

    async def _handle_question_communication(self, user_message: str,
                                           session_state: Dict[str, Any],
                                           user_context: Dict[str, Any]) -> str:
        """处理问答沟通"""
        # 分析问题类型
        if any(keyword in user_message.lower() for keyword in ["算命", "紫薇", "八字", "六爻"]):
            # 算命相关问题
            prompt = f"""
用户问："{user_message}"

请作为专业算命师回答这个关于算命的问题。要求：
1. 回答准确专业
2. 语言通俗易懂
3. 适当介绍相关算命方法
4. 鼓励用户尝试算命服务

回答："""
        else:
            # 一般问题
            prompt = f"""
用户问："{user_message}"

请作为友善的AI助手回答用户的问题。要求：
1. 回答有帮助
2. 语言自然友好
3. 如果不确定答案，诚实说明
4. 适当引导回到算命话题

回答："""

        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=300)
            return response.strip() if response else "这是一个很好的问题。作为算命师，我更擅长为您分析命理运势。您想了解算命相关的内容吗？"
        except Exception as e:
            logger.error(f"LLM回答问题失败: {e}")
            return "这是一个很好的问题。作为算命师，我更擅长为您分析命理运势。您想了解算命相关的内容吗？"

    async def _handle_result_explanation(self, content: Dict[str, Any]) -> AgentResponse:
        """处理算命结果解释"""
        calculation_data = content.get("calculation_data", {})
        user_message = content.get("user_message", "")
        session_id = content.get("session_id", "")

        try:
            # 生成结果解释
            explanation = await self._generate_result_explanation(calculation_data, user_message)

            return AgentResponse(
                success=True,
                data={"response": explanation}
            )

        except Exception as e:
            logger.error(f"解释算命结果失败: {e}")
            return AgentResponse(
                success=False,
                data={"response": "算命分析已完成，但解释生成遇到问题。"},
                error=str(e)
            )

    async def _analyze_fortune_intent(self, user_message: str) -> Dict[str, Any]:
        """分析算命意图和提取信息"""
        # 确定算命类型
        calculation_type = "ziwei"  # 默认紫薇斗数
        if "八字" in user_message:
            calculation_type = "bazi"
        elif "六爻" in user_message or "占卜" in user_message:
            calculation_type = "liuyao"
        elif "紫薇" in user_message or "斗数" in user_message:
            calculation_type = "ziwei"

        # 提取生辰信息
        birth_info = self._extract_birth_info(user_message)

        return {
            "calculation_type": calculation_type,
            "birth_info": birth_info
        }

    def _extract_birth_info(self, text: str) -> Dict[str, Any]:
        """从文本中提取生辰信息"""
        birth_info = {}

        # 提取年份
        year_pattern = r'(\d{4})[年\s]'
        year_match = re.search(year_pattern, text)
        if year_match:
            birth_info["year"] = year_match.group(1)

        # 提取月份
        month_pattern = r'(\d{1,2})[月\s]'
        month_match = re.search(month_pattern, text)
        if month_match:
            birth_info["month"] = month_match.group(1)

        # 提取日期
        day_pattern = r'(\d{1,2})[日号\s]'
        day_match = re.search(day_pattern, text)
        if day_match:
            birth_info["day"] = day_match.group(1)

        # 提取时辰
        time_patterns = [
            r'(子时|丑时|寅时|卯时|辰时|巳时|午时|未时|申时|酉时|戌时|亥时)',
            r'(\d{1,2})[点时:]',
            r'(上午|下午|早上|晚上|中午)'
        ]

        for pattern in time_patterns:
            time_match = re.search(pattern, text)
            if time_match:
                birth_info["hour"] = time_match.group(1)
                break

        # 提取性别
        if "男" in text:
            birth_info["gender"] = "男"
        elif "女" in text:
            birth_info["gender"] = "女"

        return birth_info

    def _is_birth_info_complete(self, birth_info: Dict[str, Any]) -> bool:
        """检查生辰信息是否完整"""
        required_fields = ["year", "month", "day", "hour", "gender"]
        return all(field in birth_info and birth_info[field] for field in required_fields)

    async def _generate_info_collection_response(self, birth_info: Dict[str, Any],
                                               calculation_type: str) -> str:
        """生成信息收集回复"""
        missing_info = []

        if not birth_info.get("year"):
            missing_info.append("出生年份")
        if not birth_info.get("month"):
            missing_info.append("出生月份")
        if not birth_info.get("day"):
            missing_info.append("出生日期")
        if not birth_info.get("hour"):
            missing_info.append("出生时辰")
        if not birth_info.get("gender"):
            missing_info.append("性别")

        calc_type_name = self.calculation_types.get(calculation_type, calculation_type)

        if missing_info:
            response = f"好的，我来为您进行{calc_type_name}。\n\n"
            response += f"还需要您提供：{', '.join(missing_info)}。\n\n"
            response += "请按照这个格式告诉我：YYYY年MM月DD日 时辰 性别\n"
            response += "例如：1988年6月1日 午时 男"
        else:
            response = f"信息收集完成，我马上为您进行{calc_type_name}分析。"

        return response

    async def _generate_result_explanation(self, calculation_data: Dict[str, Any],
                                         user_message: str) -> str:
        """生成算命结果解释"""
        # 构建解释提示词
        prompt = f"""
作为专业算命师，请根据以下计算结果为用户提供详细解读：

用户请求：{user_message}

计算结果：{calculation_data}

请提供：
1. 简要概述
2. 性格特点分析
3. 事业运势
4. 财运分析
5. 感情婚姻
6. 健康状况
7. 近期运势提醒
8. 实用建议

要求：
- 语言专业但通俗易懂
- 内容积极正面
- 提供具体指导
- 篇幅适中（800-1200字）

解读："""

        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=1500)
            return response.strip() if response else "您的算命分析已经完成。根据您的命盘显示，您是一个有潜力的人，建议保持积极心态，把握机会，相信未来会更好。"
        except Exception as e:
            logger.error(f"生成结果解释失败: {e}")
            return "您的算命分析已经完成。根据您的命盘显示，您是一个有潜力的人，建议保持积极心态，把握机会，相信未来会更好。"
