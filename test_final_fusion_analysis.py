#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试紫薇+八字融合分析
"""

import asyncio

async def test_final_fusion_analysis():
    """最终测试紫薇+八字融合分析"""
    print("🎯 最终测试紫薇+八字融合分析")
    print("=" * 60)
    
    try:
        # 1. 获取融合分析数据
        print("1️⃣ 获取融合分析数据")
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        print("✅ 融合分析数据获取成功")
        
        # 2. 执行完整分析
        print("\n2️⃣ 执行完整分析")
        from core.analysis.analysis_controller import AnalysisController
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        controller = AnalysisController()
        analysis_result = await controller.execute_single_analysis(
            raw_data=result,
            birth_info=birth_info,
            analysis_type="personality_destiny"
        )
        
        success = analysis_result.get("success", False)
        print(f"  分析执行: {'✅' if success else '❌'}")
        
        if not success:
            error = analysis_result.get("error", "未知错误")
            print(f"  错误信息: {error}")
            
            # 即使失败，也检查是否有内容
            content = analysis_result.get("content", "")
            if content:
                print(f"  备用内容长度: {len(content)} 字符")
        else:
            content = analysis_result.get("content", "")
            print(f"  分析内容长度: {len(content)} 字符")
            
            # 检查内容质量
            if content:
                checks = {
                    "包含紫薇信息": any(keyword in content for keyword in ["紫薇", "命宫", "天相", "亥"]),
                    "包含八字信息": any(keyword in content for keyword in ["八字", "四柱", "日主", "丁火"]),
                    "包含五行信息": "五行" in content,
                    "包含融合分析": any(keyword in content for keyword in ["印证", "融合", "综合", "结合"]),
                    "内容充实": len(content) > 1000
                }
                
                print(f"  内容质量检查:")
                for check_name, result in checks.items():
                    print(f"    {check_name}: {'✅' if result else '❌'}")
                
                # 保存分析内容
                with open("final_fusion_analysis_result.txt", "w", encoding="utf-8") as f:
                    f.write("最终紫薇+八字融合分析结果:\n")
                    f.write("=" * 60 + "\n\n")
                    f.write(f"分析成功: {success}\n")
                    f.write(f"内容长度: {len(content)} 字符\n")
                    f.write(f"分析类型: personality_destiny\n")
                    f.write("\n" + "="*60 + "\n")
                    f.write("分析内容:\n")
                    f.write("="*60 + "\n\n")
                    f.write(content)
                
                print(f"  💾 分析结果已保存到 final_fusion_analysis_result.txt")
        
        # 3. 验证报告
        validation_report = analysis_result.get("validation_report", {})
        if validation_report:
            print(f"\n3️⃣ 验证报告")
            print(f"  总体有效: {'✅' if validation_report.get('overall_valid') else '❌'}")
            print(f"  评分: {validation_report.get('score', 0):.1f}/100")
            
            errors = validation_report.get('errors', [])
            if errors:
                print(f"  错误数量: {len(errors)}")
                for i, error in enumerate(errors[:3], 1):  # 只显示前3个错误
                    print(f"    {i}. {error}")
        
        # 4. 总结
        print(f"\n4️⃣ 测试总结")
        
        if success:
            print("🎉 紫薇+八字融合分析系统完全正常！")
            print("✅ 数据提取成功")
            print("✅ 分析执行成功")
            print("✅ 内容生成成功")
            print("✅ 真正实现了紫薇+八字相互印证")
            return True
        else:
            print("⚠️ 分析执行有问题，但系统基本可用")
            print("✅ 数据提取成功")
            print("❌ 分析执行失败")
            print("✅ 备用内容可用")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_final_fusion_analysis())
