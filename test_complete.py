#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试双Agent系统 - 等待后台分析完成
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_complete_flow():
    print('🧪 完整测试双Agent系统...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = 'test_session_complete'
        
        # 第一步：提供完整信息
        print('\n📝 第一步：提供完整生辰信息')
        result1 = await coordinator.handle_user_message(
            session_id, 
            '我是1988年6月1日午时出生的男命，想算命'
        )
        
        print(f'✅ 第一步响应: {result1.get("success")}')
        print(f'阶段: {result1.get("stage")}')
        
        # 第二步：立即询问财运
        print('\n💬 第二步：立即询问财运（后台分析中）')
        result2 = await coordinator.handle_user_message(
            session_id, 
            '我想看看财运'
        )
        
        print(f'✅ 第二步响应: {result2.get("success")}')
        print(f'响应类型: {"分析进行中" if "正在后台进行中" in result2.get("response", "") else "其他"}')
        
        # 等待后台分析完成
        print('\n⏳ 等待后台分析完成...')
        max_wait = 300  # 最多等待5分钟
        check_interval = 10  # 每10秒检查一次
        
        for i in range(0, max_wait, check_interval):
            time.sleep(check_interval)
            
            # 检查会话状态
            session_state = master_agent.get_session_state(session_id)
            stage = session_state.get("stage", "unknown")
            result_id = session_state.get("result_id")
            
            print(f'⏰ {i+check_interval}秒: 阶段={stage}, result_id={result_id is not None}')
            
            # 如果有result_id，说明分析完成
            if result_id:
                print(f'🎉 后台分析完成！result_id: {result_id[:8]}...')
                break
            
            # 如果分析失败
            if stage == "analysis_failed":
                print('❌ 后台分析失败')
                break
        else:
            print('⏰ 等待超时，分析可能还在进行中')
            return
        
        # 第三步：分析完成后再次询问财运
        if result_id:
            print('\n💰 第三步：分析完成后询问财运')
            result3 = await coordinator.handle_user_message(
                session_id, 
                '现在我的财运如何？'
            )
            
            print(f'✅ 第三步响应: {result3.get("success")}')
            response = result3.get("response", "")
            print(f'响应长度: {len(response)}字')
            print(f'响应预览: {response[:200]}...')
            
            # 检查是否基于真实分析
            if "命盘" in response or "紫薇" in response or "八字" in response:
                print('🎯 ✅ 基于真实分析回答')
            else:
                print('🎯 ❌ 可能不是基于真实分析')
        
        # 第四步：测试其他问题
        print('\n🏥 第四步：询问健康')
        result4 = await coordinator.handle_user_message(
            session_id, 
            '我的健康状况如何？'
        )
        
        print(f'✅ 第四步响应: {result4.get("success")}')
        health_response = result4.get("response", "")
        print(f'健康回答预览: {health_response[:150]}...')
        
        # 最终总结
        print('\n📊 测试总结:')
        final_state = master_agent.get_session_state(session_id)
        print(f'  最终阶段: {final_state.get("stage")}')
        print(f'  有result_id: {final_state.get("result_id") is not None}')
        print(f'  生辰信息: {final_state.get("birth_info")}')
        
        # 检查分析进度
        if final_state.get("result_id"):
            progress = master_agent.get_analysis_progress(final_state["result_id"])
            print(f'  分析进度: {progress}')
        
        print('\n🎉 完整测试流程结束')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_complete_flow())
