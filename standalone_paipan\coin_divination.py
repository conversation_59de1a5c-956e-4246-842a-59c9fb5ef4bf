#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬币起卦模块
实现传统的六爻硬币起卦方法
"""

import random
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple


class CoinDivination:
    """硬币起卦类"""
    
    def __init__(self):
        """初始化硬币起卦器"""
        self.trigram_names = {
            0: "坤", 1: "震", 2: "坎", 3: "兑",
            4: "艮", 5: "离", 6: "巽", 7: "乾"
        }

        # 完整的64卦对照表
        self.hexagram_names = {
            (7, 7): "乾为天", (0, 0): "坤为地", (2, 7): "水天需", (7, 2): "天水讼",
            (0, 2): "地水师", (2, 0): "水地比", (7, 6): "天风小畜", (6, 7): "风天大畜",
            (0, 5): "地火明夷", (5, 0): "火地晋", (1, 0): "雷地豫", (0, 1): "地雷复",
            (7, 1): "天雷无妄", (1, 7): "雷天大壮", (5, 4): "火山旅", (4, 5): "山火贲",
            (2, 2): "坎为水", (5, 5): "离为火", (1, 1): "震为雷", (3, 3): "兑为泽",
            (6, 6): "巽为风", (4, 4): "艮为山", (7, 0): "天地否", (0, 7): "地天泰",
            (5, 2): "火水未济", (2, 5): "水火既济", (1, 4): "雷山小过", (4, 1): "山雷颐",
            (6, 3): "风泽中孚", (3, 6): "泽风大过", (2, 4): "水山蹇", (4, 2): "山水蒙",
            (3, 5): "泽火革", (5, 3): "火泽睽", (1, 2): "雷水解", (2, 1): "水雷屯",
            (6, 4): "风山渐", (4, 6): "山风蛊", (3, 7): "泽天夬", (7, 3): "天泽履",
            (0, 6): "地风升", (6, 0): "风地观", (5, 1): "火雷噬嗑", (1, 5): "雷火丰",
            (2, 3): "水泽节", (3, 2): "泽水困", (4, 7): "山天大畜", (7, 4): "天山遁",
            (0, 1): "地雷复", (1, 0): "雷地豫", (5, 6): "火风鼎", (6, 5): "风火家人",
            (2, 6): "水风井", (6, 2): "风水涣", (3, 4): "泽山咸", (4, 3): "山泽损",
            (7, 5): "天火同人", (5, 7): "火天大有", (0, 3): "地泽临", (3, 0): "泽地萃",
            (1, 6): "雷风恒", (6, 1): "风雷益", (2, 7): "水天需", (7, 2): "天水讼",
            (4, 0): "山地剥", (0, 4): "地山谦", (5, 2): "火水未济", (2, 5): "水火既济"
        }

        # 六神对应表（按甲子日起青龙）
        self.six_spirits = ["青龙", "朱雀", "勾陈", "腾蛇", "白虎", "玄武"]

        # 天干对应的六神起始位置
        self.spirit_start = {
            "甲": 0, "乙": 1, "丙": 2, "丁": 3, "戊": 4, "己": 5,
            "庚": 0, "辛": 1, "壬": 2, "癸": 3
        }

        # 地支对应的五行
        self.dizhi_wuxing = {
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", "巳": "火",
            "午": "火", "未": "土", "申": "金", "酉": "金", "戌": "土", "亥": "水"
        }

        # 八卦对应的五行
        self.bagua_wuxing = {
            "乾": "金", "兑": "金", "离": "火", "震": "木",
            "巽": "木", "坎": "水", "艮": "土", "坤": "土"
        }

        # 地支序列
        self.dizhi_sequence = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

    def throw_coins(self, manual_results: List[str] = None) -> List[Dict[str, Any]]:
        """
        投掷硬币6次，生成六爻
        
        Args:
            manual_results: 手动指定的硬币结果，格式如 ["正正反", "反正正", ...]
            
        Returns:
            六次投掷的结果列表
        """
        coin_throws = []
        
        for i in range(6):
            if manual_results and i < len(manual_results):
                # 使用手动指定的结果
                throw_result = manual_results[i]
                coins = list(throw_result)
            else:
                # 随机投掷3枚硬币
                coins = [random.choice(["正", "反"]) for _ in range(3)]
                throw_result = "".join(coins)
            
            # 计算爻的性质
            zheng_count = coins.count("正")  # 正面数量
            fan_count = coins.count("反")    # 反面数量
            
            # 根据传统规则确定爻的性质
            if zheng_count == 3:
                # 三个正面 = 老阳 = 动爻 = 阳爻变阴爻
                yao_type = "老阳"
                yao_symbol = "▅▅▅▅▅"
                is_moving = True
                changed_symbol = "▅▅  ▅▅"
            elif zheng_count == 2:
                # 两正一反 = 少阴 = 静爻 = 阴爻
                yao_type = "少阴"
                yao_symbol = "▅▅  ▅▅"
                is_moving = False
                changed_symbol = "▅▅  ▅▅"
            elif zheng_count == 1:
                # 一正两反 = 少阳 = 静爻 = 阳爻
                yao_type = "少阳"
                yao_symbol = "▅▅▅▅▅"
                is_moving = False
                changed_symbol = "▅▅▅▅▅"
            else:
                # 三个反面 = 老阴 = 动爻 = 阴爻变阳爻
                yao_type = "老阴"
                yao_symbol = "▅▅  ▅▅"
                is_moving = True
                changed_symbol = "▅▅▅▅▅"
            
            coin_throws.append({
                "throw_number": i + 1,
                "coins": coins,
                "throw_result": throw_result,
                "zheng_count": zheng_count,
                "fan_count": fan_count,
                "yao_type": yao_type,
                "yao_symbol": yao_symbol,
                "is_moving": is_moving,
                "changed_symbol": changed_symbol
            })
        
        return coin_throws

    def build_hexagram(self, coin_throws: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        根据硬币投掷结果构建卦象
        
        Args:
            coin_throws: 硬币投掷结果
            
        Returns:
            完整的卦象数据
        """
        # 构建主卦（从下到上：初爻到上爻）
        main_hexagram_lines = []
        changed_hexagram_lines = []
        moving_lines = []
        
        for i, throw in enumerate(coin_throws):
            main_hexagram_lines.append(throw["yao_symbol"])
            changed_hexagram_lines.append(throw["changed_symbol"])
            
            if throw["is_moving"]:
                moving_lines.append(i + 1)  # 爻位从1开始
        
        # 计算上卦和下卦
        upper_trigram_value = self._calculate_trigram_value(main_hexagram_lines[3:6])
        lower_trigram_value = self._calculate_trigram_value(main_hexagram_lines[0:3])
        
        upper_trigram_name = self.trigram_names.get(upper_trigram_value, "未知")
        lower_trigram_name = self.trigram_names.get(lower_trigram_value, "未知")
        
        # 获取卦名
        main_hexagram_name = self.hexagram_names.get(
            (upper_trigram_value, lower_trigram_value),
            f"{upper_trigram_name}{lower_trigram_name}"
        )

        # 如果有动爻，计算变卦
        changed_hexagram_name = main_hexagram_name
        if moving_lines:
            changed_upper_value = self._calculate_trigram_value(changed_hexagram_lines[3:6])
            changed_lower_value = self._calculate_trigram_value(changed_hexagram_lines[0:3])
            changed_hexagram_name = self.hexagram_names.get(
                (changed_upper_value, changed_lower_value),
                f"{self.trigram_names.get(changed_upper_value, '未知')}{self.trigram_names.get(changed_lower_value, '未知')}"
            )

        # 配置六亲和六神
        hexagram_details = self._configure_liuqin_liushen(
            main_hexagram_name, upper_trigram_value, lower_trigram_value, main_hexagram_lines
        )
        
        return {
            "main_hexagram": {
                "name": main_hexagram_name,
                "upper_trigram": upper_trigram_name,
                "lower_trigram": lower_trigram_name,
                "lines": main_hexagram_lines
            },
            "changed_hexagram": {
                "name": changed_hexagram_name,
                "lines": changed_hexagram_lines
            },
            "moving_lines": moving_lines,
            "coin_throws": coin_throws,
            "divination_time": datetime.now().isoformat(),
            "hexagram_details": hexagram_details
        }

    def _calculate_trigram_value(self, lines: List[str]) -> int:
        """
        计算三爻的八卦数值

        Args:
            lines: 三个爻的符号列表

        Returns:
            八卦数值 (0-7)
        """
        value = 0
        for i, line in enumerate(lines):
            if "▅▅▅▅▅" in line:  # 阳爻
                value += 2 ** i
        return value

    def _configure_liuqin_liushen(self, hexagram_name: str, upper_trigram: int, lower_trigram: int, lines: List[str]) -> dict:
        """
        配置六亲和六神

        Args:
            hexagram_name: 卦名
            upper_trigram: 上卦数值
            lower_trigram: 下卦数值
            lines: 爻线列表

        Returns:
            包含六亲六神信息的字典
        """
        from datetime import datetime

        # 获取当前时间的天干地支（简化处理，实际应该用准确的干支历）
        now = datetime.now()

        # 简化的天干地支计算（仅用于演示）
        tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        current_tiangan = tiangan[now.year % 10]

        # 配置六神（从初爻到上爻）
        spirit_start_index = self.spirit_start.get(current_tiangan, 0)
        six_spirits_config = []
        for i in range(6):
            spirit_index = (spirit_start_index + i) % 6
            six_spirits_config.append(self.six_spirits[spirit_index])

        # 配置地支（简化处理）
        dizhi_start = now.hour % 12  # 简化的地支起始
        six_dizhi = []
        for i in range(6):
            dizhi_index = (dizhi_start + i) % 12
            six_dizhi.append(self.dizhi_sequence[dizhi_index])

        # 获取卦宫五行（以下卦为准）
        lower_trigram_name = self.trigram_names[lower_trigram]
        gua_gong_wuxing = self.bagua_wuxing.get(lower_trigram_name, "土")

        # 配置六亲（根据地支五行与卦宫五行的关系）
        six_relatives_config = []
        for dizhi in six_dizhi:
            dizhi_wuxing = self.dizhi_wuxing[dizhi]
            relative = self._get_liuqin_by_wuxing(gua_gong_wuxing, dizhi_wuxing)
            six_relatives_config.append(relative)

        # 构建详细信息
        yao_details = []
        for i in range(6):
            yao_info = {
                "position": f"第{i+1}爻",
                "symbol": lines[i],
                "dizhi": six_dizhi[i],
                "wuxing": self.dizhi_wuxing[six_dizhi[i]],
                "liuqin": six_relatives_config[i],
                "liushen": six_spirits_config[i],
                "is_yang": "▅▅▅▅▅" in lines[i]
            }
            yao_details.append(yao_info)

        return {
            "gua_gong": lower_trigram_name,
            "gua_gong_wuxing": gua_gong_wuxing,
            "yao_details": yao_details,
            "six_spirits": six_spirits_config,
            "six_relatives": six_relatives_config,
            "six_dizhi": six_dizhi
        }

    def _get_liuqin_by_wuxing(self, gua_gong_wuxing: str, yao_wuxing: str) -> str:
        """
        根据五行关系确定六亲

        Args:
            gua_gong_wuxing: 卦宫五行
            yao_wuxing: 爻的五行

        Returns:
            六亲名称
        """
        # 五行生克关系
        wuxing_sheng = {
            "木": "火", "火": "土", "土": "金", "金": "水", "水": "木"
        }
        wuxing_ke = {
            "木": "土", "火": "金", "土": "水", "金": "木", "水": "火"
        }

        if yao_wuxing == gua_gong_wuxing:
            return "兄弟"
        elif wuxing_sheng.get(gua_gong_wuxing) == yao_wuxing:
            return "子孙"
        elif wuxing_ke.get(gua_gong_wuxing) == yao_wuxing:
            return "妻财"
        elif wuxing_ke.get(yao_wuxing) == gua_gong_wuxing:
            return "官鬼"
        elif wuxing_sheng.get(yao_wuxing) == gua_gong_wuxing:
            return "父母"
        else:
            return "兄弟"  # 默认

    def format_hexagram_display(self, hexagram_data: Dict[str, Any]) -> str:
        """
        格式化卦象显示

        Args:
            hexagram_data: 卦象数据

        Returns:
            格式化的卦象字符串
        """
        main_hex = hexagram_data["main_hexagram"]
        changed_hex = hexagram_data["changed_hexagram"]
        moving_lines = hexagram_data["moving_lines"]
        hexagram_details = hexagram_data.get("hexagram_details", {})

        output = f"""
═══════════════════════════════════════
            硬币起卦结果
═══════════════════════════════════════
起卦时间: {hexagram_data['divination_time'][:19].replace('T', ' ')}

【卦象信息】
主卦: {main_hex['name']} ({main_hex['upper_trigram']}上{main_hex['lower_trigram']}下)
"""

        if moving_lines:
            output += f"变卦: {changed_hex['name']}\n"
            output += f"动爻: 第{', '.join(map(str, moving_lines))}爻\n"

        if hexagram_details:
            output += f"卦宫: {hexagram_details.get('gua_gong', '未知')}宫 ({hexagram_details.get('gua_gong_wuxing', '未知')})\n"

        output += "\n【详细卦象】\n"
        output += "爻位    卦象    地支  五行  六亲    六神    动静\n"
        output += "─────────────────────────────────────────\n"

        # 显示卦象（从上到下）
        yao_details = hexagram_details.get("yao_details", [])
        for i in range(5, -1, -1):  # 从上爻到初爻
            line_num = i + 1
            main_line = main_hex["lines"][i]

            # 获取详细信息
            if i < len(yao_details):
                yao_info = yao_details[i]
                dizhi = yao_info.get("dizhi", "？")
                wuxing = yao_info.get("wuxing", "？")
                liuqin = yao_info.get("liuqin", "？")
                liushen = yao_info.get("liushen", "？")
            else:
                dizhi = wuxing = liuqin = liushen = "？"

            # 动静标记
            if line_num in moving_lines:
                dong_jing = "动"
            else:
                dong_jing = "静"

            output += f"第{line_num}爻   {main_line}   {dizhi}   {wuxing}   {liuqin}    {liushen}    {dong_jing}\n"

        # 显示硬币投掷详情
        output += "\n【硬币投掷详情】\n"
        for throw in hexagram_data["coin_throws"]:
            output += f"第{throw['throw_number']}次: {throw['throw_result']} → {throw['yao_type']}\n"

        output += "\n═══════════════════════════════════════\n"

        return output

    def divine_with_coins(self, question: str, manual_results: List[str] = None) -> Dict[str, Any]:
        """
        完整的硬币起卦流程
        
        Args:
            question: 占卜问题
            manual_results: 手动指定的硬币结果
            
        Returns:
            完整的占卜结果
        """
        try:
            # 投掷硬币
            coin_throws = self.throw_coins(manual_results)
            
            # 构建卦象
            hexagram_data = self.build_hexagram(coin_throws)
            
            # 格式化显示
            formatted_output = self.format_hexagram_display(hexagram_data)
            
            return {
                "success": True,
                "method": "硬币起卦",
                "question": question,
                "divination_data": {
                    "coin_throws": coin_throws,
                    "manual_results": manual_results
                },
                "hexagram_data": hexagram_data,
                "formatted_output": formatted_output,
                "divination_type": "六爻算卦"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"硬币起卦失败: {str(e)}"
            }


def test_coin_divination():
    """测试硬币起卦功能"""
    print("=== 硬币起卦测试 ===")
    
    divination = CoinDivination()
    
    # 测试1: 随机硬币起卦
    print("\n1. 随机硬币起卦测试:")
    result1 = divination.divine_with_coins("我的事业发展如何？")
    if result1["success"]:
        print("起卦成功!")
        print(result1["formatted_output"])
    else:
        print(f"起卦失败: {result1['error']}")
    
    # 测试2: 手动指定硬币结果
    print("\n2. 手动硬币结果测试:")
    manual_coins = ["正正反", "反正正", "正反反", "反反反", "正正正", "反正反"]
    result2 = divination.divine_with_coins("感情运势如何？", manual_coins)
    if result2["success"]:
        print("起卦成功!")
        print(result2["formatted_output"])
    else:
        print(f"起卦失败: {result2['error']}")


if __name__ == "__main__":
    test_coin_divination()
