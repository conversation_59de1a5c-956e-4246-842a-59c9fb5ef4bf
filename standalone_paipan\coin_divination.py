#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬币起卦模块
实现传统的六爻硬币起卦方法
"""

import random
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple


class CoinDivination:
    """硬币起卦类"""
    
    def __init__(self):
        """初始化硬币起卦器"""
        self.trigram_names = {
            0: "坤", 1: "震", 2: "坎", 3: "兑", 
            4: "艮", 5: "离", 6: "巽", 7: "乾"
        }
        
        self.hexagram_names = {
            # 这里只列出部分常用卦名，实际应用中需要完整的64卦对照表
            (7, 7): "乾为天", (0, 0): "坤为地", (2, 7): "水天需", (7, 2): "天水讼",
            (0, 2): "地水师", (2, 0): "水地比", (7, 6): "天风小畜", (6, 7): "风天大畜",
            (0, 5): "地火明夷", (5, 0): "火地晋", (1, 0): "雷地豫", (0, 1): "地雷复",
            (7, 1): "天雷无妄", (1, 7): "雷天大壮", (5, 0): "火地晋", (0, 5): "地火明夷"
        }
        
        # 六神对应表
        self.six_spirits = ["青龙", "朱雀", "勾陈", "腾蛇", "白虎", "玄武"]
        
        # 六亲对应表
        self.six_relatives = ["父母", "兄弟", "子孙", "妻财", "官鬼"]

    def throw_coins(self, manual_results: List[str] = None) -> List[Dict[str, Any]]:
        """
        投掷硬币6次，生成六爻
        
        Args:
            manual_results: 手动指定的硬币结果，格式如 ["正正反", "反正正", ...]
            
        Returns:
            六次投掷的结果列表
        """
        coin_throws = []
        
        for i in range(6):
            if manual_results and i < len(manual_results):
                # 使用手动指定的结果
                throw_result = manual_results[i]
                coins = list(throw_result)
            else:
                # 随机投掷3枚硬币
                coins = [random.choice(["正", "反"]) for _ in range(3)]
                throw_result = "".join(coins)
            
            # 计算爻的性质
            zheng_count = coins.count("正")  # 正面数量
            fan_count = coins.count("反")    # 反面数量
            
            # 根据传统规则确定爻的性质
            if zheng_count == 3:
                # 三个正面 = 老阳 = 动爻 = 阳爻变阴爻
                yao_type = "老阳"
                yao_symbol = "▅▅▅▅▅"
                is_moving = True
                changed_symbol = "▅▅  ▅▅"
            elif zheng_count == 2:
                # 两正一反 = 少阴 = 静爻 = 阴爻
                yao_type = "少阴"
                yao_symbol = "▅▅  ▅▅"
                is_moving = False
                changed_symbol = "▅▅  ▅▅"
            elif zheng_count == 1:
                # 一正两反 = 少阳 = 静爻 = 阳爻
                yao_type = "少阳"
                yao_symbol = "▅▅▅▅▅"
                is_moving = False
                changed_symbol = "▅▅▅▅▅"
            else:
                # 三个反面 = 老阴 = 动爻 = 阴爻变阳爻
                yao_type = "老阴"
                yao_symbol = "▅▅  ▅▅"
                is_moving = True
                changed_symbol = "▅▅▅▅▅"
            
            coin_throws.append({
                "throw_number": i + 1,
                "coins": coins,
                "throw_result": throw_result,
                "zheng_count": zheng_count,
                "fan_count": fan_count,
                "yao_type": yao_type,
                "yao_symbol": yao_symbol,
                "is_moving": is_moving,
                "changed_symbol": changed_symbol
            })
        
        return coin_throws

    def build_hexagram(self, coin_throws: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        根据硬币投掷结果构建卦象
        
        Args:
            coin_throws: 硬币投掷结果
            
        Returns:
            完整的卦象数据
        """
        # 构建主卦（从下到上：初爻到上爻）
        main_hexagram_lines = []
        changed_hexagram_lines = []
        moving_lines = []
        
        for i, throw in enumerate(coin_throws):
            main_hexagram_lines.append(throw["yao_symbol"])
            changed_hexagram_lines.append(throw["changed_symbol"])
            
            if throw["is_moving"]:
                moving_lines.append(i + 1)  # 爻位从1开始
        
        # 计算上卦和下卦
        upper_trigram_value = self._calculate_trigram_value(main_hexagram_lines[3:6])
        lower_trigram_value = self._calculate_trigram_value(main_hexagram_lines[0:3])
        
        upper_trigram_name = self.trigram_names.get(upper_trigram_value, "未知")
        lower_trigram_name = self.trigram_names.get(lower_trigram_value, "未知")
        
        # 获取卦名
        main_hexagram_name = self.hexagram_names.get(
            (upper_trigram_value, lower_trigram_value), 
            f"{upper_trigram_name}{lower_trigram_name}"
        )
        
        # 如果有动爻，计算变卦
        changed_hexagram_name = main_hexagram_name
        if moving_lines:
            changed_upper_value = self._calculate_trigram_value(changed_hexagram_lines[3:6])
            changed_lower_value = self._calculate_trigram_value(changed_hexagram_lines[0:3])
            changed_hexagram_name = self.hexagram_names.get(
                (changed_upper_value, changed_lower_value),
                f"{self.trigram_names.get(changed_upper_value, '未知')}{self.trigram_names.get(changed_lower_value, '未知')}"
            )
        
        return {
            "main_hexagram": {
                "name": main_hexagram_name,
                "upper_trigram": upper_trigram_name,
                "lower_trigram": lower_trigram_name,
                "lines": main_hexagram_lines
            },
            "changed_hexagram": {
                "name": changed_hexagram_name,
                "lines": changed_hexagram_lines
            },
            "moving_lines": moving_lines,
            "coin_throws": coin_throws,
            "divination_time": datetime.now().isoformat()
        }

    def _calculate_trigram_value(self, lines: List[str]) -> int:
        """
        计算三爻的八卦数值
        
        Args:
            lines: 三个爻的符号列表
            
        Returns:
            八卦数值 (0-7)
        """
        value = 0
        for i, line in enumerate(lines):
            if "▅▅▅▅▅" in line:  # 阳爻
                value += 2 ** i
        return value

    def format_hexagram_display(self, hexagram_data: Dict[str, Any]) -> str:
        """
        格式化卦象显示
        
        Args:
            hexagram_data: 卦象数据
            
        Returns:
            格式化的卦象字符串
        """
        main_hex = hexagram_data["main_hexagram"]
        changed_hex = hexagram_data["changed_hexagram"]
        moving_lines = hexagram_data["moving_lines"]
        
        output = f"""
硬币起卦结果
起卦时间: {hexagram_data['divination_time'][:19].replace('T', ' ')}

主卦: {main_hex['name']} ({main_hex['upper_trigram']}上{main_hex['lower_trigram']}下)
"""
        
        if moving_lines:
            output += f"变卦: {changed_hex['name']}\n"
            output += f"动爻: 第{', '.join(map(str, moving_lines))}爻\n"
        
        output += "\n卦象:\n"
        
        # 显示卦象（从上到下）
        for i in range(5, -1, -1):  # 从上爻到初爻
            line_num = i + 1
            main_line = main_hex["lines"][i]
            
            # 添加爻位标识
            if line_num in moving_lines:
                marker = "○"  # 动爻标记
            else:
                marker = " "
            
            output += f"{main_line} {marker} 第{line_num}爻\n"
        
        # 显示硬币投掷详情
        output += "\n硬币投掷详情:\n"
        for throw in hexagram_data["coin_throws"]:
            output += f"第{throw['throw_number']}次: {throw['throw_result']} → {throw['yao_type']}\n"
        
        return output

    def divine_with_coins(self, question: str, manual_results: List[str] = None) -> Dict[str, Any]:
        """
        完整的硬币起卦流程
        
        Args:
            question: 占卜问题
            manual_results: 手动指定的硬币结果
            
        Returns:
            完整的占卜结果
        """
        try:
            # 投掷硬币
            coin_throws = self.throw_coins(manual_results)
            
            # 构建卦象
            hexagram_data = self.build_hexagram(coin_throws)
            
            # 格式化显示
            formatted_output = self.format_hexagram_display(hexagram_data)
            
            return {
                "success": True,
                "method": "硬币起卦",
                "question": question,
                "divination_data": {
                    "coin_throws": coin_throws,
                    "manual_results": manual_results
                },
                "hexagram_data": hexagram_data,
                "formatted_output": formatted_output,
                "divination_type": "六爻算卦"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"硬币起卦失败: {str(e)}"
            }


def test_coin_divination():
    """测试硬币起卦功能"""
    print("=== 硬币起卦测试 ===")
    
    divination = CoinDivination()
    
    # 测试1: 随机硬币起卦
    print("\n1. 随机硬币起卦测试:")
    result1 = divination.divine_with_coins("我的事业发展如何？")
    if result1["success"]:
        print("起卦成功!")
        print(result1["formatted_output"])
    else:
        print(f"起卦失败: {result1['error']}")
    
    # 测试2: 手动指定硬币结果
    print("\n2. 手动硬币结果测试:")
    manual_coins = ["正正反", "反正正", "正反反", "反反反", "正正正", "反正反"]
    result2 = divination.divine_with_coins("感情运势如何？", manual_coins)
    if result2["success"]:
        print("起卦成功!")
        print(result2["formatted_output"])
    else:
        print(f"起卦失败: {result2['error']}")


if __name__ == "__main__":
    test_coin_divination()
