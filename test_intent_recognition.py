#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
意图识别测试 - 阶段2.1测试
"""

import sys
import os
sys.path.append('.')

def test_llm_client():
    """测试LLM客户端"""
    print("🤖 测试LLM客户端")
    print("-" * 40)
    
    try:
        from core.nlu.llm_client import LLMClient
        
        # 创建LLM客户端
        client = LLMClient()
        print("✅ LLM客户端创建成功")
        print(f"   模型: {client.model_name}")
        print(f"   API密钥: {client.api_key[:20]}...")
        
        # 测试简单对话
        print("\n🔍 测试简单LLM调用")
        messages = [
            {"role": "user", "content": "你好，请简单回复一句话"}
        ]
        
        response = client.chat_completion(messages, temperature=0.7, max_tokens=100)
        if response:
            print(f"✅ LLM响应成功: {response[:100]}...")
            return True
        else:
            print("❌ LLM响应失败")
            return False
            
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intent_recognition_basic():
    """测试基础意图识别"""
    print("\n🧠 测试基础意图识别")
    print("-" * 40)
    
    try:
        from core.nlu.llm_client import LLMClient
        
        client = LLMClient()
        
        # 测试意图识别
        test_cases = [
            {
                "message": "我想看紫薇斗数",
                "expected": "ziwei"
            },
            {
                "message": "帮我算八字",
                "expected": "bazi"
            },
            {
                "message": "我要六爻占卜",
                "expected": "liuyao"
            },
            {
                "message": "我1988年6月1日午时出生，男，想算命",
                "expected": "general"
            },
            {
                "message": "你好",
                "expected": "chat"
            }
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            message = test_case["message"]
            expected = test_case["expected"]
            
            print(f"\n测试 {i}/{total_count}: {message}")
            
            # 调用意图识别
            result = client.intent_recognition(message)
            
            if result:
                actual = result.get("intent", "unknown")
                confidence = result.get("confidence", 0.0)
                reasoning = result.get("reasoning", "无")
                
                print(f"预期: {expected}")
                print(f"实际: {actual} (置信度: {confidence:.2f})")
                print(f"理由: {reasoning}")
                
                # 检查实体提取
                entities = result.get("entities", {})
                if entities:
                    print(f"实体: {entities}")
                
                # 判断是否正确（允许一定的灵活性）
                if actual == expected or (expected == "general" and actual in ["ziwei", "bazi", "liuyao"]):
                    print("✅ 识别正确")
                    success_count += 1
                else:
                    print("❌ 识别错误")
            else:
                print("❌ 识别失败")
        
        print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
        accuracy = success_count / total_count * 100
        print(f"准确率: {accuracy:.1f}%")
        
        return success_count >= total_count * 0.6  # 60%以上通过率算成功
        
    except Exception as e:
        print(f"❌ 意图识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_extraction():
    """测试实体提取"""
    print("\n🔍 测试实体提取")
    print("-" * 40)
    
    try:
        from core.nlu.llm_client import LLMClient
        
        client = LLMClient()
        
        # 测试包含出生信息的消息
        test_message = "我1988年6月1日午时出生，男，想看紫薇斗数"
        
        print(f"测试消息: {test_message}")
        
        result = client.intent_recognition(test_message)
        
        if result:
            print(f"意图: {result.get('intent')}")
            print(f"置信度: {result.get('confidence', 0):.2f}")
            
            entities = result.get("entities", {})
            if entities:
                print("✅ 实体提取成功:")
                for key, value in entities.items():
                    print(f"  {key}: {value}")
                
                # 检查关键实体
                expected_entities = ["birth_year", "birth_month", "birth_day", "gender"]
                found_entities = [key for key in expected_entities if key in entities and entities[key]]
                
                print(f"找到关键实体: {len(found_entities)}/{len(expected_entities)}")
                
                return len(found_entities) >= 2  # 至少找到2个关键实体
            else:
                print("⚠️ 未提取到实体")
                return False
        else:
            print("❌ 意图识别失败")
            return False
            
    except Exception as e:
        print(f"❌ 实体提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_session():
    """测试与会话管理的集成"""
    print("\n🔗 测试与会话管理集成")
    print("-" * 40)
    
    try:
        from core.nlu.llm_client import LLMClient
        from core.chat.session_manager import SessionManager
        
        # 创建组件
        llm_client = LLMClient()
        session_manager = SessionManager()
        
        session_id = "test_intent_session"
        
        # 模拟多轮对话
        conversations = [
            "你好",
            "我想算命",
            "我1988年6月1日午时出生，男",
            "看看紫薇斗数"
        ]
        
        for i, message in enumerate(conversations, 1):
            print(f"\n轮次 {i}: {message}")
            
            # 获取会话上下文
            context = session_manager.get_conversation_context(session_id)
            
            # 意图识别
            intent_result = llm_client.intent_recognition(message, context)
            
            if intent_result:
                intent = intent_result.get("intent", "unknown")
                confidence = intent_result.get("confidence", 0.0)
                
                print(f"意图: {intent} (置信度: {confidence:.2f})")
                
                # 更新会话
                message_record = {
                    "timestamp": "now",
                    "user_message": message,
                    "intent": intent,
                    "confidence": confidence
                }
                
                context_updates = {}
                entities = intent_result.get("entities", {})
                if entities:
                    print(f"实体: {entities}")
                    # 如果有出生信息，保存到会话
                    if any(key.startswith("birth_") for key in entities.keys()):
                        birth_info = {k.replace("birth_", ""): v for k, v in entities.items() if k.startswith("birth_")}
                        if birth_info:
                            context_updates["birth_info"] = birth_info
                            print(f"保存出生信息: {birth_info}")
                
                session_manager.update_session(session_id, context_updates, message_record)
            else:
                print("❌ 意图识别失败")
        
        # 检查最终会话状态
        final_context = session_manager.get_conversation_context(session_id)
        print(f"\n📊 最终会话状态:")
        print(f"总消息数: {final_context['total_messages']}")
        print(f"历史记录: {len(final_context['recent_history'])} 条")
        if final_context.get('birth_info'):
            print(f"出生信息: {final_context['birth_info']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 阶段2.1：智能意图识别器测试")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 1. 测试LLM客户端
    results.append(("LLM客户端", test_llm_client()))
    
    # 2. 测试基础意图识别
    results.append(("基础意图识别", test_intent_recognition_basic()))
    
    # 3. 测试实体提取
    results.append(("实体提取", test_entity_extraction()))
    
    # 4. 测试集成
    results.append(("会话集成", test_integration_with_session()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 阶段2.1测试全部通过！智能意图识别器工作正常！")
        print("\n📋 下一步：开始阶段2.2 - 实体提取器优化")
        print("\n🎯 当前功能状态:")
        print("  ✅ LLM API调用正常")
        print("  ✅ 意图识别准确")
        print("  ✅ 实体提取有效")
        print("  ✅ 会话集成成功")
    else:
        print("💥 部分测试失败，需要修复问题后再继续")
        print("\n🔧 建议检查:")
        print("  - API密钥配置")
        print("  - 网络连接")
        print("  - LLM模型响应格式")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
