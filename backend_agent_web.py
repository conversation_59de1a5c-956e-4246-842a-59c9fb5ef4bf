#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇+八字融合分析后台管理系统 v3.0
优化版本 - 模块化架构，提升性能和用户体验
"""

# 🔧 编码和环境配置
import os
import sys
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ["STREAMLIT_SERVER_FILE_WATCHER_TYPE"] = "none"
os.environ["STREAMLIT_SERVER_RUN_ON_SAVE"] = "false"

if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

# 核心依赖
import streamlit as st
import asyncio
import time
import json
import hashlib
import psutil
import re
from datetime import datetime, date
from typing import Dict, List, Optional, Any
import threading
import traceback
sys.path.append('.')

# 设置页面配置
st.set_page_config(
    page_title="紫薇+八字融合分析后台系统 v3.0",
    page_icon="🔮",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 🎨 现代化赛博朋克管理界面CSS
st.markdown("""
<style>
    /* 导入现代字体 */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* 全局样式重置 */
    * {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* 主体背景 - 现代渐变深色主题 */
    .main {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        color: #ffffff;
        padding: 0.5rem;
        min-height: 100vh;
    }
    .stApp {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    }

    /* 标题样式 - 现代化设计 */
    h1 {
        background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #00ff00 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 2rem !important;
        font-weight: 700 !important;
        margin: 1rem 0 !important;
        text-align: center;
        letter-spacing: -0.02em;
        line-height: 1.2;
    }
    h2 {
        color: #00f5ff;
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        margin: 1rem 0 0.5rem 0 !important;
        position: relative;
        padding-left: 1rem;
    }
    h2::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 100%;
        background: linear-gradient(135deg, #00f5ff, #ff00ff);
        border-radius: 2px;
    }
    h3 {
        color: #ff6b6b;
        font-size: 1.2rem !important;
        font-weight: 500 !important;
        margin: 0.8rem 0 0.4rem 0 !important;
    }

    /* 现代化卡片样式 - 玻璃拟态设计 */
    .record-table {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }
    .record-table::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    }
    .record-table:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.4),
            0 0 40px rgba(0, 245, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(0, 245, 255, 0.3);
    }
    .record-row {
        display: flex;
        align-items: center;
        padding: 0.8rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .record-row:last-child {
        border-bottom: none;
    }

    /* 状态标签 - 霓虹灯效果 */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 600;
        margin: 0 0.5rem;
        display: inline-block;
        position: relative;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }
    .status-success {
        background: linear-gradient(135deg, #00ff88, #00cc6a);
        color: #000;
        box-shadow:
            0 0 20px rgba(0, 255, 136, 0.5),
            0 4px 15px rgba(0, 255, 136, 0.3);
    }
    .status-success:hover {
        box-shadow:
            0 0 30px rgba(0, 255, 136, 0.8),
            0 6px 20px rgba(0, 255, 136, 0.4);
        transform: translateY(-2px);
    }
    .status-warning {
        background: linear-gradient(135deg, #ffaa00, #ff8800);
        color: #000;
        box-shadow:
            0 0 20px rgba(255, 170, 0, 0.5),
            0 4px 15px rgba(255, 170, 0, 0.3);
    }
    .status-error {
        background: linear-gradient(135deg, #ff4757, #ff3742);
        color: #fff;
        box-shadow:
            0 0 20px rgba(255, 71, 87, 0.5),
            0 4px 15px rgba(255, 71, 87, 0.3);
    }
    .status-info {
        background: linear-gradient(135deg, #00f5ff, #0099cc);
        color: #000;
        box-shadow:
            0 0 20px rgba(0, 245, 255, 0.5),
            0 4px 15px rgba(0, 245, 255, 0.3);
    }

    /* 按钮样式 - 未来科技感 */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow:
            0 8px 25px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
        min-height: 2.5rem;
    }
    .stButton > button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    .stButton > button:hover::before {
        left: 100%;
    }
    .stButton > button:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        transform: translateY(-2px) scale(1.05);
        box-shadow:
            0 15px 40px rgba(102, 126, 234, 0.4),
            0 0 30px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    .stButton > button:active {
        transform: translateY(-1px) scale(1.02);
    }

    /* 输入框样式 - 玻璃拟态 */
    .stTextInput > div > div > input,
    .stSelectbox > div > div > select,
    .stTextArea > div > div > textarea,
    .stNumberInput > div > div > input {
        background: rgba(255, 255, 255, 0.05) !important;
        backdrop-filter: blur(10px) !important;
        color: #ffffff !important;
        border: 2px solid rgba(255, 255, 255, 0.1) !important;
        border-radius: 12px !important;
        padding: 0.8rem !important;
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    }
    .stTextInput > div > div > input:focus,
    .stSelectbox > div > div > select:focus,
    .stTextArea > div > div > textarea:focus,
    .stNumberInput > div > div > input:focus {
        border-color: rgba(0, 245, 255, 0.6) !important;
        box-shadow:
            0 0 30px rgba(0, 245, 255, 0.3),
            0 8px 25px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
        background: rgba(255, 255, 255, 0.08) !important;
        transform: translateY(-2px);
    }
    .stTextInput > div > div > input::placeholder,
    .stTextArea > div > div > textarea::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    /* 表单样式 - 透明玻璃效果 */
    .stForm {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 15px !important;
        padding: 2rem !important;
        margin: 1rem 0 !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1) !important;
    }

    /* 表单内容区域透明背景 */
    .stForm > div,
    .stForm .element-container,
    .stForm .stMarkdown,
    .stForm .stColumns,
    .stForm .stSelectbox,
    .stForm .stTextInput,
    .stForm .stDateInput,
    .stForm .stTextArea {
        background: transparent !important;
    }

    /* 表单内文字颜色 */
    .stForm h1, .stForm h2, .stForm h3, .stForm h4, .stForm h5, .stForm h6,
    .stForm p, .stForm span, .stForm div, .stForm label {
        color: #2c3e50 !important;
    }

    /* 信息框样式 - 紧凑 */
    .stInfo, .stSuccess, .stWarning, .stError {
        padding: 0.5rem;
        margin: 0.3rem 0;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    /* 进度条样式 - 紧凑 */
    .progress-compact {
        background: #3d3d3d;
        border-radius: 4px;
        height: 1.5rem;
        margin: 0.3rem 0;
        overflow: hidden;
    }
    .progress-bar-compact {
        background: linear-gradient(90deg, #4CAF50, #8BC34A);
        height: 100%;
        text-align: center;
        line-height: 1.5rem;
        color: white;
        font-size: 0.8rem;
        font-weight: bold;
    }

    /* 数据表格样式 */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        background: #2d2d2d;
        border-radius: 8px;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    .data-table th {
        background: #4CAF50;
        color: white;
        padding: 0.5rem;
        text-align: left;
        font-size: 0.9rem;
    }
    .data-table td {
        padding: 0.5rem;
        border-bottom: 1px solid #444;
        font-size: 0.85rem;
    }
    .data-table tr:hover {
        background: #3d3d3d;
    }

    /* 工具栏样式 */
    .toolbar {
        background: #2d2d2d;
        border-radius: 8px;
        padding: 0.5rem;
        margin: 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
        width: 8px;
    }
    ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #00f5ff, #ff00ff);
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
    }
    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #00ccff, #cc00cc);
    }

    /* 动画效果 */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    @keyframes glow-pulse {
        0%, 100% {
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }
        50% {
            box-shadow: 0 0 40px rgba(0, 245, 255, 0.6), 0 0 60px rgba(255, 0, 255, 0.4);
        }
    }
    .record-card-left {
        animation: fadeInUp 0.6s ease-out;
    }
    .sidebar-container {
        animation: slideInLeft 0.8s ease-out;
    }
    .glow-effect {
        animation: glow-pulse 3s ease-in-out infinite;
    }

    /* 隐藏Streamlit默认元素 */
    .stDeployButton { display: none !important; }
    .stDecoration { display: none !important; }
    header[data-testid="stHeader"] { display: none !important; }
    .stMainBlockContainer { padding-top: 1rem !important; }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .block-container { padding: 1rem; }
        h1 { font-size: 1.8rem !important; }
        h2 { font-size: 1.3rem !important; }
        .record-card-left { padding: 1rem; margin: 0.8rem 0; }
    }
    @media (max-width: 768px) {
        .block-container { padding: 0.5rem; }
        h1 { font-size: 1.5rem !important; }
        h2 { font-size: 1.1rem !important; }
        .record-card-left { padding: 0.8rem; margin: 0.5rem 0; }
        .sidebar-container { padding: 1rem; margin: 0.5rem 0; }
    }

    /* 侧边栏样式 - 玻璃拟态 */
    .sidebar-container {
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.08);
        border-radius: 20px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
    }
    .sidebar-container:hover {
        border-color: rgba(0, 245, 255, 0.2);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.4),
            0 0 20px rgba(0, 245, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    /* 左右栏样式 - 现代化 */
    .left-panel {
        background: rgba(255, 255, 255, 0.02);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 1.5rem;
        height: 100vh;
        overflow-y: auto;
        border: 1px solid rgba(255, 255, 255, 0.05);
    }
    .right-panel {
        background: rgba(255, 255, 255, 0.01);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 1.5rem;
        height: 100vh;
        overflow-y: auto;
        border: 1px solid rgba(255, 255, 255, 0.03);
    }

    /* 记录卡片 - 左侧栏专用 */
    .record-card-left {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(15px);
        border-radius: 15px;
        padding: 1.2rem;
        margin: 1rem 0;
        border-left: 4px solid #00ff88;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
    }
    .record-card-left::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.5), transparent);
    }
    .record-card-left:hover {
        background: rgba(255, 255, 255, 0.08);
        border-left-color: #00ff88;
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(0, 255, 136, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }

    /* 表单样式 - 左侧栏专用 */
    .form-compact {
        background: rgba(255, 255, 255, 0.04);
        backdrop-filter: blur(15px);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    /* 右侧详情区域 */
    .detail-section {
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin: 1rem 0;
        border-left: 4px solid #00f5ff;
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }
    .detail-section:hover {
        border-left-color: #00f5ff;
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.4),
            0 0 20px rgba(0, 245, 255, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
</style>
""", unsafe_allow_html=True)

def get_all_cache_records():
    """获取所有缓存记录"""
    try:
        cache_dir = 'data/calculation_cache'
        if not os.path.exists(cache_dir):
            return []

        records = []
        for filename in os.listdir(cache_dir):
            if filename.endswith('.json') and filename != 'index.json':
                filepath = os.path.join(cache_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    birth_info = data.get('birth_info', {})
                    detailed_analysis = data.get('detailed_analysis', {})

                    # 获取分析类型
                    calculation_type = data.get('calculation_type', 'ziwei')

                    # 计算完成角度数和总字数
                    completed_angles = 0
                    total_words = 0

                    # 检查分析类型
                    if calculation_type == 'compatibility':
                        # 合盘分析的完成度计算
                        compatibility_analysis = detailed_analysis.get('compatibility_analysis', '')
                        if compatibility_analysis and compatibility_analysis.strip():
                            completed_angles = 1  # 合盘分析完成
                            total_words = len(compatibility_analysis)
                        else:
                            completed_angles = 0  # 合盘分析未完成
                            total_words = 0
                    elif calculation_type == 'liuyao':
                        # 六爻占卜的完成度计算
                        liuyao_analysis = detailed_analysis.get('liuyao_analysis', '')
                        if liuyao_analysis and liuyao_analysis.strip():
                            completed_angles = 1  # 六爻占卜完成
                            total_words = len(liuyao_analysis)
                        else:
                            completed_angles = 0  # 六爻占卜未完成
                            total_words = 0
                    else:
                        # 命理分析的完成度计算
                        if isinstance(detailed_analysis, dict):
                            angle_analyses = detailed_analysis.get('angle_analyses', {})
                            completed_angles = len([v for v in angle_analyses.values() if v and len(v) > 100])
                            total_words = sum(len(v) for v in angle_analyses.values() if v)

                    # 检查图片是否存在
                    chart_exists = False
                    chart_path = data.get('chart_image_path')
                    if chart_path and os.path.exists(chart_path):
                        chart_exists = True
                    else:
                        # 尝试查找对应的图片文件
                        charts_dir = "charts"
                        if os.path.exists(charts_dir):
                            chart_files = [f for f in os.listdir(charts_dir) if f.startswith("integrated_chart_") and f.endswith(".png")]
                            if chart_files:
                                chart_exists = True

                    # 根据分析类型构建不同的显示格式
                    if calculation_type == 'compatibility':
                        # 合盘分析的显示格式
                        person_a = birth_info.get('person_a', '')
                        person_b = birth_info.get('person_b', '')
                        analysis_dimension = birth_info.get('analysis_dimension', '')

                        # 提取姓名
                        name_a = person_a.split('(')[0] if person_a else 'A'
                        name_b = person_b.split('(')[0] if person_b else 'B'

                        birth_display = f"💕 {name_a} & {name_b} - {analysis_dimension}"
                        if len(birth_display) > 40:
                            birth_display = birth_display[:37] + "..."
                    elif calculation_type == 'liuyao':
                        # 六爻占卜的显示格式
                        question = birth_info.get('question', '未知问题')
                        gender = birth_info.get('gender', '未知')

                        # 截断过长的问题
                        if len(question) > 20:
                            question = question[:20] + "..."

                        birth_display = f"🔮 {question} ({gender})"
                    else:
                        # 命理分析的显示格式
                        birth_display = f"{birth_info.get('year', '?')}年{birth_info.get('month', '?')}月{birth_info.get('day', '?')}日 {birth_info.get('hour', '?')} {birth_info.get('gender', '?')}命"

                    record = {
                        'result_id': data.get('result_id', filename[:-5]),
                        'birth_info': birth_display,
                        'created_at': data.get('created_at', '未知')[:19],
                        'has_analysis': completed_angles > 0,
                        'completed_angles': completed_angles,
                        'total_words': total_words,
                        'chart_exists': chart_exists,
                        'raw_birth_info': birth_info,
                        'calculation_type': calculation_type  # 添加类型信息
                    }
                    records.append(record)

                except Exception as e:
                    continue

        # 按创建时间排序
        records.sort(key=lambda x: x['created_at'], reverse=True)
        return records

    except Exception as e:
        return []

def check_existing_analysis(birth_info, analysis_type=None):
    """检查是否已有现存的分析结果"""
    records = get_all_cache_records()
    for record in records:
        cached_birth = record['raw_birth_info']
        if (cached_birth.get('year') == birth_info['year'] and
            cached_birth.get('month') == birth_info['month'] and
            cached_birth.get('day') == birth_info['day'] and
            cached_birth.get('hour') == birth_info['hour'] and
            cached_birth.get('gender') == birth_info['gender']):

            # 如果指定了分析类型，还要检查类型是否匹配
            if analysis_type:
                # 从result_id中提取分析类型（如果有的话）
                result_id = record['result_id']
                if analysis_type == "ziwei" and "ziwei" in result_id.lower():
                    return result_id
                elif analysis_type == "bazi" and "bazi" in result_id.lower():
                    return result_id
                elif analysis_type == "combined" and ("combined" in result_id.lower() or "ziwei_bazi" in result_id.lower()):
                    return result_id
                # 如果没有明确的类型标识，返回第一个匹配的记录
                elif "_" not in result_id or analysis_type == "ziwei":  # 默认为紫薇
                    return result_id
            else:
                # 没有指定类型，返回第一个匹配的记录
                return record['result_id']
    return None

def main():
    # 现代化主标题
    st.markdown("""
    <div style="text-align: center; margin: 2rem 0 3rem 0; position: relative;">
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 3rem;
            font-weight: 800;
            margin: 0;
            letter-spacing: -0.02em;
            line-height: 1.1;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
        ">
            🔮 紫薇+八字融合分析后台系统
        </div>
        <div style="
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
            margin-top: 1rem;
            font-weight: 400;
            letter-spacing: 0.5px;
        ">
            Advanced Fortune Analysis Management Platform v3.0
        </div>
        <div style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
            animation: glow-pulse 4s ease-in-out infinite;
        "></div>
    </div>
    """, unsafe_allow_html=True)

    # 初始化session状态
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'overview'
    if 'selected_record' not in st.session_state:
        st.session_state.selected_record = None

    # 🎨 创建左右布局
    with st.container():
        left_sidebar, main_content = st.columns([1, 3])

        # 🎨 左侧工具栏
        with left_sidebar:
            render_sidebar()

        # 🎨 右侧主内容区
        with main_content:
            render_main_content()

def render_sidebar():
    """渲染优化的左侧导航栏"""

    # 获取数据
    cache_records = get_all_cache_records()
    current_view = st.session_state.get('current_view', 'overview')

    # 🎨 现代化侧边栏标题
    st.markdown("""
    <div class="sidebar-container glow-effect">
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="
                background: linear-gradient(135deg, #00f5ff, #ff00ff);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                font-size: 1.5rem;
                font-weight: 700;
                margin-bottom: 0.5rem;
            ">🛠️ 控制中心</div>
            <div style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">
                Advanced Management Hub
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 🔧 核心功能导航 - 分组设计
    render_navigation_tabs(current_view, cache_records)

    st.markdown("---")

    # 🔧 实时状态面板
    render_realtime_status_panel(cache_records)

    # 🔧 快速访问最近记录
    render_quick_access_panel(cache_records)

    st.markdown("---")

    # 🔧 系统工具集
    render_system_tools_panel()

def render_navigation_tabs(current_view, cache_records):
    """渲染分组导航标签页"""

    # 主要功能分组
    st.markdown("""
    <div class="sidebar-container">
        <h4 style="color: #00f5ff; margin-bottom: 1rem; text-align: center;">
            🎯 核心功能
        </h4>
    </div>
    """, unsafe_allow_html=True)

    # 核心功能按钮 - 使用现代化设计
    core_nav = {
        "overview": ("📊", "系统概览", "#4CAF50"),
        "create": ("🆕", "创建分析", "#2196F3"),
        "records": ("📋", "分析记录", "#FF9800")
    }

    for key, (icon, label, color) in core_nav.items():
        is_active = (key == current_view)
        button_style = f"""
        background: {'linear-gradient(135deg, ' + color + ', ' + color + '88)' if is_active else 'rgba(255, 255, 255, 0.05)'};
        border: {'2px solid ' + color if is_active else '1px solid rgba(255, 255, 255, 0.1)'};
        color: {'#000' if is_active else '#fff'};
        font-weight: {'700' if is_active else '500'};
        transform: {'scale(1.05)' if is_active else 'scale(1)'};
        box-shadow: {'0 0 20px ' + color + '50' if is_active else 'none'};
        """

        if st.button(f"{icon} {label}",
                    key=f"nav_{key}",
                    use_container_width=True,
                    help=f"切换到{label}页面"):
            navigate_to_view(key)

    st.markdown("---")

    # 高级功能分组
    st.markdown("""
    <div class="sidebar-container">
        <h4 style="color: #ff6b6b; margin-bottom: 1rem; text-align: center;">
            🔧 高级功能
        </h4>
    </div>
    """, unsafe_allow_html=True)

    # 高级功能按钮
    advanced_nav = {
        "compatibility": ("💕", "合盘分析"),
        "liuyao": ("🔮", "六爻占卜"),
        "export": ("📊", "数据导出")
    }

    for key, (icon, label) in advanced_nav.items():
        if st.button(f"{icon} {label}",
                    key=f"adv_{key}",
                    use_container_width=True):
            navigate_to_view(key)

def navigate_to_view(view_name, record_id=None):
    """优化的导航函数 - 减少页面刷新"""
    st.session_state.current_view = view_name
    if record_id:
        st.session_state.selected_record = record_id
    else:
        st.session_state.selected_record = None

    # 添加页面切换动画效果
    st.session_state.page_transition = True
    st.rerun()

def render_realtime_status_panel(cache_records):
    """渲染实时状态面板"""
    st.markdown("#### 📊 实时状态")

    if cache_records:
        total_records = len(cache_records)

        # 计算各种状态的记录数
        completed = 0
        in_progress = 0

        for r in cache_records:
            calculation_type = r.get('calculation_type', 'ziwei')

            if calculation_type in ['compatibility', 'liuyao']:
                if r['completed_angles'] > 0 and r['total_words'] > 0:
                    completed += 1
                elif r['has_analysis']:
                    in_progress += 1
            else:
                if r['has_analysis'] and r['completed_angles'] >= 12:
                    completed += 1
                elif r['has_analysis'] and r['completed_angles'] < 12:
                    in_progress += 1

        # 现代化状态卡片
        st.markdown(f"""
        <div class="record-card-left glow-effect">
            <div style="text-align: center;">
                <div style="font-size: 2rem; color: #00f5ff; font-weight: bold; margin-bottom: 0.5rem;">
                    {total_records}
                </div>
                <div style="font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">
                    总记录数
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # 状态指标
        col1, col2 = st.columns(2)
        with col1:
            st.metric("✅ 已完成", completed, delta=None)
        with col2:
            st.metric("🔄 进行中", in_progress, delta=None)

        # 系统性能监控
        render_system_performance()

    else:
        st.markdown("""
        <div class="record-card-left">
            <div style="text-align: center; color: rgba(255, 255, 255, 0.6);">
                <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">📊</div>
                <div>暂无数据</div>
            </div>
        </div>
        """, unsafe_allow_html=True)

def render_system_performance():
    """渲染系统性能监控"""
    st.markdown("---")
    st.markdown("#### ⚡ 系统性能")

    try:
        import psutil

        # CPU和内存使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_percent = psutil.virtual_memory().percent

        # 性能状态卡片
        col1, col2 = st.columns(2)

        with col1:
            cpu_color = "#00ff88" if cpu_percent < 50 else "#ffaa00" if cpu_percent < 80 else "#ff4757"
            st.markdown(f"""
            <div class="record-card-left" style="border-left-color: {cpu_color};">
                <div style="text-align: center;">
                    <div style="font-size: 1.2rem; color: {cpu_color}; font-weight: bold;">
                        {cpu_percent:.1f}%
                    </div>
                    <div style="font-size: 0.8rem; color: rgba(255, 255, 255, 0.7);">
                        CPU使用率
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            mem_color = "#00ff88" if memory_percent < 60 else "#ffaa00" if memory_percent < 80 else "#ff4757"
            st.markdown(f"""
            <div class="record-card-left" style="border-left-color: {mem_color};">
                <div style="text-align: center;">
                    <div style="font-size: 1.2rem; color: {mem_color}; font-weight: bold;">
                        {memory_percent:.1f}%
                    </div>
                    <div style="font-size: 0.8rem; color: rgba(255, 255, 255, 0.7);">
                        内存使用率
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

    except ImportError:
        st.info("性能监控模块未安装")

    # 自动刷新选项
    if st.checkbox("🔄 自动刷新 (10秒)", key="auto_refresh_sidebar"):
        import time
        time.sleep(10)
        st.rerun()

def render_quick_access_panel(cache_records):
    """渲染快速访问面板"""
    st.markdown("#### 🕒 快速访问")

    if cache_records:
        for i, record in enumerate(cache_records[:3]):  # 显示最近3条
            calculation_type = record.get('calculation_type', 'ziwei')

            # 根据类型确定状态颜色
            if calculation_type in ['compatibility', 'liuyao']:
                is_completed = (record['completed_angles'] > 0 and record['total_words'] > 0)
            else:
                is_completed = (record['completed_angles'] >= 12)

            status_color = "#00ff88" if is_completed else "#ffaa00"

            st.markdown(f"""
            <div class="record-card-left" style="border-left-color: {status_color};">
                <div style="font-size: 0.85rem; color: #fff; margin-bottom: 0.5rem;">
                    <strong>{record['birth_info'][:20]}...</strong>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span class="status-badge status-{'success' if is_completed else 'warning'}">
                        {'✅ 完成' if is_completed else '🔄 进行中'}
                    </span>
                    <span style="font-size: 0.7rem; color: rgba(255, 255, 255, 0.6);">
                        {record['created_at'][11:16]}
                    </span>
                </div>
            </div>
            """, unsafe_allow_html=True)

            if st.button("查看详情", key=f"quick_view_{record['result_id']}", use_container_width=True):
                navigate_to_view('detail', record['result_id'])
    else:
        st.markdown("""
        <div class="record-card-left">
            <div style="text-align: center; color: rgba(255, 255, 255, 0.6);">
                <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">📋</div>
                <div>暂无记录</div>
            </div>
        </div>
        """, unsafe_allow_html=True)

def render_system_tools_panel():
    """渲染系统工具面板"""
    st.markdown("#### ⚙️ 系统工具")

    # 工具按钮网格
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🧹 清理缓存", key="tool_clean_cache", use_container_width=True):
            navigate_to_view('clean_cache')
        if st.button("📊 数据导出", key="tool_export_data", use_container_width=True):
            navigate_to_view('export')

    with col2:
        if st.button("🔧 系统设置", key="tool_settings", use_container_width=True):
            navigate_to_view('settings')
        if st.button("📖 使用手册", key="tool_help", use_container_width=True):
            navigate_to_view('help')

    # 版本信息
    st.markdown("---")
    st.markdown("""
    <div class="record-card-left">
        <div style="text-align: center; font-size: 0.8rem; color: rgba(255, 255, 255, 0.7);">
            <div style="font-weight: 600; margin-bottom: 0.3rem;">紫薇+八字融合分析系统</div>
            <div>Version 3.0 | Build 20241224</div>
            <div style="margin-top: 0.5rem;">
                <span style="color: #00ff88;">●</span> 运行正常
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)





def render_main_content():
    """渲染优化的右侧主内容区"""
    current_view = st.session_state.current_view

    # 添加页面切换动画效果
    if st.session_state.get('page_transition', False):
        st.markdown("""
        <style>
        .main-content-transition {
            animation: fadeInUp 0.5s ease-out;
        }
        </style>
        <div class="main-content-transition">
        """, unsafe_allow_html=True)
        st.session_state.page_transition = False

    # 路由到不同页面
    page_routes = {
        'overview': render_overview_enhanced,
        'records': render_records_enhanced,
        'create': render_create_simple,  # 使用美化后的创建页面
        'compatibility': render_compatibility_analysis,
        'detail': render_completed_detail_enhanced,
        'export': render_export_data,
        'liuyao': render_liuyao_divination,
        'clean_cache': render_clean_cache,
        'settings': render_settings_page,
        'help': render_help_page
    }

    # 渲染对应页面
    render_func = page_routes.get(current_view, render_overview_enhanced)
    render_func()

    # 关闭动画容器
    if st.session_state.get('page_transition', False):
        st.markdown("</div>", unsafe_allow_html=True)

# ===== 增强版页面渲染函数 =====

def render_overview_enhanced():
    """渲染增强版系统概览"""
    st.markdown("""
    <div class="detail-section">
        <h2 style="margin-bottom: 2rem;">📊 系统概览仪表板</h2>
    </div>
    """, unsafe_allow_html=True)

    cache_records = get_all_cache_records()

    if cache_records:
        # 实时数据统计
        render_dashboard_metrics(cache_records)

        st.markdown("---")

        # 最近活动
        render_recent_activities(cache_records)

        st.markdown("---")

        # 系统状态图表
        render_system_charts(cache_records)

    else:
        render_welcome_screen()

def render_dashboard_metrics(cache_records):
    """渲染仪表板指标"""
    total_records = len(cache_records)
    completed = sum(1 for r in cache_records if is_record_completed(r))
    in_progress = sum(1 for r in cache_records if is_record_in_progress(r))
    pending = total_records - completed - in_progress

    # 现代化指标卡片
    col1, col2, col3, col4 = st.columns(4)

    metrics = [
        (col1, "📊 总记录", total_records, "#00f5ff"),
        (col2, "✅ 已完成", completed, "#00ff88"),
        (col3, "🔄 进行中", in_progress, "#ffaa00"),
        (col4, "⏳ 待处理", pending, "#ff6b6b")
    ]

    for col, label, value, color in metrics:
        with col:
            st.markdown(f"""
            <div class="record-card-left glow-effect" style="border-left-color: {color};">
                <div style="text-align: center;">
                    <div style="font-size: 2.5rem; color: {color}; font-weight: bold; margin-bottom: 0.5rem;">
                        {value}
                    </div>
                    <div style="font-size: 0.9rem; color: rgba(255, 255, 255, 0.8);">
                        {label.split(' ')[1]}
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

def render_recent_activities(cache_records):
    """渲染最近活动"""
    st.markdown("### 📋 最近活动")

    # 按时间排序，显示最近5条
    recent_records = sorted(cache_records, key=lambda x: x['created_at'], reverse=True)[:5]

    for record in recent_records:
        col1, col2, col3 = st.columns([3, 1, 1])

        with col1:
            status_icon = "✅" if is_record_completed(record) else "🔄" if is_record_in_progress(record) else "⏳"
            st.markdown(f"{status_icon} **{record['birth_info']}**")
            st.caption(f"创建时间: {record['created_at'][:19]}")

        with col2:
            if is_record_completed(record):
                st.success("已完成")
            elif is_record_in_progress(record):
                st.warning("进行中")
            else:
                st.info("待处理")

        with col3:
            if is_record_completed(record):
                if st.button("查看详情", key=f"overview_view_{record['result_id']}", use_container_width=True):
                    navigate_to_view('detail', record['result_id'])

def render_system_charts(cache_records):
    """渲染系统图表"""
    st.markdown("### 📈 系统统计图表")

    # 分析类型分布
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 分析类型分布")
        type_counts = {}
        for record in cache_records:
            calc_type = record.get('calculation_type', 'ziwei')
            type_counts[calc_type] = type_counts.get(calc_type, 0) + 1

        if type_counts:
            for calc_type, count in type_counts.items():
                st.metric(f"{calc_type.title()}", count)

    with col2:
        st.markdown("#### 完成率统计")
        if cache_records:
            completion_rate = (sum(1 for r in cache_records if is_record_completed(r)) / len(cache_records)) * 100
            st.metric("完成率", f"{completion_rate:.1f}%")

            # 进度条
            st.progress(completion_rate / 100)

def render_welcome_screen():
    """渲染欢迎屏幕"""
    st.markdown("""
    <div class="detail-section">
        <div style="text-align: center; padding: 3rem 0;">
            <div style="font-size: 4rem; margin-bottom: 1rem;">🔮</div>
            <h2 style="color: #00f5ff; margin-bottom: 1rem;">欢迎使用紫薇+八字融合分析系统</h2>
            <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.1rem; margin-bottom: 2rem;">
                基于传统命理学与现代AI技术的智能分析平台
            </p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 2rem;">
                <div class="record-card-left">
                    <h4 style="color: #00ff88;">🔮 紫薇+八字融合</h4>
                    <p style="font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">双重算法相互印证，确保分析准确性</p>
                </div>
                <div class="record-card-left">
                    <h4 style="color: #ffaa00;">🎯 六爻占卜功能</h4>
                    <p style="font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">独立的占卜预测系统</p>
                </div>
                <div class="record-card-left">
                    <h4 style="color: #ff6b6b;">📊 可视化图表</h4>
                    <p style="font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">现代化命盘展示</p>
                </div>
                <div class="record-card-left">
                    <h4 style="color: #00f5ff;">🎨 12角度分析</h4>
                    <p style="font-size: 0.9rem; color: rgba(255, 255, 255, 0.7);">专业深度解读</p>
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # 快速开始按钮
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if st.button("🚀 开始第一个分析", key="welcome_start", use_container_width=True):
            navigate_to_view('create')

def render_records_enhanced():
    """渲染增强版记录列表"""
    st.markdown("""
    <div class="detail-section">
        <h2 style="margin-bottom: 2rem;">📋 分析记录管理</h2>
    </div>
    """, unsafe_allow_html=True)

    cache_records = get_all_cache_records()

    if cache_records:
        # 增强的筛选和搜索
        render_advanced_filters(cache_records)

        st.markdown("---")

        # 记录列表
        render_records_list_enhanced(cache_records)
    else:
        st.info("暂无分析记录")

def render_advanced_filters(cache_records):
    """渲染高级筛选器"""
    col1, col2, col3 = st.columns(3)

    with col1:
        search_term = st.text_input("🔍 搜索记录", placeholder="输入生辰信息或关键词...")

    with col2:
        status_options = ["全部", "已完成", "进行中", "待处理"]
        status_filter = st.selectbox("📊 状态筛选", status_options)

    with col3:
        type_options = ["全部"] + list(set(r.get('calculation_type', 'ziwei') for r in cache_records))
        type_filter = st.selectbox("🔮 类型筛选", type_options)

    # 应用筛选逻辑
    filtered_records = cache_records

    if search_term:
        filtered_records = [r for r in filtered_records
                          if search_term.lower() in r['birth_info'].lower()]

    if status_filter != "全部":
        if status_filter == "已完成":
            filtered_records = [r for r in filtered_records if is_record_completed(r)]
        elif status_filter == "进行中":
            filtered_records = [r for r in filtered_records if is_record_in_progress(r)]
        elif status_filter == "待处理":
            filtered_records = [r for r in filtered_records
                              if not is_record_completed(r) and not is_record_in_progress(r)]

    if type_filter != "全部":
        filtered_records = [r for r in filtered_records
                          if r.get('calculation_type', 'ziwei') == type_filter]

    st.session_state.filtered_records = filtered_records
    st.markdown(f"### 找到 {len(filtered_records)} 条记录")

def render_records_list_enhanced(cache_records):
    """渲染增强版记录列表"""
    filtered_records = getattr(st.session_state, 'filtered_records', cache_records)

    for record in filtered_records:
        with st.container():
            col1, col2, col3 = st.columns([3, 1, 1])

            with col1:
                # 状态图标和信息
                if is_record_completed(record):
                    status_icon = "✅"
                    status_text = "已完成"
                    status_color = "#00ff88"
                elif is_record_in_progress(record):
                    status_icon = "🔄"
                    status_text = "进行中"
                    status_color = "#ffaa00"
                else:
                    status_icon = "⏳"
                    status_text = "待处理"
                    status_color = "#ff6b6b"

                st.markdown(f"""
                <div class="record-card-left" style="border-left-color: {status_color};">
                    <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem; margin-right: 0.5rem;">{status_icon}</span>
                        <strong style="color: #fff;">{record['birth_info']}</strong>
                    </div>
                    <div style="font-size: 0.8rem; color: rgba(255, 255, 255, 0.7);">
                        类型: {record.get('calculation_type', 'ziwei').title()} |
                        创建: {record['created_at'][:19]} |
                        字数: {record['total_words']:,}
                    </div>
                </div>
                """, unsafe_allow_html=True)

            with col2:
                st.markdown(f"""
                <div style="text-align: center; padding: 1rem 0;">
                    <div style="color: {status_color}; font-weight: bold;">{status_text}</div>
                    <div style="font-size: 0.8rem; color: rgba(255, 255, 255, 0.6);">
                        {record['completed_angles']}/12
                    </div>
                </div>
                """, unsafe_allow_html=True)

            with col3:
                # 所有记录都可以查看详情，不再区分状态
                if st.button("查看详情", key=f"view_{record['result_id']}", use_container_width=True):
                    navigate_to_view('detail', record['result_id'])

            st.markdown("---")

def render_create_enhanced():
    """渲染增强版创建分析页面"""
    st.markdown("""
    <div class="detail-section">
        <h2 style="margin-bottom: 2rem;">🆕 创建新分析</h2>
    </div>
    """, unsafe_allow_html=True)

    # 调用原有的创建功能
    render_create_simple()



def render_completed_detail_enhanced():
    """渲染增强版详情页面"""
    # 调用原有的详情功能
    render_completed_detail()

def render_settings_page():
    """渲染设置页面"""
    st.markdown("""
    <div class="detail-section">
        <h2 style="margin-bottom: 2rem;">⚙️ 系统设置</h2>
        <p style="color: rgba(255, 255, 255, 0.7);">系统配置和参数调整</p>
    </div>
    """, unsafe_allow_html=True)

    st.info("设置功能正在开发中...")

def render_help_page():
    """渲染帮助页面"""
    st.markdown("""
    <div class="detail-section">
        <h2 style="margin-bottom: 2rem;">📖 使用手册</h2>
        <p style="color: rgba(255, 255, 255, 0.7);">系统使用指南和常见问题</p>
    </div>
    """, unsafe_allow_html=True)

    st.info("帮助文档正在编写中...")

# ===== 辅助函数 =====

def is_record_completed(record):
    """判断记录是否已完成"""
    calculation_type = record.get('calculation_type', 'ziwei')
    if calculation_type in ['compatibility', 'liuyao']:
        return record['completed_angles'] > 0 and record['total_words'] > 0
    else:
        return record['completed_angles'] >= 12

def is_record_in_progress(record):
    """判断记录是否进行中"""
    calculation_type = record.get('calculation_type', 'ziwei')
    if calculation_type in ['compatibility', 'liuyao']:
        return record['has_analysis'] and not is_record_completed(record)
    else:
        return 0 < record['completed_angles'] < 12

def render_overview_simple():
    """渲染简化的系统概览"""
    st.markdown("## 📊 系统概览")

    cache_records = get_all_cache_records()

    if cache_records:
        # 🔧 核心指标卡片
        col1, col2, col3, col4 = st.columns(4)

        total_records = len(cache_records)

        # 修复：根据分析类型判断完成状态
        completed_records = 0
        in_progress = 0

        for r in cache_records:
            calculation_type = r.get('calculation_type', 'ziwei')

            if calculation_type in ['compatibility', 'liuyao']:
                # 合盘分析和六爻占卜：completed_angles=1且有内容就是完成
                if r['completed_angles'] > 0 and r['total_words'] > 0:
                    completed_records += 1
                elif r['has_analysis']:
                    in_progress += 1
            else:
                # 命理分析：12个角度完成
                if r['has_analysis'] and r['completed_angles'] >= 12:
                    completed_records += 1
                elif r['has_analysis'] and r['completed_angles'] < 12:
                    in_progress += 1

        pending = total_records - completed_records - in_progress

        with col1:
            st.metric("总记录", total_records)
        with col2:
            st.metric("已完成", completed_records)
        with col3:
            st.metric("进行中", in_progress)
        with col4:
            st.metric("待处理", pending)

        # 🔧 最近完成的分析
        st.markdown("### 📋 最近完成的分析")

        completed_records = [r for r in cache_records if r['completed_angles'] >= 12]
        if completed_records:
            for record in completed_records[:5]:  # 显示最近5条完成的
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.markdown(f"**{record['birth_info']}** - {record['total_words']:,}字")
                with col2:
                    if st.button("查看", key=f"overview_view_{record['result_id']}", use_container_width=True):
                        st.session_state.current_view = 'detail'
                        st.session_state.selected_record = record['result_id']
                        st.rerun()
        else:
            st.info("暂无已完成的分析")
    else:
        st.markdown("""
        ### 🎯 欢迎使用紫薇+八字融合分析系统

        **🌟 系统特色：**
        - 🔮 **紫薇+八字融合分析** - 双重算法相互印证
        - 🎯 **六爻占卜功能** - 独立的占卜预测
        - 📊 **HTML可视化图表** - 现代化命盘展示
        - 🎨 **12角度详细分析** - 专业深度解读

        当前系统中暂无分析记录。点击左侧 "🆕 创建分析" 开始第一个融合分析。
        """)

def render_records_simple():
    """渲染简化的记录列表"""
    st.markdown("## 📋 分析记录")

    cache_records = get_all_cache_records()

    if cache_records:
        # 🔧 简单筛选
        col1, col2 = st.columns([2, 1])
        with col1:
            search_term = st.text_input("🔍 搜索", placeholder="输入生辰信息...")
        with col2:
            status_filter = st.selectbox("状态", ["全部", "已完成", "进行中"])

        # 筛选记录
        filtered_records = cache_records
        if search_term:
            filtered_records = [r for r in filtered_records if search_term.lower() in r['birth_info'].lower()]

        if status_filter == "已完成":
            filtered_records = [r for r in filtered_records if r['completed_angles'] >= 12]
        elif status_filter == "进行中":
            filtered_records = [r for r in filtered_records if 0 < r['completed_angles'] < 12]

        st.markdown(f"### 找到 {len(filtered_records)} 条记录")

        # 记录列表
        for record in filtered_records:
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                # 根据分析类型显示不同的状态
                calculation_type = record.get('calculation_type', 'ziwei')

                if calculation_type == 'compatibility':
                    # 合盘分析的状态显示
                    if record['completed_angles'] > 0 and record['total_words'] > 0:  # 合盘完成
                        status_emoji = "✅"
                        st.markdown(f"{status_emoji} **{record['birth_info']}**")
                        st.caption(f"合盘完成 - {record['total_words']:,}字")
                    else:
                        status_emoji = "⏳"
                        st.markdown(f"{status_emoji} **{record['birth_info']}**")
                        st.caption("分析中...")
                elif calculation_type == 'liuyao':
                    # 六爻占卜的状态显示
                    if record['completed_angles'] > 0 and record['total_words'] > 0:  # 六爻完成
                        status_emoji = "🔮"
                        st.markdown(f"{status_emoji} **{record['birth_info']}**")
                        st.caption(f"占卜完成 - {record['total_words']:,}字")
                    else:
                        status_emoji = "⏳"
                        st.markdown(f"{status_emoji} **{record['birth_info']}**")
                        st.caption("待解卦")
                else:
                    # 命理分析的状态显示
                    status_emoji = "✅" if record['completed_angles'] >= 12 else "🔄" if record['has_analysis'] else "⏳"
                    st.markdown(f"{status_emoji} **{record['birth_info']}**")
                    st.caption(f"{record['completed_angles']}/12 - {record['total_words']:,}字")

            with col2:
                # 根据分析类型显示不同的进度
                if calculation_type == 'compatibility':
                    if record['completed_angles'] > 0 and record['total_words'] > 0:  # 合盘完成
                        st.markdown("**✅ 完成**")
                    else:
                        st.markdown("**💕 分析中**")
                elif calculation_type == 'liuyao':
                    if record['completed_angles'] > 0 and record['total_words'] > 0:  # 六爻完成
                        st.markdown("**✅ 完成**")
                    else:
                        st.markdown("**🔮 解卦中**")
                else:
                    st.markdown(f"**{record['completed_angles']}/12**")

            with col3:
                # 根据分析类型判断是否可以查看
                can_view = False
                if calculation_type == 'compatibility':
                    can_view = (record['completed_angles'] > 0 and record['total_words'] > 0)  # 合盘完成
                elif calculation_type == 'liuyao':
                    can_view = (record['completed_angles'] > 0 and record['total_words'] > 0)  # 六爻完成
                else:
                    can_view = (record['completed_angles'] >= 12)  # 命理分析完成

                if can_view:
                    if st.button("查看详情", key=f"records_view_{record['result_id']}", use_container_width=True):
                        st.session_state.current_view = 'detail'
                        st.session_state.selected_record = record['result_id']
                        st.rerun()
                else:
                    st.markdown("*处理中...*")

            st.markdown("---")
    else:
        st.info("暂无分析记录")

def render_create_simple():
    """渲染优化的创建分析页面"""
    # 添加美化的CSS样式
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    .info-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        padding: 1.5rem;
        border-radius: 12px;
        color: white;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 4px solid #667eea;
        margin: 1rem 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .input-section {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e1e8ed;
    }
    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
    }
    </style>
    """, unsafe_allow_html=True)

    # 主标题
    st.markdown("""
    <div class="main-header">
        <h1>🆕 创建新的命理分析</h1>
        <p>紫薇斗数 + 八字算命 双重印证，为您提供最准确的命理分析</p>
    </div>
    """, unsafe_allow_html=True)

    # 优化的创建表单
    with st.form("simple_create_form"):
        # 基本信息输入区域
        st.markdown("""
        <div class="section-title">👤 基本信息</div>
        """, unsafe_allow_html=True)

        # 使用日期选择器，更现代化
        from datetime import date
        col1, col2 = st.columns(2)

        with col1:
            birth_date = st.date_input(
                "📅 出生日期",
                value=date(1990, 3, 15),
                min_value=date(1900, 1, 1),
                max_value=date(2025, 12, 31),
                help="请选择准确的出生日期"
            )

            gender = st.selectbox(
                "👤 性别",
                ["女", "男"],
                help="性别影响命理分析的重点方向"
            )

        with col2:
            # 时辰选择优化 - 更清晰的显示
            hour_options_with_time = [
                "子时 (23:00-01:00)", "丑时 (01:00-03:00)", "寅时 (03:00-05:00)",
                "卯时 (05:00-07:00)", "辰时 (07:00-09:00)", "巳时 (09:00-11:00)",
                "午时 (11:00-13:00)", "未时 (13:00-15:00)", "申时 (15:00-17:00)",
                "酉时 (17:00-19:00)", "戌时 (19:00-21:00)", "亥时 (21:00-23:00)"
            ]
            hour_display = st.selectbox(
                "🕐 出生时辰",
                hour_options_with_time,
                index=4,
                help="时辰对命理分析极其重要，请尽量选择准确的时辰"
            )
            hour = hour_display.split(" ")[0]  # 提取时辰名称

        # 分析类型说明
        st.markdown("""
        <div class="info-card">
            <h3>🌟 紫薇+八字融合分析</h3>
            <p>本系统专注于紫薇斗数与八字算命的融合分析，双重算法相互印证，确保分析的准确性和全面性</p>
        </div>
        """, unsafe_allow_html=True)

        # 特点展示
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div class="feature-card">
                <h4>🔮 双重算法</h4>
                <p>紫薇斗数排盘 + 八字四柱计算</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div class="feature-card">
                <h4>📊 12角度分析</h4>
                <p>命宫、财富、婚姻、健康等全方位</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div class="feature-card">
                <h4>📈 可视化图表</h4>
                <p>HTML交互式排盘图表</p>
            </div>
            """, unsafe_allow_html=True)

        # 固定为融合分析
        selected_type = "combined"

        # 提交按钮
        submitted = st.form_submit_button(
            "🚀 开始紫薇+八字融合分析",
            use_container_width=True,
            type="primary"
        )

    if submitted:
        # 验证输入 - 使用日期选择器的值
        try:
            year_int = birth_date.year
            month_int = birth_date.month
            day_int = birth_date.day

            if not (1900 <= year_int <= 2100):
                st.error("❌ 请选择有效的年份")
                return

        except Exception as e:
            st.error(f"❌ 日期验证失败: {e}")
            return

        # 构建生辰信息
        birth_info = {
            "year": str(birth_date.year),
            "month": str(birth_date.month),
            "day": str(birth_date.day),
            "hour": hour,
            "gender": gender
        }

        # 智能检查重复 - 直接调用后台Agent
        result_id = create_new_analysis_with_type(birth_info, selected_type)
        if result_id:
            st.session_state.current_view = 'monitor'
            st.session_state.monitoring_task = result_id
            st.rerun()





def render_completed_detail():
    """渲染已完成分析的详情页面"""
    if not st.session_state.selected_record:
        st.error("❌ 未选择记录")
        return

    result_id = st.session_state.selected_record

    # 返回按钮
    if st.button("← 返回", key="detail_back"):
        st.session_state.current_view = 'records'
        st.session_state.selected_record = None
        st.rerun()

    st.markdown("---")

    # 加载详情数据
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(result_id)

        if cached_result:
            birth_info = cached_result.birth_info

            # 检查分析类型
            is_liuyao = hasattr(cached_result, 'calculation_type') and cached_result.calculation_type == 'liuyao'
            is_compatibility = hasattr(cached_result, 'calculation_type') and cached_result.calculation_type == 'compatibility'

            if is_compatibility:
                # 合盘分析的显示
                show_compatibility_analysis_content(cached_result)
            elif is_liuyao:
                # 六爻占卜的显示
                st.markdown(f"## 🔮 六爻占卜结果")

                # 显示占卜问题
                question = birth_info.get('question', '未知问题')
                gender = birth_info.get('gender', '未知')
                method = birth_info.get('method', '未知')

                st.markdown(f"""
                **占卜问题**: {question}

                **性别**: {gender} | **起卦方式**: {method}
                """)

                # 显示六爻分析内容
                show_liuyao_analysis_content(cached_result)
            else:
                # 命理分析的显示
                st.markdown(f"## 👤 {birth_info['year']}年{birth_info['month']}月{birth_info['day']}日 {birth_info['hour']} {birth_info['gender']}命")

                # 显示排盘图片
                show_chart_image_simple(cached_result)

                # 显示分析内容
                show_analysis_content_simple(cached_result)

        else:
            st.error(f"❌ 未找到分析结果: {result_id}")

    except Exception as e:
        st.error(f"❌ 加载详情失败: {e}")

def render_export_data():
    """渲染导出单人数据页面"""
    st.markdown("## 📊 导出个人分析报告")

    cache_records = get_all_cache_records()

    # 根据分析类型判断完成状态
    completed_records = []
    incomplete_records = []

    for r in cache_records:
        calculation_type = r.get('calculation_type', 'ziwei')
        if calculation_type in ['compatibility', 'liuyao']:
            # 合盘分析和六爻占卜：有内容就算完成
            if r['completed_angles'] > 0 and r['total_words'] > 0:
                completed_records.append(r)
            else:
                incomplete_records.append(r)
        else:
            # 命理分析：12个角度完成
            if r['completed_angles'] >= 12:
                completed_records.append(r)
            else:
                incomplete_records.append(r)

    # 显示统计信息
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("📊 总记录", len(cache_records))
    with col2:
        st.metric("✅ 可导出", len(completed_records))
    with col3:
        st.metric("⏳ 分析中", len(incomplete_records))

    # 如果有未完成的记录，显示提示
    if incomplete_records:
        st.info(f"💡 建议等待分析完成后再导出，当前有 {len(incomplete_records)} 个记录正在分析中")

        with st.expander("查看分析中的记录"):
            for r in incomplete_records:
                calculation_type = r.get('calculation_type', 'ziwei')
                if calculation_type in ['compatibility', 'liuyao']:
                    progress_text = "分析中..." if r['has_analysis'] else "等待开始"
                else:
                    progress_text = f"{r['completed_angles']}/12 角度"

                st.markdown(f"- **{r['birth_info']}** - {progress_text} ({r['total_words']:,}字)")

    if not completed_records:
        st.warning("⚠️ 暂无已完成的分析记录可导出")
        st.markdown("### 📝 导出说明")
        st.markdown("""
        - **命理分析**：需要完成全部12个角度的分析
        - **合盘分析**：需要完成合盘分析内容
        - **六爻占卜**：需要完成占卜解析

        请等待分析完成后再进行导出。
        """)
        return

    st.markdown(f"### 📋 选择要导出的记录 ({len(completed_records)} 个可用)")

    # 选择要导出的记录
    selected_record = st.selectbox(
        "选择记录",
        options=completed_records,
        format_func=lambda x: f"{x['birth_info']} - {x['total_words']:,}字 - {x['created_at'][:10]}",
        key="export_record_select"
    )

    if selected_record:
        # 显示选中记录的详细信息
        st.markdown(f"""
        <div class="detail-section">
            <h4>📋 选中记录信息</h4>
            <p><strong>生辰信息:</strong> {selected_record['birth_info']}</p>
            <p><strong>分析完成度:</strong> {selected_record['completed_angles']}/12</p>
            <p><strong>总字数:</strong> {selected_record['total_words']:,} 字</p>
            <p><strong>创建时间:</strong> {selected_record['created_at']}</p>
        </div>
        """, unsafe_allow_html=True)

        # 导出格式选择
        st.markdown("### 📄 选择导出格式")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📄 导出为Word", key="export_single_word", use_container_width=True):
                with st.spinner("正在生成Word文档..."):
                    export_file = export_single_to_word(selected_record)
                if export_file:
                    st.success(f"✅ Word文档导出成功!")
                    st.info(f"📁 文件保存位置: {export_file}")

                    # 提供下载链接
                    try:
                        with open(export_file, "rb") as file:
                            birth_info_str = selected_record['birth_info'].replace(' ', '_').replace('年', '').replace('月', '').replace('日', '').replace('时', '')
                            clean_filename = f"ziwei_analysis_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
                            st.download_button(
                                label="⬇️ 点击下载Word文档",
                                data=file.read(),
                                file_name=clean_filename,
                                mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                key="download_single_word"
                            )
                    except Exception as e:
                        st.error(f"❌ 下载准备失败: {e}")
                        st.info(f"💡 请直接到文件夹中查看: {export_file}")

        with col2:
            if st.button("📄 导出为HTML", key="export_single_html", use_container_width=True):
                with st.spinner("正在生成HTML文档..."):
                    export_file = export_single_to_html_with_toc(selected_record)  # 带目录的HTML导出
                if export_file:
                    st.success(f"✅ HTML报告导出成功!")
                    st.info(f"📁 文件保存位置: {export_file}")
                    st.info("💡 HTML格式支持完美的中文显示，可以在浏览器中打开并打印为PDF")

                    # 提供下载链接
                    try:
                        with open(export_file, "r", encoding="utf-8") as file:
                            birth_info_str = selected_record['birth_info'].replace(' ', '_').replace('年', '').replace('月', '').replace('日', '').replace('时', '')
                            clean_filename = f"ziwei_analysis_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                            st.download_button(
                                label="⬇️ 点击下载HTML文档",
                                data=file.read(),
                                file_name=clean_filename,
                                mime="text/html",
                                key="download_single_html"
                            )
                    except Exception as e:
                        st.error(f"❌ 下载准备失败: {e}")
                        st.info(f"💡 请直接到文件夹中查看: {export_file}")

        with col3:
            if st.button("📄 导出为文本", key="export_single_txt", use_container_width=True):
                with st.spinner("正在生成文本文件..."):
                    export_file = export_single_to_txt(selected_record)
                if export_file:
                    st.success(f"✅ 文本文件导出成功!")
                    st.info(f"📁 文件保存位置: {export_file}")

                    # 提供下载链接
                    try:
                        with open(export_file, "r", encoding="utf-8") as file:
                            birth_info_str = selected_record['birth_info'].replace(' ', '_').replace('年', '').replace('月', '').replace('日', '').replace('时', '')
                            clean_filename = f"ziwei_analysis_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                            st.download_button(
                                label="⬇️ 点击下载文本文件",
                                data=file.read(),
                                file_name=clean_filename,
                                mime="text/plain",
                                key="download_single_txt"
                            )
                    except Exception as e:
                        st.error(f"❌ 下载准备失败: {e}")
                        st.info(f"💡 请直接到文件夹中查看: {export_file}")

        # 添加文件管理说明
        st.markdown("---")
        st.markdown("### 📁 文件说明")
        st.info("""
        **导出文件位置**: `exports/` 文件夹

        **如果下载按钮无法使用**:
        1. 直接到 `exports` 文件夹查看导出的文件
        2. HTML文件可以直接用浏览器打开
        3. Word文件可以用Microsoft Word打开
        4. 文本文件可以用任何文本编辑器打开

        **推荐使用HTML格式**: 支持完美的中文显示，可以在浏览器中打开后打印为PDF
        """)

        # 显示导出文件列表
        st.markdown("### 📂 已导出文件")
        export_dir = "exports"
        if os.path.exists(export_dir):
            files = [f for f in os.listdir(export_dir) if f.endswith(('.docx', '.pdf', '.html', '.txt'))]
            if files:
                # 按修改时间排序
                files.sort(key=lambda x: os.path.getmtime(os.path.join(export_dir, x)), reverse=True)

                for i, filename in enumerate(files[:10]):  # 显示最近10个文件
                    filepath = os.path.join(export_dir, filename)
                    file_size = os.path.getsize(filepath)
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath)).strftime("%Y-%m-%d %H:%M:%S")

                    col1, col2, col3 = st.columns([3, 1, 1])
                    with col1:
                        st.markdown(f"📄 **{filename}**")
                        st.caption(f"大小: {file_size:,} bytes | 时间: {file_time}")

                    with col2:
                        file_ext = filename.split('.')[-1].upper()
                        st.markdown(f"**{file_ext}**")

                    with col3:
                        if st.button("🔗 复制路径", key=f"copy_path_{i}"):
                            full_path = os.path.abspath(filepath)
                            st.code(full_path)
                            st.success("✅ 路径已显示，可以复制")
            else:
                st.info("暂无导出文件")
        else:
            st.info("导出目录不存在")

def render_compatibility_analysis():
    """渲染合盘分析页面"""
    # 添加美化的CSS样式
    st.markdown("""
    <style>
    .compatibility-header {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        padding: 2rem;
        border-radius: 15px;
        color: #2c3e50;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    .person-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        border: 2px solid #ff9a9e;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .dimension-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        color: #2c3e50;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .compatibility-info {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        padding: 1.5rem;
        border-radius: 12px;
        color: #2c3e50;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    </style>
    """, unsafe_allow_html=True)

    # 主标题
    st.markdown("""
    <div class="compatibility-header">
        <h1>💕 合盘分析</h1>
        <p>分析两人的命盘匹配度和相互适应性，探索关系的和谐之道</p>
    </div>
    """, unsafe_allow_html=True)

    # 功能说明
    st.markdown("""
    <div class="compatibility-info">
        <h3>🔮 合盘分析功能说明</h3>
        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-top: 1rem;">
            <div>💑 <strong>情侣夫妻</strong>：分析感情和谐度、性格互补性、婚姻前景</div>
            <div>🤝 <strong>合作伙伴</strong>：分析事业合作潜力、财运配合度</div>
            <div>👨‍👩‍👧‍👦 <strong>家庭关系</strong>：分析家庭和睦度、子女缘分</div>
            <div>🏥 <strong>健康影响</strong>：分析两人健康方面的相互影响</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    with st.form("compatibility_form"):
        st.markdown("""
        <div style="text-align: center; margin: 2rem 0;">
            <h3 style="color: #2c3e50;">👤 双方基本信息</h3>
            <p style="color: #7f8c8d;">请准确填写双方的出生信息，确保分析的准确性</p>
        </div>
        """, unsafe_allow_html=True)

        # 创建两列布局
        col1, col2 = st.columns(2)

        # A的信息
        with col1:
            st.markdown("""
            <div class="person-card">
                <h4 style="color: #e74c3c; text-align: center;">👨 第一人信息</h4>
            </div>
            """, unsafe_allow_html=True)

            name_a = st.text_input(
                "👤 姓名/称呼",
                value="",
                key="name_a",
                help="用于区分，可以是真名或昵称",
                placeholder="请输入姓名或昵称"
            )

            # 使用日期选择器，更方便
            from datetime import date
            birth_date_a = st.date_input(
                "📅 出生日期",
                value=date(1995, 3, 15),  # 默认1995年3月15日
                min_value=date(1900, 1, 1),
                max_value=date(2025, 12, 31),
                key="birth_date_a",
                help="请选择准确的出生日期"
            )

            col_a1, col_a2 = st.columns(2)
            with col_a1:
                gender_a = st.selectbox("👤 性别", ["男", "女"], index=0, key="gender_a")
            with col_a2:
                hour_a = st.selectbox(
                    "🕐 出生时辰",
                    ["子时(23-01)", "丑时(01-03)", "寅时(03-05)", "卯时(05-07)",
                     "辰时(07-09)", "巳时(09-11)", "午时(11-13)", "未时(13-15)",
                     "申时(15-17)", "酉时(17-19)", "戌时(19-21)", "亥时(21-23)"],
                    index=4,  # 默认辰时
                    key="hour_a",
                    help="请选择准确的出生时辰，影响分析准确性"
                )

        # B的信息
        with col2:
            st.markdown("""
            <div class="person-card">
                <h4 style="color: #3498db; text-align: center;">👩 第二人信息</h4>
            </div>
            """, unsafe_allow_html=True)

            name_b = st.text_input(
                "👤 姓名/称呼",
                value="",
                key="name_b",
                help="用于区分，可以是真名或昵称",
                placeholder="请输入姓名或昵称"
            )

            # 使用日期选择器，更方便
            birth_date_b = st.date_input(
                "📅 出生日期",
                value=date(1996, 8, 20),  # 默认1996年8月20日
                min_value=date(1900, 1, 1),
                max_value=date(2025, 12, 31),
                key="birth_date_b",
                help="请选择准确的出生日期"
            )

            col_b1, col_b2 = st.columns(2)
            with col_b1:
                gender_b = st.selectbox("👤 性别", ["女", "男"], index=0, key="gender_b")
            with col_b2:
                hour_b = st.selectbox(
                    "🕐 出生时辰",
                    ["子时(23-01)", "丑时(01-03)", "寅时(03-05)", "卯时(05-07)",
                     "辰时(07-09)", "巳时(09-11)", "午时(11-13)", "未时(13-15)",
                     "申时(15-17)", "酉时(17-19)", "戌时(19-21)", "亥时(21-23)"],
                    index=9,  # 默认戌时
                    key="hour_b",
                    help="请选择准确的出生时辰，影响分析准确性"
                )

        # 分析维度选择
        st.markdown("### 🎯 选择分析维度")

        analysis_dimensions = {
            "personality_compatibility": "💭 性格互补性 - 分析两人性格匹配度和相处模式",
            "emotional_harmony": "💕 感情和谐度 - 分析感情关系的和谐程度和发展前景",
            "wealth_cooperation": "💰 财运配合度 - 分析两人在财富方面的配合和影响",
            "career_partnership": "💼 事业合作潜力 - 分析两人在事业方面的合作可能性",
            "family_harmony": "🏠 家庭和睦度 - 分析两人组建家庭的和睦程度",
            "children_fortune": "👶 子女缘分 - 分析两人的子女运势和教育配合",
            "health_influence": "🏥 健康相互影响 - 分析两人健康方面的相互影响",
            "overall_compatibility": "🌟 综合匹配度 - 综合评估两人的整体匹配度"
        }

        selected_dimension = st.selectbox(
            "选择分析重点",
            list(analysis_dimensions.keys()),
            format_func=lambda x: analysis_dimensions[x],
            index=0,
            help="选择您最关心的分析维度，系统将重点分析该方面"
        )

        # 关系类型说明
        st.markdown("### 💡 常见合盘分析类型")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            **💑 情侣夫妻**
            - 感情和谐度
            - 性格互补性
            - 婚姻前景
            - 家庭和睦度
            """)

        with col2:
            st.markdown("""
            **🤝 商业合作**
            - 事业合作潜力
            - 财运配合度
            - 性格互补性
            - 合作风险评估
            """)

        with col3:
            st.markdown("""
            **👨‍👩‍👧‍👦 家庭关系**
            - 家庭和睦度
            - 子女缘分
            - 健康相互影响
            - 综合匹配度
            """)

        submitted = st.form_submit_button("💕 开始合盘分析", use_container_width=True)

    if submitted:
        # 验证输入
        if not name_a.strip() or not name_b.strip():
            st.error("❌ 请输入双方的姓名或称呼")
            return

        # 构建双方信息
        person_a_info = {
            "name": name_a.strip(),
            "year": str(birth_date_a.year),
            "month": str(birth_date_a.month),
            "day": str(birth_date_a.day),
            "hour": hour_a.split("(")[0],  # 提取时辰名称
            "gender": gender_a
        }

        person_b_info = {
            "name": name_b.strip(),
            "year": str(birth_date_b.year),
            "month": str(birth_date_b.month),
            "day": str(birth_date_b.day),
            "hour": hour_b.split("(")[0],  # 提取时辰名称
            "gender": gender_b
        }

        # 创建合盘分析
        result_id = create_compatibility_analysis(person_a_info, person_b_info, selected_dimension)
        if result_id:
            st.success("✅ 合盘分析已启动！正在为您分析...")
            # 直接跳转到详情页面
            st.session_state.current_view = 'detail'
            st.session_state.selected_record = result_id
            st.rerun()

def create_compatibility_analysis(person_a_info, person_b_info, analysis_dimension):
    """创建合盘分析"""
    try:
        # 显示详细的进度信息
        progress_placeholder = st.empty()
        progress_placeholder.info("💕 正在初始化合盘分析系统...")

        # 导入合盘分析引擎
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        from datetime import datetime
        import time

        progress_placeholder.info("🔮 正在加载合盘算法模块...")

        # 初始化合盘分析引擎
        compatibility_engine = CompatibilityAnalysisEngine()

        progress_placeholder.info("📊 正在计算双方命盘数据...")

        # 计算合盘数据
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)

        if not compatibility_data.get("success"):
            error_msg = compatibility_data.get('error', '未知错误')
            progress_placeholder.error(f"❌ 合盘数据计算失败: {error_msg}")
            st.error(f"❌ 合盘数据计算失败: {error_msg}")
            return None

        progress_placeholder.info("🤖 正在执行合盘分析...")

        # 显示合盘信息
        st.info(f"""
        **合盘分析信息**
        - {person_a_info['name']}：{person_a_info['year']}年{person_a_info['month']}月{person_a_info['day']}日 {person_a_info['hour']} {person_a_info['gender']}
        - {person_b_info['name']}：{person_b_info['year']}年{person_b_info['month']}月{person_b_info['day']}日 {person_b_info['hour']} {person_b_info['gender']}
        - 分析维度：{analysis_dimension}
        - 关系类型：{compatibility_data.get('relationship_type', '未知关系')}
        """)

        # 执行合盘分析
        print(f"💕 开始执行合盘分析: {person_a_info['name']} & {person_b_info['name']}")

        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        result = loop.run_until_complete(
            compatibility_engine.execute_compatibility_analysis(compatibility_data, analysis_dimension)
        )

        loop.close()

        print(f"💕 合盘分析执行结果: {result}")

        progress_placeholder.info("💾 正在保存合盘分析结果...")

        if result.get("success"):
            # 保存合盘结果到缓存
            from core.storage.calculation_cache import CalculationCache
            cache = CalculationCache()

            # 构建合盘专用的birth_info（用于缓存）
            compatibility_birth_info = {
                "person_a": f"{person_a_info['name']}({person_a_info['year']}-{person_a_info['month']}-{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']})",
                "person_b": f"{person_b_info['name']}({person_b_info['year']}-{person_b_info['month']}-{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']})",
                "analysis_dimension": analysis_dimension,
                "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
                "timestamp": datetime.now().isoformat()
            }

            # 保存合盘结果
            result_id = cache.save_result(
                user_id=f"compatibility_{int(time.time())}",
                session_id=f"compatibility_{person_a_info['name']}_{person_b_info['name']}_{int(time.time())}",
                calculation_type="compatibility",
                birth_info=compatibility_birth_info,
                raw_calculation=compatibility_data,
                detailed_analysis={
                    "compatibility_analysis": result.get("content", ""),
                    "analysis_dimension": analysis_dimension,
                    "person_a_info": person_a_info,
                    "person_b_info": person_b_info,
                    "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
                    "compatibility_result": result
                },
                summary=f"合盘分析：{person_a_info['name']} & {person_b_info['name']} - {analysis_dimension}",
                keywords=["合盘", "匹配度", analysis_dimension, compatibility_data.get('relationship_type', '未知关系')],
                confidence=0.9,
                chart_image_path=""  # 合盘分析暂不生成图片
            )

            progress_placeholder.success("✅ 合盘分析完成！")

            # 显示合盘结果摘要
            if result.get("content"):
                st.success("🎯 合盘分析结果已生成")
                with st.expander("📋 查看合盘分析摘要", expanded=True):
                    analysis_text = result.get("content", "")
                    if len(analysis_text) > 500:
                        st.write(analysis_text[:500] + "...")
                        st.info("完整结果请在详情页面查看")
                    else:
                        st.write(analysis_text)

            print(f"✅ 合盘分析成功保存: {result_id}")
            return result_id
        else:
            error_msg = result.get('error', '未知错误')
            progress_placeholder.error(f"❌ 合盘分析失败: {error_msg}")
            st.error(f"❌ 合盘分析失败: {error_msg}")
            print(f"❌ 合盘分析失败: {error_msg}")
            return None

    except Exception as e:
        st.error(f"❌ 创建合盘分析异常: {e}")
        print(f"❌ 合盘分析异常: {e}")
        import traceback
        error_details = traceback.format_exc()
        st.error(f"详细错误: {error_details}")
        print(f"详细错误: {error_details}")
        return None

def render_liuyao_divination():
    """渲染六爻卜卦页面"""
    # 添加美化的CSS样式
    st.markdown("""
    <style>
    .liuyao-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    .question-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        border: 2px solid #667eea;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    .method-card {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        color: #2c3e50;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .divination-info {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 1.5rem;
        border-radius: 12px;
        color: #2c3e50;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    </style>
    """, unsafe_allow_html=True)

    # 主标题
    st.markdown("""
    <div class="liuyao-header">
        <h1>🔮 六爻卜卦</h1>
        <p>传统占卜，问事求卦，探索未来的奥秘</p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("liuyao_form"):
        # 问题输入区域
        st.markdown("""
        <div class="question-card">
            <h3 style="color: #2c3e50; text-align: center;">🤔 请输入您要占卜的问题</h3>
            <p style="color: #7f8c8d; text-align: center;">问题越具体，卦象解读越准确</p>
        </div>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns([2, 1])

        with col1:
            question = st.text_area(
                "📝 占卜问题",
                placeholder="请详细描述您想要占卜的问题，如：\n• 工作：这次面试能否成功？\n• 感情：我和TA的关系会如何发展？\n• 财运：这次投资是否明智？\n• 健康：身体状况如何？",
                height=120,
                help="问题越具体，卦象解读越准确"
            )

        with col2:
            gender = st.selectbox(
                "👤 性别",
                ["男", "女"],
                help="用于卦象解读的参考"
            )

            divination_method = st.selectbox(
                "🎯 起卦方式",
                [
                    "🎲 数字起卦 - 随机生成",
                    "🔢 数字起卦 - 手动输入",
                    "🕐 时间起卦 - 基于当前时间",
                    "📱 手机起卦 - 基于设备信息"
                ],
                index=0,
                help="不同起卦方式适用于不同类型的问题"
            )

        # 如果选择手动输入数字，显示数字输入框
        numbers = []
        if "手动输入" in divination_method:
            st.markdown("### 🔢 请输入两个数字")
            st.info("💡 传统六爻数字起卦需要两个数字，可以是任意正整数（如：心中默想问题，随意想到的两个数字）")

            col1, col2 = st.columns(2)
            with col1:
                num1 = st.number_input("第一个数字", min_value=1, max_value=999, value=1, help="请输入1-999之间的数字")
            with col2:
                num2 = st.number_input("第二个数字", min_value=1, max_value=999, value=1, help="请输入1-999之间的数字")

            numbers = [num1, num2]

            st.markdown("**数字起卦说明**：")
            st.markdown("""
            - 第一个数字用于确定上卦
            - 第二个数字用于确定下卦
            - 两数之和用于确定动爻
            - 数字可以是任意正整数，建议随心而定
            """)

        # 问题类型提示
        st.markdown("""
        <div class="divination-info">
            <h3 style="text-align: center;">💡 常见占卜问题类型</h3>
            <p style="text-align: center; color: #7f8c8d;">选择您关心的问题类型，获得更精准的指导</p>
        </div>
        """, unsafe_allow_html=True)

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div class="method-card">
                <h4>💼 事业工作</h4>
                <ul style="text-align: left; margin: 0.5rem 0;">
                    <li>工作变动</li>
                    <li>升职加薪</li>
                    <li>创业投资</li>
                    <li>合作伙伴</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div class="method-card">
                <h4>💕 感情婚姻</h4>
                <ul style="text-align: left; margin: 0.5rem 0;">
                    <li>恋爱发展</li>
                    <li>婚姻状况</li>
                    <li>复合可能</li>
                    <li>桃花运势</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div class="method-card">
                <h4>💰 财运健康</h4>
                <ul style="text-align: left; margin: 0.5rem 0;">
                    <li>财运走势</li>
                    <li>投资理财</li>
                    <li>身体健康</li>
                    <li>出行安全</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        # 提交按钮
        submitted = st.form_submit_button(
            "🔮 开始占卜",
            use_container_width=True,
            type="primary"
        )

    if submitted:
        if not question.strip():
            st.error("❌ 请输入占卜问题")
            return

        # 构建占卜信息
        method_key = divination_method.split(" ")[0]  # 提取方法名

        divination_info = {
            "question": question.strip(),
            "gender": gender,
            "method": method_key,
            "timestamp": datetime.now().isoformat()
        }

        # 如果是手动输入数字，添加数字信息
        if "手动输入" in divination_method and numbers:
            divination_info["numbers"] = numbers
            divination_info["method"] = "numbers"  # 设置为数字起卦方法

        # 创建六爻占卜分析
        result_id = create_liuyao_divination(divination_info)
        if result_id:
            st.success("✅ 占卜已启动！正在为您解卦...")
            st.session_state.current_view = 'monitor'
            st.session_state.monitoring_task = result_id
            st.rerun()

def create_liuyao_divination(divination_info):
    """创建六爻占卜分析"""
    try:
        # 显示详细的进度信息
        progress_placeholder = st.empty()
        progress_placeholder.info("🔮 正在初始化六爻占卜系统...")

        # 直接调用六爻工具，不走命宫分析
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool
        from datetime import datetime
        import time

        progress_placeholder.info("📿 正在加载六爻算法模块...")

        # 初始化六爻工具
        liuyao_tool = HumanizedLiuyaoTool()

        progress_placeholder.info("🎯 正在构建占卜请求...")

        # 构建六爻分析的intent和context
        intent = {
            "intent": "liuyao",
            "confidence": 0.95,
            "original_message": divination_info['question'],
            "entities": {
                "question": divination_info['question'],
                "gender": divination_info.get('gender', '男'),
                "method": divination_info.get('method', '🎲'),
                "timestamp": divination_info.get('timestamp', datetime.now().isoformat())
            }
        }

        context = {
            "session_id": f"liuyao_{int(time.time())}",
            "user_question": divination_info['question'],
            "divination_method": divination_info.get('method', '🎲'),
            "user_gender": divination_info.get('gender', '男')
        }

        progress_placeholder.info("🔮 正在起卦占卜，请稍候...")

        # 显示占卜信息
        st.info(f"""
        **占卜信息**
        - 问题：{divination_info['question']}
        - 性别：{divination_info.get('gender', '男')}
        - 起卦方式：{divination_info.get('method', '🎲')}
        """)

        # 执行六爻占卜
        print(f"🔮 开始执行六爻占卜: {divination_info['question']}")
        result = liuyao_tool.execute(intent, context)
        print(f"🔮 六爻占卜执行结果: {result}")

        progress_placeholder.info("📊 正在处理占卜结果...")

        if result.get("success"):
            progress_placeholder.info("💾 正在保存占卜结果...")

            # 保存六爻结果到缓存
            from core.storage.calculation_cache import CalculationCache
            cache = CalculationCache()

            # 构建六爻专用的birth_info（用于缓存）
            liuyao_birth_info = {
                "question": divination_info['question'],
                "gender": divination_info.get('gender', '男'),
                "method": divination_info.get('method', '🎲'),
                "timestamp": divination_info.get('timestamp', datetime.now().isoformat())
            }

            # 保存六爻结果
            result_id = cache.save_result(
                user_id=f"liuyao_{int(time.time())}",
                session_id=context["session_id"],
                calculation_type="liuyao",
                birth_info=liuyao_birth_info,
                raw_calculation=result.get("chart_data", {}),
                detailed_analysis={
                    "liuyao_analysis": result.get("analysis", ""),
                    "question": divination_info['question'],
                    "chart_image": result.get("chart_image", ""),
                    "divination_result": result
                },
                summary=f"六爻占卜：{divination_info['question'][:50]}...",
                keywords=["六爻", "占卜", "卦象"],
                confidence=0.9,
                chart_image_path=result.get("chart_image", "")
            )

            progress_placeholder.success("✅ 六爻占卜完成！")

            # 显示占卜结果摘要
            if result.get("analysis"):
                st.success("🎯 占卜结果已生成")
                with st.expander("📋 查看占卜结果摘要", expanded=True):
                    analysis_text = result.get("analysis", "")
                    if len(analysis_text) > 500:
                        st.write(analysis_text[:500] + "...")
                        st.info("完整结果请在详情页面查看")
                    else:
                        st.write(analysis_text)

            print(f"✅ 六爻占卜成功保存: {result_id}")
            return result_id
        else:
            error_msg = result.get('error', '未知错误')
            progress_placeholder.error(f"❌ 六爻占卜失败: {error_msg}")
            st.error(f"❌ 六爻占卜失败: {error_msg}")
            print(f"❌ 六爻占卜失败: {error_msg}")
            return None

    except Exception as e:
        st.error(f"❌ 创建六爻占卜异常: {e}")
        print(f"❌ 六爻占卜异常: {e}")
        import traceback
        error_details = traceback.format_exc()
        st.error(f"详细错误: {error_details}")
        print(f"详细错误: {error_details}")
        return None

def create_new_analysis_with_type(birth_info, analysis_type):
    """支持不同分析类型的创建新分析"""
    try:
        # 根据分析类型显示不同的提示
        type_names = {
            "ziwei": "紫薇斗数分析",
            "bazi": "八字命理分析",
            "combined": "紫薇+八字综合分析"
        }

        with st.spinner(f"🔄 正在启动{type_names.get(analysis_type, '分析')}..."):
            from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
            from core.agents.base_agent import AgentMessage, MessageType

            # 初始化后台Agent
            calculator_agent = FortuneCalculatorAgent()

            # 构建分析请求
            session_id = f"web_{analysis_type}_{int(time.time())}"

            calculation_request = AgentMessage(
                message_id=f"web_{datetime.now().timestamp()}",
                message_type=MessageType.CALCULATION_REQUEST,
                sender_id="web_interface",
                receiver_id=calculator_agent.agent_id,
                content={
                    "user_message": f"Web{type_names.get(analysis_type, '分析')}请求：{birth_info}",
                    "birth_info": birth_info,
                    "calculation_type": analysis_type,
                    "session_id": session_id
                },
                timestamp=datetime.now().isoformat()
            )

            # 异步调用后台Agent
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            response = loop.run_until_complete(
                calculator_agent.process_message(calculation_request)
            )

            loop.close()

            if response.success:
                # 检查是否需要用户选择
                if response.data.get('cache_type') == 'different_type':
                    # 显示智能选择界面
                    st.info("🔍 **发现相同生辰信息的分析记录**")

                    message = response.data.get('message', '')
                    st.markdown(message)

                    # 提供选择按钮
                    col1, col2, col3 = st.columns(3)

                    options = response.data.get('options', {})
                    existing_analyses = response.data.get('existing_analyses', [])
                    requested_type = response.data.get('requested_type', '')
                    birth_info_data = response.data.get('birth_info', {})

                    with col1:
                        if st.button(f"🆕 {options.get('create_new', '创建新分析')}", key="force_create_new_main", use_container_width=True):
                            # 强制创建新分析 - 跳过缓存检查
                            force_result_id = force_create_new_analysis(birth_info_data, requested_type)
                            if force_result_id:
                                st.success(f"✅ 新{type_names.get(requested_type, '分析')}已启动！")
                                # 跳转到监控页面
                                st.session_state.current_view = 'monitor'
                                st.session_state.monitoring_task = force_result_id
                                st.rerun()
                                return force_result_id

                    with col2:
                        if st.button(f"👁️ {options.get('view_existing', '查看已有分析')}", key="view_existing_main", use_container_width=True):
                            # 跳转到已有分析
                            if existing_analyses:
                                st.session_state.current_view = 'detail'
                                st.session_state.selected_record = existing_analyses[0]['result_id']
                                st.rerun()
                                return None

                    with col3:
                        if st.button(f"🔮 {options.get('comprehensive', '综合分析')}", key="create_comprehensive_main", use_container_width=True):
                            # 创建综合分析
                            comp_result_id = force_create_new_analysis(birth_info_data, "combined")
                            if comp_result_id:
                                st.success(f"✅ 综合分析已启动！")
                                # 跳转到监控页面
                                st.session_state.current_view = 'monitor'
                                st.session_state.monitoring_task = comp_result_id
                                st.rerun()
                                return comp_result_id

                    # 显示已有分析的简要信息
                    if existing_analyses:
                        st.markdown("### 📋 已有分析概览")
                        for analysis in existing_analyses:
                            calc_type_name = {
                                'ziwei': '紫薇斗数',
                                'bazi': '八字命理',
                                'liuyao': '六爻占卜'
                            }.get(analysis['calculation_type'], analysis['calculation_type'])

                            with st.expander(f"🔮 {calc_type_name} - {analysis.get('created_at', '')[:10]}"):
                                st.write(analysis.get('summary', '暂无总结')[:200] + "...")

                    return None  # 等待用户选择

                # 正常的分析创建结果
                result_id = response.data.get("result_id")
                if result_id:
                    st.success(f"✅ {type_names.get(analysis_type, '分析')}已启动！")
                    return result_id
                else:
                    st.error("❌ 分析创建失败：未返回结果ID")
                    return None
            else:
                st.error(f"❌ 分析启动失败: {response.error}")
                return None

    except Exception as e:
        st.error(f"❌ 创建分析异常: {e}")
        return None

def force_create_new_analysis(birth_info, analysis_type):
    """强制创建新分析，跳过缓存检查"""
    try:
        import asyncio
        import time
        from datetime import datetime
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import AgentMessage, MessageType

        # 初始化后台Agent
        calculator_agent = FortuneCalculatorAgent()

        # 构建分析请求，添加强制创建标志
        session_id = f"web_force_{analysis_type}_{int(time.time())}"

        calculation_request = AgentMessage(
            message_id=f"web_force_{datetime.now().timestamp()}",
            message_type=MessageType.CALCULATION_REQUEST,
            sender_id="web_interface",
            receiver_id=calculator_agent.agent_id,
            content={
                "user_message": f"Web强制创建{analysis_type}分析请求：{birth_info}",
                "birth_info": birth_info,
                "calculation_type": analysis_type,
                "session_id": session_id,
                "force_create": True  # 强制创建标志
            },
            timestamp=datetime.now().isoformat()
        )

        # 异步调用后台Agent
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        response = loop.run_until_complete(
            calculator_agent.process_message(calculation_request)
        )

        loop.close()

        if response.success:
            result_id = response.data.get("result_id")
            st.success(f"✅ 强制创建{analysis_type}分析成功: {result_id[:8]}...")
            return result_id
        else:
            st.error(f"❌ 强制创建分析失败: {response.error}")
            return None

    except Exception as e:
        st.error(f"❌ 强制创建分析异常: {e}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")
        return None

def show_compatibility_analysis_content(cached_result):
    """显示合盘分析内容 - 结构化显示方式"""
    try:
        birth_info = cached_result.birth_info
        detailed_analysis = cached_result.detailed_analysis

        if isinstance(birth_info, dict) and isinstance(detailed_analysis, dict):
            # 显示合盘基本信息
            person_a_info = detailed_analysis.get("person_a_info", {})
            person_b_info = detailed_analysis.get("person_b_info", {})
            analysis_dimension = detailed_analysis.get("analysis_dimension", "")
            relationship_type = detailed_analysis.get("relationship_type", "")

            st.markdown(f"## 💕 合盘分析结果")

            # 显示双方信息
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                **👤 {person_a_info.get('name', 'A')}**
                - 出生：{person_a_info.get('year')}年{person_a_info.get('month')}月{person_a_info.get('day')}日 {person_a_info.get('hour')}
                - 性别：{person_a_info.get('gender')}
                """)

            with col2:
                st.markdown(f"""
                **👤 {person_b_info.get('name', 'B')}**
                - 出生：{person_b_info.get('year')}年{person_b_info.get('month')}月{person_b_info.get('day')}日 {person_b_info.get('hour')}
                - 性别：{person_b_info.get('gender')}
                """)

            # 显示分析信息
            st.markdown(f"""
            **🎯 分析维度**: {analysis_dimension}
            **🔗 关系类型**: {relationship_type}
            """)

            st.markdown("---")

            # 显示合盘分析结果 - 简洁清晰的显示方式
            compatibility_analysis = detailed_analysis.get("compatibility_analysis", "")

            if compatibility_analysis:
                st.markdown("## 📝 详细分析内容")

                # 直接显示完整内容，就像紫薇+八字融合分析一样
                word_count = len(compatibility_analysis)

                with st.expander(f"📋 查看完整合盘分析 ({word_count:,}字)", expanded=True):
                    # 直接显示markdown内容，保持原有格式
                    st.markdown(compatibility_analysis)

                # 显示分析维度说明
                dimension_descriptions = {
                    "personality_compatibility": "💭 性格互补性分析",
                    "emotional_harmony": "💕 感情和谐度分析",
                    "wealth_cooperation": "💰 财运配合度分析",
                    "career_partnership": "💼 事业合作潜力分析",
                    "family_harmony": "🏠 家庭和睦度分析",
                    "children_fortune": "👶 子女缘分分析",
                    "health_influence": "🏥 健康相互影响分析",
                    "overall_compatibility": "🌟 综合匹配度分析"
                }

                dimension_desc = dimension_descriptions.get(analysis_dimension, analysis_dimension)
                st.success(f"✅ {dimension_desc}完成")
            else:
                st.warning("⚠️ 合盘分析内容为空")
        else:
            st.error("❌ 合盘分析数据格式异常")

    except Exception as e:
        st.error(f"❌ 显示合盘分析失败: {e}")
        import traceback
        st.code(traceback.format_exc())

def show_liuyao_analysis_content(cached_result):
    """显示六爻占卜分析内容"""
    try:
        detailed_analysis = cached_result.detailed_analysis

        if not detailed_analysis:
            st.warning("⚠️ 暂无详细分析内容")
            return

        # 获取六爻分析结果
        liuyao_analysis = detailed_analysis.get("liuyao_analysis", "")
        divination_result = detailed_analysis.get("divination_result", {})

        # 显示卦象信息
        st.markdown("### 🎯 卦象信息")

        # 尝试从分析结果中提取卦象数据
        hexagram_info = divination_result.get("formatted_output", "")
        if hexagram_info:
            # 使用代码块显示格式化的卦象
            st.code(hexagram_info, language="text")

        # 显示卦象图片（如果有）
        chart_image = detailed_analysis.get("chart_image", "")
        if chart_image and os.path.exists(chart_image):
            st.markdown("### 🎯 卦象图")
            try:
                st.image(chart_image, caption="六爻卦象图", use_column_width=True)
            except Exception as e:
                st.warning(f"⚠️ 卦象图显示失败: {e}")

        # 显示占卜分析
        if liuyao_analysis:
            st.markdown("### 📋 占卜分析")

            # 清理和格式化分析内容
            clean_content = clean_markdown_text(liuyao_analysis)

            # 将内容分割为段落并格式化
            paragraphs = clean_content.split('\n\n')
            content_html = '<div class="content">\n'

            for para in paragraphs:
                para = para.strip()
                if para:
                    # 检查是否是列表项
                    if para.startswith('• '):
                        list_content = para[2:].strip()
                        content_html += f'<div class="list-item">{list_content}</div>\n'
                    else:
                        # 普通段落
                        para_lines = para.split('\n')
                        formatted_para = []
                        for line in para_lines:
                            line = line.strip()
                            if line:
                                formatted_para.append(line)
                        para_text = ' '.join(formatted_para)
                        content_html += f'<p>{para_text}</p>\n'

            content_html += '</div>\n'

            # 显示格式化的内容
            st.markdown(content_html, unsafe_allow_html=True)

        # 显示原始占卜结果（调试用）
        if divination_result and st.checkbox("🔧 显示详细技术信息", key="show_liuyao_debug"):
            st.markdown("### 🔧 技术详情")
            with st.expander("查看原始占卜数据"):
                st.json(divination_result)

        # 显示占卜总结
        summary = cached_result.summary
        if summary:
            st.markdown("### 📝 占卜总结")
            st.info(summary)

    except Exception as e:
        st.error(f"❌ 显示六爻分析内容失败: {e}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")

def create_new_analysis_simple(birth_info):
    """简化的创建新分析 - 兼容性保留"""
    return create_new_analysis_with_type(birth_info, "ziwei")

def show_chart_image_simple(cached_result):
    """简化的排盘图表显示 - 支持HTML和图片"""
    st.markdown("### 🎨 排盘图表")

    chart_path = cached_result.chart_image_path

    # 检查是否是HTML文件
    if chart_path and chart_path.endswith('.html') and os.path.exists(chart_path):
        st.markdown("#### 🌐 HTML可视化图表")
        try:
            with open(chart_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            st.components.v1.html(html_content, height=800, scrolling=True)
            return
        except Exception as e:
            st.warning(f"⚠️ HTML图表显示失败: {e}")

    # 如果不是HTML，尝试显示图片
    image_path = chart_path

    if not image_path:
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            # 优先查找HTML文件
            html_files = [f for f in os.listdir(charts_dir) if f.startswith("fusion_chart_") and f.endswith(".html")]
            if html_files:
                html_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_dir, x)), reverse=True)
                html_path = os.path.join(charts_dir, html_files[0])
                try:
                    with open(html_path, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    st.markdown("#### 🌐 HTML可视化图表")
                    st.components.v1.html(html_content, height=800, scrolling=True)
                    return
                except Exception as e:
                    st.warning(f"⚠️ HTML图表显示失败: {e}")

            # 如果没有HTML，查找图片文件
            chart_files = [f for f in os.listdir(charts_dir) if f.startswith("integrated_chart_") and f.endswith(".png")]
            if chart_files:
                chart_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_dir, x)), reverse=True)
                image_path = os.path.join(charts_dir, chart_files[0])

    if image_path and os.path.exists(image_path):
        st.markdown("#### 🖼️ 传统图片格式")
        st.image(image_path, caption="紫薇斗数排盘图", use_container_width=True)
    else:
        st.warning("⚠️ 排盘图表生成中...")

def show_analysis_content_simple(cached_result):
    """按需生成的分析内容显示 - 12个角度按需点击生成"""
    st.markdown("### 📝 分析内容")

    # 检查生成状态并更新界面
    check_generation_status()

    detailed_analysis = cached_result.detailed_analysis
    if isinstance(detailed_analysis, dict):
        angle_analyses = detailed_analysis.get("angle_analyses", {})

        # 完整的12个角度名称映射
        angle_names = {
            "personality_destiny": "🏛️ 命宫分析 - 性格命运核心特征",
            "wealth_fortune": "💰 财富分析 - 财运状况与理财投资",
            "marriage_love": "💕 婚姻分析 - 感情婚姻与桃花运势",
            "health_wellness": "🏥 健康分析 - 身体状况与养生建议",
            "career_achievement": "💼 事业分析 - 职业发展与成就潜力",
            "children_creativity": "👶 子女分析 - 生育状况与子女关系",
            "interpersonal_relationship": "🤝 人际分析 - 社交关系与贵人运",
            "education_learning": "📚 学业分析 - 教育学习与知识发展",
            "family_environment": "🏠 家庭分析 - 家庭环境与亲情关系",
            "travel_relocation": "✈️ 迁移分析 - 搬迁旅行与环境变化",
            "spiritual_blessing": "🙏 精神分析 - 精神状态与福德运势",
            "authority_parents": "👑 权威分析 - 领导能力与权威地位"
        }

        # 按需生成的12个角度 - 3列4行紧凑布局
        result_id = st.session_state.selected_record

        st.markdown("### 🎯 12个分析角度")

        # 3列4行布局
        angle_list = list(angle_names.items())
        for row in range(4):
            cols = st.columns(3)
            for col_idx in range(3):
                angle_idx = row * 3 + col_idx
                if angle_idx < len(angle_list):
                    angle_key, angle_name = angle_list[angle_idx]

                    with cols[col_idx]:
                        # 检查是否已生成
                        is_generated = angle_key in angle_analyses and angle_analyses[angle_key] and len(angle_analyses[angle_key]) > 100

                        # 检查是否正在生成
                        is_generating = (
                            "generating_analyses" in st.session_state and
                            angle_key in st.session_state.generating_analyses and
                            st.session_state.generating_analyses[angle_key]["status"] == "generating"
                        )

                        # 构建紧凑的按钮文本
                        angle_title = angle_name.split(' - ')[0]

                        if is_generated:
                            word_count = len(angle_analyses[angle_key])

                            # 主按钮：查看内容
                            button_text = f"✅ {angle_title}\n({word_count}字)"

                            # 使用expander来提供重试选项
                            with st.expander(f"✅ {angle_title} ({word_count}字)", expanded=False):
                                col1, col2 = st.columns(2)
                                with col1:
                                    if st.button("📖 查看内容", key=f"view_{angle_key}", use_container_width=True):
                                        st.session_state[f"show_content_{angle_key}"] = True
                                        st.rerun()
                                with col2:
                                    if st.button("🔄 重新生成", key=f"retry_{angle_key}", use_container_width=True):
                                        generate_single_analysis(result_id, angle_key, angle_name)
                                        st.rerun()

                        elif is_generating:
                            button_text = f"🔄 {angle_title}\n生成中..."
                            if st.button(button_text, key=f"cancel_{angle_key}", use_container_width=True):
                                if angle_key in st.session_state.generating_analyses:
                                    del st.session_state.generating_analyses[angle_key]
                                st.rerun()

                        else:
                            button_text = f"⏳ {angle_title}\n待生成"
                            if st.button(button_text, key=f"generate_{angle_key}", use_container_width=True):
                                start_async_analysis_generation(result_id, angle_key, angle_name)

        # 显示选中的分析内容
        for angle_key, angle_name in angle_names.items():
            if st.session_state.get(f"show_content_{angle_key}", False):
                if angle_key in angle_analyses and angle_analyses[angle_key]:
                    content = angle_analyses[angle_key]
                    word_count = len(content)

                    st.markdown(f"### {angle_name}")

                    # 关闭按钮
                    if st.button("❌ 关闭", key=f"close_{angle_key}"):
                        st.session_state[f"show_content_{angle_key}"] = False
                        st.rerun()

                    # 显示内容
                    with st.container():
                        st.markdown(f"**字数**: {word_count}")
                        st.markdown("---")
                        st.markdown(content)
                        st.markdown("---")

        # 统计信息
        completed_angles = len([v for v in angle_analyses.values() if v and len(v) > 100])
        total_words = sum(len(v) for v in angle_analyses.values() if v)

        st.markdown(f"""
        ---
        **📊 分析统计**: {completed_angles}/12 个角度完成，总计 {total_words:,} 字
        """)

        # 添加即时聊天功能
        add_interactive_chat(cached_result)

    else:
        st.warning("⚠️ 分析数据格式异常")

def start_async_analysis_generation(result_id, angle_key, angle_name):
    """启动异步分析生成"""
    try:
        # 设置生成状态
        if "generating_analyses" not in st.session_state:
            st.session_state.generating_analyses = {}

        st.session_state.generating_analyses[angle_key] = {
            "status": "generating",
            "start_time": time.time(),
            "angle_name": angle_name
        }

        # 显示开始生成的消息
        st.success(f"🚀 {angle_name.split(' - ')[0]} 开始生成...")

        # 启动后台线程生成
        import threading

        def background_generate():
            try:
                success = generate_single_analysis(result_id, angle_key, angle_name)

                # 使用文件系统来传递状态，避免直接访问session_state
                import json
                import os
                status_file = f"temp_status_{angle_key}_{result_id}.json"

                if success:
                    status_data = {
                        "status": "completed",
                        "end_time": time.time(),
                        "angle_name": angle_name
                    }
                else:
                    status_data = {
                        "status": "failed",
                        "end_time": time.time(),
                        "angle_name": angle_name
                    }

                # 保存状态到临时文件
                with open(status_file, 'w', encoding='utf-8') as f:
                    json.dump(status_data, f, ensure_ascii=False, indent=2)

            except Exception as e:
                # 保存错误状态到临时文件
                import json
                status_file = f"temp_status_{angle_key}_{result_id}.json"
                status_data = {
                    "status": "failed",
                    "end_time": time.time(),
                    "angle_name": angle_name,
                    "error": str(e)
                }
                with open(status_file, 'w', encoding='utf-8') as f:
                    json.dump(status_data, f, ensure_ascii=False, indent=2)

        # 启动后台线程
        thread = threading.Thread(target=background_generate, daemon=True)
        thread.start()

        # 设置自动刷新
        time.sleep(0.5)  # 短暂延迟让用户看到开始消息
        st.rerun()

    except Exception as e:
        st.error(f"❌ 启动生成失败: {e}")

def check_generation_status():
    """检查生成状态并更新界面"""
    if "generating_analyses" not in st.session_state:
        return

    # 检查临时状态文件
    import json
    import glob

    # 查找所有临时状态文件
    status_files = glob.glob("temp_status_*.json")

    for status_file in status_files:
        try:
            # 从文件名提取angle_key
            filename = os.path.basename(status_file)
            if filename.startswith("temp_status_") and filename.endswith(".json"):
                parts = filename[12:-5].split("_")  # 去掉"temp_status_"和".json"
                if len(parts) >= 2:
                    angle_key = parts[0]

                    # 读取状态文件
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status_data = json.load(f)

                    # 检查是否是当前正在生成的任务
                    if angle_key in st.session_state.generating_analyses:
                        # 更新session_state中的状态
                        st.session_state.generating_analyses[angle_key].update(status_data)

                    # 删除临时文件
                    os.remove(status_file)

        except Exception as file_error:
            print(f"处理状态文件 {status_file} 时出错: {file_error}")
            # 删除有问题的文件
            try:
                os.remove(status_file)
            except:
                pass

    completed_analyses = []
    failed_analyses = []

    for angle_key, status_info in st.session_state.generating_analyses.items():
        if status_info["status"] == "completed":
            completed_analyses.append(angle_key)
        elif status_info["status"] == "failed":
            failed_analyses.append(angle_key)

    # 显示完成的分析
    for angle_key in completed_analyses:
        status_info = st.session_state.generating_analyses[angle_key]
        duration = status_info.get("end_time", 0) - status_info.get("start_time", 0)
        st.success(f"✅ {status_info['angle_name'].split(' - ')[0]} 生成完成！({duration:.1f}秒)")

        # 清除已完成的状态
        del st.session_state.generating_analyses[angle_key]

    # 显示失败的分析
    for angle_key in failed_analyses:
        status_info = st.session_state.generating_analyses[angle_key]
        error_msg = status_info.get("error", "未知错误")
        st.error(f"❌ {status_info['angle_name'].split(' - ')[0]} 生成失败: {error_msg}")

        # 清除已失败的状态
        del st.session_state.generating_analyses[angle_key]

    # 如果有状态更新，刷新页面
    if completed_analyses or failed_analyses:
        time.sleep(1)  # 让用户看到消息
        st.rerun()

def generate_single_analysis(result_id, angle_key, angle_name):
    """生成单个角度的分析"""
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent

        # 显示生成状态
        with st.spinner(f"正在生成 {angle_name.split(' - ')[0]}..."):
            # 使用全局缓存实例，确保缓存一致性
            if 'global_calculator_agent' not in st.session_state:
                st.session_state.global_calculator_agent = FortuneCalculatorAgent()

            calculator_agent = st.session_state.global_calculator_agent
            cached_result = calculator_agent.cache.get_result(result_id)

            if not cached_result:
                st.error("❌ 未找到分析数据")
                return False

            # 获取原始数据
            raw_data = cached_result.raw_calculation
            birth_info = cached_result.birth_info

            # 生成单个角度分析
            import asyncio

            async def generate_async():
                return await calculator_agent._analyze_single_angle(
                    angle_name.split(' - ')[0],
                    angle_key,
                    angle_name.split(' - ')[1] if ' - ' in angle_name else angle_name,
                    raw_data,
                    birth_info,
                    "紫薇+八字融合分析"
                )

            # 运行异步函数
            try:
                # 检查是否已有运行中的事件循环
                try:
                    loop = asyncio.get_running_loop()
                    # 如果有运行中的循环，使用 asyncio.create_task
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, generate_async())
                        analysis_result = future.result()
                except RuntimeError:
                    # 没有运行中的循环，直接运行
                    analysis_result = asyncio.run(generate_async())
            except Exception as e:
                st.error(f"❌ 异步执行失败: {e}")
                return False

            if analysis_result and len(analysis_result) > 100:
                # 更新缓存中的分析结果
                if not hasattr(cached_result, 'detailed_analysis') or not cached_result.detailed_analysis:
                    cached_result.detailed_analysis = {"angle_analyses": {}}
                elif not isinstance(cached_result.detailed_analysis, dict):
                    cached_result.detailed_analysis = {"angle_analyses": {}}

                if "angle_analyses" not in cached_result.detailed_analysis:
                    cached_result.detailed_analysis["angle_analyses"] = {}

                cached_result.detailed_analysis["angle_analyses"][angle_key] = analysis_result

                # 更新现有缓存记录
                try:
                    # 直接更新内存中的缓存
                    if hasattr(calculator_agent.cache, 'memory_cache') and result_id in calculator_agent.cache.memory_cache:
                        calculator_agent.cache.memory_cache[result_id].detailed_analysis = cached_result.detailed_analysis
                        print(f"✅ 内存缓存更新成功: {result_id}")

                    # 保存到文件
                    import json
                    import os
                    cache_file = os.path.join(calculator_agent.cache.cache_dir, f"{result_id}.json")
                    if os.path.exists(cache_file):
                        with open(cache_file, 'r', encoding='utf-8') as f:
                            cache_data = json.load(f)
                        cache_data['detailed_analysis'] = cached_result.detailed_analysis
                        cache_data['updated_at'] = datetime.now().isoformat()
                        with open(cache_file, 'w', encoding='utf-8') as f:
                            json.dump(cache_data, f, ensure_ascii=False, indent=2)
                        print(f"✅ 文件缓存更新成功: {result_id}")
                    else:
                        print(f"⚠️ 缓存文件不存在: {cache_file}")

                except Exception as cache_error:
                    print(f"⚠️ 缓存更新失败: {cache_error}")
                    # 继续执行，不影响主要功能

                st.success(f"✅ {angle_name.split(' - ')[0]} 生成成功！({len(analysis_result)}字)")
                return True
            else:
                st.error(f"❌ {angle_name.split(' - ')[0]} 生成失败或内容过短")
                return False

    except Exception as e:
        st.error(f"❌ 生成分析失败: {e}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")
        return False

def add_interactive_chat(cached_result):
    """添加即时聊天功能"""
    st.markdown("---")
    st.markdown("### 💬 即时互动")
    st.markdown("基于您的排盘信息，可以随时提问任何相关问题")

    # 初始化聊天历史
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []

    # 显示聊天历史
    if st.session_state.chat_history:
        st.markdown("#### 💭 对话记录")
        # 使用expander来节省空间，但默认展开
        with st.expander(f"📝 共 {len(st.session_state.chat_history)} 条对话", expanded=True):
            for i, (question, answer) in enumerate(st.session_state.chat_history):
                # 使用不同的背景色区分问答，确保文字颜色清晰可见
                st.markdown(f"""
                <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin: 5px 0; color: #333;">
                    <strong style="color: #2c3e50;">👤 您</strong>: <span style="color: #2c3e50;">{question}</span>
                </div>
                """, unsafe_allow_html=True)

                st.markdown(f"""
                <div style="background-color: #e8f4fd; padding: 10px; border-radius: 5px; margin: 5px 0 15px 0; color: #333; border-left: 4px solid #4CAF50;">
                    <strong style="color: #2c3e50;">🤖 分析师</strong>: <span style="color: #2c3e50;">{answer}</span>
                </div>
                """, unsafe_allow_html=True)

    # 输入框和发送按钮
    with st.form("chat_form", clear_on_submit=True):
        user_question = st.text_area(
            "请输入您的问题",
            placeholder="例如：我什么时候适合结婚？我的财运如何？我适合什么职业？",
            height=100
        )

        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            submit_chat = st.form_submit_button("💬 发送", use_container_width=True)
        with col2:
            clear_chat = st.form_submit_button("🗑️ 清空", use_container_width=True)

    # 处理聊天
    if submit_chat and user_question.strip():
        with st.spinner("🤔 正在思考..."):
            answer = generate_chat_response(cached_result, user_question.strip())
            if answer:
                st.session_state.chat_history.append((user_question.strip(), answer))
                st.success("✅ 回复已生成！")
                time.sleep(0.5)  # 短暂延迟让用户看到成功消息
                st.rerun()

    # 清空聊天记录
    if clear_chat:
        st.session_state.chat_history = []
        st.success("✅ 聊天记录已清空")
        st.rerun()

def generate_chat_response(cached_result, question):
    """生成聊天回复 - 使用知识库方案"""
    try:
        from core.nlu.llm_client import LLMClient
        from core.chat.knowledge_base import ChatKnowledgeBase
        import logging

        logger = logging.getLogger(__name__)

        # 创建并加载知识库
        knowledge_base = ChatKnowledgeBase()
        knowledge_base.load_from_cache_result(cached_result)

        # 获取知识库统计
        kb_stats = knowledge_base.get_stats()
        logger.info(f"知识库加载完成: {kb_stats}")

        # 格式化知识库为LLM可用文本
        knowledge_text = knowledge_base.format_for_llm(max_length=1500)  # 限制长度避免超token

        birth_info = cached_result.birth_info

        # 构建优化的系统提示词
        system_prompt = f"""
你是一位资深命理分析师，必须基于用户的专属知识库提供专业分析。

【用户基本信息】
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}
- 性别：{birth_info.get('gender')}

{knowledge_text}

【核心要求 - 必须遵守】
1. 必须引用知识库中的具体信息（如：天相星、丁火日主、土偏旺等）
2. 禁止编造不在知识库中的信息
3. 重点使用★标记的核心命理配置
4. 结合紫薇宫位和八字五行进行综合分析
5. 回答要详细专业，控制在800-1200字之间
6. 结构：现状分析-深层解读-实用建议-总结展望

【回答模板】
开头：根据您的排盘信息显示...（引用具体数据）
分析：从紫薇斗数看...从八字命理看...（使用知识库信息）
建议：基于您的...配置，建议...（具体可操作的建议）
总结：综合来看...（整体展望）

请严格按照知识库信息回答，确保专业性和准确性。
"""

        # 调用LLM - 使用更大的max_tokens确保回复完整
        llm_client = LLMClient()
        response = llm_client.chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question}
            ],
            temperature=0.7,
            max_tokens=4096  # 增加到4096确保回复完整，避免截断
        )

        if response and response.strip():
            # 检查回复是否完整（避免截断）
            response_text = response.strip()

            # 检查是否被截断的几种情况
            truncation_indicators = [
                "...",  # 省略号
                "。。。",  # 中文省略号
                response_text.endswith("，"),  # 以逗号结尾
                response_text.endswith("、"),  # 以顿号结尾
                response_text.endswith("和"),  # 以"和"结尾
                response_text.endswith("或"),  # 以"或"结尾
                response_text.endswith("但"),  # 以"但"结尾
                response_text.endswith("而"),  # 以"而"结尾
                len(response_text) < 200,  # 回复过短
            ]

            # 如果检测到可能的截断，添加提示
            if any(truncation_indicators):
                response_text += "\n\n💡 如需更详细的分析，请继续提问或要求我展开说明。"

            return response_text
        else:
            return "抱歉，我现在无法回答这个问题，请稍后再试。"

    except Exception as e:
        return f"抱歉，回答生成失败：{str(e)}"

def format_chart_data_for_chat(raw_data):
    """格式化排盘数据用于聊天 - 增强版，提供完整详细信息"""
    try:
        if not raw_data or not raw_data.get("success"):
            return "排盘数据不完整"

        result = "【完整排盘信息】\n"

        # 提取八字信息 - 优先显示
        bazi_info = ""
        if "bazi_analysis" in raw_data:
            bazi_data = raw_data["bazi_analysis"].get("bazi_info", {})
            if bazi_data:
                # 尝试获取完整八字信息
                year_pillar = bazi_data.get('year_pillar', '')
                month_pillar = bazi_data.get('month_pillar', '')
                day_pillar = bazi_data.get('day_pillar', '')
                hour_pillar = bazi_data.get('hour_pillar', '')

                if year_pillar and month_pillar and day_pillar and hour_pillar:
                    bazi_info = f"八字四柱：{year_pillar} {month_pillar} {day_pillar} {hour_pillar}\n"

                    # 添加五行分析
                    elements = bazi_data.get('elements', {})
                    if elements:
                        bazi_info += f"五行分布：{elements}\n"

                    # 添加五行分析
                    elements = bazi_data.get('elements', {})
                    if elements:
                        bazi_info += f"五行分布：{elements}\n"

                    # 添加日主分析
                    day_master = bazi_data.get('day_master', '')
                    if day_master:
                        bazi_info += f"日主：{day_master}\n"

                    # 添加大运信息
                    dayun = bazi_data.get('dayun', [])
                    if dayun:
                        current_dayun = None
                        for dy in dayun:
                            if isinstance(dy, dict) and dy.get('start_age', 0) <= 36 <= dy.get('end_age', 100):
                                current_dayun = dy
                                break
                        if current_dayun:
                            bazi_info += f"当前大运：{current_dayun.get('pillar', '')} ({current_dayun.get('start_age', '')}-{current_dayun.get('end_age', '')}岁)\n"

                    # 添加流年信息
                    liunian = bazi_data.get('liunian', {})
                    if liunian:
                        current_year = liunian.get('2024', '') or liunian.get('2025', '')
                        if current_year:
                            bazi_info += f"近期流年：{current_year}\n"

                    # 添加格局信息
                    pattern = bazi_data.get('pattern', '')
                    if pattern:
                        bazi_info += f"命局格局：{pattern}\n"
                else:
                    # 尝试其他格式
                    chinese_date = bazi_data.get('chinese_date', '')
                    if chinese_date:
                        bazi_info = f"八字：{chinese_date}\n"

        # 提取紫薇斗数信息
        ziwei_info = ""
        if "ziwei_analysis" in raw_data:
            ziwei_data = raw_data["ziwei_analysis"]

            # 尝试多种数据结构
            palaces = None
            if isinstance(ziwei_data, dict):
                palaces = ziwei_data.get("palaces", {})

                # 如果palaces为空，尝试其他可能的字段名
                if not palaces:
                    palaces = ziwei_data.get("宫位", {})
                if not palaces:
                    palaces = ziwei_data.get("palace_data", {})

            if palaces and isinstance(palaces, dict):
                # 重要宫位信息
                important_palaces = ["命宫", "财帛宫", "夫妻宫", "事业宫", "迁移宫", "疾厄宫"]

                for palace in important_palaces:
                    if palace in palaces:
                        palace_data = palaces[palace]
                        if isinstance(palace_data, dict):
                            stars = palace_data.get("主星", []) or palace_data.get("major_stars", [])
                            minor_stars = palace_data.get("辅星", []) or palace_data.get("minor_stars", [])

                            if stars or minor_stars:
                                star_info = ""
                                if stars:
                                    star_info += f"主星：{', '.join(stars)}"
                                if minor_stars:
                                    if star_info:
                                        star_info += f"，辅星：{', '.join(minor_stars[:3])}"  # 只显示前3个辅星
                                    else:
                                        star_info += f"辅星：{', '.join(minor_stars[:3])}"

                                ziwei_info += f"{palace}：{star_info}\n"

            # 如果没有获取到宫位信息，尝试其他字段
            if not ziwei_info and isinstance(ziwei_data, dict):
                # 尝试获取基本信息
                basic_info = ziwei_data.get("basic_info", {})
                if basic_info:
                    ziwei_info = f"紫薇基本信息：{str(basic_info)[:200]}...\n"

                # 尝试获取命盘信息
                chart_info = ziwei_data.get("chart_info", {})
                if chart_info:
                    ziwei_info += f"命盘信息：{str(chart_info)[:200]}...\n"

                # 如果还是没有，显示可用字段
                if not ziwei_info:
                    available_keys = list(ziwei_data.keys())
                    ziwei_info = f"紫薇数据字段：{available_keys[:5]}\n"

        # 构建返回信息
        if bazi_info:
            result += f"【八字命理】\n{bazi_info}\n"

        if ziwei_info:
            result += f"【紫薇斗数】\n{ziwei_info}\n"

        # 添加融合分析信息
        if "fusion_analysis" in raw_data:
            fusion_data = raw_data["fusion_analysis"]
            if isinstance(fusion_data, dict):
                key_points = fusion_data.get("key_points", [])
                if key_points:
                    result += f"【融合要点】\n"
                    for point in key_points[:3]:  # 只显示前3个要点
                        result += f"• {point}\n"
                    result += "\n"

        if not bazi_info and not ziwei_info:
            result += "排盘数据正在解析中...\n"
            # 显示原始数据的键，帮助调试
            result += f"可用数据字段：{list(raw_data.keys())}"

        return result

    except Exception as e:
        return f"排盘数据解析异常：{str(e)}\n可用数据字段：{list(raw_data.keys()) if raw_data else '无'}"


def clean_markdown_text(text):
    """清理markdown格式，转换为可读文本，优化段落结构"""
    if not text:
        return ""

    import re

    # 移除markdown标记
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 粗体
    text = re.sub(r'\*(.*?)\*', r'\1', text)      # 斜体
    text = re.sub(r'`(.*?)`', r'\1', text)        # 代码
    text = re.sub(r'#{1,6}\s*(.*)', r'\1', text)  # 标题

    # 处理列表项
    text = re.sub(r'^\s*[-*+]\s+', '• ', text, flags=re.MULTILINE)  # 列表
    text = re.sub(r'^\s*\d+\.\s+', '• ', text, flags=re.MULTILINE)  # 数字列表

    # 清理多余的空行和空白字符
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    text = re.sub(r'[ \t]+', ' ', text)  # 多个空格合并为一个

    # 分割段落并重新组织
    paragraphs = text.split('\n\n')
    cleaned_paragraphs = []

    for para in paragraphs:
        para = para.strip()
        if para:
            # 处理列表项
            if para.startswith('• '):
                # 列表项可能包含多行，需要特殊处理
                lines = para.split('\n')
                formatted_lines = []
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line:
                        if i == 0:
                            formatted_lines.append(line)
                        else:
                            # 续行缩进
                            formatted_lines.append('  ' + line)
                cleaned_paragraphs.append('\n'.join(formatted_lines))
            else:
                # 普通段落，处理内部换行
                lines = para.split('\n')
                formatted_lines = []
                for line in lines:
                    line = line.strip()
                    if line:
                        formatted_lines.append(line)

                # 将多行合并为一个段落
                if formatted_lines:
                    cleaned_paragraphs.append(' '.join(formatted_lines))

    return '\n\n'.join(cleaned_paragraphs)

def export_single_to_word(record):
    """导出单人为Word文档"""
    try:
        from docx import Document
        from docx.shared import Inches
        from docx.enum.text import WD_ALIGN_PARAGRAPH

        # 创建Word文档
        doc = Document()

        # 加载详细数据
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(record['result_id'])

        if not cached_result:
            st.error("❌ 无法加载分析数据")
            return None

        birth_info = cached_result.birth_info

        # 添加标题
        title = doc.add_heading('紫薇斗数分析报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加个人信息
        doc.add_heading(f'{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命', level=1)

        # 添加基本信息表格
        info_table = doc.add_table(rows=4, cols=2)
        info_table.style = 'Table Grid'

        info_data = [
            ('生辰信息', f'{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]}'),
            ('性别', birth_info["gender"]),
            ('分析完成度', f'{record["completed_angles"]}/12'),
            ('总字数', f'{record["total_words"]:,}字')
        ]

        for i, (key, value) in enumerate(info_data):
            info_table.cell(i, 0).text = key
            info_table.cell(i, 1).text = value

        doc.add_paragraph()  # 空行

        # 添加分析内容
        detailed_analysis = cached_result.detailed_analysis
        if isinstance(detailed_analysis, dict):
            angle_analyses = detailed_analysis.get("angle_analyses", {})

            angle_names = {
                "personality_destiny": "命宫分析 - 性格命运核心特征",
                "wealth_fortune": "财富分析 - 财运状况与理财投资",
                "marriage_love": "婚姻分析 - 感情婚姻与桃花运势",
                "health_wellness": "健康分析 - 身体状况与养生建议",
                "career_achievement": "事业分析 - 职业发展与成就潜力",
                "children_creativity": "子女分析 - 生育状况与子女关系",
                "interpersonal_relationship": "人际分析 - 社交关系与贵人运",
                "education_learning": "学业分析 - 教育学习与知识发展",
                "family_environment": "家庭分析 - 家庭环境与亲情关系",
                "travel_relocation": "迁移分析 - 搬迁旅行与环境变化",
                "spiritual_blessing": "精神分析 - 精神状态与福德运势",
                "authority_parents": "权威分析 - 领导能力与权威地位"
            }

            for angle_key, angle_name in angle_names.items():
                if angle_key in angle_analyses and angle_analyses[angle_key]:
                    # 添加角度标题
                    doc.add_heading(angle_name, level=2)

                    # 清理并添加内容
                    clean_content = clean_markdown_text(angle_analyses[angle_key])
                    doc.add_paragraph(clean_content)

                    doc.add_paragraph()  # 空行分隔

        # 添加页脚
        doc.add_paragraph()
        footer_para = doc.add_paragraph(f'生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}')
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 保存文件
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        birth_info_str = record['birth_info'].replace(' ', '_')
        filename = f"紫薇分析_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(export_dir, filename)
        doc.save(filepath)

        return filepath

    except Exception as e:
        st.error(f"❌ 导出Word失败: {e}")
        return None

def export_single_to_pdf(record):
    """导出单人为PDF文档 - 使用简化方案避免中文字体问题"""
    try:
        # 先尝试导出为HTML再转PDF的方案
        return export_single_to_pdf_via_html(record)

    except Exception as e:
        st.error(f"❌ 导出PDF失败: {e}")
        return None

def export_single_to_pdf_via_html(record):
    """通过HTML转PDF的方式导出"""
    try:
        # 加载详细数据
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(record['result_id'])

        if not cached_result:
            st.error("❌ 无法加载分析数据")
            return None

        birth_info = cached_result.birth_info

        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        birth_info_str = record['birth_info'].replace(' ', '_')

        # 先生成HTML文件
        html_filename = f"紫薇分析_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        html_filepath = os.path.join(export_dir, html_filename)

        # 生成HTML内容
        html_content = generate_html_report(cached_result, record)

        with open(html_filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        st.success(f"✅ 已生成HTML报告: {html_filename}")
        st.info("💡 HTML格式支持完美的中文显示，可以在浏览器中打开并打印为PDF")

        return html_filepath

    except Exception as e:
        st.error(f"❌ 导出HTML失败: {e}")
        return None

def generate_html_report(cached_result, record):
    """生成HTML格式的分析报告"""
    birth_info = cached_result.birth_info

    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紫薇斗数分析报告</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.8;
            margin: 40px;
            color: #333;
            background-color: #fff;
        }}
        .header {{
            text-align: center;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 40px;
        }}
        .title {{
            font-size: 28px;
            color: #4CAF50;
            margin-bottom: 10px;
            font-weight: bold;
        }}
        .subtitle {{
            font-size: 20px;
            color: #666;
            margin-bottom: 20px;
            font-weight: normal;
        }}
        .info-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 40px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .info-table th, .info-table td {{
            border: 1px solid #ddd;
            padding: 15px;
            text-align: left;
        }}
        .info-table th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            width: 25%;
        }}
        .info-table td {{
            background-color: #fff;
        }}
        .section {{
            margin-bottom: 40px;
            page-break-inside: avoid;
            background-color: #fafafa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }}
        .section-title {{
            font-size: 18px;
            color: #4CAF50;
            border-left: 4px solid #4CAF50;
            padding-left: 15px;
            margin-bottom: 20px;
            font-weight: bold;
            background-color: #fff;
            padding: 10px 15px;
            border-radius: 4px;
        }}
        .content {{
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-top: 10px;
        }}
        .content p {{
            text-indent: 2em;
            text-align: justify;
            line-height: 2.0;
            margin-bottom: 15px;
            color: #444;
        }}
        .content p:first-child {{
            margin-top: 0;
        }}
        .content p:last-child {{
            margin-bottom: 0;
        }}
        .list-item {{
            text-indent: 0;
            padding-left: 1em;
            margin-bottom: 8px;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }}
        @media print {{
            body {{
                margin: 20px;
                background-color: #fff;
            }}
            .section {{
                page-break-inside: avoid;
                background-color: #fff;
                border: none;
                box-shadow: none;
            }}
            .section-title {{
                background-color: #f8f9fa;
            }}
            .content {{
                background-color: #fff;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="title">紫薇斗数分析报告</div>
        <div class="subtitle">{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命</div>
    </div>

    <table class="info-table">
        <tr>
            <th>生辰信息</th>
            <td>{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]}</td>
        </tr>
        <tr>
            <th>性别</th>
            <td>{birth_info["gender"]}</td>
        </tr>
        <tr>
            <th>分析完成度</th>
            <td>{record["completed_angles"]}/12</td>
        </tr>
        <tr>
            <th>总字数</th>
            <td>{record["total_words"]:,}字</td>
        </tr>
    </table>
"""

    # 添加分析内容
    detailed_analysis = cached_result.detailed_analysis
    if isinstance(detailed_analysis, dict):
        angle_analyses = detailed_analysis.get("angle_analyses", {})

        angle_names = {
            "personality_destiny": "命宫分析 - 性格命运核心特征",
            "wealth_fortune": "财富分析 - 财运状况与理财投资",
            "marriage_love": "婚姻分析 - 感情婚姻与桃花运势",
            "health_wellness": "健康分析 - 身体状况与养生建议",
            "career_achievement": "事业分析 - 职业发展与成就潜力",
            "children_creativity": "子女分析 - 生育状况与子女关系",
            "interpersonal_relationship": "人际分析 - 社交关系与贵人运",
            "education_learning": "学业分析 - 教育学习与知识发展",
            "family_environment": "家庭分析 - 家庭环境与亲情关系",
            "travel_relocation": "迁移分析 - 搬迁旅行与环境变化",
            "spiritual_blessing": "精神分析 - 精神状态与福德运势",
            "authority_parents": "权威分析 - 领导能力与权威地位"
        }

        for angle_key, angle_name in angle_names.items():
            if angle_key in angle_analyses and angle_analyses[angle_key]:
                clean_content = clean_markdown_text(angle_analyses[angle_key])

                # 将内容分割为段落并格式化
                paragraphs = clean_content.split('\n\n')
                content_html = '<div class="content">\n'

                for para in paragraphs:
                    para = para.strip()
                    if para:
                        # 检查是否是列表项
                        if para.startswith('• '):
                            content_html += f'<p class="list-item">{para}</p>\n'
                        else:
                            # 普通段落
                            content_html += f'<p>{para}</p>\n'

                content_html += '</div>\n'

                html_content += f"""
    <div class="section">
        <div class="section-title">{angle_name}</div>
        {content_html}
    </div>
"""

    # 添加页脚
    html_content += f"""
    <div class="footer">
        <p>生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}</p>
    </div>
</body>
</html>
"""

    return html_content

def convert_markdown_to_html(markdown_text):
    """将markdown文本转换为HTML，保持良好的格式"""
    if not markdown_text:
        return ""

    html_content = ""
    lines = markdown_text.split('\n')
    current_paragraph = []
    in_list = False
    in_table = False
    table_rows = []

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        if not line:
            # 空行 - 结束当前段落
            if current_paragraph:
                html_content += f'<p>{"".join(current_paragraph)}</p>\n'
                current_paragraph = []
            if in_list:
                html_content += '</ul>\n'
                in_list = False
            if in_table:
                html_content += process_table(table_rows)
                table_rows = []
                in_table = False
            i += 1
            continue

        # 标题处理
        if line.startswith('#'):
            if current_paragraph:
                html_content += f'<p>{"".join(current_paragraph)}</p>\n'
                current_paragraph = []

            level = len(line) - len(line.lstrip('#'))
            title_text = line.lstrip('#').strip()
            html_content += f'<h{level}>{title_text}</h{level}>\n'
            i += 1
            continue

        # 列表处理
        if line.startswith(('- ', '• ', '* ', '① ', '② ', '③ ', '④ ', '⑤ ')):
            if current_paragraph:
                html_content += f'<p>{"".join(current_paragraph)}</p>\n'
                current_paragraph = []

            if not in_list:
                html_content += '<ul>\n'
                in_list = True

            list_text = line[2:].strip() if line.startswith(('- ', '• ', '* ')) else line[2:].strip()
            html_content += f'<li>{list_text}</li>\n'
            i += 1
            continue

        # 表格处理
        if '|' in line and line.count('|') >= 2:
            if current_paragraph:
                html_content += f'<p>{"".join(current_paragraph)}</p>\n'
                current_paragraph = []

            if not in_table:
                in_table = True

            # 跳过表格分隔行
            if not re.match(r'^[\|\-\s]+$', line):
                table_rows.append(line)
            i += 1
            continue

        # 结束表格
        if in_table and '|' not in line:
            html_content += process_table(table_rows)
            table_rows = []
            in_table = False

        # 结束列表
        if in_list and not line.startswith(('- ', '• ', '* ', '① ', '② ', '③ ', '④ ', '⑤ ')):
            html_content += '</ul>\n'
            in_list = False

        # 普通段落
        if line:
            # 处理粗体和斜体
            line = re.sub(r'\*\*([^*]+)\*\*', r'<strong>\1</strong>', line)
            line = re.sub(r'\*([^*]+)\*', r'<em>\1</em>', line)
            line = re.sub(r'`([^`]+)`', r'<code>\1</code>', line)

            current_paragraph.append(line + ' ')

        i += 1

    # 处理最后的内容
    if current_paragraph:
        html_content += f'<p>{"".join(current_paragraph)}</p>\n'
    if in_list:
        html_content += '</ul>\n'
    if in_table:
        html_content += process_table(table_rows)

    return html_content

def process_table(table_rows):
    """处理表格行，转换为HTML表格"""
    if not table_rows:
        return ""

    html = '<table>\n'

    for i, row in enumerate(table_rows):
        cells = [cell.strip() for cell in row.split('|') if cell.strip()]

        if i == 0:
            # 表头
            html += '<thead><tr>\n'
            for cell in cells:
                html += f'<th>{cell}</th>\n'
            html += '</tr></thead>\n<tbody>\n'
        else:
            # 表格内容
            html += '<tr>\n'
            for cell in cells:
                html += f'<td>{cell}</td>\n'
            html += '</tr>\n'

    html += '</tbody></table>\n'
    return html

def get_chart_html_content(birth_info=None):
    """获取排盘图表的HTML内容 - 直接嵌入内容，避免iframe问题"""
    try:
        # 直接查找charts目录中的HTML文件
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            # 查找融合分析的HTML文件
            html_files = [f for f in os.listdir(charts_dir) if f.startswith("fusion_chart_") and f.endswith(".html")]
            if html_files:
                # 使用最新的HTML文件
                html_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_dir, x)), reverse=True)
                html_path = os.path.join(charts_dir, html_files[0])

                try:
                    with open(html_path, 'r', encoding='utf-8') as f:
                        html_content = f.read()

                    # 提取样式和内容，但避免冲突
                    import re

                    # 提取style标签内容，并添加作用域前缀
                    style_match = re.search(r'<style[^>]*>(.*?)</style>', html_content, re.DOTALL)
                    if style_match:
                        original_styles = style_match.group(1)
                        # 为所有样式添加.chart-container前缀，避免与主页面样式冲突
                        scoped_styles = re.sub(r'([^{}]+)\s*\{', r'.chart-container \1 {', original_styles)
                        # 处理一些特殊情况
                        scoped_styles = scoped_styles.replace('.chart-container *', '.chart-container *')
                        scoped_styles = scoped_styles.replace('.chart-container body', '.chart-container')
                        scoped_styles = scoped_styles.replace('.chart-container html', '.chart-container')
                    else:
                        scoped_styles = ""

                    # 提取body内容
                    body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, re.DOTALL)
                    if body_match:
                        body_content = body_match.group(1)
                    else:
                        # 如果没有body标签，提取container内容
                        container_match = re.search(r'<div class="container"[^>]*>(.*?)</div>\s*</body>', html_content, re.DOTALL)
                        if container_match:
                            body_content = f'<div class="container">{container_match.group(1)}</div>'
                        else:
                            body_content = html_content

                    # 组合样式和内容
                    return f'''
                    <style>
                    /* 排盘图表专用样式 - 作用域限制 */
                    {scoped_styles}

                    /* 确保图表容器样式 */
                    .chart-container {{
                        width: 100%;
                        background: white;
                        border-radius: 15px;
                        overflow: hidden;
                        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                        font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
                    }}
                    </style>
                    <div class="chart-container">
                        {body_content}
                    </div>
                    '''

                except Exception as e:
                    # 如果处理失败，返回简化版本
                    return f'''
                    <div style="text-align: center; padding: 40px; border: 2px solid #e0e0e0; border-radius: 15px; background: #f8f9fa;">
                        <div style="font-size: 48px; margin-bottom: 20px;">🎯</div>
                        <div style="font-size: 20px; margin-bottom: 10px; color: #2c3e50;">紫薇排盘图表</div>
                        <div style="font-size: 14px; color: #666; margin-bottom: 20px;">图表处理失败: {e}</div>
                        <div style="padding: 15px; background: #fff3cd; border-radius: 10px; max-width: 500px; margin: 0 auto;">
                            <div style="font-size: 16px; color: #856404; margin-bottom: 8px;">📁 图表文件位置</div>
                            <div style="font-size: 12px; color: #856404; word-break: break-all;">
                                {html_path}
                            </div>
                            <div style="margin-top: 10px;">
                                <a href="file:///{os.path.abspath(html_path).replace(chr(92), '/')}"
                                   target="_blank"
                                   style="color: #007bff; text-decoration: none;">
                                    🔗 直接打开图表文件
                                </a>
                            </div>
                        </div>
                    </div>
                    '''

        # 如果都没有找到，返回占位符
        if birth_info:
            year = birth_info.get("year", "?")
            month = birth_info.get("month", "?")
            day = birth_info.get("day", "?")
            hour = birth_info.get("hour", "?")
        else:
            year = month = day = hour = "?"

        return f'''
        <div style="text-align: center; padding: 40px; border: 2px dashed #e0e0e0; border-radius: 15px; background: #f8f9fa;">
            <div style="font-size: 48px; margin-bottom: 20px;">🎯</div>
            <div style="font-size: 20px; margin-bottom: 10px; color: #2c3e50;">紫薇排盘图表</div>
            <div style="font-size: 14px; color: #666; margin-bottom: 20px;">基于{year}年{month}月{day}日{hour}生成</div>
            <div style="padding: 15px; background: #e8f5e8; border-radius: 10px; max-width: 400px; margin: 0 auto;">
                <div style="font-size: 16px; color: #2c3e50; margin-bottom: 10px;">排盘图表生成中</div>
                <div style="font-size: 14px; color: #34495e; line-height: 1.6;">
                    请稍后刷新页面查看完整的排盘图表
                </div>
            </div>
        </div>
        '''

    except Exception as e:
        return f'<div style="text-align: center; padding: 40px; color: #666;">排盘图表获取失败: {e}</div>'

def export_single_to_html_with_toc(record):
    """导出带左侧目录的HTML报告"""
    try:
        # 加载详细数据
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(record['result_id'])

        if not cached_result:
            st.error("❌ 无法加载分析数据")
            return None

        birth_info = cached_result.birth_info

        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        birth_info_str = record['birth_info'].replace(' ', '_')

        # 生成HTML文件
        html_filename = f"紫薇分析_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        html_filepath = os.path.join(export_dir, html_filename)

        # 生成带目录的HTML内容
        html_content = generate_html_with_toc(cached_result, record)

        with open(html_filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        st.success(f"✅ 已生成带目录的HTML报告: {html_filename}")
        st.info("💡 HTML格式支持完美的中文显示，左侧有目录导航，可以在浏览器中打开并打印为PDF")

        return html_filepath

    except Exception as e:
        st.error(f"❌ 导出HTML失败: {e}")
        return None

def generate_html_with_toc(cached_result, record):
    """生成带左侧目录的HTML格式分析报告 - 移动端适配版"""
    birth_info = cached_result.birth_info

    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <title>紫薇斗数分析报告</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "SimSun", Arial, sans-serif;
            line-height: 1.8;
            color: #2c3e50;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-size: 16px;
        }}

        .container {{
            display: flex;
            min-height: 100vh;
        }}

        /* 移动端菜单按钮 */
        .mobile-menu-btn {{
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }}

        /* 左侧目录样式 */
        .toc {{
            width: 300px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            z-index: 1000;
            transition: transform 0.3s ease;
        }}

        .toc.hidden {{
            transform: translateX(-100%);
        }}

        .toc h2 {{
            font-size: 20px;
            margin-bottom: 25px;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 15px;
        }}

        .toc-item {{
            display: block;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            padding: 15px 18px;
            margin-bottom: 8px;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 15px;
            border-left: 4px solid transparent;
        }}

        .toc-item:hover {{
            background: rgba(255,255,255,0.2);
            color: white;
            border-left-color: #4CAF50;
            transform: translateX(8px);
        }}

        .toc-item.active {{
            background: rgba(255,255,255,0.3);
            color: white;
            border-left-color: #4CAF50;
        }}

        /* 主内容区域 */
        .main-content {{
            margin-left: 300px;
            padding: 50px;
            background-color: #fff;
            min-height: 100vh;
            width: calc(100% - 300px);
            transition: margin-left 0.3s ease, width 0.3s ease;
        }}

        .main-content.expanded {{
            margin-left: 0;
            width: 100%;
        }}

        .header {{
            text-align: center;
            padding: 50px;
            margin-bottom: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }}

        .title {{
            font-size: 36px;
            margin-bottom: 20px;
            font-weight: bold;
        }}

        .subtitle {{
            font-size: 24px;
            margin-bottom: 25px;
            opacity: 0.9;
        }}

        /* 排盘图表区域 */
        .chart-section {{
            margin-bottom: 50px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #e0e0e0;
        }}

        .chart-title {{
            background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
            color: white;
            padding: 25px 35px;
            font-size: 22px;
            font-weight: bold;
            margin: 0;
        }}

        .chart-content {{
            padding: 40px;
            text-align: center;
        }}

        .chart-iframe {{
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}

        .info-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 50px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }}

        .info-table th, .info-table td {{
            padding: 20px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }}

        .info-table th {{
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            font-weight: bold;
            width: 25%;
            font-size: 16px;
        }}

        .info-table td {{
            background-color: #fafafa;
            font-size: 15px;
        }}

        .info-table tr:last-child td {{
            border-bottom: none;
        }}

        /* 章节样式 */
        .section {{
            margin-bottom: 60px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #e0e0e0;
        }}

        .section-title {{
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 25px 35px;
            font-size: 22px;
            font-weight: bold;
            margin: 0;
        }}

        .content {{
            padding: 40px;
            background: #fafafa;
            border-radius: 0 0 20px 20px;
        }}

        /* Markdown样式 */
        .content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {{
            color: #2c3e50;
            margin: 30px 0 20px 0;
            font-weight: bold;
            line-height: 1.4;
        }}

        .content h1 {{ font-size: 28px; border-bottom: 3px solid #4CAF50; padding-bottom: 10px; }}
        .content h2 {{ font-size: 24px; border-bottom: 2px solid #81C784; padding-bottom: 8px; }}
        .content h3 {{ font-size: 20px; color: #388E3C; }}
        .content h4 {{ font-size: 18px; color: #4CAF50; }}
        .content h5 {{ font-size: 16px; color: #66BB6A; }}
        .content h6 {{ font-size: 14px; color: #81C784; }}

        .content p {{
            text-align: justify;
            line-height: 2.2;
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 16px;
            word-spacing: 1px;
            letter-spacing: 0.5px;
        }}

        .content ul, .content ol {{
            margin: 20px 0;
            padding-left: 30px;
        }}

        .content li {{
            margin-bottom: 10px;
            line-height: 1.8;
            color: #34495e;
        }}

        .content table {{
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }}

        .content table th {{
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }}

        .content table td {{
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
            background-color: #fff;
        }}

        .content table tr:nth-child(even) td {{
            background-color: #f8f9fa;
        }}

        .content blockquote {{
            border-left: 4px solid #4CAF50;
            margin: 20px 0;
            padding: 15px 20px;
            background: #f0f8f0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }}

        .content code {{
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }}

        .content pre {{
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 20px 0;
        }}

        .footer {{
            text-align: center;
            margin-top: 80px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}

        /* 滚动条样式 */
        .toc::-webkit-scrollbar {{
            width: 8px;
        }}

        .toc::-webkit-scrollbar-track {{
            background: rgba(255,255,255,0.1);
        }}

        .toc::-webkit-scrollbar-thumb {{
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }}

        /* 移动端适配 */
        @media (max-width: 1024px) {{
            .mobile-menu-btn {{
                display: block;
            }}

            .toc {{
                width: 280px;
                transform: translateX(-100%);
            }}

            .toc.show {{
                transform: translateX(0);
            }}

            .main-content {{
                margin-left: 0;
                width: 100%;
                padding: 30px 20px;
            }}

            .title {{
                font-size: 28px;
            }}

            .subtitle {{
                font-size: 20px;
            }}

            .chart-iframe {{
                height: 400px;
            }}
        }}

        @media (max-width: 480px) {{
            .header {{
                padding: 30px 20px;
            }}

            .title {{
                font-size: 24px;
            }}

            .subtitle {{
                font-size: 18px;
            }}

            .content {{
                padding: 25px 20px;
            }}

            .chart-iframe {{
                height: 300px;
            }}

            .info-table th, .info-table td {{
                padding: 12px 8px;
                font-size: 14px;
            }}
        }}
    </style>
</head>
<body>
    <button class="mobile-menu-btn" onclick="toggleMenu()">☰</button>

    <div class="container">
        <!-- 左侧目录 -->
        <nav class="toc" id="toc">
            <h2>📋 目录导航</h2>
            <a href="#basic-info" class="toc-item">📋 基本信息</a>"""

    # 添加12个角度的目录项
    angle_names = {
        "personality_destiny": "🏛️ 命宫分析",
        "wealth_fortune": "💰 财富分析",
        "marriage_love": "💕 婚姻分析",
        "health_wellness": "🏥 健康分析",
        "career_achievement": "💼 事业分析",
        "children_creativity": "👶 子女分析",
        "interpersonal_relationship": "🤝 人际分析",
        "education_learning": "📚 学业分析",
        "family_environment": "🏠 家庭分析",
        "travel_relocation": "✈️ 迁移分析",
        "spiritual_blessing": "🙏 精神分析",
        "authority_parents": "👑 权威分析"
    }

    # 检查哪些角度有内容
    detailed_analysis = cached_result.detailed_analysis
    available_angles = []
    if isinstance(detailed_analysis, dict):
        angle_analyses = detailed_analysis.get("angle_analyses", {})
        for angle_key, angle_name in angle_names.items():
            if angle_key in angle_analyses and angle_analyses[angle_key]:
                available_angles.append((angle_key, angle_name))
                html_content += f'\n            <a href="#{angle_key}" class="toc-item">{angle_name}</a>'

    html_content += f"""
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content" id="main-content">
            <div class="header" id="basic-info">
                <div class="title">紫薇斗数分析报告</div>
                <div class="subtitle">{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命</div>
            </div>

            <table class="info-table">
                <tr>
                    <th>生辰信息</th>
                    <td>{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]}</td>
                </tr>
                <tr>
                    <th>性别</th>
                    <td>{birth_info["gender"]}</td>
                </tr>
                <tr>
                    <th>分析完成度</th>
                    <td>{record["completed_angles"]}/12</td>
                </tr>
                <tr>
                    <th>总字数</th>
                    <td>{record["total_words"]:,}字</td>
                </tr>
            </table>

            <!-- 排盘图表部分已删除 -->
"""

    # 添加分析内容
    for angle_key, angle_name in available_angles:
        if angle_key in angle_analyses and angle_analyses[angle_key]:
            content_html = convert_markdown_to_html(angle_analyses[angle_key])

            html_content += f"""
            <div class="section" id="{angle_key}">
                <h2 class="section-title">{angle_name}</h2>
                <div class="content">
                    {content_html}
                </div>
            </div>
"""

    # 添加页脚和JavaScript
    html_content += f"""
            <div class="footer">
                <p>生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}</p>
                <p>紫薇斗数分析系统 | 专业命理分析报告</p>
            </div>
        </main>
    </div>

    <script>
        // 移动端菜单切换
        function toggleMenu() {{
            const toc = document.getElementById('toc');
            const mainContent = document.getElementById('main-content');

            if (toc.classList.contains('show')) {{
                toc.classList.remove('show');
                mainContent.classList.remove('expanded');
            }} else {{
                toc.classList.add('show');
                mainContent.classList.add('expanded');
            }}
        }}

        // 目录导航功能
        document.addEventListener('DOMContentLoaded', function() {{
            const tocItems = document.querySelectorAll('.toc-item');
            const sections = document.querySelectorAll('.section, .header, .chart-section');

            // 点击目录项平滑滚动
            tocItems.forEach(item => {{
                item.addEventListener('click', function(e) {{
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {{
                        targetElement.scrollIntoView({{
                            behavior: 'smooth',
                            block: 'start'
                        }});

                        // 更新活跃状态
                        tocItems.forEach(t => t.classList.remove('active'));
                        this.classList.add('active');

                        // 移动端自动关闭菜单
                        if (window.innerWidth <= 1024) {{
                            const toc = document.getElementById('toc');
                            const mainContent = document.getElementById('main-content');
                            toc.classList.remove('show');
                            mainContent.classList.remove('expanded');
                        }}
                    }}
                }});
            }});

            // 滚动时高亮当前章节
            window.addEventListener('scroll', function() {{
                let current = '';
                sections.forEach(section => {{
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 100) {{
                        current = section.getAttribute('id');
                    }}
                }});

                tocItems.forEach(item => {{
                    item.classList.remove('active');
                    if (item.getAttribute('href') === '#' + current) {{
                        item.classList.add('active');
                    }}
                }});
            }});

            // 点击主内容区域关闭移动端菜单
            document.getElementById('main-content').addEventListener('click', function() {{
                if (window.innerWidth <= 1024) {{
                    const toc = document.getElementById('toc');
                    const mainContent = document.getElementById('main-content');
                    if (toc.classList.contains('show')) {{
                        toc.classList.remove('show');
                        mainContent.classList.remove('expanded');
                    }}
                }}
            }});

            // 窗口大小改变时重置菜单状态
            window.addEventListener('resize', function() {{
                const toc = document.getElementById('toc');
                const mainContent = document.getElementById('main-content');

                if (window.innerWidth > 1024) {{
                    toc.classList.remove('show');
                    mainContent.classList.remove('expanded');
                }}
            }});
        }});
    </script>
</body>
</html>
"""

    return html_content

def export_single_to_pdf_original(record):
    """原始的PDF导出方法（备用）"""
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont

        # 注册中文字体
        font_registered = False
        try:
            # 尝试注册系统中文字体
            import platform
            system = platform.system()

            if system == "Windows":
                # Windows系统字体路径
                font_paths = [
                    "C:/Windows/Fonts/simhei.ttf",  # 黑体
                    "C:/Windows/Fonts/msyh.ttf",    # 微软雅黑
                    "C:/Windows/Fonts/simsun.ttc",  # 宋体
                ]
            elif system == "Darwin":  # macOS
                font_paths = [
                    "/System/Library/Fonts/PingFang.ttc",
                    "/Library/Fonts/Arial Unicode MS.ttf",
                ]
            else:  # Linux
                font_paths = [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                ]

            # 尝试注册第一个可用的字体
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        font_registered = True
                        st.success(f"✅ 成功注册中文字体: {font_path}")
                        break
                    except Exception as font_error:
                        st.warning(f"⚠️ 字体注册失败 {font_path}: {font_error}")
                        continue

            if not font_registered:
                # 尝试使用reportlab内置的中文字体支持
                try:
                    from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                    pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                    font_registered = True
                    st.info("✅ 使用内置中文字体支持")
                except:
                    st.error("❌ 无法注册中文字体，PDF中文可能显示为方块")

        except Exception as e:
            st.error(f"❌ 字体注册异常: {e}")

        # 加载详细数据
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(record['result_id'])

        if not cached_result:
            st.error("❌ 无法加载分析数据")
            return None

        birth_info = cached_result.birth_info

        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        birth_info_str = record['birth_info'].replace(' ', '_')
        filename = f"紫薇分析_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join(export_dir, filename)

        # 创建PDF文档
        doc = SimpleDocTemplate(filepath, pagesize=A4, topMargin=1*inch)
        story = []

        # 获取样式并设置中文字体
        styles = getSampleStyleSheet()

        # 确定要使用的字体名称
        if font_registered:
            font_name = 'ChineseFont'
        else:
            # 尝试使用内置中文字体
            try:
                font_name = 'STSong-Light'
            except:
                font_name = 'Helvetica'

        # 创建支持中文的样式
        title_style = ParagraphStyle(
            'ChineseTitle',
            parent=styles['Title'],
            fontName=font_name,
            fontSize=18,
            alignment=1  # 居中
        )
        heading_style = ParagraphStyle(
            'ChineseHeading1',
            parent=styles['Heading1'],
            fontName=font_name,
            fontSize=14
        )
        heading2_style = ParagraphStyle(
            'ChineseHeading2',
            parent=styles['Heading2'],
            fontName=font_name,
            fontSize=12
        )
        normal_style = ParagraphStyle(
            'ChineseNormal',
            parent=styles['Normal'],
            fontName=font_name,
            fontSize=10,
            leading=14
        )

        # 添加标题
        story.append(Paragraph('紫薇斗数分析报告', title_style))
        story.append(Spacer(1, 20))

        # 添加个人信息
        story.append(Paragraph(f'{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命', heading_style))
        story.append(Spacer(1, 12))

        # 基本信息表格
        info_data = [
            ['生辰信息', f'{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]}'],
            ['性别', birth_info["gender"]],
            ['分析完成度', f'{record["completed_angles"]}/12'],
            ['总字数', f'{record["total_words"]:,}字']
        ]

        info_table = Table(info_data, colWidths=[2*inch, 4*inch])

        # 设置表格样式，使用中文字体
        table_style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]

        # 使用相同的字体名称
        table_style.extend([
            ('FONTNAME', (0, 0), (-1, 0), font_name),
            ('FONTNAME', (0, 1), (-1, -1), font_name)
        ])

        info_table.setStyle(TableStyle(table_style))

        story.append(info_table)
        story.append(Spacer(1, 20))

        # 分析内容
        detailed_analysis = cached_result.detailed_analysis
        if isinstance(detailed_analysis, dict):
            angle_analyses = detailed_analysis.get("angle_analyses", {})

            angle_names = {
                "personality_destiny": "命宫分析 - 性格命运核心特征",
                "wealth_fortune": "财富分析 - 财运状况与理财投资",
                "marriage_love": "婚姻分析 - 感情婚姻与桃花运势",
                "health_wellness": "健康分析 - 身体状况与养生建议",
                "career_achievement": "事业分析 - 职业发展与成就潜力",
                "children_creativity": "子女分析 - 生育状况与子女关系",
                "interpersonal_relationship": "人际分析 - 社交关系与贵人运",
                "education_learning": "学业分析 - 教育学习与知识发展",
                "family_environment": "家庭分析 - 家庭环境与亲情关系",
                "travel_relocation": "迁移分析 - 搬迁旅行与环境变化",
                "spiritual_blessing": "精神分析 - 精神状态与福德运势",
                "authority_parents": "权威分析 - 领导能力与权威地位"
            }

            for angle_key, angle_name in angle_names.items():
                if angle_key in angle_analyses and angle_analyses[angle_key]:
                    story.append(Paragraph(angle_name, heading2_style))
                    story.append(Spacer(1, 6))

                    # 清理并添加内容
                    clean_content = clean_markdown_text(angle_analyses[angle_key])
                    story.append(Paragraph(clean_content, normal_style))
                    story.append(Spacer(1, 12))

        # 添加页脚
        story.append(Spacer(1, 20))
        footer_text = f'生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}'
        story.append(Paragraph(footer_text, normal_style))

        # 构建PDF
        doc.build(story)
        return filepath

    except Exception as e:
        st.error(f"❌ 导出PDF失败: {e}")
        return None

def export_single_to_txt(record):
    """导出单人为文本文件"""
    try:
        # 加载详细数据
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(record['result_id'])

        if not cached_result:
            st.error("❌ 无法加载分析数据")
            return None

        birth_info = cached_result.birth_info

        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        birth_info_str = record['birth_info'].replace(' ', '_')
        filename = f"紫薇分析_{birth_info_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(export_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('紫薇斗数分析报告\n')
            f.write('=' * 50 + '\n\n')

            # 个人信息
            f.write(f'{birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命\n')
            f.write('-' * 30 + '\n\n')

            f.write(f'生辰信息: {birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]}\n')
            f.write(f'性别: {birth_info["gender"]}\n')
            f.write(f'分析完成度: {record["completed_angles"]}/12\n')
            f.write(f'总字数: {record["total_words"]:,}字\n')
            f.write(f'创建时间: {record["created_at"]}\n\n')

            # 分析内容
            detailed_analysis = cached_result.detailed_analysis
            if isinstance(detailed_analysis, dict):
                angle_analyses = detailed_analysis.get("angle_analyses", {})

                angle_names = {
                    "personality_destiny": "命宫分析 - 性格命运核心特征",
                    "wealth_fortune": "财富分析 - 财运状况与理财投资",
                    "marriage_love": "婚姻分析 - 感情婚姻与桃花运势",
                    "health_wellness": "健康分析 - 身体状况与养生建议",
                    "career_achievement": "事业分析 - 职业发展与成就潜力",
                    "children_creativity": "子女分析 - 生育状况与子女关系",
                    "interpersonal_relationship": "人际分析 - 社交关系与贵人运",
                    "education_learning": "学业分析 - 教育学习与知识发展",
                    "family_environment": "家庭分析 - 家庭环境与亲情关系",
                    "travel_relocation": "迁移分析 - 搬迁旅行与环境变化",
                    "spiritual_blessing": "精神分析 - 精神状态与福德运势",
                    "authority_parents": "权威分析 - 领导能力与权威地位"
                }

                for angle_key, angle_name in angle_names.items():
                    if angle_key in angle_analyses and angle_analyses[angle_key]:
                        f.write(f'{angle_name}\n')
                        f.write('-' * len(angle_name) + '\n')

                        # 清理并写入内容
                        clean_content = clean_markdown_text(angle_analyses[angle_key])
                        f.write(clean_content + '\n\n')

            # 页脚
            f.write('\n' + '=' * 50 + '\n')
            f.write(f'生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}\n')

        return filepath

    except Exception as e:
        st.error(f"❌ 导出文本失败: {e}")
        return None

def export_to_pdf(selected_records):
    """导出为PDF文档"""
    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont

        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        filename = f"紫薇分析报告_{len(selected_records)}人_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join(export_dir, filename)

        # 创建PDF文档
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []

        # 获取样式
        styles = getSampleStyleSheet()
        title_style = styles['Title']
        heading_style = styles['Heading1']
        normal_style = styles['Normal']

        # 添加标题
        story.append(Paragraph('紫薇斗数分析报告', title_style))
        story.append(Spacer(1, 12))
        story.append(Paragraph(f'生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}', normal_style))
        story.append(Paragraph(f'分析人数: {len(selected_records)}人', normal_style))
        story.append(Spacer(1, 24))

        for i, record in enumerate(selected_records, 1):
            try:
                from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
                calculator_agent = FortuneCalculatorAgent()
                cached_result = calculator_agent.cache.get_result(record['result_id'])

                if cached_result:
                    birth_info = cached_result.birth_info

                    # 添加个人信息
                    story.append(Paragraph(f'{i}. {birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命', heading_style))
                    story.append(Spacer(1, 12))

                    # 基本信息
                    story.append(Paragraph(f'分析完成度: {record["completed_angles"]}/12 | 总字数: {record["total_words"]:,}字', normal_style))
                    story.append(Spacer(1, 12))

                    # 分析内容
                    detailed_analysis = cached_result.detailed_analysis
                    if isinstance(detailed_analysis, dict):
                        angle_analyses = detailed_analysis.get("angle_analyses", {})

                        angle_names = {
                            "personality_destiny": "命宫分析",
                            "wealth_fortune": "财富分析",
                            "marriage_love": "婚姻分析",
                            "health_wellness": "健康分析",
                            "career_achievement": "事业分析",
                            "children_creativity": "子女分析",
                            "interpersonal_relationship": "人际分析",
                            "education_learning": "学业分析",
                            "family_environment": "家庭分析",
                            "travel_relocation": "迁移分析",
                            "spiritual_blessing": "精神分析",
                            "authority_parents": "权威分析"
                        }

                        for angle_key, angle_name in angle_names.items():
                            if angle_key in angle_analyses and angle_analyses[angle_key]:
                                story.append(Paragraph(angle_name, styles['Heading2']))
                                story.append(Paragraph(angle_analyses[angle_key], normal_style))
                                story.append(Spacer(1, 12))

                    # 添加分页符（除了最后一个）
                    if i < len(selected_records):
                        story.append(PageBreak())

            except Exception as e:
                story.append(Paragraph(f'加载记录失败: {e}', normal_style))

        # 构建PDF
        doc.build(story)
        return filepath

    except ImportError:
        st.error("❌ 缺少reportlab库，请安装: pip install reportlab")
        return None
    except Exception as e:
        st.error(f"❌ 导出PDF失败: {e}")
        return None

def export_to_txt(selected_records):
    """导出为文本文件"""
    try:
        # 创建导出目录
        export_dir = "exports"
        os.makedirs(export_dir, exist_ok=True)
        filename = f"紫薇分析报告_{len(selected_records)}人_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(export_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('紫薇斗数分析报告\n')
            f.write('=' * 50 + '\n')
            f.write(f'生成时间: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}\n')
            f.write(f'分析人数: {len(selected_records)}人\n')
            f.write('=' * 50 + '\n\n')

            for i, record in enumerate(selected_records, 1):
                try:
                    from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
                    calculator_agent = FortuneCalculatorAgent()
                    cached_result = calculator_agent.cache.get_result(record['result_id'])

                    if cached_result:
                        birth_info = cached_result.birth_info

                        f.write(f'{i}. {birth_info["year"]}年{birth_info["month"]}月{birth_info["day"]}日 {birth_info["hour"]} {birth_info["gender"]}命\n')
                        f.write('-' * 30 + '\n')
                        f.write(f'记录ID: {record["result_id"]}\n')
                        f.write(f'分析完成度: {record["completed_angles"]}/12\n')
                        f.write(f'总字数: {record["total_words"]:,}字\n')
                        f.write(f'创建时间: {record["created_at"]}\n\n')

                        # 分析内容
                        detailed_analysis = cached_result.detailed_analysis
                        if isinstance(detailed_analysis, dict):
                            angle_analyses = detailed_analysis.get("angle_analyses", {})

                            angle_names = {
                                "personality_destiny": "命宫分析 - 性格命运核心特征",
                                "wealth_fortune": "财富分析 - 财运状况与理财投资",
                                "marriage_love": "婚姻分析 - 感情婚姻与桃花运势",
                                "health_wellness": "健康分析 - 身体状况与养生建议",
                                "career_achievement": "事业分析 - 职业发展与成就潜力",
                                "children_creativity": "子女分析 - 生育状况与子女关系",
                                "interpersonal_relationship": "人际分析 - 社交关系与贵人运",
                                "education_learning": "学业分析 - 教育学习与知识发展",
                                "family_environment": "家庭分析 - 家庭环境与亲情关系",
                                "travel_relocation": "迁移分析 - 搬迁旅行与环境变化",
                                "spiritual_blessing": "精神分析 - 精神状态与福德运势",
                                "authority_parents": "权威分析 - 领导能力与权威地位"
                            }

                            for angle_key, angle_name in angle_names.items():
                                if angle_key in angle_analyses and angle_analyses[angle_key]:
                                    f.write(f'{angle_name}\n')
                                    f.write(angle_analyses[angle_key] + '\n\n')

                        f.write('\n' + '=' * 50 + '\n\n')

                except Exception as e:
                    f.write(f'加载记录失败: {e}\n\n')

        return filepath

    except Exception as e:
        st.error(f"❌ 导出文本失败: {e}")
        return None

def render_overview():
    """渲染系统概览"""
    st.markdown("## 📊 系统概览")

    cache_records = get_all_cache_records()

    if cache_records:
        # 🔧 核心指标卡片
        col1, col2, col3, col4 = st.columns(4)

        total_records = len(cache_records)

        # 修复：根据分析类型判断完成状态
        completed_records = 0
        in_progress = 0

        for r in cache_records:
            calculation_type = r.get('calculation_type', 'ziwei')

            if calculation_type in ['compatibility', 'liuyao']:
                # 合盘分析和六爻占卜：completed_angles=1且有内容就是完成
                if r['completed_angles'] > 0 and r['total_words'] > 0:
                    completed_records += 1
                elif r['has_analysis']:
                    in_progress += 1
            else:
                # 命理分析：12个角度完成
                if r['has_analysis'] and r['completed_angles'] >= 12:
                    completed_records += 1
                elif r['has_analysis'] and r['completed_angles'] < 12:
                    in_progress += 1

        pending = total_records - completed_records - in_progress

        with col1:
            st.markdown(f"""
            <div class="detail-section">
                <h3 style="color: #4CAF50; text-align: center; margin: 0;">{total_records}</h3>
                <p style="text-align: center; margin: 0; color: #ccc;">总记录数</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div class="detail-section">
                <h3 style="color: #2196F3; text-align: center; margin: 0;">{completed_records}</h3>
                <p style="text-align: center; margin: 0; color: #ccc;">已完成</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown(f"""
            <div class="detail-section">
                <h3 style="color: #FF9800; text-align: center; margin: 0;">{in_progress}</h3>
                <p style="text-align: center; margin: 0; color: #ccc;">进行中</p>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            st.markdown(f"""
            <div class="detail-section">
                <h3 style="color: #f44336; text-align: center; margin: 0;">{pending}</h3>
                <p style="text-align: center; margin: 0; color: #ccc;">待处理</p>
            </div>
            """, unsafe_allow_html=True)

        # 🔧 最近活动时间线
        st.markdown("### 📈 最近活动")

        for i, record in enumerate(cache_records[:8]):  # 显示最近8条
            progress_text = f"{record['completed_angles']}/12" if record['has_analysis'] else "待开始"
            status_color = "#4CAF50" if record['completed_angles'] >= 12 else "#FF9800" if record['has_analysis'] else "#f44336"

            st.markdown(f"""
            <div class="detail-section" style="border-left-color: {status_color};">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong style="color: #fff;">{record['birth_info']}</strong>
                        <span class="status-badge" style="background: {status_color}; margin-left: 0.5rem;">
                            {progress_text}
                        </span>
                    </div>
                    <div style="text-align: right; color: #ccc; font-size: 0.8rem;">
                        <div>{record['created_at'][:10]}</div>
                        <div>{record['created_at'][11:16]}</div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # 🔧 快速操作按钮
        st.markdown("### 🚀 快速操作")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📋 查看所有记录", key="overview_view_records", use_container_width=True):
                st.session_state.current_view = 'records'
                st.rerun()

        with col2:
            if st.button("🆕 创建新分析", key="overview_create_analysis", use_container_width=True):
                st.session_state.current_view = 'create'
                st.rerun()

        with col3:
            if st.button("📈 实时监控", key="overview_monitor", use_container_width=True):
                st.session_state.current_view = 'monitor'
                st.rerun()

    else:
        st.markdown("""
        <div class="detail-section">
            <h3>🎯 欢迎使用后台管理系统</h3>
            <p>当前系统中暂无分析记录。</p>
            <p>您可以：</p>
            <ul>
                <li>点击左侧 "🆕 创建分析" 开始新的命理分析</li>
                <li>查看系统监控了解运行状态</li>
                <li>浏览功能导航熟悉系统操作</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

def render_records_list():
    """渲染分析记录列表"""
    st.markdown("## 📋 分析记录管理")

    cache_records = get_all_cache_records()

    if cache_records:
        # 🔧 搜索和筛选工具栏
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            search_term = st.text_input("🔍 搜索记录", placeholder="输入生辰信息或ID...")

        with col2:
            status_filter = st.selectbox("状态筛选", ["全部", "已完成", "进行中", "待处理"])

        with col3:
            if st.button("🔄 刷新列表", key="records_refresh_list", use_container_width=True):
                st.rerun()

        # 🔧 筛选记录
        filtered_records = cache_records
        if search_term:
            filtered_records = [r for r in filtered_records if search_term.lower() in r['birth_info'].lower() or search_term.lower() in r['result_id'].lower()]

        if status_filter != "全部":
            if status_filter == "已完成":
                # 修复：根据分析类型判断完成状态
                def is_completed(r):
                    calculation_type = r.get('calculation_type', 'ziwei')
                    if calculation_type in ['compatibility', 'liuyao']:
                        return r['completed_angles'] > 0 and r['total_words'] > 0
                    else:
                        return r['completed_angles'] >= 12
                filtered_records = [r for r in filtered_records if is_completed(r)]
            elif status_filter == "进行中":
                # 修复：根据分析类型判断进行中状态
                def is_in_progress(r):
                    calculation_type = r.get('calculation_type', 'ziwei')
                    if calculation_type in ['compatibility', 'liuyao']:
                        return r['has_analysis'] and not (r['completed_angles'] > 0 and r['total_words'] > 0)
                    else:
                        return 0 < r['completed_angles'] < 12
                filtered_records = [r for r in filtered_records if is_in_progress(r)]
            elif status_filter == "待处理":
                filtered_records = [r for r in filtered_records if r['completed_angles'] == 0]

        st.markdown(f"### 📊 找到 {len(filtered_records)} 条记录")

        # 🔧 记录表格
        for i, record in enumerate(filtered_records):
            status_color = "#4CAF50" if record['completed_angles'] >= 12 else "#FF9800" if record['has_analysis'] else "#f44336"

            st.markdown(f"""
            <div class="detail-section" style="border-left-color: {status_color};">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                    <div>
                        <h4 style="margin: 0; color: #fff;">{record['birth_info']}</h4>
                        <p style="margin: 0; color: #ccc; font-size: 0.8rem;">ID: {record['result_id'][:16]}...</p>
                    </div>
                    <div style="text-align: right;">
                        <span class="status-badge" style="background: {status_color};">
                            {record['completed_angles']}/12
                        </span>
                        {'<span class="status-badge status-success">图</span>' if record['chart_exists'] else '<span class="status-badge status-error">无图</span>'}
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #ccc; font-size: 0.8rem;">
                        创建时间: {record['created_at']} | 总字数: {record['total_words']:,}
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            col1, col2, col3 = st.columns([1, 1, 1])
            with col1:
                if st.button("📖 查看详情", key=f"view_detail_{record['result_id']}", use_container_width=True):
                    st.session_state.current_view = 'detail'
                    st.session_state.selected_record = record['result_id']
                    st.rerun()
            with col2:
                if st.button("▶️ 继续分析", key=f"continue_{record['result_id']}", use_container_width=True):
                    continue_analysis(record['result_id'])
                    st.success("✅ 已触发继续分析")
                    st.rerun()
            with col3:
                if st.button("🗑️ 删除", key=f"delete_{record['result_id']}", use_container_width=True):
                    # TODO: 实现删除功能
                    st.warning("删除功能待实现")

            st.markdown("---")
    else:
        st.markdown("""
        <div class="detail-section">
            <h3>📝 暂无分析记录</h3>
            <p>系统中还没有任何分析记录。</p>
            <p>点击左侧 "🆕 创建分析" 开始第一个分析。</p>
        </div>
        """, unsafe_allow_html=True)

def render_create_analysis():
    """渲染创建分析页面"""
    st.markdown("## 🆕 创建新分析")

    # 🔧 分析类型选择
    st.markdown("### 📋 选择分析类型")

    analysis_types = {
        "ziwei": {
            "name": "紫薇斗数",
            "icon": "🔮",
            "desc": "基于出生时间的紫薇斗数命盘分析",
            "fields": ["year", "month", "day", "hour", "gender"]
        },
        "bazi": {
            "name": "八字命理",
            "icon": "📿",
            "desc": "传统八字四柱命理分析",
            "fields": ["year", "month", "day", "hour", "gender"]
        },
        "liuyao": {
            "name": "六爻卜卦",
            "icon": "🎯",
            "desc": "针对具体问题的六爻占卜",
            "fields": ["question", "gender", "method"]
        }
    }

    selected_type = None
    cols = st.columns(3)

    for i, (key, info) in enumerate(analysis_types.items()):
        with cols[i]:
            if st.button(f"{info['icon']} {info['name']}", key=f"type_{key}", use_container_width=True):
                selected_type = key
                st.session_state.selected_analysis_type = key
                st.rerun()

    # 显示选中的分析类型
    if 'selected_analysis_type' in st.session_state:
        selected_type = st.session_state.selected_analysis_type
        info = analysis_types[selected_type]

        st.markdown(f"""
        <div class="detail-section">
            <h3>{info['icon']} {info['name']}</h3>
            <p>{info['desc']}</p>
        </div>
        """, unsafe_allow_html=True)

        # 🔧 输入表单
        st.markdown("### 📝 输入信息")

        with st.form("create_analysis_form"):
            if selected_type in ["ziwei", "bazi"]:
                # 生辰信息输入
                col1, col2 = st.columns(2)

                with col1:
                    year = st.text_input("出生年份", value="1990", placeholder="如：1990")
                    day = st.text_input("出生日", value="15", placeholder="如：15")
                    gender = st.selectbox("性别", ["女", "男"], index=0)

                with col2:
                    month = st.text_input("出生月份", value="3", placeholder="如：3")
                    hour = st.selectbox("出生时辰",
                        ["子时(23-01)", "丑时(01-03)", "寅时(03-05)", "卯时(05-07)",
                         "辰时(07-09)", "巳时(09-11)", "午时(11-13)", "未时(13-15)",
                         "申时(15-17)", "酉时(17-19)", "戌时(19-21)", "亥时(21-23)"],
                        index=4)

                # 提取时辰名称
                hour_name = hour.split("(")[0]

            elif selected_type == "liuyao":
                # 卜卦信息输入
                question = st.text_area("占卜问题", placeholder="请详细描述您要占卜的问题...")
                gender = st.selectbox("性别", ["女", "男"], index=0)
                divination_method = st.selectbox("起卦方式", ["时间起卦", "手动起卦", "随机起卦"], index=0)

            submitted = st.form_submit_button("🚀 开始分析", use_container_width=True)

        # 处理表单提交
        if submitted:
            if selected_type in ["ziwei", "bazi"]:
                # 验证输入
                try:
                    year_int = int(year)
                    month_int = int(month)
                    day_int = int(day)

                    if not (1900 <= year_int <= 2100):
                        st.error("❌ 年份应在1900-2100之间")
                        return
                    if not (1 <= month_int <= 12):
                        st.error("❌ 月份应在1-12之间")
                        return
                    if not (1 <= day_int <= 31):
                        st.error("❌ 日期应在1-31之间")
                        return

                except ValueError:
                    st.error("❌ 请输入有效的数字")
                    return

                # 构建生辰信息
                birth_info = {
                    "year": year,
                    "month": month,
                    "day": day,
                    "hour": hour_name,
                    "gender": gender
                }

                # 检查重复
                existing_result = check_existing_analysis(birth_info)
                if existing_result:
                    st.warning(f"⚠️ 已存在相同的{info['name']}分析记录")
                    st.info(f"💡 记录ID: {existing_result[:8]}...")
                    if st.button("查看现有记录"):
                        st.session_state.current_view = 'detail'
                        st.session_state.selected_record = existing_result
                        st.rerun()
                else:
                    # 创建新分析
                    create_new_analysis_task(selected_type, birth_info)

            elif selected_type == "liuyao":
                if not question.strip():
                    st.error("❌ 请输入占卜问题")
                    return

                divination_info = {
                    "question": question,
                    "gender": gender,
                    "method": divination_method,
                    "timestamp": datetime.now().isoformat()
                }

                # 创建新卜卦分析
                create_new_analysis_task(selected_type, divination_info)

def render_monitor():
    """渲染实时监控页面"""
    st.markdown("## 📈 实时监控")

    # 🔧 系统状态监控
    st.markdown("### 🖥️ 系统状态")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown("""
        <div class="detail-section">
            <h4 style="color: #4CAF50; text-align: center; margin: 0;">运行中</h4>
            <p style="text-align: center; margin: 0; color: #ccc;">系统状态</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        import psutil
        cpu_percent = psutil.cpu_percent()
        st.markdown(f"""
        <div class="detail-section">
            <h4 style="color: #2196F3; text-align: center; margin: 0;">{cpu_percent:.1f}%</h4>
            <p style="text-align: center; margin: 0; color: #ccc;">CPU使用率</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        memory = psutil.virtual_memory()
        st.markdown(f"""
        <div class="detail-section">
            <h4 style="color: #FF9800; text-align: center; margin: 0;">{memory.percent:.1f}%</h4>
            <p style="text-align: center; margin: 0; color: #ccc;">内存使用率</p>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        cache_records = get_all_cache_records()

        # 修复：根据分析类型判断活跃任务
        active_tasks = 0
        for r in cache_records:
            calculation_type = r.get('calculation_type', 'ziwei')
            if calculation_type in ['compatibility', 'liuyao']:
                # 合盘分析和六爻占卜：有分析但未完成
                if r['has_analysis'] and not (r['completed_angles'] > 0 and r['total_words'] > 0):
                    active_tasks += 1
            else:
                # 命理分析：0-11个角度完成
                if 0 < r['completed_angles'] < 12:
                    active_tasks += 1
        st.markdown(f"""
        <div class="detail-section">
            <h4 style="color: #9C27B0; text-align: center; margin: 0;">{active_tasks}</h4>
            <p style="text-align: center; margin: 0; color: #ccc;">活跃任务</p>
        </div>
        """, unsafe_allow_html=True)

    # 🔧 任务监控
    st.markdown("### 📋 任务监控")

    if cache_records:
        # 进行中的任务
        active_records = []
        for r in cache_records:
            calculation_type = r.get('calculation_type', 'ziwei')
            if calculation_type in ['compatibility', 'liuyao']:
                # 合盘分析和六爻占卜：有分析但未完成
                if r['has_analysis'] and not (r['completed_angles'] > 0 and r['total_words'] > 0):
                    active_records.append(r)
            else:
                # 命理分析：0-11个角度完成
                if 0 < r['completed_angles'] < 12:
                    active_records.append(r)

        if active_records:
            for record in active_records:
                progress = record['completed_angles'] / 12 * 100
                st.markdown(f"""
                <div class="detail-section">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <strong>{record['birth_info']}</strong>
                        <span class="status-badge status-warning">{record['completed_angles']}/12</span>
                    </div>
                    <div class="progress-compact">
                        <div class="progress-bar-compact" style="width: {progress}%">
                            {progress:.1f}% - {record['total_words']:,}字
                        </div>
                    </div>
                    <div style="font-size: 0.8rem; color: #ccc; margin-top: 0.3rem;">
                        ID: {record['result_id'][:16]}... | 创建: {record['created_at'][11:16]}
                    </div>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("📝 当前没有进行中的任务")

    # 🔧 自动刷新
    st.markdown("### 🔄 自动刷新")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("🔄 立即刷新", use_container_width=True):
            st.rerun()

    with col2:
        auto_refresh = st.checkbox("启用自动刷新 (10秒)", value=False)

    if auto_refresh:
        import time
        if 'last_monitor_refresh' not in st.session_state:
            st.session_state.last_monitor_refresh = time.time()

        elapsed = time.time() - st.session_state.last_monitor_refresh
        if elapsed >= 10:
            st.session_state.last_monitor_refresh = time.time()
            st.rerun()
        else:
            remaining = 10 - int(elapsed)
            st.info(f"⏰ 下次刷新: {remaining}秒")

def render_record_detail():
    """渲染记录详情页面"""
    if not st.session_state.selected_record:
        st.error("❌ 未选择记录")
        return

    result_id = st.session_state.selected_record

    # 🔧 顶部导航
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

    with col1:
        if st.button("← 返回", use_container_width=True):
            st.session_state.current_view = 'records'
            st.session_state.selected_record = None
            st.rerun()

    with col2:
        if st.button("🔄 刷新", use_container_width=True):
            st.rerun()

    with col3:
        if st.button("▶️ 继续", use_container_width=True):
            continue_analysis(result_id)
            st.success("✅ 已触发继续分析")
            st.rerun()

    with col4:
        if st.button("🗑️ 删除", use_container_width=True):
            st.warning("删除功能待实现")

    st.markdown("---")

    # 🔧 加载详情数据
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(result_id)

        if cached_result:
            birth_info = cached_result.birth_info

            # 🔧 基本信息卡片
            st.markdown("## 📋 基本信息")
            st.markdown(f"""
            <div class="detail-section">
                <h3>👤 {birth_info['year']}年{birth_info['month']}月{birth_info['day']}日 {birth_info['hour']} {birth_info['gender']}命</h3>
                <p><strong>记录ID:</strong> {result_id}</p>
                <p><strong>创建时间:</strong> {cached_result.created_at if hasattr(cached_result, 'created_at') else '未知'}</p>
            </div>
            """, unsafe_allow_html=True)

            # 🔧 进度信息
            detailed_analysis = cached_result.detailed_analysis
            if isinstance(detailed_analysis, dict):
                angle_analyses = detailed_analysis.get("angle_analyses", {})
                completed = len([v for v in angle_analyses.values() if v and len(v) > 100])
                total_words = sum(len(v) for v in angle_analyses.values() if v)

                progress = completed / 12 * 100

                st.markdown("## 📊 分析进度")
                st.markdown(f"""
                <div class="detail-section">
                    <div class="progress-compact">
                        <div class="progress-bar-compact" style="width: {progress}%">
                            进度: {completed}/12 ({progress:.1f}%) - 总字数: {total_words:,}
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)

            # 🔧 排盘图片
            st.markdown("## 🎨 排盘图片")
            show_chart_image_detail(cached_result)

            # 🔧 详细分析
            st.markdown("## 📝 详细分析")
            show_angle_analyses_detail(cached_result)

        else:
            st.error(f"❌ 未找到分析结果: {result_id}")

    except Exception as e:
        st.error(f"❌ 加载详情失败: {e}")
        import traceback
        with st.expander("错误详情"):
            st.code(traceback.format_exc())

def create_new_analysis_task(analysis_type, data):
    """创建新分析任务"""
    st.markdown("### 🚀 正在创建分析任务...")

    try:
        with st.spinner("🔄 正在启动后台Agent..."):
            from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
            from core.agents.base_agent import AgentMessage, MessageType

            # 初始化后台Agent
            calculator_agent = FortuneCalculatorAgent()

            # 构建分析请求
            session_id = f"web_new_{int(time.time())}"

            calculation_request = AgentMessage(
                message_id=f"web_{datetime.now().timestamp()}",
                message_type=MessageType.CALCULATION_REQUEST,
                sender_id="web_interface",
                receiver_id=calculator_agent.agent_id,
                content={
                    "user_message": f"Web新分析请求：{data}",
                    "birth_info": data if analysis_type in ["ziwei", "bazi"] else None,
                    "divination_info": data if analysis_type == "liuyao" else None,
                    "calculation_type": analysis_type,
                    "session_id": session_id
                },
                timestamp=datetime.now().isoformat()
            )

            # 异步调用后台Agent
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            response = loop.run_until_complete(
                calculator_agent.process_message(calculation_request)
            )

            loop.close()

            if response.success:
                # 检查是否需要用户选择
                if response.data.get('cache_type') == 'different_type':
                    # 显示智能选择界面
                    st.info("🔍 **发现相同生辰信息的分析记录**")

                    message = response.data.get('message', '')
                    st.markdown(message)

                    # 提供选择按钮
                    col1, col2, col3 = st.columns(3)

                    options = response.data.get('options', {})
                    existing_analyses = response.data.get('existing_analyses', [])
                    requested_type = response.data.get('requested_type', '')
                    birth_info = response.data.get('birth_info', {})

                    with col1:
                        if st.button(f"🆕 {options.get('create_new', '创建新分析')}", key="force_create_new", use_container_width=True):
                            # 强制创建新分析 - 重新调用但跳过缓存检查
                            st.session_state.force_create_new = True
                            st.rerun()

                    with col2:
                        if st.button(f"👁️ {options.get('view_existing', '查看已有分析')}", key="view_existing_analysis", use_container_width=True):
                            # 跳转到已有分析
                            if existing_analyses:
                                st.session_state.current_view = 'detail'
                                st.session_state.selected_record = existing_analyses[0]['result_id']
                                st.rerun()

                    with col3:
                        if st.button(f"🔮 {options.get('comprehensive', '综合分析')}", key="create_comprehensive_analysis", use_container_width=True):
                            # 创建综合分析
                            st.session_state.create_comprehensive = True
                            st.rerun()

                    # 显示已有分析的简要信息
                    if existing_analyses:
                        st.markdown("### 📋 已有分析概览")
                        for analysis in existing_analyses:
                            calc_type_name = {
                                'ziwei': '紫薇斗数',
                                'bazi': '八字命理',
                                'liuyao': '六爻占卜'
                            }.get(analysis['calculation_type'], analysis['calculation_type'])

                            with st.expander(f"🔮 {calc_type_name} - {analysis.get('created_at', '')[:10]}"):
                                st.write(analysis.get('summary', '暂无总结')[:200] + "...")

                    return  # 不继续执行后续逻辑

                # 正常的分析创建结果
                result_id = response.data.get("result_id")
                if result_id:
                    st.success(f"✅ 新分析已启动！")
                    st.info(f"📋 记录ID: {result_id[:8]}...")

                    # 切换到详情查看
                    st.session_state.current_view = 'detail'
                    st.session_state.selected_record = result_id
                    if 'selected_analysis_type' in st.session_state:
                        del st.session_state.selected_analysis_type
                    st.rerun()
                else:
                    st.error("❌ 分析创建失败：未返回结果ID")
            else:
                st.error(f"❌ 新分析启动失败: {response.error}")

    except Exception as e:
        st.error(f"❌ 创建新分析异常: {e}")
        import traceback
        with st.expander("错误详情"):
            st.code(traceback.format_exc())

def show_chart_image_detail(cached_result):
    """显示详情页面的排盘图表 - 支持HTML和图片"""
    # 🔧 查找图表路径
    chart_path = cached_result.chart_image_path

    # 检查是否是HTML文件
    if chart_path and chart_path.endswith('.html') and os.path.exists(chart_path):
        st.markdown(f"""
        <div class="detail-section">
            <h4>🌐 HTML可视化图表</h4>
        </div>
        """, unsafe_allow_html=True)

        try:
            with open(chart_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            st.components.v1.html(html_content, height=800, scrolling=True)
            return
        except Exception as e:
            st.warning(f"⚠️ HTML图表显示失败: {e}")

    # 如果不是HTML，尝试查找图表文件
    image_path = chart_path

    if not image_path:
        # 尝试查找对应的图表文件
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            # 优先查找HTML文件
            html_files = [f for f in os.listdir(charts_dir) if f.startswith("fusion_chart_") and f.endswith(".html")]
            if html_files:
                html_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_dir, x)), reverse=True)
                html_path = os.path.join(charts_dir, html_files[0])

                st.markdown(f"""
                <div class="detail-section">
                    <h4>🌐 HTML可视化图表</h4>
                </div>
                """, unsafe_allow_html=True)

                try:
                    with open(html_path, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    st.components.v1.html(html_content, height=800, scrolling=True)
                    return
                except Exception as e:
                    st.warning(f"⚠️ HTML图表显示失败: {e}")

            # 如果没有HTML，查找图片文件
            chart_files = [f for f in os.listdir(charts_dir) if f.startswith("integrated_chart_") and f.endswith(".png")]
            if chart_files:
                chart_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_dir, x)), reverse=True)
                image_path = os.path.join(charts_dir, chart_files[0])

    if image_path and os.path.exists(image_path):
        st.markdown(f"""
        <div class="detail-section">
            <h4>🖼️ 传统图片格式</h4>
        </div>
        """, unsafe_allow_html=True)

        # 图片展示
        col1, col2, col3 = st.columns([1, 4, 1])
        with col2:
            st.image(image_path, caption="紫薇斗数排盘图", use_container_width=True)

        # 图片信息
        file_size = os.path.getsize(image_path)
        st.markdown(f"""
        <div class="detail-section">
            <p><strong>文件路径:</strong> {image_path}</p>
            <p><strong>文件大小:</strong> {file_size:,} bytes</p>
            <p><strong>修改时间:</strong> {datetime.fromtimestamp(os.path.getmtime(image_path)).strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown("""
        <div class="detail-section">
            <h4>⚠️ 排盘图片生成中...</h4>
            <p>图片正在后台生成，请稍后刷新查看。</p>
        </div>
        """, unsafe_allow_html=True)

        # 🔧 显示可用的图片文件列表用于调试
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            chart_files = [f for f in os.listdir(charts_dir) if f.endswith(".png")]
            if chart_files:
                with st.expander("🔍 调试信息 - 可用图片文件"):
                    chart_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_dir, x)), reverse=True)
                    for i, file in enumerate(chart_files[:5]):
                        file_path = os.path.join(charts_dir, file)
                        file_time = os.path.getmtime(file_path)
                        time_str = datetime.fromtimestamp(file_time).strftime("%Y-%m-%d %H:%M:%S")
                        st.text(f"{i+1}. {file} ({time_str})")

def show_angle_analyses_detail(cached_result):
    """显示详情页面的12角度分析"""
    detailed_analysis = cached_result.detailed_analysis

    if isinstance(detailed_analysis, dict):
        angle_analyses = detailed_analysis.get("angle_analyses", {})

        # 角度名称映射
        angle_names = {
            "personality_destiny": "🏛️ 命宫分析 - 性格命运核心特征",
            "wealth_fortune": "💰 财富分析 - 财运状况与理财投资",
            "marriage_love": "💕 婚姻分析 - 感情婚姻与桃花运势",
            "health_wellness": "🏥 健康分析 - 身体状况与养生建议",
            "career_achievement": "💼 事业分析 - 职业发展与成就潜力",
            "children_creativity": "👶 子女分析 - 生育状况与子女关系",
            "interpersonal_relationship": "🤝 人际分析 - 社交关系与贵人运",
            "education_learning": "📚 学业分析 - 教育学习与知识发展",
            "family_environment": "🏠 家庭分析 - 家庭环境与亲情关系",
            "travel_relocation": "✈️ 迁移分析 - 搬迁旅行与环境变化",
            "spiritual_blessing": "🙏 精神分析 - 精神状态与福德运势",
            "authority_parents": "👑 权威分析 - 领导能力与权威地位"
        }

        # 统计信息
        completed_angles = len([v for v in angle_analyses.values() if v and len(v) > 100])
        total_words = sum(len(v) for v in angle_analyses.values() if v)
        avg_words = total_words / completed_angles if completed_angles > 0 else 0

        st.markdown(f"""
        <div class="detail-section">
            <h4>📊 分析统计</h4>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin: 1rem 0;">
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; color: #4CAF50; font-weight: bold;">{completed_angles}</div>
                    <div style="font-size: 0.8rem; color: #ccc;">已完成角度</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; color: #2196F3; font-weight: bold;">{total_words:,}</div>
                    <div style="font-size: 0.8rem; color: #ccc;">总字数</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; color: #FF9800; font-weight: bold;">{avg_words:.0f}</div>
                    <div style="font-size: 0.8rem; color: #ccc;">平均字数</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; color: #9C27B0; font-weight: bold;">{completed_angles/12*100:.1f}%</div>
                    <div style="font-size: 0.8rem; color: #ccc;">完成度</div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # 显示每个角度的分析
        for angle_key, angle_name in angle_names.items():
            if angle_key in angle_analyses and angle_analyses[angle_key]:
                content = angle_analyses[angle_key]
                word_count = len(content)

                st.markdown(f"""
                <div class="detail-section">
                    <h4>{angle_name}</h4>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <span class="status-badge status-success">已完成</span>
                        <span class="status-badge status-info">{word_count:,}字</span>
                    </div>
                </div>
                """, unsafe_allow_html=True)

                # 内容展示
                with st.expander(f"查看完整内容", expanded=False):
                    # 处理换行符并显示
                    formatted_content = content.replace('\n', '<br>')
                    st.markdown(f"""
                    <div style="background: #2d2d2d; padding: 1rem; border-radius: 8px; max-height: 500px; overflow-y: auto; line-height: 1.6;">
                        {formatted_content}
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div class="detail-section" style="opacity: 0.6;">
                    <h4>{angle_name}</h4>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span class="status-badge status-warning">生成中</span>
                        <span style="color: #ccc; font-size: 0.8rem;">⏳ 等待分析...</span>
                    </div>
                </div>
                """, unsafe_allow_html=True)

    else:
        st.markdown("""
        <div class="detail-section">
            <h4>⚠️ 分析数据格式异常</h4>
            <p>详细分析数据格式不正确，无法正常显示。</p>
        </div>
        """, unsafe_allow_html=True)

# 旧函数已被新的架构替代，保留continue_analysis函数

def continue_analysis(result_id):
    """继续未完成的分析"""
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import AgentMessage, MessageType

        calculator_agent = FortuneCalculatorAgent()

        # 获取现有的缓存结果
        cached_result = calculator_agent.cache.get_result(result_id)
        if not cached_result:
            st.error(f"❌ 未找到分析结果: {result_id}")
            return

        # 检查是否需要继续分析
        detailed_analysis = cached_result.detailed_analysis
        if isinstance(detailed_analysis, dict):
            angle_analyses = detailed_analysis.get("angle_analyses", {})
            completed = len([v for v in angle_analyses.values() if v and len(v) > 100])

            if completed >= 12:
                st.info("✅ 分析已完成，无需继续")
                return

            # 🔧 实现继续分析逻辑
            st.info(f"🔄 继续分析中...当前已完成 {completed}/12 个角度")

            # 构建继续分析请求
            birth_info = cached_result.birth_info
            session_id = f"continue_{result_id[:8]}"

            calculation_request = AgentMessage(
                message_id=f"continue_{datetime.now().timestamp()}",
                message_type=MessageType.CALCULATION_REQUEST,
                sender_id="web_interface",
                receiver_id=calculator_agent.agent_id,
                content={
                    "user_message": f"继续分析请求：{birth_info}",
                    "birth_info": birth_info,
                    "calculation_type": "ziwei",  # 根据实际情况调整
                    "session_id": session_id,
                    "continue_analysis": True,
                    "existing_result_id": result_id
                },
                timestamp=datetime.now().isoformat()
            )

            # 启动后台继续分析
            import threading
            def background_continue_analysis():
                try:
                    print(f"🔄 继续分析线程启动: {result_id}")

                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    response = loop.run_until_complete(
                        calculator_agent.process_message(calculation_request)
                    )

                    if response.success:
                        print(f"✅ 继续分析启动成功: {result_id}")
                    else:
                        print(f"❌ 继续分析启动失败: {response.error}")

                    loop.close()

                except Exception as e:
                    print(f"❌ 继续分析异常: {e}")

            # 启动后台线程
            continue_thread = threading.Thread(target=background_continue_analysis, daemon=True)
            continue_thread.start()

            st.success("✅ 继续分析已启动，请等待进度更新")
        else:
            st.warning("⚠️ 分析数据格式异常，无法继续")

    except Exception as e:
        st.error(f"❌ 继续分析失败: {e}")
        import traceback
        st.code(traceback.format_exc())

def render_clean_cache():
    """渲染清理缓存页面"""
    st.markdown("## 🧹 清理缓存")

    # 返回按钮
    if st.button("← 返回", key="clean_cache_back"):
        st.session_state.current_view = 'overview'
        st.rerun()

    st.markdown("---")

    # 获取缓存统计信息
    cache_records = get_all_cache_records()

    if cache_records:
        # 显示缓存统计
        st.markdown("### 📊 缓存统计")

        col1, col2, col3, col4 = st.columns(4)

        total_records = len(cache_records)

        # 修复：根据分析类型判断完成状态
        completed_records = 0
        in_progress = 0

        for r in cache_records:
            calculation_type = r.get('calculation_type', 'ziwei')

            if calculation_type in ['compatibility', 'liuyao']:
                # 合盘分析和六爻占卜：completed_angles=1且有内容就是完成
                if r['completed_angles'] > 0 and r['total_words'] > 0:
                    completed_records += 1
                elif r['has_analysis']:
                    in_progress += 1
            else:
                # 命理分析：12个角度完成
                if r['completed_angles'] >= 12:
                    completed_records += 1
                elif 0 < r['completed_angles'] < 12:
                    in_progress += 1

        pending = total_records - completed_records - in_progress

        with col1:
            st.metric("总记录", total_records)
        with col2:
            st.metric("已完成", completed_records)
        with col3:
            st.metric("进行中", in_progress)
        with col4:
            st.metric("待处理", pending)

        # 计算缓存大小
        cache_size = calculate_cache_size()
        st.markdown(f"**缓存总大小**: {cache_size}")

        st.markdown("---")

        # 清理选项
        st.markdown("### 🗑️ 清理选项")

        # 选择性清理
        st.markdown("#### 选择性清理")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🧹 清理已完成的分析", key="clean_completed", use_container_width=True):
                if st.session_state.get('confirm_clean_completed', False):
                    deleted_count = clean_completed_cache()
                    st.success(f"✅ 已清理 {deleted_count} 个已完成的分析记录")
                    st.session_state.confirm_clean_completed = False
                    st.rerun()
                else:
                    st.session_state.confirm_clean_completed = True
                    st.warning("⚠️ 再次点击确认清理已完成的分析")

        with col2:
            if st.button("🗑️ 清理失败的分析", key="clean_failed", use_container_width=True):
                if st.session_state.get('confirm_clean_failed', False):
                    deleted_count = clean_failed_cache()
                    st.success(f"✅ 已清理 {deleted_count} 个失败的分析记录")
                    st.session_state.confirm_clean_failed = False
                    st.rerun()
                else:
                    st.session_state.confirm_clean_failed = True
                    st.warning("⚠️ 再次点击确认清理失败的分析")

        st.markdown("#### 全量清理")

        # 危险操作 - 全部清理
        st.warning("⚠️ **危险操作** - 以下操作将删除所有缓存数据，无法恢复！")

        if st.button("💥 清理所有缓存", key="clean_all", use_container_width=True):
            if st.session_state.get('confirm_clean_all', False):
                deleted_count = clean_all_cache()
                st.success(f"✅ 已清理所有缓存，共删除 {deleted_count} 个记录")
                st.session_state.confirm_clean_all = False
                st.rerun()
            else:
                st.session_state.confirm_clean_all = True
                st.error("🚨 再次点击确认清理所有缓存（此操作不可恢复）")

        st.markdown("---")

        # 清理图片文件
        st.markdown("### 🖼️ 图片文件清理")

        chart_files = get_chart_files()
        if chart_files:
            st.markdown(f"发现 {len(chart_files)} 个图片文件")

            if st.button("🗑️ 清理孤立图片", key="clean_orphan_images", use_container_width=True):
                if st.session_state.get('confirm_clean_images', False):
                    deleted_count = clean_orphan_images()
                    st.success(f"✅ 已清理 {deleted_count} 个孤立图片文件")
                    st.session_state.confirm_clean_images = False
                    st.rerun()
                else:
                    st.session_state.confirm_clean_images = True
                    st.warning("⚠️ 再次点击确认清理孤立图片")
        else:
            st.info("未发现图片文件")

    else:
        st.info("当前没有缓存数据")

def calculate_cache_size():
    """计算缓存大小"""
    try:
        total_size = 0
        cache_dir = 'data/calculation_cache'

        if os.path.exists(cache_dir):
            for filename in os.listdir(cache_dir):
                filepath = os.path.join(cache_dir, filename)
                if os.path.isfile(filepath):
                    total_size += os.path.getsize(filepath)

        # 添加图片文件大小
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            for filename in os.listdir(charts_dir):
                filepath = os.path.join(charts_dir, filename)
                if os.path.isfile(filepath):
                    total_size += os.path.getsize(filepath)

        # 格式化大小
        if total_size < 1024:
            return f"{total_size} B"
        elif total_size < 1024 * 1024:
            return f"{total_size / 1024:.1f} KB"
        else:
            return f"{total_size / (1024 * 1024):.1f} MB"

    except Exception as e:
        return "计算失败"

def clean_completed_cache():
    """清理已完成的分析缓存"""
    try:
        cache_dir = 'data/calculation_cache'
        deleted_count = 0

        if os.path.exists(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.endswith('.json') and filename != 'index.json':
                    filepath = os.path.join(cache_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 检查是否已完成
                        detailed_analysis = data.get('detailed_analysis', {})
                        if isinstance(detailed_analysis, dict):
                            angle_analyses = detailed_analysis.get('angle_analyses', {})
                            completed_angles = len([v for v in angle_analyses.values() if v and len(v) > 100])

                            if completed_angles >= 12:
                                os.remove(filepath)
                                deleted_count += 1

                    except Exception as e:
                        continue

        return deleted_count

    except Exception as e:
        return 0

def clean_failed_cache():
    """清理失败的分析缓存"""
    try:
        cache_dir = 'data/calculation_cache'
        deleted_count = 0

        if os.path.exists(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.endswith('.json') and filename != 'index.json':
                    filepath = os.path.join(cache_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 检查是否失败（没有分析内容）
                        detailed_analysis = data.get('detailed_analysis', {})
                        if isinstance(detailed_analysis, dict):
                            angle_analyses = detailed_analysis.get('angle_analyses', {})
                            completed_angles = len([v for v in angle_analyses.values() if v and len(v) > 100])

                            if completed_angles == 0:
                                os.remove(filepath)
                                deleted_count += 1

                    except Exception as e:
                        # 文件损坏也算失败
                        os.remove(filepath)
                        deleted_count += 1

        return deleted_count

    except Exception as e:
        return 0

def clean_all_cache():
    """清理所有缓存"""
    try:
        deleted_count = 0

        # 清理缓存文件
        cache_dir = 'data/calculation_cache'
        if os.path.exists(cache_dir):
            for filename in os.listdir(cache_dir):
                filepath = os.path.join(cache_dir, filename)
                if os.path.isfile(filepath):
                    os.remove(filepath)
                    deleted_count += 1

        # 清理图片文件
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            for filename in os.listdir(charts_dir):
                filepath = os.path.join(charts_dir, filename)
                if os.path.isfile(filepath):
                    os.remove(filepath)

        return deleted_count

    except Exception as e:
        return 0

def get_chart_files():
    """获取图片文件列表"""
    try:
        chart_files = []
        charts_dir = "charts"

        if os.path.exists(charts_dir):
            for filename in os.listdir(charts_dir):
                if filename.endswith(('.png', '.jpg', '.jpeg', '.gif')):
                    chart_files.append(filename)

        return chart_files

    except Exception as e:
        return []

def clean_orphan_images():
    """清理孤立的图片文件"""
    try:
        deleted_count = 0
        charts_dir = "charts"

        if not os.path.exists(charts_dir):
            return 0

        # 获取所有缓存记录中引用的图片
        referenced_images = set()
        cache_dir = 'data/calculation_cache'

        if os.path.exists(cache_dir):
            for filename in os.listdir(cache_dir):
                if filename.endswith('.json') and filename != 'index.json':
                    filepath = os.path.join(cache_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        chart_path = data.get('chart_image_path')
                        if chart_path:
                            chart_filename = os.path.basename(chart_path)
                            referenced_images.add(chart_filename)

                    except Exception as e:
                        continue

        # 删除未被引用的图片
        for filename in os.listdir(charts_dir):
            if filename.endswith(('.png', '.jpg', '.jpeg', '.gif')):
                if filename not in referenced_images:
                    filepath = os.path.join(charts_dir, filename)
                    os.remove(filepath)
                    deleted_count += 1

        return deleted_count

    except Exception as e:
        return 0

# 旧的分析函数已被新架构替代

# 旧的显示函数已被新架构替代，保留必要的辅助函数

if __name__ == "__main__":
    main()
