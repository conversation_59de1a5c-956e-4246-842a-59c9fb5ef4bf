#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修复后的算法
"""

def simple_test():
    """简单测试"""
    try:
        print("🔮 简单测试修复后的算法")
        print("=" * 40)
        
        # 直接使用py-iztro测试
        import py_iztro
        astro = py_iztro.Astro()
        
        print("📅 测试: 1985年4月23日亥时女命")
        astrolabe = astro.by_solar('1985-4-23', 11, '女')
        
        print("✅ 排盘成功")
        print(f"命宫地支: {astrolabe.earthly_branch_of_soul_palace}")
        print(f"身宫地支: {astrolabe.earthly_branch_of_body_palace}")
        
        # 手动实现修复逻辑
        earthly_branches = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        
        # 创建地支到宫位的映射
        branch_to_palace = {}
        for palace in astrolabe.palaces:
            branch_to_palace[palace.earthly_branch] = palace
        
        # 找到命宫的地支位置
        soul_palace_branch = astrolabe.earthly_branch_of_soul_palace
        soul_palace_index = earthly_branches.index(soul_palace_branch)
        
        # 十二宫名称（从命宫开始逆时针排列）
        palace_names = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                       "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]
        
        print("\n🏰 修复后的十二宫配置:")
        print("-" * 30)
        
        # 按正确的宫位顺序填充数据
        for i, palace_name in enumerate(palace_names):
            # 计算当前宫位对应的地支索引（逆时针）
            branch_index = (soul_palace_index - i) % 12
            branch = earthly_branches[branch_index]
            
            if branch in branch_to_palace:
                palace = branch_to_palace[branch]
                
                # 获取该宫的星曜
                major_stars = [star.name for star in palace.major_stars] if hasattr(palace, 'major_stars') else []
                is_body_palace = getattr(palace, 'is_body_palace', False)
                
                body_mark = " [身宫]" if is_body_palace else ""
                stars_display = " ".join(major_stars) if major_stars else "无主星"
                
                print(f"{palace_name}({branch}){body_mark}: {stars_display}")
        
        # 验证关键结果
        print("\n🎯 验证结果:")
        print("-" * 20)
        
        # 找命宫
        ming_gong_branch = astrolabe.earthly_branch_of_soul_palace
        ming_gong_palace = branch_to_palace[ming_gong_branch]
        ming_gong_stars = [star.name for star in ming_gong_palace.major_stars]
        
        print(f"命宫: {ming_gong_branch}宫 {ming_gong_stars}")
        print(f"期望: 巳宫 ['巨门']")
        print(f"匹配: {'✅' if ming_gong_branch == '巳' and '巨门' in ming_gong_stars else '❌'}")
        
        # 找身宫
        body_palace_branch = astrolabe.earthly_branch_of_body_palace
        body_palace = branch_to_palace[body_palace_branch]
        body_stars = [star.name for star in body_palace.major_stars]
        
        print(f"身宫: {body_palace_branch}宫 {body_stars}")
        print(f"期望: 卯宫 ['太阴']")
        print(f"匹配: {'✅' if body_palace_branch == '卯' and '太阴' in body_stars else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    print()
    if success:
        print("🎉 简单测试成功！")
    else:
        print("💥 简单测试失败！")
