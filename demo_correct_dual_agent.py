#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确双Agent协作模式演示
"""

import asyncio
import sys
import time
from datetime import datetime
sys.path.append('.')

async def demo_correct_collaboration():
    """演示正确的双Agent协作模式"""
    print("🎭 正确双Agent协作模式演示")
    print("=" * 80)
    print("🎯 目标: 展示沟通Agent主导的自然对话流程")
    print("=" * 80)
    
    try:
        # 导入正确的组件
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建系统
        master_agent = MasterCustomerAgent("demo_master")
        calculator_agent = FortuneCalculatorAgent("demo_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print("✅ 正确双Agent系统初始化完成")
        print(f"   主控沟通Agent: {master_agent.agent_id}")
        print(f"   计算Agent: {calculator_agent.agent_id}")
        print(f"   简化协调器: {coordinator.coordinator_id}")
        
        # 演示对话场景
        session_id = "demo_session"
        
        print(f"\n🎬 开始演示自然对话流程")
        print("=" * 60)
        
        # 场景1: 用户问候
        print(f"\n📱 场景1: 用户初次访问")
        print("-" * 40)
        
        user_msg = "你好"
        print(f"👤 用户: {user_msg}")
        
        result = await coordinator.handle_user_message(session_id, user_msg)
        ai_response = result.get('response', '')
        stage = result.get('stage', 'unknown')
        
        print(f"🤖 AI: {ai_response[:120]}...")
        print(f"📊 当前阶段: {stage}")
        print(f"⏱️  响应时间: {result.get('processing_time', 0):.2f}秒")
        
        # 场景2: 算命请求
        print(f"\n📱 场景2: 用户提出算命需求")
        print("-" * 40)
        
        user_msg = "我想算命，看看我的运势"
        print(f"👤 用户: {user_msg}")
        
        result = await coordinator.handle_user_message(session_id, user_msg)
        ai_response = result.get('response', '')
        stage = result.get('stage', 'unknown')
        
        print(f"🤖 AI: {ai_response[:120]}...")
        print(f"📊 当前阶段: {stage}")
        print(f"⏱️  响应时间: {result.get('processing_time', 0):.2f}秒")
        
        # 场景3: 提供部分信息
        print(f"\n📱 场景3: 用户提供部分生辰信息")
        print("-" * 40)
        
        user_msg = "我是1992年3月8日出生的"
        print(f"👤 用户: {user_msg}")
        
        result = await coordinator.handle_user_message(session_id, user_msg)
        ai_response = result.get('response', '')
        stage = result.get('stage', 'unknown')
        
        print(f"🤖 AI: {ai_response[:120]}...")
        print(f"📊 当前阶段: {stage}")
        print(f"⏱️  响应时间: {result.get('processing_time', 0):.2f}秒")
        
        # 场景4: 补充完整信息
        print(f"\n📱 场景4: 用户补充完整信息")
        print("-" * 40)
        
        user_msg = "下午2点，女性"
        print(f"👤 用户: {user_msg}")
        
        result = await coordinator.handle_user_message(session_id, user_msg)
        ai_response = result.get('response', '')
        stage = result.get('stage', 'unknown')
        
        print(f"🤖 AI: {ai_response[:120]}...")
        print(f"📊 当前阶段: {stage}")
        print(f"⏱️  响应时间: {result.get('processing_time', 0):.2f}秒")
        
        # 等待计算完成
        print(f"\n⏳ 等待计算Agent处理...")
        await asyncio.sleep(3)
        
        # 检查计算状态
        status = await coordinator.get_session_status(session_id)
        print(f"🔍 计算状态: {status}")
        
        # 场景5: 后续问答
        print(f"\n📱 场景5: 基于结果的问答")
        print("-" * 40)
        
        user_msg = "我的事业运势怎么样？"
        print(f"👤 用户: {user_msg}")
        
        result = await coordinator.handle_user_message(session_id, user_msg)
        ai_response = result.get('response', '')
        stage = result.get('stage', 'unknown')
        
        print(f"🤖 AI: {ai_response[:120]}...")
        print(f"📊 当前阶段: {stage}")
        print(f"⏱️  响应时间: {result.get('processing_time', 0):.2f}秒")
        
        # 演示总结
        print(f"\n🎯 演示总结")
        print("=" * 60)
        
        coord_stats = coordinator.get_stats()
        master_stats = master_agent.get_stats()
        calc_stats = calculator_agent.get_stats()
        
        print(f"📊 系统性能:")
        print(f"   总请求数: {coord_stats['total_requests']}")
        print(f"   成功率: {coord_stats.get('success_rate', 0)*100:.1f}%")
        print(f"   平均响应时间: {coord_stats['average_response_time']:.2f}秒")
        
        print(f"\n🗣️ 主控沟通Agent:")
        print(f"   处理消息: {master_stats['messages_processed']}")
        print(f"   平均耗时: {master_stats['average_processing_time']:.2f}秒")
        print(f"   角色: 主导整个对话流程")
        
        print(f"\n🧮 计算Agent:")
        print(f"   处理消息: {calc_stats['messages_processed']}")
        print(f"   平均耗时: {calc_stats['average_processing_time']:.2f}秒")
        print(f"   角色: 按需提供计算服务")
        
        print(f"\n🌟 协作模式优势:")
        print(f"   ✅ 自然流畅的对话体验")
        print(f"   ✅ 智能的信息收集过程")
        print(f"   ✅ 高效的资源利用")
        print(f"   ✅ 完整的状态管理")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def compare_architectures():
    """对比新旧架构"""
    print(f"\n🔄 新旧架构对比")
    print("=" * 80)
    
    print(f"❌ 旧架构 (错误模式):")
    print(f"   用户输入 → 协调器 → 同时调用沟通Agent + 计算Agent")
    print(f"   问题: 不自然、资源浪费、复杂度高")
    
    print(f"\n✅ 新架构 (正确模式):")
    print(f"   用户输入 → 主控沟通Agent → 收集信息 → 按需调用计算Agent → 解释结果")
    print(f"   优势: 自然对话、高效资源利用、简单清晰")
    
    print(f"\n🎯 关键改进:")
    print(f"   1. 沟通Agent成为主控，负责整个流程")
    print(f"   2. 计算Agent变为服务提供者，按需调用")
    print(f"   3. 协调器简化为路由器，降低复杂度")
    print(f"   4. 对话状态完整管理，支持多轮交互")

def main():
    """主函数"""
    print("🎭 正确双Agent协作模式完整演示")
    print("=" * 80)
    print("🎯 展示沟通Agent主导的自然对话流程")
    print("🚀 Web界面地址: http://localhost:8504")
    print("=" * 80)
    
    # 运行演示
    success = asyncio.run(demo_correct_collaboration())
    
    # 架构对比
    asyncio.run(compare_architectures())
    
    # 最终总结
    print(f"\n" + "=" * 80)
    print("🏁 演示完成")
    print("=" * 80)
    
    if success:
        print("🎉 正确的双Agent协作模式演示成功！")
        print("\n💪 核心成就:")
        print("   ✅ 实现了沟通Agent主导的正确架构")
        print("   ✅ 自然流畅的对话体验")
        print("   ✅ 智能的信息收集和状态管理")
        print("   ✅ 高效的按需计算调用")
        
        print("\n🚀 现在您可以:")
        print("   1. 访问 http://localhost:8504 体验Web界面")
        print("   2. 进行自然的算命对话")
        print("   3. 观察Agent协作过程")
        print("   4. 享受专业的算命服务")
        
        print("\n🌟 这就是您最初设想的正确双Agent协作模式！")
        
    else:
        print("💥 演示过程中遇到问题，请检查系统配置")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
