#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试卦象显示修复
"""

import requests
import json
import time

def test_display_fix():
    """测试显示修复"""
    base_url = "http://localhost:5000"
    
    print("=== 测试卦象显示修复 ===")
    
    # 获取最新的六爻记录
    try:
        response = requests.get(f"{base_url}/api/liuyao/list", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('records'):
                # 获取第一条记录
                record = result['records'][0]
                liuyao_id = record['liuyao_id']
                
                print(f"📋 获取记录: {liuyao_id}")
                print(f"📝 问题: {record['question']}")
                print(f"📊 状态: {record['status']}")
                
                if record['status'] == 'completed':
                    # 获取详细结果
                    detail_response = requests.get(f"{base_url}/api/liuyao/{liuyao_id}")
                    if detail_response.status_code == 200:
                        detail_result = detail_response.json()
                        if detail_result.get('success'):
                            analysis_content = detail_result.get('analysis_content', '')
                            
                            print(f"\n✅ 获取分析内容成功，长度: {len(analysis_content)}字")
                            
                            # 检查关键内容
                            checks = {
                                "卦象图形": "【卦象图形】" in analysis_content,
                                "详细卦象": "【详细卦象】" in analysis_content,
                                "本卦变卦": "本卦" in analysis_content and "变卦" in analysis_content,
                                "六神正确": all(spirit in analysis_content for spirit in ["青龙", "朱雀", "勾陈", "腾蛇", "白虎", "玄武"]),
                                "旬空显示": "旬空" in analysis_content,
                                "神煞显示": any(sha in analysis_content for sha in ["驿马", "桃花", "天乙贵人"]),
                                "日期显示": "乙丑日" in analysis_content
                            }
                            
                            print("\n📊 内容检查结果:")
                            for item, passed in checks.items():
                                status = "✅" if passed else "❌"
                                print(f"  {status} {item}: {'通过' if passed else '未通过'}")
                            
                            # 提取并显示卦象部分
                            print(f"\n🔮 卦象信息提取:")
                            
                            # 提取卦象图形
                            import re
                            hexagram_match = re.search(r'【卦象图形】(.*?)【详细卦象】', analysis_content, re.DOTALL)
                            if hexagram_match:
                                hexagram_content = hexagram_match.group(1).strip()
                                print("=" * 50)
                                print("卦象图形:")
                                print(hexagram_content[:300] + "..." if len(hexagram_content) > 300 else hexagram_content)
                                print("=" * 50)
                            
                            # 提取详细表格
                            detail_match = re.search(r'【详细卦象】(.*?)【硬币投掷详情】', analysis_content, re.DOTALL)
                            if detail_match:
                                detail_content = detail_match.group(1).strip()
                                print("详细卦象表格:")
                                print(detail_content[:500] + "..." if len(detail_content) > 500 else detail_content)
                                print("=" * 50)
                            
                        else:
                            print(f"❌ 获取详细结果失败: {detail_result.get('error')}")
                    else:
                        print(f"❌ 详细结果请求失败: {detail_response.status_code}")
                else:
                    print(f"⏳ 记录状态为: {record['status']}，等待完成...")
            else:
                print("❌ 没有找到六爻记录")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_display_fix()
