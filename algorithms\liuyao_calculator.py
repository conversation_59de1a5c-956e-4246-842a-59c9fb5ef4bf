#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六爻算卦真实算法实现
基于yxf_yixue_py项目
"""

import sys
import os
import datetime
from typing import Dict, List, Optional

# 添加yxf_yixue_py路径
yixue_path = os.path.join(os.path.dirname(__file__), 'yxf_yixue_py')
sys.path.append(yixue_path)

try:
    from yxf_yixue.liuyao import LiuyaoApi
    from yxf_yixue.wannianli import WannianliApi
    LIUYAO_AVAILABLE = True
    print("✅ 六爻算法模块导入成功")
except ImportError as e:
    LIUYAO_AVAILABLE = False
    print(f"❌ 六爻算法模块导入失败: {e}")

class LiuyaoCalculator:
    """六爻算卦计算器"""

    def __init__(self):
        if not LIUYAO_AVAILABLE:
            raise ImportError("六爻算法模块未安装")
        self.api = <PERSON>ya<PERSON><PERSON><PERSON>()
        self.wannianli = <PERSON><PERSON><PERSON><PERSON><PERSON>()

    def divine_by_time(self, year: int, month: int, day: int, hour: int, minute: int = 0) -> Dict:
        """根据时间起卦"""
        try:
            # 创建时间对象
            datetime_obj = datetime.datetime(year, month, day, hour, minute)

            # 排盘
            result = self.api.paipan(datetime_obj, qiguafangfa='标准时间起卦')

            # 获取格式化输出
            formatted_output = self.api.print_pan()

            return {
                "success": True,
                "method": "时间起卦",
                "datetime": f"{year}年{month}月{day}日{hour}时{minute}分",
                "raw_result": result,
                "formatted_output": formatted_output,
                "divination_type": "六爻算卦"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"时间起卦失败: {str(e)}"
            }

    def divine_by_numbers(self, year: int, month: int, day: int, hour: int,
                         num1: int, num2: int, minute: int = 0) -> Dict:
        """根据两个数字起卦"""
        try:
            # 创建时间对象
            datetime_obj = datetime.datetime(year, month, day, hour, minute)

            # 排盘
            result = self.api.paipan(datetime_obj, qiguafangfa='两数字起卦', qiguashuru=[num1, num2])

            # 获取格式化输出
            formatted_output = self.api.print_pan()

            return {
                "success": True,
                "method": "数字起卦",
                "datetime": f"{year}年{month}月{day}日{hour}时{minute}分",
                "numbers": [num1, num2],
                "raw_result": result,
                "formatted_output": formatted_output,
                "divination_type": "六爻算卦"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"数字起卦失败: {str(e)}"
            }

    def divine_with_analysis(self, year: int, month: int, day: int, hour: int,
                           method: str = "time", numbers: Optional[List[int]] = None) -> Dict:
        """起卦并进行传统分析"""
        try:
            # 创建时间对象
            datetime_obj = datetime.datetime(year, month, day, hour, 0)

            # 根据方法起卦
            if method == "time":
                result = self.api.paipan(datetime_obj, qiguafangfa='标准时间起卦')
            elif method == "numbers" and numbers and len(numbers) >= 2:
                result = self.api.paipan(datetime_obj, qiguafangfa='两数字起卦', qiguashuru=numbers[:2])
            else:
                return {"success": False, "error": "无效的起卦方法或参数"}

            # 进行传统分析（只标记，不调用有问题的output_addition方法）
            analysis_result = self.api.get_chuantongfenxi()

            # 直接获取基础排盘输出，避免调用有问题的方法
            formatted_output = self.api.P.output() if self.api.P else "排盘输出失败"

            return {
                "success": True,
                "method": f"{'时间' if method == 'time' else '数字'}起卦+传统分析",
                "datetime": f"{year}年{month}月{day}日{hour}时",
                "numbers": numbers if method == "numbers" else None,
                "raw_result": result,
                "analysis_result": analysis_result,
                "formatted_output": formatted_output,
                "analysis_note": "已进行传统分析标记，详细分析功能需要完善",
                "divination_type": "六爻算卦+分析"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"起卦分析失败: {str(e)}"
            }

    def get_calendar_info(self, year: int, month: int, day: int, hour: int) -> Dict:
        """获取万年历信息"""
        try:
            datetime_obj = datetime.datetime(year, month, day, hour, 0)
            calendar_info = self.wannianli.get_Calendar(datetime_obj)

            return {
                "success": True,
                "calendar_info": calendar_info,
                "formatted_calendar": self.wannianli.print_Calendar(datetime_obj)
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"获取万年历信息失败: {str(e)}"
            }

def test_liuyao():
    """测试六爻算卦功能"""
    if not LIUYAO_AVAILABLE:
        print("❌ 六爻算法模块不可用，无法测试")
        return

    try:
        calc = LiuyaoCalculator()

        print("=== 六爻算卦测试 ===")

        # 测试1: 时间起卦
        print("\n1. 时间起卦测试:")
        result1 = calc.divine_by_time(2024, 6, 18, 20, 30)
        if result1["success"]:
            print(f"起卦方法: {result1['method']}")
            print(f"时间: {result1['datetime']}")
            print("卦象结果:")
            print(result1["formatted_output"])
        else:
            print(f"错误: {result1['error']}")

        # 测试2: 数字起卦
        print("\n2. 数字起卦测试:")
        result2 = calc.divine_by_numbers(2024, 6, 18, 20, 7, 9, 30)
        if result2["success"]:
            print(f"起卦方法: {result2['method']}")
            print(f"时间: {result2['datetime']}")
            print(f"数字: {result2['numbers']}")
            print("卦象结果:")
            print(result2["formatted_output"])
        else:
            print(f"错误: {result2['error']}")

        # 测试3: 带分析的起卦
        print("\n3. 带传统分析的起卦测试:")
        result3 = calc.divine_with_analysis(2024, 6, 18, 20, "numbers", [3, 8])
        if result3["success"]:
            print(f"起卦方法: {result3['method']}")
            print(f"时间: {result3['datetime']}")
            print("分析结果:")
            print(result3["formatted_output"])
        else:
            print(f"错误: {result3['error']}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_liuyao()
