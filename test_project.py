#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目基础测试
"""

import sys
import os
from pathlib import Path

def test_project_structure():
    """测试项目结构"""
    print("🧪 测试项目结构...")
    
    required_dirs = [
        "algorithms",
        "models", 
        "services",
        "utils",
        "web",
        "config",
        "data",
        "tests"
    ]
    
    all_exist = True
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✅ {dir_name}")
        else:
            print(f"❌ {dir_name}")
            all_exist = False
    
    return all_exist

def test_config():
    """测试配置模块"""
    print("\n🧪 测试配置模块...")
    
    try:
        from config import config
        print(f"✅ 配置模块导入成功")
        print(f"   LLM模型: {config.llm.model_name}")
        print(f"   API密钥: {'已配置' if config.llm.api_key else '未配置'}")
        print(f"   温度设置: {config.llm.temperature}")
        return True
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False

def test_models():
    """测试数据模型"""
    print("\n🧪 测试数据模型...")
    
    try:
        from models.birth_info import BirthInfo
        birth_info = BirthInfo(1988, 6, 1, 11, "男")
        print(f"✅ BirthInfo创建成功: {birth_info.to_display_string()}")
        
        cache_key = birth_info.get_cache_key()
        print(f"✅ 缓存键生成: {cache_key[:16]}...")
        
        return True
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False

def test_utils():
    """测试工具模块"""
    print("\n🧪 测试工具模块...")
    
    try:
        from utils.simple_logger import get_logger
        logger = get_logger()
        logger.info("测试日志消息")
        print("✅ 日志工具正常")
        
        from utils.cache_manager import get_cache
        cache = get_cache()
        
        # 测试缓存功能
        test_key = "test_key"
        test_data = {"test": "data"}
        
        cache.set(test_key, test_data)
        cached_data = cache.get(test_key)
        
        if cached_data == test_data:
            print("✅ 缓存功能正常")
            cache.delete(test_key)
        else:
            print("❌ 缓存功能异常")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 工具模块测试失败: {e}")
        return False

def test_services():
    """测试服务模块"""
    print("\n🧪 测试服务模块...")
    
    try:
        from services.chart_service import ChartService
        from models.birth_info import BirthInfo
        
        chart_service = ChartService()
        print("✅ 排盘服务创建成功")
        
        # 测试生辰信息
        birth_info = BirthInfo(1988, 6, 1, 11, "男")
        
        # 尝试生成排盘（可能因算法依赖而失败）
        try:
            chart_data = chart_service.generate_chart(birth_info)
            if chart_data.success:
                print("✅ 排盘生成成功")
            else:
                print(f"⚠️ 排盘生成失败: {chart_data.error_message}")
        except Exception as e:
            print(f"⚠️ 排盘生成异常（可能是算法依赖问题）: {e}")
        
        return True
    except Exception as e:
        print(f"❌ 服务模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔮 智能算命AI系统 v4.0 重构版测试")
    print("=" * 50)
    
    tests = [
        ("项目结构", test_project_structure),
        ("配置模块", test_config),
        ("数据模型", test_models),
        ("工具模块", test_utils),
        ("服务模块", test_services)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目基础功能正常")
        print("\n📋 下一步建议:")
        print("1. 配置API密钥: 编辑 config/.env.example 为 .env")
        print("2. 安装依赖: pip install -r config/requirements.txt")
        print("3. 启动应用: python start.py")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
        print("\n🔧 可能的解决方案:")
        print("1. 检查Python环境和依赖包")
        print("2. 确保所有文件都已正确创建")
        print("3. 检查文件路径和导入语句")

if __name__ == "__main__":
    main()
