#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人性化六爻算卦工具
集成到人性化交互系统中的六爻占卜功能
"""

import logging
import time
import datetime
from typing import Dict, Any, List, Optional
from core.tools.base_tool import BaseTool

logger = logging.getLogger(__name__)

class HumanizedLiuyaoTool(BaseTool):
    """人性化六爻算卦工具"""

    def __init__(self):
        """初始化六爻工具"""
        super().__init__(
            name="humanized_liuyao",
            description="人性化六爻算卦占卜工具",
            version="1.0.0"
        )

        # 初始化六爻计算器 - 只使用真实算法
        try:
            from algorithms.liuyao_calculator import LiuyaoCalculator
            self.liuyao_calculator = LiuyaoCalculator()
            logger.info("真实六爻计算器初始化成功")
        except Exception as e:
            logger.error(f"真实六爻计算器初始化失败: {e}")
            logger.error("六爻占卜功能不可用 - 需要安装真实算法模块")
            self.liuyao_calculator = None

    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行六爻算卦分析

        Args:
            intent: 意图识别结果
            context: 会话上下文

        Returns:
            六爻分析结果
        """
        try:
            logger.info("开始执行人性化六爻算卦分析")

            # 1. 检查六爻计算器 - 必须使用真实算法
            if not self.liuyao_calculator:
                return {
                    "success": False,
                    "error": "六爻计算器未初始化",
                    "message": "六爻占卜功能暂时不可用，需要安装真实算法模块"
                }

            # 2. 提取问题信息
            question_info = self._extract_question_info(intent, context)
            if not question_info:
                return {
                    "success": False,
                    "error": "缺少问题信息",
                    "message": "请告诉我您想要占卜的具体问题，例如：我的事业发展如何？"
                }

            # 3. 调用六爻算法 - 只使用真实算法
            calculation_result = self._calculate_liuyao(question_info)

            if not calculation_result.get("success"):
                return {
                    "success": False,
                    "error": calculation_result.get("error", "算法调用失败"),
                    "message": "六爻起卦失败，请稍后重试"
                }

            # 4. 调用LLM进行六爻分析
            analysis_result = self._analyze_liuyao_with_llm(calculation_result, question_info)

            # 5. 返回成功结果（包含LLM分析）
            return {
                "success": True,
                "type": "liuyao_analysis",
                "calculation_result": calculation_result,
                "question_info": question_info,
                "analysis": analysis_result.get("analysis", ""),
                "chart_image": analysis_result.get("chart_image", ""),
                "message": "六爻占卜分析完成"
            }

        except Exception as e:
            logger.error(f"六爻算卦执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "六爻算卦过程中出现错误，请稍后重试"
            }

    def _extract_question_info(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """提取问题信息"""
        try:
            entities = intent.get("entities", {})
            original_message = intent.get("original_message", "")

            # 提取问题 - 多种来源尝试
            question = (
                original_message or
                entities.get("question") or
                context.get("question") or
                context.get("user_message") or  # 添加这个常用字段
                ""
            )

            if not question:
                logger.warning("未找到问题信息")
                return None

            # 确定问题类型
            question_type = self._determine_question_type(question)

            # 确定起卦方法
            divination_method = entities.get("method", "time")  # 默认时间起卦

            # 提取数字（如果是数字起卦）
            numbers = entities.get("numbers", [])

            question_info = {
                "question": question,
                "question_type": question_type,
                "method": divination_method,
                "numbers": numbers
            }

            logger.info(f"成功提取问题信息: {question_info}")
            return question_info

        except Exception as e:
            logger.error(f"提取问题信息失败: {e}")
            return None

    def _determine_question_type(self, question: str) -> str:
        """确定问题类型"""
        question_lower = question.lower()

        if any(keyword in question for keyword in ["事业", "工作", "职业", "升职", "跳槽"]):
            return "career"
        elif any(keyword in question for keyword in ["财运", "财富", "赚钱", "投资", "理财"]):
            return "wealth"
        elif any(keyword in question for keyword in ["感情", "爱情", "婚姻", "恋爱", "结婚"]):
            return "love"
        elif any(keyword in question for keyword in ["健康", "身体", "疾病", "病情"]):
            return "health"
        elif any(keyword in question for keyword in ["学业", "考试", "升学", "学习"]):
            return "study"
        else:
            return "general"

    def _calculate_liuyao(self, question_info: Dict[str, str]) -> Dict[str, Any]:
        """调用六爻算法进行计算"""
        try:
            logger.info(f"开始六爻计算: {question_info}")

            # 获取当前时间
            now = datetime.datetime.now()

            method = question_info.get("method", "time")
            logger.info(f"起卦方式: {method}")

            if method == "🕐" or method == "time":
                # 时间起卦
                result = self.liuyao_calculator.divine_by_time(
                    year=now.year,
                    month=now.month,
                    day=now.day,
                    hour=now.hour,
                    minute=now.minute
                )
                logger.info("使用时间起卦")
            elif method == "numbers" or method == "🔢":
                # 数字起卦（手动输入）
                numbers = question_info.get("numbers", [])
                if len(numbers) >= 2:
                    result = self.liuyao_calculator.divine_by_numbers(
                        year=now.year,
                        month=now.month,
                        day=now.day,
                        hour=now.hour,
                        num1=int(numbers[0]),
                        num2=int(numbers[1])
                    )
                    logger.info(f"使用手动数字起卦: {numbers[0]}, {numbers[1]}")
                else:
                    return {
                        "success": False,
                        "error": "数字起卦需要两个数字"
                    }
            elif method == "🎲" or method == "random":
                # 随机数字起卦
                import random
                num1 = random.randint(1, 100)
                num2 = random.randint(1, 100)
                result = self.liuyao_calculator.divine_by_numbers(
                    year=now.year,
                    month=now.month,
                    day=now.day,
                    hour=now.hour,
                    num1=num1,
                    num2=num2
                )
                logger.info(f"使用随机数字起卦: {num1}, {num2}")
            elif method == "📱" or method == "device":
                # 设备信息起卦（使用时间作为替代）
                result = self.liuyao_calculator.divine_by_time(
                    year=now.year,
                    month=now.month,
                    day=now.day,
                    hour=now.hour,
                    minute=now.minute
                )
                logger.info("使用设备信息起卦（时间方式）")
            else:
                # 默认使用时间起卦
                result = self.liuyao_calculator.divine_by_time(
                    year=now.year,
                    month=now.month,
                    day=now.day,
                    hour=now.hour,
                    minute=now.minute
                )
                logger.info("使用默认时间起卦")

            if result.get("success"):
                logger.info("六爻计算成功")
                return result
            else:
                error_msg = result.get("error", "未知错误")
                logger.error(f"六爻计算失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }

        except Exception as e:
            logger.error(f"六爻计算异常: {e}")
            return {
                "success": False,
                "error": f"六爻计算异常: {str(e)}"
            }

    def _analyze_liuyao_with_llm(self, calculation_result: Dict[str, Any], question_info: Dict[str, str]) -> Dict[str, Any]:
        """使用LLM分析六爻卦象"""
        try:
            logger.info("开始LLM六爻分析")

            # 获取卦象信息
            raw_result = calculation_result.get("raw_result", {})
            formatted_output = calculation_result.get("formatted_output", "")
            method = calculation_result.get("method", "时间起卦")
            datetime_str = calculation_result.get("datetime", "")

            # 构建分析提示词
            analysis_prompt = self._build_liuyao_analysis_prompt(
                question_info, raw_result, formatted_output, method, datetime_str
            )

            # 调用LLM进行分析
            from core.nlu.llm_client import LLMClient
            llm_client = LLMClient()

            analysis_response = llm_client.chat_completion(
                messages=[
                    {"role": "system", "content": "你是一位专业的六爻占卜师，擅长用通俗易懂的语言为用户解卦，语言自然流畅。"},
                    {"role": "user", "content": analysis_prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )

            if analysis_response:  # chat_completion返回字符串或None
                analysis_text = analysis_response
                logger.info("LLM六爻分析成功")

                return {
                    "success": True,
                    "analysis": analysis_text,
                    "chart_image": "",  # 暂时没有图片生成
                    "processing_time": 0
                }
            else:
                logger.error("LLM六爻分析失败: 返回空响应")
                return {
                    "success": False,
                    "analysis": "分析生成失败，请稍后重试",
                    "chart_image": "",
                    "error": "LLM调用失败"
                }

        except Exception as e:
            logger.error(f"LLM六爻分析异常: {e}")
            return {
                "success": False,
                "analysis": "分析过程中出现异常，请稍后重试",
                "chart_image": "",
                "error": str(e)
            }

    def _build_liuyao_analysis_prompt(self, question_info: Dict[str, str], raw_result: Dict[str, Any],
                                    formatted_output: str, method: str, datetime_str: str) -> str:
        """构建六爻分析提示词"""
        question = question_info.get("question", "")
        question_type = question_info.get("question_type", "general")

        # 提取卦象关键信息
        主卦 = raw_result.get("主卦", "未知卦")
        变卦 = raw_result.get("变卦", "未知卦")
        动爻 = raw_result.get("动爻", [])
        盘 = raw_result.get("盘", {})

        # 构建卦象描述
        卦象描述 = f"""
卦象信息：
{formatted_output}

主卦：{主卦}
变卦：{变卦}
动爻：{', '.join(动爻) if 动爻 else '无'}
"""

        # 根据问题类型调整分析重点
        分析重点 = {
            "wealth": "财运、投资、理财方面",
            "career": "事业、工作、职业发展方面",
            "love": "感情、婚姻、恋爱方面",
            "health": "健康、身体状况方面",
            "study": "学业、考试、学习方面",
            "general": "综合运势方面"
        }.get(question_type, "综合运势方面")

        prompt = f"""
请为用户进行专业的六爻卦象分析。

【用户问题】
{question}

【卦象信息】
{卦象描述}

【分析要求】
1. 重点分析{分析重点}的情况
2. 根据六神、六亲、五行、世应等要素进行分析
3. 分析动爻的影响和变化趋势
4. 给出实用的建议和指导
5. 语言通俗易懂，避免过多专业术语

【分析结构】
请按以下结构进行分析：

**卦象解读**
- 主卦含义和当前情况
- 变卦含义和发展趋势
- 动爻影响和变化因素

**具体分析**
- 针对问题的详细分析
- 有利因素和不利因素
- 时机把握和注意事项

**建议指导**
- 具体的行动建议
- 需要注意的问题
- 最佳的处理方式

请用通俗易懂的语言进行分析，字数控制在800-1200字左右。
"""

        return prompt

    def can_handle(self, intent: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查是否能处理该意图"""
        return self.validate_input(intent, context)

    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """验证输入参数"""
        try:
            # 检查是否是六爻相关意图
            intent_type = intent.get("intent", "").lower()
            if intent_type not in self.get_supported_intents():
                return False

            # 检查是否有问题
            entities = intent.get("entities", {})
            original_message = intent.get("original_message", "")
            context_question = context.get("question")

            # 至少要有一个问题来源
            has_question = (
                entities.get("question") or
                original_message or
                context_question
            )

            return bool(has_question)

        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False

    def get_supported_intents(self) -> List[str]:
        """获取支持的意图类型"""
        return ["liuyao", "liuyao_analysis", "六爻", "六爻占卜", "算卦", "占卜"]

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.name,
            "description": self.description,
            "supported_intents": self.get_supported_intents(),
            "required_entities": ["question"],
            "optional_entities": ["method", "numbers"],
            "output_type": "liuyao_analysis",
            "features": [
                "真实六爻算法",
                "时间起卦",
                "数字起卦",
                "卦象分析",
                "人性化交互"
            ]
        }

def test_humanized_liuyao_tool():
    """测试人性化六爻工具"""
    print("🔮 测试人性化六爻工具")
    print("-" * 50)

    try:
        # 创建工具实例
        tool = HumanizedLiuyaoTool()

        # 测试数据
        intent = {
            "intent": "liuyao",
            "original_message": "我想问问我的事业发展如何？",
            "entities": {
                "question": "我的事业发展如何？",
                "method": "time"
            }
        }

        context = {}

        # 执行测试
        result = tool.execute(intent, context)

        if result.get("success"):
            print("✅ 六爻工具执行成功")
            print(f"   类型: {result.get('type')}")
            print(f"   消息: {result.get('message')}")

            # 检查计算结果
            calc_result = result.get("calculation_result", {})
            if calc_result.get("success"):
                print("✅ 六爻计算成功")
                method = calc_result.get("method", "未知")
                datetime_str = calc_result.get("datetime", "未知")
                print(f"   起卦方法: {method}")
                print(f"   起卦时间: {datetime_str}")

                raw_result = calc_result.get("raw_result", {})
                if "主卦" in raw_result:
                    print(f"   主卦: {raw_result.get('主卦', '未知')}")
                if "变卦" in raw_result:
                    print(f"   变卦: {raw_result.get('变卦', '未知')}")
            else:
                print(f"❌ 六爻计算失败: {calc_result.get('error')}")
        else:
            print(f"❌ 六爻工具执行失败: {result.get('error')}")

        # 测试工具信息
        tool_info = tool.get_tool_info()
        print(f"\n工具信息:")
        print(f"   名称: {tool_info['name']}")
        print(f"   描述: {tool_info['description']}")
        print(f"   支持意图: {tool_info['supported_intents']}")

        return result.get("success", False)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_humanized_liuyao_tool()
