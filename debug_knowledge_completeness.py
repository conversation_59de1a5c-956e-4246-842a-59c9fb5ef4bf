#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试知识库完整性 - 确保所有数据都能正确输入
"""

import sys
sys.path.append('.')

import json
from pathlib import Path
from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
from core.chat.knowledge_base import ChatKnowledgeBase

def debug_raw_data_structure():
    """调试原始数据结构"""
    print("🔍 调试原始数据结构")
    print("=" * 50)
    
    try:
        # 获取缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        if not cache_files:
            print("❌ 没有找到缓存文件")
            return False
            
        cache_file = cache_files[0]
        result_id = cache_file.stem
        
        print(f"📄 分析文件: {cache_file.name}")
        
        # 直接读取JSON文件
        with open(cache_file, 'r', encoding='utf-8') as f:
            raw_json = json.load(f)
            
        print(f"\n📊 JSON文件顶级字段:")
        for key in raw_json.keys():
            print(f"  - {key}")
            
        # 详细分析raw_calculation字段
        raw_calculation = raw_json.get('raw_calculation', {})
        print(f"\n📊 raw_calculation字段结构:")
        print(f"  类型: {type(raw_calculation)}")
        print(f"  字段数: {len(raw_calculation) if isinstance(raw_calculation, dict) else 'N/A'}")
        
        if isinstance(raw_calculation, dict):
            print(f"  字段列表:")
            for key, value in raw_calculation.items():
                value_type = type(value).__name__
                value_size = len(value) if hasattr(value, '__len__') else 'N/A'
                print(f"    - {key}: {value_type} (size: {value_size})")
                
                # 深入分析重要字段
                if key in ['ziwei', 'bazi', 'ziwei_analysis', 'bazi_analysis']:
                    print(f"      详细结构:")
                    if isinstance(value, dict):
                        for subkey in list(value.keys())[:5]:  # 只显示前5个
                            print(f"        - {subkey}: {type(value[subkey]).__name__}")
                    elif isinstance(value, str):
                        print(f"        内容预览: {value[:100]}...")
                        
        # 分析detailed_analysis字段
        detailed_analysis = raw_json.get('detailed_analysis', {})
        print(f"\n📊 detailed_analysis字段结构:")
        if isinstance(detailed_analysis, dict):
            angle_analyses = detailed_analysis.get('angle_analyses', {})
            print(f"  angle_analyses: {len(angle_analyses)} 个分析")
            for angle_key, content in angle_analyses.items():
                content_length = len(content) if isinstance(content, str) else 0
                print(f"    - {angle_key}: {content_length} 字符")
                
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_extraction():
    """测试知识提取的完整性"""
    print(f"\n🧪 测试知识提取完整性")
    print("=" * 50)
    
    try:
        # 获取缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        cache_file = cache_files[0]
        result_id = cache_file.stem
        cached_result = cache.get_result(result_id)
        
        print(f"📄 测试缓存: {cache_file.name}")
        
        # 创建知识库并加载
        knowledge_base = ChatKnowledgeBase()
        
        # 分步测试每个加载函数
        print(f"\n1️⃣ 测试基本信息加载...")
        birth_info = cached_result.birth_info
        print(f"  生辰信息: {birth_info}")
        
        # 手动添加基本信息测试
        knowledge_base.add_knowledge("basic_info", "出生年份", birth_info.get('year', ''), source="birth_info")
        knowledge_base.add_knowledge("basic_info", "出生月份", birth_info.get('month', ''), source="birth_info")
        knowledge_base.add_knowledge("basic_info", "出生日期", birth_info.get('day', ''), source="birth_info")
        knowledge_base.add_knowledge("basic_info", "出生时辰", birth_info.get('hour', ''), source="birth_info")
        knowledge_base.add_knowledge("basic_info", "性别", birth_info.get('gender', ''), source="birth_info")
        
        basic_items = knowledge_base.get_knowledge_by_category("basic_info")
        print(f"  ✅ 基本信息: {len(basic_items)} 项")
        
        print(f"\n2️⃣ 测试紫薇数据加载...")
        raw_calculation = cached_result.raw_calculation
        print(f"  raw_calculation字段: {list(raw_calculation.keys())}")
        
        # 测试紫薇数据提取
        knowledge_base._load_ziwei_knowledge(raw_calculation)
        ziwei_items = knowledge_base.get_knowledge_by_category("ziwei_palace")
        print(f"  ✅ 紫薇宫位: {len(ziwei_items)} 项")
        
        if len(ziwei_items) > 0:
            print(f"  紫薇数据示例:")
            for item in ziwei_items[:3]:
                print(f"    - {item.key}: {item.value}")
        else:
            print(f"  ❌ 紫薇数据提取失败，尝试调试...")
            debug_ziwei_extraction(raw_calculation)
            
        print(f"\n3️⃣ 测试八字数据加载...")
        knowledge_base._load_bazi_knowledge(raw_calculation)
        bazi_items = knowledge_base.get_knowledge_by_category("bazi_pillar")
        bazi_elements = knowledge_base.get_knowledge_by_category("bazi_elements")
        bazi_dayun = knowledge_base.get_knowledge_by_category("bazi_dayun")
        
        print(f"  ✅ 八字四柱: {len(bazi_items)} 项")
        print(f"  ✅ 五行分析: {len(bazi_elements)} 项")
        print(f"  ✅ 大运流年: {len(bazi_dayun)} 项")
        
        if len(bazi_items) == 0:
            print(f"  ❌ 八字数据提取失败，尝试调试...")
            debug_bazi_extraction(raw_calculation)
            
        print(f"\n4️⃣ 测试分析结果加载...")
        detailed_analysis = cached_result.detailed_analysis
        knowledge_base._load_analysis_knowledge(detailed_analysis)
        analysis_items = knowledge_base.get_knowledge_by_category("analysis_result")
        print(f"  ✅ 分析结果: {len(analysis_items)} 项")
        
        # 总体统计
        total_stats = knowledge_base.get_stats()
        print(f"\n📊 知识库总体统计:")
        for key, value in total_stats.items():
            print(f"  {key}: {value}")
            
        # 检查完整性
        completeness_score = 0
        if total_stats['basic_info_count'] >= 5:
            completeness_score += 1
        if total_stats['ziwei_palace_count'] >= 10:
            completeness_score += 1
        if total_stats['bazi_pillar_count'] >= 3:
            completeness_score += 1
        if total_stats['analysis_result_count'] >= 1:
            completeness_score += 1
            
        print(f"\n🎯 知识库完整性评分: {completeness_score}/4")
        
        if completeness_score >= 3:
            print("✅ 知识库数据基本完整")
        else:
            print("❌ 知识库数据不完整，需要修复")
            
        return completeness_score >= 3
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_ziwei_extraction(raw_calculation):
    """调试紫薇数据提取"""
    print(f"    🔍 调试紫薇数据提取:")
    
    # 检查所有可能的紫薇数据字段
    possible_fields = ['ziwei', 'ziwei_analysis', 'ziwei_data', 'ziwei_result']
    
    for field in possible_fields:
        if field in raw_calculation:
            data = raw_calculation[field]
            print(f"      找到字段 {field}: {type(data)}")
            
            if isinstance(data, dict):
                print(f"        子字段: {list(data.keys())}")
                
                # 查找宫位数据
                palace_fields = ['palaces', '宫位', 'palace_data', 'gongwei']
                for palace_field in palace_fields:
                    if palace_field in data:
                        palaces = data[palace_field]
                        print(f"        找到宫位数据 {palace_field}: {type(palaces)}")
                        if isinstance(palaces, dict):
                            print(f"          宫位列表: {list(palaces.keys())}")
                            
def debug_bazi_extraction(raw_calculation):
    """调试八字数据提取"""
    print(f"    🔍 调试八字数据提取:")
    
    # 检查所有可能的八字数据字段
    possible_fields = ['bazi', 'bazi_analysis', 'bazi_data', 'bazi_result', 'bazi_info']
    
    for field in possible_fields:
        if field in raw_calculation:
            data = raw_calculation[field]
            print(f"      找到字段 {field}: {type(data)}")
            
            if isinstance(data, dict):
                print(f"        子字段: {list(data.keys())}")
                
                # 查找八字信息
                bazi_fields = ['bazi_info', 'pillars', '四柱', 'sizhu']
                for bazi_field in bazi_fields:
                    if bazi_field in data:
                        bazi_info = data[bazi_field]
                        print(f"        找到八字信息 {bazi_field}: {type(bazi_info)}")
                        if isinstance(bazi_info, dict):
                            print(f"          八字字段: {list(bazi_info.keys())}")

if __name__ == "__main__":
    print("🔍 知识库完整性调试")
    print("=" * 60)
    
    # 1. 调试原始数据结构
    structure_ok = debug_raw_data_structure()
    
    if structure_ok:
        # 2. 测试知识提取
        extraction_ok = test_knowledge_extraction()
        
        print(f"\n🎯 调试总结:")
        print(f"  数据结构: {'✅ 正常' if structure_ok else '❌ 异常'}")
        print(f"  知识提取: {'✅ 正常' if extraction_ok else '❌ 异常'}")
        
        if structure_ok and extraction_ok:
            print("🎉 知识库数据基本完整！")
        else:
            print("⚠️  需要进一步修复数据提取逻辑")
    else:
        print("❌ 数据结构异常，无法进行知识提取测试")
