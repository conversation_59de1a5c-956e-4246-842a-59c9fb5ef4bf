#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的八字算法准确性
"""

def test_fixed_bazi():
    """测试修复后的八字算法"""
    print("🔧 测试修复后的八字算法")
    print("=" * 50)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        calc = RealBaziCalculator()
        
        # 测试1988年6月1日11时
        print("📅 测试数据: 1988年6月1日11时(午时)")
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        if result["success"]:
            print("✅ 修复后的八字算法结果:")
            raw_result = result.get("raw_result", {})
            ganzhi = raw_result.get("干支", {})
            
            print(f"  四柱: {ganzhi.get('文本', '')}")
            print(f"  年柱: {ganzhi.get('年柱', '')}")
            print(f"  月柱: {ganzhi.get('月柱', '')}")
            print(f"  日柱: {ganzhi.get('日柱', '')}")
            print(f"  时柱: {ganzhi.get('时柱', '')}")
            print(f"  数据源: {result.get('data_source', '')}")
            
            birth_info = result.get("birth_info", {})
            print(f"  农历: {birth_info.get('lunar_date', '')}")
            print(f"  生肖: {birth_info.get('zodiac', '')}")
            
        else:
            print(f"❌ 错误: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def compare_with_ziwei():
    """与紫薇斗数对比"""
    print("\n🔮 与紫薇斗数对比")
    print("=" * 30)
    
    try:
        # 紫薇斗数结果
        import py_iztro
        astro = py_iztro.Astro()
        astrolabe = astro.by_solar("1988-6-1", 6, "男", "zh-CN")
        ziwei_bazi = astrolabe.chinese_date
        
        # 修复后的八字算法结果
        from algorithms.real_bazi_calculator import RealBaziCalculator
        calc = RealBaziCalculator()
        result = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        if result["success"]:
            bazi_result = result["raw_result"]["干支"]["文本"]
            
            print(f"紫薇斗数: {ziwei_bazi}")
            print(f"八字算法: {bazi_result}")
            
            if ziwei_bazi == bazi_result:
                print("✅ 两个算法结果完全一致！")
                return True
            else:
                print("❌ 结果不一致")
                return False
        else:
            print(f"❌ 八字算法失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def test_multiple_dates():
    """测试多个日期的准确性"""
    print("\n📊 测试多个日期的准确性")
    print("=" * 40)
    
    test_cases = [
        (1988, 6, 1, 11, "男"),
        (1990, 3, 15, 8, "女"),
        (1985, 12, 25, 14, "男"),
        (1995, 7, 20, 20, "女")
    ]
    
    success_count = 0
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        calc = RealBaziCalculator()
        
        for i, (year, month, day, hour, gender) in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {year}年{month}月{day}日{hour}时 {gender}")
            
            # 八字算法
            result = calc.calculate_bazi(year, month, day, hour, 0, gender)
            
            # py-iztro对比
            import py_iztro
            astro = py_iztro.Astro()
            time_index = calc._convert_hour_to_index(hour)
            astrolabe = astro.by_solar(f"{year}-{month}-{day}", time_index, gender, "zh-CN")
            
            if result["success"]:
                bazi_result = result["raw_result"]["干支"]["文本"]
                ziwei_bazi = astrolabe.chinese_date
                
                print(f"   八字算法: {bazi_result}")
                print(f"   py-iztro: {ziwei_bazi}")
                
                if bazi_result == ziwei_bazi:
                    print("   ✅ 一致")
                    success_count += 1
                else:
                    print("   ❌ 不一致")
            else:
                print(f"   ❌ 失败: {result['error']}")
    
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
    
    print(f"\n📈 测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count == len(test_cases)

def main():
    """主函数"""
    print("🔍 修复后八字算法准确性验证")
    print("=" * 60)
    
    # 1. 基础测试
    basic_success = test_fixed_bazi()
    
    # 2. 与紫薇对比
    compare_success = compare_with_ziwei()
    
    # 3. 多日期测试
    batch_success = test_multiple_dates()
    
    print("\n" + "=" * 60)
    print("🎯 验证总结:")
    print(f"基础测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"紫薇对比: {'✅ 通过' if compare_success else '❌ 失败'}")
    print(f"批量测试: {'✅ 通过' if batch_success else '❌ 失败'}")
    
    if basic_success and compare_success and batch_success:
        print("\n🎉 八字算法修复成功！")
        print("✅ 已统一使用py-iztro作为数据源")
        print("✅ 确保了与紫薇斗数的一致性")
    else:
        print("\n⚠️ 仍有问题需要解决")

if __name__ == "__main__":
    main()
