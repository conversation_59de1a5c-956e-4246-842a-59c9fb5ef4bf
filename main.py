#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能算命AI系统主启动脚本 - 完整的生产级启动程序
"""

import os
import sys
import signal
import argparse
import threading
import time
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置和日志
from config.settings import ConfigManager, get_config, validate_config
from utils.logger import init_logging, get_logger, get_performance_monitor
from utils.error_handler import graceful_shutdown, handle_error

# 导入核心组件
from interfaces.unified_api import UnifiedAPI
from interfaces.web_interface import WebInterface

logger = None

def setup_signal_handlers():
    """设置信号处理器"""

    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，开始优雅关闭...")
        graceful_shutdown.shutdown()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def validate_environment():
    """验证运行环境"""

    # 确保logger已初始化
    if 'logger' in globals() and logger:
        logger.info("验证运行环境...")
    else:
        print("验证运行环境...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error("Python版本过低，需要3.8或更高版本")
        return False

    # 检查必要的目录
    required_dirs = ["logs", "charts", "config"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            logger.info(f"创建目录: {dir_name}")

    # 验证配置
    validation = validate_config()
    if not validation["valid"]:
        logger.error("配置验证失败:")
        for issue in validation["issues"]:
            logger.error(f"  - {issue}")
        return False

    logger.info("环境验证通过")
    return True

def start_api_server(config: Dict[str, Any]) -> threading.Thread:
    """启动API服务器"""

    def run_api():
        try:
            logger.info("启动API服务器...")

            api_config = {
                "api_key": config.llm.api_key,
                "model_name": config.llm.model_name,
                "max_history": config.session.max_history,
                "session_timeout": config.session.session_timeout
            }

            api = UnifiedAPI(api_config)

            # 注册关闭处理器
            graceful_shutdown.register_handler(lambda: logger.info("API服务器关闭"))

            # 启动服务器
            api.run(
                host=config.api.host,
                port=config.api.port,
                debug=config.api.debug
            )

        except Exception as e:
            logger.error(f"API服务器启动失败: {e}")
            handle_error(e, {"component": "api_server"})

    thread = threading.Thread(target=run_api, daemon=True)
    thread.start()

    # 等待服务器启动
    time.sleep(2)

    return thread

def start_web_interface(config: Dict[str, Any]) -> threading.Thread:
    """启动Web界面"""

    def run_web():
        try:
            logger.info("启动Web界面...")

            # 使用streamlit命令启动
            import subprocess

            cmd = [
                sys.executable, "-m", "streamlit", "run",
                "interfaces/web_interface.py",
                "--server.port", "8501",
                "--server.address", "0.0.0.0",
                "--server.headless", "true"
            ]

            subprocess.run(cmd)

        except Exception as e:
            logger.error(f"Web界面启动失败: {e}")
            handle_error(e, {"component": "web_interface"})

    thread = threading.Thread(target=run_web, daemon=True)
    thread.start()

    return thread

def monitor_system(config: Dict[str, Any]):
    """系统监控"""

    logger.info("启动系统监控...")

    monitor = get_performance_monitor()

    while True:
        try:
            time.sleep(60)  # 每分钟检查一次

            # 获取性能统计
            stats = monitor.get_performance_stats()

            # 记录关键指标
            if stats["api_requests"] > 0:
                logger.info(f"系统状态 - API请求: {stats['api_requests']}, "
                           f"平均响应时间: {stats['avg_api_response_time']}s, "
                           f"LLM调用: {stats['llm_calls']}")

            # 检查异常情况
            if stats["avg_api_response_time"] > 10:
                logger.warning(f"API响应时间过长: {stats['avg_api_response_time']}s")

            if stats["avg_llm_time"] > 60:
                logger.warning(f"LLM响应时间过长: {stats['avg_llm_time']}s")

        except Exception as e:
            logger.error(f"系统监控异常: {e}")
            time.sleep(60)

def print_startup_info(config: Dict[str, Any]):
    """打印启动信息"""

    print("\n" + "=" * 80)
    print("🔮 智能算命AI系统 v2.0")
    print("=" * 80)
    print(f"🚀 API服务器: http://{config.api.host}:{config.api.port}")
    print(f"🌐 Web界面: http://localhost:8501")
    print(f"🤖 LLM模型: {config.llm.model_name}")
    print(f"📊 环境: {config.environment}")
    print("=" * 80)
    print("\n📋 可用端点:")
    print(f"  • 健康检查: GET  http://localhost:{config.api.port}/health")
    print(f"  • 智能聊天: POST http://localhost:{config.api.port}/v2/chat")
    print(f"  • 工具列表: GET  http://localhost:{config.api.port}/v2/tools")
    print(f"  • 系统统计: GET  http://localhost:{config.api.port}/v2/stats")
    print(f"  • 旧版兼容: POST http://localhost:{config.api.port}/v1/chat/completions")
    print("\n💡 使用示例:")
    print(f"""  curl -X POST http://localhost:{config.api.port}/v2/chat \\
    -H "Content-Type: application/json" \\
    -d '{{"message": "我想看紫薇斗数，1988年6月1日午时男", "session_id": "test"}}'""")
    print("\n🔧 管理命令:")
    print("  • Ctrl+C: 优雅关闭系统")
    print("  • 查看日志: tail -f logs/app.log")
    print("=" * 80)

def main():
    """主函数"""

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="智能算命AI系统")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--api-only", action="store_true", help="仅启动API服务器")
    parser.add_argument("--web-only", action="store_true", help="仅启动Web界面")
    parser.add_argument("--no-monitor", action="store_true", help="禁用系统监控")
    parser.add_argument("--port", "-p", type=int, help="API端口号")
    parser.add_argument("--debug", action="store_true", help="调试模式")

    args = parser.parse_args()

    try:
        # 初始化配置
        config_manager = ConfigManager(args.config)
        config = config_manager.get_config()

        # 命令行参数覆盖
        if args.port:
            config.api.port = args.port
        if args.debug:
            config.debug = True
            config.api.debug = True

        # 初始化日志
        log_config = {
            "level": "DEBUG" if config.debug else config.log.level,
            "format": config.log.format,
            "file_path": config.log.file_path,
            "max_size": config.log.max_size,
            "backup_count": config.log.backup_count
        }
        init_logging(log_config)

        global logger
        logger = get_logger("main")

        # 设置信号处理器
        setup_signal_handlers()

        # 验证环境
        if not validate_environment():
            sys.exit(1)

        # 打印启动信息
        print_startup_info(config)

        # 启动服务
        api_thread = None
        web_thread = None

        if not args.web_only:
            api_thread = start_api_server(config)
            logger.info("API服务器启动完成")

        if not args.api_only:
            web_thread = start_web_interface(config)
            logger.info("Web界面启动完成")

        # 启动监控
        if not args.no_monitor:
            monitor_thread = threading.Thread(target=monitor_system, args=(config,), daemon=True)
            monitor_thread.start()

        logger.info("系统启动完成，等待请求...")

        # 主循环
        try:
            while True:
                time.sleep(1)

                # 检查线程状态
                if api_thread and not api_thread.is_alive():
                    logger.error("API服务器线程异常退出")
                    break

                if web_thread and not web_thread.is_alive():
                    logger.warning("Web界面线程异常退出")

        except KeyboardInterrupt:
            logger.info("收到中断信号，开始关闭...")

    except Exception as e:
        if logger:
            logger.error(f"系统启动失败: {e}")
            handle_error(e, {"component": "main"})
        else:
            print(f"系统启动失败: {e}")
        sys.exit(1)

    finally:
        # 优雅关闭
        if logger:
            logger.info("系统关闭完成")
        print("\n👋 感谢使用智能算命AI系统！")

if __name__ == "__main__":
    main()
