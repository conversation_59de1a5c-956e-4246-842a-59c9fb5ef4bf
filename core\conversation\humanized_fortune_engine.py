#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人性化算命引擎 - 集成人性化对话管理的完整算命系统
"""

import logging
import time
import random
from typing import Dict, Any, List, Generator, Optional

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from core.conversation.humanized_chat import HumanizedChatManager, ConversationStage
from core.chat.session_manager import SessionManager
from core.nlu.llm_client import LLMClient
from core.tools.tool_selector import ToolSelector

logger = logging.getLogger(__name__)

class HumanizedFortuneEngine:
    """人性化算命引擎 - 模拟真人算命师的交流方式"""

    def __init__(self):
        """初始化人性化算命引擎"""
        # 核心组件
        self.session_manager = SessionManager()
        self.llm_client = LLMClient()
        self.tool_selector = ToolSelector()
        self.chat_manager = HumanizedChatManager()

        # 统计信息
        self.stats = {
            "total_conversations": 0,
            "successful_analyses": 0,
            "user_interruptions": 0,
            "start_time": time.time()
        }

        logger.info("人性化算命引擎初始化完成")

    def process_user_message(self, user_message: str, session_id: str = None) -> List[Dict[str, Any]]:
        """
        处理用户消息 - 返回分段式响应列表

        Args:
            user_message: 用户消息
            session_id: 会话ID

        Returns:
            分段响应列表，每个响应包含消息内容和类型
        """
        if not session_id:
            session_id = f"humanized_session_{int(time.time())}"

        self.stats["total_conversations"] += 1

        try:
            # 获取对话上下文
            chat_context = self.chat_manager.get_conversation_context(session_id)
            session_context = self.session_manager.get_conversation_context(session_id)

            # 智能意图识别
            intent_result = self.llm_client.intent_recognition(user_message, session_context)

            if not intent_result or intent_result.get("intent") == "error":
                return [self._create_response("error", "抱歉，我无法理解您的需求，请重新描述。")]

            # 确保添加original_message字段
            intent_result["original_message"] = user_message

            intent = intent_result["intent"]
            confidence = intent_result["confidence"]
            entities = intent_result.get("entities", {})

            logger.info(f"意图识别: {intent} (置信度: {confidence:.2f})")

            # 根据意图和当前阶段生成响应
            responses = self._generate_conversation_responses(
                session_id, intent_result, session_context, chat_context
            )

            # 更新会话状态
            self._update_session_state(session_id, user_message, intent_result, responses)

            return responses

        except Exception as e:
            logger.error(f"处理用户消息失败: {e}")
            return [self._create_response("error", f"系统处理出错: {str(e)}")]

    def _generate_conversation_responses(self, session_id: str, intent_result: Dict[str, Any],
                                       session_context: Dict[str, Any],
                                       chat_context) -> List[Dict[str, Any]]:
        """生成对话响应序列"""
        intent = intent_result["intent"]
        entities = intent_result.get("entities", {})
        responses = []

        # 处理聊天意图
        if intent == "chat":
            if chat_context.current_stage == ConversationStage.GREETING:
                greeting = self.chat_manager.generate_humanized_response(session_id, "greeting")
                responses.append(self._create_response("chat", greeting))

                # 引导到信息收集
                info_prompt = self.chat_manager.generate_humanized_response(session_id, "info_collection")
                responses.append(self._create_response("info_collection", info_prompt))

                self.chat_manager.update_conversation_stage(session_id, ConversationStage.INFO_COLLECTION)
            else:
                # 处理对话中的聊天
                chat_response = self._handle_general_chat(user_message=intent_result.get("original_message", ""))
                responses.append(self._create_response("chat", chat_response))

        # 处理算命意图
        elif intent in ["ziwei", "bazi", "liuyao", "general"]:
            responses.extend(self._handle_fortune_request(session_id, intent_result, session_context, chat_context))

        # 处理用户问题（打断）
        elif intent == "question":
            interruption_response = self.chat_manager.handle_user_interruption(
                session_id, intent_result.get("original_message", "")
            )
            responses.append(self._create_response("interruption", interruption_response))

            # 尝试回答用户问题
            answer = self._answer_user_question(session_id, intent_result, chat_context)
            if answer:
                responses.append(self._create_response("answer", answer))

            # 询问是否继续
            continue_prompt = "我回答完了您的问题，我们继续之前的分析吗？"
            responses.append(self._create_response("continue_check", continue_prompt))

            self.stats["user_interruptions"] += 1

        else:
            # 默认处理
            default_response = "我理解您的意思，让我为您继续分析。"
            responses.append(self._create_response("default", default_response))

        return responses

    def _handle_fortune_request(self, session_id: str, intent_result: Dict[str, Any],
                              session_context: Dict[str, Any], chat_context) -> List[Dict[str, Any]]:
        """处理算命请求"""
        responses = []

        # 工具选择和执行
        tool_result = self.tool_selector.select_tool(intent_result, session_context)

        if not tool_result.get("success"):
            error_msg = f"算命分析出现问题: {tool_result.get('error')}"
            return [self._create_response("error", error_msg)]

        result_data = tool_result.get("result", {})
        result_type = result_data.get("type", "unknown")

        # 处理不同类型的结果
        if result_type == "entity_collection":
            # 需要收集更多信息
            collection_response = self.chat_manager.generate_humanized_response(
                session_id, "info_collection", result_data
            )
            responses.append(self._create_response("info_collection", collection_response))

        elif result_type in ["ziwei_analysis", "bazi_analysis", "liuyao_analysis"]:
            # 成功的算命分析
            responses.extend(self._generate_analysis_sequence(session_id, result_data, result_type))

        elif result_type == "clarification":
            # 需要澄清
            clarification = result_data.get("message", "请明确您的需求。")
            responses.append(self._create_response("clarification", clarification))

        else:
            # 其他情况
            message = result_data.get("message", "分析完成。")
            responses.append(self._create_response("general", message))

        return responses

    def _generate_analysis_sequence(self, session_id: str, result_data: Dict[str, Any],
                                  analysis_type: str) -> List[Dict[str, Any]]:
        """生成分析序列 - 核心的分段式分析"""
        responses = []

        # 1. 排盘展示阶段
        fortune_type_map = {
            "ziwei_analysis": "紫薇斗数",
            "bazi_analysis": "八字命理",
            "liuyao_analysis": "六爻占卜"
        }

        chart_data = {
            **result_data,
            "fortune_type": fortune_type_map.get(analysis_type, "命理")
        }

        chart_presentation = self.chat_manager.generate_humanized_response(
            session_id, "chart_presentation", chart_data
        )
        responses.append(self._create_response("chart_presentation", chart_presentation))

        self.chat_manager.update_conversation_stage(session_id, ConversationStage.CHART_PRESENTATION)

        # 2. 分析引导阶段
        analysis_intro = self.chat_manager.generate_humanized_response(session_id, "analysis_intro")
        responses.append(self._create_response("analysis_intro", analysis_intro))

        self.chat_manager.update_conversation_stage(session_id, ConversationStage.ANALYSIS_INTRO)

        # 3. 分段分析阶段
        aspects = ["personality", "career", "wealth", "love"]

        for i, aspect in enumerate(aspects):
            # 分析该方面
            aspect_analysis = self.chat_manager.generate_humanized_response(
                session_id, "aspect_analysis", {"aspect": aspect}
            )
            responses.append(self._create_response("aspect_analysis", aspect_analysis))

            # 添加具体的算命分析内容
            detailed_analysis = self._generate_detailed_analysis(result_data, aspect, analysis_type)
            if detailed_analysis:
                responses.append(self._create_response("detailed_analysis", detailed_analysis))

            # 互动检查点（除了最后一个方面）
            if i < len(aspects) - 1:
                interaction_check = self.chat_manager.generate_humanized_response(
                    session_id, "interaction_check"
                )
                responses.append(self._create_response("interaction_check", interaction_check))

        # 4. 综合总结阶段
        synthesis_data = self._prepare_synthesis_data(result_data, analysis_type)
        synthesis = self.chat_manager.generate_humanized_response(
            session_id, "synthesis", synthesis_data
        )
        responses.append(self._create_response("synthesis", synthesis))

        self.chat_manager.update_conversation_stage(session_id, ConversationStage.SYNTHESIS)

        # 5. 结论阶段
        conclusion = self.chat_manager.generate_humanized_response(session_id, "conclusion")
        responses.append(self._create_response("conclusion", conclusion))

        self.chat_manager.update_conversation_stage(session_id, ConversationStage.CONCLUSION)

        self.stats["successful_analyses"] += 1

        return responses

    def _generate_detailed_analysis(self, result_data: Dict[str, Any], aspect: str,
                                  analysis_type: str) -> Optional[str]:
        """生成具体的算命分析内容（使用Few-shot Learning）"""
        try:
            # 映射aspect到category
            aspect_category_map = {
                "personality": "general",
                "career": "career",
                "wealth": "wealth",
                "love": "love"
            }
            category = aspect_category_map.get(aspect, "general")

            # 构建分析问题
            aspect_questions = {
                "personality": "请分析我的性格特质和天赋才能",
                "career": "请分析我的事业运势和工作发展",
                "wealth": "请分析我的财运状况和理财建议",
                "love": "请分析我的感情运势和婚姻状况"
            }

            analysis_question = aspect_questions.get(aspect, f"请分析我的{aspect}运势")

            # 尝试使用Few-shot Learning生成专业分析
            try:
                from core.nlu.llm_client import LLMClient
                llm_client = LLMClient()

                response = llm_client.fewshot_chat(
                    user_message=analysis_question,
                    category=category,
                    temperature=0.8,
                    max_tokens=800
                )

                if response and len(response) > 50:  # 确保响应有足够内容
                    logger.info(f"Few-shot分析成功 - {aspect}: {len(response)}字符")
                    return response
                else:
                    logger.warning(f"Few-shot响应过短，使用降级方法 - {aspect}")

            except Exception as e:
                logger.warning(f"Few-shot Learning失败，使用降级方法: {e}")

            # 降级到原有方法
            calc_result = result_data.get("calculation_result", {})

            if analysis_type == "ziwei_analysis" and "palaces" in calc_result:
                return self._analyze_ziwei_aspect(calc_result, aspect)
            elif analysis_type == "bazi_analysis" and ("bazi" in calc_result or "raw_result" in calc_result):
                return self._analyze_bazi_aspect(calc_result, aspect)
            elif analysis_type == "liuyao_analysis" and ("raw_result" in calc_result or "formatted_output" in calc_result):
                return self._analyze_liuyao_aspect(calc_result, aspect)
            elif analysis_type == "ziwei_analysis" and ("raw_result" in calc_result or "formatted_output" in calc_result):
                return self._analyze_ziwei_aspect(calc_result, aspect)
            else:
                return f"关于{aspect}方面的详细分析正在完善中..."

        except Exception as e:
            logger.error(f"生成详细分析失败: {e}")
            return None

    def _analyze_ziwei_aspect(self, calc_result: Dict[str, Any], aspect: str) -> str:
        """分析紫薇斗数的特定方面（融入专业话术）"""
        palaces = calc_result.get("palaces", {})

        aspect_palace_map = {
            "personality": "命宫",
            "career": "事业宫",
            "wealth": "财帛宫",
            "love": "夫妻宫"
        }

        target_palace = aspect_palace_map.get(aspect, "命宫")

        if target_palace in palaces:
            palace_info = palaces[target_palace]
            main_star = palace_info.get("主星", "未知")
            earthly_branch = palace_info.get("地支", "未知")

            # 根据不同方面提供专业分析
            if aspect == "personality":
                return f"根据您的{target_palace}，主星是{main_star}，地支为{earthly_branch}。这表明您的性格特质会有所波动，建议您保持内心的平衡。您天生具有独特的才能，但需要通过不断的努力才能充分发挥。保持积极的心态将会帮助您实现人生目标。"
            elif aspect == "career":
                return f"从{target_palace}来看，您的主星是{main_star}，地支为{earthly_branch}。最近您的工作运势会有所波动，建议您注意与同事的沟通，避免冲突。职场上会有机会突破，但同时也需要付出更多努力才能取得成功。"
            elif aspect == "wealth":
                return f"根据您的{target_palace}，主星{main_star}配合地支{earthly_branch}，显示您的财运状况整体稳定。建议您在理财方面要谨慎，避免冒险投资。通过稳健的方式积累财富，将会为您带来长期的收益。"
            elif aspect == "love":
                return f"从{target_palace}的角度分析，您的主星{main_star}，地支{earthly_branch}，感情运势需要您多加注意。建议您在感情中保持真诚，避免因为小事产生误解。用心经营感情，将会收获美满的姻缘。"
        else:
            return f"根据您的生辰八字，{target_palace}方面需要您特别关注。建议您保持积极的心态，通过自身的努力来改善运势。"

    def _analyze_bazi_aspect(self, calc_result: Dict[str, Any], aspect: str) -> str:
        """分析八字的特定方面（专业话术）"""
        # 获取八字数据
        raw_result = calc_result.get("raw_result", {})
        bazi_info = calc_result.get("bazi", {})

        # 提取四柱信息
        ganzhi_text = ""
        if raw_result and "干支" in raw_result:
            ganzhi_text = raw_result["干支"].get("文本", "")

        # 提取五行信息
        wuxing_info = ""
        if raw_result and "五行" in raw_result:
            wuxing = raw_result["五行"]
            strong_elements = []
            weak_elements = []
            for element, info in wuxing.items():
                wangcui = info.get("旺衰", "")
                if "旺" in wangcui or "强" in wangcui:
                    strong_elements.append(element)
                elif "弱" in wangcui or "衰" in wangcui:
                    weak_elements.append(element)

            if strong_elements:
                wuxing_info = f"五行中{', '.join(strong_elements)}较旺"
            if weak_elements:
                if wuxing_info:
                    wuxing_info += f"，{', '.join(weak_elements)}偏弱"
                else:
                    wuxing_info = f"五行中{', '.join(weak_elements)}偏弱"

        if raw_result or bazi_info:
            if aspect == "personality":
                base_text = f"根据您的生辰八字{ganzhi_text}，从四柱来看，您的性格特质较为复杂。"
                if wuxing_info:
                    base_text += f"{wuxing_info}，这影响着您的性格倾向。"
                return base_text + "建议您发挥自身的优势，同时注意克制不利的因素。保持内心的平衡，将会帮助您在人际关系中更加游刃有余。"
            elif aspect == "career":
                base_text = f"从八字{ganzhi_text}的角度分析您的事业运势，最近会有一些变化。"
                if wuxing_info:
                    base_text += f"{wuxing_info}，这对您的事业发展有重要影响。"
                return base_text + "建议您抓住机遇，但也要谨慎行事。通过自身的努力和正确的判断，您的事业将会有所突破。"
            elif aspect == "wealth":
                base_text = f"根据您的八字{ganzhi_text}分析，财运方面需要您多加留意。"
                if wuxing_info:
                    base_text += f"{wuxing_info}，这关系到您的财运走向。"
                return base_text + "建议您在投资理财时要谨慎，避免盲目跟风。通过稳健的方式管理财务，将会为您带来稳定的收入。"
            elif aspect == "love":
                base_text = f"从八字{ganzhi_text}来看，您的感情运势有起有伏。"
                if wuxing_info:
                    base_text += f"{wuxing_info}，这影响着您的感情表达方式。"
                return base_text + "建议您在感情中要真诚待人，避免因为误解而产生矛盾。用心经营感情，将会收获幸福的爱情。"
        else:
            return f"根据传统八字命理学，{aspect}方面建议您保持积极的心态，通过自身的修养来提升运势。"

    def _analyze_liuyao_aspect(self, calc_result: Dict[str, Any], aspect: str) -> str:
        """分析六爻的特定方面（专业话术）"""
        # 获取六爻数据
        raw_result = calc_result.get("raw_result", {})
        formatted_output = calc_result.get("formatted_output", "")

        # 提取卦象信息
        main_gua = raw_result.get("主卦", "")
        bian_gua = raw_result.get("变卦", "")
        dong_yao = raw_result.get("动爻", [])

        # 提取卦象详细信息
        pan_info = raw_result.get("盘", {})

        # 分析世应关系
        shi_ying_info = ""
        if pan_info:
            for yao_num, yao_info in pan_info.items():
                shi_ying = yao_info.get("世应", "")
                if shi_ying:
                    liu_qin = yao_info.get("六亲", "")
                    wu_xing = yao_info.get("五行", "")
                    if shi_ying == "世":
                        shi_ying_info += f"世爻为{liu_qin}{wu_xing}，"
                    elif shi_ying == "应":
                        shi_ying_info += f"应爻为{liu_qin}{wu_xing}，"

        # 动爻分析
        dong_yao_info = ""
        if dong_yao:
            dong_yao_info = f"动爻在{', '.join(dong_yao)}，"

        if raw_result or formatted_output:
            if aspect == "personality":
                base_text = f"根据您的六爻卦象{main_gua}，从卦理来看，您的性格特质有其独特之处。"
                if shi_ying_info:
                    base_text += f"{shi_ying_info}这反映了您的内在特质。"
                if dong_yao_info:
                    base_text += f"{dong_yao_info}表明您的性格中有变化的因素。"
                return base_text + "建议您发挥自身的优势，保持内心的平衡，通过不断的自我完善来提升人格魅力。"
            elif aspect == "career":
                base_text = f"从六爻卦象{main_gua}来看，您的事业运势有其特定的走向。"
                if bian_gua:
                    base_text += f"变卦{bian_gua}显示事业会有新的发展。"
                if dong_yao_info:
                    base_text += f"{dong_yao_info}预示着事业上的变化机遇。"
                return base_text + "建议您把握时机，积极进取，通过自身的努力和正确的判断，事业将会有所突破。"
            elif aspect == "wealth":
                base_text = f"根据六爻卦象{main_gua}分析，您的财运状况需要仔细观察。"
                if shi_ying_info:
                    base_text += f"{shi_ying_info}这关系到您的财运基础。"
                if dong_yao_info:
                    base_text += f"{dong_yao_info}暗示财运上的变化。"
                return base_text + "建议您在理财方面要谨慎，通过稳健的方式积累财富，避免冒险投资。"
            elif aspect == "love":
                base_text = f"从六爻卦象{main_gua}来看，您的感情运势有其发展轨迹。"
                if bian_gua:
                    base_text += f"变卦{bian_gua}预示感情会有新的变化。"
                if dong_yao_info:
                    base_text += f"{dong_yao_info}表明感情中的动态因素。"
                return base_text + "建议您在感情中要真诚待人，用心经营，通过相互理解和包容来维护感情。"
        else:
            return f"根据传统六爻占卜学，{aspect}方面建议您保持积极的心态，通过自身的努力来改善运势。"

    def _analyze_ziwei_aspect(self, calc_result: Dict[str, Any], aspect: str) -> str:
        """分析紫薇斗数的特定方面（专业话术）"""
        # 获取紫薇数据
        raw_result = calc_result.get("raw_result", {})
        formatted_output = calc_result.get("formatted_output", "")

        # 提取命盘信息
        palaces = raw_result.get("palaces", {})
        birth_info = raw_result.get("birth_info", {})

        # 分析关键宫位
        ming_gong = palaces.get("命宫", {})  # 命宫
        cai_bo_gong = palaces.get("财帛宫", {})  # 财帛宫
        guan_lu_gong = palaces.get("官禄宫", {})  # 官禄宫
        fu_qi_gong = palaces.get("夫妻宫", {})  # 夫妻宫

        # 提取主星信息
        ming_gong_stars = "、".join(ming_gong.get("major_stars", []))
        cai_bo_stars = "、".join(cai_bo_gong.get("major_stars", []))
        guan_lu_stars = "、".join(guan_lu_gong.get("major_stars", []))
        fu_qi_stars = "、".join(fu_qi_gong.get("major_stars", []))

        if raw_result or formatted_output:
            if aspect == "personality":
                base_text = f"根据您的紫薇斗数命盘，从命宫来看，您的性格特质有其独特之处。"
                if ming_gong_stars:
                    base_text += f"命宫有{ming_gong_stars}，这塑造了您的基本性格。"
                if ming_gong.get("position"):
                    base_text += f"命宫在{ming_gong.get('position')}，影响着您的行为模式。"
                return base_text + "建议您发挥主星的正面特质，通过不断的自我修养来完善人格，这样能够更好地发挥您的天赋潜能。"
            elif aspect == "career":
                base_text = f"从紫薇斗数的官禄宫来看，您的事业运势有其特定的发展轨迹。"
                if guan_lu_stars:
                    base_text += f"官禄宫有{guan_lu_stars}，这决定了您的事业方向和发展潜力。"
                if guan_lu_gong.get("position"):
                    base_text += f"官禄宫在{guan_lu_gong.get('position')}，影响着您的职业选择。"
                return base_text + "建议您根据星曜特性选择合适的发展方向，通过持续的努力和正确的策略，事业将会有显著的成就。"
            elif aspect == "wealth":
                base_text = f"根据紫薇斗数的财帛宫分析，您的财运状况有其内在规律。"
                if cai_bo_stars:
                    base_text += f"财帛宫有{cai_bo_stars}，这关系到您的财富积累能力。"
                if cai_bo_gong.get("position"):
                    base_text += f"财帛宫在{cai_bo_gong.get('position')}，影响着您的理财方式。"
                return base_text + "建议您根据星曜特性制定理财策略，通过稳健的投资和合理的规划，财富将会稳步增长。"
            elif aspect == "love":
                base_text = f"从紫薇斗数的夫妻宫来看，您的感情运势有其发展特点。"
                if fu_qi_stars:
                    base_text += f"夫妻宫有{fu_qi_stars}，这影响着您的感情模式和婚姻状况。"
                if fu_qi_gong.get("position"):
                    base_text += f"夫妻宫在{fu_qi_gong.get('position')}，决定了您的感情表达方式。"
                return base_text + "建议您根据星曜特性调整感情策略，通过真诚的沟通和相互理解，感情将会更加和谐美满。"
        else:
            return f"根据传统紫薇斗数学，{aspect}方面建议您保持积极的心态，通过自身的修养来提升运势。"

    def _prepare_synthesis_data(self, result_data: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """准备综合分析数据"""
        return {
            "summary_points": [
                "性格方面有独特的优势",
                "事业发展前景良好",
                "财运整体稳定",
                "感情生活需要注意平衡"
            ]
        }

    def _handle_general_chat(self, user_message: str) -> str:
        """处理一般聊天"""
        chat_responses = [
            "我明白您的意思。",
            "好的，我理解。",
            "没问题。",
            "我知道了。"
        ]
        return random.choice(chat_responses)

    def _answer_user_question(self, session_id: str, intent_result: Dict[str, Any],
                            chat_context) -> Optional[str]:
        """回答用户问题"""
        # 这里可以基于用户问题和当前分析结果生成回答
        question = intent_result.get("original_message", "")

        if "财运" in question or "钱" in question:
            return "关于财运，从您的命盘来看..."
        elif "事业" in question or "工作" in question:
            return "关于事业发展，您的命理显示..."
        elif "感情" in question or "婚姻" in question:
            return "关于感情方面，您的情况是..."
        else:
            return "这是一个很好的问题，让我为您详细解答..."

    def _create_response(self, response_type: str, content: str) -> Dict[str, Any]:
        """创建响应对象"""
        return {
            "type": response_type,
            "content": content,
            "timestamp": time.time()
        }

    def _update_session_state(self, session_id: str, user_message: str,
                            intent_result: Dict[str, Any], responses: List[Dict[str, Any]]):
        """更新会话状态"""
        try:
            # 准备消息记录
            message_record = {
                "user_message": user_message,
                "intent": intent_result,
                "responses": responses,
                "timestamp": time.time()
            }

            # 准备上下文更新
            context_updates = {}
            entities = intent_result.get("entities", {})

            # 保存出生信息
            if entities:
                birth_info = {}
                for key, value in entities.items():
                    if key.startswith("birth_") and value:
                        birth_info[key.replace("birth_", "")] = value
                    elif key == "gender" and value:
                        birth_info["gender"] = value

                if birth_info:
                    context_updates["birth_info"] = birth_info

            # 更新会话
            self.session_manager.update_session(session_id, context_updates, message_record)

        except Exception as e:
            logger.error(f"会话状态更新失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        uptime = time.time() - self.stats["start_time"]
        return {
            **self.stats,
            "uptime_seconds": uptime,
            "average_interruptions": self.stats["user_interruptions"] / max(1, self.stats["total_conversations"])
        }

def test_humanized_fortune_engine():
    """测试人性化算命引擎"""
    print("🔮 测试人性化算命引擎")
    print("-" * 60)

    try:
        engine = HumanizedFortuneEngine()
        session_id = "test_humanized_engine"

        # 测试对话流程
        test_messages = [
            "你好",
            "我想看紫薇斗数",
            "我1988年6月1日午时出生，男"
        ]

        for i, message in enumerate(test_messages, 1):
            print(f"\n第 {i} 轮对话:")
            print(f"👤 用户: {message}")

            responses = engine.process_user_message(message, session_id)

            for j, response in enumerate(responses, 1):
                print(f"🤖 AI回复 {j}: [{response['type']}] {response['content'][:100]}...")

        # 获取统计信息
        stats = engine.get_stats()
        print(f"\n📊 引擎统计:")
        print(f"   总对话数: {stats['total_conversations']}")
        print(f"   成功分析数: {stats['successful_analyses']}")
        print(f"   用户打断数: {stats['user_interruptions']}")

        print("\n✅ 人性化算命引擎测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import random
    test_humanized_fortune_engine()
