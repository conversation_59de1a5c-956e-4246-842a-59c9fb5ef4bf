#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证双Agent系统
"""

import sys
sys.path.append('.')

def verify_imports():
    """验证导入"""
    try:
        from core.agents.customer_service_agent import CustomerServiceAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.agent_coordinator import AgentCoordinator
        print("✅ 双Agent组件导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def verify_creation():
    """验证创建"""
    try:
        from core.agents.customer_service_agent import CustomerServiceAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.agent_coordinator import AgentCoordinator
        
        customer_agent = CustomerServiceAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = AgentCoordinator()
        
        print("✅ Agent实例创建成功")
        print(f"   沟通Agent: {customer_agent.agent_id}")
        print(f"   计算Agent: {calculator_agent.agent_id}")
        print(f"   协调器: {coordinator.coordinator_id}")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 双Agent系统验证")
    print("=" * 40)
    
    # 验证导入
    import_ok = verify_imports()
    
    # 验证创建
    creation_ok = verify_creation()
    
    # 总结
    print("\n" + "=" * 40)
    if import_ok and creation_ok:
        print("🎉 双Agent系统验证通过！")
        print("✅ 组件导入正常")
        print("✅ 实例创建正常")
        print("🚀 Web界面已启动: http://localhost:8504")
        return True
    else:
        print("❌ 双Agent系统验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
