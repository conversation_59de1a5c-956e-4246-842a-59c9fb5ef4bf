#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的12角度数据传递
"""

import asyncio

async def test_12_angle_data_fix():
    """测试修复后的12角度数据传递"""
    print("🔧 测试修复后的12角度数据传递")
    print("=" * 60)
    
    try:
        # 1. 清理缓存
        print("1️⃣ 清理缓存")
        import os
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            for file in os.listdir(cache_dir):
                if file.endswith('.json'):
                    os.remove(os.path.join(cache_dir, file))
        print("✅ 缓存已清理")
        
        # 2. 测试前3个角度的数据传递
        print("\n2️⃣ 测试前3个角度的数据传递")
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        calculator_agent = FortuneCalculatorAgent()
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        # 获取融合分析数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if not raw_data.get("success"):
            print(f"❌ 融合分析失败: {raw_data.get('error')}")
            return False
        
        print("✅ 融合分析数据获取成功")
        
        # 测试前3个角度
        test_angles = [
            ("命宫分析", "personality_destiny", "性格命运核心特征与天赋潜能"),
            ("财富分析", "wealth_fortune", "财运状况、理财投资与财富积累"),
            ("婚姻分析", "marriage_love", "感情婚姻、桃花运势与配偶关系")
        ]
        
        angle_results = {}
        
        for i, (angle_name, analysis_key, description) in enumerate(test_angles, 1):
            print(f"\n🔍 测试第{i}/3个角度: {angle_name}")
            
            try:
                # 测试单个角度分析
                angle_analysis = await calculator_agent._analyze_single_angle(
                    angle_name, analysis_key, description, raw_data, birth_info, "紫薇+八字融合分析"
                )
                
                if angle_analysis and len(angle_analysis) > 100:
                    angle_results[analysis_key] = angle_analysis
                    word_count = len(angle_analysis)
                    print(f"  ✅ {angle_name}成功: {word_count} 字符")
                    
                    # 检查内容质量
                    content_checks = {
                        "包含紫薇信息": any(keyword in angle_analysis for keyword in ["紫薇", "命宫", "宫位"]),
                        "包含八字信息": any(keyword in angle_analysis for keyword in ["八字", "四柱", "五行"]),
                        "包含具体数据": any(keyword in angle_analysis for keyword in ["天相", "亥", "丁火"]),
                        "内容充实": word_count >= 1000
                    }
                    
                    passed_checks = sum(content_checks.values())
                    total_checks = len(content_checks)
                    
                    print(f"    内容质量: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
                    for check_name, result in content_checks.items():
                        print(f"      {check_name}: {'✅' if result else '❌'}")
                    
                    # 保存测试内容
                    with open(f"test_{analysis_key}_content.txt", "w", encoding="utf-8") as f:
                        f.write(f"{angle_name}测试结果:\n")
                        f.write("=" * 50 + "\n\n")
                        f.write(f"字符数: {word_count}\n")
                        f.write(f"质量评分: {passed_checks}/{total_checks}\n")
                        f.write("\n" + "="*50 + "\n")
                        f.write("分析内容:\n")
                        f.write("="*50 + "\n\n")
                        f.write(angle_analysis)
                    
                    print(f"    💾 内容已保存到 test_{analysis_key}_content.txt")
                    
                else:
                    print(f"  ❌ {angle_name}失败: 内容为空或过短")
                    angle_results[analysis_key] = f"{angle_name}分析失败"
                
            except Exception as e:
                print(f"  ❌ {angle_name}异常: {e}")
                angle_results[analysis_key] = f"{angle_name}分析异常: {str(e)}"
        
        # 3. 统计测试结果
        print(f"\n3️⃣ 测试结果统计")
        print("=" * 60)
        
        successful_angles = [key for key, value in angle_results.items() 
                           if value and len(value) > 100 and "失败" not in value and "异常" not in value]
        
        total_word_count = sum(len(value) for value in angle_results.values() 
                             if value and len(value) > 100 and "失败" not in value and "异常" not in value)
        
        print(f"📊 测试统计:")
        print(f"  成功角度: {len(successful_angles)}/3")
        print(f"  总字符数: {total_word_count:,}")
        print(f"  平均字符数: {total_word_count//len(successful_angles) if successful_angles else 0:,}")
        
        # 显示各角度结果
        print(f"\n📋 各角度详细结果:")
        for analysis_key, content in angle_results.items():
            angle_name = {
                "personality_destiny": "命宫分析",
                "wealth_fortune": "财富分析", 
                "marriage_love": "婚姻分析"
            }.get(analysis_key, analysis_key)
            
            if content and len(content) > 100 and "失败" not in content and "异常" not in content:
                status = "✅"
                length = len(content)
            else:
                status = "❌"
                length = 0
            
            print(f"  {status} {angle_name}: {length:,}字符")
        
        # 4. 总结
        print(f"\n4️⃣ 修复效果总结")
        print("=" * 60)
        
        if len(successful_angles) >= 2:
            print("🎉 12角度数据传递修复成功！")
            print("✅ 数据格式正确传递")
            print("✅ 紫薇+八字数据完整")
            print("✅ 分析内容质量良好")
            print("✅ 不再出现数据获取错误")
            return True
        elif len(successful_angles) >= 1:
            print("⚠️ 12角度数据传递部分修复")
            print("✅ 至少有1个角度成功")
            print("❌ 仍有角度存在问题")
            return True
        else:
            print("❌ 12角度数据传递修复失败")
            print("❌ 所有角度都存在问题")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_12_angle_data_fix())
