#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试合盘数据流
"""

import asyncio
import json

def debug_compatibility_data_flow():
    """调试合盘数据流"""
    print("🔍 调试合盘数据流")
    
    try:
        # 1. 导入必要模块
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        from core.analysis.compatibility_prompt_builder import CompatibilityPromptBuilder
        
        # 2. 测试数据
        person_a_info = {
            "name": "调试男",
            "year": "1985",
            "month": "3", 
            "day": "20",
            "hour": "午时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "调试女",
            "year": "1987",
            "month": "8",
            "day": "15", 
            "hour": "酉时",
            "gender": "女"
        }
        
        analysis_dimension = "emotional_harmony"
        
        print(f"📊 测试数据:")
        print(f"   A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"   B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        
        # 3. 初始化引擎并计算数据
        print("🔧 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()
        
        print("📊 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)
        
        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False
        
        print(f"✅ 合盘数据计算成功")
        
        # 4. 详细检查数据结构
        print(f"\n🔍 详细检查合盘数据结构:")
        
        # 检查person_a数据
        person_a_data = compatibility_data["person_a"]["analysis"]
        print(f"\n👤 person_a 分析数据:")
        print(f"   success: {person_a_data.get('success')}")
        print(f"   keys: {list(person_a_data.keys())}")
        
        if "ziwei_analysis" in person_a_data:
            ziwei_data = person_a_data["ziwei_analysis"]
            print(f"   ziwei_analysis keys: {list(ziwei_data.keys()) if ziwei_data else 'None'}")
            if ziwei_data and "palaces" in ziwei_data:
                palaces = ziwei_data["palaces"]
                print(f"   palaces count: {len(palaces)}")
                print(f"   palace names: {list(palaces.keys())[:5]}...")  # 显示前5个
        
        if "bazi_analysis" in person_a_data:
            bazi_data = person_a_data["bazi_analysis"]
            print(f"   bazi_analysis keys: {list(bazi_data.keys()) if bazi_data else 'None'}")
            if bazi_data and "bazi_info" in bazi_data:
                bazi_info = bazi_data["bazi_info"]
                print(f"   bazi_info keys: {list(bazi_info.keys()) if bazi_info else 'None'}")
        
        # 检查person_b数据
        person_b_data = compatibility_data["person_b"]["analysis"]
        print(f"\n👤 person_b 分析数据:")
        print(f"   success: {person_b_data.get('success')}")
        print(f"   keys: {list(person_b_data.keys())}")
        
        # 5. 测试提示词构建器的数据访问
        print(f"\n🔨 测试提示词构建器数据访问:")
        
        prompt_builder = CompatibilityPromptBuilder()
        
        # 获取分析数据（模拟提示词构建器的逻辑）
        person_a_analysis = compatibility_data["person_a"]["analysis"]
        person_b_analysis = compatibility_data["person_b"]["analysis"]
        
        person_a_ziwei = person_a_analysis.get("ziwei_analysis", {})
        person_a_bazi = person_a_analysis.get("bazi_analysis", {})
        person_b_ziwei = person_b_analysis.get("ziwei_analysis", {})
        person_b_bazi = person_b_analysis.get("bazi_analysis", {})
        
        print(f"   person_a_ziwei: {type(person_a_ziwei)} - {bool(person_a_ziwei)}")
        print(f"   person_a_bazi: {type(person_a_bazi)} - {bool(person_a_bazi)}")
        print(f"   person_b_ziwei: {type(person_b_ziwei)} - {bool(person_b_ziwei)}")
        print(f"   person_b_bazi: {type(person_b_bazi)} - {bool(person_b_bazi)}")
        
        # 6. 测试数据格式化
        print(f"\n📋 测试数据格式化:")
        
        # 测试紫薇数据格式化
        formatted_a_ziwei = prompt_builder._format_detailed_ziwei_data(person_a_ziwei)
        print(f"   A紫薇格式化结果长度: {len(formatted_a_ziwei)}")
        print(f"   A紫薇格式化前50字符: {formatted_a_ziwei[:50]}...")
        
        # 测试八字数据格式化
        formatted_a_bazi = prompt_builder._format_detailed_bazi_data(person_a_bazi)
        print(f"   A八字格式化结果长度: {len(formatted_a_bazi)}")
        print(f"   A八字格式化前50字符: {formatted_a_bazi[:50]}...")
        
        # 7. 构建完整提示词
        print(f"\n🔨 构建完整提示词:")
        prompt = prompt_builder.build_compatibility_prompt(compatibility_data, analysis_dimension)
        
        print(f"   提示词长度: {len(prompt)}字符")
        
        # 检查提示词中是否包含实际数据
        if "数据缺失" in prompt:
            print(f"   ⚠️ 提示词中包含'数据缺失'")
            # 找出哪些部分缺失
            lines = prompt.split('\n')
            missing_lines = [line for line in lines if "数据缺失" in line]
            for line in missing_lines[:3]:  # 显示前3个缺失行
                print(f"     {line.strip()}")
        else:
            print(f"   ✅ 提示词中包含实际数据")
        
        # 8. 保存调试信息
        print(f"\n💾 保存调试信息...")
        
        debug_info = {
            "person_a_info": person_a_info,
            "person_b_info": person_b_info,
            "compatibility_data_keys": list(compatibility_data.keys()),
            "person_a_analysis_keys": list(person_a_analysis.keys()),
            "person_b_analysis_keys": list(person_b_analysis.keys()),
            "person_a_ziwei_exists": bool(person_a_ziwei),
            "person_a_bazi_exists": bool(person_a_bazi),
            "person_b_ziwei_exists": bool(person_b_ziwei),
            "person_b_bazi_exists": bool(person_b_bazi),
            "formatted_a_ziwei_length": len(formatted_a_ziwei),
            "formatted_a_bazi_length": len(formatted_a_bazi),
            "prompt_length": len(prompt),
            "prompt_contains_missing_data": "数据缺失" in prompt
        }
        
        with open("compatibility_data_flow_debug.json", 'w', encoding='utf-8') as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 调试信息已保存: compatibility_data_flow_debug.json")
        
        # 9. 显示关键数据样本
        print(f"\n📄 关键数据样本:")
        
        if person_a_ziwei and "palaces" in person_a_ziwei:
            palaces = person_a_ziwei["palaces"]
            if "命宫" in palaces:
                mingong = palaces["命宫"]
                print(f"   A命宫: {mingong}")
        
        if person_a_bazi and "bazi_info" in person_a_bazi:
            bazi_info = person_a_bazi["bazi_info"]
            print(f"   A八字信息: {list(bazi_info.keys()) if bazi_info else 'None'}")
        
        print("🎉 合盘数据流调试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_compatibility_data_flow()
    if success:
        print("\n✅ 数据流调试完成！")
        print("📋 请检查生成的JSON文件了解详细信息")
    else:
        print("\n❌ 调试失败！请检查错误信息。")
