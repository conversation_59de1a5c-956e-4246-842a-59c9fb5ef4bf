#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话引擎 - 核心聊天处理逻辑，支持多轮对话和上下文记忆
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from .session_manager import SessionManager
from ..nlu.intent_recognizer import IntentRecognizer
from ..tools.tool_registry import ToolRegistry

logger = logging.getLogger(__name__)

class ConversationEngine:
    """对话引擎 - 处理完整的对话流程"""
    
    def __init__(self, session_manager: SessionManager, 
                 intent_recognizer: IntentRecognizer, 
                 tool_registry: ToolRegistry):
        """
        初始化对话引擎
        
        Args:
            session_manager: 会话管理器
            intent_recognizer: 意图识别器
            tool_registry: 工具注册中心
        """
        self.session_manager = session_manager
        self.intent_recognizer = intent_recognizer
        self.tool_registry = tool_registry
        
        # 对话统计
        self.stats = {
            "total_conversations": 0,
            "successful_conversations": 0,
            "failed_conversations": 0,
            "average_turns_per_conversation": 0.0
        }
        
        logger.info("对话引擎初始化完成")
    
    def process_message(self, session_id: str, message: str, 
                       user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        处理用户消息的完整流程
        
        Args:
            session_id: 会话ID
            message: 用户消息
            user_id: 用户ID
            
        Returns:
            处理结果
        """
        start_time = datetime.now()
        self.stats["total_conversations"] += 1
        
        try:
            logger.info(f"开始处理消息 - 会话: {session_id}, 消息: {message[:50]}...")
            
            # 1. 获取会话上下文
            session = self.session_manager.get_session(session_id)
            context = self.session_manager.get_conversation_context(session_id)
            
            # 2. 语义理解
            intent = self.intent_recognizer.recognize_intent(message, context)
            logger.debug(f"意图识别结果: {intent}")
            
            # 3. 处理特殊指令
            special_response = self._handle_special_commands(message, session_id, intent)
            if special_response:
                return special_response
            
            # 4. 选择并调用工具
            tool_result = self._execute_tool(intent, context, session_id)
            
            # 5. 生成最终响应
            final_response = self._generate_final_response(tool_result, intent, context)
            
            # 6. 更新会话状态
            self._update_session_state(session_id, message, intent, tool_result, final_response)
            
            # 7. 更新统计
            processing_time = (datetime.now() - start_time).total_seconds()
            self.stats["successful_conversations"] += 1
            
            logger.info(f"消息处理完成 - 耗时: {processing_time:.2f}秒")
            
            return {
                "success": True,
                "session_id": session_id,
                "message": final_response["content"],
                "intent": intent,
                "tool_result": tool_result,
                "processing_time": processing_time,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            self.stats["failed_conversations"] += 1
            logger.error(f"消息处理失败: {e}")
            
            return {
                "success": False,
                "session_id": session_id,
                "error": str(e),
                "message": "抱歉，处理您的消息时出现了问题，请稍后重试。",
                "timestamp": datetime.now()
            }
    
    def _handle_special_commands(self, message: str, session_id: str, 
                                intent: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理特殊指令
        
        Args:
            message: 用户消息
            session_id: 会话ID
            intent: 意图信息
            
        Returns:
            特殊指令响应或None
        """
        message_lower = message.lower().strip()
        
        # 清除会话
        if message_lower in ["清除", "重置", "清空", "clear", "reset"]:
            self.session_manager.clear_session(session_id)
            return {
                "success": True,
                "session_id": session_id,
                "message": "会话已清除，我们可以重新开始对话。",
                "command": "clear_session",
                "timestamp": datetime.now()
            }
        
        # 帮助信息
        if message_lower in ["帮助", "help", "?"]:
            help_text = self._generate_help_text()
            return {
                "success": True,
                "session_id": session_id,
                "message": help_text,
                "command": "help",
                "timestamp": datetime.now()
            }
        
        # 状态查询
        if message_lower in ["状态", "统计", "stats", "status"]:
            stats_text = self._generate_stats_text(session_id)
            return {
                "success": True,
                "session_id": session_id,
                "message": stats_text,
                "command": "stats",
                "timestamp": datetime.now()
            }
        
        return None
    
    def _execute_tool(self, intent: Dict[str, Any], context: Dict[str, Any], 
                     session_id: str) -> Dict[str, Any]:
        """
        执行工具
        
        Args:
            intent: 意图信息
            context: 上下文信息
            session_id: 会话ID
            
        Returns:
            工具执行结果
        """
        tool_name = intent.get("tool_name", "general")
        
        # 如果是一般对话，直接返回对话响应
        if tool_name == "general":
            return self._handle_general_conversation(intent, context)
        
        # 检查是否需要出生信息
        if tool_name in ["ziwei", "bazi", "comprehensive"]:
            birth_info = self._get_or_request_birth_info(intent, context, session_id)
            if not birth_info.get("complete"):
                return birth_info
        
        # 执行具体工具
        try:
            result = self.tool_registry.execute_tool(tool_name, intent, context, session_id)
            return result
        except Exception as e:
            logger.error(f"工具执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
    
    def _get_or_request_birth_info(self, intent: Dict[str, Any], 
                                  context: Dict[str, Any], session_id: str) -> Dict[str, Any]:
        """
        获取或请求出生信息
        
        Args:
            intent: 意图信息
            context: 上下文信息
            session_id: 会话ID
            
        Returns:
            出生信息或请求信息
        """
        # 检查意图中的出生信息
        birth_info = intent.get("entities", {}).get("birth_info")
        
        # 检查上下文中的出生信息
        if not birth_info:
            birth_info = context.get("birth_info")
        
        if birth_info:
            # 保存到会话中
            self.session_manager.set_user_birth_info(session_id, birth_info)
            return {"complete": True, "birth_info": birth_info}
        
        # 请求出生信息
        return {
            "complete": False,
            "success": True,
            "message": "请提供您的出生信息（年月日时和性别），例如：1988年6月1日午时男",
            "request_type": "birth_info",
            "tool_name": intent.get("tool_name")
        }
    
    def _handle_general_conversation(self, intent: Dict[str, Any], 
                                   context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理一般对话
        
        Args:
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            对话响应
        """
        message = intent.get("raw_response", "")
        
        # 简单的对话响应
        if any(greeting in message.lower() for greeting in ["你好", "hello", "hi"]):
            response = "您好！我是专业的算命AI助手，可以为您提供紫薇斗数、八字算命和六爻算卦服务。请告诉我您的需求。"
        elif any(thanks in message.lower() for thanks in ["谢谢", "thank"]):
            response = "不客气！如果您还有其他问题，随时可以问我。"
        else:
            response = "我是专业的算命AI助手，可以为您提供算命分析服务。请告诉我您想了解什么，或者提供您的出生信息进行详细分析。"
        
        return {
            "success": True,
            "tool_name": "general",
            "message": response,
            "type": "conversation"
        }
    
    def _generate_final_response(self, tool_result: Dict[str, Any], 
                               intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成最终响应
        
        Args:
            tool_result: 工具执行结果
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            最终响应
        """
        if not tool_result.get("success"):
            # 错误处理
            error_message = tool_result.get("message", tool_result.get("error", "处理失败"))
            return {
                "content": error_message,
                "type": "error",
                "tool_result": tool_result
            }
        
        # 成功响应
        if "message" in tool_result:
            content = tool_result["message"]
        elif "analysis" in tool_result:
            content = tool_result["analysis"]
        elif "data" in tool_result:
            content = str(tool_result["data"])
        else:
            content = "处理完成"
        
        return {
            "content": content,
            "type": "success",
            "tool_result": tool_result
        }
    
    def _update_session_state(self, session_id: str, user_message: str, 
                            intent: Dict[str, Any], tool_result: Dict[str, Any], 
                            final_response: Dict[str, Any]) -> None:
        """
        更新会话状态
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            intent: 意图信息
            tool_result: 工具结果
            final_response: 最终响应
        """
        # 更新上下文
        context_updates = {}
        
        # 更新当前话题
        if intent.get("tool_name") != "general":
            context_updates["current_topic"] = intent.get("tool_name")
            context_updates["last_analysis_type"] = intent.get("tool_name")
        
        # 更新出生信息
        birth_info = intent.get("entities", {}).get("birth_info")
        if birth_info:
            context_updates["birth_info"] = birth_info
        
        # 创建消息记录
        message_record = {
            "timestamp": datetime.now(),
            "user_message": user_message,
            "intent": intent,
            "tool_result": tool_result,
            "response": final_response["content"],
            "tool_used": tool_result.get("tool_name")
        }
        
        # 更新会话
        self.session_manager.update_session(session_id, context_updates, message_record)
    
    def _generate_help_text(self) -> str:
        """生成帮助文本"""
        return """🔮 算命AI助手使用指南

📋 **支持的功能:**
• 紫薇斗数 - 详细的命盘分析
• 八字算命 - 传统四柱分析  
• 六爻算卦 - 占卜预测
• 综合分析 - 多维度命理分析

💬 **使用方法:**
• 直接说出您的需求，如"我想看紫薇斗数"
• 提供出生信息进行分析，如"1988年6月1日午时男"
• 询问具体问题，如"今年运势如何"

🎯 **特殊指令:**
• "清除" - 清空当前会话
• "帮助" - 显示此帮助信息
• "状态" - 查看会话统计

有任何问题都可以直接问我！"""
    
    def _generate_stats_text(self, session_id: str) -> str:
        """生成统计文本"""
        session_stats = self.session_manager.get_session_stats()
        global_stats = self.tool_registry.get_global_stats()
        
        return f"""📊 **系统状态统计**

🗣️ **会话统计:**
• 总会话数: {session_stats['total_sessions']}
• 活跃会话: {session_stats['active_sessions']}
• 总消息数: {session_stats['total_messages']}

🛠️ **工具统计:**
• 可用工具: {global_stats['total_tools']}
• 执行成功率: {global_stats['success_rate']}
• 总执行次数: {global_stats['total_executions']}

💬 **对话引擎:**
• 处理成功率: {self.stats['successful_conversations']}/{self.stats['total_conversations']}

系统运行正常！"""
    
    def get_stats(self) -> Dict[str, Any]:
        """获取对话引擎统计信息"""
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            "total_conversations": 0,
            "successful_conversations": 0,
            "failed_conversations": 0,
            "average_turns_per_conversation": 0.0
        }
        logger.info("对话引擎统计信息已重置")
