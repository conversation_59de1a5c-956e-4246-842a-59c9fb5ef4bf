#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六爻服务 - 独立的六爻占卜功能
"""

import sys
import os
from typing import Optional
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.liuyao_result import LiuyaoResult
from utils.simple_logger import get_logger
from utils.cache_manager import get_cache

try:
    from utils.simple_llm_client import get_llm_client
except ImportError:
    get_llm_client = None

logger = get_logger()
cache = get_cache()

class LiuyaoService:
    """六爻服务"""
    
    def __init__(self):
        """初始化六爻服务"""
        try:
            if get_llm_client:
                self.llm_client = get_llm_client()
            else:
                self.llm_client = None
            logger.info("六爻服务初始化完成")
        except Exception as e:
            logger.error(f"六爻服务初始化失败: {e}")
            self.llm_client = None
    
    def divine_by_time(self, question: str) -> LiuyaoResult:
        """
        时间起卦
        
        Args:
            question: 占卜问题
            
        Returns:
            六爻结果
        """
        logger.info(f"🎲 时间起卦: {question}")
        
        try:
            # 基于当前时间生成卦象
            now = datetime.now()
            hexagram_data = self._generate_time_hexagram(now)
            
            # LLM解卦
            interpretation = self._interpret_hexagram(question, hexagram_data, "时间起卦")
            
            result = LiuyaoResult(
                question=question,
                method="时间起卦",
                hexagram_data=hexagram_data,
                interpretation=interpretation,
                success=True
            )
            
            logger.info("✅ 时间起卦完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ 时间起卦失败: {e}")
            return LiuyaoResult(
                question=question,
                method="时间起卦",
                success=False,
                error_message=str(e)
            )
    
    def divine_by_numbers(self, question: str, numbers: list) -> LiuyaoResult:
        """
        数字起卦
        
        Args:
            question: 占卜问题
            numbers: 数字列表
            
        Returns:
            六爻结果
        """
        logger.info(f"🎲 数字起卦: {question}, 数字: {numbers}")
        
        try:
            # 基于数字生成卦象
            hexagram_data = self._generate_number_hexagram(numbers)
            
            # LLM解卦
            interpretation = self._interpret_hexagram(question, hexagram_data, "数字起卦")
            
            result = LiuyaoResult(
                question=question,
                method="数字起卦",
                hexagram_data=hexagram_data,
                interpretation=interpretation,
                success=True
            )
            
            logger.info("✅ 数字起卦完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ 数字起卦失败: {e}")
            return LiuyaoResult(
                question=question,
                method="数字起卦",
                success=False,
                error_message=str(e)
            )
    
    def _generate_time_hexagram(self, time: datetime) -> dict:
        """基于时间生成卦象"""
        # 简化的时间起卦算法
        year = time.year % 12
        month = time.month
        day = time.day
        hour = time.hour
        
        # 计算上卦和下卦
        upper = (year + month + day) % 8
        lower = (year + month + day + hour) % 8
        
        # 计算动爻
        moving_line = (year + month + day + hour) % 6 + 1
        
        hexagram_data = {
            "卦名": f"第{upper * 8 + lower + 1}卦",
            "上卦": self._get_trigram_name(upper),
            "下卦": self._get_trigram_name(lower),
            "动爻": f"第{moving_line}爻",
            "起卦时间": time.strftime("%Y年%m月%d日%H时"),
            "起卦方式": "时间起卦"
        }
        
        return hexagram_data
    
    def _generate_number_hexagram(self, numbers: list) -> dict:
        """基于数字生成卦象"""
        if len(numbers) < 3:
            numbers = numbers + [1] * (3 - len(numbers))
        
        # 计算上卦和下卦
        upper = sum(numbers[:2]) % 8
        lower = sum(numbers[1:3]) % 8
        
        # 计算动爻
        moving_line = sum(numbers) % 6 + 1
        
        hexagram_data = {
            "卦名": f"第{upper * 8 + lower + 1}卦",
            "上卦": self._get_trigram_name(upper),
            "下卦": self._get_trigram_name(lower),
            "动爻": f"第{moving_line}爻",
            "起卦数字": numbers,
            "起卦方式": "数字起卦"
        }
        
        return hexagram_data
    
    def _get_trigram_name(self, index: int) -> str:
        """获取八卦名称"""
        trigrams = ["坤", "震", "坎", "兑", "艮", "离", "巽", "乾"]
        return trigrams[index % 8]
    
    def _interpret_hexagram(self, question: str, hexagram_data: dict, method: str) -> str:
        """LLM解卦"""
        if not self.llm_client:
            return "LLM客户端不可用，无法进行解卦分析"
        
        try:
            system_prompt = """你是一位精通六爻占卜的易学大师。请根据卦象信息和问题进行专业解读。

要求：
1. 基于传统六爻理论进行分析
2. 结合具体问题给出针对性建议
3. 分析长度控制在1000-2000字
4. 语言通俗易懂，避免过于晦涩
5. 既要分析卦象含义，也要给出实用建议"""
            
            user_prompt = f"""请解读以下六爻卦象：

【问题】: {question}
【起卦方式】: {method}
【卦象信息】:
- 卦名: {hexagram_data.get('卦名', '未知')}
- 上卦: {hexagram_data.get('上卦', '未知')}
- 下卦: {hexagram_data.get('下卦', '未知')}
- 动爻: {hexagram_data.get('动爻', '未知')}

请详细解读此卦象对问题的指导意义。"""
            
            interpretation = self.llm_client.chat(user_prompt, system_prompt)
            
            if interpretation:
                return interpretation
            else:
                return "解卦分析生成失败，请重试"
                
        except Exception as e:
            logger.error(f"LLM解卦失败: {e}")
            return f"解卦分析失败: {str(e)}"
