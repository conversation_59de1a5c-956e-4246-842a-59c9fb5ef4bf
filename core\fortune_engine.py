#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算命AI核心引擎
负责智能解析用户输入，调用真实算法，生成AI分析
"""

import re
import json
import requests
import logging
import os
from datetime import datetime
from typing import Dict, Optional, Tuple, List

# 图片生成相关导入
try:
    from PIL import Image, ImageDraw, ImageFont
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.font_manager import FontProperties
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ PIL/matplotlib未安装，将使用文本排盘图")

# 设置日志
logger = logging.getLogger(__name__)

class FortuneEngine:
    """算命AI核心引擎"""

    def __init__(self, ziwei_calc=None, bazi_calc=None, liuyao_calc=None, chat_api_func=None):
        # 直接使用算法实例，避免HTTP循环调用
        self.ziwei_calc = ziwei_calc
        self.bazi_calc = bazi_calc
        self.liuyao_calc = liuyao_calc
        # 聊天API函数，用于LLM解析
        self.chat_api_func = chat_api_func
        # 会话上下文存储
        self.session_context = {}

        # 如果没有传入算法实例，尝试自动初始化
        self._auto_init_algorithms()

    def _auto_init_algorithms(self):
        """自动初始化算法实例"""
        try:
            import sys
            import os

            # 添加算法路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            algorithms_dir = os.path.join(os.path.dirname(current_dir), 'algorithms')
            if algorithms_dir not in sys.path:
                sys.path.append(algorithms_dir)

            # 初始化紫薇斗数算法
            if not self.ziwei_calc:
                try:
                    from real_ziwei_calculator import RealZiweiCalculator
                    self.ziwei_calc = RealZiweiCalculator()
                    print("✅ DEBUG: 紫薇斗数算法自动初始化成功")
                except Exception as e:
                    print(f"⚠️ DEBUG: 紫薇斗数算法自动初始化失败: {e}")

            # 初始化增强版八字算法（废除旧的real_bazi_calculator）
            if not self.bazi_calc:
                try:
                    from enhanced_bazi_calculator import EnhancedBaziCalculator
                    self.bazi_calc = EnhancedBaziCalculator()
                    print("✅ DEBUG: 增强版八字算法自动初始化成功")
                except Exception as e:
                    print(f"⚠️ DEBUG: 增强版八字算法自动初始化失败: {e}")

            # 初始化六爻算法
            if not self.liuyao_calc:
                try:
                    from liuyao_calculator import LiuyaoCalculator
                    self.liuyao_calc = LiuyaoCalculator()
                    print("✅ DEBUG: 六爻算法自动初始化成功")
                except Exception as e:
                    print(f"⚠️ DEBUG: 六爻算法自动初始化失败: {e}")

        except Exception as e:
            print(f"❌ DEBUG: 算法自动初始化过程出错: {e}")

    def parse_user_input(self, user_input: str) -> Dict:
        """智能解析用户输入，提取关键信息"""
        print(f"🔍 DEBUG: 开始解析用户输入: {user_input}")
        logger.info(f"🔍 开始解析用户输入: {user_input}")

        result = {
            "birth_info": None,
            "fortune_type": None,
            "question_type": None,
            "extracted_data": {}
        }

        # 提取出生时间信息
        print("📅 DEBUG: 提取出生时间信息...")
        logger.info("📅 提取出生时间信息...")
        birth_info = self._extract_birth_info(user_input)
        if birth_info:
            result["birth_info"] = birth_info
            print(f"✅ DEBUG: 出生信息提取成功: {birth_info}")
            logger.info(f"✅ 出生信息提取成功: {birth_info}")
        else:
            print("⚠️ DEBUG: 未找到出生信息")
            logger.warning("⚠️ 未找到出生信息")

        # 判断算命类型
        print("🔮 DEBUG: 判断算命类型...")
        logger.info("🔮 判断算命类型...")
        fortune_type = self._detect_fortune_type(user_input)
        result["fortune_type"] = fortune_type
        print(f"✅ DEBUG: 算命类型: {fortune_type}")
        logger.info(f"✅ 算命类型: {fortune_type}")

        # 判断问题类型
        print("❓ DEBUG: 判断问题类型...")
        logger.info("❓ 判断问题类型...")
        question_type = self._detect_question_type(user_input)
        result["question_type"] = question_type
        print(f"✅ DEBUG: 问题类型: {question_type}")
        logger.info(f"✅ 问题类型: {question_type}")

        print(f"🎯 DEBUG: 解析完成: {result}")
        logger.info(f"🎯 解析完成: {result}")
        return result

    def _extract_birth_info(self, text: str) -> Optional[Dict]:
        """使用LLM智能提取出生信息"""
        print(f"🤖 DEBUG: 使用LLM解析出生信息: {text}")

        # 构建LLM提示词
        prompt = f"""
请从以下文本中提取出生信息，返回JSON格式：

文本：{text}

请提取以下信息：
1. 年份（4位数字）
2. 月份（1-12）
3. 日期（1-31）
4. 时辰（0-23小时，如果是传统时辰请转换）
5. 性别（男/女）

传统时辰对应表：
子时=0, 丑时=1, 寅时=3, 卯时=5, 辰时=7, 巳时=9, 午时=11, 未时=13, 申时=15, 酉时=17, 戌时=19, 亥时=21

只返回JSON格式，如果无法提取则返回null：
{{
  "year": 年份,
  "month": 月份,
  "day": 日期,
  "hour": 小时,
  "gender": "性别"
}}
"""

        try:
            # 调用本地聊天API进行解析
            response = self._call_local_chat_api(prompt)
            print(f"🤖 DEBUG: LLM解析响应: {response}")

            # 尝试解析JSON
            import json
            # 提取JSON部分
            if "{" in response and "}" in response:
                start = response.find("{")
                end = response.rfind("}") + 1
                json_str = response[start:end]

                result = json.loads(json_str)

                # 验证结果
                if (result and
                    isinstance(result.get("year"), int) and
                    isinstance(result.get("month"), int) and
                    isinstance(result.get("day"), int) and
                    isinstance(result.get("hour"), int) and
                    result.get("gender") in ["男", "女"]):

                    print(f"✅ DEBUG: LLM解析成功: {result}")
                    return result

            print("❌ DEBUG: LLM解析失败，返回格式不正确")
            return None

        except Exception as e:
            print(f"❌ DEBUG: LLM解析出错: {e}")
            return None

    def _call_local_chat_api(self, prompt: str) -> str:
        """调用本地聊天API进行解析"""
        if not self.chat_api_func:
            return "聊天API未配置"

        try:
            # 调用传入的聊天API函数
            response = self.chat_api_func(prompt)
            return response
        except Exception as e:
            print(f"❌ DEBUG: 调用聊天API失败: {e}")
            return f"API调用失败: {str(e)}"

    def _convert_traditional_hour(self, time_str: str) -> int:
        """传统时辰转现代小时"""
        time_mapping = {
            "子": 0, "丑": 1, "寅": 3, "卯": 5, "辰": 7, "巳": 9,
            "午": 11, "未": 13, "申": 15, "酉": 17, "戌": 19, "亥": 21,
            "子时": 0, "丑时": 1, "寅时": 3, "卯时": 5, "辰时": 7, "巳时": 9,
            "午时": 11, "未时": 13, "申时": 15, "酉时": 17, "戌时": 19, "亥时": 21
        }
        return time_mapping.get(time_str, 11)

    def _detect_fortune_type(self, text: str) -> str:
        """使用LLM智能检测算命类型"""
        print("🤖 DEBUG: 使用LLM智能检测算命类型...")

        try:
            # 构建意图识别提示词
            intent_prompt = f"""请分析用户问题并识别算命类型。

用户问题：{text}

算命类型说明：
- ziwei：用户明确提到"紫薇"、"斗数"、"命宫"、"星曜"
- bazi：用户明确提到"八字"、"四柱"、"天干"、"地支"、"合婚"
- liuyao：用户提到"算卦"、"占卜"、"起卦"、"算个卦"、"算一卦"、"问卦"、"卜卦"、"看卦"、"卦象"
- comprehensive：用户想看"运势"、"命运"、"算命"但没有指定具体方式

判断规则：
1. 优先识别明确的算命方式关键词
2. "算卦"类表达优先识别为liuyao
3. 只有通用词汇时选择comprehensive

请只返回：ziwei、bazi、liuyao、comprehensive"""

            # 调用LLM进行意图识别
            response = self._call_local_chat_api(intent_prompt)

            # 智能解析响应（支持JSON和纯文本）
            detected_type = self._parse_llm_response(response, ["ziwei", "bazi", "liuyao", "comprehensive"])

            if detected_type:
                print(f"✅ DEBUG: LLM识别算命类型: {detected_type}")
                return detected_type
            else:
                print(f"⚠️ DEBUG: LLM返回无效类型: {response}, 使用备用关键词检测")
                return self._fallback_keyword_detection(text)

        except Exception as e:
            print(f"❌ DEBUG: LLM意图识别失败: {e}, 使用备用关键词检测")
            return self._fallback_keyword_detection(text)

    def _fallback_keyword_detection(self, text: str) -> str:
        """备用关键词检测方法"""
        print("🔄 DEBUG: 使用备用关键词检测...")

        # 优先检测具体的算命类型
        if any(keyword in text for keyword in ["紫薇", "斗数", "命宫", "星曜"]):
            return "ziwei"
        elif any(keyword in text for keyword in ["八字", "四柱", "天干", "地支"]):
            return "bazi"
        # 六爻关键词优先级最高，包含更多表达方式
        elif any(keyword in text for keyword in ["六爻", "算卦", "占卜", "起卦", "算个卦", "看卦", "卦象", "起个卦", "问卦", "算一卦", "卜卦"]):
            return "liuyao"
        # 通用关键词优先级较低
        elif any(keyword in text for keyword in ["运势", "命运", "算命"]):
            return "comprehensive"  # 综合分析
        else:
            # 默认使用综合分析，避免报错
            print("⚠️ DEBUG: 未识别具体算命类型，使用默认综合分析")
            return "comprehensive"

    def _detect_question_type(self, text: str) -> str:
        """使用LLM智能检测问题类型"""
        print("🤖 DEBUG: 使用LLM智能检测问题类型...")

        try:
            # 构建问题类型识别提示词
            question_prompt = f"""请分析用户问题并识别问题类型。

用户问题：{text}

问题类型说明：
- career：询问"事业"、"工作"、"职业"、"升职"
- love：询问"感情"、"婚姻"、"恋爱"、"配偶"、"合婚"
- wealth：询问"财运"、"财富"、"投资"、"赚钱"
- health：询问"健康"、"身体"、"疾病"
- fortune：询问"运势"、"今年"、"明年"、"流年"
- general：综合命理分析或问题不明确

请只返回：career、love、wealth、health、fortune、general"""

            # 调用LLM进行问题类型识别
            response = self._call_local_chat_api(question_prompt)

            # 智能解析响应（支持JSON和纯文本）
            detected_type = self._parse_llm_response(response, ["career", "love", "wealth", "health", "fortune", "general"])

            if detected_type:
                print(f"✅ DEBUG: LLM识别问题类型: {detected_type}")
                return detected_type
            else:
                print(f"⚠️ DEBUG: LLM返回无效问题类型: {response}, 使用备用关键词检测")
                return self._fallback_question_detection(text)

        except Exception as e:
            print(f"❌ DEBUG: LLM问题类型识别失败: {e}, 使用备用关键词检测")
            return self._fallback_question_detection(text)

    def _fallback_question_detection(self, text: str) -> str:
        """备用问题类型关键词检测"""
        print("🔄 DEBUG: 使用备用问题类型关键词检测...")

        if any(keyword in text for keyword in ["事业", "工作", "职业"]):
            return "career"
        elif any(keyword in text for keyword in ["感情", "婚姻", "爱情"]):
            return "love"
        elif any(keyword in text for keyword in ["财运", "财富", "金钱"]):
            return "wealth"
        elif any(keyword in text for keyword in ["健康", "身体"]):
            return "health"
        elif any(keyword in text for keyword in ["运势", "今年", "明年"]):
            return "fortune"
        else:
            return "general"

    def _parse_llm_response(self, response: str, valid_options: list) -> Optional[str]:
        """智能解析LLM响应，支持JSON和纯文本格式"""
        if not response or not response.strip():
            return None

        # 清理响应
        cleaned_response = response.strip()

        # 尝试直接匹配（纯文本格式）
        for option in valid_options:
            if cleaned_response.lower() == option.lower():
                return option

        # 尝试解析JSON格式
        try:
            # 移除可能的markdown代码块标记
            if "```json" in cleaned_response:
                cleaned_response = cleaned_response.split("```json")[1].split("```")[0].strip()
            elif "```" in cleaned_response:
                cleaned_response = cleaned_response.split("```")[1].strip()

            # 解析JSON
            json_data = json.loads(cleaned_response)

            # 查找可能的键值
            possible_keys = [
                "算命类型", "fortune_type", "type", "算命",
                "问题类型", "question_type", "problem_type", "问题",
                "类型", "category", "result"
            ]

            for key in possible_keys:
                if key in json_data:
                    value = str(json_data[key]).lower()
                    for option in valid_options:
                        if value == option.lower():
                            return option

            # 如果JSON中有直接的值
            if isinstance(json_data, str):
                for option in valid_options:
                    if json_data.lower() == option.lower():
                        return option

        except (json.JSONDecodeError, KeyError, TypeError):
            pass

        # 尝试在响应中查找关键词
        response_lower = cleaned_response.lower()
        for option in valid_options:
            if option.lower() in response_lower:
                return option

        return None

    def call_real_algorithm(self, fortune_type: str, birth_info: Dict, question_data: Dict = None) -> Dict:
        """调用真实算法进行排盘"""
        logger.info(f"🧮 开始调用真实算法: {fortune_type}")
        logger.info(f"📊 出生信息: {birth_info}")

        try:
            if fortune_type == "ziwei":
                logger.info("🔮 调用紫薇斗数算法...")
                return self._call_ziwei_api(birth_info)
            elif fortune_type == "bazi":
                logger.info("📜 调用八字算命算法...")
                return self._call_bazi_api(birth_info)
            elif fortune_type == "liuyao":
                logger.info("🎯 调用六爻算卦算法...")
                return self._call_liuyao_api(birth_info, question_data)
            elif fortune_type == "comprehensive":
                logger.info("🌟 调用综合分析算法...")
                # 综合分析：调用多个算法
                return self._call_comprehensive_api(birth_info)
            else:
                logger.error(f"❌ 未知的算命类型: {fortune_type}")
                return {"success": False, "error": "未知的算命类型"}
        except Exception as e:
            logger.error(f"❌ 算法调用失败: {str(e)}")
            return {"success": False, "error": f"算法调用失败: {str(e)}"}

    def _call_ziwei_api(self, birth_info: Dict) -> Dict:
        """直接调用紫薇斗数算法"""
        logger.info(f"🔮 直接调用紫薇算法")
        logger.info(f"📤 请求数据: {birth_info}")

        if not self.ziwei_calc:
            return {"success": False, "error": "紫薇算法未初始化"}

        try:
            result = self.ziwei_calc.calculate_chart(
                birth_info["year"],
                birth_info["month"],
                birth_info["day"],
                birth_info["hour"],
                birth_info.get("gender", "男")
            )

            if "error" in result:
                return {"success": False, "error": result["error"]}
            else:
                return {"success": True, "data": result}

        except Exception as e:
            logger.error(f"❌ 紫薇算法调用失败: {e}")
            return {"success": False, "error": str(e)}

    def _call_bazi_api(self, birth_info: Dict) -> Dict:
        """直接调用增强版八字算命算法"""
        logger.info(f"📜 直接调用增强版八字算法")
        logger.info(f"📤 请求数据: {birth_info}")

        if not self.bazi_calc:
            return {"success": False, "error": "增强版八字算法未初始化"}

        try:
            result = self.bazi_calc.calculate_enhanced_bazi(
                birth_info["year"],
                birth_info["month"],
                birth_info["day"],
                birth_info["hour"],
                birth_info.get("gender", "男")
            )

            if not result.get("success"):
                return {"success": False, "error": result.get("error", "未知错误")}
            else:
                return {"success": True, "data": result}

        except Exception as e:
            logger.error(f"❌ 增强版八字算法调用失败: {e}")
            return {"success": False, "error": str(e)}

    def _call_liuyao_api(self, birth_info: Dict, question_data: Dict = None) -> Dict:
        """直接调用六爻算卦算法"""
        logger.info(f"🎯 直接调用六爻算法")
        logger.info(f"📤 请求数据: {birth_info}")

        # 如果六爻算法未初始化，尝试动态初始化
        if not self.liuyao_calc:
            print("⚠️ DEBUG: 六爻算法未初始化，尝试动态初始化...")
            try:
                import sys
                import os

                # 添加算法路径
                current_dir = os.path.dirname(os.path.abspath(__file__))
                algorithms_dir = os.path.join(os.path.dirname(current_dir), 'algorithms')
                if algorithms_dir not in sys.path:
                    sys.path.append(algorithms_dir)

                from liuyao_calculator import LiuyaoCalculator
                self.liuyao_calc = LiuyaoCalculator()
                print("✅ DEBUG: 六爻算法动态初始化成功")

                # 测试算法功能
                test_result = self.liuyao_calc.divine_by_time(2025, 6, 19, 9, 0)
                if test_result.get("success"):
                    print("✅ DEBUG: 六爻算法功能测试成功")
                else:
                    print(f"⚠️ DEBUG: 六爻算法功能测试失败: {test_result.get('error')}")

            except Exception as e:
                print(f"❌ DEBUG: 六爻算法动态初始化失败: {e}")
                return {"success": False, "error": f"六爻算法初始化失败: {str(e)}"}

        if not self.liuyao_calc:
            return {"success": False, "error": "六爻算法未初始化"}

        try:
            result = self.liuyao_calc.divine_with_analysis(
                birth_info["year"],
                birth_info["month"],
                birth_info["day"],
                birth_info["hour"],
                "time"  # 使用时间起卦
            )

            if "error" in result:
                return {"success": False, "error": result["error"]}
            else:
                return {"success": True, "data": result}

        except Exception as e:
            logger.error(f"❌ 六爻算法调用失败: {e}")
            return {"success": False, "error": str(e)}

    def _call_comprehensive_api(self, birth_info: Dict) -> Dict:
        """综合分析：统一使用py-iztro算法确保八字准确性"""
        results = {}
        errors = []

        # 调用紫薇斗数（同时获取正确的八字）
        print("🔮 DEBUG: 开始调用紫薇斗数算法...")
        ziwei_result = self._call_ziwei_api(birth_info)
        print(f"🔮 DEBUG: 紫薇斗数调用结果: {ziwei_result}")

        if ziwei_result.get("success"):
            results["ziwei"] = ziwei_result
            print("✅ DEBUG: 紫薇斗数调用成功")

            # 从紫薇斗数结果中提取正确的八字信息
            ziwei_data = ziwei_result.get("data", {})
            birth_info_data = ziwei_data.get("birth_info", {})
            chinese_date = birth_info_data.get("chinese_date", "")

            print(f"📜 DEBUG: 从紫薇斗数提取八字: {chinese_date}")

            # 构造正确的八字结果（基于py-iztro）
            bazi_data = {
                "success": True,
                "birth_info": {
                    "datetime": f"{birth_info['year']}年{birth_info['month']}月{birth_info['day']}日{birth_info['hour']}时",
                    "gender": birth_info.get("gender", "男"),
                    "solar": birth_info_data.get("solar", ""),
                    "lunar": birth_info_data.get("lunar", "")
                },
                "chinese_date": chinese_date,
                "calculation_type": "基于py-iztro的正确八字",
                "note": "使用py-iztro算法确保八字准确性，避免yxf_yixue_py的计算错误",
                "zodiac": ziwei_data.get("zodiac", ""),
                "sign": ziwei_data.get("sign", "")
            }

            results["bazi"] = {"success": True, "data": bazi_data}
            print("✅ DEBUG: 基于紫薇斗数构造正确八字成功")

        else:
            error_msg = f"紫薇斗数算法失败: {ziwei_result.get('error', '未知错误')}"
            errors.append(error_msg)
            print(f"❌ DEBUG: {error_msg}")

            # 紫薇斗数失败，无法获取正确八字
            error_msg2 = "无法获取正确八字（依赖紫薇斗数算法）"
            errors.append(error_msg2)
            print(f"❌ DEBUG: {error_msg2}")

        # 检查是否两个算法都成功
        if len(results) != 2:
            print(f"❌ DEBUG: 综合分析失败 - 需要紫薇斗数成功以获取正确八字，当前只有: {list(results.keys())}")
            return {
                "success": False,
                "error": f"综合分析需要紫薇斗数算法成功以确保八字准确性。失败原因: {'; '.join(errors)}",
                "partial_results": results
            }

        print(f"🌟 DEBUG: 综合分析成功 - 包含正确的紫薇斗数+八字数据: {list(results.keys())}")
        return {
            "success": True,
            "type": "comprehensive",
            "results": results
        }

    def generate_ai_analysis(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """基于真实排盘结果生成专业分析 - 根据算命类型选择不同的分析流程"""

        # 严格检查算法结果
        if not algorithm_result.get("success"):
            error_msg = algorithm_result.get('error', '未知错误')
            print(f"❌ DEBUG: 算法失败，停止分析: {error_msg}")
            return f"""
# ❌ 算命分析失败

## 错误信息
{error_msg}

## 说明
算法计算失败，无法进行准确的分析。

请检查：
1. 出生信息是否完整准确
2. 时间格式是否正确
3. 稍后重试

如果问题持续存在，请联系技术支持。
"""

        # 根据算命类型选择不同的分析流程
        algorithm_data = algorithm_result.get("data", {})
        divination_type = algorithm_data.get("divination_type", "")

        print(f"🔍 DEBUG: 算法结果类型检查:")
        print(f"  algorithm_result.type: {algorithm_result.get('type', 'None')}")
        print(f"  algorithm_data.divination_type: {divination_type}")
        print(f"  六爻检查: {'六爻' in divination_type or '算卦' in divination_type}")

        if "六爻" in divination_type or "算卦" in divination_type:
            # 六爻算卦分析流程
            print(f"🤖 DEBUG: 开始生成六爻算卦专业分析")
            return self._generate_liuyao_analysis(algorithm_result, user_question, question_type)

        elif algorithm_result.get("type") == "comprehensive":
            # 紫薇斗数+八字综合分析流程
            print(f"🤖 DEBUG: 开始生成专业分析 (紫薇斗数+八字综合分析)")

            results = algorithm_result.get("results", {})
            if "ziwei" not in results or "bazi" not in results:
                missing = []
                if "ziwei" not in results:
                    missing.append("紫薇斗数")
                if "bazi" not in results:
                    missing.append("八字命理")

                error_msg = f"综合分析数据不完整，缺少: {', '.join(missing)}"
                print(f"❌ DEBUG: {error_msg}")
                return f"""
# ❌ 算命分析数据不完整

## 错误信息
{error_msg}

## 说明
本系统需要紫薇斗数和八字命理两套完整数据才能进行准确分析。

当前状态：
- 紫薇斗数: {'✅ 成功' if 'ziwei' in results else '❌ 失败'}
- 八字命理: {'✅ 成功' if 'bazi' in results else '❌ 失败'}

请重新提交完整的出生信息进行分析。
"""

            print("✅ DEBUG: 算法数据完整，开始生成分析...")
            # 开始4角度分析流程（带重试机制）
            return self._generate_four_angle_analysis_with_retry(algorithm_result, user_question, question_type)

        else:
            # 单一算法分析流程（紫薇斗数或八字）
            print(f"🤖 DEBUG: 开始生成单一算法专业分析")
            return self._generate_single_algorithm_analysis(algorithm_result, user_question, question_type)

    def _generate_liuyao_analysis(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """生成六爻算卦专业分析"""
        print("🔮 DEBUG: 生成六爻算卦专业分析...")

        try:
            # 提取六爻算卦数据
            liuyao_data = algorithm_result.get("data", {})

            # 第0步：生成卦象图
            print("📊 DEBUG: 第0步 - 生成六爻卦象图...")
            chart_display = self._generate_liuyao_chart_display(liuyao_data)

            # 第1步：生成详细分析
            print("🔮 DEBUG: 第1步 - 生成六爻详细分析...")
            detailed_analysis = self._generate_liuyao_detailed_analysis(liuyao_data, user_question, question_type)

            # 第2步：生成简洁版分析
            print("📝 DEBUG: 第2步 - 生成六爻简洁版分析...")
            compact_analysis = self._generate_liuyao_compact_analysis(liuyao_data, user_question, question_type)

            # 合并完整分析
            final_result = f"""
# 🔮 六爻算卦分析报告

## 📊 【卦象排盘图】

{chart_display}

## 📋 【核心要点 - 紧凑版】

{compact_analysis}

---

## 📚 【深度解读 - 详细版】

{detailed_analysis}

---
*本分析基于真实的六爻算卦算法计算结果，卦象图可作为分析参照*
"""

            print(f"🎉 DEBUG: 六爻分析完成，总长度: {len(final_result)}")
            return final_result

        except Exception as e:
            print(f"❌ DEBUG: 六爻分析失败: {e}")
            return f"六爻分析生成失败: {str(e)}"

    def _generate_liuyao_chart_display(self, liuyao_data: Dict) -> str:
        """生成六爻卦象图显示 - 支持图片生成"""
        try:
            # 生成六爻卦象图片
            image_path = self._generate_liuyao_chart_image(liuyao_data)

            if image_path:
                return f"""📊 **六爻卦象排盘图**
图片已生成: {image_path}

**起卦信息:**
- 起卦方法: {liuyao_data.get('method', '时间起卦')}
- 起卦时间: {liuyao_data.get('datetime', '')}
- 动爻: {liuyao_data.get('analysis_result', {}).get('动爻', [])}
"""
            else:
                # 备用：文字版卦象
                formatted_output = liuyao_data.get("formatted_output", "")
                return f"""📊 **六爻卦象排盘**

```
{formatted_output}
```

**起卦信息:**
- 起卦方法: {liuyao_data.get('method', '时间起卦')}
- 起卦时间: {liuyao_data.get('datetime', '')}
- 动爻: {liuyao_data.get('analysis_result', {}).get('动爻', [])}
"""

        except Exception as e:
            print(f"❌ DEBUG: 六爻卦象图生成失败: {e}")
            return "📊 **六爻卦象排盘**\n卦象图生成失败，请稍后重试"

    def _generate_liuyao_chart_image(self, liuyao_data: Dict) -> str:
        """生成六爻卦象图片"""
        if not PIL_AVAILABLE:
            print("⚠️ 图片库未安装，跳过六爻卦象图片生成")
            return None

        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            from matplotlib.font_manager import FontProperties
            import random

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建图形
            fig, ax = plt.subplots(1, 1, figsize=(12, 8))
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 10)
            ax.axis('off')

            # 获取卦象信息
            analysis_result = liuyao_data.get("analysis_result", {})
            pan_info = analysis_result.get("盘", {})
            dongyao = analysis_result.get("动爻", [])

            # 标题
            bengua_info = pan_info.get("10", {})
            biangua_info = pan_info.get("20", {})
            title = f"{bengua_info.get('六十四卦', '本卦')} 之 {biangua_info.get('六十四卦', '变卦')}"
            ax.text(5, 9.5, title, fontsize=16, ha='center', weight='bold')

            # 绘制六爻（从上到下：6爻到1爻）
            yao_positions = [8, 7.2, 6.4, 5.6, 4.8, 4]
            yao_names = ["六爻", "五爻", "四爻", "三爻", "二爻", "初爻"]

            for i, yao_num in enumerate([16, 15, 14, 13, 12, 11]):
                yao_info = pan_info.get(str(yao_num), {})
                if not yao_info:
                    continue

                y_pos = yao_positions[i]

                # 六神
                liushen = yao_info.get("六神", "")
                ax.text(0.5, y_pos, liushen, fontsize=10, ha='center', va='center')

                # 卦爻（阳爻或阴爻）
                guayao = yao_info.get("卦爻", "")
                if "▅▅▅▅▅" in guayao:  # 阳爻
                    ax.add_patch(patches.Rectangle((1.5, y_pos-0.1), 1.5, 0.2,
                                                 facecolor='black', edgecolor='black'))
                elif "▅▅  ▅▅" in guayao:  # 阴爻
                    ax.add_patch(patches.Rectangle((1.5, y_pos-0.1), 0.6, 0.2,
                                                 facecolor='black', edgecolor='black'))
                    ax.add_patch(patches.Rectangle((2.4, y_pos-0.1), 0.6, 0.2,
                                                 facecolor='black', edgecolor='black'))

                # 动爻标记
                dongyao_mark = yao_info.get("动爻", "")
                if dongyao_mark:
                    color = 'red' if dongyao_mark == 'X' else 'blue'
                    ax.text(3.2, y_pos, dongyao_mark, fontsize=12, ha='center', va='center',
                           color=color, weight='bold')

                # 六亲
                liuqin = yao_info.get("六亲", "")
                ax.text(4, y_pos, liuqin, fontsize=10, ha='center', va='center')

                # 纳甲
                nagan = yao_info.get("纳干", "")
                nazhi = yao_info.get("纳支", "")
                ax.text(5.5, y_pos, f"{nagan}{nazhi}", fontsize=10, ha='center', va='center')

                # 五行
                wuxing = yao_info.get("五行", "")
                ax.text(6.5, y_pos, wuxing, fontsize=10, ha='center', va='center')

                # 世应
                shiying = yao_info.get("世应", "")
                if shiying:
                    ax.text(7.5, y_pos, shiying, fontsize=10, ha='center', va='center',
                           color='red', weight='bold')

                # 爻位名称
                ax.text(8.5, y_pos, yao_names[i], fontsize=9, ha='center', va='center')

            # 添加列标题
            headers = ["六神", "卦爻", "动", "六亲", "纳甲", "五行", "世应", "爻位"]
            header_positions = [0.5, 2.25, 3.2, 4, 5.5, 6.5, 7.5, 8.5]

            for header, x_pos in zip(headers, header_positions):
                ax.text(x_pos, 8.8, header, fontsize=10, ha='center', va='center',
                       weight='bold', color='blue')

            # 起卦信息
            method = liuyao_data.get('method', '时间起卦')
            datetime_str = liuyao_data.get('datetime', '')
            ax.text(5, 3.2, f"起卦方法: {method}", fontsize=10, ha='center')
            ax.text(5, 2.8, f"起卦时间: {datetime_str}", fontsize=10, ha='center')
            ax.text(5, 2.4, f"动爻: {dongyao}", fontsize=10, ha='center')

            # 保存图片
            charts_dir = "charts"
            import os
            if not os.path.exists(charts_dir):
                os.makedirs(charts_dir)

            image_filename = f"liuyao_chart_{random.randint(10000, 99999)}.png"
            image_path = os.path.join(charts_dir, image_filename)

            plt.tight_layout()
            plt.savefig(image_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            print(f"✅ 六爻卦象图片已生成: {image_path}")
            return image_path

        except Exception as e:
            print(f"❌ DEBUG: 六爻卦象图片生成失败: {e}")
            return None

    def _generate_liuyao_detailed_analysis(self, liuyao_data: Dict, user_question: str, question_type: str) -> str:
        """生成六爻详细分析"""

        # 构建六爻专用提示词
        liuyao_prompt = self._build_liuyao_analysis_prompt(liuyao_data, user_question, question_type, detailed=True)

        try:
            analysis = self._call_local_chat_api(liuyao_prompt)
            print(f"✅ DEBUG: 六爻详细分析完成，长度: {len(analysis)}")
            return analysis
        except Exception as e:
            print(f"❌ DEBUG: 六爻详细分析失败: {e}")
            return "六爻详细分析生成失败，请稍后重试。"

    def _generate_liuyao_compact_analysis(self, liuyao_data: Dict, user_question: str, question_type: str) -> str:
        """生成六爻简洁分析"""

        # 构建六爻专用提示词
        liuyao_prompt = self._build_liuyao_analysis_prompt(liuyao_data, user_question, question_type, detailed=False)

        try:
            analysis = self._call_local_chat_api(liuyao_prompt)
            print(f"✅ DEBUG: 六爻简洁分析完成，长度: {len(analysis)}")
            return analysis
        except Exception as e:
            print(f"❌ DEBUG: 六爻简洁分析失败: {e}")
            return "六爻简洁分析生成失败，请稍后重试。"

    def _build_liuyao_analysis_prompt(self, liuyao_data: Dict, user_question: str, question_type: str, detailed: bool = True) -> str:
        """构建六爻算卦专用分析提示词"""

        # 提取卦象信息
        analysis_result = liuyao_data.get("analysis_result", {})
        formatted_output = liuyao_data.get("formatted_output", "")
        method = liuyao_data.get("method", "时间起卦")
        datetime_str = liuyao_data.get("datetime", "")

        # 提取卦象详细信息
        pan_info = analysis_result.get("盘", {})
        dongyao = analysis_result.get("动爻", [])

        # 构建卦象数据文本
        gua_data = f"""
【六爻卦象信息】
起卦方法: {method}
起卦时间: {datetime_str}
动爻位置: {dongyao}

【卦象排盘】
{formatted_output}

【卦象详细信息】
"""

        # 添加六爻详细信息
        if pan_info:
            # 本卦信息
            bengua_info = pan_info.get("10", {})
            if bengua_info:
                gua_data += f"本卦: {bengua_info.get('六十四卦', '')} ({bengua_info.get('卦宫', '')}宫)\n"

            # 变卦信息
            biangua_info = pan_info.get("20", {})
            if biangua_info:
                gua_data += f"变卦: {biangua_info.get('六十四卦', '')} ({biangua_info.get('卦宫', '')}宫)\n"

            gua_data += "\n【六爻详细配置】\n"

            # 六爻信息（从上到下：6爻到1爻）
            for yao_num in [16, 15, 14, 13, 12, 11]:
                yao_info = pan_info.get(str(yao_num), {})
                if yao_info:
                    liushen = yao_info.get("六神", "")
                    guayao = yao_info.get("卦爻", "")
                    dongyao_mark = yao_info.get("动爻", "")
                    liuqin = yao_info.get("六亲", "")
                    nagan = yao_info.get("纳干", "")
                    nazhi = yao_info.get("纳支", "")
                    wuxing = yao_info.get("五行", "")
                    shiying = yao_info.get("世应", "")

                    yao_line = f"{liushen} {guayao} {dongyao_mark} {liuqin} {nagan}{nazhi} {wuxing} {shiying}".strip()
                    gua_data += f"第{7-int(str(yao_num)[-1])}爻: {yao_line}\n"

        if detailed:
            # 详细版提示词
            prompt = f"""你是一位精通六爻算卦的顶级易学大师，请基于真实的六爻卦象进行专业深度分析。

【用户问题】{user_question}
【问题类型】{question_type}

{gua_data}

【六爻分析要求】
1. **卦象解读**：详细分析本卦和变卦的含义，卦象所代表的事物和趋势
2. **爻位分析**：逐一分析各爻的含义，特别是动爻的作用和影响
3. **六神作用**：分析六神（青龙、朱雀、勾陈、腾蛇、白虎、玄武）的影响
4. **六亲关系**：分析六亲（父母、兄弟、子孙、妻财、官鬼）的生克关系
5. **世应关系**：分析世爻和应爻的关系，判断吉凶
6. **动静变化**：重点分析动爻的变化对整个卦象的影响

【分析深度要求】
1. **理论依据**：每个结论都要有六爻理论依据，说明"为什么"
2. **客观平衡**：既要指出有利因素，也要明确指出不利因素和需要注意的问题
3. **具体指导**：给出明确的行动建议和时间节点
4. **逻辑连贯**：从卦象→爻位→六神六亲→世应→动变→结论建议，层层递进

【内容要求】
1. **卦象基础**：详细解读本卦和变卦的基本含义（约20%）
2. **有利因素**：分析卦象中的吉利配置和有利条件（约30%）
3. **不利警示**：重点分析卦象中的不利因素和需要注意的问题（约30%）
4. **具体建议**：针对问题给出明确的应对策略和时间建议（约20%）

【输出要求 - 极其重要】
1. **绝对禁止JSON格式**：直接输出流畅的中文文章，不要任何JSON、XML或代码块格式
2. **文章形式**：像写文章一样，使用自然的中文段落和句子
3. **理论依据**：每个观点都要有六爻理论依据和详细推理过程
4. **平衡分析**：必须包含具体的不利因素分析（30%内容）
5. **具体建议**：给出明确可执行的建议，包括时间节点
6. **字数要求**：至少1500-2500字的深度分析
7. **专业性**：分析要深入具体，体现六爻算卦的专业性

【格式示例】
直接这样开始：
根据您提供的卦象，得到火天大有卦，这是一个...
不要写成：
分析: 火天大有卦...

【重要提醒】
- 不要只说好话，要客观指出问题和挑战
- 每个结论都要有六爻理论支撑
- 建议要具体可行，包含时间因素
- 绝对不要使用任何格式化标记

请直接开始六爻卦象的深度分析（用自然的中文文章形式）：
"""
        else:
            # 简洁版提示词
            prompt = f"""你是一位专业的六爻算卦师，请基于真实卦象提供简洁明了的分析。

【用户问题】{user_question}

{gua_data}

【输出要求 - 极其重要】
1. **绝对禁止JSON格式**：直接输出流畅的中文文章，不要任何JSON、XML或代码块格式
2. **文章形式**：像写文章一样，使用自然的中文段落
3. **内容简洁**：控制在300-500字
4. **重点突出**：核心信息和关键建议
5. **通俗易懂**：使用通俗易懂的语言
6. **时间建议**：必须包含具体的时间建议

【格式示例】
直接这样开始：
根据卦象显示，您目前的运势...
不要写成：
分析: 运势...

请提供简洁而专业的六爻分析（用自然的中文文章形式）：
"""

        return prompt

    def _generate_single_algorithm_analysis(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """生成单一算法分析（紫薇斗数或八字）"""
        print("🔮 DEBUG: 生成单一算法分析...")

        try:
            # 简化的单一算法分析
            data = algorithm_result.get("data", {})

            # 构建简单的提示词
            prompt = f"""你是一位专业的命理师，请基于提供的算法数据进行分析。

【用户问题】{user_question}

【算法数据】
{json.dumps(data, ensure_ascii=False, indent=2)}

【输出要求】
1. 基于提供的真实数据进行分析
2. 内容控制在800-1200字
3. 使用通俗易懂的语言
4. 不要使用JSON格式，使用流畅的中文
5. 给出具体的建议

请提供专业的分析：
"""

            analysis = self._call_local_chat_api(prompt)
            print(f"✅ DEBUG: 单一算法分析完成，长度: {len(analysis)}")

            return f"""
# 🔮 命理分析报告

## 📋 【专业分析】

{analysis}

---
*本分析基于真实的算法计算结果*
"""

        except Exception as e:
            print(f"❌ DEBUG: 单一算法分析失败: {e}")
            return f"单一算法分析生成失败: {str(e)}"

    def _generate_four_angle_analysis_with_retry(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """4角度分析流程 - 带重试机制"""
        print("🔮 DEBUG: 开始4角度分析流程（带重试机制）")

        try:
            # 第0步：先快速生成并返回排盘图
            print("📊 DEBUG: 第0步 - 快速生成排盘图...")
            chart_display = self._generate_chart_display(algorithm_result)

            # 第1步：4角度深度分析（带重试）
            print("🔮 DEBUG: 第1步 - 4角度深度分析...")
            detailed_analysis = self._generate_detailed_analysis_with_retry(algorithm_result, user_question, question_type)

            # 第2步：基于4角度分析生成简洁版
            print("📝 DEBUG: 第2步 - 基于4角度分析生成简洁版...")
            compact_analysis = self._generate_compact_from_detailed(detailed_analysis, user_question, question_type)

            # 合并完整分析
            final_result = f"""
# 🔮 紫薇斗数命理分析报告

## 📊 【命盘排盘图】

{chart_display}

## 📋 【核心要点 - 紧凑版】

{compact_analysis}

---

## 📚 【深度解读 - 详细版】

{detailed_analysis}

---
*本分析基于真实的紫薇斗数和八字算法计算结果，排盘图可作为分析参照*
"""

            print(f"🎉 DEBUG: 完整分析完成，总长度: {len(final_result)}")
            return final_result

        except KeyboardInterrupt:
            print("⚠️ DEBUG: 用户中断分析")
            return "分析已被用户中断。"
        except Exception as e:
            print(f"❌ DEBUG: 分析过程出错: {e}")
            return f"分析生成失败: {str(e)}"

    def _generate_detailed_analysis_with_retry(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """生成详细版4角度分析 - 带重试机制"""
        print("🔮 DEBUG: 生成详细版4角度分析...")

        # 4个角度的分析
        angle_analyses = []

        for angle_num in range(1, 5):
            print(f"🎯 DEBUG: 生成第{angle_num}角度分析...")

            # 每个角度最多重试3次
            analysis = self._generate_single_angle_with_retry(
                angle_num, algorithm_result, user_question, question_type, max_retries=3
            )

            if analysis is None:
                print(f"❌ DEBUG: 第{angle_num}角度分析3次重试后仍然失败，停止整个分析流程")
                return f"""
# ❌ 分析生成失败

第{angle_num}角度分析在3次重试后仍然失败，为确保分析质量，已停止整个分析流程。

## 可能原因
1. 网络连接问题
2. API服务异常
3. 数据处理错误

## 建议
1. 检查网络连接
2. 稍后重试
3. 如问题持续，请联系技术支持

**注意：本系统要求所有角度分析都成功才能提供完整的专业分析，确保结果的准确性和完整性。**
"""

            angle_analyses.append(analysis)
            print(f"✅ DEBUG: 第{angle_num}角度分析完成，长度: {len(analysis)}")

        # 所有角度分析成功，进行合并
        print("🧠 DEBUG: 第3步 - 智能合并整合...")
        merged_analysis = self._merge_multi_analysis(angle_analyses, user_question, question_type)

        print(f"🎉 DEBUG: 详细版分析完成，最终长度: {len(merged_analysis)}")
        return merged_analysis

    def _generate_single_angle_with_retry(self, angle_num: int, algorithm_result: Dict,
                                        user_question: str, question_type: str, max_retries: int = 3) -> Optional[str]:
        """生成单个角度分析 - 带重试机制"""

        for attempt in range(1, max_retries + 1):
            try:
                print(f"🎯 DEBUG: 第{angle_num}角度分析 - 第{attempt}次尝试...")

                # 生成单个角度的分析
                analysis = self._generate_single_angle_analysis(angle_num, algorithm_result, user_question, question_type)

                # 检查分析质量
                if self._validate_analysis_quality(analysis, angle_num):
                    print(f"✅ DEBUG: 第{angle_num}角度分析第{attempt}次尝试成功")
                    return analysis
                else:
                    print(f"⚠️ DEBUG: 第{angle_num}角度分析第{attempt}次尝试质量不合格，准备重试...")
                    if attempt == max_retries:
                        print(f"❌ DEBUG: 第{angle_num}角度分析{max_retries}次尝试后仍不合格")
                        return None

            except Exception as e:
                print(f"❌ DEBUG: 第{angle_num}角度分析第{attempt}次尝试出错: {e}")
                if attempt == max_retries:
                    print(f"❌ DEBUG: 第{angle_num}角度分析{max_retries}次尝试后仍然失败")
                    return None

        return None

    def _validate_analysis_quality(self, analysis: str, angle_num: int) -> bool:
        """验证分析质量"""
        if not analysis or not analysis.strip():
            print(f"❌ DEBUG: 第{angle_num}角度分析为空")
            return False

        # 检查长度（至少应该有500字）
        if len(analysis.strip()) < 500:
            print(f"❌ DEBUG: 第{angle_num}角度分析长度不足: {len(analysis.strip())}字 (最少需要500字)")
            return False

        # 检查是否包含基本内容
        required_keywords = ["紫薇", "八字", "命理", "分析"]
        if not any(keyword in analysis for keyword in required_keywords):
            print(f"❌ DEBUG: 第{angle_num}角度分析缺少关键内容")
            return False

        print(f"✅ DEBUG: 第{angle_num}角度分析质量合格: {len(analysis.strip())}字")
        return True

    def _generate_single_angle_analysis(self, angle_num: int, algorithm_result: Dict,
                                      user_question: str, question_type: str) -> str:
        """生成单个角度的分析"""

        # 构建角度特定的提示词
        angle_prompt = self._build_angle_specific_prompt(angle_num, algorithm_result, user_question, question_type)

        # 调用API生成分析
        try:
            analysis = self._call_local_chat_api(angle_prompt)
            return analysis
        except Exception as e:
            print(f"❌ DEBUG: 第{angle_num}角度分析API调用失败: {e}")
            raise e

    def _build_angle_specific_prompt(self, angle_num: int, algorithm_result: Dict,
                                   user_question: str, question_type: str) -> str:
        """构建角度特定的提示词"""

        # 基础数据提取
        base_data = self._extract_algorithm_data(algorithm_result)

        # 角度特定的分析重点
        angle_focuses = {
            1: {
                "title": "命理基础与天赋特质",
                "focus": "基于紫薇斗数命宫、身宫配置和八字日主特质，深度分析先天禀赋、性格特点、天赋才能",
                "keywords": ["命宫主星", "身宫配置", "日主强弱", "五行特质", "先天禀赋", "性格分析"]
            },
            2: {
                "title": "人生格局与发展方向",
                "focus": "结合紫薇斗数格局和八字格局，分析人生发展方向、事业适性、财运特点",
                "keywords": ["命理格局", "事业宫", "财帛宫", "官禄宫", "用神喜忌", "发展方向"]
            },
            3: {
                "title": "感情婚姻与人际关系",
                "focus": "通过夫妻宫、子女宫配置和八字桃花、配偶星分析感情模式、婚姻状况、人际关系",
                "keywords": ["夫妻宫", "子女宫", "桃花星", "配偶星", "感情模式", "人际关系"]
            },
            4: {
                "title": "健康状况与人生警示",
                "focus": "基于疾厄宫、父母宫和八字五行平衡分析健康状况、需要注意的问题和人生警示",
                "keywords": ["疾厄宫", "父母宫", "五行平衡", "健康状况", "煞星影响", "人生警示"]
            }
        }

        current_angle = angle_focuses[angle_num]

        prompt = f"""你是一位资深的紫薇斗数和八字命理大师，请基于真实排盘数据进行第{angle_num}角度专业分析。

【用户问题】{user_question}
【问题类型】{question_type}

【分析角度】第{angle_num}角度 - {current_angle['title']}
【分析重点】{current_angle['focus']}

【真实排盘数据】
{base_data}

【第{angle_num}角度分析要求】
1. **专业深度**：必须基于真实排盘数据，不得编造任何星曜位置或命理信息
2. **分析重点**：重点关注{current_angle['keywords']}等方面
3. **双重体系**：同时运用紫薇斗数和八字命理两套体系进行分析
4. **内容平衡**：优势分析30%，挑战警示40%，具体建议30%
5. **字数要求**：至少2000-3000字的深度分析，越详细越好
6. **客观真实**：不能只说好话，必须指出具体的不利因素和潜在问题
7. **理论依据**：每个结论都要有明确的星曜配置或八字理论依据

【输出要求】
- 直接输出纯文本分析内容，不要任何格式化
- 绝对禁止使用JSON、XML或代码块格式
- 使用流畅的中文段落，就像写文章一样
- 分析要深入具体，避免泛泛而谈
- 必须包含具体的不利因素分析
- 给出明确可行的建议

请直接开始第{angle_num}角度的深度分析：
"""

        return prompt

    def _extract_algorithm_data(self, algorithm_result: Dict) -> str:
        """提取算法数据用于提示词"""
        data_text = ""

        if algorithm_result.get("type") == "comprehensive":
            results = algorithm_result.get("results", {})

            # 紫薇斗数数据
            if "ziwei" in results:
                ziwei_data = results["ziwei"]["data"]
                birth_info = ziwei_data.get("birth_info", {})
                palaces = ziwei_data.get("palaces", {})

                data_text += f"""
【紫薇斗数排盘】
出生信息: {birth_info.get('solar', '')} ({birth_info.get('lunar', '')})
八字: {birth_info.get('chinese_date', '')}
生肖: {ziwei_data.get('zodiac', '')}

十二宫详细配置:
"""

                for palace_name, palace_info in palaces.items():
                    major_stars = "、".join(palace_info.get("major_stars", []))
                    minor_stars = "、".join(palace_info.get("minor_stars", []))
                    body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""

                    data_text += f"- {palace_name}({palace_info.get('position', '')}){body_mark}:\n"
                    if major_stars:
                        data_text += f"  主星: {major_stars}\n"
                    if minor_stars:
                        data_text += f"  辅星: {minor_stars}\n"

            # 八字数据
            if "bazi" in results:
                bazi_data = results["bazi"]["data"]
                if "raw_result" in bazi_data:
                    raw_result = bazi_data["raw_result"]

                    data_text += f"""
【八字命理分析】
四柱: {raw_result.get("干支", {}).get("文本", "")}
"""

                    # 五行分析
                    if "五行" in raw_result:
                        data_text += "五行强弱:\n"
                        for element, info in raw_result["五行"].items():
                            data_text += f"- {element}: {info.get('旺衰', '')} (数值: {info.get('五行数', '')})\n"

        return data_text

    def _call_local_chat_api(self, prompt: str) -> str:
        """调用本地聊天API - 支持六爻算卦"""
        try:
            # 使用已有的chat_api_func
            if hasattr(self, 'chat_api_func') and self.chat_api_func:
                response = self.chat_api_func(prompt)
                print(f"✅ DEBUG: API调用成功，响应长度: {len(response)}")
                return response

            # 备用：直接调用API
            import requests

            # 根据提示词内容选择合适的系统提示
            if "六爻" in prompt or "算卦" in prompt or "卦象" in prompt:
                system_content = "你是专业的六爻算卦大师，精通易经卦象分析，必须基于提供的真实卦象数据进行专业分析，不得编造任何信息。"
            else:
                system_content = "你是专业的紫薇斗数和八字命理大师，必须基于提供的真实排盘数据进行分析，不得编造任何信息。"

            url = "http://localhost:8001/v1/chat/completions"
            data = {
                "model": "deepseek-ai/DeepSeek-V3",
                "messages": [
                    {
                        "role": "system",
                        "content": system_content
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "stream": False,
                "max_tokens": 12000,  # 支持长篇分析
                "temperature": 0.7
            }

            print(f"🔗 DEBUG: 调用API - {url}")
            response = requests.post(url, json=data, timeout=600)  # 10分钟超时
            result = response.json()

            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"✅ DEBUG: API调用成功，响应长度: {len(content)}")
                return content
            else:
                print(f"❌ DEBUG: API返回格式异常: {result}")
                raise Exception("API返回格式异常")

        except Exception as e:
            print(f"❌ DEBUG: API调用失败: {e}")
            # 返回一个默认响应而不是抛出异常
            return f"API调用失败: {str(e)}，请稍后重试。"

    def _generate_compact_analysis(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """生成紧凑版分析"""
        print("📝 DEBUG: 生成紧凑版分析...")

        # 构建包含真实排盘数据的提示词
        real_data_prompt = self._build_real_data_prompt(algorithm_result)

        prompt = f"""你是一位专业的紫薇算命师，请基于真实排盘数据提供简洁明了的分析。

【真实排盘数据】
{real_data_prompt}

【用户问题】
{user_question}

【输出要求】
1. 内容简洁明了，控制在300-500字
2. 重点突出核心信息和关键建议
3. 使用通俗易懂的语言
4. 分段清晰，便于快速阅读
5. 不要使用JSON格式，使用流畅的中文

请提供简洁而专业的分析。
"""

        try:
            response = self._call_local_chat_api(prompt)
            print(f"✅ DEBUG: 紧凑版分析完成，长度: {len(response)}")
            return response
        except Exception as e:
            print(f"❌ DEBUG: 紧凑版分析失败: {e}")
            return "分析生成失败，请稍后重试。"

    def _generate_compact_from_detailed(self, detailed_analysis: str, user_question: str, question_type: str) -> str:
        """基于详细分析生成简洁版 - 修复后的正确方法"""
        print("📝 DEBUG: 基于详细分析生成简洁版...")

        prompt = f"""请将下面的详细紫薇算命分析总结为简洁版本。

【详细分析内容】
{detailed_analysis}

【用户问题】
{user_question}

【输出要求】
1. 提取最核心的3-5个要点
2. 控制在300-500字以内
3. 保持专业性但语言通俗易懂
4. 突出最重要的建议和预测
5. 不要使用JSON格式，使用流畅的中文
6. 保留关键的星曜信息和具体建议

请直接输出简洁版分析：
"""

        try:
            response = self._call_local_chat_api(prompt)
            print(f"✅ DEBUG: 简洁版生成完成，长度: {len(response)}")
            return response
        except KeyboardInterrupt:
            print("⚠️ DEBUG: 简洁版生成被中断")
            return "简洁版分析生成被中断。"
        except Exception as e:
            print(f"❌ DEBUG: 简洁版生成失败: {e}")
            return "简洁版分析生成失败，请稍后重试。"

    def _generate_detailed_analysis(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """生成详细版4角度分析 - 添加中断机制"""
        print("🔮 DEBUG: 生成详细版4角度分析...")

        try:
            # 第1步：问题分类
            print("📝 DEBUG: 第1步 - 问题分类分析...")
            theme = self._get_question_classification(user_question)
            print(f"✅ DEBUG: 问题分类: {theme}")

            # 第2步：4角度深度分析
            print("🔮 DEBUG: 第2步 - 4角度深度分析...")
            multi_responses = []
            for i in range(4):
                print(f"🎯 DEBUG: 生成第{i+1}角度分析...")
                response = self._generate_angle_analysis(algorithm_result, user_question, theme, i)
                multi_responses.append(response)
                print(f"✅ DEBUG: 第{i+1}角度分析完成，长度: {len(response)}")

                # 检查是否需要中断
                if len(response) == 0:
                    print("⚠️ DEBUG: 检测到空响应，可能被中断")
                    break

            # 第3步：智能合并整合
            print("🧠 DEBUG: 第3步 - 智能合并整合...")
            final_analysis = self._merge_multi_analysis(multi_responses, user_question, theme)

            print(f"🎉 DEBUG: 详细版分析完成，最终长度: {len(final_analysis)}")
            return final_analysis

        except KeyboardInterrupt:
            print("⚠️ DEBUG: 详细分析被用户中断")
            return "详细分析已被用户中断。"
        except Exception as e:
            print(f"❌ DEBUG: 详细分析失败: {e}")
            return f"详细分析生成失败: {str(e)}"

    def _get_question_classification(self, user_question: str) -> str:
        """获取问题分类"""
        prompt = f"""
请判断下列问题属于占卜的哪一种分类或主题
注意：你只需要输出你判断的主题分类，即输出一个或几个词语，而不是一段话。
###问题:{user_question}
"""

        try:
            response = self._call_local_chat_api(prompt)
            return response.strip()
        except Exception as e:
            print(f"❌ DEBUG: 问题分类失败: {e}")
            return "综合运势"

    def _generate_angle_analysis(self, algorithm_result: Dict, user_question: str, theme: str, angle_num: int) -> str:
        """生成单个角度的专业分析 - 添加中断机制"""

        # 构建包含真实排盘数据的专业提示词
        real_data_prompt = self._build_real_data_prompt(algorithm_result)

        prompt = f"""你是一位精通紫薇斗数和八字命理的顶级大师，擅长紫薇斗数与八字命理的综合分析。请基于真实的紫薇斗数排盘和八字命理数据进行极其专业的综合分析。

【真实排盘数据】
{real_data_prompt}

【用户问题】{user_question}
【分析主题】{theme}

【综合分析要求 - 紫薇斗数+八字命理】
你必须同时运用紫薇斗数和八字命理两套体系进行综合分析：

1. **紫薇斗数分析**：
   - 十二宫星曜配置的深层含义
   - 主星、辅星、煞星的相互作用
   - 命宫、身宫的核心特质
   - 各宫位星曜组合的吉凶判断

2. **八字命理分析**：
   - 四柱干支的五行生克关系
   - 五行强弱对性格和运势的影响
   - 十神配置的人生格局
   - 大运流年的运势变化

3. **双重体系互证**：
   - 紫薇斗数与八字命理的相互印证
   - 两套体系得出结论的一致性验证
   - 不同体系角度的补充和深化
   - 综合判断的准确性提升

【分析深度要求】
1. **推理性分析**：每个结论都要有星曜理论依据，说明"为什么"
2. **客观平衡**：既要指出优势，也要明确指出挑战和需要注意的问题
3. **具体指导**：给出明确的行动建议，不能模棱两可
4. **逻辑连贯**：从星曜配置→性格特质→人生影响→具体建议，层层递进

【内容要求】
1. **命理基础**：详细分析命宫、身宫主星的特质和相互影响
2. **优势分析**：基于星曜配置分析天赋和有利因素（约30%）
3. **挑战警示**：重点分析煞星、空星、不利组合的影响（约40%）
4. **具体建议**：针对挑战给出明确的应对策略（约30%）

【分析角度】（选择其中一个重点展开）
- 角度1：性格特质与人际关系
- 角度2：事业发展与财运趋势
- 角度3：感情婚姻与家庭关系
- 角度4：健康状况与人生阶段

【输出要求 - 极其重要】
1. 使用流畅中文，绝对不要JSON格式
2. 每个观点都要有星曜依据和详细推理过程
3. 必须包含具体的不利因素分析（40%内容）
4. 给出明确可执行的建议，不能模棱两可
5. 字数要求：至少2000-3000字，内容极其充实详细，长篇大论
6. 深度挖掘：比如夫妻宫要分析对象类型、对象父母、相处方式等
7. 层层递进：从星曜配置→性格特质→具体影响→明确建议

【深度分析示例要求】
- 夫妻宫分析要包括：对象性格、外貌、家庭背景、父母特质、相处模式、感情问题、婚姻建议
- 事业宫分析要包括：适合行业、具体职位、发展阶段、合作伙伴、事业挑战、成功策略
- 财运分析要包括：赚钱方式、投资倾向、理财建议、财务风险、财富积累方法
- 健康分析要包括：具体部位、疾病倾向、预防方法、养生建议、生活习惯调整

【重要提醒】
- 不要只说好话，要客观指出问题和挑战
- 每个结论都要有理论支撑和推理过程
- 建议要具体可行，不能泛泛而谈
- 分析要深入细致，能挖掘的都要挖掘
- 必须有指导性，帮助用户具体改善
"""

        try:
            response = self._call_local_chat_api(prompt)
            return response
        except KeyboardInterrupt:
            print(f"⚠️ DEBUG: 第{angle_num+1}角度分析被中断")
            return ""
        except Exception as e:
            print(f"❌ DEBUG: 第{angle_num+1}角度分析失败: {e}")
            return f"第{angle_num+1}角度分析暂时不可用"

    def _build_real_data_prompt(self, algorithm_result: Dict) -> str:
        """构建包含真实排盘数据的提示词"""

        if algorithm_result.get("type") == "comprehensive":
            # 综合分析结果
            results = algorithm_result.get("results", {})
            data_prompt = ""

            # 紫薇斗数数据
            if "ziwei" in results and results["ziwei"].get("success"):
                ziwei_data = results["ziwei"]["data"]
                birth_info = ziwei_data.get("birth_info", {})
                palaces = ziwei_data.get("palaces", {})

                data_prompt += f"""
【紫薇斗数排盘】
出生信息: {birth_info.get('solar', '')} ({birth_info.get('lunar', '')})
八字: {birth_info.get('chinese_date', '')}
生肖: {ziwei_data.get('zodiac', '')}
星座: {ziwei_data.get('sign', '')}

十二宫详细配置:
"""

                for palace_name, palace_info in palaces.items():
                    major_stars = "、".join(palace_info.get("major_stars", []))
                    minor_stars = "、".join(palace_info.get("minor_stars", []))
                    adjective_stars = "、".join(palace_info.get("adjective_stars", []))
                    body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""

                    data_prompt += f"- {palace_name}({palace_info.get('position', '')}){body_mark}:\n"
                    if major_stars:
                        data_prompt += f"  主星: {major_stars}\n"
                    if minor_stars:
                        data_prompt += f"  辅星: {minor_stars}\n"
                    if adjective_stars:
                        data_prompt += f"  杂曜: {adjective_stars}\n"

            # 八字数据
            if "bazi" in results and results["bazi"].get("success"):
                bazi_data = results["bazi"]["data"]
                if "raw_result" in bazi_data:
                    raw_result = bazi_data["raw_result"]

                    data_prompt += f"""
【八字命理分析】
四柱: {raw_result.get("干支", {}).get("文本", "")}
"""

                    # 五行分析
                    if "五行" in raw_result:
                        data_prompt += "五行强弱:\n"
                        for element, info in raw_result["五行"].items():
                            data_prompt += f"- {element}: {info.get('旺衰', '')} (数值: {info.get('五行数', '')})\n"

        else:
            # 单一算法结果
            real_data = algorithm_result.get("data", {})
            data_prompt = f"【排盘数据】\n{json.dumps(real_data, ensure_ascii=False, indent=2)}"

        return data_prompt

    def _generate_chart_display(self, algorithm_result: Dict) -> str:
        """生成排盘图显示 - 支持紫薇斗数+八字整合"""
        try:
            ziwei_data = None
            bazi_data = None

            if algorithm_result.get("type") == "comprehensive":
                # 综合分析结果
                results = algorithm_result.get("results", {})

                # 获取紫薇斗数数据
                if "ziwei" in results and results["ziwei"].get("success"):
                    ziwei_data = results["ziwei"]["data"]

                # 获取八字数据
                if "bazi" in results and results["bazi"].get("success"):
                    bazi_data = results["bazi"]["data"]

                if ziwei_data:
                    return self._build_chart_with_image(ziwei_data, bazi_data)
                else:
                    return "紫薇斗数数据不可用"
            else:
                # 单一紫薇算法结果
                data = algorithm_result.get("data", {})
                return self._build_chart_with_image(data)
        except Exception as e:
            print(f"❌ DEBUG: 排盘图生成失败: {e}")
            return "排盘图生成失败"

    def _build_chart_with_image(self, ziwei_data: Dict, bazi_data: Dict = None) -> str:
        """构建带图片的整合排盘图 - 只显示图片，不要文字排盘"""
        try:
            # 生成整合图片
            image_path = self._generate_chart_image(ziwei_data, bazi_data)

            if image_path:
                chart_type = "整合命理排盘" if bazi_data else "紫薇斗数命盘"
                return f"""📊 **{chart_type}图片**
图片已生成: {image_path}"""
            else:
                return "📊 **命盘排盘图**\n排盘图生成失败，请稍后重试"

        except Exception as e:
            print(f"❌ DEBUG: 整合图片排盘生成失败: {e}")
            return "📊 **命盘排盘图**\n排盘图生成失败，请稍后重试"

    def _build_integrated_text_chart(self, ziwei_data: Dict, bazi_data: Dict = None) -> str:
        """构建整合的文本排盘"""
        try:
            # 紫薇斗数部分
            ziwei_chart = self._build_ziwei_chart(ziwei_data)

            if not bazi_data:
                return ziwei_chart

            # 八字部分
            bazi_chart = self._build_bazi_text_chart(bazi_data)

            # 整合显示
            integrated_chart = f"""
{ziwei_chart}

╔═══════════════════════════════════════════════════════════════════════════════╗
║                           八字命理分析                                        ║
╚═══════════════════════════════════════════════════════════════════════════════╝

{bazi_chart}
"""
            return integrated_chart

        except Exception as e:
            print(f"❌ DEBUG: 整合文本排盘失败: {e}")
            return self._build_ziwei_chart(ziwei_data)

    def _build_bazi_text_chart(self, bazi_data: Dict) -> str:
        """构建八字文本排盘"""
        try:
            raw_result = bazi_data.get("raw_result", {})
            birth_info = bazi_data.get("birth_info", {})

            chart = f"""
【出生信息】
时间: {birth_info.get('datetime', '')}
性别: {birth_info.get('gender', '')}

"""

            # 四柱八字
            if "干支" in raw_result:
                ganzhi = raw_result["干支"]
                chart += f"""【四柱八字】
{ganzhi.get('文本', '')}

"""

            # 五行分析
            if "五行" in raw_result:
                wuxing = raw_result["五行"]
                chart += "【五行强弱】\n"
                for element, info in wuxing.items():
                    wangcui = info.get("旺衰", "")
                    shuzhi = info.get("五行数", "")
                    chart += f"• {element}: {wangcui} (数值: {shuzhi})\n"
                chart += "\n"

            # 大运信息
            if "大运" in raw_result:
                dayun = raw_result["大运"]
                chart += "【大运流年】\n"
                for i, (dayun_name, dayun_info) in enumerate(list(dayun.items())[:5]):
                    shishen = dayun_info.get("十神", "")
                    nianfen = dayun_info.get("年份", "")
                    chart += f"• {dayun_name}: {shishen} ({nianfen}年起)\n"
                chart += "\n"

            return chart

        except Exception as e:
            print(f"❌ DEBUG: 八字文本排盘失败: {e}")
            return "八字排盘数据解析失败"

    def _generate_chart_image(self, ziwei_data: Dict, bazi_data: Dict = None) -> str:
        """生成整合的排盘图片 - 紫薇斗数 + 八字"""
        if not PIL_AVAILABLE:
            print("⚠️ 图片库未安装，跳过图片生成")
            return None

        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            from matplotlib.font_manager import FontProperties

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建更大的图形来容纳两个系统
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 12))

            # 左侧：紫薇斗数
            self._draw_ziwei_chart(ax1, ziwei_data)

            # 右侧：八字命理
            if bazi_data:
                self._draw_bazi_chart(ax2, bazi_data)
            else:
                ax2.text(0.5, 0.5, "八字数据不可用", ha='center', va='center',
                        transform=ax2.transAxes, fontsize=16)
                ax2.axis('off')

            # 保存图片
            output_dir = "charts"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 使用更复杂的hash来避免冲突
            data_hash = hash(str(ziwei_data) + str(bazi_data)) % 100000
            image_path = f"{output_dir}/integrated_chart_{data_hash}.png"
            plt.savefig(image_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 整合排盘图片已生成: {image_path}")
            return image_path

        except Exception as e:
            print(f"❌ 整合图片生成失败: {e}")
            return None

    def _draw_ziwei_chart(self, ax, ziwei_data: Dict):
        """绘制紫薇斗数图表"""
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.set_aspect('equal')
        ax.axis('off')

        # 绘制外框
        outer_rect = patches.Rectangle((1, 1), 8, 8, linewidth=3, edgecolor='purple', facecolor='white')
        ax.add_patch(outer_rect)

        # 绘制十二宫格
        self._draw_palace_grid(ax, ziwei_data)

        # 添加标题
        birth_info = ziwei_data.get("birth_info", {})
        title = f"紫薇斗数命盘\n{birth_info.get('solar', '')}"
        ax.text(5, 9.5, title, ha='center', va='center', fontsize=14, fontweight='bold', color='purple')

    def _draw_bazi_chart(self, ax, bazi_data: Dict):
        """绘制八字命理图表"""
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.set_aspect('equal')
        ax.axis('off')

        # 绘制外框
        outer_rect = patches.Rectangle((1, 1), 8, 8, linewidth=3, edgecolor='darkgreen', facecolor='white')
        ax.add_patch(outer_rect)

        # 获取八字数据
        raw_result = bazi_data.get("raw_result", {})
        birth_info = bazi_data.get("birth_info", {})

        # 添加标题
        title = f"八字命理分析\n{birth_info.get('datetime', '')}"
        ax.text(5, 9.5, title, ha='center', va='center', fontsize=14, fontweight='bold', color='darkgreen')

        # 绘制四柱八字
        if "干支" in raw_result:
            ganzhi = raw_result["干支"]
            self._draw_sizhu(ax, ganzhi)

        # 绘制五行分析
        if "五行" in raw_result:
            wuxing = raw_result["五行"]
            self._draw_wuxing_analysis(ax, wuxing)

        # 绘制大运信息
        if "大运" in raw_result:
            dayun = raw_result["大运"]
            self._draw_dayun_info(ax, dayun)

    def _draw_sizhu(self, ax, ganzhi_data: Dict):
        """绘制四柱八字"""
        try:
            # 四柱位置
            positions = [(2, 7.5), (3.5, 7.5), (5, 7.5), (6.5, 7.5)]
            labels = ["年柱", "月柱", "日柱", "时柱"]

            if "文本" in ganzhi_data:
                sizhu_text = ganzhi_data["文本"]
                # 解析四柱文本，例如："甲子 乙丑 丙寅 丁卯"
                sizhu_parts = sizhu_text.split()

                for i, (x, y) in enumerate(positions):
                    # 绘制柱子框
                    rect = patches.Rectangle((x-0.4, y-0.8), 0.8, 1.6,
                                           linewidth=1, edgecolor='darkgreen',
                                           facecolor='lightgreen', alpha=0.3)
                    ax.add_patch(rect)

                    # 添加标签
                    ax.text(x, y+0.6, labels[i], ha='center', va='center',
                           fontsize=10, fontweight='bold')

                    # 添加干支
                    if i < len(sizhu_parts):
                        ganzhi = sizhu_parts[i]
                        if len(ganzhi) >= 2:
                            # 天干
                            ax.text(x, y+0.2, ganzhi[0], ha='center', va='center',
                                   fontsize=12, color='red', fontweight='bold')
                            # 地支
                            ax.text(x, y-0.2, ganzhi[1], ha='center', va='center',
                                   fontsize=12, color='blue', fontweight='bold')
        except Exception as e:
            print(f"❌ 四柱绘制失败: {e}")

    def _draw_wuxing_analysis(self, ax, wuxing_data: Dict):
        """绘制五行分析"""
        try:
            # 五行位置（圆形排列）
            import math
            center_x, center_y = 5, 4.5
            radius = 1.5

            wuxing_order = ["木", "火", "土", "金", "水"]
            colors = ["green", "red", "yellow", "gold", "blue"]

            for i, wuxing in enumerate(wuxing_order):
                if wuxing in wuxing_data:
                    angle = i * 2 * math.pi / 5 - math.pi / 2  # 从顶部开始
                    x = center_x + radius * math.cos(angle)
                    y = center_y + radius * math.sin(angle)

                    # 获取五行信息
                    wuxing_info = wuxing_data[wuxing]
                    wangcui = wuxing_info.get("旺衰", "")
                    shuzhi = wuxing_info.get("五行数", "")

                    # 绘制五行圆圈
                    circle = patches.Circle((x, y), 0.3, facecolor=colors[i],
                                          alpha=0.6, edgecolor='black')
                    ax.add_patch(circle)

                    # 添加五行文字
                    ax.text(x, y+0.1, wuxing, ha='center', va='center',
                           fontsize=10, fontweight='bold', color='white')
                    ax.text(x, y-0.1, wangcui, ha='center', va='center',
                           fontsize=8, color='white')

                    # 添加数值
                    ax.text(x, y-0.5, f"({shuzhi})", ha='center', va='center',
                           fontsize=8, color='black')

            # 中心标题
            ax.text(center_x, center_y, "五行", ha='center', va='center',
                   fontsize=12, fontweight='bold')

        except Exception as e:
            print(f"❌ 五行分析绘制失败: {e}")

    def _draw_dayun_info(self, ax, dayun_data: Dict):
        """绘制大运信息"""
        try:
            # 大运显示区域
            start_y = 2.5
            ax.text(5, start_y + 0.5, "大运流年", ha='center', va='center',
                   fontsize=12, fontweight='bold', color='darkgreen')

            # 显示前5个大运
            dayun_items = list(dayun_data.items())[:5]
            for i, (dayun_name, dayun_info) in enumerate(dayun_items):
                y = start_y - i * 0.3

                shishen = dayun_info.get("十神", "")
                nianfen = dayun_info.get("年份", "")

                text = f"{dayun_name} {shishen} ({nianfen}年起)"
                ax.text(5, y, text, ha='center', va='center', fontsize=9)

        except Exception as e:
            print(f"❌ 大运信息绘制失败: {e}")

    def _draw_palace_grid(self, ax, ziwei_data: Dict):
        """绘制十二宫格和星曜"""
        palaces = ziwei_data.get("palaces", {})

        # 十二宫位置坐标 (按传统排盘顺序) - 修正坐标
        palace_positions = {
            "巳": (7, 7, 2, 2),   # 右上
            "午": (5, 7, 2, 2),   # 上中
            "未": (3, 7, 2, 2),   # 左上
            "申": (1, 7, 2, 2),   # 左上角
            "辰": (7, 5, 2, 2),   # 右中
            "酉": (1, 5, 2, 2),   # 左中
            "卯": (7, 3, 2, 2),   # 右下
            "戌": (1, 3, 2, 2),   # 左下
            "寅": (7, 1, 2, 2),   # 右下角
            "丑": (5, 1, 2, 2),   # 下中
            "子": (3, 1, 2, 2),   # 左下
            "亥": (1, 1, 2, 2),   # 左下角
        }

        print(f"🎨 DEBUG: 开始绘制紫薇斗数宫位，共{len(palaces)}个宫位")

        # 绘制每个宫位
        for palace_name, palace_info in palaces.items():
            position = palace_info.get("position", "")
            print(f"🎨 DEBUG: 绘制宫位 {palace_name} 在 {position}")

            if position in palace_positions:
                x, y, w, h = palace_positions[position]
                print(f"🎨 DEBUG: 宫位坐标 ({x}, {y}, {w}, {h})")

                # 绘制宫位框
                rect = patches.Rectangle((x, y), w, h, linewidth=1, edgecolor='gray', facecolor='lightblue', alpha=0.3)
                ax.add_patch(rect)

                # 添加宫位名称
                body_mark = "[身]" if palace_info.get("is_body_palace") else ""
                palace_text = f"{palace_name}{body_mark}\n({position})"
                ax.text(x + w/2, y + h - 0.3, palace_text, ha='center', va='center', fontsize=8, fontweight='bold')

                # 添加主星
                major_stars = palace_info.get("major_stars", [])
                if major_stars:
                    stars_text = " ".join(major_stars[:3])  # 最多显示3个主星
                    ax.text(x + w/2, y + h/2, stars_text, ha='center', va='center', fontsize=7, color='red')
                    print(f"🎨 DEBUG: 主星 {stars_text}")

                # 添加辅星
                minor_stars = palace_info.get("minor_stars", [])
                if minor_stars:
                    minor_text = " ".join(minor_stars[:2])  # 最多显示2个辅星
                    ax.text(x + w/2, y + 0.3, minor_text, ha='center', va='center', fontsize=6, color='blue')
                    print(f"🎨 DEBUG: 辅星 {minor_text}")
            else:
                print(f"⚠️ DEBUG: 宫位 {palace_name} 的地支 {position} 不在坐标映射中")

        # 绘制中宫
        center_rect = patches.Rectangle((3, 3), 4, 4, linewidth=2, edgecolor='purple', facecolor='lightyellow', alpha=0.5)
        ax.add_patch(center_rect)

        # 中宫信息
        birth_info = ziwei_data.get("birth_info", {})
        center_text = f"中宫\n{ziwei_data.get('zodiac', '')}年生人\n{birth_info.get('chinese_date', '')}"
        ax.text(5, 5, center_text, ha='center', va='center', fontsize=11, fontweight='bold')

    def _build_ziwei_chart(self, ziwei_data: Dict) -> str:
        """构建紫薇斗数排盘图 - 改进版"""
        try:
            birth_info = ziwei_data.get("birth_info", {})
            palaces = ziwei_data.get("palaces", {})

            # 地支顺序（逆时针，符合传统排盘）
            branches = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

            # 创建12宫位置映射
            chart_positions = {}
            for palace_name, palace_info in palaces.items():
                position = palace_info.get("position", "")
                if position in branches:
                    chart_positions[position] = {
                        "name": palace_name,
                        "major_stars": palace_info.get("major_stars", []),
                        "minor_stars": palace_info.get("minor_stars", []),
                        "is_body_palace": palace_info.get("is_body_palace", False)
                    }

            # 构建详细排盘图
            chart = f"""
╔═══════════════════════════════════════════════════════════════════════════════╗
║                           紫薇斗数命盘                                        ║
║  出生: {birth_info.get('solar', '')} ({birth_info.get('lunar', '')})           ║
║  八字: {birth_info.get('chinese_date', '')}                                   ║
║  生肖: {ziwei_data.get('zodiac', '')}  星座: {ziwei_data.get('sign', '')}                                      ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║  ┌─────────────┬─────────────┬─────────────┬─────────────┐                   ║
║  │    巳宫     │    午宫     │    未宫     │    申宫     │                   ║
║  │{self._format_palace_detailed(chart_positions.get('巳', {}), 11)}│{self._format_palace_detailed(chart_positions.get('午', {}), 11)}│{self._format_palace_detailed(chart_positions.get('未', {}), 11)}│{self._format_palace_detailed(chart_positions.get('申', {}), 11)}│                   ║
║  │{self._format_stars_line(chart_positions.get('巳', {}), 11)}│{self._format_stars_line(chart_positions.get('午', {}), 11)}│{self._format_stars_line(chart_positions.get('未', {}), 11)}│{self._format_stars_line(chart_positions.get('申', {}), 11)}│                   ║
║  ├─────────────┼─────────────┼─────────────┼─────────────┤                   ║
║  │    辰宫     │             │             │    酉宫     │                   ║
║  │{self._format_palace_detailed(chart_positions.get('辰', {}), 11)}│    中宫     │   命主      │{self._format_palace_detailed(chart_positions.get('酉', {}), 11)}│                   ║
║  │{self._format_stars_line(chart_positions.get('辰', {}), 11)}│  {ziwei_data.get('zodiac', '')}年生人  │   身主      │{self._format_stars_line(chart_positions.get('酉', {}), 11)}│                   ║
║  ├─────────────┼─────────────┼─────────────┼─────────────┤                   ║
║  │    卯宫     │             │             │    戌宫     │                   ║
║  │{self._format_palace_detailed(chart_positions.get('卯', {}), 11)}│             │             │{self._format_palace_detailed(chart_positions.get('戌', {}), 11)}│                   ║
║  │{self._format_stars_line(chart_positions.get('卯', {}), 11)}│             │             │{self._format_stars_line(chart_positions.get('戌', {}), 11)}│                   ║
║  ├─────────────┼─────────────┼─────────────┼─────────────┤                   ║
║  │    寅宫     │    丑宫     │    子宫     │    亥宫     │                   ║
║  │{self._format_palace_detailed(chart_positions.get('寅', {}), 11)}│{self._format_palace_detailed(chart_positions.get('丑', {}), 11)}│{self._format_palace_detailed(chart_positions.get('子', {}), 11)}│{self._format_palace_detailed(chart_positions.get('亥', {}), 11)}│                   ║
║  │{self._format_stars_line(chart_positions.get('寅', {}), 11)}│{self._format_stars_line(chart_positions.get('丑', {}), 11)}│{self._format_stars_line(chart_positions.get('子', {}), 11)}│{self._format_stars_line(chart_positions.get('亥', {}), 11)}│                   ║
║  └─────────────┴─────────────┴─────────────┴─────────────┘                   ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝

【十二宫详细信息】
{self._build_palace_details(palaces)}
"""
            return chart

        except Exception as e:
            print(f"❌ DEBUG: 排盘图构建失败: {e}")
            return "排盘图构建失败"

    def _format_palace_content(self, palace_info: Dict, width: int) -> str:
        """格式化宫位内容 - 简单版"""
        if not palace_info:
            return " " * width

        name = palace_info.get("name", "")
        major_stars = palace_info.get("major_stars", [])
        body_mark = "[身]" if palace_info.get("is_body_palace") else ""

        # 组合显示内容
        content = f"{name}{body_mark}"
        if major_stars:
            stars_str = " ".join(major_stars[:2])  # 最多显示2个主星
            content += f" {stars_str}"

        # 截断或填充到指定宽度
        if len(content) > width:
            content = content[:width-1] + "…"
        else:
            content = content.ljust(width)

        return content

    def _format_palace_detailed(self, palace_info: Dict, width: int) -> str:
        """格式化宫位内容 - 详细版"""
        if not palace_info:
            return " " * width

        name = palace_info.get("name", "")
        body_mark = "[身]" if palace_info.get("is_body_palace") else ""

        # 组合显示内容
        content = f"{name}{body_mark}"

        # 截断或填充到指定宽度
        if len(content) > width:
            content = content[:width-1] + "…"
        else:
            content = content.ljust(width)

        return content

    def _format_stars_line(self, palace_info: Dict, width: int) -> str:
        """格式化星曜行"""
        if not palace_info:
            return " " * width

        major_stars = palace_info.get("major_stars", [])
        minor_stars = palace_info.get("minor_stars", [])

        # 优先显示主星，然后是辅星
        all_stars = major_stars + minor_stars
        if all_stars:
            stars_str = " ".join(all_stars[:3])  # 最多显示3个星
            if len(stars_str) > width:
                stars_str = stars_str[:width-1] + "…"
            else:
                stars_str = stars_str.ljust(width)
            return stars_str
        else:
            return " " * width

    def _build_palace_details(self, palaces: Dict) -> str:
        """构建宫位详细信息"""
        details = ""

        # 按照传统顺序显示十二宫
        palace_order = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
                       "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]

        for palace_name in palace_order:
            if palace_name in palaces:
                palace_info = palaces[palace_name]
                position = palace_info.get("position", "")
                major_stars = palace_info.get("major_stars", [])
                minor_stars = palace_info.get("minor_stars", [])
                adjective_stars = palace_info.get("adjective_stars", [])
                body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""

                details += f"• {palace_name}({position}){body_mark}:\n"

                if major_stars:
                    details += f"  主星: {' '.join(major_stars)}\n"
                if minor_stars:
                    details += f"  辅星: {' '.join(minor_stars)}\n"
                if adjective_stars:
                    details += f"  杂曜: {' '.join(adjective_stars)}\n"

                details += "\n"

        return details

    def _merge_multi_analysis(self, multi_responses: list, user_question: str, theme: str) -> str:
        """合并多个角度的分析 - 添加中断机制"""

        # 过滤掉空响应
        valid_responses = [resp for resp in multi_responses if resp and resp.strip()]

        if not valid_responses:
            return "分析生成失败，所有角度分析都为空。"

        # 构建合并提示词
        reply = ""
        for i, response in enumerate(valid_responses):
            reply += f"\n第{i+1}段文字: {response}"

        prompt = f"""请将下面{len(valid_responses)}段专业命理分析合并为一篇极其详细的紫薇斗数+八字命理综合报告。

【用户问题】{user_question}
【分析主题】{theme}

【综合分析要求 - 紫薇斗数+八字命理双重体系】
1. **双重体系融合**：必须同时运用紫薇斗数和八字命理两套体系进行分析
2. **相互印证**：两套体系的结论要相互印证，增强准确性
3. **结构完整**：按照"命理基础→优势分析→挑战警示→具体建议"的逻辑组织
4. **内容平衡**：优势30%，挑战和问题40%，具体建议30%
5. **深度分析**：每个观点都要有紫薇斗数星曜和八字命理的双重理论依据
6. **客观真实**：不能只说好话，必须指出具体的不利因素和潜在问题
7. **指导明确**：给出具体可执行的建议，避免模棱两可的表述

【双重体系分析框架】
- 紫薇斗数角度：十二宫星曜配置、主星特质、煞星影响
- 八字命理角度：五行生克、十神配置、大运流年
- 综合判断：两套体系的一致性验证和互补分析

【输出结构 - 紫薇斗数+八字命理综合分析】
## 一、命理基础分析（紫薇斗数+八字双重验证）
- 紫薇斗数：命宫、身宫主星特质及相互影响
- 八字命理：四柱干支配置、五行强弱分析
- 双重体系：两套体系的一致性验证和互补

## 二、天赋优势解析（双重体系互证）
- 紫薇斗数：基于吉星配置的有利因素
- 八字命理：基于五行配置的天赋才能
- 综合判断：可以发挥的天赋和机遇

## 三、挑战与警示（重点 - 双重体系警示）
- 紫薇斗数：煞星、空星带来的具体影响
- 八字命理：五行失衡、十神不利的问题
- 综合警示：需要特别注意的人生阶段和风险

## 四、具体指导建议（双重体系指导）
- 紫薇斗数角度：基于星曜配置的应对策略
- 八字命理角度：基于五行调和的改善方法
- 综合建议：明确的行动方案和时机选择

【质量要求 - 长篇大论】
- 总长度要求：至少8000-12000字，越详细越好，不要担心字数太多
- 十二宫全面分析：每个宫位都要详细分析，不能遗漏
- 每个结论都要有理论依据和详细推理过程
- 必须包含具体的不利因素分析（40%内容）
- 建议要明确可行，不能泛泛而谈
- 使用流畅中文，分段清晰，层次分明
- 用户付费了，要物超所值，越详细越好

【输出格式要求 - 极其重要】
- 必须直接输出纯文本分析内容，不要任何格式化
- 绝对禁止使用JSON格式 {{"key": "value"}}
- 绝对禁止使用XML格式 <tag>content</tag>
- 绝对禁止使用代码块格式 ```text```
- 绝对禁止使用结构化数据格式
- 直接以自然语言文字形式进行分析
- 使用流畅的中文段落，就像写文章一样
- 不要使用任何特殊标记或符号

【待整合的{len(valid_responses)}段分析】{reply}

请直接输出合并后的深度分析报告：
"""

        try:
            final_response = self._call_local_chat_api(prompt)
            return final_response
        except KeyboardInterrupt:
            print("⚠️ DEBUG: 分析合并被中断")
            # 备用方案：直接返回第一个分析
            return valid_responses[0] if valid_responses else "分析合并被中断"
        except Exception as e:
            print(f"❌ DEBUG: 多角度分析合并失败: {e}")
            # 备用方案：直接返回第一个分析
            return valid_responses[0] if valid_responses else "分析合并失败"

    def _build_professional_analysis_prompt(self, algorithm_result: Dict, user_question: str, question_type: str) -> str:
        """构建专业命理分析提示词"""

        # 提取真实排盘数据
        if algorithm_result.get("type") == "comprehensive":
            # 综合分析
            results = algorithm_result.get("results", {})

            prompt = f"""你是一位资深的紫薇斗数和八字命理大师，请基于以下真实的排盘数据进行专业分析。

【用户问题】
{user_question}

【问题类型】
{question_type}

【真实排盘数据】
"""

            # 紫薇斗数数据
            if "ziwei" in results and results["ziwei"].get("success"):
                ziwei_data = results["ziwei"]["data"]
                birth_info = ziwei_data.get("birth_info", {})
                palaces = ziwei_data.get("palaces", {})

                prompt += f"""
【紫薇斗数排盘】
出生信息: {birth_info.get('solar', '')} ({birth_info.get('lunar', '')})
八字: {birth_info.get('chinese_date', '')}
生肖: {ziwei_data.get('zodiac', '')}
星座: {ziwei_data.get('sign', '')}

十二宫详细配置:
"""

                for palace_name, palace_info in palaces.items():
                    major_stars = "、".join(palace_info.get("major_stars", []))
                    minor_stars = "、".join(palace_info.get("minor_stars", []))
                    adjective_stars = "、".join(palace_info.get("adjective_stars", []))
                    body_mark = " [身宫]" if palace_info.get("is_body_palace") else ""

                    prompt += f"- {palace_name}({palace_info.get('position', '')}){body_mark}:\n"
                    if major_stars:
                        prompt += f"  主星: {major_stars}\n"
                    if minor_stars:
                        prompt += f"  辅星: {minor_stars}\n"
                    if adjective_stars:
                        prompt += f"  杂曜: {adjective_stars}\n"

            # 八字数据
            if "bazi" in results and results["bazi"].get("success"):
                bazi_data = results["bazi"]["data"]
                if "raw_result" in bazi_data:
                    raw_result = bazi_data["raw_result"]

                    prompt += f"""
【八字命理分析】
四柱: {raw_result.get("干支", {}).get("文本", "")}
"""

                    # 五行分析
                    if "五行" in raw_result:
                        prompt += "五行强弱:\n"
                        for element, info in raw_result["五行"].items():
                            prompt += f"- {element}: {info.get('旺衰', '')} (数值: {info.get('五行数', '')})\n"

                    # 大运信息
                    if "大运" in raw_result:
                        prompt += "大运流年:\n"
                        for dayun, info in list(raw_result["大运"].items())[:5]:  # 只显示前5个大运
                            prompt += f"- {dayun}: {info.get('十神', '')} {info.get('年份', '')}年起\n"

        else:
            # 单一算法结果
            real_data = algorithm_result.get("data", {})
            prompt = f"""你是一位资深的命理大师，请基于以下真实的排盘数据进行专业分析。

【用户问题】
{user_question}

【真实排盘数据】
{json.dumps(real_data, ensure_ascii=False, indent=2)}
"""

        prompt += f"""

【分析要求】
1. 必须基于以上真实排盘数据进行分析，不得编造任何星曜位置或命理信息
2. 重点分析命宫、身宫的星曜配置及其含义
3. 结合用户的具体问题({question_type})给出针对性建议
4. 分析要专业、准确，体现传统命理学的深度
5. 语言要通俗易懂，避免过于晦涩的术语
6. 给出实用的人生建议和注意事项

请提供详细的专业分析报告。
"""

        return prompt



    def process_user_request(self, user_input: str) -> Dict:
        """处理用户请求的完整流程"""
        print(f"🚀 DEBUG: 开始处理用户请求: {user_input}")
        logger.info(f"🚀 开始处理用户请求: {user_input}")

        # 1. 解析用户输入
        print("📝 DEBUG: 第1步: 解析用户输入...")
        logger.info("📝 第1步: 解析用户输入...")
        parsed_info = self.parse_user_input(user_input)

        # 2. 检查是否有足够信息进行排盘
        print("🔍 DEBUG: 第2步: 检查算命类型和所需信息...")
        print(f"🔍 DEBUG: 算命类型: {parsed_info['fortune_type']}")
        print(f"🔍 DEBUG: 出生信息: {parsed_info['birth_info']}")
        logger.info("🔍 第2步: 检查算命类型和所需信息...")

        # 六爻算卦不需要出生信息，其他算命类型需要
        if parsed_info["fortune_type"] == "liuyao":
            print("🎯 DEBUG: 六爻算卦不需要出生信息，使用当前时间起卦")
            # 六爻使用当前时间起卦
            from datetime import datetime
            now = datetime.now()
            parsed_info["birth_info"] = {
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "hour": now.hour,
                "gender": "男"  # 六爻不需要性别，随便设置
            }
        elif not parsed_info["birth_info"]:
            print("⚠️ DEBUG: 缺少出生信息，无法进行排盘")
            logger.warning("⚠️ 缺少出生信息，无法进行排盘")
            return {
                "success": False,
                "message": "请提供您的出生信息（年月日时）以便进行准确的算命分析。\n\n注意：如果您想进行六爻算卦，请在问题中明确提到'六爻'、'算卦'或'占卜'等关键词。",
                "need_birth_info": True
            }

        # 3. 调用真实算法
        print("🧮 DEBUG: 第3步: 调用真实算法进行排盘...")
        print(f"🧮 DEBUG: 算命类型: {parsed_info['fortune_type']}")
        logger.info("🧮 第3步: 调用真实算法进行排盘...")
        algorithm_result = self.call_real_algorithm(
            parsed_info["fortune_type"],
            parsed_info["birth_info"]
        )

        print(f"🧮 DEBUG: 算法调用结果: {algorithm_result}")

        # 4. 生成AI分析
        print("🤖 DEBUG: 第4步: 生成AI分析...")
        logger.info("🤖 第4步: 生成AI分析...")
        if algorithm_result.get("success"):
            print("✅ DEBUG: 排盘成功，开始AI分析...")
            logger.info("✅ 排盘成功，开始AI分析...")
            ai_analysis = self.generate_ai_analysis(
                algorithm_result,
                user_input,
                parsed_info["question_type"]
            )

            print("🎉 DEBUG: 处理完成！")
            logger.info("🎉 处理完成！")
            return {
                "success": True,
                "parsed_info": parsed_info,
                "algorithm_result": algorithm_result,
                "ai_analysis": ai_analysis,
                "message": ai_analysis
            }
        else:
            print(f"❌ DEBUG: 排盘失败: {algorithm_result.get('error', '未知错误')}")
            logger.error(f"❌ 排盘失败: {algorithm_result.get('error', '未知错误')}")
            return {
                "success": False,
                "message": f"排盘计算失败: {algorithm_result.get('error', '未知错误')}",
                "parsed_info": parsed_info
            }
