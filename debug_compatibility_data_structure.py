#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试合盘数据结构
"""

def debug_compatibility_data_structure():
    """调试合盘数据结构"""
    print("🔍 调试合盘数据结构")
    
    try:
        # 1. 导入必要模块
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        import json
        
        # 2. 测试数据
        person_a_info = {
            "name": "张三",
            "year": "1985",
            "month": "8", 
            "day": "15",
            "hour": "申时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "李四",
            "year": "1987",
            "month": "11",
            "day": "22", 
            "hour": "午时",
            "gender": "女"
        }
        
        # 3. 初始化引擎并计算数据
        print("🔧 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()
        
        print("📊 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)
        
        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False
        
        # 4. 分析数据结构
        print(f"\n📋 合盘数据结构分析:")
        print(f"   顶级键: {list(compatibility_data.keys())}")
        
        # 分析person_a数据
        if "person_a" in compatibility_data:
            person_a_data = compatibility_data["person_a"]
            print(f"\n👤 person_a 数据结构:")
            print(f"   键: {list(person_a_data.keys())}")
            
            for key, value in person_a_data.items():
                if isinstance(value, dict):
                    print(f"   {key}: dict with keys {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__}")
        
        # 分析person_b数据
        if "person_b" in compatibility_data:
            person_b_data = compatibility_data["person_b"]
            print(f"\n👤 person_b 数据结构:")
            print(f"   键: {list(person_b_data.keys())}")
            
            for key, value in person_b_data.items():
                if isinstance(value, dict):
                    print(f"   {key}: dict with keys {list(value.keys())}")
                else:
                    print(f"   {key}: {type(value).__name__}")
        
        # 5. 保存完整数据结构
        print(f"\n💾 保存完整数据结构...")
        
        debug_filename = f"compatibility_data_structure_debug.json"
        
        # 创建可序列化的数据副本
        serializable_data = {}
        for key, value in compatibility_data.items():
            try:
                json.dumps(value)  # 测试是否可序列化
                serializable_data[key] = value
            except (TypeError, ValueError):
                serializable_data[key] = str(value)  # 转换为字符串
        
        with open(debug_filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 数据结构已保存: {debug_filename}")
        
        # 6. 检查紫薇和八字数据的具体结构
        print(f"\n🔍 详细检查紫薇和八字数据:")
        
        for person_key in ["person_a", "person_b"]:
            if person_key in compatibility_data:
                person_data = compatibility_data[person_key]
                print(f"\n{person_key} 详细结构:")
                
                # 查找紫薇数据
                ziwei_keys = [k for k in person_data.keys() if 'ziwei' in k.lower()]
                print(f"   紫薇相关键: {ziwei_keys}")
                
                # 查找八字数据
                bazi_keys = [k for k in person_data.keys() if 'bazi' in k.lower()]
                print(f"   八字相关键: {bazi_keys}")
                
                # 显示所有键的详细信息
                for key, value in person_data.items():
                    if isinstance(value, dict):
                        sub_keys = list(value.keys())[:5]  # 只显示前5个子键
                        print(f"   {key}: dict({len(value)} keys) - {sub_keys}...")
                    elif isinstance(value, list):
                        print(f"   {key}: list({len(value)} items)")
                    else:
                        print(f"   {key}: {type(value).__name__} = {str(value)[:50]}...")
        
        print(f"\n🎉 合盘数据结构调试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_compatibility_data_structure()
    if success:
        print("\n✅ 数据结构调试完成！")
        print("📋 请检查生成的JSON文件了解完整数据结构")
    else:
        print("\n❌ 调试失败！请检查错误信息。")
