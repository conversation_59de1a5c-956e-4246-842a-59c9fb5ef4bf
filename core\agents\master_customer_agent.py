#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主控沟通Agent - 正确的双Agent协作模式
负责：用户交互 → 信息收集 → 调用计算Agent → 解释结果
"""

import logging
import re
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_agent import BaseAgent, AgentMessage, AgentResponse, MessageType, agent_registry
from core.nlu.llm_client import LLMClient
from core.storage.calculation_cache import CalculationCache
from core.storage.calculation_cache import CalculationCache

logger = logging.getLogger(__name__)

class MasterCustomerAgent(BaseAgent):
    """主控沟通Agent - 负责整个对话流程的控制和管理"""

    def __init__(self, agent_id: str = "master_customer_001"):
        super().__init__(
            agent_id=agent_id,
            name="主控沟通专家",
            description="主控Agent，负责用户交互、信息收集、调用计算Agent和结果解释"
        )

        # 初始化LLM客户端和缓存管理器
        self.llm_client = LLMClient()
        self.cache = CalculationCache()

        # 对话状态管理
        self.conversation_states = {
            "greeting": "问候阶段",
            "info_collection": "信息收集阶段",
            "calculation": "计算处理阶段",
            "result_explanation": "结果解释阶段",
            "follow_up": "后续问答阶段"
        }

        # 🔧 修复：添加线程管理
        self.background_threads = {}

        # 算命类型映射
        self.calculation_types = {
            "ziwei": "紫薇斗数",
            "bazi": "八字算命",
            "liuyao": "六爻占卜"
        }

        logger.info(f"主控沟通专家 {self.agent_id} 初始化完成")

    async def process_message(self, message: AgentMessage) -> AgentResponse:
        """处理用户消息 - 主入口"""
        try:
            content = message.content
            user_message = content.get("user_message", "")
            session_id = content.get("session_id", "")

            # 获取会话状态
            session_state = self.get_session_state(session_id)
            logger.info(f"🔍 获取会话状态 session_id={session_id}: {session_state}")

            if not session_state:
                session_state = {
                    "stage": "greeting",
                    "birth_info": {},
                    "calculation_type": None,
                    "calculation_result": None,
                    "conversation_history": [],
                    "session_id": session_id
                }
                logger.info(f"🆕 创建新会话状态: {session_state}")

            # 添加用户消息到历史
            session_state["conversation_history"].append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })

            # 根据当前阶段处理消息
            response = await self._handle_conversation_flow(user_message, session_state)

            # 添加助手回复到历史
            session_state["conversation_history"].append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat()
            })

            # 更新会话状态
            logger.info(f"💾 保存会话状态 session_id={session_id}: stage={session_state.get('stage')}, birth_info={session_state.get('birth_info')}, result_id={session_state.get('result_id')}")
            self.set_session_state(session_id, session_state)

            return AgentResponse(
                success=True,
                data={
                    "response": response,
                    "stage": session_state["stage"],
                    "session_state": session_state
                }
            )

        except Exception as e:
            logger.error(f"主控Agent处理消息失败: {e}")
            return AgentResponse(
                success=False,
                data={"response": "抱歉，我现在无法处理您的请求，请稍后再试。"},
                error=str(e)
            )

    async def _handle_conversation_flow(self, user_message: str, session_state: Dict[str, Any]) -> str:
        """处理对话流程"""
        current_stage = session_state.get("stage", "greeting")

        # 分析用户意图
        intent = await self._analyze_user_intent(user_message, session_state)
        print(f"🎯 意图识别结果: {intent}")  # 强制输出调试信息

        if intent["type"] == "fortune_telling":
            # 算命请求流程
            return await self._handle_fortune_telling_flow(user_message, session_state, intent)
        elif intent["type"] == "question":
            # 问答流程
            return await self._handle_question_flow(user_message, session_state)
        else:
            # 一般聊天流程
            return await self._handle_chat_flow(user_message, session_state)

    async def _handle_fortune_telling_flow(self, user_message: str,
                                         session_state: Dict[str, Any],
                                         intent: Dict[str, Any]) -> str:
        """处理算命流程"""
        current_stage = session_state.get("stage", "greeting")

        # 提取生辰信息
        birth_info = self._extract_birth_info(user_message)
        logger.info(f"🔍 从用户消息提取到的信息: {birth_info}")

        # 合并到会话状态
        if not session_state.get("birth_info"):
            session_state["birth_info"] = {}

        # 记录合并前的状态
        logger.info(f"📝 合并前会话状态: {session_state.get('birth_info', {})}")

        session_state["birth_info"].update(birth_info)

        # 记录合并后的状态
        logger.info(f"✅ 合并后会话状态: {session_state['birth_info']}")

        # 检查信息完整性
        is_complete = self._is_birth_info_complete(session_state["birth_info"])
        logger.info(f"🎯 信息完整性检查: {is_complete}")

        # 设置算命类型
        if intent.get("calculation_type"):
            session_state["calculation_type"] = intent["calculation_type"]

        # 检查信息完整性
        if self._is_birth_info_complete(session_state["birth_info"]):
            # 信息完整，立即返回响应，后台异步分析
            session_state["stage"] = "analysis_started"

            calc_type = session_state.get("calculation_type", "ziwei")
            calc_name = self.calculation_types.get(calc_type, "算命")

            # 🚀 关键修复：使用独立线程进行真正的后台处理
            # 立即返回响应，不等待计算完成
            try:
                import threading

                # 使用独立线程进行后台计算，避免阻塞主事件循环
                def background_calculation():
                    """独立线程中的后台计算"""
                    try:
                        print("🔥 后台线程开始执行")  # 强制输出

                        # 在新的事件循环中运行异步计算
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        print("🔥 新事件循环已创建")  # 强制输出

                        # 运行后台分析
                        print("🔥 开始调用计算Agent")  # 强制输出
                        result = loop.run_until_complete(self._call_calculator_agent(session_state))
                        print(f"🔥 计算Agent调用完成: {result}")  # 强制输出

                        # 更新会话状态
                        session_id = session_state.get("session_id")
                        if session_id and result:
                            # 🔧 修复：使用线程安全的状态更新
                            try:
                                current_state = self.get_session_state(session_id) or session_state.copy()
                                if result.get("success"):
                                    # 🎯 关键修复：分析启动后立即设置result_id，不等待全部完成
                                    current_state["stage"] = "analysis_in_progress"  # 改为进行中状态
                                    current_state["calculation_result"] = result
                                    current_state["result_id"] = result.get("result_id", "")

                                    # 立即保存状态，让用户可以开始提问
                                    self.set_session_state(session_id, current_state)

                                    print(f"🎯 关键修复：result_id已设置: {result.get('result_id', '')[:8]}...")
                                    logger.info(f"✅ 后台分析已启动，result_id设置: {result.get('result_id', '')[:8]}...")
                                else:
                                    current_state["stage"] = "analysis_failed"
                                    current_state["error"] = result.get('error', '未知错误')
                                    logger.error(f"❌ 后台分析失败: {result.get('error', '未知错误')}")
                                    self.set_session_state(session_id, current_state)
                            except Exception as state_error:
                                logger.error(f"❌ 更新会话状态失败: {state_error}")

                        # 🔧 修复：安全关闭事件循环
                        try:
                            loop.close()
                        except Exception as loop_error:
                            logger.error(f"❌ 关闭事件循环失败: {loop_error}")

                    except Exception as e:
                        logger.error(f"❌ 后台线程计算异常: {e}")
                        # 更新状态为失败
                        session_id = session_state.get("session_id")
                        if session_id:
                            try:
                                current_state = self.get_session_state(session_id) or session_state.copy()
                                current_state["stage"] = "analysis_failed"
                                current_state["error"] = str(e)
                                self.set_session_state(session_id, current_state)
                            except Exception as state_error:
                                logger.error(f"❌ 更新失败状态异常: {state_error}")

                # 🔧 修复：启动后台线程并管理
                thread = threading.Thread(target=background_calculation, daemon=True)
                thread.start()

                # 管理线程
                session_id = session_state.get("session_id")
                if session_id:
                    self.background_threads[session_id] = thread

                print(f"🚀 后台计算线程已启动: {thread.name}")  # 强制输出到控制台
                logger.info(f"🚀 后台计算线程已启动: {thread.name}")

            except Exception as e:
                logger.error(f"❌ 启动后台线程失败: {e}")
                # 即使启动失败也继续，用户可以手动触发分析

            # 立即返回响应，让用户开始互动
            birth_info = session_state['birth_info']
            response = f"好，开始给你算{calc_name}。\n\n"
            response += f"你的信息：{birth_info['year']}年{birth_info['month']}月{birth_info['day']}日 {birth_info['hour']}，{birth_info['gender']}命\n\n"
            response += f"我正在算，你现在就可以问我任何问题，比如财运、感情、事业什么的。"

            return response
        else:
            # 信息不完整，继续收集
            session_state["stage"] = "info_collection"
            return await self._generate_info_collection_response(session_state)

    async def _start_background_analysis_async(self, session_state: Dict[str, Any]):
        """真正的异步后台分析（fire-and-forget模式）"""
        try:
            logger.info("🚀 启动真正的后台异步分析")

            # 获取session_id用于后续状态更新
            session_id = session_state.get("session_id")
            if not session_id:
                logger.error("❌ 缺少session_id，无法进行后台分析")
                return

            # 调用计算Agent进行后台分析
            calculation_result = await self._call_calculator_agent(session_state)

            # 重新获取最新的会话状态（因为可能被其他操作修改）
            current_session_state = self.get_session_state(session_id)
            if not current_session_state:
                logger.error(f"❌ 无法获取会话状态: {session_id}")
                return

            if calculation_result and calculation_result.get("success"):
                # 分析成功，更新会话状态
                current_session_state["stage"] = "analysis_completed"
                current_session_state["calculation_result"] = calculation_result

                result_id = calculation_result.get("result_id", "")
                current_session_state["result_id"] = result_id

                # 保存更新后的状态
                self.set_session_state(session_id, current_session_state)

                logger.info(f"✅ 后台分析完成: {result_id[:8]}...")
            else:
                # 分析失败
                current_session_state["stage"] = "analysis_failed"
                error_msg = calculation_result.get("error", "后台分析失败") if calculation_result else "无法连接到计算服务"

                # 保存更新后的状态
                self.set_session_state(session_id, current_session_state)

                logger.error(f"❌ 后台分析失败: {error_msg}")

        except Exception as e:
            logger.error(f"❌ 后台分析异常: {e}")
            # 尝试更新状态为失败
            try:
                session_id = session_state.get("session_id")
                if session_id:
                    current_session_state = self.get_session_state(session_id)
                    if current_session_state:
                        current_session_state["stage"] = "analysis_failed"
                        self.set_session_state(session_id, current_session_state)
            except:
                pass

    async def _call_calculator_agent(self, session_state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用计算Agent进行算命计算"""
        try:
            logger.info("🧮 主控Agent开始调用计算Agent")

            # 获取计算Agent
            calculator_agent = self._get_calculator_agent()
            if not calculator_agent:
                logger.error("❌ 计算Agent不可用")
                return {"success": False, "error": "计算Agent不可用"}

            logger.info(f"✅ 找到计算Agent: {calculator_agent.agent_id}")

            # 构建计算请求
            calculation_request = AgentMessage(
                message_id=f"calc_{datetime.now().timestamp()}",
                message_type=MessageType.CALCULATION_REQUEST,
                sender_id=self.agent_id,
                receiver_id=calculator_agent.agent_id,
                content={
                    "user_message": f"计算请求：{session_state['birth_info']}",
                    "birth_info": session_state["birth_info"],
                    "calculation_type": session_state.get("calculation_type", "ziwei"),
                    "session_id": session_state.get("session_id", "")
                },
                timestamp=datetime.now().isoformat()
            )

            logger.info(f"📤 发送计算请求到计算Agent")
            logger.info(f"   请求内容: {calculation_request.content}")

            # 发送请求给计算Agent
            response = await calculator_agent.process_message(calculation_request)

            logger.info(f"📥 收到计算Agent响应: success={response.success}")

            if response.success:
                logger.info("✅ 计算Agent处理成功")

                # 保存结果ID到会话状态，便于后续查询
                result_id = response.data.get("result_id")
                if result_id:
                    session_state["result_id"] = result_id

                return {
                    "success": True,
                    "result_id": result_id,
                    "summary": response.data.get("summary", ""),
                    "from_cache": response.data.get("from_cache", False),
                    "calculation_type": session_state.get("calculation_type", "ziwei")
                }
            else:
                logger.error(f"❌ 计算Agent处理失败: {response.error}")
                return {
                    "success": False,
                    "error": response.error or "计算Agent处理失败"
                }

        except Exception as e:
            logger.error(f"❌ 调用计算Agent异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": f"调用计算Agent异常: {str(e)}"
            }

    async def _handle_question_flow(self, user_message: str, session_state: Dict[str, Any]) -> str:
        """处理问答流程 - 严格基于后台计算结果"""

        # 检查是否是算命相关问题
        fortune_keywords = ["财运", "事业", "感情", "婚姻", "运势", "命运", "性格", "健康", "学业", "工作", "桃花", "贵人", "命宫", "命格", "特点"]
        message_lower = user_message.lower()
        is_fortune_question = any(keyword in message_lower for keyword in fortune_keywords)

        if is_fortune_question:
            # 算命相关问题必须基于后台计算结果
            if session_state.get("result_id"):
                # 有计算结果，基于真实分析回答
                return await self._answer_based_on_calculation(user_message, session_state)
            elif session_state.get("stage") in ["analysis_started", "analysis_in_progress"]:
                # 🎯 关键修复：分析进行中时，尝试基于已完成的角度回答
                result_id = session_state.get("result_id")
                if result_id:
                    # 有result_id，尝试基于已完成的分析回答
                    return await self._answer_based_on_calculation(user_message, session_state)
                else:
                    # 没有result_id，给出进度反馈
                    return f"我还在算呢，你等等。算好了再告诉你{user_message}的情况。"
            else:
                # 没有计算结果，拒绝回答
                return "你还没告诉我生辰八字呢，我怎么算？先把你的出生年月日时和性别告诉我。"
        else:
            # 非算命问题，一般问答
            return await self._generate_general_answer(user_message)

    async def _answer_based_on_calculation(self, user_message: str, session_state: Dict[str, Any]) -> str:
        """基于算命结果回答问题 - 智能查询缓存详细分析，支持渐进式生成"""
        try:
            # 获取结果ID
            result_id = session_state.get("result_id")
            if not result_id:
                return "抱歉，我需要先为您进行算命分析才能回答相关问题。"

            # 分析用户问题类型，确定需要查询的详细章节
            question_type = self._analyze_question_type(user_message)

            # 检查对应角度是否已生成
            angle_available = await self._check_angle_availability(result_id, question_type)

            if not angle_available:
                # 如果对应角度未生成，先触发生成，然后自然回答
                generation_result = await self._trigger_angle_generation(result_id, question_type)
                if generation_result.get("success"):
                    # 不提示"刚刚完成"，直接基于分析回答
                    return await self._get_angle_based_answer(result_id, question_type, user_message, session_state)
                else:
                    # 不说"正在生成"，而是基于已有信息给出概括性回答
                    return await self._get_general_fortune_answer(user_message, session_state)

            # 角度已存在，直接基于详细分析回答
            return await self._get_angle_based_answer(result_id, question_type, user_message, session_state)

        except Exception as e:
            logger.error(f"基于计算结果回答失败: {e}")
            return "根据您的命盘分析，这个问题需要结合具体情况来看。"

    async def _check_angle_availability(self, result_id: str, question_type: str) -> bool:
        """检查对应角度是否已生成"""
        try:
            from core.agents.base_agent import agent_registry
            calculator_agent = None
            for agent in agent_registry.agents.values():
                if agent.__class__.__name__ == "FortuneCalculatorAgent":
                    calculator_agent = agent
                    break
            if not calculator_agent:
                return False

            # 使用新的角度查询方法
            angle_content = calculator_agent.get_specific_angle(result_id, question_type)
            return angle_content is not None and len(angle_content) > 100

        except Exception as e:
            logger.error(f"检查角度可用性失败: {e}")
            return False

    def get_analysis_progress(self, result_id: str) -> Dict[str, Any]:
        """获取分析进度"""
        try:
            from core.agents.base_agent import agent_registry
            calculator_agent = None
            for agent in agent_registry.agents.values():
                if agent.__class__.__name__ == "FortuneCalculatorAgent":
                    calculator_agent = agent
                    break

            if not calculator_agent:
                return {"completed_angles": 0, "total_angles": 12}

            return calculator_agent.get_angle_progress(result_id)

        except Exception as e:
            logger.error(f"获取分析进度失败: {e}")
            return {"completed_angles": 0, "total_angles": 12}

    async def _trigger_angle_generation(self, result_id: str, question_type: str) -> Dict[str, Any]:
        """触发特定角度的生成"""
        try:
            from core.agents.base_agent import agent_registry
            calculator_agent = None
            for agent in agent_registry.agents.values():
                if agent.__class__.__name__ == "FortuneCalculatorAgent":
                    calculator_agent = agent
                    break

            if not calculator_agent:
                return {"success": False, "error": "未找到计算Agent"}

            # 调用继续角度分析方法
            result = await calculator_agent.continue_angle_analysis(result_id, question_type)
            return result

        except Exception as e:
            logger.error(f"触发角度生成失败: {e}")
            return {"success": False, "error": str(e)}

    async def _get_angle_based_answer(self, result_id: str, question_type: str,
                                     user_message: str, session_state: Dict[str, Any]) -> str:
        """基于特定角度的详细分析回答问题"""
        try:
            # 从缓存获取相关的详细分析
            detailed_content = await self._get_relevant_analysis(result_id, question_type)

            birth_info = session_state.get("birth_info", {})

            prompt = f"""
你是一个算命师，用户问：{user_message}

用户信息：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}，{birth_info.get('gender')}命

详细分析：
{detailed_content}

请用自然的口语回答用户问题。要求：
1. 像朋友聊天一样自然
2. 不要用"您"，用"你"
3. 不要用emoji和特殊符号
4. 不要重复介绍用户的出生信息
5. 直接回答问题，不要啰嗦的开头
6. 语言通俗易懂，300-500字

回答："""

            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=800)

            # 🔧 修复：在回答后添加排盘图片（如果有的话）
            if response:
                # 检查是否有排盘图片
                cached_result = self.cache.get_result(session_state.get("result_id", ""))
                if cached_result and cached_result.chart_image_path:
                    response += f"\n\n📊 **排盘图片**\n图片已生成: {cached_result.chart_image_path}"

                response += "\n\n还有什么想问的吗？"

            return response.strip() if response else "这个问题比较复杂，需要结合你的具体情况。"

        except Exception as e:
            logger.error(f"基于角度回答失败: {e}")
            return "这个问题比较复杂，需要结合你的具体情况。"

    def _analyze_question_type(self, user_message: str) -> str:
        """分析问题类型，确定需要查询的分析章节"""
        message_lower = user_message.lower()

        # 🔧 修复：使用与后台Agent一致的角度映射

        # 性格相关（包括命宫）
        if any(keyword in message_lower for keyword in ["性格", "特点", "个性", "脾气", "命宫", "命格", "天性", "本性"]):
            return "personality_destiny"  # 🔧 修复：与后台一致

        # 财运相关
        elif any(keyword in message_lower for keyword in ["财运", "财富", "赚钱", "投资", "理财", "收入"]):
            return "wealth_fortune"  # 🔧 修复：与后台一致

        # 感情婚姻相关
        elif any(keyword in message_lower for keyword in ["感情", "爱情", "婚姻", "恋爱", "桃花", "配偶", "结婚", "对象"]):
            return "marriage_love"  # 🔧 修复：与后台一致

        # 健康相关
        elif any(keyword in message_lower for keyword in ["健康", "身体", "疾病", "养生"]):
            return "health_wellness"  # 🔧 修复：与后台一致

        # 事业相关
        elif any(keyword in message_lower for keyword in ["事业", "工作", "职业", "升职", "跳槽", "创业"]):
            return "career_achievement"  # 🔧 修复：与后台一致

        # 子女相关
        elif any(keyword in message_lower for keyword in ["子女", "孩子", "生育", "怀孕", "小孩"]):
            return "children_creativity"  # 🔧 修复：与后台一致

        # 人际关系
        elif any(keyword in message_lower for keyword in ["人际", "朋友", "同事", "关系", "贵人"]):
            return "interpersonal_relationship"  # 🔧 修复：与后台一致

        # 学业相关
        elif any(keyword in message_lower for keyword in ["学业", "学习", "考试", "教育", "读书"]):
            return "education_learning"  # 🔧 修复：与后台一致

        # 家庭相关
        elif any(keyword in message_lower for keyword in ["家庭", "父母", "家人"]):
            return "family_environment"  # 🔧 修复：与后台一致

        # 迁移相关
        elif any(keyword in message_lower for keyword in ["搬家", "迁移", "出国", "旅行", "外地"]):
            return "travel_relocation"  # 🔧 修复：与后台一致

        # 精神相关
        elif any(keyword in message_lower for keyword in ["运势", "运气", "今年", "明年", "流年", "福气", "精神"]):
            return "spiritual_blessing"  # 🔧 修复：与后台一致

        # 权威相关
        elif any(keyword in message_lower for keyword in ["领导", "权威", "管理", "地位"]):
            return "authority_parents"  # 🔧 修复：与后台一致

        # 默认返回命宫分析
        else:
            return "personality_destiny"  # 🔧 修复：默认返回命宫

    async def _get_relevant_analysis(self, result_id: str, question_type: str) -> str:
        """获取相关的详细分析内容"""
        try:
            from core.agents.base_agent import agent_registry
            calculator_agent = None
            for agent in agent_registry.agents.values():
                if agent.__class__.__name__ == "FortuneCalculatorAgent":
                    calculator_agent = agent
                    break

            if not calculator_agent:
                return "详细分析暂时无法获取。"

            # 直接获取特定角度的分析
            angle_content = calculator_agent.get_specific_angle(result_id, question_type)

            if angle_content and len(angle_content) > 100:
                return angle_content
            else:
                # 如果特定角度不可用，检查进度并提供反馈
                progress = calculator_agent.get_angle_progress(result_id)
                completed = progress.get("completed_angles", 0)
                total = progress.get("total_angles", 12)

                angle_names = {
                    "personality_destiny": "命宫分析",
                    "wealth_fortune": "财富分析",
                    "marriage_love": "婚姻分析",
                    "health_wellness": "健康分析",
                    "career_achievement": "事业分析",
                    "children_creativity": "子女分析"
                }

                angle_name = angle_names.get(question_type, "相关分析")
                return f"您的{angle_name}正在生成中，当前已完成 {completed}/{total} 个角度的分析。请稍等片刻再询问这个问题，或者先问问其他已完成的方面。"

        except Exception as e:
            logger.error(f"获取相关分析失败: {e}")
            return "详细分析暂时无法获取。"

    async def _generate_general_answer(self, user_message: str) -> str:
        """生成一般问答回复 - 严格禁止算命分析"""

        # 检查是否是算命相关问题
        fortune_keywords = ["财运", "事业", "感情", "婚姻", "运势", "命运", "性格", "健康", "学业", "工作"]
        message_lower = user_message.lower()

        if any(keyword in message_lower for keyword in fortune_keywords):
            # 如果是算命相关问题，严格拒绝自己分析
            return "这个得算过才知道。先告诉我你的生辰八字：年月日时和性别。"

        # 对于非算命问题，提供一般性回复
        prompt = f"""
用户问："{user_message}"

你是算命师，简单回答这个问题。要求：
1. 不要算命
2. 用"你"不用"您"
3. 不要emoji
4. 简单直接

回答："""

        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=200)
            return response.strip() if response else "有什么想算的吗？告诉我你的生辰八字。"
        except Exception as e:
            logger.error(f"生成一般回答失败: {e}")
            return "有什么想算的吗？告诉我你的生辰八字。"

    async def _handle_chat_flow(self, user_message: str, session_state: Dict[str, Any]) -> str:
        """处理聊天流程"""
        # 生成自然对话回复
        prompt = f"""
你是一个算命师，用户说："{user_message}"

请用自然的口语回复。要求：
1. 像朋友聊天一样
2. 不要用"您"，用"你"
3. 不要用emoji
4. 简单直接

回复："""

        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=200)
            return response.strip() if response else "你好！我是算命师，有什么想算的吗？"
        except Exception as e:
            logger.error(f"生成聊天回复失败: {e}")
            return "您好！我是您的专属算命师，很高兴为您服务。请问您想了解什么呢？"

    async def _analyze_user_intent(self, user_message: str, session_state: Dict[str, Any]) -> Dict[str, Any]:
        """分析用户意图"""
        message_lower = user_message.lower()

        # 🔧 修复：如果已有生辰信息，优先识别为问答而不是重新算命
        has_birth_info_in_session = session_state.get("birth_info") and self._is_birth_info_complete(session_state["birth_info"])

        # 检查算命意图（包括提供生辰信息）
        fortune_keywords = ["算命", "紫薇", "斗数", "八字", "六爻", "占卜", "命理"]
        birth_info_keywords = ["出生", "生于", "年", "月", "日", "时", "男命", "女命", "男", "女"]

        has_fortune_keyword = any(keyword in message_lower for keyword in fortune_keywords)
        has_birth_info = any(keyword in message_lower for keyword in birth_info_keywords)

        # 🔧 修复：如果会话中已有完整生辰信息，且用户问的是运势相关问题，识别为问答
        if has_birth_info_in_session:
            question_indicators = ["如何", "怎么样", "怎样", "什么", "?", "？"]
            fortune_question_keywords = ["财运", "事业", "感情", "婚姻", "运势", "健康", "学业", "工作", "桃花", "贵人", "命宫", "命格", "特点"]

            is_question_format = any(indicator in message_lower for indicator in question_indicators)
            is_fortune_topic = any(keyword in message_lower for keyword in fortune_question_keywords)

            if is_question_format or is_fortune_topic:
                return {
                    "type": "question",
                    "confidence": 0.9
                }

        if has_fortune_keyword or has_birth_info:
            # 确定算命类型
            calculation_type = "ziwei"  # 默认
            if "八字" in user_message:
                calculation_type = "bazi"
            elif "六爻" in user_message or "占卜" in user_message:
                calculation_type = "liuyao"
            elif "紫薇" in user_message or "斗数" in user_message:
                calculation_type = "ziwei"

            return {
                "type": "fortune_telling",
                "calculation_type": calculation_type,
                "confidence": 0.9
            }

        # 🔧 修复：移除重复的问答意图识别逻辑（已在上面处理）
        # 检查一般问答意图
        question_keywords = ["什么", "怎么", "如何", "为什么", "哪里", "谁", "何时"]
        is_question = any(keyword in message_lower for keyword in question_keywords) or "?" in user_message

        if is_question:
            return {
                "type": "question",
                "confidence": 0.8
            }

        # 默认为聊天
        return {
            "type": "chat",
            "confidence": 0.7
        }

    def _extract_birth_info(self, text: str) -> Dict[str, Any]:
        """用LLM智能提取生辰信息"""
        try:
            prompt = f"""
从用户消息中智能提取生辰信息。

用户消息："{text}"

请分析并提取以下信息，返回JSON格式：
{{
    "year": "年份(4位数字)",
    "month": "月份(1-12)",
    "day": "日期(1-31)",
    "hour": "时辰(子时/丑时/寅时等或具体时间)",
    "gender": "性别(男/女)"
}}

提取规则：
1. 年份：识别4位数字年份
2. 月份：识别月份数字
3. 日期：识别日期数字
4. 时辰：识别传统时辰或现代时间表达
5. 性别：识别性别信息
6. 如果某项信息不存在，该字段返回null

只返回JSON，不要其他内容："""

            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=200)

            if response:
                import json
                try:
                    # 清理LLM返回的格式（去除markdown标记）
                    cleaned_response = response.strip()
                    if cleaned_response.startswith("```json"):
                        cleaned_response = cleaned_response[7:]  # 去除```json
                    if cleaned_response.endswith("```"):
                        cleaned_response = cleaned_response[:-3]  # 去除```
                    cleaned_response = cleaned_response.strip()

                    logger.info(f"🧠 LLM原始返回: {response}")
                    logger.info(f"🔧 清理后JSON: {cleaned_response}")

                    # 尝试解析JSON
                    birth_info = json.loads(cleaned_response)

                    # 清理null值
                    cleaned_info = {}
                    for key, value in birth_info.items():
                        if value and value != "null" and value != None:
                            cleaned_info[key] = str(value)

                    logger.info(f"✅ LLM智能提取生辰信息: {cleaned_info}")
                    return cleaned_info

                except json.JSONDecodeError as e:
                    logger.warning(f"❌ LLM返回JSON解析失败: {e}")
                    logger.warning(f"原始返回: {response}")
                    return self._fallback_extract_birth_info(text)
            else:
                logger.warning("LLM提取失败，使用备用提取")
                return self._fallback_extract_birth_info(text)

        except Exception as e:
            logger.error(f"LLM提取生辰信息异常: {e}")
            return self._fallback_extract_birth_info(text)

    def _fallback_extract_birth_info(self, text: str) -> Dict[str, Any]:
        """备用的正则表达式提取（仅在LLM失败时使用）"""
        birth_info = {}

        # 简化的正则提取作为备用
        import re

        # 提取年份
        year_match = re.search(r'(\d{4})', text)
        if year_match:
            birth_info["year"] = year_match.group(1)

        # 提取月份
        month_match = re.search(r'(\d{1,2})[月]', text)
        if month_match:
            birth_info["month"] = month_match.group(1)

        # 提取日期
        day_match = re.search(r'(\d{1,2})[日号]', text)
        if day_match:
            birth_info["day"] = day_match.group(1)

        # 提取时辰
        time_match = re.search(r'(子时|丑时|寅时|卯时|辰时|巳时|午时|未时|申时|酉时|戌时|亥时)', text)
        if time_match:
            birth_info["hour"] = time_match.group(1)

        # 提取性别
        if "男" in text and "女" not in text:
            birth_info["gender"] = "男"
        elif "女" in text and "男" not in text:
            birth_info["gender"] = "女"

        logger.info(f"📝 备用正则提取生辰信息: {birth_info}")
        return birth_info

    def _is_birth_info_complete(self, birth_info: Dict[str, Any]) -> bool:
        """检查生辰信息是否完整"""
        required_fields = ["year", "month", "day", "hour", "gender"]

        # 详细检查每个字段
        missing_fields = []
        for field in required_fields:
            if field not in birth_info or not birth_info[field]:
                missing_fields.append(field)

        is_complete = len(missing_fields) == 0

        print(f"🔍 信息完整性检查:")  # 强制输出
        print(f"   生辰信息: {birth_info}")
        print(f"   缺失字段: {missing_fields}")
        print(f"   是否完整: {is_complete}")

        logger.info(f"🔍 信息完整性检查: birth_info={birth_info}, missing={missing_fields}, complete={is_complete}")

        return is_complete

    async def _generate_info_collection_response(self, session_state: Dict[str, Any]) -> str:
        """生成信息收集回复"""
        birth_info = session_state.get("birth_info", {})
        calculation_type = session_state.get("calculation_type", "ziwei")

        # 显示已收集到的信息
        collected_info = []
        missing_info = []

        if birth_info.get("year"):
            collected_info.append(f"出生年份：{birth_info['year']}")
        else:
            missing_info.append("出生年份")

        if birth_info.get("month"):
            collected_info.append(f"出生月份：{birth_info['month']}")
        else:
            missing_info.append("出生月份")

        if birth_info.get("day"):
            collected_info.append(f"出生日期：{birth_info['day']}")
        else:
            missing_info.append("出生日期")

        if birth_info.get("hour"):
            collected_info.append(f"出生时辰：{birth_info['hour']}")
        else:
            missing_info.append("出生时辰")

        if birth_info.get("gender"):
            collected_info.append(f"性别：{birth_info['gender']}")
        else:
            missing_info.append("性别")

        calc_name = self.calculation_types.get(calculation_type, "算命")

        response = f"好，给你算{calc_name}。\n\n"

        # 显示已收集的信息
        if collected_info:
            response += f"已经知道：\n"
            for info in collected_info:
                response += f"• {info}\n"
            response += "\n"

        # 显示还需要的信息
        if missing_info:
            response += f"还需要：{', '.join(missing_info)}\n\n"
            response += "告诉我剩下的信息，比如：1988年6月1日 午时 男"
        else:
            response = f"信息齐了！开始给你算{calc_name}。"

        return response

    async def _generate_result_explanation(self, calculation_data: Dict[str, Any],
                                         session_state: Dict[str, Any]) -> str:
        """生成算命结果解释"""
        birth_info = session_state.get("birth_info", {})
        calculation_type = session_state.get("calculation_type", "ziwei")

        prompt = f"""
作为专业算命师，请根据以下{self.calculation_types.get(calculation_type)}计算结果为用户提供详细解读：

用户信息：
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}
- 性别：{birth_info.get('gender')}

计算结果：{calculation_data}

请提供专业而通俗的解读，包括：
1. 性格特点分析
2. 事业运势
3. 财运状况
4. 感情婚姻
5. 实用建议

要求：语言亲切专业，内容积极正面，篇幅800-1200字。

解读："""

        try:
            messages = [{"role": "user", "content": prompt}]
            response = self.llm_client.chat_completion(messages, max_tokens=1500)
            return response.strip() if response else "您的算命分析已经完成，显示您有很好的潜质，建议保持积极心态。"
        except Exception as e:
            logger.error(f"生成结果解释失败: {e}")
            return "您的算命分析已经完成，显示您有很好的潜质，建议保持积极心态。"

    def _get_calculator_agent(self):
        """获取计算Agent"""
        agents = agent_registry.get_agents_by_type("FortuneCalculatorAgent")
        return agents[0] if agents else None

    async def get_calculation_status(self, session_id: str) -> Dict[str, Any]:
        """获取计算状态"""
        session_state = self.get_session_state(session_id)
        if not session_state:
            return {"status": "no_session"}

        stage = session_state.get("stage", "greeting")

        if stage == "calculation":
            return {"status": "calculating", "message": "正在计算中..."}
        elif stage == "result_explanation":
            return {
                "status": "completed",
                "message": "计算完成",
                "result": session_state.get("calculation_result")
            }
        else:
            return {"status": stage, "message": f"当前阶段: {stage}"}
