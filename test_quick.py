#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的逻辑
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_quick():
    print('🧪 快速测试修复后的逻辑...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = 'test_session_002'
        
        # 第一步：提供完整信息
        print('\n📝 第一步：提供完整生辰信息')
        result1 = await coordinator.handle_user_message(
            session_id, 
            '我是1988年6月1日午时出生的男命，想算命'
        )
        
        print(f'✅ 第一步响应: {result1.get("success")}')
        print(f'阶段: {result1.get("stage")}')
        
        # 立即第二步：询问财运（不等待）
        print('\n💬 第二步：立即询问财运（后台还在分析中）')
        result2 = await coordinator.handle_user_message(
            session_id, 
            '我想看看财运'
        )
        
        print(f'✅ 第二步响应: {result2.get("success")}')
        print(f'响应内容: {result2.get("response", "")[:100]}...')
        
        # 检查会话状态
        session_state = master_agent.get_session_state(session_id)
        print(f'\n📊 会话状态:')
        print(f'  阶段: {session_state.get("stage")}')
        print(f'  结果ID: {session_state.get("result_id")}')
        
        # 等待一段时间，看后台是否完成
        print('\n⏳ 等待10秒，看后台分析是否完成...')
        time.sleep(10)
        
        # 再次检查状态
        session_state = master_agent.get_session_state(session_id)
        print(f'\n📊 10秒后的会话状态:')
        print(f'  阶段: {session_state.get("stage")}')
        print(f'  结果ID: {session_state.get("result_id")}')
        
        # 如果有结果ID，再次询问财运
        if session_state.get("result_id"):
            print('\n💰 第三步：后台分析完成后再次询问财运')
            result3 = await coordinator.handle_user_message(
                session_id, 
                '现在我的财运分析完成了吗？'
            )
            print(f'✅ 第三步响应: {result3.get("success")}')
            print(f'响应内容: {result3.get("response", "")[:200]}...')
        else:
            print('\n❌ 后台分析还未完成，result_id仍为空')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_quick())
