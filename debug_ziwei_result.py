#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试紫薇斗数返回结果
"""

def debug_ziwei_result():
    """调试紫薇斗数返回结果"""
    print("🔍 调试紫薇斗数返回结果")
    print("=" * 50)
    
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms'))
        from real_ziwei_calculator import RealZiweiCalculator
        
        calc = RealZiweiCalculator()
        
        # 测试数据
        result = calc.calculate_chart(1988, 6, 1, 11, "男")
        
        print(f"📊 返回结果详情:")
        print(f"  类型: {type(result)}")
        print(f"  键: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
        
        if isinstance(result, dict):
            for key, value in result.items():
                print(f"\n🔹 {key}:")
                if isinstance(value, dict):
                    print(f"    类型: dict, 键: {list(value.keys())}")
                    # 显示部分内容
                    for subkey, subvalue in list(value.items())[:3]:
                        print(f"      {subkey}: {str(subvalue)[:100]}...")
                elif isinstance(value, list):
                    print(f"    类型: list, 长度: {len(value)}")
                    if value:
                        print(f"      首项: {str(value[0])[:100]}...")
                else:
                    print(f"    类型: {type(value)}, 值: {str(value)[:100]}...")
        
        # 检查是否有错误信息
        if isinstance(result, dict):
            if 'error' in result:
                print(f"\n❌ 发现错误: {result['error']}")
            elif 'success' in result:
                print(f"\n✅ 成功标志: {result['success']}")
            else:
                print(f"\n⚠️ 没有success字段，但有数据")
                
                # 检查是否有有效数据
                has_data = False
                if 'palaces' in result and result['palaces']:
                    has_data = True
                    print(f"    ✅ 有宫位数据: {len(result['palaces'])}个")
                
                if 'birth_info' in result and result['birth_info']:
                    has_data = True
                    print(f"    ✅ 有出生信息")
                
                if 'zodiac' in result and result['zodiac']:
                    has_data = True
                    print(f"    ✅ 有生肖: {result['zodiac']}")
                
                if has_data:
                    print(f"    🎯 结论: 计算成功，只是缺少success字段")
                else:
                    print(f"    ❌ 结论: 没有有效数据")
        
        return result
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def fix_ziwei_result_format():
    """修复紫薇斗数结果格式"""
    print(f"\n🔧 修复紫薇斗数结果格式")
    print("=" * 50)
    
    # 读取当前算法文件
    try:
        ziwei_file = os.path.join("algorithms", "real_ziwei_calculator.py")
        with open(ziwei_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📊 当前算法文件内容分析:")
        
        # 查找calculate_chart方法
        lines = content.splitlines()
        in_calculate_chart = False
        method_lines = []
        
        for i, line in enumerate(lines):
            if "def calculate_chart" in line:
                in_calculate_chart = True
                print(f"  找到calculate_chart方法在第{i+1}行")
            
            if in_calculate_chart:
                method_lines.append((i+1, line))
                
                # 查找return语句
                if line.strip().startswith("return") and "result" in line:
                    print(f"  找到return语句在第{i+1}行: {line.strip()}")
                
                # 方法结束
                if line.strip() and not line.startswith(' ') and not line.startswith('\t') and i > 0:
                    if not "def calculate_chart" in line:
                        break
        
        print(f"\n📋 calculate_chart方法内容:")
        for line_num, line in method_lines[-10:]:  # 显示最后10行
            print(f"  {line_num}: {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 紫薇斗数返回结果调试")
    print("=" * 80)
    
    # 调试返回结果
    result = debug_ziwei_result()
    
    # 分析算法文件
    fix_ziwei_result_format()
    
    print("\n" + "=" * 80)
    print("🎯 问题分析:")
    
    if result:
        if isinstance(result, dict) and ('palaces' in result or 'birth_info' in result):
            print("✅ 紫薇斗数计算实际上是成功的")
            print("⚠️ 问题：缺少success字段，导致融合系统误判为失败")
            print("💡 解决方案：修改融合系统的判断逻辑")
        else:
            print("❌ 紫薇斗数计算确实失败")
    else:
        print("❌ 无法获取紫薇斗数结果")

if __name__ == "__main__":
    import os
    main()
