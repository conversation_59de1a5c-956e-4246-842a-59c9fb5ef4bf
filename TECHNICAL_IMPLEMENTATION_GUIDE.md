# 🔧 技术实施指导文档

## 🎯 **实施策略**

### 核心原则
1. **渐进式重构** - 不影响现有功能，逐步迁移
2. **保留可用代码** - 最大化利用现有代码资源
3. **模块化设计** - 每个组件独立，便于测试和维护
4. **向后兼容** - 确保现有接口继续工作

### 实施顺序
```
阶段1: 基础架构 → 阶段2: 功能迁移 → 阶段3: 增强功能 → 阶段4: 优化完善
```

## 🏗️ **阶段1：基础架构搭建**

### 1.1 创建新目录结构
```bash
# 创建核心目录
mkdir -p core/chat core/nlu core/tools
mkdir -p interfaces config/prompts utils

# 创建初始化文件
touch core/__init__.py core/chat/__init__.py core/nlu/__init__.py core/tools/__init__.py
touch interfaces/__init__.py config/__init__.py utils/__init__.py
```

### 1.2 实现聊天引擎基础框架

#### 会话管理器 (Session Manager)
```python
# core/chat/session_manager.py
class SessionManager:
    """会话管理 - 管理用户会话状态"""
    
    def __init__(self):
        self.sessions = {}  # 内存存储，后期可扩展为Redis
    
    def get_session(self, session_id: str) -> dict:
        """获取会话信息"""
        if session_id not in self.sessions:
            self.sessions[session_id] = {
                "id": session_id,
                "created_at": datetime.now(),
                "context": {},
                "history": []
            }
        return self.sessions[session_id]
    
    def update_session(self, session_id: str, context: dict, message: dict):
        """更新会话信息"""
        session = self.get_session(session_id)
        session["context"].update(context)
        session["history"].append(message)
        # 保持历史记录在合理范围内
        if len(session["history"]) > 20:
            session["history"] = session["history"][-20:]
```

#### 对话引擎 (Conversation Engine)
```python
# core/chat/conversation_engine.py
class ConversationEngine:
    """对话引擎 - 核心聊天处理逻辑"""
    
    def __init__(self, session_manager, intent_recognizer, tool_registry):
        self.session_manager = session_manager
        self.intent_recognizer = intent_recognizer
        self.tool_registry = tool_registry
    
    def process_message(self, session_id: str, message: str) -> dict:
        """处理用户消息的完整流程"""
        # 1. 获取会话上下文
        session = self.session_manager.get_session(session_id)
        
        # 2. 语义理解
        intent = self.intent_recognizer.recognize_intent(message, session["context"])
        
        # 3. 选择并调用工具
        tool = self.tool_registry.get_tool(intent["tool_name"])
        if tool and tool.can_handle(intent):
            result = tool.execute(intent, session["context"])
        else:
            result = {"error": "无法处理该请求"}
        
        # 4. 更新会话
        message_record = {
            "timestamp": datetime.now(),
            "user_message": message,
            "intent": intent,
            "response": result
        }
        self.session_manager.update_session(session_id, intent.get("context", {}), message_record)
        
        return result
```

### 1.3 实现语义理解基础框架

#### 意图识别器 (Intent Recognizer)
```python
# core/nlu/intent_recognizer.py
class IntentRecognizer:
    """意图识别 - 基于LLM的智能语义理解"""
    
    def __init__(self, llm_client, prompt_manager):
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
    
    def recognize_intent(self, message: str, context: dict) -> dict:
        """识别用户意图"""
        # 构建上下文感知的提示词
        prompt = self.prompt_manager.get_intent_recognition_prompt(message, context)
        
        # 调用LLM进行意图识别
        response = self.llm_client.chat(prompt)
        
        # 解析LLM响应
        intent = self._parse_intent_response(response)
        
        return intent
    
    def _parse_intent_response(self, response: str) -> dict:
        """解析LLM的意图识别响应"""
        # 智能解析JSON或自然语言响应
        # 返回标准化的意图结构
        pass
```

### 1.4 实现工具层基础框架

#### 工具基类 (Base Tool)
```python
# core/tools/base_tool.py
from abc import ABC, abstractmethod

class BaseTool(ABC):
    """工具基类 - 所有功能工具的基础"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    @abstractmethod
    def can_handle(self, intent: dict) -> bool:
        """判断是否能处理该意图"""
        pass
    
    @abstractmethod
    def execute(self, intent: dict, context: dict) -> dict:
        """执行工具功能"""
        pass
    
    def get_prompt_template(self, task_type: str) -> str:
        """获取工具专用的提示词模板"""
        pass
```

#### 工具注册中心 (Tool Registry)
```python
# core/tools/tool_registry.py
class ToolRegistry:
    """工具注册中心 - 管理所有可用工具"""
    
    def __init__(self):
        self.tools = {}
    
    def register_tool(self, tool: BaseTool):
        """注册工具"""
        self.tools[tool.name] = tool
    
    def get_tool(self, tool_name: str) -> BaseTool:
        """获取工具"""
        return self.tools.get(tool_name)
    
    def list_tools(self) -> list:
        """列出所有工具"""
        return list(self.tools.keys())
```

## 🔄 **阶段2：功能迁移**

### 2.1 迁移策略
1. **包装现有功能** - 将现有FortuneEngine包装为工具
2. **保持接口兼容** - 现有API继续工作
3. **逐步替换** - 新功能使用新架构

### 2.2 紫薇斗数工具迁移
```python
# core/tools/ziwei_tool.py
class ZiweiTool(BaseTool):
    """紫薇斗数工具"""
    
    def __init__(self, ziwei_calc, llm_client):
        super().__init__("ziwei", "紫薇斗数算命")
        self.ziwei_calc = ziwei_calc
        self.llm_client = llm_client
    
    def can_handle(self, intent: dict) -> bool:
        """判断是否能处理紫薇斗数相关请求"""
        return intent.get("tool_name") == "ziwei"
    
    def execute(self, intent: dict, context: dict) -> dict:
        """执行紫薇斗数分析"""
        # 1. 提取出生信息
        birth_info = intent.get("birth_info")
        if not birth_info:
            return {"error": "缺少出生信息"}
        
        # 2. 调用算法
        result = self.ziwei_calc.calculate_chart(
            birth_info["year"], birth_info["month"], 
            birth_info["day"], birth_info["hour"], 
            birth_info.get("gender", "男")
        )
        
        # 3. 生成AI分析
        analysis = self._generate_analysis(result, intent, context)
        
        return {
            "success": True,
            "tool": "ziwei",
            "chart_data": result,
            "analysis": analysis
        }
```

### 2.3 统一接口层
```python
# interfaces/base_interface.py
class BaseInterface(ABC):
    """接口基类 - 统一所有接入方式"""
    
    def __init__(self, conversation_engine):
        self.conversation_engine = conversation_engine
    
    @abstractmethod
    def handle_message(self, session_id: str, message: str) -> str:
        """处理消息的统一接口"""
        pass
    
    def process_message(self, session_id: str, message: str) -> dict:
        """通用消息处理逻辑"""
        return self.conversation_engine.process_message(session_id, message)
```

## 🚀 **阶段3：增强功能**

### 3.1 多轮对话支持
```python
# 在意图识别中考虑对话历史
def recognize_intent_with_history(self, message: str, context: dict, history: list) -> dict:
    """基于历史对话的意图识别"""
    # 分析对话历史，理解上下文
    # 支持"继续分析"、"换个角度"等指令
    pass
```

### 3.2 智能提示词切换
```python
# config/prompts/prompt_manager.py
class PromptManager:
    """提示词管理 - 根据场景自动选择提示词"""
    
    def get_prompt(self, tool_name: str, task_type: str, context: dict) -> str:
        """根据工具、任务类型和上下文选择最佳提示词"""
        # 1. 基础提示词
        base_prompt = self.prompts[tool_name][task_type]
        
        # 2. 上下文增强
        if context.get("previous_analysis"):
            base_prompt += self.get_continuation_prompt()
        
        # 3. 个性化调整
        if context.get("user_preference"):
            base_prompt += self.get_personalization_prompt(context["user_preference"])
        
        return base_prompt
```

### 3.3 微信接口支持
```python
# interfaces/wechat_interface.py
class WeChatInterface(BaseInterface):
    """微信接口 - 支持微信聊天"""
    
    def handle_wechat_message(self, user_id: str, message: str) -> str:
        """处理微信消息"""
        # 使用user_id作为session_id
        result = self.process_message(user_id, message)
        
        # 格式化为微信友好的回复
        return self._format_wechat_response(result)
```

## 📊 **实施时间表**

### 第1周：基础架构
- [ ] 创建目录结构
- [ ] 实现会话管理
- [ ] 实现对话引擎框架
- [ ] 实现工具基类

### 第2周：语义理解
- [ ] 实现意图识别器
- [ ] 实现提示词管理
- [ ] 实现工具注册中心
- [ ] 基础测试

### 第3周：功能迁移
- [ ] 迁移紫薇斗数功能
- [ ] 迁移八字算命功能
- [ ] 迁移六爻算卦功能
- [ ] 兼容性测试

### 第4周：增强功能
- [ ] 多轮对话支持
- [ ] 智能提示词切换
- [ ] 微信接口开发
- [ ] 完整测试

## 🧪 **测试策略**

### 单元测试
- 每个组件独立测试
- 模拟依赖，专注逻辑

### 集成测试
- 组件间协作测试
- 端到端流程测试

### 兼容性测试
- 现有功能不受影响
- API接口向后兼容

## 📝 **风险控制**

### 技术风险
1. **功能回归** - 通过完整测试确保现有功能正常
2. **性能影响** - 监控响应时间，优化瓶颈
3. **数据丢失** - 做好备份，渐进式迁移

### 业务风险
1. **用户体验** - 保持现有体验，逐步增强
2. **服务中断** - 蓝绿部署，无缝切换
3. **功能缺失** - 完整功能对比，确保不遗漏

---

**🎯 目标：在保持现有功能稳定的前提下，构建面向未来的智能聊天架构！**
