#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试整合排盘功能 - 紫薇斗数 + 八字
"""

def test_integrated_algorithms():
    """测试整合算法"""
    print("🔮 测试整合算法功能")
    print("=" * 40)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        # 测试数据
        year, month, day, hour = 1985, 4, 23, 22
        gender = "女"
        
        print(f"📅 测试数据: {year}年{month}月{day}日{hour}时 {gender}")
        print()
        
        # 紫薇斗数计算
        print("🔮 紫薇斗数计算...")
        ziwei_calc = RealZiweiCalculator()
        ziwei_result = ziwei_calc.calculate_chart(year, month, day, hour, gender)
        
        if "error" in ziwei_result:
            print(f"❌ 紫薇斗数失败: {ziwei_result['error']}")
            return False
        else:
            print("✅ 紫薇斗数计算成功")
            birth_info = ziwei_result.get("birth_info", {})
            print(f"  出生信息: {birth_info.get('solar', '')}")
        
        # 八字计算
        print("\n📊 八字计算...")
        bazi_calc = RealBaziCalculator()
        bazi_result = bazi_calc.calculate_bazi(year, month, day, hour, 0, gender)
        
        if not bazi_result.get("success"):
            print(f"❌ 八字计算失败: {bazi_result.get('error')}")
            return False
        else:
            print("✅ 八字计算成功")
            raw_result = bazi_result.get("raw_result", {})
            if "干支" in raw_result:
                print(f"  四柱: {raw_result['干支'].get('文本', '')}")
        
        return ziwei_result, bazi_result
        
    except Exception as e:
        print(f"❌ 整合算法测试失败: {e}")
        return False

def test_integrated_chart_generation():
    """测试整合排盘图生成"""
    print("\n🎨 测试整合排盘图生成")
    print("=" * 40)
    
    # 获取算法结果
    algorithm_results = test_integrated_algorithms()
    if not algorithm_results:
        return False
    
    ziwei_result, bazi_result = algorithm_results
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 创建引擎实例
        engine = FortuneEngine()
        
        # 构造综合结果
        comprehensive_result = {
            "type": "comprehensive",
            "success": True,
            "results": {
                "ziwei": {
                    "success": True,
                    "data": ziwei_result
                },
                "bazi": {
                    "success": True,
                    "data": bazi_result
                }
            }
        }
        
        # 生成整合排盘图
        print("🖼️ 生成整合排盘图...")
        chart_display = engine._generate_chart_display(comprehensive_result)
        
        print("✅ 整合排盘图生成成功")
        
        # 显示部分内容
        lines = chart_display.split('\n')
        for i, line in enumerate(lines[:15]):
            print(line)
        
        if len(lines) > 15:
            print("... (更多内容)")
        
        # 检查特征
        features_check = {
            "图片生成": "图片已生成:" in chart_display,
            "紫薇斗数": "紫薇斗数命盘" in chart_display,
            "八字命理": "八字命理分析" in chart_display,
            "四柱八字": "四柱八字" in chart_display,
            "五行强弱": "五行强弱" in chart_display,
            "大运流年": "大运流年" in chart_display
        }
        
        print("\n✅ 整合特征检查:")
        for feature, status in features_check.items():
            print(f"  {feature}: {'✅' if status else '❌'}")
        
        return all(features_check.values())
        
    except Exception as e:
        print(f"❌ 整合排盘图测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_classification_logic():
    """测试分类逻辑"""
    print("\n📋 测试分类逻辑")
    print("=" * 30)
    
    print("🎯 三大算命系统分类:")
    print("  1. 紫薇斗数 + 八字 (基于出生时间)")
    print("     - 都需要准确的出生年月日时")
    print("     - 可以完美结合分析")
    print("     - 互相验证和补充")
    print()
    print("  2. 六爻卜卦 (独立占卜系统)")
    print("     - 用于具体问题占卜")
    print("     - 基于时间或数字起卦")
    print("     - 独立的分析体系")
    print()
    
    print("✅ 整合优势:")
    print("  - 紫薇斗数: 看性格、命运格局")
    print("  - 八字命理: 看五行、大运流年")
    print("  - 两者结合: 更全面的分析")
    print("  - 图片展示: 直观的视觉效果")
    
    return True

def test_user_experience():
    """测试用户体验"""
    print("\n🚀 用户体验测试")
    print("=" * 30)
    
    print("📊 现在用户输入算命请求后，会得到:")
    print()
    print("1. 📊 【整合命理排盘图片】")
    print("   - 左侧: 精美的紫薇斗数十二宫图")
    print("   - 右侧: 详细的八字命理分析图")
    print("   - 高质量PNG图片，可保存分享")
    print()
    print("2. 📝 【完整文本排盘】")
    print("   - 紫薇斗数: 十二宫详细配置")
    print("   - 八字命理: 四柱、五行、大运")
    print("   - 双重信息，互相补充")
    print()
    print("3. 📋 【核心要点 - 紧凑版】")
    print("   - 基于双重算法的精华总结")
    print()
    print("4. 📚 【深度解读 - 详细版】")
    print("   - 结合紫薇和八字的综合分析")
    print("   - 40%内容关注挑战和问题")
    print("   - 明确的指导建议")
    
    return True

def main():
    """主测试函数"""
    print("🎉 整合排盘功能测试")
    print("=" * 60)
    
    # 测试1: 整合算法
    algorithm_success = bool(test_integrated_algorithms())
    
    # 测试2: 整合排盘图
    chart_success = test_integrated_chart_generation() if algorithm_success else False
    
    # 测试3: 分类逻辑
    logic_success = test_classification_logic()
    
    # 测试4: 用户体验
    ux_success = test_user_experience()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 整合功能测试总结:")
    print(f"  整合算法: {'✅' if algorithm_success else '❌'}")
    print(f"  整合排盘图: {'✅' if chart_success else '❌'}")
    print(f"  分类逻辑: {'✅' if logic_success else '❌'}")
    print(f"  用户体验: {'✅' if ux_success else '❌'}")
    
    if all([algorithm_success, chart_success, logic_success, ux_success]):
        print("\n🎊 整合功能开发完成！")
        print("\n📝 整合特色:")
        print("  1. ✅ 紫薇斗数 + 八字双重算法")
        print("  2. ✅ 左右分屏的整合图片")
        print("  3. ✅ 文本 + 图片双重显示")
        print("  4. ✅ 更丰富的命理信息")
        print("  5. ✅ 专业的视觉效果")
        
        print("\n🚀 现在的系统:")
        print("  - 不再是简单的排盘图")
        print("  - 而是专业的整合命理分析")
        print("  - 包含紫薇斗数和八字两大体系")
        print("  - 提供网页级别的用户体验")
    else:
        print("\n⚠️ 部分功能需要进一步完善")

if __name__ == "__main__":
    main()
