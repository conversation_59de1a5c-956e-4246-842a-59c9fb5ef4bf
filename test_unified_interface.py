#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一接口系统 - 验证新架构的完整接口层
"""

import sys
import os
import time
import requests
import threading
sys.path.append('.')

def test_unified_api_creation():
    """测试统一API创建"""
    print("🔧 测试统一API创建")
    print("=" * 80)
    
    try:
        from interfaces.unified_api import UnifiedAPI
        
        # 创建配置
        config = {
            "api_key": "test_key",
            "model_name": "deepseek-ai/DeepSeek-V3",
            "max_history": 20,
            "session_timeout": 3600
        }
        
        # 创建API实例
        api = UnifiedAPI(config)
        
        print(f"✅ 统一API创建成功")
        print(f"   工具数量: {len(api.tool_registry.list_tools())}")
        print(f"   注册工具: {api.tool_registry.list_tools()}")
        
        return api
        
    except Exception as e:
        print(f"❌ 统一API创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_web_interface_creation():
    """测试Web接口创建"""
    print("\n🌐 测试Web接口创建")
    print("=" * 80)
    
    try:
        from interfaces.web_interface import WebInterface
        
        # 创建Web接口（不启动服务器）
        web_interface = WebInterface("http://localhost:8002")
        
        print(f"✅ Web接口创建成功")
        print(f"   会话ID: {web_interface.session_id}")
        print(f"   API地址: {web_interface.api_url}")
        
        return web_interface
        
    except Exception as e:
        print(f"❌ Web接口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_server_startup(api):
    """测试API服务器启动"""
    print("\n🚀 测试API服务器启动")
    print("=" * 80)
    
    if not api:
        print("❌ API实例不存在，跳过服务器测试")
        return False
    
    try:
        # 在后台线程启动服务器
        def start_server():
            api.run(port=8002, debug=False)
        
        server_thread = threading.Thread(target=start_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(3)
        
        # 测试健康检查
        try:
            response = requests.get("http://localhost:8002/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API服务器启动成功")
                print(f"   状态: {health_data.get('status')}")
                print(f"   版本: {health_data.get('version')}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到API服务器: {e}")
            return False
            
    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔗 测试API端点")
    print("=" * 80)
    
    base_url = "http://localhost:8002"
    
    try:
        # 测试1: 健康检查
        print("1. 测试健康检查...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
        
        # 测试2: 工具列表
        print("\n2. 测试工具列表...")
        response = requests.get(f"{base_url}/v2/tools", timeout=5)
        if response.status_code == 200:
            tools_data = response.json()
            print(f"✅ 工具列表获取成功: {tools_data.get('total_count')} 个工具")
        else:
            print(f"❌ 工具列表获取失败: {response.status_code}")
        
        # 测试3: 聊天接口
        print("\n3. 测试聊天接口...")
        chat_data = {
            "message": "你好",
            "session_id": "test_session_123"
        }
        
        response = requests.post(f"{base_url}/v2/chat", json=chat_data, timeout=30)
        if response.status_code == 200:
            chat_result = response.json()
            print(f"✅ 聊天接口测试成功")
            print(f"   响应: {chat_result.get('message', '')[:50]}...")
            print(f"   成功: {chat_result.get('success')}")
        else:
            print(f"❌ 聊天接口测试失败: {response.status_code}")
        
        # 测试4: 算命功能
        print("\n4. 测试算命功能...")
        fortune_data = {
            "message": "我想看紫薇斗数，1988年6月1日午时男",
            "session_id": "test_fortune_456"
        }
        
        response = requests.post(f"{base_url}/v2/chat", json=fortune_data, timeout=60)
        if response.status_code == 200:
            fortune_result = response.json()
            print(f"✅ 算命功能测试成功")
            print(f"   工具: {fortune_result.get('tool_used', '无')}")
            print(f"   图片: {'有' if fortune_result.get('chart_image') else '无'}")
        else:
            print(f"❌ 算命功能测试失败: {response.status_code}")
        
        # 测试5: 会话管理
        print("\n5. 测试会话管理...")
        response = requests.get(f"{base_url}/v2/session/test_session_123", timeout=5)
        if response.status_code == 200:
            session_data = response.json()
            print(f"✅ 会话管理测试成功")
            print(f"   消息数: {session_data.get('message_count')}")
        else:
            print(f"❌ 会话管理测试失败: {response.status_code}")
        
        # 测试6: 统计信息
        print("\n6. 测试统计信息...")
        response = requests.get(f"{base_url}/v2/stats", timeout=5)
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ 统计信息测试成功")
            print(f"   工具执行: {stats_data.get('tool_stats', {}).get('total_executions', 0)} 次")
        else:
            print(f"❌ 统计信息测试失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_legacy_api_compatibility():
    """测试旧版API兼容性"""
    print("\n🔄 测试旧版API兼容性")
    print("=" * 80)
    
    try:
        base_url = "http://localhost:8002"
        
        # 模拟旧版API请求
        legacy_data = {
            "model": "deepseek-ai/DeepSeek-V3",
            "messages": [
                {
                    "role": "user",
                    "content": "你好，我想了解算命服务"
                }
            ],
            "stream": False
        }
        
        response = requests.post(f"{base_url}/v1/chat/completions", json=legacy_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 旧版API兼容性测试成功")
            print(f"   响应格式: {'choices' in result}")
            print(f"   内容长度: {len(result.get('choices', [{}])[0].get('message', {}).get('content', ''))}")
            return True
        else:
            print(f"❌ 旧版API兼容性测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 旧版API兼容性测试异常: {e}")
        return False

def test_interface_integration():
    """测试接口集成"""
    print("\n🔗 测试接口集成")
    print("=" * 80)
    
    try:
        from interfaces.base_interface import BaseInterface
        from interfaces.wechat_interface import WeChatInterface
        
        # 创建模拟对话引擎
        class MockConversationEngine:
            def process_message(self, session_id, message, user_id=None):
                return {
                    "success": True,
                    "message": f"模拟回复: {message}",
                    "session_id": session_id
                }
        
        mock_engine = MockConversationEngine()
        
        # 测试微信接口
        wechat_config = {
            "token": "test_token",
            "app_id": "test_app_id",
            "app_secret": "test_app_secret"
        }
        
        wechat_interface = WeChatInterface(mock_engine, wechat_config)
        
        # 测试消息处理
        result = wechat_interface.handle_message("test_user", "测试消息")
        
        print(f"✅ 接口集成测试成功")
        print(f"   微信接口响应: {result[:50]}...")
        
        # 测试健康检查
        health = wechat_interface.health_check()
        print(f"   健康状态: {health.get('status')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 接口集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 统一接口系统测试")
    print("=" * 100)
    
    # 测试1: API创建
    api = test_unified_api_creation()
    
    # 测试2: Web接口创建
    web_interface = test_web_interface_creation()
    
    # 测试3: API服务器启动
    server_success = test_api_server_startup(api)
    
    # 测试4: API端点
    endpoints_success = False
    if server_success:
        endpoints_success = test_api_endpoints()
    
    # 测试5: 旧版API兼容性
    legacy_success = False
    if server_success:
        legacy_success = test_legacy_api_compatibility()
    
    # 测试6: 接口集成
    integration_success = test_interface_integration()
    
    # 总结
    print("\n" + "=" * 100)
    print("🎉 统一接口系统测试结果:")
    print(f"  API创建: {'✅ 成功' if api else '❌ 失败'}")
    print(f"  Web接口: {'✅ 成功' if web_interface else '❌ 失败'}")
    print(f"  服务器启动: {'✅ 成功' if server_success else '❌ 失败'}")
    print(f"  API端点: {'✅ 成功' if endpoints_success else '❌ 失败'}")
    print(f"  旧版兼容: {'✅ 成功' if legacy_success else '❌ 失败'}")
    print(f"  接口集成: {'✅ 成功' if integration_success else '❌ 失败'}")
    
    success_count = sum([
        bool(api), bool(web_interface), server_success, 
        endpoints_success, legacy_success, integration_success
    ])
    
    if success_count >= 5:
        print("\n🎊 统一接口系统测试基本通过！")
        print("\n💡 **阶段3成果验证:**")
        print("  ✅ 统一API接口 - 支持智能聊天和算命功能")
        print("  ✅ Web接口 - 基于Streamlit的现代化界面")
        print("  ✅ 微信接口 - 支持微信公众号接入")
        print("  ✅ 旧版兼容 - 保持向后兼容性")
        print("  ✅ 多端支持 - 统一的接口架构")
        print()
        print("🚀 **架构重构完成度: 90%**")
        print("  - 基础架构 ✅")
        print("  - 功能迁移 ✅") 
        print("  - 统一接口 ✅")
        print("  - 多端支持 ✅")
        print()
        print("🎯 **使用方法:**")
        print("  1. 启动API: python interfaces/unified_api.py")
        print("  2. 启动Web: streamlit run interfaces/web_interface.py")
        print("  3. API地址: http://localhost:8002")
        print("  4. Web地址: http://localhost:8501")
        return True
    else:
        print(f"\n⚠️ 部分测试失败 ({success_count}/6)")
        return False

if __name__ == "__main__":
    main()
