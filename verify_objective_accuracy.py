#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证算法输出的客观数据是否准确
对比权威资料和传统典籍
"""

def verify_bazi_accuracy():
    """验证八字排盘的准确性"""
    print("🔍 验证八字排盘准确性")
    print("=" * 60)

    # 测试案例：1988年6月1日午时
    test_cases = [
        {
            "date": "1988年6月1日午时",
            "expected_bazi": "戊辰 丁巳 丁亥 丙午",
            "source": "网上权威万年历"
        },
        {
            "date": "1990年3月15日未时",
            "expected_bazi": "庚午 己卯 己卯 辛未",
            "source": "传统万年历"
        }
    ]

    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()

        print("📊 八字排盘验证:")
        all_correct = True

        for case in test_cases:
            if case["date"] == "1988年6月1日午时":
                result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")
            else:
                result = calc.calculate_enhanced_bazi(1990, 3, 15, 14, "女")

            if result["success"]:
                actual_bazi = result["bazi_info"]["chinese_date"]
                expected_bazi = case["expected_bazi"]

                status = "✅" if actual_bazi == expected_bazi else "❌"
                if actual_bazi != expected_bazi:
                    all_correct = False

                print(f"  {case['date']}:")
                print(f"    期望: {expected_bazi}")
                print(f"    实际: {actual_bazi}")
                print(f"    状态: {status}")
                print(f"    来源: {case['source']}")
            else:
                print(f"  ❌ {case['date']}: 计算失败")
                all_correct = False

        return all_correct

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_wuxing_calculation():
    """验证五行计算的准确性"""
    print(f"\n🔍 验证五行计算准确性")
    print("=" * 60)

    # 手动验证戊辰 丁巳 丁亥 丙午的五行
    print("📊 手动验证 戊辰 丁巳 丁亥 丙午:")

    manual_calculation = {
        "天干": [
            ("戊", "土"), ("丁", "火"), ("丁", "火"), ("丙", "火")
        ],
        "地支": {
            "辰": "土", "巳": "火", "亥": "水", "午": "火"
        },
        "藏干": {
            "辰": ["戊(土)", "乙(木)", "癸(水)"],
            "巳": ["丙(火)", "戊(土)", "庚(金)"],
            "亥": ["壬(水)", "甲(木)"],
            "午": ["丁(火)", "己(土)"]
        }
    }

    # 手动统计
    manual_count = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}

    # 天干
    for gan, element in manual_calculation["天干"]:
        manual_count[element] += 1
        print(f"  天干{gan} = {element}")

    # 地支
    for zhi, element in manual_calculation["地支"].items():
        manual_count[element] += 1
        print(f"  地支{zhi} = {element}")

    # 藏干（权重0.5）
    for zhi, canggan_list in manual_calculation["藏干"].items():
        print(f"  {zhi}藏干: {', '.join(canggan_list)}")
        for canggan_info in canggan_list:
            element = canggan_info.split('(')[1].rstrip(')')
            manual_count[element] += 0.5

    print(f"\n📊 手动计算结果:")
    for element, count in manual_count.items():
        print(f"  {element}: {count}个")

    # 对比算法结果
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()

        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")

        if result["success"]:
            algo_count = result["analysis"]["wuxing"]["count"]

            print(f"\n📊 算法计算结果:")
            for element, count in algo_count.items():
                print(f"  {element}: {count}个")

            print(f"\n🔍 对比验证:")
            all_match = True
            for element in ["木", "火", "土", "金", "水"]:
                manual = manual_count[element]
                algo = algo_count[element]
                status = "✅" if abs(manual - algo) < 0.01 else "❌"
                if abs(manual - algo) >= 0.01:
                    all_match = False
                print(f"  {element}: 手动={manual}, 算法={algo} {status}")

            return all_match
        else:
            print(f"❌ 算法计算失败")
            return False

    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def verify_traditional_references():
    """验证传统典籍参考的准确性"""
    print(f"\n📚 验证传统典籍参考")
    print("=" * 60)

    traditional_references = {
        "天干五行": {
            "来源": "《易经》、《黄帝内经》",
            "内容": "甲乙木、丙丁火、戊己土、庚辛金、壬癸水",
            "验证": "与算法映射一致"
        },
        "地支五行": {
            "来源": "《渊海子平》",
            "内容": "子亥水、寅卯木、巳午火、申酉金、辰戌丑未土",
            "验证": "与算法映射一致"
        },
        "地支藏干": {
            "来源": "《三命通会》",
            "内容": "子藏癸、丑藏己癸辛、寅藏甲丙戊...",
            "验证": "需要逐一对比"
        },
        "纳音五行": {
            "来源": "《珞琭子三命消息赋》",
            "内容": "六十甲子纳音表",
            "验证": "需要对比完整纳音表"
        }
    }

    print("📊 传统典籍参考验证:")
    for category, info in traditional_references.items():
        print(f"\n🔹 {category}:")
        for key, value in info.items():
            print(f"  {key}: {value}")

    # 重点验证地支藏干
    print(f"\n🔍 重点验证地支藏干:")

    # 《三命通会》标准地支藏干
    standard_canggan = {
        "子": ["癸"],
        "丑": ["己", "癸", "辛"],
        "寅": ["甲", "丙", "戊"],
        "卯": ["乙"],
        "辰": ["戊", "乙", "癸"],
        "巳": ["丙", "戊", "庚"],
        "午": ["丁", "己"],
        "未": ["己", "丁", "乙"],
        "申": ["庚", "壬", "戊"],
        "酉": ["辛"],
        "戌": ["戊", "辛", "丁"],
        "亥": ["壬", "甲"]
    }

    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()
        algo_canggan = calc.dizhi_canggan

        all_correct = True
        for zhi, standard_list in standard_canggan.items():
            algo_list = algo_canggan.get(zhi, [])
            status = "✅" if algo_list == standard_list else "❌"
            if algo_list != standard_list:
                all_correct = False
            print(f"  {zhi}: 典籍={standard_list}, 算法={algo_list} {status}")

        return all_correct

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_dayun_calculation():
    """验证大运推算的准确性"""
    print(f"\n🔍 验证大运推算准确性")
    print("=" * 60)

    print("📊 大运推算原理验证:")
    print("1. 起运年龄计算:")
    print("   - 男命阳年、女命阴年：顺排")
    print("   - 男命阴年、女命阳年：逆排")
    print("   - 起运年龄通常为8岁左右（简化计算）")

    print("\n2. 大运干支推算:")
    print("   - 以月柱为起点")
    print("   - 顺排或逆排60甲子")
    print("   - 每步大运10年")

    # 测试算法的大运推算
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()

        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")

        if result["success"] and "dayun" in result["analysis"]:
            dayun = result["analysis"]["dayun"]

            print(f"\n📊 算法大运推算结果:")
            print(f"  起运年龄: {dayun['start_age']}岁")
            print(f"  计算方法: {dayun['calculation_method']}")

            if dayun["dayun_list"]:
                print(f"  前三步大运:")
                for i, step in enumerate(dayun["dayun_list"][:3]):
                    print(f"    第{i+1}步: {step['ganzhi']} ({step['shishen']}) {step['age_range']}")

            # 这里需要人工验证是否符合传统推算方法
            print(f"\n⚠️ 注意: 大运推算需要考虑节气，当前为简化算法")
            print(f"建议: 与传统命理师或专业软件对比验证")

            return True
        else:
            print(f"❌ 大运推算失败")
            return False

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def identify_improvement_areas():
    """识别需要改进的地方"""
    print(f"\n💡 识别需要改进的地方")
    print("=" * 60)

    improvement_areas = {
        "高优先级": [
            "大运起运年龄需要精确到节气计算",
            "月柱干支推算需要考虑节气交替",
            "时辰划分需要考虑真太阳时",
            "闰月处理需要更精确"
        ],
        "中优先级": [
            "添加更多纳音五行对照",
            "完善神煞推算功能",
            "增加流年推算",
            "添加胎元、命宫等推算"
        ],
        "低优先级": [
            "优化算法性能",
            "增加更多验证测试",
            "完善错误处理",
            "添加更多传统典籍参考"
        ]
    }

    print("📋 改进优先级:")
    for priority, items in improvement_areas.items():
        print(f"\n🔹 {priority}:")
        for item in items:
            print(f"  • {item}")

    print(f"\n🎯 核心原则:")
    print("1. 确保基础计算100%准确")
    print("2. 所有算法都要有传统典籍依据")
    print("3. 明确区分客观计算和主观解读")
    print("4. 提供算法来源和参考文献")

def main():
    """主函数"""
    print("🧪 算法客观数据准确性验证")
    print("=" * 80)

    # 验证八字排盘
    bazi_correct = verify_bazi_accuracy()

    # 验证五行计算
    wuxing_correct = verify_wuxing_calculation()

    # 验证传统参考
    reference_correct = verify_traditional_references()

    # 验证大运推算
    dayun_correct = verify_dayun_calculation()

    # 识别改进点
    identify_improvement_areas()

    print("\n" + "=" * 80)
    print("🎯 验证总结:")
    print(f"  八字排盘: {'✅' if bazi_correct else '❌'}")
    print(f"  五行计算: {'✅' if wuxing_correct else '❌'}")
    print(f"  传统参考: {'✅' if reference_correct else '❌'}")
    print(f"  大运推算: {'✅' if dayun_correct else '❌'}")

    all_correct = all([bazi_correct, wuxing_correct, reference_correct, dayun_correct])

    if all_correct:
        print("\n🎉 所有客观数据验证通过！")
        print("可以放心地让LLM基于这些数据进行传统解读")
    else:
        print("\n⚠️ 部分验证失败，需要修正算法")
        print("建议优先修正失败项目，确保客观数据准确")

if __name__ == "__main__":
    main()
