#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Agent时序问题
验证主Agent是否真的等待后台Agent完成
"""

import asyncio
import sys
import time
import logging
from datetime import datetime
sys.path.append('.')

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def debug_agent_timing():
    """调试Agent时序问题"""
    print("🔍 调试Agent时序问题")
    print("=" * 60)
    print("目标: 验证主Agent是否真的等待后台Agent完成")
    print("=" * 60)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建系统
        master_agent = MasterCustomerAgent("debug_master")
        calculator_agent = FortuneCalculatorAgent("debug_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "debug_timing_session"
        
        print("1️⃣ 开始时序调试...")
        
        # 测试消息
        user_message = "我想看紫薇斗数，1988年6月1日午时出生，男性"
        print(f"👤 用户: {user_message}")
        
        # 记录开始时间
        start_time = time.time()
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        
        # 调用协调器
        print(f"📤 调用协调器...")
        result = await coordinator.handle_user_message(session_id, user_message)
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        print(f"⏰ 结束时间: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}")
        print(f"⏱️  总耗时: {total_time:.3f}秒")
        
        # 分析结果
        print(f"\n📊 结果分析:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   阶段: {result.get('stage', 'unknown')}")
        print(f"   处理时间: {result.get('processing_time', 0):.3f}秒")
        
        response = result.get('response', '')
        print(f"   响应长度: {len(response)} 字符")
        print(f"   响应预览: {response[:200]}...")
        
        # 检查是否包含分析完成标识
        if "分析已完成" in response or "专业解读" in response:
            print(f"✅ 响应包含分析完成标识")
            
            # 检查是否有缓存结果
            session_state = master_agent.get_session_state(session_id)
            if session_state and session_state.get("result_id"):
                result_id = session_state["result_id"]
                print(f"✅ 找到结果ID: {result_id[:8]}...")
                
                # 检查缓存
                cached_result = calculator_agent.get_cached_result(result_id)
                if cached_result:
                    print(f"✅ 缓存查询成功")
                    print(f"   计算类型: {cached_result['calculation_type']}")
                    print(f"   置信度: {cached_result['confidence']}")
                    print(f"   简要总结: {cached_result['summary'][:100]}...")
                    
                    # 检查详细分析
                    detailed = cached_result.get('detailed_analysis', {})
                    if detailed:
                        word_count = detailed.get('word_count', 0)
                        print(f"   详细分析字数: {word_count}")
                        
                        if word_count > 1000:
                            print(f"✅ 后台Agent确实生成了详细分析")
                        else:
                            print(f"⚠️  详细分析字数较少，可能不是真实计算")
                    else:
                        print(f"❌ 没有找到详细分析")
                else:
                    print(f"❌ 缓存查询失败")
            else:
                print(f"❌ 没有找到结果ID")
        else:
            print(f"❌ 响应不包含分析完成标识")
        
        # 时序分析
        print(f"\n⏰ 时序分析:")
        if total_time < 2:
            print(f"⚠️  总耗时过短 ({total_time:.3f}s)，可能没有真正调用后台Agent")
            print(f"   可能原因:")
            print(f"   1. 使用了缓存结果")
            print(f"   2. 主Agent自己生成了回复")
            print(f"   3. 后台Agent没有进行真正的计算")
        elif total_time < 10:
            print(f"✅ 总耗时适中 ({total_time:.3f}s)，可能进行了真实计算")
        else:
            print(f"✅ 总耗时较长 ({total_time:.3f}s)，确实进行了详细计算")
        
        # 验证后台Agent是否被调用
        print(f"\n🔍 验证后台Agent调用:")
        calc_stats = calculator_agent.get_stats()
        print(f"   后台Agent处理消息数: {calc_stats['messages_processed']}")
        print(f"   后台Agent平均耗时: {calc_stats['average_processing_time']:.3f}秒")
        
        if calc_stats['messages_processed'] > 0:
            print(f"✅ 后台Agent确实被调用了")
            
            if calc_stats['average_processing_time'] > 5:
                print(f"✅ 后台Agent进行了长时间计算")
            else:
                print(f"⚠️  后台Agent计算时间较短，可能使用了缓存")
        else:
            print(f"❌ 后台Agent没有被调用")
        
        # 最终判断
        print(f"\n🎯 最终判断:")
        
        issues = []
        
        if total_time < 2:
            issues.append("总耗时过短")
        
        if calc_stats['messages_processed'] == 0:
            issues.append("后台Agent未被调用")
        
        if "分析已完成" not in response:
            issues.append("响应不包含分析完成标识")
        
        if issues:
            print(f"❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
            
            print(f"\n💡 可能的原因:")
            print(f"   1. 主Agent没有真正等待后台Agent")
            print(f"   2. 后台Agent没有进行真实计算")
            print(f"   3. 缓存机制导致跳过了计算")
            print(f"   4. 异步调用逻辑有问题")
            
            return False
        else:
            print(f"✅ 时序正常，主Agent确实等待了后台Agent完成")
            return True
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_without_cache():
    """测试无缓存情况"""
    print(f"\n🧪 测试无缓存情况")
    print("-" * 40)
    
    try:
        # 清除缓存
        from core.storage.calculation_cache import CalculationCache
        import shutil
        import os
        
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            print(f"✅ 已清除缓存目录")
        
        # 重新测试
        return await debug_agent_timing()
        
    except Exception as e:
        print(f"❌ 无缓存测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 Agent时序问题调试")
    print("=" * 80)
    print("目标: 确认主Agent是否真的等待后台Agent完成计算")
    print("=" * 80)
    
    # 测试1: 正常情况
    normal_result = await debug_agent_timing()
    
    # 测试2: 无缓存情况
    no_cache_result = await test_without_cache()
    
    # 最终结论
    print(f"\n" + "=" * 80)
    print("🏁 调试结论")
    print("=" * 80)
    
    if normal_result and no_cache_result:
        print(f"✅ 时序正常！主Agent确实等待后台Agent完成")
        print(f"✅ 双Agent协作机制工作正常")
        print(f"✅ 用户看到的是真实的算命结果")
    elif normal_result and not no_cache_result:
        print(f"⚠️  可能存在缓存依赖问题")
        print(f"   正常情况下工作，但无缓存时可能有问题")
    elif not normal_result and no_cache_result:
        print(f"⚠️  可能存在缓存干扰问题")
        print(f"   缓存情况下有问题，但无缓存时正常")
    else:
        print(f"❌ 确认存在时序问题！")
        print(f"   主Agent可能没有真正等待后台Agent")
        print(f"   用户看到的可能不是真实的算命结果")
        
        print(f"\n🔧 建议的修复方案:")
        print(f"   1. 检查主Agent的await调用")
        print(f"   2. 确认后台Agent的计算逻辑")
        print(f"   3. 验证异步调用的正确性")
        print(f"   4. 添加更多的日志和时序标记")
    
    return normal_result or no_cache_result

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
