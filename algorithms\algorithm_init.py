#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法模块初始化 - 重构版
"""

try:
    from .ziwei_calculator import RealZiweiCalculator as ZiweiCalculator
except ImportError:
    ZiweiCalculator = None

try:
    from .bazi_calculator import RealBaziCalculator as BaziCalculator
except ImportError:
    BaziCalculator = None

try:
    from .liuyao_calculator import LiuyaoCalculator
except ImportError:
    LiuyaoCalculator = None

def get_ziwei_calculator():
    """获取紫薇算法实例"""
    if ZiweiCalculator is None:
        raise ImportError("紫薇算法模块未找到")
    return ZiweiCalculator()

def get_bazi_calculator():
    """获取八字算法实例"""
    if BaziCalculator is None:
        raise ImportError("八字算法模块未找到")
    return BaziCalculator()

def get_liuyao_calculator():
    """获取六爻算法实例"""
    if LiuyaoCalculator is None:
        raise ImportError("六爻算法模块未找到")
    return LiuyaoCalculator()

__all__ = [
    'ZiweiCalculator',
    'BaziCalculator', 
    'LiuyaoCalculator',
    'get_ziwei_calculator',
    'get_bazi_calculator',
    'get_liuyao_calculator'
]
