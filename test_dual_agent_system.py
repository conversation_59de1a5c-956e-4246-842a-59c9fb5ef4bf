#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双Agent协作系统测试
"""

import asyncio
import sys
import time
from datetime import datetime
sys.path.append('.')

async def test_dual_agent_system():
    """测试双Agent协作系统"""
    print("🤖 双Agent协作系统测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 导入Agent类
        from core.agents.base_agent import BaseAgent, agent_registry
        from core.agents.customer_service_agent import CustomerServiceAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.agent_coordinator import AgentCoordinator
        
        print("✅ Agent类导入成功")
        
        # 创建Agent实例
        print("\n🔧 创建Agent实例...")
        customer_agent = CustomerServiceAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = AgentCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(customer_agent)
        agent_registry.register_agent(calculator_agent)
        
        print(f"✅ 沟通Agent创建: {customer_agent.agent_id}")
        print(f"✅ 计算Agent创建: {calculator_agent.agent_id}")
        print(f"✅ 协调器创建: {coordinator.coordinator_id}")
        
        # 测试用例
        test_cases = [
            {
                "name": "紫薇斗数算命",
                "message": "我想看紫薇斗数，1988年6月1日午时出生，男性",
                "expected_type": "fortune_telling"
            },
            {
                "name": "八字算命",
                "message": "我想看八字算命，1990年5月15日上午10点，女",
                "expected_type": "fortune_telling"
            },
            {
                "name": "一般聊天",
                "message": "你好，请介绍一下自己",
                "expected_type": "chat"
            },
            {
                "name": "算命咨询",
                "message": "什么是紫薇斗数？",
                "expected_type": "question"
            }
        ]
        
        print(f"\n🧪 开始测试 {len(test_cases)} 个用例...")
        
        success_count = 0
        total_time = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
            print(f"用户输入: {test_case['message']}")
            
            start_time = time.time()
            
            try:
                # 使用协调器处理用户消息
                result = await coordinator.handle_user_message(
                    session_id=f"test_session_{i}",
                    user_message=test_case['message']
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                total_time += processing_time
                
                print(f"⏱️  处理时间: {processing_time:.2f}秒")
                
                if result.get("success"):
                    print("✅ 处理成功")
                    print(f"📝 AI回复: {result.get('response', '')[:200]}...")
                    
                    # 检查是否有计算结果
                    if result.get("calculation_result"):
                        print("🧮 包含计算结果")
                    
                    success_count += 1
                else:
                    print("❌ 处理失败")
                    print(f"错误: {result.get('error', '未知错误')}")
                
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试结果统计
        print(f"\n📊 测试结果统计")
        print("=" * 50)
        print(f"总测试用例: {len(test_cases)}")
        print(f"成功用例: {success_count}")
        print(f"失败用例: {len(test_cases) - success_count}")
        print(f"成功率: {success_count/len(test_cases)*100:.1f}%")
        print(f"平均处理时间: {total_time/len(test_cases):.2f}秒")
        
        # Agent统计信息
        print(f"\n📈 Agent统计信息")
        print("=" * 50)
        
        customer_stats = customer_agent.get_stats()
        calculator_stats = calculator_agent.get_stats()
        coordinator_stats = coordinator.get_stats()
        
        print(f"沟通Agent:")
        print(f"  处理消息数: {customer_stats['messages_processed']}")
        print(f"  平均处理时间: {customer_stats['average_processing_time']:.2f}秒")
        print(f"  错误数: {customer_stats['errors']}")
        
        print(f"计算Agent:")
        print(f"  处理消息数: {calculator_stats['messages_processed']}")
        print(f"  平均处理时间: {calculator_stats['average_processing_time']:.2f}秒")
        print(f"  错误数: {calculator_stats['errors']}")
        
        print(f"协调器:")
        print(f"  总任务数: {coordinator_stats['total_tasks']}")
        print(f"  完成任务数: {coordinator_stats['completed_tasks']}")
        print(f"  失败任务数: {coordinator_stats['failed_tasks']}")
        print(f"  平均响应时间: {coordinator_stats['average_response_time']:.2f}秒")
        
        # 性能评估
        print(f"\n⚡ 性能评估")
        print("=" * 50)
        
        if total_time / len(test_cases) < 5:
            print("🌟 响应速度: 优秀 (< 5秒)")
        elif total_time / len(test_cases) < 10:
            print("✅ 响应速度: 良好 (< 10秒)")
        else:
            print("⚠️  响应速度: 需要优化 (> 10秒)")
        
        if success_count / len(test_cases) >= 0.8:
            print("🌟 成功率: 优秀 (≥ 80%)")
        elif success_count / len(test_cases) >= 0.6:
            print("✅ 成功率: 良好 (≥ 60%)")
        else:
            print("⚠️  成功率: 需要改进 (< 60%)")
        
        # 最终评估
        print(f"\n🎯 双Agent系统评估")
        print("=" * 80)
        
        if success_count >= len(test_cases) * 0.8 and total_time / len(test_cases) < 10:
            print("🎉 双Agent协作系统测试通过！")
            print("✅ 系统运行稳定，性能良好")
            print("✅ 沟通Agent和计算Agent协作正常")
            print("✅ 可以投入生产使用")
            
            print("\n🚀 建议的下一步:")
            print("  1. 集成到Web界面")
            print("  2. 进行压力测试")
            print("  3. 优化响应时间")
            print("  4. 添加更多算命类型")
            
            return True
        else:
            print("⚠️  双Agent系统需要进一步优化")
            
            if success_count < len(test_cases) * 0.8:
                print("  - 提升系统稳定性和成功率")
            
            if total_time / len(test_cases) >= 10:
                print("  - 优化响应时间和性能")
            
            print("\n🔧 建议的改进措施:")
            print("  1. 检查Agent间通信机制")
            print("  2. 优化算法调用性能")
            print("  3. 改进错误处理逻辑")
            print("  4. 增强LLM提示词效果")
            
            return False
            
    except Exception as e:
        print(f"❌ 双Agent系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_communication():
    """测试Agent间通信"""
    print("\n🔗 Agent间通信测试")
    print("-" * 50)
    
    try:
        from core.agents.customer_service_agent import CustomerServiceAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import MessageType, AgentMessage
        
        # 创建Agent
        customer_agent = CustomerServiceAgent("test_customer")
        calculator_agent = FortuneCalculatorAgent("test_calculator")
        
        print("✅ 测试Agent创建成功")
        
        # 测试消息发送
        message_id = await customer_agent.send_message(
            calculator_agent,
            MessageType.CALCULATION_REQUEST,
            {
                "user_message": "测试消息",
                "session_id": "test_session"
            }
        )
        
        print(f"✅ 消息发送成功: {message_id}")
        
        # 检查消息队列
        queue_size = calculator_agent.message_queue.qsize()
        print(f"✅ 计算Agent消息队列大小: {queue_size}")
        
        if queue_size > 0:
            print("🌟 Agent间通信正常")
            return True
        else:
            print("❌ Agent间通信异常")
            return False
            
    except Exception as e:
        print(f"❌ Agent通信测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 双Agent协作架构完整测试")
    print("=" * 80)
    print("目标: 验证双Agent协作系统的功能和性能")
    print("=" * 80)
    
    # 测试1: Agent间通信
    comm_success = await test_agent_communication()
    
    # 测试2: 完整系统测试
    system_success = await test_dual_agent_system()
    
    # 最终结论
    print("\n" + "=" * 80)
    print("🏁 最终测试结论")
    print("=" * 80)
    
    if comm_success and system_success:
        print("🎉 双Agent协作架构开发成功！")
        print("\n💪 系统能力:")
        print("  ✅ 专业分工 - 沟通和计算Agent各司其职")
        print("  ✅ 并行处理 - 提升响应速度和用户体验")
        print("  ✅ 智能协调 - 自动任务分配和结果整合")
        print("  ✅ 错误处理 - 完善的异常处理和降级机制")
        
        print("\n🌟 技术优势:")
        print("  🚀 异步协作 - Agent间高效通信")
        print("  📊 性能监控 - 详细的统计和分析")
        print("  🔧 模块化设计 - 易于维护和扩展")
        print("  🛡️ 稳定可靠 - 多层错误处理保障")
        
        print("\n🎯 用户体验:")
        print("  💬 自然对话 - 沟通Agent提供流畅交互")
        print("  🧮 专业计算 - 计算Agent确保准确性")
        print("  ⚡ 快速响应 - 并行处理减少等待时间")
        print("  🎨 个性化服务 - 智能适配用户需求")
        
        print("\n🚀 现在可以集成到Web界面，为用户提供专业的算命服务！")
        return True
    else:
        print("💥 双Agent系统存在问题，需要进一步调试")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
