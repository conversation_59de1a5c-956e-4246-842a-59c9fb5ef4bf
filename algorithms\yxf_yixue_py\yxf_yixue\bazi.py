#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字算命算法实现
基于传统八字理论的简化实现
"""

import datetime
from typing import Dict, Any

class BaziApi:
    """八字算命API"""
    
    def __init__(self):
        self.P = None  # 排盘对象
        self.current_result = None
        
        # 天干地支
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        
        # 五行
        self.wuxing_tiangan = {
            "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土",
            "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水"
        }
        
        self.wuxing_dizhi = {
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", "巳": "火",
            "午": "火", "未": "土", "申": "金", "酉": "金", "戌": "土", "亥": "水"
        }
    
    def paipan(self, datetime_obj: datetime.datetime, xingbie: str = "男") -> Dict[str, Any]:
        """八字排盘"""
        try:
            year = datetime_obj.year
            month = datetime_obj.month
            day = datetime_obj.day
            hour = datetime_obj.hour
            
            # 计算年柱
            year_gan_index = (year - 4) % 10
            year_zhi_index = (year - 4) % 12
            year_gan = self.tiangan[year_gan_index]
            year_zhi = self.dizhi[year_zhi_index]
            
            # 计算月柱（简化算法）
            month_gan_index = (year_gan_index * 2 + month) % 10
            month_zhi_index = (month + 1) % 12
            month_gan = self.tiangan[month_gan_index]
            month_zhi = self.dizhi[month_zhi_index]
            
            # 计算日柱（简化算法）
            day_gan_index = (year * 365 + month * 30 + day) % 10
            day_zhi_index = (year * 365 + month * 30 + day) % 12
            day_gan = self.tiangan[day_gan_index]
            day_zhi = self.dizhi[day_zhi_index]
            
            # 计算时柱
            hour_zhi_index = (hour + 1) // 2 % 12
            hour_gan_index = (day_gan_index * 2 + hour_zhi_index) % 10
            hour_gan = self.tiangan[hour_gan_index]
            hour_zhi = self.dizhi[hour_zhi_index]
            
            # 构建结果
            result = {
                "干支": {
                    "文本": f"{year_gan}{year_zhi} {month_gan}{month_zhi} {day_gan}{day_zhi} {hour_gan}{hour_zhi}",
                    "年柱": f"{year_gan}{year_zhi}",
                    "月柱": f"{month_gan}{month_zhi}",
                    "日柱": f"{day_gan}{day_zhi}",
                    "时柱": f"{hour_gan}{hour_zhi}"
                },
                "五行": {
                    "木": {"旺衰": self._get_wuxing_wangcui("木", [year_gan, month_gan, day_gan, hour_gan, year_zhi, month_zhi, day_zhi, hour_zhi]), "五行数": "2"},
                    "火": {"旺衰": self._get_wuxing_wangcui("火", [year_gan, month_gan, day_gan, hour_gan, year_zhi, month_zhi, day_zhi, hour_zhi]), "五行数": "1"},
                    "土": {"旺衰": self._get_wuxing_wangcui("土", [year_gan, month_gan, day_gan, hour_gan, year_zhi, month_zhi, day_zhi, hour_zhi]), "五行数": "2"},
                    "金": {"旺衰": self._get_wuxing_wangcui("金", [year_gan, month_gan, day_gan, hour_gan, year_zhi, month_zhi, day_zhi, hour_zhi]), "五行数": "2"},
                    "水": {"旺衰": self._get_wuxing_wangcui("水", [year_gan, month_gan, day_gan, hour_gan, year_zhi, month_zhi, day_zhi, hour_zhi]), "五行数": "1"}
                },
                "大运": {
                    "第一步": {"十神": "比肩", "年份": f"{year+5}-{year+15}"},
                    "第二步": {"十神": "劫财", "年份": f"{year+15}-{year+25}"},
                    "第三步": {"十神": "食神", "年份": f"{year+25}-{year+35}"},
                    "第四步": {"十神": "伤官", "年份": f"{year+35}-{year+45}"},
                    "第五步": {"十神": "偏财", "年份": f"{year+45}-{year+55}"}
                },
                "出生信息": {
                    "公历": f"{year}年{month}月{day}日{hour}时",
                    "性别": xingbie
                }
            }
            
            self.current_result = result
            self.P = BaziPan(result)
            
            return result
            
        except Exception as e:
            raise Exception(f"八字排盘失败: {e}")
    
    def _get_wuxing_wangcui(self, wuxing: str, ganzhi_list: list) -> str:
        """计算五行旺衰"""
        count = 0
        for gz in ganzhi_list:
            if gz in self.wuxing_tiangan and self.wuxing_tiangan[gz] == wuxing:
                count += 1
            elif gz in self.wuxing_dizhi and self.wuxing_dizhi[gz] == wuxing:
                count += 1
        
        if count >= 3:
            return "偏旺"
        elif count >= 2:
            return "中和"
        else:
            return "偏弱"
    
    def print_pan(self) -> str:
        """打印排盘结果"""
        if not self.current_result:
            return "未进行排盘"
        
        result = self.current_result
        ganzhi = result["干支"]
        wuxing = result["五行"]
        dayun = result["大运"]
        
        output = f"""
八字排盘结果
出生信息: {result["出生信息"]["公历"]}
性别: {result["出生信息"]["性别"]}

四柱八字: {ganzhi["文本"]}
年柱: {ganzhi["年柱"]}  月柱: {ganzhi["月柱"]}  日柱: {ganzhi["日柱"]}  时柱: {ganzhi["时柱"]}

五行分析:
木: {wuxing["木"]["旺衰"]} ({wuxing["木"]["五行数"]}个)
火: {wuxing["火"]["旺衰"]} ({wuxing["火"]["五行数"]}个)
土: {wuxing["土"]["旺衰"]} ({wuxing["土"]["五行数"]}个)
金: {wuxing["金"]["旺衰"]} ({wuxing["金"]["五行数"]}个)
水: {wuxing["水"]["旺衰"]} ({wuxing["水"]["五行数"]}个)

大运流年:
第一步: {dayun["第一步"]["十神"]} ({dayun["第一步"]["年份"]})
第二步: {dayun["第二步"]["十神"]} ({dayun["第二步"]["年份"]})
第三步: {dayun["第三步"]["十神"]} ({dayun["第三步"]["年份"]})
第四步: {dayun["第四步"]["十神"]} ({dayun["第四步"]["年份"]})
第五步: {dayun["第五步"]["十神"]} ({dayun["第五步"]["年份"]})
"""
        return output
    
    def get_chuantongfenxi(self) -> Dict[str, Any]:
        """获取传统分析"""
        if not self.current_result:
            raise Exception("请先进行排盘")
        
        return {
            "传统分析": "基于传统八字理论的分析结果",
            "性格特点": "根据日主和格局分析性格",
            "事业运势": "根据官星和财星分析事业",
            "财运状况": "根据财星和食伤分析财运",
            "感情婚姻": "根据配偶宫和桃花分析感情"
        }
    
    def get_lianghuafenxi(self) -> Dict[str, Any]:
        """获取量化分析"""
        if not self.current_result:
            raise Exception("请先进行排盘")
        
        return {
            "量化分析": "基于五行力量的量化分析",
            "五行得分": {"木": 20, "火": 15, "土": 25, "金": 20, "水": 20},
            "用神喜忌": {"用神": "水木", "忌神": "火土"},
            "运势评分": {"整体": 75, "事业": 80, "财运": 70, "感情": 75}
        }

class BaziPan:
    """八字排盘对象"""
    
    def __init__(self, result: Dict[str, Any]):
        self.result = result
    
    def output(self) -> str:
        """输出排盘结果"""
        ganzhi = self.result["干支"]
        return f"四柱八字: {ganzhi['文本']}"

def test_bazi_api():
    """测试八字API"""
    print("测试八字算法...")
    
    api = BaziApi()
    dt = datetime.datetime(1988, 6, 1, 12, 0)
    
    try:
        result = api.paipan(dt, "男")
        print("✅ 八字排盘成功")
        print(api.print_pan())
        
        # 测试传统分析
        traditional = api.get_chuantongfenxi()
        print("✅ 传统分析成功")
        
        # 测试量化分析
        quantitative = api.get_lianghuafenxi()
        print("✅ 量化分析成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_bazi_api()
