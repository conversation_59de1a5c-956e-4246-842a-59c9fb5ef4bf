#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具选择器测试 - 阶段2.3测试
"""

import sys
import os
sys.path.append('.')

def test_tool_selector_basic():
    """测试工具选择器基本功能"""
    print("🛠️ 测试工具选择器基本功能")
    print("-" * 50)
    
    try:
        from core.tools.tool_selector import ToolSelector
        
        # 创建工具选择器
        selector = ToolSelector()
        print("✅ 工具选择器创建成功")
        
        # 查看可用工具
        tools = selector.get_available_tools()
        print(f"✅ 可用工具: {len(tools)} 个")
        for intent, description in tools.items():
            print(f"  - {intent}: {description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具选择器基本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selection():
    """测试工具选择逻辑"""
    print("\n🎯 测试工具选择逻辑")
    print("-" * 50)
    
    try:
        from core.tools.tool_selector import ToolSelector
        
        selector = ToolSelector()
        
        # 测试用例
        test_cases = [
            {
                "name": "紫薇斗数 - 完整信息",
                "intent_result": {
                    "intent": "ziwei",
                    "confidence": 0.9,
                    "entities": {
                        "birth_year": "1988",
                        "birth_month": "6", 
                        "birth_day": "1",
                        "birth_hour": "午时",
                        "gender": "男"
                    }
                },
                "context": {},
                "expected_success": True
            },
            {
                "name": "八字算命 - 缺少信息",
                "intent_result": {
                    "intent": "bazi",
                    "confidence": 0.8,
                    "entities": {
                        "birth_year": "1990"
                    }
                },
                "context": {},
                "expected_success": True,  # 应该返回实体收集请求
                "expected_type": "entity_collection"
            },
            {
                "name": "六爻占卜 - 无需出生信息",
                "intent_result": {
                    "intent": "liuyao",
                    "confidence": 0.85,
                    "entities": {}
                },
                "context": {},
                "expected_success": True
            },
            {
                "name": "普通聊天",
                "intent_result": {
                    "intent": "chat",
                    "confidence": 0.9,
                    "entities": {}
                },
                "context": {},
                "expected_success": True
            },
            {
                "name": "低置信度意图",
                "intent_result": {
                    "intent": "ziwei",
                    "confidence": 0.3,  # 低于阈值
                    "entities": {}
                },
                "context": {},
                "expected_success": True,
                "expected_type": "clarification"
            }
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            name = test_case["name"]
            intent_result = test_case["intent_result"]
            context = test_case["context"]
            expected_success = test_case["expected_success"]
            expected_type = test_case.get("expected_type")
            
            print(f"\n测试 {i}/{total_count}: {name}")
            print(f"意图: {intent_result['intent']} (置信度: {intent_result['confidence']})")
            
            # 执行工具选择
            result = selector.select_tool(intent_result, context)
            
            print(f"选择结果: {result.get('success', False)}")
            print(f"工具名称: {result.get('tool_name', 'N/A')}")
            
            if result.get("result"):
                result_data = result["result"]
                print(f"结果类型: {result_data.get('type', 'N/A')}")
                if "message" in result_data:
                    print(f"消息: {result_data['message'][:100]}...")
            
            # 验证结果
            is_correct = result.get("success") == expected_success
            if expected_type:
                actual_type = result.get("result", {}).get("type")
                is_correct = is_correct and actual_type == expected_type
            
            if is_correct:
                print("✅ 测试通过")
                success_count += 1
            else:
                print("❌ 测试失败")
        
        print(f"\n📊 工具选择测试结果: {success_count}/{total_count} 通过")
        return success_count >= total_count * 0.8  # 80%通过率
        
    except Exception as e:
        print(f"❌ 工具选择测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_algorithms():
    """测试与算法的集成"""
    print("\n🔗 测试与算法集成")
    print("-" * 50)
    
    try:
        from core.tools.tool_selector import ToolSelector
        
        selector = ToolSelector()
        
        # 测试紫薇斗数集成
        print("测试紫薇斗数算法集成...")
        ziwei_intent = {
            "intent": "ziwei",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1", 
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        result = selector.select_tool(ziwei_intent, {})
        
        if result.get("success"):
            print("✅ 紫薇斗数工具调用成功")
            tool_result = result.get("result", {})
            if tool_result.get("type") == "ziwei_analysis":
                print("✅ 紫薇斗数计算完成")
                if "calculation_result" in tool_result:
                    calc_result = tool_result["calculation_result"]
                    print(f"✅ 计算结果包含: {list(calc_result.keys()) if isinstance(calc_result, dict) else type(calc_result)}")
            else:
                print(f"⚠️ 结果类型: {tool_result.get('type')}")
        else:
            print("❌ 紫薇斗数工具调用失败")
            print(f"错误: {result.get('error', 'Unknown')}")
        
        # 测试六爻占卜集成
        print("\n测试六爻占卜算法集成...")
        liuyao_intent = {
            "intent": "liuyao",
            "confidence": 0.85,
            "entities": {}
        }
        
        result = selector.select_tool(liuyao_intent, {})
        
        if result.get("success"):
            print("✅ 六爻占卜工具调用成功")
            tool_result = result.get("result", {})
            if tool_result.get("type") == "liuyao_analysis":
                print("✅ 六爻占卜计算完成")
            else:
                print(f"⚠️ 结果类型: {tool_result.get('type')}")
        else:
            print("❌ 六爻占卜工具调用失败")
            print(f"错误: {result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_flow():
    """测试端到端流程"""
    print("\n🚀 测试端到端流程")
    print("-" * 50)
    
    try:
        from core.nlu.llm_client import LLMClient
        from core.tools.tool_selector import ToolSelector
        from core.chat.session_manager import SessionManager
        
        # 创建组件
        llm_client = LLMClient()
        tool_selector = ToolSelector()
        session_manager = SessionManager()
        
        session_id = "end_to_end_test"
        
        # 模拟完整对话流程
        test_message = "我1988年6月1日午时出生，男，想看紫薇斗数"
        
        print(f"用户消息: {test_message}")
        
        # 1. 意图识别
        print("\n1️⃣ 意图识别...")
        context = session_manager.get_conversation_context(session_id)
        intent_result = llm_client.intent_recognition(test_message, context)
        
        if intent_result and intent_result.get("intent") != "error":
            print(f"✅ 意图: {intent_result['intent']} (置信度: {intent_result['confidence']:.2f})")
            print(f"✅ 实体: {intent_result.get('entities', {})}")
            
            # 2. 工具选择
            print("\n2️⃣ 工具选择...")
            tool_result = tool_selector.select_tool(intent_result, context)
            
            if tool_result.get("success"):
                print(f"✅ 工具: {tool_result['tool_name']}")
                print(f"✅ 描述: {tool_result['tool_description']}")
                
                # 3. 检查结果
                result_data = tool_result.get("result", {})
                result_type = result_data.get("type", "unknown")
                
                print(f"\n3️⃣ 执行结果: {result_type}")
                
                if result_type == "ziwei_analysis":
                    print("✅ 紫薇斗数分析完成")
                    if "calculation_result" in result_data:
                        print("✅ 包含计算结果")
                elif result_type == "entity_collection":
                    print("✅ 实体收集请求")
                    print(f"消息: {result_data.get('message', '')}")
                else:
                    print(f"结果: {result_data.get('message', 'No message')}")
                
                # 4. 更新会话
                print("\n4️⃣ 更新会话...")
                message_record = {
                    "user_message": test_message,
                    "intent": intent_result,
                    "tool_result": tool_result,
                    "timestamp": "now"
                }
                
                context_updates = {}
                entities = intent_result.get("entities", {})
                if entities:
                    birth_info = {}
                    for key, value in entities.items():
                        if key.startswith("birth_") and value:
                            birth_info[key.replace("birth_", "")] = value
                        elif key == "gender" and value:
                            birth_info["gender"] = value
                    
                    if birth_info:
                        context_updates["birth_info"] = birth_info
                
                session_manager.update_session(session_id, context_updates, message_record)
                print("✅ 会话更新完成")
                
                return True
            else:
                print(f"❌ 工具选择失败: {tool_result.get('error', 'Unknown')}")
                return False
        else:
            print("❌ 意图识别失败")
            return False
            
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 阶段2.3：智能工具选择器测试")
    print("=" * 70)
    
    # 测试结果
    results = []
    
    # 1. 基本功能测试
    results.append(("工具选择器基本功能", test_tool_selector_basic()))
    
    # 2. 工具选择逻辑测试
    results.append(("工具选择逻辑", test_tool_selection()))
    
    # 3. 算法集成测试
    results.append(("算法集成", test_integration_with_algorithms()))
    
    # 4. 端到端流程测试
    results.append(("端到端流程", test_end_to_end_flow()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 阶段2.3测试全部通过！智能工具选择器工作正常！")
        print("\n🎯 完成功能:")
        print("  ✅ 智能工具选择和路由")
        print("  ✅ 实体完整性检查")
        print("  ✅ 算法工具集成")
        print("  ✅ 端到端对话流程")
        print("\n📋 下一步：开始阶段3 - 工具层重构")
    else:
        print("💥 部分测试失败，需要修复问题后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
