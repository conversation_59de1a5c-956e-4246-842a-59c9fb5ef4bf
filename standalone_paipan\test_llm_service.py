#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM服务
"""

from llm_service import LLMService

def test_llm_service():
    """测试LLM服务"""
    print("🧪 测试LLM服务")
    
    # 初始化LLM服务
    llm = LLMService()
    
    # 测试简单对话
    messages = [
        {"role": "system", "content": "你是一个测试助手，请简短回答问题。"},
        {"role": "user", "content": "你好，请说一句话测试一下。"}
    ]
    
    print("🔄 发送测试消息...")
    response = llm.chat_completion(messages, temperature=0.3, max_tokens=100)
    
    if response:
        print(f"✅ LLM测试成功")
        print(f"📝 回复内容: {response}")
        print(f"🎯 使用模型: {llm.model_name}")
        return True
    else:
        print("❌ LLM测试失败")
        return False

if __name__ == "__main__":
    test_llm_service()
