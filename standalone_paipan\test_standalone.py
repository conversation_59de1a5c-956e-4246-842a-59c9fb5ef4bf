#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立排盘模块测试脚本
"""

import sys
import os

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from simple_interface import SimplePaipanInterface

def test_basic_calculation():
    """测试基本排盘计算"""
    print("🧪 测试1: 基本排盘计算")
    print("-" * 40)
    
    interface = SimplePaipanInterface()
    
    # 测试数据
    test_cases = [
        (1990, 3, 15, 8, "女"),   # 1990年3月15日8时 女
        (1988, 6, 1, 12, "男"),   # 1988年6月1日12时 男
        (1995, 10, 20, 18, "女"), # 1995年10月20日18时 女
    ]
    
    for i, (year, month, day, hour, gender) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {year}年{month}月{day}日{hour}时 {gender}")
        try:
            result = interface.calculate_and_save(year, month, day, hour, gender)
            
            if result["success"]:
                print(f"✅ 计算成功: {result['status']}")
                saved_files = result.get("saved_files", {})
                print(f"📁 保存文件数: {len(saved_files)}")
            else:
                print(f"❌ 计算失败")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def test_quick_calculation():
    """测试快速计算功能"""
    print("\n🧪 测试2: 快速计算功能")
    print("-" * 40)
    
    interface = SimplePaipanInterface()
    
    # 测试不同的时间格式
    test_strings = [
        "1990-3-15-8",
        "1988年6月1日12时",
        "1995-10-20-18",
        "2000年1月1日0时"
    ]
    
    for i, birth_string in enumerate(test_strings, 1):
        print(f"\n测试字符串 {i}: '{birth_string}'")
        try:
            result = interface.quick_calculate(birth_string, "男")
            
            if result["success"]:
                print(f"✅ 解析和计算成功")
            else:
                print(f"❌ 失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试3: 错误处理")
    print("-" * 40)
    
    interface = SimplePaipanInterface()
    
    # 测试无效输入
    invalid_cases = [
        (1800, 1, 1, 12, "男"),    # 年份过早
        (2200, 1, 1, 12, "男"),    # 年份过晚
        (1990, 13, 1, 12, "男"),   # 月份无效
        (1990, 1, 32, 12, "男"),   # 日期无效
        (1990, 1, 1, 25, "男"),    # 小时无效
    ]
    
    for i, (year, month, day, hour, gender) in enumerate(invalid_cases, 1):
        print(f"\n无效测试 {i}: {year}年{month}月{day}日{hour}时")
        try:
            result = interface.calculate_and_save(year, month, day, hour, gender)
            
            if not result["success"]:
                print(f"✅ 正确识别无效输入")
            else:
                print(f"⚠️ 意外成功了")
                
        except Exception as e:
            print(f"✅ 正确抛出异常: {e}")

def test_file_operations():
    """测试文件操作"""
    print("\n🧪 测试4: 文件操作")
    print("-" * 40)
    
    interface = SimplePaipanInterface()
    
    # 执行一次计算以生成文件
    print("生成测试文件...")
    result = interface.calculate_and_save(1990, 3, 15, 8, "女")
    
    if result["success"]:
        print("✅ 测试文件生成成功")
        
        # 测试文件列表功能
        print("\n测试文件列表功能:")
        interface.list_saved_results()
        
    else:
        print("❌ 测试文件生成失败")

def run_all_tests():
    """运行所有测试"""
    print("🔮 独立排盘模块测试")
    print("=" * 60)
    
    try:
        test_basic_calculation()
        test_quick_calculation()
        test_error_handling()
        test_file_operations()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
