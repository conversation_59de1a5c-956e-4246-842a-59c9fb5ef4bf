<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紫薇斗数排盘管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        /* 顶部导航栏 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .navbar-brand {
            font-size: 1.5em;
            font-weight: bold;
            display: flex;
            align-items: center;
        }

        .navbar-brand .icon {
            margin-right: 10px;
            font-size: 1.8em;
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.2);
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 250px;
            height: calc(100vh - 60px);
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 999;
        }

        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
        }

        .menu-item {
            margin-bottom: 5px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-link:hover,
        .menu-link.active {
            background: #f8f9fa;
            color: #667eea;
            border-left-color: #667eea;
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 1.2em;
            width: 20px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 250px;
            margin-top: 60px;
            padding: 30px;
            min-height: calc(100vh - 60px);
        }

        /* 页面标题 */
        .page-header {
            background: white;
            padding: 20px 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .page-subtitle {
            color: #666;
            font-size: 1em;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e1e5e9;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .card-body {
            padding: 30px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 0.9em;
            color: #27ae60;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1em;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }

        .btn-info {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 加载动画 */
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 内容区域 */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* 表单样式 */
        .admin-form {
            max-width: 800px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 0.95em;
        }

        .form-group input,
        .form-group select {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-start;
            margin-top: 20px;
        }

        .search-form .form-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .calculate-result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
        }

        .result-success {
            background: #d4edda;
            border-left-color: #27ae60;
            color: #155724;
        }

        .result-error {
            background: #f8d7da;
            border-left-color: #e74c3c;
            color: #721c24;
        }

        .card-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .record-count {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.85em;
            border-radius: 6px;
        }

        .btn-view {
            background: linear-gradient(45deg, #3498db, #2980b9);
        }

        .btn-delete {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        /* 分析管理样式 */
        .record-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            border-left: 4px solid #667eea;
        }

        .angles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .angle-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .angle-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .angle-card.completed {
            border-color: #27ae60;
            background: #f8fff9;
        }

        .angle-card.analyzing {
            border-color: #f39c12;
            background: #fffbf0;
        }

        .angle-card.failed {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .angle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .angle-title {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }

        .angle-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-pending {
            background: #e9ecef;
            color: #6c757d;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-analyzing {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        /* Badge样式 */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-align: center;
        }

        .badge-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .badge-secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            color: white;
        }

        .angle-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .angle-info {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        /* 聊天界面样式 */
        .chat-container {
            max-width: 1600px; /* 进一步增加宽度 */
            margin: 0 auto;
            width: 100%;
        }

        .chat-messages {
            height: 650px; /* 稍微增加高度 */
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        /* 对话组卡片样式 */
        .conversation-group {
            background: white;
            border-radius: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .conversation-group:hover {
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
            transform: translateY(-1px);
        }

        .conversation-group::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 20px 20px 0 0;
        }

        /* 对话组头部 */
        .conversation-header {
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }

        .conversation-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .conversation-preview {
            flex: 1;
            margin-right: 15px;
        }

        .conversation-question {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 14px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-answer-preview {
            color: #7f8c8d;
            font-size: 12px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
            color: #999;
        }

        .conversation-timestamp {
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 8px;
            border-radius: 10px;
        }

        .conversation-toggle {
            background: none;
            border: none;
            font-size: 16px;
            color: #667eea;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .conversation-toggle:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.1);
        }

        .conversation-toggle.expanded {
            transform: rotate(180deg);
        }

        /* 对话组内容 */
        .conversation-content {
            padding: 20px;
            display: none;
        }

        .conversation-content.expanded {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                padding-top: 0;
                padding-bottom: 0;
            }
            to {
                opacity: 1;
                max-height: 1000px;
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }

        .chat-message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .chat-message:last-child {
            margin-bottom: 0;
        }

        .chat-message.user {
            justify-content: flex-end;
            margin-bottom: 15px;
        }

        .chat-message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 90%; /* 进一步增加消息宽度 */
            padding: 20px 25px;
            border-radius: 20px;
            line-height: 1.8;
            font-size: 15px;
            word-wrap: break-word;
            white-space: pre-wrap;
            position: relative;
        }

        .chat-message.user .message-content {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-bottom-right-radius: 8px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            margin-left: auto;
        }

        .chat-message.user .message-content::before {
            content: '👤';
            position: absolute;
            top: -8px;
            right: 15px;
            background: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-message.assistant .message-content {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #333;
            border: 2px solid rgba(102, 126, 234, 0.1);
            border-bottom-left-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }

        .chat-message.assistant .message-content::before {
            content: '🤖';
            position: absolute;
            top: -8px;
            left: 15px;
            background: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 助手回复内容格式化 */
        .chat-message.assistant .message-content {
            /* 段落间距 */
        }

        .chat-message.assistant .message-content p {
            margin-bottom: 12px;
            line-height: 1.8;
        }

        .chat-message.assistant .message-content h1,
        .chat-message.assistant .message-content h2,
        .chat-message.assistant .message-content h3 {
            color: #2c3e50;
            margin: 20px 0 10px 0;
            font-weight: 600;
        }

        .chat-message.assistant .message-content ul,
        .chat-message.assistant .message-content ol {
            margin: 15px 0;
            padding-left: 25px;
        }

        .chat-message.assistant .message-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .chat-message.assistant .message-content strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .chat-message.assistant .message-content em {
            color: #7f8c8d;
            font-style: italic;
        }

        .input-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .input-group .form-control {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: white;
        }

        .input-group .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-group .btn {
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            min-width: 100px;
        }

        .chat-typing {
            color: #666;
            font-style: italic;
            padding: 15px 20px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        /* 聊天输入区域 */
        .chat-input-area {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e1e5e9;
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 对话组动画效果 */
        .conversation-group {
            animation: slideInUp 0.3s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .chat-container {
                max-width: 1200px;
            }
        }

        @media (max-width: 1200px) {
            .chat-container {
                max-width: 1000px;
            }

            .message-content {
                max-width: 95%;
            }
        }

        @media (max-width: 768px) {
            .chat-container {
                max-width: 100%;
                padding: 0 15px;
            }

            .chat-messages {
                height: 500px;
                padding: 20px;
            }

            .conversation-group {
                padding: 20px;
                margin-bottom: 20px;
            }

            .message-content {
                padding: 15px 18px;
                font-size: 14px;
            }
        }

        /* 合盘分析样式 */
        .compatibility-form {
            max-width: 100%;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 25px;
        }

        .form-column {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .form-column h4 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .compatibility-form .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .compatibility-form .form-group {
            margin-bottom: 20px;
        }

        .compatibility-form .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .compatibility-form .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .compatibility-form .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .compatibility-form .form-actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 25px;
            border-top: 2px solid rgba(102, 126, 234, 0.1);
        }

        .compatibility-form .btn {
            margin: 0 10px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .compatibility-form .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .compatibility-form .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* 合盘记录表格样式 */
        .person-info {
            font-size: 13px;
            line-height: 1.4;
        }

        .person-info strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .person-info .gender {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
            margin-top: 3px;
        }

        .person-info .gender {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .compatibility-result {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            line-height: 1.8;
            font-size: 16px;
        }

        .compatibility-result .result-meta {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }

        .compatibility-result .result-meta p {
            margin: 8px 0;
            color: #495057;
        }

        .compatibility-result .result-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .compatibility-result .analysis-text {
            color: #2c3e50;
            line-height: 2.0;
            font-size: 16px;
            text-align: justify;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .compatibility-result .analysis-text h1,
        .compatibility-result .analysis-text h2,
        .compatibility-result .analysis-text h3,
        .compatibility-result .analysis-text h4 {
            color: #2c3e50;
            margin: 30px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e8f4fd;
            font-weight: 600;
        }

        .compatibility-result .analysis-text h1 {
            font-size: 24px;
            text-align: center;
            color: #667eea;
            margin-bottom: 35px;
            border-bottom: 3px solid #667eea;
        }

        .compatibility-result .analysis-text h2 {
            font-size: 20px;
            color: #4a90e2;
            margin-top: 35px;
        }

        .compatibility-result .analysis-text h3 {
            font-size: 18px;
            color: #5a6c7d;
            margin-top: 25px;
        }

        .compatibility-result .analysis-text h4 {
            font-size: 16px;
            color: #6c757d;
            margin-top: 20px;
        }

        .compatibility-result .analysis-text p {
            margin: 18px 0;
            text-indent: 2em;
            line-height: 2.0;
        }

        .compatibility-result .analysis-text ul,
        .compatibility-result .analysis-text ol {
            margin: 20px 0;
            padding-left: 30px;
        }

        .compatibility-result .analysis-text li {
            margin: 10px 0;
            line-height: 1.8;
        }

        .compatibility-result .analysis-text strong {
            color: #e74c3c;
            font-weight: 600;
        }

        .compatibility-result .analysis-text em {
            color: #8e44ad;
            font-style: normal;
            background: rgba(142, 68, 173, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .compatibility-result .analysis-text blockquote {
            background: rgba(102, 126, 234, 0.05);
            border-left: 4px solid #667eea;
            margin: 20px 0;
            padding: 15px 20px;
            font-style: italic;
            color: #495057;
        }

        /* 优化数字和特殊符号的显示 */
        .compatibility-result .analysis-text {
            font-feature-settings: "kern" 1, "liga" 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 优化引用和重点内容的显示 */
        .compatibility-result .analysis-text .highlight {
            background: linear-gradient(120deg, rgba(255, 235, 59, 0.3) 0%, rgba(255, 235, 59, 0.1) 100%);
            padding: 2px 4px;
            border-radius: 3px;
        }

        /* 分隔线样式 */
        .compatibility-result .analysis-text hr {
            border: none;
            height: 2px;
            background: linear-gradient(to right, transparent, #667eea, transparent);
            margin: 30px 0;
        }

        /* 表格样式（如果有的话） */
        .compatibility-result .analysis-text table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .compatibility-result .analysis-text th,
        .compatibility-result .analysis-text td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .compatibility-result .analysis-text th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .compatibility-result {
                padding: 20px;
                font-size: 15px;
            }

            .compatibility-result .analysis-text {
                font-size: 15px;
                line-height: 1.8;
            }

            .compatibility-result .analysis-text h1 {
                font-size: 20px;
            }

            .compatibility-result .analysis-text h2 {
                font-size: 18px;
            }

            .compatibility-result .analysis-text h3 {
                font-size: 16px;
            }

            .compatibility-result .analysis-text p {
                text-indent: 1.5em;
            }
        }

        /* 六爻占卜样式 */
        .liuyao-form {
            max-width: 100%;
        }

        .coins-container {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
        }

        .coins-instruction {
            text-align: center;
            margin-bottom: 20px;
            color: #495057;
            font-size: 14px;
        }

        .coin-throws {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .throw-row {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .throw-row label {
            min-width: 80px;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .coin-buttons {
            display: flex;
            gap: 8px;
        }

        .coin-btn {
            width: 50px;
            height: 50px;
            border: 2px solid #667eea;
            border-radius: 50%;
            background: white;
            color: #667eea;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .coin-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .coin-btn.heads {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .coin-btn.tails {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-color: #e74c3c;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
        }

        .coin-btn.flipping {
            animation: coinFlip 0.6s ease-in-out;
        }

        @keyframes coinFlip {
            0% { transform: rotateY(0deg) scale(1); }
            25% { transform: rotateY(90deg) scale(1.1); }
            50% { transform: rotateY(180deg) scale(1.2); }
            75% { transform: rotateY(270deg) scale(1.1); }
            100% { transform: rotateY(360deg) scale(1); }
        }

        .coin-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .coin-btn:hover::before {
            opacity: 1;
        }

        .throw-result {
            min-width: 120px;
            font-weight: 600;
            color: #495057;
        }

        .coin-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .liuyao-result {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            line-height: 1.8;
            font-size: 16px;
        }

        .liuyao-result .result-meta {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }

        .liuyao-result .result-meta p {
            margin: 8px 0;
            color: #495057;
        }

        .liuyao-result .result-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .liuyao-result .hexagram-display {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.8;
            white-space: pre-wrap;
            margin: 20px 0;
            border: 1px solid #34495e;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .hexagram-lines {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .hexagram-line {
            display: flex;
            align-items: center;
            margin: 5px 0;
            font-size: 18px;
            font-family: 'Courier New', monospace;
        }

        .line-symbol {
            margin-right: 15px;
            color: #f39c12;
        }

        .line-info {
            color: #ecf0f1;
            font-size: 14px;
        }

        .moving-line {
            color: #e74c3c;
            font-weight: bold;
        }

        .hexagram-name {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #f39c12;
            margin: 15px 0;
            padding: 10px;
            border: 2px solid #f39c12;
            border-radius: 8px;
            background: rgba(243, 156, 18, 0.1);
        }

        .liuyao-result .analysis-text {
            color: #2c3e50;
            line-height: 2.0;
            font-size: 16px;
            text-align: justify;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 响应式优化 - 六爻 */
        @media (max-width: 768px) {
            .coin-buttons {
                gap: 5px;
            }

            .coin-btn {
                width: 40px;
                height: 40px;
                font-size: 12px;
            }

            .throw-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .throw-row label {
                min-width: auto;
            }

            .coin-actions {
                flex-direction: column;
            }
        }

        .result-meta {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .result-meta p {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .result-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .analysis-text {
            line-height: 1.8;
            color: #333;
            font-size: 15px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .compatibility-form .form-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .form-column {
                padding: 20px;
            }

            .compatibility-form .btn {
                display: block;
                width: 100%;
                margin: 10px 0;
            }

            .person-info {
                font-size: 12px;
            }

            .table-container {
                overflow-x: auto;
            }
        }

        /* 响应式表格 */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .table-container {
                overflow-x: auto;
            }

            .table {
                min-width: 600px;
            }

            .angles-grid {
                grid-template-columns: 1fr;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">
            <span class="icon">🔮</span>
            紫薇斗数排盘管理系统
        </div>
        <ul class="navbar-nav">
            <li><a href="/" class="nav-link">🏠 前台首页</a></li>
            <li><a href="/debug" class="nav-link">🔍 数据调试</a></li>
            <li><a href="#" class="nav-link">👤 管理员</a></li>
        </ul>
    </nav>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li class="menu-item">
                <a href="#" class="menu-link active" onclick="showSection('dashboard', event)">
                    <span class="menu-icon">📊</span>
                    仪表盘
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-link" onclick="showSection('paipan', event)">
                    <span class="menu-icon">🔮</span>
                    新建排盘
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-link" onclick="showSection('records', event)">
                    <span class="menu-icon">📋</span>
                    记录管理
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-link" onclick="showSection('compatibility', event)">
                    <span class="menu-icon">💕</span>
                    合盘分析
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-link" onclick="showSection('liuyao', event)">
                    <span class="menu-icon">🔮</span>
                    六爻占卜
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-link" onclick="showSection('statistics', event)">
                    <span class="menu-icon">📈</span>
                    数据统计
                </a>
            </li>
            <li class="menu-item">
                <a href="#" class="menu-link" onclick="showSection('settings', event)">
                    <span class="menu-icon">⚙️</span>
                    系统设置
                </a>
            </li>
        </ul>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 仪表盘 -->
        <section id="dashboard" class="content-section active">
            <div class="page-header">
                <h1 class="page-title">仪表盘</h1>
                <p class="page-subtitle">系统概览和快速操作</p>
            </div>

            <div class="stats-grid" id="statsGrid">
                <div class="loading">
                    <div class="spinner"></div>
                    加载统计数据中...
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">快速操作</h3>
                </div>
                <div class="card-body">
                    <a href="#" class="btn btn-success" onclick="showSection('paipan')">🔮 新建排盘</a>
                    <a href="#" class="btn btn-info" onclick="showSection('records')">📋 查看记录</a>
                    <a href="#" class="btn btn-warning" onclick="showSection('statistics')">📈 数据分析</a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">最近排盘记录</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table" id="recentTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>出生信息</th>
                                    <th>性别</th>
                                    <th>计算时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="6" class="loading">
                                        <div class="spinner"></div>
                                        加载最近记录中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- 新建排盘 -->
        <section id="paipan" class="content-section">
            <div class="page-header">
                <h1 class="page-title">新建排盘</h1>
                <p class="page-subtitle">输入出生信息进行紫薇斗数排盘</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">排盘信息录入</h3>
                </div>
                <div class="card-body">
                    <form id="adminPaipanForm" class="admin-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="adminYear">出生年份</label>
                                <input type="number" id="adminYear" name="year" min="1900" max="2030" required>
                            </div>
                            <div class="form-group">
                                <label for="adminMonth">出生月份</label>
                                <select id="adminMonth" name="month" required>
                                    <option value="">选择月份</option>
                                    <option value="1">1月</option>
                                    <option value="2">2月</option>
                                    <option value="3">3月</option>
                                    <option value="4">4月</option>
                                    <option value="5">5月</option>
                                    <option value="6">6月</option>
                                    <option value="7">7月</option>
                                    <option value="8">8月</option>
                                    <option value="9">9月</option>
                                    <option value="10">10月</option>
                                    <option value="11">11月</option>
                                    <option value="12">12月</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="adminDay">出生日期</label>
                                <input type="number" id="adminDay" name="day" min="1" max="31" required>
                            </div>
                            <div class="form-group">
                                <label for="adminHour">出生时辰</label>
                                <select id="adminHour" name="hour" required>
                                    <option value="">选择时辰</option>
                                    <option value="0">子时 (23:00-01:00)</option>
                                    <option value="1">丑时 (01:00-03:00)</option>
                                    <option value="3">寅时 (03:00-05:00)</option>
                                    <option value="5">卯时 (05:00-07:00)</option>
                                    <option value="7">辰时 (07:00-09:00)</option>
                                    <option value="9">巳时 (09:00-11:00)</option>
                                    <option value="11">午时 (11:00-13:00)</option>
                                    <option value="13">未时 (13:00-15:00)</option>
                                    <option value="15">申时 (15:00-17:00)</option>
                                    <option value="17">酉时 (17:00-19:00)</option>
                                    <option value="19">戌时 (19:00-21:00)</option>
                                    <option value="21">亥时 (21:00-23:00)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="adminGender">性别</label>
                                <select id="adminGender" name="gender" required>
                                    <option value="">选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="adminRemark">备注信息</label>
                                <input type="text" id="adminRemark" name="remark" placeholder="可选，如姓名或其他备注">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success" id="adminCalculateBtn">
                                🔮 开始排盘计算
                            </button>
                            <button type="reset" class="btn" style="background: #95a5a6;">
                                🔄 重置表单
                            </button>
                        </div>
                    </form>

                    <div id="adminCalculateResult" class="calculate-result" style="display: none;">
                        <!-- 计算结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 记录管理（合并排盘记录和分析管理） -->
        <section id="records" class="content-section">
            <div class="page-header">
                <h1 class="page-title">记录管理</h1>
                <p class="page-subtitle">排盘记录查看、分析管理和知识库互动</p>
            </div>

            <!-- 搜索筛选 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔍 记录搜索</h3>
                </div>
                <div class="card-body">
                    <form id="recordSearchForm" class="search-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="searchGender">性别筛选</label>
                                <select id="searchGender" name="gender">
                                    <option value="">全部性别</option>
                                    <option value="男">男性</option>
                                    <option value="女">女性</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="searchYear">出生年份</label>
                                <input type="number" id="searchYear" name="birth_year" placeholder="如：1990" min="1900" max="2030">
                            </div>
                            <div class="form-group">
                                <label for="searchStatus">计算状态</label>
                                <select id="searchStatus" name="success">
                                    <option value="">全部状态</option>
                                    <option value="true">计算成功</option>
                                    <option value="false">计算失败</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="searchLimit">显示数量</label>
                                <select id="searchLimit" name="limit">
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                    <option value="200">200条</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-info">🔍 搜索记录</button>
                            <button type="reset" class="btn" onclick="loadAllRecords()">🔄 重置筛选</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 记录列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📋 记录列表</h3>
                    <div class="card-actions">
                        <span id="recordCount" class="record-count">加载中...</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table" id="recordsTable">
                            <thead>
                                <tr>
                                    <th>记录ID</th>
                                    <th>出生信息</th>
                                    <th>性别</th>
                                    <th>生肖星座</th>
                                    <th>计算时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="recordsTableBody">
                                <tr>
                                    <td colspan="7" class="loading">
                                        <div class="spinner"></div>
                                        正在加载记录...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 选中记录的详细信息和分析 -->
            <div id="selectedRecordSection" class="card" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">🎯 记录详情与分析</h3>
                    <div class="card-actions">
                        <button class="btn btn-small" onclick="closeRecordDetail()">✖️ 关闭</button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="selectedRecordInfo" class="record-info">
                        <!-- 选中记录的信息将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 12角度分析 -->
            <div id="angleAnalysisSection" class="card" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">🎯 12角度分析</h3>
                    <div class="card-actions">
                        <span id="analysisProgress" class="record-count">0/12 完成</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="angles-grid" id="anglesGrid">
                        <!-- 12个角度将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 知识库互动 -->
            <div id="chatSection" class="card" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">💬 知识库互动</h3>
                    <div class="card-actions">
                        <button class="btn btn-small" onclick="clearChatHistory()">🗑️ 清空历史</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chat-container">
                        <div class="chat-messages" id="chatMessages">
                            <div class="conversation-group">
                                <div class="conversation-header">
                                    <div class="conversation-preview">
                                        <div class="conversation-question">欢迎使用知识库互动</div>
                                        <div class="conversation-answer-preview">我是您的专属命理分析师，基于排盘数据为您答疑解惑</div>
                                    </div>
                                    <div class="conversation-meta">
                                        <div class="conversation-timestamp">系统</div>
                                    </div>
                                    <button class="conversation-toggle expanded" onclick="toggleConversation(this.closest('.conversation-group'))">▲</button>
                                </div>
                                <div class="conversation-content expanded">
                                    <div class="chat-message assistant">
                                        <div class="message-content">
                                            👋 您好！我是您的专属命理分析师。基于您的排盘数据，我可以回答关于命理、运势、性格等各方面的问题。请随时提问！
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <div class="input-group">
                                <input type="text" id="chatInput" class="form-control" placeholder="请输入您的问题..." onkeypress="if(event.key==='Enter') sendChatMessage()">
                                <button class="btn btn-primary" onclick="sendChatMessage()">💬 发送</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 合盘分析 -->
        <section id="compatibility" class="content-section">
            <div class="page-header">
                <h1 class="page-title">合盘分析</h1>
                <p class="page-subtitle">双人命盘对比分析，评估感情匹配度和合作潜力</p>
            </div>

            <!-- 创建合盘分析 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">💕 创建合盘分析</h3>
                </div>
                <div class="card-body">
                    <form id="compatibilityForm" class="compatibility-form">
                        <div class="form-row">
                            <div class="form-column">
                                <h4>👤 甲方信息</h4>
                                <div class="form-group">
                                    <label for="nameA">姓名/称呼</label>
                                    <input type="text" id="nameA" name="nameA" class="form-control" placeholder="请输入甲方姓名" required>
                                </div>
                                <div class="form-group">
                                    <label for="genderA">性别</label>
                                    <select id="genderA" name="genderA" class="form-control" required>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                </div>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="yearA">出生年份</label>
                                        <input type="number" id="yearA" name="yearA" class="form-control" min="1900" max="2030" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="monthA">月份</label>
                                        <input type="number" id="monthA" name="monthA" class="form-control" min="1" max="12" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="dayA">日期</label>
                                        <input type="number" id="dayA" name="dayA" class="form-control" min="1" max="31" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="hourA">出生时辰</label>
                                    <select id="hourA" name="hourA" class="form-control" required>
                                        <option value="子时">子时 (23:00~1:00)</option>
                                        <option value="丑时">丑时 (1:00~3:00)</option>
                                        <option value="寅时">寅时 (3:00~5:00)</option>
                                        <option value="卯时">卯时 (5:00~7:00)</option>
                                        <option value="辰时">辰时 (7:00~9:00)</option>
                                        <option value="巳时">巳时 (9:00~11:00)</option>
                                        <option value="午时">午时 (11:00~13:00)</option>
                                        <option value="未时">未时 (13:00~15:00)</option>
                                        <option value="申时">申时 (15:00~17:00)</option>
                                        <option value="酉时">酉时 (17:00~19:00)</option>
                                        <option value="戌时">戌时 (19:00~21:00)</option>
                                        <option value="亥时">亥时 (21:00~23:00)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-column">
                                <h4>👤 乙方信息</h4>
                                <div class="form-group">
                                    <label for="nameB">姓名/称呼</label>
                                    <input type="text" id="nameB" name="nameB" class="form-control" placeholder="请输入乙方姓名" required>
                                </div>
                                <div class="form-group">
                                    <label for="genderB">性别</label>
                                    <select id="genderB" name="genderB" class="form-control" required>
                                        <option value="女">女</option>
                                        <option value="男">男</option>
                                    </select>
                                </div>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="yearB">出生年份</label>
                                        <input type="number" id="yearB" name="yearB" class="form-control" min="1900" max="2030" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="monthB">月份</label>
                                        <input type="number" id="monthB" name="monthB" class="form-control" min="1" max="12" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="dayB">日期</label>
                                        <input type="number" id="dayB" name="dayB" class="form-control" min="1" max="31" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="hourB">出生时辰</label>
                                    <select id="hourB" name="hourB" class="form-control" required>
                                        <option value="子时">子时 (23:00~1:00)</option>
                                        <option value="丑时">丑时 (1:00~3:00)</option>
                                        <option value="寅时">寅时 (3:00~5:00)</option>
                                        <option value="卯时">卯时 (5:00~7:00)</option>
                                        <option value="辰时">辰时 (7:00~9:00)</option>
                                        <option value="巳时">巳时 (9:00~11:00)</option>
                                        <option value="午时">午时 (11:00~13:00)</option>
                                        <option value="未时">未时 (13:00~15:00)</option>
                                        <option value="申时">申时 (15:00~17:00)</option>
                                        <option value="酉时">酉时 (17:00~19:00)</option>
                                        <option value="戌时">戌时 (19:00~21:00)</option>
                                        <option value="亥时">亥时 (21:00~23:00)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="analysisDimension">分析维度</label>
                            <select id="analysisDimension" name="analysisDimension" class="form-control" required>
                                <option value="overall_compatibility">🌟 综合匹配度分析</option>
                                <option value="personality_compatibility">💭 性格互补性分析</option>
                                <option value="emotional_harmony">💕 感情和谐度分析</option>
                                <option value="wealth_cooperation">💰 财运配合度分析</option>
                                <option value="career_partnership">💼 事业合作潜力分析</option>
                                <option value="family_harmony">🏠 家庭和睦度分析</option>
                                <option value="children_fortune">👶 子女缘分分析</option>
                                <option value="health_influence">🏥 健康相互影响分析</option>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">💕 开始合盘分析</button>
                            <button type="reset" class="btn">🔄 重置表单</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 合盘记录列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📋 合盘记录</h3>
                    <div class="card-actions">
                        <span id="compatibilityCount" class="record-count">加载中...</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table" id="compatibilityTable">
                            <thead>
                                <tr>
                                    <th>记录ID</th>
                                    <th>甲方信息</th>
                                    <th>乙方信息</th>
                                    <th>分析维度</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="compatibilityTableBody">
                                <tr>
                                    <td colspan="7" class="loading">
                                        <div class="spinner"></div>
                                        正在加载合盘记录...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 合盘分析结果 -->
            <div id="compatibilityResultSection" class="card" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">💕 合盘分析结果</h3>
                    <div class="card-actions">
                        <button class="btn btn-small" onclick="closeCompatibilityResult()">✖️ 关闭</button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="compatibilityResultContent">
                        <!-- 合盘分析结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 六爻占卜 -->
        <section id="liuyao" class="content-section">
            <div class="page-header">
                <h1 class="page-title">六爻占卜</h1>
                <p class="page-subtitle">传统六爻算卦，支持时间起卦、数字起卦、硬币起卦</p>
            </div>

            <!-- 创建六爻占卜 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔮 创建六爻占卜</h3>
                </div>
                <div class="card-body">
                    <form id="liuyaoForm" class="liuyao-form">
                        <div class="form-group">
                            <label for="liuyaoQuestion">占卜问题</label>
                            <textarea id="liuyaoQuestion" name="question" class="form-control" rows="3"
                                placeholder="请输入您想要占卜的具体问题，例如：我的事业发展如何？感情运势怎样？" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="divinationMethod">起卦方式</label>
                            <select id="divinationMethod" name="method" class="form-control" required onchange="toggleMethodInputs()">
                                <option value="time">🕐 时间起卦（根据当前时间自动起卦）</option>
                                <option value="numbers">🔢 数字起卦（输入两个数字）</option>
                                <option value="coins">🪙 硬币起卦（手摇六次硬币）</option>
                            </select>
                        </div>

                        <!-- 数字起卦输入 -->
                        <div id="numbersInput" class="form-group" style="display: none;">
                            <label>起卦数字</label>
                            <div class="form-row">
                                <div class="form-column">
                                    <input type="number" id="number1" name="number1" class="form-control"
                                        placeholder="第一个数字" min="1" max="999">
                                </div>
                                <div class="form-column">
                                    <input type="number" id="number2" name="number2" class="form-control"
                                        placeholder="第二个数字" min="1" max="999">
                                </div>
                            </div>
                        </div>

                        <!-- 硬币起卦界面 -->
                        <div id="coinsInput" class="form-group" style="display: none;">
                            <label>硬币起卦</label>
                            <div class="coins-container">
                                <div class="coins-instruction">
                                    <p>请连续投掷6次硬币（每次投掷3枚），记录每次的正反面结果：</p>
                                </div>
                                <div class="coin-throws">
                                    <div class="throw-row" data-throw="1">
                                        <label>第1次投掷：</label>
                                        <div class="coin-buttons">
                                            <button type="button" class="coin-btn" data-coin="1" onclick="toggleCoin(1, 1)">正</button>
                                            <button type="button" class="coin-btn" data-coin="2" onclick="toggleCoin(1, 2)">正</button>
                                            <button type="button" class="coin-btn" data-coin="3" onclick="toggleCoin(1, 3)">正</button>
                                        </div>
                                        <span class="throw-result" id="result1"></span>
                                    </div>
                                    <div class="throw-row" data-throw="2">
                                        <label>第2次投掷：</label>
                                        <div class="coin-buttons">
                                            <button type="button" class="coin-btn" data-coin="1" onclick="toggleCoin(2, 1)">正</button>
                                            <button type="button" class="coin-btn" data-coin="2" onclick="toggleCoin(2, 2)">正</button>
                                            <button type="button" class="coin-btn" data-coin="3" onclick="toggleCoin(2, 3)">正</button>
                                        </div>
                                        <span class="throw-result" id="result2"></span>
                                    </div>
                                    <div class="throw-row" data-throw="3">
                                        <label>第3次投掷：</label>
                                        <div class="coin-buttons">
                                            <button type="button" class="coin-btn" data-coin="1" onclick="toggleCoin(3, 1)">正</button>
                                            <button type="button" class="coin-btn" data-coin="2" onclick="toggleCoin(3, 2)">正</button>
                                            <button type="button" class="coin-btn" data-coin="3" onclick="toggleCoin(3, 3)">正</button>
                                        </div>
                                        <span class="throw-result" id="result3"></span>
                                    </div>
                                    <div class="throw-row" data-throw="4">
                                        <label>第4次投掷：</label>
                                        <div class="coin-buttons">
                                            <button type="button" class="coin-btn" data-coin="1" onclick="toggleCoin(4, 1)">正</button>
                                            <button type="button" class="coin-btn" data-coin="2" onclick="toggleCoin(4, 2)">正</button>
                                            <button type="button" class="coin-btn" data-coin="3" onclick="toggleCoin(4, 3)">正</button>
                                        </div>
                                        <span class="throw-result" id="result4"></span>
                                    </div>
                                    <div class="throw-row" data-throw="5">
                                        <label>第5次投掷：</label>
                                        <div class="coin-buttons">
                                            <button type="button" class="coin-btn" data-coin="1" onclick="toggleCoin(5, 1)">正</button>
                                            <button type="button" class="coin-btn" data-coin="2" onclick="toggleCoin(5, 2)">正</button>
                                            <button type="button" class="coin-btn" data-coin="3" onclick="toggleCoin(5, 3)">正</button>
                                        </div>
                                        <span class="throw-result" id="result5"></span>
                                    </div>
                                    <div class="throw-row" data-throw="6">
                                        <label>第6次投掷：</label>
                                        <div class="coin-buttons">
                                            <button type="button" class="coin-btn" data-coin="1" onclick="toggleCoin(6, 1)">正</button>
                                            <button type="button" class="coin-btn" data-coin="2" onclick="toggleCoin(6, 2)">正</button>
                                            <button type="button" class="coin-btn" data-coin="3" onclick="toggleCoin(6, 3)">正</button>
                                        </div>
                                        <span class="throw-result" id="result6"></span>
                                    </div>
                                </div>
                                <div class="coin-actions">
                                    <button type="button" class="btn btn-secondary" onclick="randomCoins()">🎲 随机投掷</button>
                                    <button type="button" class="btn btn-secondary" onclick="clearCoins()">🔄 重新投掷</button>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">🔮 开始占卜</button>
                            <button type="reset" class="btn">🔄 重置表单</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 六爻记录列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📋 六爻记录</h3>
                    <div class="card-actions">
                        <span id="liuyaoCount" class="record-count">加载中...</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table" id="liuyaoTable">
                            <thead>
                                <tr>
                                    <th>记录ID</th>
                                    <th>占卜问题</th>
                                    <th>起卦方式</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="liuyaoTableBody">
                                <tr>
                                    <td colspan="6" class="loading">
                                        <div class="spinner"></div>
                                        正在加载六爻记录...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 六爻占卜结果 -->
            <div id="liuyaoResultSection" class="card" style="display: none;">
                <div class="card-header">
                    <h3 class="card-title">🔮 六爻占卜结果</h3>
                    <div class="card-actions">
                        <button class="btn btn-small" onclick="closeLiuyaoResult()">✖️ 关闭</button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="liuyaoResultContent">
                        <!-- 六爻占卜结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 数据统计 -->
        <section id="statistics" class="content-section">
            <div class="page-header">
                <h1 class="page-title">数据统计</h1>
                <p class="page-subtitle">详细的使用统计和数据分析</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">统计图表</h3>
                </div>
                <div class="card-body">
                    <p>统计图表功能开发中...</p>
                </div>
            </div>
        </section>



        <!-- 系统设置 -->
        <section id="settings" class="content-section">
            <div class="page-header">
                <h1 class="page-title">系统设置</h1>
                <p class="page-subtitle">系统配置和参数设置</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">数据库设置</h3>
                </div>
                <div class="card-body">
                    <p>数据库配置功能开发中...</p>
                </div>
            </div>
        </section>
    </main>

    <script>
        let currentSection = 'dashboard';

        // 页面加载时初始化
        window.addEventListener('load', function() {
            loadDashboardData();
            initializeForms();
        });

        // 初始化表单
        function initializeForms() {
            // 排盘表单提交
            document.getElementById('adminPaipanForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitPaipanForm();
            });

            // 搜索表单提交
            document.getElementById('recordSearchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                searchRecords();
            });
        }

        // 切换内容区域
        function showSection(sectionName, clickEvent = null) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-link').forEach(link => {
                link.classList.remove('active');
            });

            // 显示选中的内容区域
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // 设置对应菜单项为active
            if (clickEvent && clickEvent.target) {
                clickEvent.target.classList.add('active');
            } else {
                // 如果没有event，根据sectionName找到对应的菜单项
                const targetMenuItem = document.querySelector(`[onclick*="showSection('${sectionName}')"]`);
                if (targetMenuItem) {
                    targetMenuItem.classList.add('active');
                }
            }

            currentSection = sectionName;

            // 根据不同区域加载相应数据
            if (sectionName === 'dashboard') {
                loadDashboardData();
            } else if (sectionName === 'records') {
                loadAllRecords();
            } else if (sectionName === 'compatibility') {
                loadCompatibilityRecords();
                checkProcessingCompatibilityRecords(); // 检查处理中的记录
            } else if (sectionName === 'liuyao') {
                loadLiuyaoRecords();
                checkProcessingLiuyaoRecords(); // 检查处理中的记录
            }
        }

        // 提交排盘表单
        function submitPaipanForm() {
            const form = document.getElementById('adminPaipanForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // 验证表单
            if (!data.year || !data.month || !data.day || !data.hour || !data.gender) {
                showCalculateResult('请填写完整的出生信息', 'error');
                return;
            }

            // 显示加载状态
            const btn = document.getElementById('adminCalculateBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '🔄 计算中...';
            btn.disabled = true;

            showCalculateResult('正在进行排盘计算，请稍候...', 'loading');

            // 提交数据
            fetch('/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                btn.innerHTML = originalText;
                btn.disabled = false;

                if (result.success) {
                    if (result.record_id) {
                        showCalculateResult(
                            `✅ 排盘计算成功！<br>记录ID: ${result.record_id}<br>` +
                            `<a href="/result/${result.record_id}" target="_blank" class="btn btn-small btn-view" style="margin-top: 10px;">查看详细结果</a>`,
                            'success'
                        );
                        // 刷新仪表盘数据
                        loadDashboardData();
                        // 如果在记录页面，刷新记录列表
                        if (currentSection === 'records') {
                            loadAllRecords();
                        }
                    } else {
                        showCalculateResult('✅ 计算完成，但记录保存异常', 'warning');
                    }
                } else {
                    showCalculateResult('❌ 计算失败: ' + (result.error || '未知错误'), 'error');
                }
            })
            .catch(error => {
                btn.innerHTML = originalText;
                btn.disabled = false;
                showCalculateResult('❌ 网络错误: ' + error.message, 'error');
            });
        }

        // 显示计算结果
        function showCalculateResult(message, type) {
            const resultDiv = document.getElementById('adminCalculateResult');
            resultDiv.style.display = 'block';
            resultDiv.className = `calculate-result result-${type}`;
            resultDiv.innerHTML = message;
        }

        // 搜索记录
        function searchRecords() {
            const form = document.getElementById('recordSearchForm');
            const formData = new FormData(form);
            const params = new URLSearchParams();

            // 构建查询参数
            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }

            loadRecords(params);
        }

        // 加载所有记录
        function loadAllRecords() {
            loadRecords(new URLSearchParams({ limit: 50 }));
        }

        // 加载记录列表
        function loadRecords(params) {
            const tbody = document.getElementById('recordsTableBody');
            const countSpan = document.getElementById('recordCount');

            // 显示加载状态
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="loading">
                        <div class="spinner"></div>
                        正在加载记录...
                    </td>
                </tr>
            `;

            fetch('/api/records?' + params)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayRecordsList(data.records);
                        countSpan.textContent = `共 ${data.records.length} 条记录`;
                    } else {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="7" style="text-align: center; color: #e74c3c; padding: 30px;">
                                    ❌ 加载失败: ${data.error}
                                </td>
                            </tr>
                        `;
                        countSpan.textContent = '加载失败';
                    }
                })
                .catch(error => {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" style="text-align: center; color: #e74c3c; padding: 30px;">
                                ❌ 网络错误: ${error.message}
                            </td>
                        </tr>
                    `;
                    countSpan.textContent = '网络错误';
                });
        }

        // 显示记录列表
        function displayRecordsList(records) {
            const tbody = document.getElementById('recordsTableBody');

            if (records.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; color: #666; padding: 30px;">
                            📭 未找到匹配的记录
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = records.map(record => `
                <tr>
                    <td><strong>#${record.id}</strong></td>
                    <td>${record.birth_datetime}</td>
                    <td>
                        <span class="badge ${record.gender === '男' ? 'badge-info' : 'badge-warning'}" style="background: ${record.gender === '男' ? '#3498db' : '#e67e22'}; color: white;">
                            ${record.gender}
                        </span>
                    </td>
                    <td>
                        <div style="font-size: 0.9em;">
                            <div>${record.zodiac || '未知'}</div>
                            <div style="color: #666;">${record.constellation || '未知'}</div>
                        </div>
                    </td>
                    <td style="font-size: 0.9em;">${new Date(record.calculation_time).toLocaleString()}</td>
                    <td>
                        <span class="badge ${record.success ? 'badge-success' : 'badge-danger'}">
                            ${record.success ? '✅ 成功' : '❌ 失败'}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="/result/${record.id}" target="_blank" class="btn btn-small btn-view">👁️ 查看</a>
                            <button class="btn btn-small" onclick="selectRecordForAnalysis(${record.id})" style="background: #667eea; color: white;">🎯 分析</button>
                            <button class="btn btn-small btn-delete" onclick="deleteRecord(${record.id})">🗑️ 删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 选择记录进行分析
        function selectRecordForAnalysis(recordId) {
            // 获取记录详情
            fetch(`/api/record/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 确保记录包含ID信息
                        currentAnalysisRecord = {
                            ...data.data,
                            record_id: recordId,
                            id: recordId
                        };
                        console.log('选择分析记录:', currentAnalysisRecord);

                        // 显示记录详情
                        displaySelectedRecordInline(data.data, recordId);

                        // 加载分析状态
                        loadAngleAnalysisStatus(recordId);

                        // 初始化聊天
                        initializeChat(recordId);

                        // 滚动到详情区域
                        document.getElementById('selectedRecordSection').scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                })
                .catch(error => {
                    console.error('加载记录详情失败:', error);
                    alert('加载记录详情失败: ' + error.message);
                });
        }

        // 在记录管理页面显示选中的记录信息
        function displaySelectedRecordInline(recordData, recordId) {
            const infoDiv = document.getElementById('selectedRecordInfo');
            const birthInfo = recordData.birth_info || {};

            infoDiv.innerHTML = `
                <h4>📋 记录 #${recordId} 详情</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div><strong>出生时间:</strong> ${birthInfo.datetime || recordData.birth_datetime || '未知'}</div>
                    <div><strong>性别:</strong> ${birthInfo.gender || recordData.gender || '未知'}</div>
                    <div><strong>生肖:</strong> ${birthInfo.zodiac || recordData.zodiac || '未知'}</div>
                    <div><strong>星座:</strong> ${birthInfo.sign || recordData.constellation || '未知'}</div>
                    <div><strong>计算时间:</strong> ${new Date(recordData.calculation_time).toLocaleString()}</div>
                    <div><strong>状态:</strong>
                        <span class="badge ${recordData.success ? 'badge-success' : 'badge-danger'}">
                            ${recordData.success ? '✅ 成功' : '❌ 失败'}
                        </span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <a href="/result/${recordId}" target="_blank" class="btn btn-small btn-view">👁️ 查看完整排盘结果</a>
                </div>
            `;

            // 显示相关区域
            document.getElementById('selectedRecordSection').style.display = 'block';
            document.getElementById('angleAnalysisSection').style.display = 'block';
            document.getElementById('chatSection').style.display = 'block';
        }

        // 关闭记录详情
        function closeRecordDetail() {
            document.getElementById('selectedRecordSection').style.display = 'none';
            document.getElementById('angleAnalysisSection').style.display = 'none';
            document.getElementById('chatSection').style.display = 'none';
            currentAnalysisRecord = null;
            currentChatSession = null;
        }

        // 删除记录
        function deleteRecord(recordId) {
            if (confirm('确定要删除这条记录吗？此操作不可恢复！')) {
                // 这里可以添加删除API调用
                alert('删除功能开发中...');
            }
        }

        // 分析管理相关变量
        let currentAnalysisRecord = null;
        let currentChatSession = null;

        // 加载分析记录列表
        function loadAnalysisRecords() {
            fetch('/api/records?limit=50')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('analysisRecordSelect');
                        select.innerHTML = '<option value="">请选择排盘记录...</option>';

                        data.records.forEach(record => {
                            const option = document.createElement('option');
                            option.value = record.id;
                            option.textContent = `#${record.id} - ${record.birth_datetime} (${record.gender})`;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载分析记录失败:', error);
                });
        }

        // 加载分析记录详情
        function loadAnalysisRecord() {
            const recordId = document.getElementById('analysisRecordSelect').value;
            if (!recordId) {
                document.getElementById('selectedRecordInfo').style.display = 'none';
                document.getElementById('angleAnalysisSection').style.display = 'none';
                document.getElementById('knowledgeChatSection').style.display = 'none';
                return;
            }

            // 获取记录详情
            fetch(`/api/record/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 确保记录包含ID信息
                        currentAnalysisRecord = {
                            ...data.data,
                            record_id: recordId,  // 确保有record_id
                            id: recordId          // 确保有id
                        };
                        console.log('当前分析记录:', currentAnalysisRecord);
                        displaySelectedRecord(data.data);
                        loadAngleAnalysisStatus(recordId);
                        initializeChat(recordId);
                    }
                })
                .catch(error => {
                    console.error('加载记录详情失败:', error);
                });
        }

        // 显示选中的记录信息
        function displaySelectedRecord(recordData) {
            const infoDiv = document.getElementById('selectedRecordInfo');
            const birthInfo = recordData.birth_info || {};

            infoDiv.innerHTML = `
                <h4>📋 记录信息</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
                    <div><strong>出生时间:</strong> ${birthInfo.datetime || '未知'}</div>
                    <div><strong>性别:</strong> ${birthInfo.gender || '未知'}</div>
                    <div><strong>生肖:</strong> ${birthInfo.zodiac || '未知'}</div>
                    <div><strong>星座:</strong> ${birthInfo.sign || '未知'}</div>
                </div>
            `;
            infoDiv.style.display = 'block';

            document.getElementById('angleAnalysisSection').style.display = 'block';
            document.getElementById('knowledgeChatSection').style.display = 'block';
        }

        // 加载角度分析状态
        function loadAngleAnalysisStatus(recordId) {
            fetch(`/api/analysis/status/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayAngleAnalysisGrid(recordId, data.status);
                    }
                })
                .catch(error => {
                    console.error('加载分析状态失败:', error);
                    // 显示默认的12个角度
                    displayAngleAnalysisGrid(recordId, {
                        completed_angles: [],
                        pending_angles: [
                            ['personality_destiny', '命宫分析'],
                            ['wealth_fortune', '财富分析'],
                            ['marriage_love', '婚姻分析'],
                            ['health_wellness', '健康分析'],
                            ['career_achievement', '事业分析'],
                            ['children_creativity', '子女分析'],
                            ['interpersonal_relationship', '人际分析'],
                            ['education_learning', '学业分析'],
                            ['family_environment', '家庭分析'],
                            ['travel_relocation', '迁移分析'],
                            ['spiritual_blessing', '精神分析'],
                            ['authority_parents', '权威分析']
                        ],
                        completed_count: 0,
                        total_angles: 12
                    });
                });
        }

        // 显示角度分析网格
        function displayAngleAnalysisGrid(recordId, status) {
            const grid = document.getElementById('anglesGrid');
            const progress = document.getElementById('analysisProgress');

            progress.textContent = `${status.completed_count || 0}/${status.total_angles || 12} 完成`;

            // 合并已完成和待完成的角度
            const allAngles = [
                ['personality_destiny', '命宫分析'],
                ['wealth_fortune', '财富分析'],
                ['marriage_love', '婚姻分析'],
                ['health_wellness', '健康分析'],
                ['career_achievement', '事业分析'],
                ['children_creativity', '子女分析'],
                ['interpersonal_relationship', '人际分析'],
                ['education_learning', '学业分析'],
                ['family_environment', '家庭分析'],
                ['travel_relocation', '迁移分析'],
                ['spiritual_blessing', '精神分析'],
                ['authority_parents', '权威分析']
            ];

            const completedKeys = (status.completed_angles || []).map(angle => angle[0]);

            grid.innerHTML = allAngles.map(([angleKey, angleName]) => {
                const isCompleted = completedKeys.includes(angleKey);
                const completedAngle = (status.completed_angles || []).find(angle => angle[0] === angleKey);
                const wordCount = completedAngle ? completedAngle[3] : 0;

                return `
                    <div class="angle-card ${isCompleted ? 'completed' : ''}" id="angle-${angleKey}">
                        <div class="angle-header">
                            <div class="angle-title">${angleName}</div>
                            <div class="angle-status ${isCompleted ? 'status-completed' : 'status-pending'}">
                                ${isCompleted ? '✅ 已完成' : '⏳ 待分析'}
                            </div>
                        </div>
                        <div class="angle-info">
                            ${isCompleted ? `字数: ${wordCount}字` : '点击开始分析'}
                        </div>
                        <div class="angle-actions">
                            ${isCompleted ?
                                `<button class="btn btn-small btn-view" onclick="viewAngleAnalysis('${recordId}', '${angleKey}', '${angleName}')">👁️ 查看</button>
                                 <button class="btn btn-small" onclick="reanalyzeAngle('${recordId}', '${angleKey}', '${angleName}')" style="background: #f39c12;">🔄 重新分析</button>` :
                                `<button class="btn btn-small btn-info" onclick="analyzeAngle('${recordId}', '${angleKey}', '${angleName}')">🎯 开始分析</button>`
                            }
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 分析单个角度
        function analyzeAngle(recordId, angleKey, angleName) {
            const card = document.getElementById(`angle-${angleKey}`);
            const button = card.querySelector('.btn-info');

            // 更新状态为分析中
            card.className = 'angle-card analyzing';
            card.querySelector('.angle-status').className = 'angle-status status-analyzing';
            card.querySelector('.angle-status').textContent = '🔄 分析中...';
            card.querySelector('.angle-info').textContent = '正在生成分析内容，请稍候...';

            button.disabled = true;
            button.textContent = '🔄 分析中...';

            // 调用分析API
            fetch('/api/analysis/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    record_id: recordId,
                    angle_key: angleKey,
                    angle_name: angleName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新为完成状态
                    card.className = 'angle-card completed';
                    card.querySelector('.angle-status').className = 'angle-status status-completed';
                    card.querySelector('.angle-status').textContent = '✅ 已完成';
                    card.querySelector('.angle-info').textContent = `字数: ${data.word_count}字`;

                    // 更新按钮
                    card.querySelector('.angle-actions').innerHTML = `
                        <button class="btn btn-small btn-view" onclick="viewAngleAnalysis('${recordId}', '${angleKey}', '${angleName}')">👁️ 查看</button>
                        <button class="btn btn-small" onclick="reanalyzeAngle('${recordId}', '${angleKey}', '${angleName}')" style="background: #f39c12;">🔄 重新分析</button>
                    `;

                    // 更新进度
                    loadAngleAnalysisStatus(recordId);
                } else {
                    // 更新为失败状态
                    card.className = 'angle-card failed';
                    card.querySelector('.angle-status').className = 'angle-status status-failed';
                    card.querySelector('.angle-status').textContent = '❌ 失败';
                    card.querySelector('.angle-info').textContent = `错误: ${data.error}`;

                    button.disabled = false;
                    button.textContent = '🔄 重试';
                }
            })
            .catch(error => {
                console.error('分析失败:', error);

                // 更新为失败状态
                card.className = 'angle-card failed';
                card.querySelector('.angle-status').className = 'angle-status status-failed';
                card.querySelector('.angle-status').textContent = '❌ 失败';
                card.querySelector('.angle-info').textContent = '网络错误，请重试';

                button.disabled = false;
                button.textContent = '🔄 重试';
            });
        }

        // 查看角度分析
        function viewAngleAnalysis(recordId, angleKey, angleName) {
            fetch(`/api/analysis/view/${recordId}/${angleKey}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 在新窗口显示分析内容
                        const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
                        newWindow.document.write(`
                            <html>
                            <head>
                                <title>${angleName} - 分析结果</title>
                                <style>
                                    body { font-family: 'Microsoft YaHei', Arial, sans-serif; padding: 20px; line-height: 1.6; }
                                    h1 { color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
                                    .meta { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                                    .content { white-space: pre-wrap; }
                                </style>
                            </head>
                            <body>
                                <h1>${angleName}</h1>
                                <div class="meta">
                                    <strong>字数:</strong> ${data.analysis.word_count}字<br>
                                    <strong>分析时间:</strong> ${new Date(data.analysis.analysis_time).toLocaleString()}
                                </div>
                                <div class="content">${data.analysis.analysis_content}</div>
                            </body>
                            </html>
                        `);
                    } else {
                        alert('获取分析内容失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('查看分析失败:', error);
                    alert('网络错误，请重试');
                });
        }

        // 重新分析角度
        function reanalyzeAngle(recordId, angleKey, angleName) {
            if (confirm(`确定要重新分析【${angleName}】吗？这将覆盖现有的分析结果。`)) {
                analyzeAngle(recordId, angleKey, angleName);
            }
        }

        // 初始化聊天
        function initializeChat(recordId) {
            currentChatSession = `admin_${recordId}_${Date.now()}`;
            console.log('初始化聊天会话:', currentChatSession);

            // 重置当前对话组
            currentConversationGroup = null;

            // 加载聊天历史
            fetch(`/api/chat/history/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.history.length > 0) {
                        const chatMessages = document.getElementById('chatMessages');
                        // 清空默认消息
                        chatMessages.innerHTML = '';

                        // 显示历史消息（倒序显示，最新的在下面）
                        data.history.reverse().forEach(chat => {
                            // 为每个历史对话创建独立的对话组（默认折叠）
                            currentConversationGroup = createConversationGroup(chat.user_message, chat.assistant_response);
                            chatMessages.appendChild(currentConversationGroup);

                            // 获取内容区域
                            const contentArea = currentConversationGroup.querySelector('.conversation-content');

                            // 添加用户消息和助手回复到同一个对话组
                            const userMsg = document.createElement('div');
                            userMsg.className = 'chat-message user';
                            const userContent = document.createElement('div');
                            userContent.className = 'message-content';
                            userContent.textContent = chat.user_message;
                            userMsg.appendChild(userContent);
                            contentArea.appendChild(userMsg);

                            const assistantMsg = document.createElement('div');
                            assistantMsg.className = 'chat-message assistant';
                            const assistantContent = document.createElement('div');
                            assistantContent.className = 'message-content';
                            assistantContent.innerHTML = formatAssistantMessage(chat.assistant_response);
                            assistantMsg.appendChild(assistantContent);
                            contentArea.appendChild(assistantMsg);
                        });

                        // 重置当前对话组
                        currentConversationGroup = null;

                        // 滚动到底部
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }
                })
                .catch(error => {
                    console.error('加载聊天历史失败:', error);
                });
        }

        // 处理聊天输入键盘事件
        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }

        // 发送聊天消息
        function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) {
                return;
            }

            if (!currentAnalysisRecord) {
                alert('请先选择一个排盘记录');
                return;
            }

            // 获取record_id
            const recordId = currentAnalysisRecord.id || currentAnalysisRecord.record_id;
            console.log('发送聊天消息:', {
                record_id: recordId,
                session_id: currentChatSession,
                message: message,
                currentAnalysisRecord: currentAnalysisRecord
            });

            if (!recordId) {
                alert('记录ID获取失败，请重新选择记录');
                return;
            }

            if (!currentChatSession) {
                alert('聊天会话未初始化，请重新选择记录');
                return;
            }

            // 清空输入框
            input.value = '';

            // 添加用户消息到界面
            addChatMessage('user', message);

            // 显示正在输入状态
            const typingDiv = addChatMessage('assistant', '正在思考中...', true);

            // 发送到后端
            fetch('/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    record_id: recordId,
                    session_id: currentChatSession,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                // 移除正在输入状态
                typingDiv.remove();

                if (data.success) {
                    // 添加助手回复
                    addChatMessage('assistant', data.response);
                } else {
                    addChatMessage('assistant', '抱歉，处理您的问题时出现了错误：' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('发送消息失败:', error);

                // 移除正在输入状态
                typingDiv.remove();

                addChatMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
            });
        }

        // 当前对话组
        let currentConversationGroup = null;

        // 当前对话的用户消息（用于预览）
        let currentUserMessage = '';

        // 添加聊天消息到界面
        function addChatMessage(role, content, isTyping = false) {
            const chatMessages = document.getElementById('chatMessages');

            // 如果是用户消息，创建新的对话组
            if (role === 'user' && !isTyping) {
                currentUserMessage = content;
                currentConversationGroup = createConversationGroup(content);
                chatMessages.appendChild(currentConversationGroup);

                // 默认展开新对话
                const content_div = currentConversationGroup.querySelector('.conversation-content');
                const toggle = currentConversationGroup.querySelector('.conversation-toggle');
                content_div.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.innerHTML = '▲';
            }

            // 如果没有当前对话组，创建一个（用于历史消息）
            if (!currentConversationGroup) {
                currentConversationGroup = createConversationGroup();
                chatMessages.appendChild(currentConversationGroup);
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${role}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = isTyping ? 'message-content chat-typing' : 'message-content';

            if (isTyping) {
                contentDiv.textContent = content;
            } else {
                // 格式化内容，特别是助手回复
                if (role === 'assistant') {
                    contentDiv.innerHTML = formatAssistantMessage(content);
                    // 更新预览
                    if (currentConversationGroup) {
                        updateConversationPreview(currentConversationGroup, currentUserMessage, content);
                    }
                } else {
                    contentDiv.textContent = content;
                }
            }

            messageDiv.appendChild(contentDiv);

            // 添加到对话组的内容区域
            const contentArea = currentConversationGroup.querySelector('.conversation-content');
            contentArea.appendChild(messageDiv);

            // 如果是助手回复完成，重置当前对话组
            if (role === 'assistant' && !isTyping) {
                currentConversationGroup = null;
                currentUserMessage = '';
            }

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        // 创建对话组卡片
        function createConversationGroup(userMessage = '', assistantMessage = '') {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'conversation-group';

            // 创建头部
            const header = document.createElement('div');
            header.className = 'conversation-header';

            // 预览内容
            const preview = document.createElement('div');
            preview.className = 'conversation-preview';

            const question = document.createElement('div');
            question.className = 'conversation-question';
            question.textContent = userMessage || '新对话...';

            const answerPreview = document.createElement('div');
            answerPreview.className = 'conversation-answer-preview';
            answerPreview.textContent = assistantMessage ?
                assistantMessage.substring(0, 50) + (assistantMessage.length > 50 ? '...' : '') :
                '等待回复...';

            preview.appendChild(question);
            preview.appendChild(answerPreview);

            // 元信息
            const meta = document.createElement('div');
            meta.className = 'conversation-meta';

            const timestamp = document.createElement('div');
            timestamp.className = 'conversation-timestamp';
            timestamp.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            meta.appendChild(timestamp);

            // 折叠按钮
            const toggle = document.createElement('button');
            toggle.className = 'conversation-toggle';
            toggle.innerHTML = '▼';
            toggle.onclick = () => toggleConversation(groupDiv);

            header.appendChild(preview);
            header.appendChild(meta);
            header.appendChild(toggle);

            // 创建内容区域
            const content = document.createElement('div');
            content.className = 'conversation-content';

            groupDiv.appendChild(header);
            groupDiv.appendChild(content);

            return groupDiv;
        }

        // 切换对话组展开/折叠
        function toggleConversation(groupDiv) {
            const content = groupDiv.querySelector('.conversation-content');
            const toggle = groupDiv.querySelector('.conversation-toggle');

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                toggle.classList.remove('expanded');
                toggle.innerHTML = '▼';
            } else {
                content.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.innerHTML = '▲';
            }
        }

        // 更新对话组预览
        function updateConversationPreview(groupDiv, userMessage, assistantMessage) {
            const question = groupDiv.querySelector('.conversation-question');
            const answerPreview = groupDiv.querySelector('.conversation-answer-preview');

            if (question && userMessage) {
                question.textContent = userMessage;
            }

            if (answerPreview && assistantMessage) {
                answerPreview.textContent = assistantMessage.substring(0, 50) +
                    (assistantMessage.length > 50 ? '...' : '');
            }
        }

        // 格式化助手消息
        function formatAssistantMessage(content) {
            // 转义HTML特殊字符
            const escapeHtml = (text) => {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            let formatted = escapeHtml(content);

            // 处理段落分隔（双换行）
            formatted = formatted.replace(/\n\n/g, '</p><p>');

            // 处理单换行
            formatted = formatted.replace(/\n/g, '<br>');

            // 包装在段落标签中
            if (formatted.includes('<br>') || formatted.includes('</p><p>')) {
                formatted = '<p>' + formatted + '</p>';
            }

            // 处理数字列表（1. 2. 3.）
            formatted = formatted.replace(/(\d+\.\s)/g, '<br><strong>$1</strong>');

            // 处理项目符号列表（- 或 •）
            formatted = formatted.replace(/^[-•]\s/gm, '<br>• ');

            // 处理强调文本（**文本**）
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // 处理斜体文本（*文本*）
            formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // 清理多余的空段落
            formatted = formatted.replace(/<p><\/p>/g, '');
            formatted = formatted.replace(/<p><br><\/p>/g, '');

            return formatted;
        }

        // 清空聊天历史
        function clearChatHistory() {
            if (confirm('确定要清空聊天记录吗？')) {
                const chatMessages = document.getElementById('chatMessages');

                // 创建欢迎消息的对话组
                chatMessages.innerHTML = `
                    <div class="conversation-group">
                        <div class="conversation-header">
                            <div class="conversation-preview">
                                <div class="conversation-question">欢迎使用知识库互动</div>
                                <div class="conversation-answer-preview">我是您的专属命理分析师，基于排盘数据为您答疑解惑</div>
                            </div>
                            <div class="conversation-meta">
                                <div class="conversation-timestamp">系统</div>
                            </div>
                            <button class="conversation-toggle expanded" onclick="toggleConversation(this.closest('.conversation-group'))">▲</button>
                        </div>
                        <div class="conversation-content expanded">
                            <div class="chat-message assistant">
                                <div class="message-content">
                                    👋 您好！我是您的专属命理分析师。基于您的排盘数据，我可以回答关于命理、运势、性格等各方面的问题。请随时提问！
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 重置当前对话组
                currentConversationGroup = null;

                // 重新初始化会话
                if (currentAnalysisRecord) {
                    currentChatSession = `admin_${currentAnalysisRecord.id || currentAnalysisRecord.record_id}_${Date.now()}`;
                }
            }
        }

        // 加载仪表盘数据
        function loadDashboardData() {
            // 加载统计数据
            fetch('/api/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayStats(data.statistics);
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                });

            // 加载最近记录
            fetch('/api/records?limit=5')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayRecentRecords(data.records);
                    }
                })
                .catch(error => {
                    console.error('加载最近记录失败:', error);
                });
        }

        // 显示统计数据
        function displayStats(stats) {
            const grid = document.getElementById('statsGrid');
            const successRate = (stats.success_rate * 100).toFixed(1);

            grid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_records}</div>
                    <div class="stat-label">总排盘数</div>
                    <div class="stat-change">+${Math.floor(Math.random() * 10)} 今日新增</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.success_records}</div>
                    <div class="stat-label">成功排盘</div>
                    <div class="stat-change">${successRate}% 成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.gender_stats['男'] || 0}</div>
                    <div class="stat-label">男性用户</div>
                    <div class="stat-change">${((stats.gender_stats['男'] || 0) / stats.total_records * 100).toFixed(1)}% 占比</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.gender_stats['女'] || 0}</div>
                    <div class="stat-label">女性用户</div>
                    <div class="stat-change">${((stats.gender_stats['女'] || 0) / stats.total_records * 100).toFixed(1)}% 占比</div>
                </div>
            `;
        }

        // 显示最近记录
        function displayRecentRecords(records) {
            const tbody = document.querySelector('#recentTable tbody');

            if (records.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">暂无记录</td></tr>';
                return;
            }

            tbody.innerHTML = records.slice(0, 5).map(record => `
                <tr>
                    <td>${record.id}</td>
                    <td>${record.birth_datetime}</td>
                    <td>${record.gender}</td>
                    <td>${new Date(record.calculation_time).toLocaleString()}</td>
                    <td>
                        <span class="badge ${record.success ? 'badge-success' : 'badge-danger'}">
                            ${record.success ? '成功' : '失败'}
                        </span>
                    </td>
                    <td>
                        <a href="/result/${record.id}" class="btn" style="padding: 5px 10px; font-size: 0.8em;">查看</a>
                    </td>
                </tr>
            `).join('');
        }

        // 合盘分析相关函数
        function loadCompatibilityRecords() {
            console.log('📋 加载合盘记录');

            const tableBody = document.getElementById('compatibilityTableBody');
            const countSpan = document.getElementById('compatibilityCount');

            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="loading">
                            <div class="spinner"></div>
                            正在加载合盘记录...
                        </td>
                    </tr>
                `;
            }

            // 调用API获取合盘记录
            fetch('/api/compatibility/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCompatibilityRecords(data.records);
                        if (countSpan) {
                            countSpan.textContent = `${data.total}条记录`;
                        }
                    } else {
                        if (tableBody) {
                            tableBody.innerHTML = `
                                <tr>
                                    <td colspan="7" class="error">
                                        ❌ 加载失败: ${data.error}
                                    </td>
                                </tr>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('加载合盘记录失败:', error);
                    if (tableBody) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="7" class="error">
                                    ❌ 网络错误: ${error.message}
                                </td>
                            </tr>
                        `;
                    }
                });
        }

        // 状态显示辅助函数
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'completed': return 'badge-success';
                case 'failed': return 'badge-danger';
                case 'processing': return 'badge-warning';
                default: return 'badge-secondary';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'completed': return '✅ 已完成';
                case 'failed': return '❌ 失败';
                case 'processing': return '🔄 处理中';
                default: return '❓ 未知';
            }
        }

        function displayCompatibilityRecords(records) {
            const tableBody = document.getElementById('compatibilityTableBody');

            if (!tableBody) {
                console.error('❌ 找不到合盘记录表格元素');
                return;
            }

            if (!records || records.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="no-data">
                            📝 暂无合盘记录
                        </td>
                    </tr>
                `;
                return;
            }

            tableBody.innerHTML = records.map(record => `
                <tr>
                    <td>${record.compatibility_id}</td>
                    <td>
                        <div class="person-info">
                            <strong>${record.person_a.name}</strong><br>
                            ${record.person_a.year}年${record.person_a.month}月${record.person_a.day}日 ${record.person_a.hour}<br>
                            <span class="gender">${record.person_a.gender}</span>
                        </div>
                    </td>
                    <td>
                        <div class="person-info">
                            <strong>${record.person_b.name}</strong><br>
                            ${record.person_b.year}年${record.person_b.month}月${record.person_b.day}日 ${record.person_b.hour}<br>
                            <span class="gender">${record.person_b.gender}</span>
                        </div>
                    </td>
                    <td>${getDimensionName(record.analysis_dimension)}</td>
                    <td>${new Date(record.created_time).toLocaleString()}</td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(record.status)}">
                            ${getStatusText(record.status)}
                        </span>
                        ${record.status === 'completed' && record.word_count ? `<br><small>${record.word_count}字</small>` : ''}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-small btn-view" onclick="viewCompatibilityResult('${record.compatibility_id}')">👁️ 查看</button>
                            <button class="btn btn-small btn-delete" onclick="deleteCompatibilityRecord('${record.compatibility_id}')">🗑️ 删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function getDimensionName(dimension) {
            const dimensionNames = {
                'overall_compatibility': '🌟 综合匹配度',
                'personality_compatibility': '💭 性格互补性',
                'emotional_harmony': '💕 感情和谐度',
                'wealth_cooperation': '💰 财运配合度',
                'career_partnership': '💼 事业合作潜力',
                'family_harmony': '🏠 家庭和睦度',
                'children_fortune': '👶 子女缘分',
                'health_influence': '🏥 健康相互影响'
            };
            return dimensionNames[dimension] || dimension;
        }

        function submitCompatibilityForm() {
            const form = document.getElementById('compatibilityForm');
            const formData = new FormData(form);

            // 构建合盘数据
            const compatibilityData = {
                person_a: {
                    name: formData.get('nameA'),
                    gender: formData.get('genderA'),
                    year: formData.get('yearA'),
                    month: formData.get('monthA'),
                    day: formData.get('dayA'),
                    hour: formData.get('hourA')
                },
                person_b: {
                    name: formData.get('nameB'),
                    gender: formData.get('genderB'),
                    year: formData.get('yearB'),
                    month: formData.get('monthB'),
                    day: formData.get('dayB'),
                    hour: formData.get('hourB')
                },
                analysis_dimension: formData.get('analysisDimension')
            };

            console.log('💕 提交合盘分析:', compatibilityData);

            // 显示提交状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '🔄 创建中...';
            submitBtn.disabled = true;

            // 调用API创建合盘分析
            fetch('/api/compatibility/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(compatibilityData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`✅ 合盘分析创建成功！\n分析ID: ${data.compatibility_id}\n状态: ${data.message}`);
                    form.reset(); // 重置表单
                    loadCompatibilityRecords(); // 刷新记录列表

                    // 开始轮询分析状态
                    startCompatibilityStatusPolling(data.compatibility_id);
                } else {
                    alert(`❌ 创建失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('创建合盘分析失败:', error);
                alert(`❌ 网络错误: ${error.message}`);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }

        function viewCompatibilityResult(compatibilityId) {
            console.log('👁️ 查看合盘结果:', compatibilityId);

            // 调用API获取合盘结果
            fetch(`/api/compatibility/${compatibilityId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCompatibilityResult(data);
                    } else {
                        alert(`❌ 获取结果失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    console.error('获取合盘结果失败:', error);
                    alert(`❌ 网络错误: ${error.message}`);
                });
        }

        function displayCompatibilityResult(resultData) {
            const resultSection = document.getElementById('compatibilityResultSection');
            const resultContent = document.getElementById('compatibilityResultContent');

            resultContent.innerHTML = `
                <div class="compatibility-result">
                    <h4>💕 合盘分析结果</h4>
                    <div class="result-meta">
                        <p><strong>分析ID:</strong> ${resultData.compatibility_id}</p>
                        <p><strong>状态:</strong> ${resultData.status === 'completed' ? '✅ 已完成' : '🔄 处理中'}</p>
                        <p><strong>创建时间:</strong> ${new Date(resultData.created_time).toLocaleString()}</p>
                        ${resultData.completed_time ? `<p><strong>完成时间:</strong> ${new Date(resultData.completed_time).toLocaleString()}</p>` : ''}
                    </div>
                    <div class="result-content">
                        <h5 style="color: #667eea; font-size: 18px; margin-bottom: 20px; text-align: center; padding-bottom: 10px; border-bottom: 2px solid #e8f4fd;">📝 分析内容</h5>
                        <div class="analysis-text">
                            ${formatAnalysisContent(resultData.analysis_content || '分析内容加载中...')}
                        </div>
                    </div>
                </div>
            `;

            resultSection.style.display = 'block';
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }

        function deleteCompatibilityRecord(compatibilityId) {
            if (confirm('确定要删除这条合盘记录吗？此操作不可恢复！')) {
                console.log('🗑️ 删除合盘记录:', compatibilityId);

                fetch(`/api/compatibility/${compatibilityId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ 删除成功！');
                        loadCompatibilityRecords(); // 刷新记录列表
                    } else {
                        alert(`❌ 删除失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    console.error('删除合盘记录失败:', error);
                    alert(`❌ 网络错误: ${error.message}`);
                });
            }
        }

        // 合盘分析状态轮询
        const activePolling = new Set(); // 跟踪正在轮询的分析

        function startCompatibilityStatusPolling(compatibilityId) {
            if (activePolling.has(compatibilityId)) {
                return; // 已经在轮询中
            }

            activePolling.add(compatibilityId);
            console.log(`🔄 开始轮询合盘分析状态: ${compatibilityId}`);

            const pollInterval = setInterval(() => {
                fetch(`/api/compatibility/${compatibilityId}/status`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log(`📊 合盘状态更新: ${compatibilityId} - ${data.status}`);

                            if (data.status === 'completed') {
                                console.log(`✅ 合盘分析完成: ${compatibilityId}, 字数: ${data.word_count}`);
                                clearInterval(pollInterval);
                                activePolling.delete(compatibilityId);

                                // 刷新记录列表显示最新状态
                                loadCompatibilityRecords();

                                // 可选：显示完成通知
                                if (Notification.permission === 'granted') {
                                    new Notification('合盘分析完成', {
                                        body: `分析ID: ${compatibilityId}\n字数: ${data.word_count}`,
                                        icon: '/static/favicon.ico'
                                    });
                                }
                            } else if (data.status === 'failed') {
                                console.log(`❌ 合盘分析失败: ${compatibilityId}`);
                                clearInterval(pollInterval);
                                activePolling.delete(compatibilityId);

                                // 刷新记录列表显示失败状态
                                loadCompatibilityRecords();
                            }
                        } else {
                            console.error(`❌ 获取合盘状态失败: ${data.error}`);
                        }
                    })
                    .catch(error => {
                        console.error(`❌ 轮询合盘状态失败: ${error}`);
                    });
            }, 5000); // 每5秒轮询一次

            // 设置最大轮询时间（10分钟）
            setTimeout(() => {
                if (activePolling.has(compatibilityId)) {
                    console.log(`⏰ 合盘分析轮询超时: ${compatibilityId}`);
                    clearInterval(pollInterval);
                    activePolling.delete(compatibilityId);
                }
            }, 10 * 60 * 1000);
        }

        // 页面加载时检查是否有处理中的合盘分析
        function checkProcessingCompatibilityRecords() {
            fetch('/api/compatibility/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        data.records.forEach(record => {
                            if (record.status === 'processing') {
                                console.log(`🔄 发现处理中的合盘分析，开始轮询: ${record.compatibility_id}`);
                                startCompatibilityStatusPolling(record.compatibility_id);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('检查处理中的合盘记录失败:', error);
                });
        }

        function closeCompatibilityResult() {
            document.getElementById('compatibilityResultSection').style.display = 'none';
        }

        // 格式化分析内容，优化排版
        function formatAnalysisContent(content) {
            if (!content || content === '分析内容加载中...') {
                return content;
            }

            // 处理标题格式
            content = content.replace(/^(#{1,4})\s*(.+)$/gm, (match, hashes, title) => {
                const level = hashes.length;
                return `<h${level}>${title.trim()}</h${level}>`;
            });

            // 处理段落 - 将连续的非空行合并为段落
            const lines = content.split('\n');
            const paragraphs = [];
            let currentParagraph = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();

                // 如果是空行或标题，结束当前段落
                if (line === '' || line.startsWith('<h')) {
                    if (currentParagraph.length > 0) {
                        paragraphs.push(`<p>${currentParagraph.join(' ')}</p>`);
                        currentParagraph = [];
                    }
                    if (line.startsWith('<h')) {
                        paragraphs.push(line);
                    }
                } else {
                    currentParagraph.push(line);
                }
            }

            // 处理最后一个段落
            if (currentParagraph.length > 0) {
                paragraphs.push(`<p>${currentParagraph.join(' ')}</p>`);
            }

            let formattedContent = paragraphs.join('\n\n');

            // 处理强调文本
            formattedContent = formattedContent.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
            formattedContent = formattedContent.replace(/\*(.+?)\*/g, '<em>$1</em>');

            // 处理列表
            formattedContent = formattedContent.replace(/^[-*]\s+(.+)$/gm, '<li>$1</li>');
            formattedContent = formattedContent.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');

            return formattedContent;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 管理后台初始化');

            // 默认显示仪表板
            showSection('dashboard');

            // 绑定搜索表单
            const searchForm = document.getElementById('recordSearchForm');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    searchRecords();
                });
            }

            // 绑定合盘分析表单
            const compatibilityForm = document.getElementById('compatibilityForm');
            if (compatibilityForm) {
                compatibilityForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitCompatibilityForm();
                });
            }

            // 绑定六爻占卜表单
            const liuyaoForm = document.getElementById('liuyaoForm');
            if (liuyaoForm) {
                liuyaoForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitLiuyaoForm();
                });
            }
        });

        // 六爻占卜相关函数
        const coinStates = {}; // 存储硬币状态

        function toggleMethodInputs() {
            const method = document.getElementById('divinationMethod').value;
            const numbersInput = document.getElementById('numbersInput');
            const coinsInput = document.getElementById('coinsInput');

            // 隐藏所有输入区域
            numbersInput.style.display = 'none';
            coinsInput.style.display = 'none';

            // 根据选择显示对应区域
            if (method === 'numbers') {
                numbersInput.style.display = 'block';
            } else if (method === 'coins') {
                coinsInput.style.display = 'block';
            }
        }

        function toggleCoin(throwNum, coinNum) {
            const key = `${throwNum}-${coinNum}`;
            const btn = document.querySelector(`[data-throw="${throwNum}"] [data-coin="${coinNum}"]`);

            // 添加翻转动画
            btn.classList.add('flipping');

            setTimeout(() => {
                if (!coinStates[key]) {
                    coinStates[key] = 'heads';
                    btn.textContent = '正';
                    btn.className = 'coin-btn heads';
                } else if (coinStates[key] === 'heads') {
                    coinStates[key] = 'tails';
                    btn.textContent = '反';
                    btn.className = 'coin-btn tails';
                } else {
                    coinStates[key] = 'heads';
                    btn.textContent = '正';
                    btn.className = 'coin-btn heads';
                }

                updateThrowResult(throwNum);
            }, 300); // 动画中间时刻更新状态

            // 移除动画类
            setTimeout(() => {
                btn.classList.remove('flipping');
            }, 600);
        }

        function updateThrowResult(throwNum) {
            const coins = [];
            for (let i = 1; i <= 3; i++) {
                const key = `${throwNum}-${i}`;
                const state = coinStates[key];
                if (state === 'heads') {
                    coins.push('正');
                } else if (state === 'tails') {
                    coins.push('反');
                } else {
                    coins.push('?');
                }
            }

            const result = coins.join('');
            const resultSpan = document.getElementById(`result${throwNum}`);
            if (resultSpan) {
                resultSpan.textContent = result;
            }
        }

        function randomCoins() {
            // 禁用随机按钮防止重复点击
            const randomBtn = document.querySelector('button[onclick="randomCoins()"]');
            if (randomBtn) {
                randomBtn.disabled = true;
                randomBtn.textContent = '🎲 投掷中...';
            }

            let delay = 0;
            for (let throwNum = 1; throwNum <= 6; throwNum++) {
                setTimeout(() => {
                    for (let coinNum = 1; coinNum <= 3; coinNum++) {
                        const key = `${throwNum}-${coinNum}`;
                        const btn = document.querySelector(`[data-throw="${throwNum}"] [data-coin="${coinNum}"]`);

                        // 添加翻转动画
                        btn.classList.add('flipping');

                        setTimeout(() => {
                            const isHeads = Math.random() < 0.5;

                            if (isHeads) {
                                coinStates[key] = 'heads';
                                btn.textContent = '正';
                                btn.className = 'coin-btn heads';
                            } else {
                                coinStates[key] = 'tails';
                                btn.textContent = '反';
                                btn.className = 'coin-btn tails';
                            }
                        }, 300);

                        setTimeout(() => {
                            btn.classList.remove('flipping');
                        }, 600);
                    }

                    setTimeout(() => {
                        updateThrowResult(throwNum);
                    }, 650);

                    // 如果是最后一次投掷，恢复按钮状态
                    if (throwNum === 6) {
                        setTimeout(() => {
                            if (randomBtn) {
                                randomBtn.disabled = false;
                                randomBtn.textContent = '🎲 随机投掷';
                            }
                        }, 700);
                    }
                }, delay);

                delay += 800; // 每次投掷间隔800ms
            }
        }

        function clearCoins() {
            for (let throwNum = 1; throwNum <= 6; throwNum++) {
                for (let coinNum = 1; coinNum <= 3; coinNum++) {
                    const key = `${throwNum}-${coinNum}`;
                    const btn = document.querySelector(`[data-throw="${throwNum}"] [data-coin="${coinNum}"]`);

                    delete coinStates[key];
                    btn.textContent = '正';
                    btn.className = 'coin-btn';
                }

                const resultSpan = document.getElementById(`result${throwNum}`);
                if (resultSpan) {
                    resultSpan.textContent = '';
                }
            }
        }

        function submitLiuyaoForm() {
            const form = document.getElementById('liuyaoForm');
            const formData = new FormData(form);
            const method = formData.get('method');

            // 构建六爻数据
            const liuyaoData = {
                question: formData.get('question'),
                method: method
            };

            // 根据起卦方式添加额外数据
            if (method === 'numbers') {
                const num1 = formData.get('number1');
                const num2 = formData.get('number2');
                if (!num1 || !num2) {
                    alert('请输入两个数字');
                    return;
                }
                liuyaoData.numbers = [parseInt(num1), parseInt(num2)];
            } else if (method === 'coins') {
                const coinResults = [];
                for (let i = 1; i <= 6; i++) {
                    const result = document.getElementById(`result${i}`).textContent;
                    if (result.length !== 3) {
                        alert(`请完成第${i}次硬币投掷`);
                        return;
                    }
                    coinResults.push(result);
                }
                liuyaoData.coin_results = coinResults;
            }

            console.log('🔮 提交六爻占卜:', liuyaoData);

            // 显示提交状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '🔄 占卜中...';
            submitBtn.disabled = true;

            // 调用API创建六爻占卜
            fetch('/api/liuyao/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(liuyaoData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`✅ 六爻占卜创建成功！\n占卜ID: ${data.liuyao_id}\n状态: ${data.message}`);
                    form.reset(); // 重置表单
                    clearCoins(); // 清除硬币状态
                    toggleMethodInputs(); // 重置输入区域
                    loadLiuyaoRecords(); // 刷新记录列表

                    // 开始轮询分析状态
                    startLiuyaoStatusPolling(data.liuyao_id);
                } else {
                    alert(`❌ 创建失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('创建六爻占卜失败:', error);
                alert(`❌ 网络错误: ${error.message}`);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }

        function loadLiuyaoRecords() {
            console.log('📋 加载六爻记录');

            const tableBody = document.getElementById('liuyaoTableBody');
            const countSpan = document.getElementById('liuyaoCount');

            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="loading">
                            <div class="spinner"></div>
                            正在加载六爻记录...
                        </td>
                    </tr>
                `;
            }

            // 调用API获取六爻记录
            fetch('/api/liuyao/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLiuyaoRecords(data.records);
                        if (countSpan) {
                            countSpan.textContent = `${data.total}条记录`;
                        }
                    } else {
                        if (tableBody) {
                            tableBody.innerHTML = `
                                <tr>
                                    <td colspan="6" class="error">
                                        ❌ 加载失败: ${data.error}
                                    </td>
                                </tr>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('加载六爻记录失败:', error);
                    if (tableBody) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="error">
                                    ❌ 网络错误: ${error.message}
                                </td>
                            </tr>
                        `;
                    }
                });
        }

        function getMethodName(method) {
            const methodNames = {
                'time': '🕐 时间起卦',
                'numbers': '🔢 数字起卦',
                'coins': '🪙 硬币起卦'
            };
            return methodNames[method] || method;
        }

        function getLiuyaoStatusText(status) {
            switch (status) {
                case 'completed': return '✅ 已完成';
                case 'failed': return '❌ 失败';
                case 'processing': return '🔄 处理中';
                default: return '❓ 未知';
            }
        }

        function displayLiuyaoRecords(records) {
            const tableBody = document.getElementById('liuyaoTableBody');

            if (!tableBody) {
                console.error('❌ 找不到六爻记录表格元素');
                return;
            }

            if (!records || records.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="no-data">
                            📝 暂无六爻记录
                        </td>
                    </tr>
                `;
                return;
            }

            tableBody.innerHTML = records.map(record => `
                <tr>
                    <td>${record.liuyao_id}</td>
                    <td>
                        <div class="question-text" title="${record.question}">
                            ${record.question.length > 30 ? record.question.substring(0, 30) + '...' : record.question}
                        </div>
                    </td>
                    <td>${getMethodName(record.divination_method)}</td>
                    <td>${new Date(record.created_time).toLocaleString()}</td>
                    <td>
                        <span class="status-badge status-${record.status}">
                            ${getLiuyaoStatusText(record.status)}
                        </span>
                        ${record.status === 'completed' && record.word_count ? `<br><small>${record.word_count}字</small>` : ''}
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-small btn-view" onclick="viewLiuyaoResult('${record.liuyao_id}')">👁️ 查看</button>
                            <button class="btn btn-small btn-delete" onclick="deleteLiuyaoRecord('${record.liuyao_id}')">🗑️ 删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function viewLiuyaoResult(liuyaoId) {
            console.log('👁️ 查看六爻结果:', liuyaoId);

            // 调用API获取六爻结果
            fetch(`/api/liuyao/${liuyaoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLiuyaoResult(data);
                    } else {
                        alert(`❌ 获取结果失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    console.error('获取六爻结果失败:', error);
                    alert(`❌ 网络错误: ${error.message}`);
                });
        }

        function displayLiuyaoResult(resultData) {
            const resultSection = document.getElementById('liuyaoResultSection');
            const resultContent = document.getElementById('liuyaoResultContent');

            resultContent.innerHTML = `
                <div class="liuyao-result">
                    <h4>🔮 六爻占卜结果</h4>
                    <div class="result-meta">
                        <p><strong>占卜ID:</strong> ${resultData.liuyao_id}</p>
                        <p><strong>问题:</strong> ${resultData.question}</p>
                        <p><strong>起卦方式:</strong> ${getMethodName(resultData.divination_method)}</p>
                        <p><strong>状态:</strong> ${resultData.status === 'completed' ? '✅ 已完成' : '🔄 处理中'}</p>
                        <p><strong>创建时间:</strong> ${new Date(resultData.created_time).toLocaleString()}</p>
                        ${resultData.completed_time ? `<p><strong>完成时间:</strong> ${new Date(resultData.completed_time).toLocaleString()}</p>` : ''}
                    </div>
                    <div class="result-content">
                        <h5 style="color: #667eea; font-size: 18px; margin-bottom: 20px; text-align: center; padding-bottom: 10px; border-bottom: 2px solid #e8f4fd;">📝 占卜结果</h5>
                        <div class="analysis-text">
                            ${formatAnalysisContent(resultData.analysis_content || '分析正在进行中...')}
                        </div>
                    </div>
                </div>
            `;

            resultSection.style.display = 'block';
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }

        function deleteLiuyaoRecord(liuyaoId) {
            if (confirm('确定要删除这条六爻记录吗？此操作不可恢复！')) {
                console.log('🗑️ 删除六爻记录:', liuyaoId);

                fetch(`/api/liuyao/${liuyaoId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ 删除成功！');
                        loadLiuyaoRecords(); // 刷新记录列表
                    } else {
                        alert(`❌ 删除失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    console.error('删除六爻记录失败:', error);
                    alert(`❌ 网络错误: ${error.message}`);
                });
            }
        }

        function closeLiuyaoResult() {
            document.getElementById('liuyaoResultSection').style.display = 'none';
        }

        // 六爻分析状态轮询
        const activeLiuyaoPolling = new Set(); // 跟踪正在轮询的分析

        function startLiuyaoStatusPolling(liuyaoId) {
            if (activeLiuyaoPolling.has(liuyaoId)) {
                return; // 已经在轮询中
            }

            activeLiuyaoPolling.add(liuyaoId);
            console.log(`🔄 开始轮询六爻分析状态: ${liuyaoId}`);

            const pollInterval = setInterval(() => {
                fetch(`/api/liuyao/${liuyaoId}/status`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log(`📊 六爻状态更新: ${liuyaoId} - ${data.status}`);

                            if (data.status === 'completed') {
                                console.log(`✅ 六爻分析完成: ${liuyaoId}, 字数: ${data.word_count}`);
                                clearInterval(pollInterval);
                                activeLiuyaoPolling.delete(liuyaoId);

                                // 刷新记录列表显示最新状态
                                loadLiuyaoRecords();

                                // 可选：显示完成通知
                                if (Notification.permission === 'granted') {
                                    new Notification('六爻占卜完成', {
                                        body: `占卜ID: ${liuyaoId}\n字数: ${data.word_count}`,
                                        icon: '/static/favicon.ico'
                                    });
                                }
                            } else if (data.status === 'failed') {
                                console.log(`❌ 六爻分析失败: ${liuyaoId}`);
                                clearInterval(pollInterval);
                                activeLiuyaoPolling.delete(liuyaoId);

                                // 刷新记录列表显示失败状态
                                loadLiuyaoRecords();
                            }
                        } else {
                            console.error(`❌ 获取六爻状态失败: ${data.error}`);
                        }
                    })
                    .catch(error => {
                        console.error(`❌ 轮询六爻状态失败: ${error}`);
                    });
            }, 5000); // 每5秒轮询一次

            // 设置最大轮询时间（10分钟）
            setTimeout(() => {
                if (activeLiuyaoPolling.has(liuyaoId)) {
                    console.log(`⏰ 六爻分析轮询超时: ${liuyaoId}`);
                    clearInterval(pollInterval);
                    activeLiuyaoPolling.delete(liuyaoId);
                }
            }, 10 * 60 * 1000);
        }

        // 页面加载时检查是否有处理中的六爻分析
        function checkProcessingLiuyaoRecords() {
            fetch('/api/liuyao/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        data.records.forEach(record => {
                            if (record.status === 'processing') {
                                console.log(`🔄 发现处理中的六爻分析，开始轮询: ${record.liuyao_id}`);
                                startLiuyaoStatusPolling(record.liuyao_id);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('检查处理中的六爻记录失败:', error);
                });
        }
    </script>
</body>
</html>
