#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面合盘分析功能
"""

import time
from datetime import datetime

def test_web_compatibility_creation():
    """测试Web界面合盘分析创建"""
    print("🔮 测试Web界面合盘分析创建")
    
    # 模拟Web界面的合盘分析创建过程
    try:
        from backend_agent_web import create_compatibility_analysis
        
        # 测试数据
        person_a_info = {
            "name": "测试A",
            "year": "1988",
            "month": "6", 
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "测试B",
            "year": "1990",
            "month": "8",
            "day": "15", 
            "hour": "申时",
            "gender": "女"
        }
        
        analysis_dimension = "emotional_harmony"
        
        print(f"📊 创建合盘分析...")
        print(f"   A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"   B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        print(f"   维度: {analysis_dimension}")
        
        # 这个函数会在Streamlit环境中运行，我们需要模拟
        # result_id = create_compatibility_analysis(person_a_info, person_b_info, analysis_dimension)
        
        print("✅ 合盘分析创建测试准备完成")
        print("🌐 请在Web界面中手动测试：http://localhost:8502")
        print("   1. 点击侧边栏 '💕 合盘分析'")
        print("   2. 输入上述测试数据")
        print("   3. 选择分析维度")
        print("   4. 点击开始分析")
        print("   5. 查看进度监控")
        print("   6. 查看结果详情")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_records_display():
    """测试缓存记录显示"""
    print("\n📋 测试缓存记录显示")
    
    try:
        from backend_agent_web import get_all_cache_records
        
        print("🔍 获取所有缓存记录...")
        records = get_all_cache_records()
        
        print(f"📊 找到 {len(records)} 条记录:")
        
        for i, record in enumerate(records, 1):
            print(f"\n{i}. 记录ID: {record['result_id'][:8]}...")
            print(f"   类型: {record.get('calculation_type', 'unknown')}")
            print(f"   信息: {record['birth_info']}")
            print(f"   完成度: {record['completed_angles']}")
            print(f"   字数: {record['total_words']}")
            print(f"   有分析: {record['has_analysis']}")
            print(f"   创建时间: {record.get('created_at', 'unknown')}")
            
            # 检查是否是合盘分析
            if record.get('calculation_type') == 'compatibility':
                print(f"   💕 这是合盘分析记录")
                if record['completed_angles'] > 0 and record['total_words'] > 0:
                    print(f"   ✅ 合盘分析已完成")
                else:
                    print(f"   ⏳ 合盘分析进行中")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compatibility_progress_logic():
    """测试合盘分析进度逻辑"""
    print("\n🔄 测试合盘分析进度逻辑")
    
    try:
        # 模拟不同状态的合盘记录
        test_records = [
            {
                'result_id': 'test_comp_1',
                'calculation_type': 'compatibility',
                'birth_info': '💕 测试A & 测试B - emotional_harmony',
                'completed_angles': 0,
                'total_words': 0,
                'has_analysis': False,
                'created_at': datetime.now().isoformat()
            },
            {
                'result_id': 'test_comp_2', 
                'calculation_type': 'compatibility',
                'birth_info': '💕 张三 & 李四 - personality_compatibility',
                'completed_angles': 1,
                'total_words': 2193,
                'has_analysis': True,
                'created_at': datetime.now().isoformat()
            }
        ]
        
        print("📊 测试进度判断逻辑:")
        
        for record in test_records:
            print(f"\n记录: {record['birth_info']}")
            print(f"  完成度: {record['completed_angles']}")
            print(f"  字数: {record['total_words']}")
            
            # 应用我们修复的逻辑
            calculation_type = record.get('calculation_type', 'ziwei')
            
            if calculation_type == 'compatibility':
                # 合盘分析：未完成的任务（completed_angles=0 且 total_words=0）
                is_active = (record['completed_angles'] == 0 and record['total_words'] == 0)
                is_completed = (record['completed_angles'] > 0 and record['total_words'] > 0)
                
                print(f"  是否进行中: {is_active}")
                print(f"  是否已完成: {is_completed}")
                
                if is_completed:
                    print(f"  ✅ 状态: 合盘完成 - {record['total_words']:,}字")
                elif is_active:
                    print(f"  ⏳ 状态: 分析中...")
                else:
                    print(f"  ❓ 状态: 未知状态")
        
        print("\n✅ 进度逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🌐 Web界面合盘分析功能测试")
    print("=" * 60)
    
    success1 = test_web_compatibility_creation()
    success2 = test_cache_records_display() 
    success3 = test_compatibility_progress_logic()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("✅ 所有测试通过！")
        print("\n🎯 下一步操作:")
        print("1. 打开浏览器访问: http://localhost:8502")
        print("2. 点击侧边栏 '💕 合盘分析'")
        print("3. 输入双方信息并开始分析")
        print("4. 检查进度监控是否正确显示")
        print("5. 查看完成后的结果展示")
    else:
        print("❌ 部分测试失败，请检查错误信息")
