#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试六神计算的正确性
"""

from coin_divination import CoinDivination
from datetime import date

def test_liushen_calculation():
    """测试六神计算"""
    divination = CoinDivination()
    
    # 测试今天的日期（乙丑日）
    print("=== 测试六神计算 ===")
    
    # 获取当前日期的干支
    day_tiangan, day_dizhi, ganzhi_day = divination._get_current_ganzhi_day()
    print(f"当前日期干支: {ganzhi_day} ({day_tiangan}{day_dizhi})")
    
    # 计算旬空
    xunkong1, xunkong2 = divination._calculate_xunkong(ganzhi_day)
    print(f"旬空: {xunkong1}, {xunkong2}")
    
    # 测试六神起法
    spirit_start_index = divination.spirit_start.get(day_tiangan, 0)
    print(f"日干 {day_tiangan} 对应的六神起始索引: {spirit_start_index}")
    print(f"六神起始: {divination.six_spirits[spirit_start_index]}")
    
    print("\n六神配置（从初爻到上爻）:")
    for i in range(6):
        spirit_index = (spirit_start_index + i) % 6
        spirit_name = divination.six_spirits[spirit_index]
        print(f"第{i+1}爻: {spirit_name}")
    
    print("\n=== 验证标准 ===")
    print("今天是乙丑日，按照传统六神起法：")
    print("甲乙日起青龙，所以乙日应该从青龙开始")
    print("初爻: 青龙")
    print("二爻: 朱雀") 
    print("三爻: 勾陈")
    print("四爻: 腾蛇")
    print("五爻: 白虎")
    print("上爻: 玄武")

if __name__ == "__main__":
    test_liushen_calculation()
