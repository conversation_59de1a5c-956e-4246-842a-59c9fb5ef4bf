#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置管理 - 重构版
简化配置，专注核心功能
"""

import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

@dataclass
class LLMConfig:
    """LLM配置"""
    api_key: str = ""
    base_url: str = "https://api.siliconflow.cn/v1"
    model_name: str = "deepseek-ai/DeepSeek-V3"
    timeout: int = 300  # 5分钟超时
    temperature: float = 0.3  # 防止幻觉
    max_tokens: int = 8192
    max_retries: int = 2

@dataclass
class SystemConfig:
    """系统配置"""
    debug: bool = False
    log_level: str = "INFO"
    cache_enabled: bool = True
    cache_ttl: int = 3600  # 缓存1小时
    max_analysis_length: int = 5000  # 每个分析最大字数

@dataclass
class WebConfig:
    """Web界面配置"""
    title: str = "智能算命AI系统 v4.0"
    icon: str = "🔮"
    layout: str = "wide"
    theme: str = "dark"

class Config:
    """配置管理器"""
    
    def __init__(self):
        self.llm = LLMConfig()
        self.system = SystemConfig()
        self.web = WebConfig()
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # LLM配置
        if os.getenv("SILICONFLOW_API_KEY"):
            self.llm.api_key = os.getenv("SILICONFLOW_API_KEY")
        
        if os.getenv("LLM_MODEL_NAME"):
            self.llm.model_name = os.getenv("LLM_MODEL_NAME")
        
        if os.getenv("LLM_TIMEOUT"):
            self.llm.timeout = int(os.getenv("LLM_TIMEOUT"))
        
        if os.getenv("LLM_TEMPERATURE"):
            self.llm.temperature = float(os.getenv("LLM_TEMPERATURE"))
        
        # 系统配置
        if os.getenv("DEBUG"):
            self.system.debug = os.getenv("DEBUG").lower() == "true"
        
        if os.getenv("LOG_LEVEL"):
            self.system.log_level = os.getenv("LOG_LEVEL")
    
    def validate(self) -> tuple[bool, list[str]]:
        """验证配置"""
        issues = []
        
        if not self.llm.api_key:
            issues.append("LLM API密钥未配置")
        
        if self.llm.timeout < 60:
            issues.append("LLM超时时间过短，建议至少60秒")
        
        if not (0.0 <= self.llm.temperature <= 1.0):
            issues.append("LLM温度设置无效，应在0.0-1.0之间")
        
        return len(issues) == 0, issues

# 全局配置实例
config = Config()

# 验证配置
is_valid, issues = config.validate()
if not is_valid:
    print("⚠️ 配置验证失败:")
    for issue in issues:
        print(f"  - {issue}")
    print("请检查环境变量配置")

# 导出配置
__all__ = ['config', 'LLMConfig', 'SystemConfig', 'WebConfig']
