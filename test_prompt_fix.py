#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提示词修复
验证年龄计算问题是否解决
"""

import asyncio

async def test_prompt_fix():
    """测试提示词修复"""
    print("🔧 测试提示词修复")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("test_calc")
        
        # 测试生辰信息 - 模拟Web界面传入的数据
        birth_info = {
            "year": "1988",  # 字符串格式，模拟Web表单输入
            "month": "6",
            "day": "1", 
            "hour": "11",
            "gender": "男"
        }
        
        print(f"📅 测试生辰: {birth_info}")
        
        # 获取融合分析数据
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=int(birth_info["hour"]),
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            print("✅ 融合分析数据获取成功")
            
            # 测试提示词构建
            from core.analysis.data_processor import DataProcessor
            from core.analysis.prompt_builder import PromptBuilder
            
            processor = DataProcessor()
            analysis_data = processor.extract_analysis_data(raw_data, "personality_destiny")
            
            prompt_builder = PromptBuilder()
            
            try:
                prompt = prompt_builder.build_analysis_prompt(analysis_data, birth_info, "personality_destiny")
                print("✅ 提示词构建成功")
                print(f"📊 提示词长度: {len(prompt)}字符")
                
                # 检查年龄计算
                if "36岁左右" in prompt:
                    print("✅ 年龄计算正确")
                elif "岁左右" in prompt:
                    print("⚠️ 年龄计算可能有问题")
                else:
                    print("❌ 未找到年龄信息")
                
                # 显示用户信息部分
                lines = prompt.split('\n')
                in_user_section = False
                for line in lines:
                    if "这个人的基本情况" in line:
                        in_user_section = True
                        print(f"\n📝 用户信息部分:")
                        print(line)
                    elif in_user_section and line.strip():
                        if line.startswith('【'):
                            break
                        print(line)
                
                return True
                
            except Exception as e:
                print(f"❌ 提示词构建失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"❌ 融合分析失败: {raw_data.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_analysis_types():
    """测试多种分析类型"""
    print("\n🔄 测试多种分析类型")
    print("=" * 40)
    
    analysis_types = [
        "personality_destiny",
        "wealth_fortune", 
        "marriage_love",
        "health_wellness"
    ]
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("test_calc")
        
        # 测试生辰信息
        birth_info = {
            "year": "1988",
            "month": "6", 
            "day": "1",
            "hour": "11",
            "gender": "男"
        }
        
        # 获取融合分析数据
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=int(birth_info["hour"]),
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            success_count = 0
            
            for analysis_type in analysis_types:
                print(f"\n🎯 测试 {analysis_type}")
                
                try:
                    result = await calculator_agent._analyze_single_angle(
                        f"{analysis_type}分析", analysis_type, f"{analysis_type}专项分析",
                        raw_data, birth_info, "紫薇+八字融合分析"
                    )
                    
                    if result and len(result) > 100:
                        print(f"✅ {analysis_type} 分析成功: {len(result)}字")
                        success_count += 1
                    else:
                        print(f"❌ {analysis_type} 分析失败或内容过短")
                        
                except Exception as e:
                    print(f"❌ {analysis_type} 分析异常: {e}")
            
            print(f"\n📊 测试结果: {success_count}/{len(analysis_types)} 成功")
            return success_count == len(analysis_types)
        else:
            print("❌ 无法获取融合分析数据")
            return False
            
    except Exception as e:
        print(f"❌ 多类型测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔧 提示词修复测试")
    print("=" * 70)
    
    # 1. 测试基础提示词构建
    success1 = await test_prompt_fix()
    
    # 2. 测试多种分析类型
    success2 = await test_multiple_analysis_types()
    
    print("\n" + "=" * 70)
    print("🎯 修复测试结果总结:")
    
    if success1:
        print("✅ 提示词构建修复成功")
    else:
        print("❌ 提示词构建仍有问题")
    
    if success2:
        print("✅ 多类型分析正常")
    else:
        print("❌ 多类型分析仍有问题")
    
    if success1 and success2:
        print("\n🎉 修复完成！现在可以正常运行Web界面了！")
    else:
        print("\n⚠️ 还有问题需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
