#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析服务 - 管理12角度LLM分析
"""

import sys
import os
from typing import Optional, List
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.chart_data import ChartData
from models.analysis_result import AnalysisResult, SingleAnalysis, AnalysisAngle, ANGLE_NAMES
from utils.simple_logger import get_logger
from utils.cache_manager import get_cache
from utils.simple_llm_client import get_llm_client

logger = get_logger()
cache = get_cache()

class AnalysisService:
    """分析服务"""
    
    def __init__(self):
        """初始化分析服务"""
        self.llm_client = get_llm_client()
        logger.info("分析服务初始化完成")
    
    def get_or_create_analysis(self, chart_data: ChartData) -> AnalysisResult:
        """
        获取或创建分析结果
        
        Args:
            chart_data: 排盘数据
            
        Returns:
            分析结果对象
        """
        cache_key = chart_data.get_cache_key()
        analysis_cache_key = f"analysis_{cache_key}"
        
        # 尝试从缓存获取
        cached_analysis = cache.get(analysis_cache_key)
        if cached_analysis:
            logger.info("📋 使用缓存的分析结果")
            return AnalysisResult.from_dict(cached_analysis)
        
        # 创建新的分析结果
        logger.info("🆕 创建新的分析结果")
        analysis_result = AnalysisResult(chart_cache_key=cache_key)
        
        # 保存到缓存
        cache.set(analysis_cache_key, analysis_result.to_dict(), "analysis")
        
        return analysis_result
    
    def analyze_single_angle(self, chart_data: ChartData, angle: AnalysisAngle) -> SingleAnalysis:
        """
        分析单个角度
        
        Args:
            chart_data: 排盘数据
            angle: 分析角度
            
        Returns:
            单个分析结果
        """
        angle_name = ANGLE_NAMES[angle]
        logger.info(f"🔍 开始分析: {angle_name}")
        
        try:
            # 构建分析提示词
            system_prompt = self._get_analysis_system_prompt()
            user_prompt = self._build_analysis_prompt(chart_data, angle)
            
            # 调用LLM分析
            content = self.llm_client.chat(user_prompt, system_prompt)
            
            if content:
                word_count = len(content)
                logger.info(f"✅ {angle_name}分析完成，字数: {word_count}")
                
                analysis = SingleAnalysis(
                    angle=angle,
                    content=content,
                    word_count=word_count,
                    success=True
                )
                
                # 验证分析质量
                if not analysis.is_valid():
                    logger.warning(f"⚠️ {angle_name}分析质量不达标")
                
                return analysis
            else:
                logger.error(f"❌ {angle_name}分析失败: LLM返回空内容")
                return SingleAnalysis(
                    angle=angle,
                    content="",
                    word_count=0,
                    success=False,
                    error_message="LLM返回空内容"
                )
                
        except Exception as e:
            logger.error(f"❌ {angle_name}分析异常: {e}")
            return SingleAnalysis(
                angle=angle,
                content="",
                word_count=0,
                success=False,
                error_message=str(e)
            )
    
    def analyze_multiple_angles(self, chart_data: ChartData, angles: List[AnalysisAngle]) -> AnalysisResult:
        """
        分析多个角度
        
        Args:
            chart_data: 排盘数据
            angles: 要分析的角度列表
            
        Returns:
            分析结果
        """
        analysis_result = self.get_or_create_analysis(chart_data)
        
        for angle in angles:
            # 检查是否已经分析过
            existing_analysis = analysis_result.get_analysis(angle)
            if existing_analysis and existing_analysis.is_valid():
                logger.info(f"⏭️ {ANGLE_NAMES[angle]}已分析，跳过")
                continue
            
            # 执行分析
            single_analysis = self.analyze_single_angle(chart_data, angle)
            analysis_result.add_analysis(single_analysis)
            
            # 保存到缓存
            self._save_analysis_result(analysis_result)
        
        return analysis_result
    
    def analyze_all_angles(self, chart_data: ChartData) -> AnalysisResult:
        """
        分析所有角度
        
        Args:
            chart_data: 排盘数据
            
        Returns:
            完整分析结果
        """
        logger.info("🎯 开始全角度分析")
        
        analysis_result = self.get_or_create_analysis(chart_data)
        pending_angles = analysis_result.get_pending_angles()
        
        if not pending_angles:
            logger.info("✅ 所有角度已分析完成")
            return analysis_result
        
        logger.info(f"📝 待分析角度: {len(pending_angles)}个")
        
        return self.analyze_multiple_angles(chart_data, pending_angles)
    
    def _get_analysis_system_prompt(self) -> str:
        """获取分析系统提示词"""
        return """你是一位专业的命理分析师，精通紫薇斗数和八字命理。

请根据提供的排盘数据进行专业分析，要求：
1. 分析内容要专业准确，基于传统命理理论
2. 分析长度控制在4000-5000字
3. 语言要通俗易懂，避免过于晦涩的术语
4. 既要分析优势，也要指出需要注意的方面
5. 提供具体的建议和指导
6. 温度设置为0.3，确保分析的一致性和准确性

请严格按照传统命理理论进行分析，不要编造不存在的信息。"""
    
    def _build_analysis_prompt(self, chart_data: ChartData, angle: AnalysisAngle) -> str:
        """构建分析提示词"""
        birth_info = chart_data.birth_info
        angle_name = ANGLE_NAMES[angle]
        
        prompt = f"""请分析以下命盘的{angle_name}方面：

【基本信息】
出生时间: {birth_info.year}年{birth_info.month}月{birth_info.day}日{birth_info.hour}时
性别: {birth_info.gender}

【紫薇斗数】
"""
        
        # 添加紫薇数据
        if chart_data.ziwei_chart and chart_data.ziwei_chart.palaces:
            prompt += "十二宫信息:\n"
            for palace_name, palace_data in chart_data.ziwei_chart.palaces.items():
                prompt += f"- {palace_name}: {palace_data}\n"
        
        # 添加八字数据
        if chart_data.bazi_chart and chart_data.bazi_chart.four_pillars:
            prompt += f"\n【八字命理】\n"
            prompt += f"四柱: {chart_data.bazi_chart.four_pillars}\n"
            
            if chart_data.bazi_chart.wuxing:
                prompt += f"五行分布: {chart_data.bazi_chart.wuxing}\n"
        
        prompt += f"\n请重点分析【{angle_name}】方面，提供详细的解读和建议。"
        
        return prompt
    
    def _save_analysis_result(self, analysis_result: AnalysisResult):
        """保存分析结果到缓存"""
        cache_key = f"analysis_{analysis_result.chart_cache_key}"
        cache.set(cache_key, analysis_result.to_dict(), "analysis")
        logger.debug("💾 分析结果已保存到缓存")
    
    def get_analysis_progress(self, chart_data: ChartData) -> dict:
        """获取分析进度"""
        analysis_result = self.get_or_create_analysis(chart_data)
        return analysis_result.get_progress()
