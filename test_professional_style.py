#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试融入专业话术的人性化算命引擎
"""

import sys
import os
sys.path.append('.')

def test_professional_fortune_telling():
    """测试专业算命话术"""
    print("测试融入专业话术的人性化算命引擎")
    print("=" * 60)
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        session_id = 'professional_test'
        
        # 测试专业话术
        message = '我1988年6月1日午时出生，男，想看紫薇斗数'
        responses = engine.process_user_message(message, session_id)
        
        print(f"总响应数: {len(responses)}")
        print()
        
        # 显示前几个响应，看看专业话术的效果
        for i, response in enumerate(responses[:5], 1):
            response_type = response.get('type', 'unknown')
            content = response.get('content', '')
            print(f"{i}. [{response_type}]")
            print(f"   {content}")
            print()
        
        print("专业话术特点验证:")
        combined_text = ' '.join([r.get('content', '') for r in responses])
        
        professional_terms = [
            '根据您的生辰八字',
            '建议您',
            '保持积极的心态', 
            '通过',
            '将会帮助您',
            '从传统命理学',
            '根据传统算命学'
        ]
        
        found_terms = 0
        for term in professional_terms:
            if term in combined_text:
                print(f"✅ 包含专业术语: {term}")
                found_terms += 1
            else:
                print(f"⚠️ 缺少专业术语: {term}")
        
        print(f"\n专业话术覆盖率: {found_terms}/{len(professional_terms)} ({found_terms/len(professional_terms)*100:.1f}%)")
        
        # 检查话术风格
        style_indicators = [
            ('专业开场', ['根据', '从']),
            ('具体分析', ['显示', '表明', '会有']),
            ('实用建议', ['建议', '注意', '避免']),
            ('平衡表述', ['但', '同时', '不过']),
            ('积极结尾', ['将会', '帮助', '实现'])
        ]
        
        print("\n话术风格分析:")
        for style_name, keywords in style_indicators:
            found = any(keyword in combined_text for keyword in keywords)
            print(f"{'✅' if found else '❌'} {style_name}: {found}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_greeting_style():
    """测试问候语风格"""
    print("\n测试专业问候语风格")
    print("-" * 40)
    
    try:
        from core.conversation.humanized_chat import HumanizedChatManager
        
        chat_manager = HumanizedChatManager()
        session_id = 'greeting_test'
        
        # 测试问候语
        greeting = chat_manager.generate_humanized_response(session_id, "greeting")
        print(f"问候语: {greeting}")
        
        # 检查专业元素
        professional_elements = ['专业', '命理', '生辰八字', '紫薇斗数', '传统']
        found_elements = [elem for elem in professional_elements if elem in greeting]
        
        print(f"包含专业元素: {found_elements}")
        print(f"专业度评分: {len(found_elements)}/{len(professional_elements)}")
        
        return len(found_elements) > 0
        
    except Exception as e:
        print(f"❌ 问候语测试失败: {e}")
        return False

def test_analysis_style():
    """测试分析风格"""
    print("\n测试专业分析风格")
    print("-" * 40)
    
    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine
        
        engine = HumanizedFortuneEngine()
        
        # 模拟紫薇分析结果
        calc_result = {
            "palaces": {
                "命宫": {"主星": "紫微", "地支": "子"},
                "事业宫": {"主星": "天府", "地支": "午"}
            }
        }
        
        # 测试不同方面的分析
        aspects = ["personality", "career", "wealth", "love"]
        
        for aspect in aspects:
            analysis = engine._analyze_ziwei_aspect(calc_result, aspect)
            print(f"\n{aspect} 分析:")
            print(f"  {analysis[:100]}...")
            
            # 检查专业话术元素
            has_professional = any(term in analysis for term in [
                '根据您的', '建议您', '保持', '通过', '将会'
            ])
            print(f"  专业话术: {'✅' if has_professional else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析风格测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 专业话术融入测试")
    print("=" * 80)
    print("目标: 验证微调模型话术风格的融入效果")
    print("=" * 80)
    
    # 执行测试
    test_results = []
    
    # 1. 专业算命话术测试
    test_results.append(("专业算命话术", test_professional_fortune_telling()))
    
    # 2. 问候语风格测试
    test_results.append(("问候语风格", test_greeting_style()))
    
    # 3. 分析风格测试
    test_results.append(("分析风格", test_analysis_style()))
    
    # 汇总结果
    print(f"\n📊 专业话术融入测试结果")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 专业话术融入成功！")
        print("\n🎯 融入的专业元素:")
        print("  ✅ '根据您的生辰八字' - 专业开场")
        print("  ✅ '建议您注意' - 实用建议")
        print("  ✅ '保持积极的心态' - 积极引导")
        print("  ✅ '通过努力' - 行动指导")
        print("  ✅ '将会帮助您实现' - 积极结尾")
        print("\n🌟 话术风格特点:")
        print("  💬 专业而亲切的表达方式")
        print("  🎯 具体的分析和建议")
        print("  ⚖️ 平衡的正面和注意事项")
        print("  🌈 积极向上的结尾引导")
        print("\n📋 基于微调模型的话术优化完成！")
    else:
        print("💥 部分话术融入存在问题，需要进一步调整")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
