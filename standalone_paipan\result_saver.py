#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排盘结果保存模块
负责将排盘结果保存到文件
"""

import os
import json
import datetime
from typing import Dict, Any, Optional

class PaipanResultSaver:
    """排盘结果保存器"""
    
    def __init__(self, output_dir: str = "paipan_outputs"):
        """
        初始化保存器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"✅ 创建输出目录: {self.output_dir}")
    
    def save_complete_result(self, paipan_result: Dict[str, Any], 
                           formatted_text: str) -> Dict[str, str]:
        """
        保存完整的排盘结果
        
        Args:
            paipan_result: 排盘计算结果
            formatted_text: 格式化的文本输出
            
        Returns:
            保存的文件路径信息
        """
        input_info = paipan_result.get("input_info", {})
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成文件名
        base_name = f"paipan_{input_info.get('year', 'unknown')}_{input_info.get('month', 'unknown')}_{input_info.get('day', 'unknown')}_{input_info.get('hour', 'unknown')}_{input_info.get('gender', 'unknown')}_{timestamp}"
        
        saved_files = {}
        
        # 1. 保存JSON格式的完整数据
        json_file = os.path.join(self.output_dir, f"{base_name}.json")
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(paipan_result, f, ensure_ascii=False, indent=2)
            saved_files["json"] = json_file
            print(f"✅ JSON数据已保存: {json_file}")
        except Exception as e:
            print(f"❌ JSON保存失败: {e}")
        
        # 2. 保存格式化的文本输出
        txt_file = os.path.join(self.output_dir, f"{base_name}.txt")
        try:
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(formatted_text)
            saved_files["txt"] = txt_file
            print(f"✅ 文本输出已保存: {txt_file}")
        except Exception as e:
            print(f"❌ 文本保存失败: {e}")
        
        # 3. 保存简化的摘要信息
        summary_file = os.path.join(self.output_dir, f"{base_name}_summary.txt")
        try:
            summary = self._generate_summary(paipan_result)
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            saved_files["summary"] = summary_file
            print(f"✅ 摘要信息已保存: {summary_file}")
        except Exception as e:
            print(f"❌ 摘要保存失败: {e}")
        
        return saved_files
    
    def _generate_summary(self, paipan_result: Dict[str, Any]) -> str:
        """
        生成排盘结果摘要
        
        Args:
            paipan_result: 排盘计算结果
            
        Returns:
            摘要文本
        """
        summary_lines = []
        input_info = paipan_result.get("input_info", {})
        
        summary_lines.append("排盘结果摘要")
        summary_lines.append("=" * 30)
        summary_lines.append(f"出生时间: {input_info.get('datetime_str', '')}")
        summary_lines.append(f"性别: {input_info.get('gender', '')}")
        summary_lines.append(f"计算状态: {paipan_result.get('status', '')}")
        summary_lines.append(f"计算时间: {paipan_result.get('calculation_time', '')}")
        summary_lines.append("")
        
        # 紫薇斗数摘要
        ziwei_result = paipan_result.get("ziwei_result", {})
        if ziwei_result and "error" not in ziwei_result:
            summary_lines.append("紫薇斗数:")
            birth_info = ziwei_result.get("birth_info", {})
            summary_lines.append(f"  农历: {birth_info.get('lunar', '')}")
            summary_lines.append(f"  生肖: {ziwei_result.get('zodiac', '')}")
            
            # 命宫信息
            palaces = ziwei_result.get("palaces", {})
            ming_gong = palaces.get("命宫", {})
            if ming_gong:
                major_stars = ming_gong.get("major_stars", [])
                if major_stars:
                    summary_lines.append(f"  命宫主星: {', '.join(major_stars)}")
                summary_lines.append(f"  命宫地支: {ming_gong.get('position', '')}")
        else:
            summary_lines.append("紫薇斗数: 计算失败")
        
        summary_lines.append("")
        
        # 八字摘要
        bazi_result = paipan_result.get("bazi_result", {})
        if bazi_result and bazi_result.get("success"):
            summary_lines.append("八字命理:")
            raw_result = bazi_result.get("raw_result", {})
            ganzhi_info = raw_result.get("干支", {})
            if ganzhi_info:
                summary_lines.append(f"  八字: {ganzhi_info.get('文本', '')}")
                summary_lines.append(f"  日柱: {ganzhi_info.get('日柱', '')} (日主)")
        else:
            summary_lines.append("八字命理: 计算失败")
        
        summary_lines.append("")
        
        # 错误信息
        errors = paipan_result.get("errors", [])
        if errors:
            summary_lines.append("错误信息:")
            for error in errors:
                summary_lines.append(f"  - {error}")
        
        return "\n".join(summary_lines)
    
    def save_ziwei_only(self, ziwei_result: Dict[str, Any], 
                       input_info: Dict[str, Any]) -> str:
        """
        仅保存紫薇斗数结果
        
        Args:
            ziwei_result: 紫薇斗数计算结果
            input_info: 输入信息
            
        Returns:
            保存的文件路径
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ziwei_only_{input_info.get('year', 'unknown')}_{input_info.get('month', 'unknown')}_{input_info.get('day', 'unknown')}_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        data = {
            "input_info": input_info,
            "ziwei_result": ziwei_result,
            "calculation_time": datetime.datetime.now().isoformat(),
            "type": "ziwei_only"
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 紫薇斗数结果已保存: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 紫薇斗数保存失败: {e}")
            return ""
    
    def save_bazi_only(self, bazi_result: Dict[str, Any], 
                      input_info: Dict[str, Any]) -> str:
        """
        仅保存八字结果
        
        Args:
            bazi_result: 八字计算结果
            input_info: 输入信息
            
        Returns:
            保存的文件路径
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"bazi_only_{input_info.get('year', 'unknown')}_{input_info.get('month', 'unknown')}_{input_info.get('day', 'unknown')}_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        data = {
            "input_info": input_info,
            "bazi_result": bazi_result,
            "calculation_time": datetime.datetime.now().isoformat(),
            "type": "bazi_only"
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 八字结果已保存: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 八字保存失败: {e}")
            return ""
    
    def list_saved_results(self) -> list:
        """
        列出已保存的结果文件
        
        Returns:
            文件列表
        """
        if not os.path.exists(self.output_dir):
            return []
        
        files = []
        for filename in os.listdir(self.output_dir):
            if filename.endswith(('.json', '.txt')):
                filepath = os.path.join(self.output_dir, filename)
                file_info = {
                    "filename": filename,
                    "filepath": filepath,
                    "size": os.path.getsize(filepath),
                    "modified_time": datetime.datetime.fromtimestamp(
                        os.path.getmtime(filepath)
                    ).isoformat()
                }
                files.append(file_info)
        
        # 按修改时间排序
        files.sort(key=lambda x: x["modified_time"], reverse=True)
        return files
