# 智能算命AI系统环境变量配置模板
# 复制此文件为 .env 并填入真实配置

# ================================
# LLM配置 (必填)
# ================================

# SiliconFlow API密钥 (必填)
SILICONFLOW_API_KEY=sk-trklwkxjmgnrgbuxhwcanaxkzwtuqevslzhoikwgwajnkqjz

# LLM模型名称
LLM_MODEL_NAME=deepseek-ai/DeepSeek-V3

# LLM请求超时时间（秒）- DeepSeek建议300秒
LLM_TIMEOUT=300

# LLM最大输出长度
LLM_MAX_TOKENS=8192

# ================================
# API服务器配置
# ================================

# API服务器地址
API_HOST=0.0.0.0

# API服务器端口
API_PORT=8002

# 是否启用调试模式
API_DEBUG=false

# 是否启用CORS
API_CORS=true

# API请求超时时间（秒）
API_TIMEOUT=60

# ================================
# 会话管理配置
# ================================

# 最大历史消息数
SESSION_MAX_HISTORY=20

# 会话超时时间（秒）
SESSION_TIMEOUT=3600

# 最大会话数
SESSION_MAX_COUNT=1000

# ================================
# Web界面配置
# ================================

# Web界面标题
WEB_TITLE=智能算命AI助手

# Web界面图标
WEB_ICON=🔮

# Web界面布局
WEB_LAYOUT=wide

# API服务器地址（Web界面调用）
WEB_API_URL=http://localhost:8002

# ================================
# 微信配置 (可选)
# ================================

# 微信公众号Token
WECHAT_TOKEN=your_wechat_token

# 微信公众号AppID
WECHAT_APP_ID=your_app_id

# 微信公众号AppSecret
WECHAT_APP_SECRET=your_app_secret

# 微信加密密钥
WECHAT_ENCODING_AES_KEY=your_encoding_aes_key

# 微信接口端口
WECHAT_PORT=8003

# ================================
# 日志配置
# ================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE_PATH=logs/app.log

# 日志文件最大大小（字节）
LOG_MAX_SIZE=10485760

# 日志文件备份数量
LOG_BACKUP_COUNT=5

# 是否输出到控制台
LOG_CONSOLE_OUTPUT=true

# ================================
# 系统配置
# ================================

# 运行环境 (development, production)
ENVIRONMENT=production

# 是否启用调试模式
DEBUG=false

# 系统版本
VERSION=2.0.0

# ================================
# 性能配置
# ================================

# API请求频率限制（每分钟）
RATE_LIMIT=100

# 会话清理间隔（秒）
SESSION_CLEANUP_INTERVAL=300

# 图片清理间隔（小时）
IMAGE_CLEANUP_INTERVAL=24

# ================================
# 安全配置
# ================================

# 是否启用HTTPS
ENABLE_HTTPS=false

# SSL证书路径
SSL_CERT_PATH=

# SSL私钥路径
SSL_KEY_PATH=

# 允许的来源域名（CORS）
ALLOWED_ORIGINS=*

# ================================
# 数据库配置 (未来扩展)
# ================================

# 数据库类型
# DB_TYPE=sqlite

# 数据库连接字符串
# DB_CONNECTION_STRING=sqlite:///data/app.db

# ================================
# 缓存配置 (未来扩展)
# ================================

# 缓存类型
# CACHE_TYPE=memory

# Redis连接字符串
# REDIS_URL=redis://localhost:6379/0

# ================================
# 监控配置 (未来扩展)
# ================================

# 是否启用监控
# ENABLE_MONITORING=false

# 监控端点
# MONITORING_ENDPOINT=/metrics

# ================================
# 备份配置 (未来扩展)
# ================================

# 是否启用自动备份
# ENABLE_AUTO_BACKUP=false

# 备份间隔（小时）
# BACKUP_INTERVAL=24

# 备份保留天数
# BACKUP_RETENTION_DAYS=7
