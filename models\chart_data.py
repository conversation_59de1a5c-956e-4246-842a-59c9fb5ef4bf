#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排盘数据模型
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional
from .birth_info import BirthInfo

@dataclass
class ZiweiChart:
    """紫薇斗数排盘数据"""
    palaces: Dict[str, Any] = field(default_factory=dict)  # 十二宫数据
    stars: Dict[str, Any] = field(default_factory=dict)    # 星曜数据
    birth_info: Dict[str, Any] = field(default_factory=dict)  # 生辰信息
    lunar_date: Dict[str, Any] = field(default_factory=dict)  # 农历信息
    
    def get_main_palace(self) -> Dict[str, Any]:
        """获取命宫信息"""
        return self.palaces.get("命宫", {})
    
    def get_wealth_palace(self) -> Dict[str, Any]:
        """获取财帛宫信息"""
        return self.palaces.get("财帛", {})
    
    def get_marriage_palace(self) -> Dict[str, Any]:
        """获取夫妻宫信息"""
        return self.palaces.get("夫妻", {})
    
    def get_career_palace(self) -> Dict[str, Any]:
        """获取官禄宫信息"""
        return self.palaces.get("官禄", {})

@dataclass
class BaziChart:
    """八字排盘数据"""
    four_pillars: Dict[str, str] = field(default_factory=dict)  # 四柱
    ten_gods: Dict[str, str] = field(default_factory=dict)      # 十神
    dayun: List[Dict] = field(default_factory=list)            # 大运
    liunian: List[Dict] = field(default_factory=list)          # 流年
    wuxing: Dict[str, float] = field(default_factory=dict)     # 五行分数
    
    def get_year_pillar(self) -> str:
        """获取年柱"""
        return self.four_pillars.get("年柱", "")
    
    def get_month_pillar(self) -> str:
        """获取月柱"""
        return self.four_pillars.get("月柱", "")
    
    def get_day_pillar(self) -> str:
        """获取日柱"""
        return self.four_pillars.get("日柱", "")
    
    def get_hour_pillar(self) -> str:
        """获取时柱"""
        return self.four_pillars.get("时柱", "")
    
    def get_wuxing_summary(self) -> str:
        """获取五行总结"""
        if not self.wuxing:
            return "五行数据未计算"
        
        # 找出最强和最弱的五行
        max_element = max(self.wuxing, key=self.wuxing.get)
        min_element = min(self.wuxing, key=self.wuxing.get)
        
        return f"{max_element}旺{min_element}弱"

@dataclass
class ChartData:
    """完整排盘数据"""
    birth_info: BirthInfo
    ziwei_chart: Optional[ZiweiChart] = None
    bazi_chart: Optional[BaziChart] = None
    calculation_time: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: str = ""
    
    def is_complete(self) -> bool:
        """检查排盘是否完整"""
        return (self.success and 
                self.ziwei_chart is not None and 
                self.bazi_chart is not None)
    
    def get_cache_key(self) -> str:
        """获取缓存键"""
        return self.birth_info.get_cache_key()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "birth_info": self.birth_info.to_dict(),
            "calculation_time": self.calculation_time.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }
        
        if self.ziwei_chart:
            result["ziwei_chart"] = {
                "palaces": self.ziwei_chart.palaces,
                "stars": self.ziwei_chart.stars,
                "birth_info": self.ziwei_chart.birth_info,
                "lunar_date": self.ziwei_chart.lunar_date
            }
        
        if self.bazi_chart:
            result["bazi_chart"] = {
                "four_pillars": self.bazi_chart.four_pillars,
                "ten_gods": self.bazi_chart.ten_gods,
                "dayun": self.bazi_chart.dayun,
                "liunian": self.bazi_chart.liunian,
                "wuxing": self.bazi_chart.wuxing
            }
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChartData':
        """从字典创建实例"""
        birth_info = BirthInfo.from_dict(data["birth_info"])
        
        chart = cls(
            birth_info=birth_info,
            calculation_time=datetime.fromisoformat(data["calculation_time"]),
            success=data["success"],
            error_message=data["error_message"]
        )
        
        if "ziwei_chart" in data:
            ziwei_data = data["ziwei_chart"]
            chart.ziwei_chart = ZiweiChart(
                palaces=ziwei_data["palaces"],
                stars=ziwei_data["stars"],
                birth_info=ziwei_data["birth_info"],
                lunar_date=ziwei_data["lunar_date"]
            )
        
        if "bazi_chart" in data:
            bazi_data = data["bazi_chart"]
            chart.bazi_chart = BaziChart(
                four_pillars=bazi_data["four_pillars"],
                ten_gods=bazi_data["ten_gods"],
                dayun=bazi_data["dayun"],
                liunian=bazi_data["liunian"],
                wuxing=bazi_data["wuxing"]
            )
        
        return chart
    
    def get_summary(self) -> str:
        """获取排盘摘要"""
        if not self.success:
            return f"排盘失败: {self.error_message}"
        
        summary_parts = [self.birth_info.to_display_string()]
        
        if self.ziwei_chart:
            main_palace = self.ziwei_chart.get_main_palace()
            if main_palace:
                summary_parts.append(f"命宫: {main_palace.get('宫位', '未知')}")
        
        if self.bazi_chart:
            wuxing_summary = self.bazi_chart.get_wuxing_summary()
            summary_parts.append(f"五行: {wuxing_summary}")
        
        return " | ".join(summary_parts)
