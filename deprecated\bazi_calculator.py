#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字算命真实算法实现
基于传统天干地支计算
"""

import datetime
from lunar_python import Lunar, <PERSON>
from typing import Dict, List, Tuple

class BaziCalculator:
    """八字排盘计算器"""
    
    def __init__(self):
        # 天干
        self.heavenly_stems = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        
        # 地支
        self.earthly_branches = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        
        # 五行属性
        self.five_elements = {
            "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土",
            "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水",
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土",
            "巳": "火", "午": "火", "未": "土", "申": "金", "酉": "金",
            "戌": "土", "亥": "水"
        }
        
        # 阴阳属性
        self.yin_yang = {
            "甲": "阳", "乙": "阴", "丙": "阳", "丁": "阴", "戊": "阳",
            "己": "阴", "庚": "阳", "辛": "阴", "壬": "阳", "癸": "阴",
            "子": "阳", "丑": "阴", "寅": "阳", "卯": "阴", "辰": "阳",
            "巳": "阴", "午": "阳", "未": "阴", "申": "阳", "酉": "阴",
            "戌": "阳", "亥": "阴"
        }
    
    def get_time_stem_branch(self, day_stem: str, hour: int) -> Tuple[str, str]:
        """根据日干和时辰推算时柱天干地支"""
        # 时支
        time_branches = [
            (23, 1, "子"), (1, 3, "丑"), (3, 5, "寅"), (5, 7, "卯"),
            (7, 9, "辰"), (9, 11, "巳"), (11, 13, "午"), (13, 15, "未"),
            (15, 17, "申"), (17, 19, "酉"), (19, 21, "戌"), (21, 23, "亥")
        ]
        
        time_branch = "子"
        for start, end, branch in time_branches:
            if start <= hour < end or (start == 23 and hour >= 23):
                time_branch = branch
                break
        
        # 时干推算（日上起时法）
        day_stem_index = self.heavenly_stems.index(day_stem)
        time_branch_index = self.earthly_branches.index(time_branch)
        
        # 甲己起甲子，乙庚起丙子，丙辛起戊子，丁壬起庚子，戊癸起壬子
        time_stem_start = {0: 0, 5: 0, 1: 2, 6: 2, 2: 4, 7: 4, 3: 6, 8: 6, 4: 8, 9: 8}
        time_stem_index = (time_stem_start[day_stem_index] + time_branch_index) % 10
        time_stem = self.heavenly_stems[time_stem_index]
        
        return time_stem, time_branch
    
    def calculate_bazi(self, year: int, month: int, day: int, hour: int) -> Dict:
        """计算八字四柱"""
        try:
            # 转换为农历
            solar = Solar.fromYmd(year, month, day)
            lunar = solar.getLunar()
            
            # 年柱
            year_stem = lunar.getYearGan()
            year_branch = lunar.getYearZhi()
            
            # 月柱
            month_stem = lunar.getMonthGan()
            month_branch = lunar.getMonthZhi()
            
            # 日柱
            day_stem = lunar.getDayGan()
            day_branch = lunar.getDayZhi()
            
            # 时柱
            time_stem, time_branch = self.get_time_stem_branch(day_stem, hour)
            
            return {
                "年柱": {"天干": year_stem, "地支": year_branch},
                "月柱": {"天干": month_stem, "地支": month_branch},
                "日柱": {"天干": day_stem, "地支": day_branch},
                "时柱": {"天干": time_stem, "地支": time_branch}
            }
            
        except Exception as e:
            return {"error": f"八字计算错误: {e}"}
    
    def analyze_five_elements(self, bazi: Dict) -> Dict:
        """分析五行强弱"""
        if "error" in bazi:
            return bazi
            
        element_count = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}
        
        # 统计五行
        for pillar in ["年柱", "月柱", "日柱", "时柱"]:
            stem = bazi[pillar]["天干"]
            branch = bazi[pillar]["地支"]
            
            element_count[self.five_elements[stem]] += 1
            element_count[self.five_elements[branch]] += 1
        
        # 找出最强和最弱的五行
        strongest = max(element_count, key=element_count.get)
        weakest = min(element_count, key=element_count.get)
        
        return {
            "五行统计": element_count,
            "最强五行": strongest,
            "最弱五行": weakest,
            "日主": bazi["日柱"]["天干"],
            "日主五行": self.five_elements[bazi["日柱"]["天干"]]
        }
    
    def check_relationships(self, bazi: Dict) -> Dict:
        """检查天干地支关系"""
        if "error" in bazi:
            return bazi
            
        relationships = {
            "天干合": [],
            "地支合": [],
            "地支冲": [],
            "地支刑": []
        }
        
        # 天干五合
        stem_combinations = {
            ("甲", "己"): "土", ("乙", "庚"): "金", ("丙", "辛"): "水",
            ("丁", "壬"): "木", ("戊", "癸"): "火"
        }
        
        stems = [bazi[p]["天干"] for p in ["年柱", "月柱", "日柱", "时柱"]]
        for i in range(len(stems)):
            for j in range(i+1, len(stems)):
                pair = tuple(sorted([stems[i], stems[j]]))
                if pair in stem_combinations:
                    relationships["天干合"].append(f"{stems[i]}{stems[j]}合{stem_combinations[pair]}")
        
        # 地支六合
        branch_combinations = {
            ("子", "丑"): "土", ("寅", "亥"): "木", ("卯", "戌"): "火",
            ("辰", "酉"): "金", ("巳", "申"): "水", ("午", "未"): "土"
        }
        
        branches = [bazi[p]["地支"] for p in ["年柱", "月柱", "日柱", "时柱"]]
        for i in range(len(branches)):
            for j in range(i+1, len(branches)):
                pair = tuple(sorted([branches[i], branches[j]]))
                if pair in branch_combinations:
                    relationships["地支合"].append(f"{branches[i]}{branches[j]}合{branch_combinations[pair]}")
        
        # 地支六冲
        branch_clashes = [
            ("子", "午"), ("丑", "未"), ("寅", "申"),
            ("卯", "酉"), ("辰", "戌"), ("巳", "亥")
        ]
        
        for i in range(len(branches)):
            for j in range(i+1, len(branches)):
                pair = tuple(sorted([branches[i], branches[j]]))
                if pair in branch_clashes:
                    relationships["地支冲"].append(f"{branches[i]}{branches[j]}相冲")
        
        return relationships
    
    def calculate_full_chart(self, year: int, month: int, day: int, hour: int) -> Dict:
        """计算完整八字命盘"""
        # 基本八字
        bazi = self.calculate_bazi(year, month, day, hour)
        if "error" in bazi:
            return bazi
        
        # 五行分析
        five_elements = self.analyze_five_elements(bazi)
        
        # 关系分析
        relationships = self.check_relationships(bazi)
        
        return {
            "出生信息": f"{year}年{month}月{day}日{hour}时",
            "八字四柱": bazi,
            "五行分析": five_elements,
            "干支关系": relationships
        }

# 测试函数
def test_bazi():
    calc = BaziCalculator()
    result = calc.calculate_full_chart(1988, 6, 1, 12)
    
    print("=== 八字排盘结果 ===")
    print(f"出生信息: {result['出生信息']}")
    print("\n四柱八字:")
    for pillar, info in result['八字四柱'].items():
        print(f"{pillar}: {info['天干']}{info['地支']}")
    
    print(f"\n五行分析: {result['五行分析']}")
    print(f"干支关系: {result['干支关系']}")

if __name__ == "__main__":
    test_bazi()
