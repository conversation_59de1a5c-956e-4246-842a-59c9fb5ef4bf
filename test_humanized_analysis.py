#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试口语化分析效果
验证紫薇+八字+六爻的口语化表达
"""

import asyncio

async def test_humanized_ziwei_bazi():
    """测试口语化的紫薇+八字分析"""
    print("🗣️ 测试口语化紫薇+八字分析")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("test_calc")
        
        # 测试生辰信息
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"📅 测试生辰: {birth_info['year']}年{birth_info['month']}月{birth_info['day']}日{birth_info['hour']}时 {birth_info['gender']}")
        
        # 获取融合分析数据
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=birth_info["year"],
            month=birth_info["month"],
            day=birth_info["day"],
            hour=birth_info["hour"],
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            print("✅ 融合分析数据获取成功")
            
            # 执行性格命运分析
            print(f"\n🎯 执行口语化性格分析...")
            
            analysis_result = await calculator_agent._analyze_single_angle(
                "性格分析", "personality_destiny", "性格特点和命运格局",
                raw_data, birth_info, "紫薇+八字融合分析"
            )
            
            if analysis_result:
                print(f"✅ 分析完成，字数: {len(analysis_result)}")
                
                # 检查口语化程度
                formal_terms = ["专业分析", "详细解析", "深度剖析", "综合评估"]
                casual_terms = ["这个人", "说说", "看看", "怎么样", "比较"]
                
                formal_count = sum(1 for term in formal_terms if term in analysis_result)
                casual_count = sum(1 for term in casual_terms if term in analysis_result)
                
                print(f"📊 语言风格分析:")
                print(f"  正式术语: {formal_count}个")
                print(f"  口语化表达: {casual_count}个")
                print(f"  口语化程度: {'高' if casual_count > formal_count else '中等' if casual_count > 0 else '低'}")
                
                # 显示分析内容的前800字
                print(f"\n📝 口语化分析内容预览:")
                print("-" * 60)
                print(analysis_result[:800] + "..." if len(analysis_result) > 800 else analysis_result)
                print("-" * 60)
                
                return True
            else:
                print("❌ 分析失败")
                return False
        else:
            print(f"❌ 融合分析失败: {raw_data.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_humanized_liuyao():
    """测试口语化的六爻占卜"""
    print("\n🔮 测试口语化六爻占卜")
    print("=" * 40)
    
    try:
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool
        
        # 创建六爻工具
        liuyao_tool = HumanizedLiuyaoTool()
        
        # 模拟用户问题
        intent = {
            "tool": "liuyao",
            "question_type": "career",
            "confidence": 0.9
        }
        
        context = {
            "user_message": "我想问问工作运势怎么样，最近想换工作",
            "session_id": "test_session"
        }
        
        print(f"👤 用户问题: {context['user_message']}")
        
        # 执行六爻占卜
        result = liuyao_tool.execute(intent, context)
        
        if result.get("success"):
            print("✅ 六爻占卜成功")
            
            analysis = result.get("analysis", "")
            if analysis:
                print(f"📊 分析字数: {len(analysis)}")
                
                # 检查口语化程度
                formal_terms = ["专业分析", "详细解析", "卦象显示", "综合判断"]
                casual_terms = ["师傅", "这个卦", "看起来", "说明", "建议你"]
                
                formal_count = sum(1 for term in formal_terms if term in analysis)
                casual_count = sum(1 for term in casual_terms if term in analysis)
                
                print(f"📊 语言风格分析:")
                print(f"  正式术语: {formal_count}个")
                print(f"  口语化表达: {casual_count}个")
                print(f"  口语化程度: {'高' if casual_count > formal_count else '中等' if casual_count > 0 else '低'}")
                
                # 显示分析内容的前600字
                print(f"\n📝 口语化六爻分析预览:")
                print("-" * 60)
                print(analysis[:600] + "..." if len(analysis) > 600 else analysis)
                print("-" * 60)
                
                return True
            else:
                print("❌ 没有分析内容")
                return False
        else:
            print(f"❌ 六爻占卜失败: {result.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 六爻测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🗣️ 口语化分析系统测试")
    print("=" * 70)
    
    # 1. 测试紫薇+八字口语化分析
    success1 = await test_humanized_ziwei_bazi()
    
    # 2. 测试六爻口语化分析
    success2 = await test_humanized_liuyao()
    
    print("\n" + "=" * 70)
    print("🎯 口语化测试结果总结:")
    
    if success1:
        print("✅ 紫薇+八字口语化分析正常")
    else:
        print("❌ 紫薇+八字口语化分析异常")
    
    if success2:
        print("✅ 六爻口语化分析正常")
    else:
        print("❌ 六爻口语化分析异常")
    
    if success1 and success2:
        print("\n🎉 口语化改造成功！现在分析更加通俗易懂了！")
        print("💡 特点:")
        print("  - 用大白话说，不再过度专业")
        print("  - 像老师傅跟人聊天一样亲切")
        print("  - 紫薇+八字互相印证更清楚")
        print("  - 六爻解卦更加生活化")
    else:
        print("\n⚠️ 还有问题需要进一步优化")

if __name__ == "__main__":
    asyncio.run(main())
