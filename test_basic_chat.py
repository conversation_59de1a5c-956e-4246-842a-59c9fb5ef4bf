#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础聊天功能测试 - 阶段1.3测试
"""

import sys
import os
sys.path.append('.')

def test_session_manager():
    """测试会话管理器"""
    print("🔧 测试会话管理器")
    print("-" * 30)
    
    try:
        from core.chat.session_manager import SessionManager
        
        # 创建会话管理器
        session_manager = SessionManager()
        print("✅ 会话管理器创建成功")
        
        # 测试创建会话
        session_id = "test_session_001"
        session = session_manager.get_session(session_id)
        print(f"✅ 会话创建成功: {session['id']}")
        
        # 测试上下文获取
        context = session_manager.get_conversation_context(session_id)
        print(f"✅ 上下文获取成功: {len(context)} 个字段")
        
        # 测试出生信息设置
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 12,
            "gender": "男"
        }
        session_manager.set_user_birth_info(session_id, birth_info)
        retrieved_info = session_manager.get_user_birth_info(session_id)
        print(f"✅ 出生信息设置成功: {retrieved_info}")
        
        # 测试统计信息
        stats = session_manager.get_session_stats()
        print(f"✅ 统计信息获取成功: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 会话管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_chat():
    """测试简单聊天功能"""
    print("\n💬 测试简单聊天功能")
    print("-" * 30)
    
    try:
        from core.chat.session_manager import SessionManager
        
        # 创建简单的聊天引擎
        session_manager = SessionManager()
        
        def simple_chat_response(session_id: str, message: str) -> str:
            """简单的聊天响应函数"""
            # 获取会话
            session = session_manager.get_session(session_id)
            
            # 简单的响应逻辑
            message_lower = message.lower()
            
            if any(word in message_lower for word in ["你好", "hello", "hi"]):
                response = "你好！我是智能算命AI助手，可以为您提供紫薇斗数、八字算命、六爻占卜等服务。"
            elif any(word in message_lower for word in ["算命", "紫薇", "八字", "六爻"]):
                response = "我可以为您提供专业的算命服务。请告诉我您的出生信息：年月日时和性别。"
            elif any(word in message_lower for word in ["帮助", "help"]):
                response = """我可以为您提供以下服务：
1. 紫薇斗数 - 详细的命盘分析
2. 八字算命 - 生辰八字解读  
3. 六爻占卜 - 占卜预测

请告诉我您想要哪种服务。"""
            else:
                response = f"我收到了您的消息：{message}。请告诉我您需要什么算命服务？"
            
            # 记录对话历史
            message_record = {
                "timestamp": "now",
                "user_message": message,
                "response": response
            }
            session_manager.update_session(session_id, {}, message_record)
            
            return response
        
        # 测试对话
        session_id = "test_chat_001"
        test_messages = [
            "你好",
            "我想算命",
            "我1988年6月1日午时出生，男",
            "帮助"
        ]
        
        for message in test_messages:
            print(f"用户: {message}")
            response = simple_chat_response(session_id, message)
            print(f"助手: {response}")
            print()
        
        # 检查会话历史
        context = session_manager.get_conversation_context(session_id)
        print(f"✅ 对话历史记录: {len(context['recent_history'])} 条消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单聊天测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置系统"""
    print("\n⚙️ 测试配置系统")
    print("-" * 30)
    
    try:
        from config.settings import get_config, validate_config
        
        # 测试配置加载
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"   LLM模型: {config.llm.model_name}")
        print(f"   API端口: {config.api.port}")
        print(f"   API密钥: {config.llm.api_key[:20]}...")
        
        # 测试配置验证
        validation = validate_config()
        if validation['valid']:
            print("✅ 配置验证通过")
        else:
            print("⚠️ 配置验证有问题:")
            for issue in validation['issues']:
                print(f"   - {issue}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 阶段1.3：基础聊天引擎测试")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 测试配置系统
    results.append(("配置系统", test_configuration()))
    
    # 2. 测试会话管理器
    results.append(("会话管理器", test_session_manager()))
    
    # 3. 测试简单聊天
    results.append(("简单聊天", test_simple_chat()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 阶段1.3测试全部通过！基础聊天引擎工作正常！")
        print("\n📋 下一步：开始阶段1.4 - 会话管理系统")
    else:
        print("💥 部分测试失败，需要修复问题后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
