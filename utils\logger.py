#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统 - 统一的日志管理和监控
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

class LogManager:
    """日志管理器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化日志管理器

        Args:
            config: 日志配置
        """
        self.config = config or self._get_default_config()
        self._setup_logging()

        self.logger = logging.getLogger(__name__)
        self.logger.info("日志系统初始化完成")

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        return {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_path": "logs/app.log",
            "max_size": 10 * 1024 * 1024,  # 10MB
            "backup_count": 5,
            "console_output": True
        }

    def _setup_logging(self):
        """设置日志系统"""

        # 创建日志目录
        log_dir = os.path.dirname(self.config["file_path"])
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        # 设置根日志级别
        level = getattr(logging, self.config["level"].upper(), logging.INFO)
        logging.getLogger().setLevel(level)

        # 清除现有处理器
        for handler in logging.getLogger().handlers[:]:
            logging.getLogger().removeHandler(handler)

        # 创建格式器
        log_format = self.config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        formatter = logging.Formatter(log_format)

        # 文件处理器（轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            self.config["file_path"],
            maxBytes=self.config["max_size"],
            backupCount=self.config["backup_count"],
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)
        logging.getLogger().addHandler(file_handler)

        # 控制台处理器
        if self.config.get("console_output", True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(level)
            logging.getLogger().addHandler(console_handler)

        # 错误日志单独文件
        error_file_path = self.config["file_path"].replace(".log", "_error.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_file_path,
            maxBytes=self.config["max_size"],
            backupCount=self.config["backup_count"],
            encoding='utf-8'
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        logging.getLogger().addHandler(error_handler)

    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        return logging.getLogger(name)

    def log_system_info(self):
        """记录系统信息"""

        logger = self.get_logger("system")

        logger.info("=" * 50)
        logger.info("智能算命AI系统启动")
        logger.info(f"启动时间: {datetime.now()}")
        logger.info(f"Python版本: {os.sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info("=" * 50)

    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能信息"""

        logger = self.get_logger("performance")

        extra_info = " ".join([f"{k}={v}" for k, v in kwargs.items()])
        logger.info(f"PERF: {operation} 耗时 {duration:.3f}s {extra_info}")

    def log_user_interaction(self, session_id: str, user_message: str,
                           response: str, tool_used: str = None, success: bool = True):
        """记录用户交互"""

        logger = self.get_logger("interaction")

        status = "SUCCESS" if success else "FAILED"
        tool_info = f" tool={tool_used}" if tool_used else ""

        logger.info(f"USER: {session_id} | {status}{tool_info} | "
                   f"input_len={len(user_message)} output_len={len(response)}")

    def log_api_request(self, endpoint: str, method: str, status_code: int,
                       duration: float, user_agent: str = None):
        """记录API请求"""

        logger = self.get_logger("api")

        ua_info = f" ua={user_agent}" if user_agent else ""
        logger.info(f"API: {method} {endpoint} {status_code} {duration:.3f}s{ua_info}")

    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """记录错误信息"""

        logger = self.get_logger("error")

        context_info = ""
        if context:
            context_info = " | " + " ".join([f"{k}={v}" for k, v in context.items()])

        logger.error(f"ERROR: {type(error).__name__}: {str(error)}{context_info}",
                    exc_info=True)

    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""

        try:
            log_file = self.config["file_path"]
            if not os.path.exists(log_file):
                return {"error": "日志文件不存在"}

            # 文件信息
            stat = os.stat(log_file)
            file_size = stat.st_size
            modified_time = datetime.fromtimestamp(stat.st_mtime)

            # 读取最近的日志行数
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 统计不同级别的日志
            level_counts = {"INFO": 0, "WARNING": 0, "ERROR": 0, "DEBUG": 0}
            recent_errors = []

            for line in lines[-1000:]:  # 只检查最近1000行
                for level in level_counts:
                    if f" - {level} - " in line:
                        level_counts[level] += 1
                        if level == "ERROR":
                            recent_errors.append(line.strip())
                        break

            return {
                "file_path": log_file,
                "file_size": file_size,
                "file_size_mb": round(file_size / 1024 / 1024, 2),
                "modified_time": modified_time.isoformat(),
                "total_lines": len(lines),
                "level_counts": level_counts,
                "recent_errors": recent_errors[-5:],  # 最近5个错误
                "log_level": self.config["level"]
            }

        except Exception as e:
            return {"error": f"获取日志统计失败: {str(e)}"}

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, log_manager: LogManager):
        """
        初始化性能监控器

        Args:
            log_manager: 日志管理器
        """
        self.log_manager = log_manager
        self.logger = log_manager.get_logger("performance")

        # 性能统计
        self.stats = {
            "api_requests": 0,
            "total_response_time": 0.0,
            "llm_calls": 0,
            "llm_total_time": 0.0,
            "tool_executions": 0,
            "tool_total_time": 0.0
        }

    def record_api_request(self, duration: float):
        """记录API请求性能"""
        self.stats["api_requests"] += 1
        self.stats["total_response_time"] += duration

        if duration > 5.0:  # 超过5秒的慢请求
            self.logger.warning(f"慢API请求: {duration:.3f}s")

    def record_llm_call(self, duration: float, tokens: int = 0):
        """记录LLM调用性能"""
        self.stats["llm_calls"] += 1
        self.stats["llm_total_time"] += duration

        self.log_manager.log_performance("LLM_CALL", duration, tokens=tokens)

        if duration > 30.0:  # 超过30秒的慢LLM调用
            self.logger.warning(f"慢LLM调用: {duration:.3f}s tokens={tokens}")

    def record_tool_execution(self, tool_name: str, duration: float):
        """记录工具执行性能"""
        self.stats["tool_executions"] += 1
        self.stats["tool_total_time"] += duration

        self.log_manager.log_performance("TOOL_EXEC", duration, tool=tool_name)

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""

        avg_api_time = 0
        if self.stats["api_requests"] > 0:
            avg_api_time = self.stats["total_response_time"] / self.stats["api_requests"]

        avg_llm_time = 0
        if self.stats["llm_calls"] > 0:
            avg_llm_time = self.stats["llm_total_time"] / self.stats["llm_calls"]

        avg_tool_time = 0
        if self.stats["tool_executions"] > 0:
            avg_tool_time = self.stats["tool_total_time"] / self.stats["tool_executions"]

        return {
            "api_requests": self.stats["api_requests"],
            "avg_api_response_time": round(avg_api_time, 3),
            "llm_calls": self.stats["llm_calls"],
            "avg_llm_time": round(avg_llm_time, 3),
            "tool_executions": self.stats["tool_executions"],
            "avg_tool_time": round(avg_tool_time, 3),
            "total_response_time": round(self.stats["total_response_time"], 3),
            "total_llm_time": round(self.stats["llm_total_time"], 3),
            "total_tool_time": round(self.stats["tool_total_time"], 3)
        }

# 全局日志管理器
_log_manager = None
_performance_monitor = None

def init_logging(config: Optional[Dict[str, Any]] = None):
    """初始化全局日志系统"""
    global _log_manager, _performance_monitor

    _log_manager = LogManager(config)
    _performance_monitor = PerformanceMonitor(_log_manager)

    _log_manager.log_system_info()

def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    if _log_manager is None:
        init_logging()
    return _log_manager.get_logger(name)

def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器"""
    if _performance_monitor is None:
        init_logging()
    return _performance_monitor

def log_user_interaction(session_id: str, user_message: str, response: str,
                        tool_used: str = None, success: bool = True):
    """记录用户交互"""
    if _log_manager:
        _log_manager.log_user_interaction(session_id, user_message, response, tool_used, success)

def log_error(error: Exception, context: Dict[str, Any] = None):
    """记录错误"""
    if _log_manager:
        _log_manager.log_error(error, context)

if __name__ == "__main__":
    # 测试日志系统
    init_logging()

    logger = get_logger("test")
    logger.info("日志系统测试开始")
    logger.warning("这是一个警告")
    logger.error("这是一个错误")

    # 测试性能监控
    monitor = get_performance_monitor()
    monitor.record_api_request(1.5)
    monitor.record_llm_call(25.0, 1000)

    print("性能统计:", monitor.get_performance_stats())
