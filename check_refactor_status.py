#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构项目状态检查
"""

import os
import sys
from pathlib import Path

def check_project_structure():
    """检查项目结构"""
    print("📁 项目结构检查:")
    
    refactor_dir = Path("ziwei_refactor")
    if not refactor_dir.exists():
        print("❌ ziwei_refactor目录不存在")
        return False
    
    required_structure = {
        "algorithms": ["ziwei_calculator.py", "bazi_calculator.py", "liuyao_calculator.py"],
        "models": ["__init__.py", "birth_info.py", "chart_data.py", "analysis_result.py"],
        "services": ["__init__.py", "chart_service.py", "analysis_service.py"],
        "utils": ["simple_logger.py", "cache_manager.py", "simple_llm_client.py"],
        "web": ["app.py"],
        "web/pages": ["__init__.py", "home.py", "chart.py"],
        "config": [".env.example", "requirements.txt"],
        "data": ["cache", "results", "sessions"],
        "tests": ["test_basic.py"]
    }
    
    all_good = True
    for dir_name, files in required_structure.items():
        dir_path = refactor_dir / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/")
            for file_name in files:
                file_path = dir_path / file_name
                if file_path.exists():
                    print(f"  ✅ {file_name}")
                else:
                    print(f"  ❌ {file_name}")
                    all_good = False
        else:
            print(f"❌ {dir_name}/")
            all_good = False
    
    return all_good

def check_dependencies():
    """检查依赖包"""
    print("\n📦 依赖包检查:")
    
    required_packages = [
        "streamlit",
        "requests", 
        "pandas",
        "python-dotenv",
        "py-iztro"
    ]
    
    all_installed = True
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            all_installed = False
    
    return all_installed

def check_configuration():
    """检查配置"""
    print("\n⚙️ 配置检查:")
    
    refactor_dir = Path("ziwei_refactor")
    
    # 检查环境变量文件
    env_file = refactor_dir / ".env"
    if env_file.exists():
        print("✅ .env文件存在")
    else:
        print("❌ .env文件不存在")
        return False
    
    # 检查配置模块
    try:
        sys.path.insert(0, str(refactor_dir))
        from config import config
        print("✅ 配置模块可导入")
        
        if config.llm.api_key:
            print("✅ API密钥已配置")
        else:
            print("⚠️ API密钥未配置")
        
        print(f"✅ LLM模型: {config.llm.model_name}")
        print(f"✅ 温度设置: {config.llm.temperature}")
        
        return True
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False

def check_algorithms():
    """检查算法模块"""
    print("\n🔮 算法模块检查:")
    
    refactor_dir = Path("ziwei_refactor")
    sys.path.insert(0, str(refactor_dir))
    
    try:
        from algorithms.algorithm_init import get_ziwei_calculator, get_bazi_calculator
        
        # 测试紫薇算法
        try:
            ziwei_calc = get_ziwei_calculator()
            print("✅ 紫薇算法可用")
        except Exception as e:
            print(f"⚠️ 紫薇算法问题: {e}")
        
        # 测试八字算法
        try:
            bazi_calc = get_bazi_calculator()
            print("✅ 八字算法可用")
        except Exception as e:
            print(f"⚠️ 八字算法问题: {e}")
        
        return True
    except Exception as e:
        print(f"❌ 算法模块导入失败: {e}")
        return False

def generate_next_steps():
    """生成下一步建议"""
    print("\n📋 下一步建议:")
    
    print("1. 🔧 立即修复:")
    print("   - 进入ziwei_refactor目录")
    print("   - 复制环境变量: cp ../ziwei_refactor.env .env")
    print("   - 安装依赖: pip install streamlit requests pandas python-dotenv py-iztro")
    
    print("\n2. 🧪 测试功能:")
    print("   - 运行: python start_refactor.py")
    print("   - 访问: http://localhost:8501")
    print("   - 测试排盘功能")
    
    print("\n3. 🚀 完善功能:")
    print("   - 添加缺失的分析页面")
    print("   - 完善六爻和合婚功能")
    print("   - 优化用户界面")
    
    print("\n4. 📊 性能优化:")
    print("   - 测试缓存机制")
    print("   - 优化启动速度")
    print("   - 减少内存使用")

def main():
    """主函数"""
    print("🔮 智能算命AI系统 v4.0 重构版状态检查")
    print("=" * 60)
    
    checks = [
        ("项目结构", check_project_structure),
        ("依赖包", check_dependencies),
        ("配置", check_configuration),
        ("算法模块", check_algorithms)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name} 检查通过")
            else:
                print(f"❌ {check_name} 检查失败")
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 检查结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 重构项目状态良好，可以启动！")
        print("运行命令: python start_refactor.py")
    else:
        print("⚠️ 发现问题，需要修复")
        generate_next_steps()

if __name__ == "__main__":
    main()
