#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇斗数真实算法实现
基于传统排盘方法
"""

import datetime
from lunar_python import Lunar, Solar
from typing import Dict, List, Tuple

class ZiweiCalculator:
    """紫薇斗数排盘计算器"""
    
    def __init__(self):
        # 十四主星
        self.main_stars = [
            "紫微", "天机", "太阳", "武曲", "天同",
            "廉贞", "天府", "太阴", "贪狼", "巨门", 
            "天相", "天梁", "七杀", "破军"
        ]
        
        # 十二宫位
        self.palaces = [
            "命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫",
            "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"
        ]
        
        # 地支
        self.earthly_branches = [
            "子", "丑", "寅", "卯", "辰", "巳",
            "午", "未", "申", "酉", "戌", "亥"
        ]
    
    def solar_to_lunar(self, year: int, month: int, day: int) -> Dict:
        """阳历转农历"""
        try:
            solar = Solar.fromYmd(year, month, day)
            lunar = solar.getLunar()
            
            return {
                "year": lunar.getYear(),
                "month": lunar.getMonth(), 
                "day": lunar.getDay(),
                "year_gan": lunar.getYearGan(),
                "year_zhi": lunar.getYearZhi(),
                "month_gan": lunar.getMonthGan(),
                "month_zhi": lunar.getMonthZhi(),
                "day_gan": lunar.getDayGan(),
                "day_zhi": lunar.getDayZhi()
            }
        except Exception as e:
            print(f"日期转换错误: {e}")
            return None
    
    def get_time_branch(self, hour: int) -> str:
        """根据时辰获取地支"""
        time_branches = {
            (23, 1): "子", (1, 3): "丑", (3, 5): "寅", (5, 7): "卯",
            (7, 9): "辰", (9, 11): "巳", (11, 13): "午", (13, 15): "未",
            (15, 17): "申", (17, 19): "酉", (19, 21): "戌", (21, 23): "亥"
        }
        
        for (start, end), branch in time_branches.items():
            if start <= hour < end or (start == 23 and hour >= 23):
                return branch
        return "子"
    
    def calculate_ming_gong(self, lunar_data: Dict, hour: int) -> int:
        """计算命宫位置"""
        if not lunar_data:
            return 0
            
        # 根据农历月份和时辰计算命宫
        month = lunar_data["month"]
        time_branch = self.get_time_branch(hour)
        time_index = self.earthly_branches.index(time_branch)
        
        # 传统算法：寅宫起正月，顺数到生月，再从卯时起，逆数到生时
        month_palace = (month + 1) % 12  # 寅宫起正月
        ming_gong = (month_palace - time_index) % 12
        
        return ming_gong
    
    def arrange_main_stars(self, ming_gong: int, lunar_data: Dict) -> Dict:
        """排列十四主星"""
        if not lunar_data:
            return {}
            
        star_positions = {}
        
        # 紫微星位置计算（简化算法）
        birth_day = lunar_data["day"]
        ziwei_pos = (birth_day + ming_gong - 1) % 12
        
        # 天府星位置（与紫微相对）
        tianfu_pos = (ziwei_pos + 6) % 12
        
        # 其他主星位置（基于传统算法）
        star_positions["紫微"] = ziwei_pos
        star_positions["天府"] = tianfu_pos
        star_positions["天机"] = (ziwei_pos + 1) % 12
        star_positions["太阳"] = (ziwei_pos + 2) % 12
        star_positions["武曲"] = (ziwei_pos + 3) % 12
        star_positions["天同"] = (ziwei_pos + 4) % 12
        star_positions["廉贞"] = (ziwei_pos + 5) % 12
        
        # 南斗星系（以天府为基础）
        star_positions["太阴"] = (tianfu_pos + 1) % 12
        star_positions["贪狼"] = (tianfu_pos + 2) % 12
        star_positions["巨门"] = (tianfu_pos + 3) % 12
        star_positions["天相"] = (tianfu_pos + 4) % 12
        star_positions["天梁"] = (tianfu_pos + 5) % 12
        star_positions["七杀"] = (tianfu_pos + 6) % 12
        star_positions["破军"] = (tianfu_pos + 7) % 12
        
        return star_positions
    
    def calculate_chart(self, year: int, month: int, day: int, hour: int, gender: str = "男") -> Dict:
        """计算完整命盘"""
        # 转换为农历
        lunar_data = self.solar_to_lunar(year, month, day)
        if not lunar_data:
            return {"error": "日期转换失败"}
        
        # 计算命宫
        ming_gong = self.calculate_ming_gong(lunar_data, hour)
        
        # 排列主星
        star_positions = self.arrange_main_stars(ming_gong, lunar_data)
        
        # 构建十二宫星曜分布
        palace_stars = {}
        for i, palace in enumerate(self.palaces):
            palace_index = (ming_gong + i) % 12
            palace_stars[palace] = {
                "position": self.earthly_branches[palace_index],
                "stars": []
            }
        
        # 将主星分配到各宫
        for star, pos in star_positions.items():
            palace_name = self.palaces[(pos - ming_gong) % 12]
            palace_stars[palace_name]["stars"].append(star)
        
        return {
            "birth_info": {
                "solar": f"{year}年{month}月{day}日{hour}时",
                "lunar": f"{lunar_data['year']}年{lunar_data['month']}月{lunar_data['day']}日",
                "ganzhi": f"{lunar_data['year_gan']}{lunar_data['year_zhi']}年{lunar_data['month_gan']}{lunar_data['month_zhi']}月{lunar_data['day_gan']}{lunar_data['day_zhi']}日"
            },
            "ming_gong": {
                "position": self.earthly_branches[ming_gong],
                "index": ming_gong
            },
            "palace_stars": palace_stars,
            "star_positions": star_positions
        }

# 测试函数
def test_ziwei():
    calc = ZiweiCalculator()
    result = calc.calculate_chart(1988, 6, 1, 12, "男")
    
    print("=== 紫薇斗数排盘结果 ===")
    print(f"出生信息: {result['birth_info']}")
    print(f"命宫位置: {result['ming_gong']}")
    print("\n十二宫星曜分布:")
    for palace, info in result['palace_stars'].items():
        stars = "、".join(info['stars']) if info['stars'] else "无主星"
        print(f"{palace}({info['position']}): {stars}")

if __name__ == "__main__":
    test_ziwei()
