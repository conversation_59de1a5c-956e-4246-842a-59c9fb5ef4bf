#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版后台系统启动脚本
自动检查环境、依赖和配置，然后启动优化版Web界面
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查必要依赖"""
    required_packages = [
        'streamlit',
        'psutil',
        'asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n📦 需要安装以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        
        install_cmd = f"pip install {' '.join(missing_packages)}"
        print(f"\n安装命令: {install_cmd}")
        
        response = input("\n是否自动安装缺失的依赖? (y/n): ")
        if response.lower() in ['y', 'yes']:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
                print("✅ 依赖安装完成")
                return True
            except subprocess.CalledProcessError:
                print("❌ 依赖安装失败，请手动安装")
                return False
        else:
            return False
    
    return True

def check_file_structure():
    """检查文件结构"""
    required_files = [
        'backend_agent_web_optimized.py',
        'web_components/__init__.py',
        'web_components/ui_components.py',
        'web_components/sidebar_manager.py',
        'web_components/page_manager.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"❌ 缺少文件: {file_path}")
        else:
            print(f"✅ 文件存在: {file_path}")
    
    if missing_files:
        print(f"\n📁 缺少以下必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    return True

def check_core_modules():
    """检查核心模块"""
    core_modules = [
        'core.agents.fortune_calculator_agent',
        'core.storage.calculation_cache',
        'algorithms.real_ziwei_calculator',
        'algorithms.real_bazi_calculator'
    ]
    
    available_modules = []
    missing_modules = []
    
    for module in core_modules:
        try:
            importlib.import_module(module)
            available_modules.append(module)
            print(f"✅ 核心模块可用: {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"⚠️ 核心模块缺失: {module}")
    
    if missing_modules:
        print(f"\n⚠️ 部分核心功能可能不可用，但基础界面仍可运行")
        print("建议检查项目完整性")
    
    return len(available_modules) > 0

def setup_environment():
    """设置环境变量"""
    env_vars = {
        'PYTHONIOENCODING': 'utf-8',
        'STREAMLIT_SERVER_FILE_WATCHER_TYPE': 'none',
        'STREAMLIT_SERVER_RUN_ON_SAVE': 'false'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"✅ 环境变量设置: {key}={value}")

def create_data_directories():
    """创建必要的数据目录"""
    directories = [
        'data',
        'data/calculation_cache',
        'data/sessions',
        'charts',
        'exports',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 目录创建: {directory}")

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动优化版后台系统...")
    print("=" * 50)
    
    try:
        # 尝试启动优化版
        cmd = [sys.executable, '-m', 'streamlit', 'run', 'backend_agent_web_optimized.py']
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n🔄 尝试启动原版本...")
        try:
            cmd = [sys.executable, '-m', 'streamlit', 'run', 'backend_agent_web.py']
            subprocess.run(cmd)
        except Exception as e2:
            print(f"❌ 原版本也启动失败: {e2}")

def main():
    """主函数"""
    print("🔮 紫薇+八字融合分析后台系统 v3.0 - 优化版启动器")
    print("=" * 60)
    
    # 检查步骤
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("文件结构", check_file_structure),
        ("核心模块", check_core_modules)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n📋 检查{check_name}...")
        if not check_func():
            all_passed = False
            print(f"❌ {check_name}检查失败")
        else:
            print(f"✅ {check_name}检查通过")
    
    if not all_passed:
        print("\n⚠️ 部分检查未通过，但仍可尝试启动")
        response = input("是否继续启动? (y/n): ")
        if response.lower() not in ['y', 'yes']:
            print("👋 启动已取消")
            return
    
    # 环境设置
    print(f"\n🔧 设置环境...")
    setup_environment()
    create_data_directories()
    
    # 启动应用
    start_streamlit()

if __name__ == "__main__":
    main()
