#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试六爻关键词优先级修复
"""

def test_liuyao_priority():
    """测试六爻关键词优先级"""
    print("🎯 测试六爻关键词优先级修复")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        engine = FortuneEngine()
        
        # 重点测试包含"运势"但应该识别为六爻的情况
        test_cases = [
            # 这些应该识别为六爻
            ("帮我算一卦，看看今年运势", "liuyao", "包含'算一卦'和'运势'"),
            ("算个卦，看看今年的运势如何？", "liuyao", "包含'算个卦'和'运势'"),
            ("起卦问问今年运势", "liuyao", "包含'起卦'和'运势'"),
            ("占卜一下今年运势", "liuyao", "包含'占卜'和'运势'"),
            ("六爻算卦看运势", "liuyao", "包含'六爻'和'运势'"),
            ("问卦：今年运势如何？", "liuyao", "包含'问卦'和'运势'"),
            ("卜卦看看运势", "liuyao", "包含'卜卦'和'运势'"),
            
            # 这些应该识别为其他类型
            ("看看今年运势如何", "comprehensive", "只有'运势'，无六爻关键词"),
            ("算算我的命运", "comprehensive", "只有'算'和'命运'"),
            ("紫薇斗数看运势", "ziwei", "包含'紫薇'"),
            ("八字算命看运势", "bazi", "包含'八字'"),
        ]
        
        print("📝 测试结果:")
        success_count = 0
        for i, (test_input, expected_type, description) in enumerate(test_cases, 1):
            actual_type = engine._detect_fortune_type(test_input)
            
            if actual_type == expected_type:
                status = "✅ 正确"
                success_count += 1
            else:
                status = f"❌ 错误(期望:{expected_type}, 实际:{actual_type})"
            
            print(f"  {i:2d}. {test_input:<30} → {status}")
            print(f"      {description}")
        
        print(f"\n📊 识别准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count >= len(test_cases) * 0.9  # 90%以上准确率
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_specific_case():
    """测试具体的问题案例"""
    print("\n🔍 测试具体问题案例")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 模拟聊天API
        def mock_chat_api(prompt: str) -> str:
            return "null"  # 模拟无法提取出生信息
        
        engine = FortuneEngine(chat_api_func=mock_chat_api)
        
        # 测试用户的具体问题
        user_question = "帮我算一卦，看看今年运势，现在已经6月份了"
        
        print(f"📝 用户问题: {user_question}")
        
        # 解析用户输入
        parsed_info = engine.parse_user_input(user_question)
        
        print(f"🔍 解析结果:")
        print(f"  算命类型: {parsed_info['fortune_type']}")
        print(f"  出生信息: {parsed_info['birth_info']}")
        print(f"  问题类型: {parsed_info['question_type']}")
        
        # 验证结果
        if parsed_info['fortune_type'] == 'liuyao':
            print("✅ 正确识别为六爻算卦")
            
            if parsed_info['birth_info'] is None:
                print("✅ 正确识别为无需出生信息")
                
                # 测试完整流程
                print("\n🔮 测试完整处理流程...")
                result = engine.process_user_request(user_question)
                
                # 检查是否正确处理
                if "六爻算法未初始化" in result.get("message", ""):
                    print("✅ 正确进入六爻算卦流程（算法未初始化是正常的）")
                    return True
                elif result.get("success"):
                    print("✅ 六爻算卦处理成功")
                    return True
                else:
                    print(f"❌ 处理失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 错误：仍然要求出生信息: {parsed_info['birth_info']}")
                return False
        else:
            print(f"❌ 错误：识别为{parsed_info['fortune_type']}，应该是liuyao")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_keyword_priority():
    """显示关键词优先级说明"""
    print("\n📋 关键词优先级说明")
    print("=" * 30)
    
    print("🔍 **检测优先级 (从高到低):**")
    print("  1. 紫薇斗数: 紫薇、斗数、命宫、星曜")
    print("  2. 八字算命: 八字、四柱、天干、地支")
    print("  3. 六爻算卦: 六爻、算卦、占卜、起卦、算个卦、看卦、卦象、起个卦、问卦、算一卦、卜卦")
    print("  4. 综合分析: 运势、命运、算命")
    print()
    
    print("⚡ **优先级设计原理:**")
    print("  - 具体算命类型优先于通用词汇")
    print("  - 六爻关键词丰富，避免被通用词汇误判")
    print("  - '运势'等通用词汇优先级最低")
    print()
    
    print("🎯 **解决的问题:**")
    print("  - '算一卦看运势' → 正确识别为六爻（不是综合分析）")
    print("  - '占卜今年运势' → 正确识别为六爻（不是综合分析）")
    print("  - '看看运势如何' → 正确识别为综合分析（无六爻关键词）")
    print()
    
    print("✅ **用户体验改进:**")
    print("  - 更准确的意图识别")
    print("  - 六爻算卦真正即问即答")
    print("  - 减少用户困惑")

def show_examples():
    """显示使用示例"""
    print("\n📝 修复后的使用示例")
    print("=" * 25)
    
    print("✅ **现在正确识别为六爻算卦:**")
    examples = [
        "帮我算一卦，看看今年运势",
        "算个卦，看看今年的运势如何？",
        "起卦问问今年运势",
        "占卜一下今年运势",
        "六爻算卦看运势",
        "问卦：今年运势如何？",
        "卜卦看看运势"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"  {i}. {example}")
    
    print("\n⚠️ **仍然识别为综合分析:**")
    other_examples = [
        "看看今年运势如何 (无六爻关键词)",
        "算算我的命运 (无六爻关键词)",
        "今年运势怎么样 (无六爻关键词)"
    ]
    
    for i, example in enumerate(other_examples, 1):
        print(f"  {i}. {example}")

def main():
    """主测试函数"""
    print("🎯 六爻关键词优先级修复测试")
    print("=" * 60)
    
    # 测试1: 关键词优先级
    priority_success = test_liuyao_priority()
    
    # 测试2: 具体案例
    case_success = test_specific_case()
    
    # 显示说明
    show_keyword_priority()
    
    # 显示示例
    show_examples()
    
    # 最终结果
    print("\n" + "=" * 60)
    print("🎉 六爻关键词优先级修复测试结果:")
    print(f"  关键词优先级测试: {'✅ 通过' if priority_success else '❌ 失败'}")
    print(f"  具体案例测试: {'✅ 通过' if case_success else '❌ 失败'}")
    
    all_success = priority_success and case_success
    
    if all_success:
        print("\n🎊 六爻关键词优先级修复完全成功！")
        print("\n🎯 **解决的核心问题:**")
        print("  ❌ '算一卦看运势' 被识别为综合分析")
        print("  ✅ 现在正确识别为六爻算卦")
        print()
        print("  ❌ 用户需要提供出生信息")
        print("  ✅ 现在六爻算卦即问即答")
        
        print("\n🚀 **现在的用户体验:**")
        print("  1. 用户: '帮我算一卦，看看今年运势'")
        print("  2. 系统: 识别为六爻算卦")
        print("  3. 系统: 使用当前时间起卦")
        print("  4. 系统: 直接返回卦象分析")
        print("  5. 用户: 无需任何个人信息")
        
        print("\n💡 **技术改进:**")
        print("  - 优化了关键词检测优先级")
        print("  - 增加了更多六爻表达方式")
        print("  - 避免了通用词汇的误判")
        print("  - 提升了意图识别准确性")
        
        print("\n🎉 **用户问题完全解决！**")
        print("**'帮我算一卦，看看今年运势' 现在能正确识别并即问即答！**")
    else:
        print("\n⚠️ 部分测试失败，需要进一步完善")

if __name__ == "__main__":
    main()
