#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的双Agent协作模式
"""

import asyncio
import sys
import time
from datetime import datetime
sys.path.append('.')

async def test_correct_dual_agent():
    """测试正确的双Agent协作模式"""
    print("🤖 测试正确的双Agent协作模式")
    print("=" * 80)
    print("正确模式: 沟通Agent主导 → 收集信息 → 调用计算Agent → 解释结果")
    print("=" * 80)
    
    try:
        # 导入新的Agent类
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        print("✅ 新Agent组件导入成功")
        
        # 创建Agent实例
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print(f"✅ 主控沟通Agent: {master_agent.agent_id}")
        print(f"✅ 计算Agent: {calculator_agent.agent_id}")
        print(f"✅ 简化协调器: {coordinator.coordinator_id}")
        
        # 测试对话流程
        session_id = "test_session_correct"
        
        print(f"\n🧪 开始测试对话流程...")
        print("-" * 50)
        
        # 测试1: 初始问候
        print("\n1️⃣ 测试初始问候")
        result1 = await coordinator.handle_user_message(session_id, "你好")
        print(f"用户: 你好")
        print(f"AI: {result1.get('response', '')[:100]}...")
        print(f"阶段: {result1.get('stage', 'unknown')}")
        print(f"成功: {result1.get('success', False)}")
        
        # 测试2: 算命请求
        print("\n2️⃣ 测试算命请求")
        result2 = await coordinator.handle_user_message(session_id, "我想看紫薇斗数")
        print(f"用户: 我想看紫薇斗数")
        print(f"AI: {result2.get('response', '')[:100]}...")
        print(f"阶段: {result2.get('stage', 'unknown')}")
        print(f"成功: {result2.get('success', False)}")
        
        # 测试3: 提供部分信息
        print("\n3️⃣ 测试提供部分信息")
        result3 = await coordinator.handle_user_message(session_id, "1988年6月1日")
        print(f"用户: 1988年6月1日")
        print(f"AI: {result3.get('response', '')[:100]}...")
        print(f"阶段: {result3.get('stage', 'unknown')}")
        print(f"成功: {result3.get('success', False)}")
        
        # 测试4: 提供完整信息
        print("\n4️⃣ 测试提供完整信息")
        result4 = await coordinator.handle_user_message(session_id, "午时，男性")
        print(f"用户: 午时，男性")
        print(f"AI: {result4.get('response', '')[:100]}...")
        print(f"阶段: {result4.get('stage', 'unknown')}")
        print(f"成功: {result4.get('success', False)}")
        
        # 等待一下，让计算Agent有时间处理
        print("\n⏳ 等待计算Agent处理...")
        await asyncio.sleep(2)
        
        # 检查计算状态
        status = await coordinator.get_session_status(session_id)
        print(f"计算状态: {status}")
        
        # 测试5: 后续问答
        print("\n5️⃣ 测试后续问答")
        result5 = await coordinator.handle_user_message(session_id, "我的事业运势如何？")
        print(f"用户: 我的事业运势如何？")
        print(f"AI: {result5.get('response', '')[:100]}...")
        print(f"阶段: {result5.get('stage', 'unknown')}")
        print(f"成功: {result5.get('success', False)}")
        
        # 统计结果
        print(f"\n📊 测试结果统计")
        print("-" * 50)
        
        results = [result1, result2, result3, result4, result5]
        success_count = sum(1 for r in results if r.get('success'))
        total_time = sum(r.get('processing_time', 0) for r in results)
        
        print(f"总测试数: {len(results)}")
        print(f"成功数: {success_count}")
        print(f"成功率: {success_count/len(results)*100:.1f}%")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均耗时: {total_time/len(results):.2f}秒")
        
        # 协调器统计
        coord_stats = coordinator.get_stats()
        print(f"\n协调器统计:")
        print(f"  总请求: {coord_stats['total_requests']}")
        print(f"  成功请求: {coord_stats['successful_requests']}")
        print(f"  成功率: {coord_stats.get('success_rate', 0)*100:.1f}%")
        print(f"  平均响应时间: {coord_stats['average_response_time']:.2f}秒")
        
        # Agent统计
        master_stats = master_agent.get_stats()
        calc_stats = calculator_agent.get_stats()
        
        print(f"\n主控Agent统计:")
        print(f"  处理消息: {master_stats['messages_processed']}")
        print(f"  平均耗时: {master_stats['average_processing_time']:.2f}秒")
        
        print(f"\n计算Agent统计:")
        print(f"  处理消息: {calc_stats['messages_processed']}")
        print(f"  平均耗时: {calc_stats['average_processing_time']:.2f}秒")
        
        # 评估结果
        print(f"\n🎯 协作模式评估")
        print("=" * 50)
        
        if success_count >= len(results) * 0.8:
            print("🌟 正确的双Agent协作模式测试通过！")
            print("✅ 沟通Agent成功主导对话流程")
            print("✅ 信息收集阶段工作正常")
            print("✅ 计算Agent按需调用")
            print("✅ 对话状态管理正确")
            
            print(f"\n🚀 协作模式优势:")
            print("  💬 自然的对话流程")
            print("  🎯 精确的信息收集")
            print("  🧮 按需计算调用")
            print("  🔄 完整的状态管理")
            
            return True
        else:
            print("⚠️  双Agent协作模式需要优化")
            print(f"  成功率: {success_count/len(results)*100:.1f}% (需要 ≥ 80%)")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_conversation_flow():
    """测试完整对话流程"""
    print("\n🗣️ 测试完整对话流程")
    print("-" * 50)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建新的Agent实例
        master_agent = MasterCustomerAgent("master_002")
        calculator_agent = FortuneCalculatorAgent("calc_002")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "flow_test_session"
        
        # 模拟完整对话
        conversation = [
            "你好，我想算命",
            "我想看八字算命",
            "我是1990年5月15日出生的",
            "上午10点，女性",
            "我的感情运势怎么样？"
        ]
        
        print("模拟对话:")
        for i, user_msg in enumerate(conversation, 1):
            print(f"\n{i}. 用户: {user_msg}")
            
            result = await coordinator.handle_user_message(session_id, user_msg)
            
            if result.get('success'):
                response = result.get('response', '')
                stage = result.get('stage', 'unknown')
                print(f"   AI: {response[:80]}...")
                print(f"   阶段: {stage}")
            else:
                print(f"   ❌ 失败: {result.get('error')}")
            
            # 短暂等待
            await asyncio.sleep(0.5)
        
        print("\n✅ 完整对话流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 对话流程测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 正确双Agent协作模式测试")
    print("=" * 80)
    print("目标: 验证沟通Agent主导的正确协作模式")
    print("=" * 80)
    
    # 测试1: 基础协作模式
    basic_success = await test_correct_dual_agent()
    
    # 测试2: 完整对话流程
    flow_success = await test_conversation_flow()
    
    # 最终结论
    print("\n" + "=" * 80)
    print("🏁 最终测试结论")
    print("=" * 80)
    
    if basic_success and flow_success:
        print("🎉 正确的双Agent协作模式验证成功！")
        print("\n💪 实现的正确模式:")
        print("  1️⃣ 沟通Agent主导整个对话流程")
        print("  2️⃣ 智能信息收集和状态管理")
        print("  3️⃣ 按需调用计算Agent进行算命")
        print("  4️⃣ 基于计算结果进行解释和问答")
        
        print("\n🌟 协作优势:")
        print("  💬 自然流畅的对话体验")
        print("  🎯 精确的信息收集过程")
        print("  🧮 高效的计算资源利用")
        print("  🔄 完整的状态和上下文管理")
        
        print("\n🚀 现在可以集成到Web界面！")
        return True
    else:
        print("💥 双Agent协作模式存在问题")
        if not basic_success:
            print("  - 基础协作模式测试失败")
        if not flow_success:
            print("  - 完整对话流程测试失败")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
