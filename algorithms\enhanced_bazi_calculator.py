#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版八字算命算法
基于py-iztro的准确八字 + 手动实现的传统分析
"""

try:
    import py_iztro
    IZTRO_AVAILABLE = True
    print("✅ py-iztro导入成功")
except ImportError as e:
    IZTRO_AVAILABLE = False
    print(f"❌ py-iztro导入失败: {e}")

class EnhancedBaziCalculator:
    """增强版八字算命计算器 - 基于py-iztro + 手动分析"""

    def __init__(self):
        if not IZTRO_AVAILABLE:
            raise ImportError("py-iztro未安装")

        # 五行映射
        self.wuxing_map = {
            # 天干五行
            '甲': '木', '乙': '木',
            '丙': '火', '丁': '火',
            '戊': '土', '己': '土',
            '庚': '金', '辛': '金',
            '壬': '水', '癸': '水',
            # 地支五行
            '子': '水', '丑': '土', '寅': '木', '卯': '木',
            '辰': '土', '巳': '火', '午': '火', '未': '土',
            '申': '金', '酉': '金', '戌': '土', '亥': '水'
        }

        # 地支藏干
        self.dizhi_canggan = {
            '子': ['癸'],
            '丑': ['己', '癸', '辛'],
            '寅': ['甲', '丙', '戊'],
            '卯': ['乙'],
            '辰': ['戊', '乙', '癸'],
            '巳': ['丙', '戊', '庚'],
            '午': ['丁', '己'],
            '未': ['己', '丁', '乙'],
            '申': ['庚', '壬', '戊'],
            '酉': ['辛'],
            '戌': ['戊', '辛', '丁'],
            '亥': ['壬', '甲']
        }

        # 十神关系（以日干为中心）
        self.shishen_map = {
            '木': {
                '木': '比肩/劫财', '火': '食神/伤官', '土': '偏财/正财',
                '金': '七杀/正官', '水': '偏印/正印'
            },
            '火': {
                '火': '比肩/劫财', '土': '食神/伤官', '金': '偏财/正财',
                '水': '七杀/正官', '木': '偏印/正印'
            },
            '土': {
                '土': '比肩/劫财', '金': '食神/伤官', '水': '偏财/正财',
                '木': '七杀/正官', '火': '偏印/正印'
            },
            '金': {
                '金': '比肩/劫财', '水': '食神/伤官', '木': '偏财/正财',
                '火': '七杀/正官', '土': '偏印/正印'
            },
            '水': {
                '水': '比肩/劫财', '木': '食神/伤官', '火': '偏财/正财',
                '土': '七杀/正官', '金': '偏印/正印'
            }
        }

    def calculate_enhanced_bazi(self, year: int, month: int, day: int, hour: int, gender: str = "男") -> dict:
        """计算增强版八字分析"""
        try:
            # 1. 使用py-iztro获取准确八字
            astro = py_iztro.Astro()

            # 时辰转换
            time_index = self._convert_hour_to_index(hour)

            astrolabe = astro.by_solar(
                solar_date_str=f"{year}-{month}-{day}",
                time_index=time_index,
                gender=gender,
                language="zh-CN"
            )

            chinese_date = astrolabe.chinese_date
            bazi_parts = chinese_date.split()

            if len(bazi_parts) != 4:
                return {"success": False, "error": f"八字格式异常: {chinese_date}"}

            year_pillar, month_pillar, day_pillar, hour_pillar = bazi_parts

            # 2. 详细分析
            analysis = self._analyze_bazi_detailed(
                year_pillar, month_pillar, day_pillar, hour_pillar, gender, year, month, day
            )

            # 3. 构建完整结果
            result = {
                "success": True,
                "birth_info": {
                    "datetime": f"{year}年{month}月{day}日{hour}时",
                    "gender": gender,
                    "solar": astrolabe.solar_date,
                    "lunar": astrolabe.lunar_date,
                    "zodiac": astrolabe.zodiac,
                    "sign": astrolabe.sign
                },
                "bazi_info": {
                    "chinese_date": chinese_date,
                    "year_pillar": year_pillar,
                    "month_pillar": month_pillar,
                    "day_pillar": day_pillar,
                    "hour_pillar": hour_pillar
                },
                "analysis": analysis,
                "calculation_type": "增强版八字分析（py-iztro + 手动算法）"
            }

            return result

        except Exception as e:
            return {"success": False, "error": f"增强八字分析失败: {str(e)}"}

    def _convert_hour_to_index(self, hour: int) -> int:
        """小时转时辰索引"""
        time_mapping = [
            (23, 1, 0),   # 子时
            (1, 3, 1),    # 丑时
            (3, 5, 2),    # 寅时
            (5, 7, 3),    # 卯时
            (7, 9, 4),    # 辰时
            (9, 11, 5),   # 巳时
            (11, 13, 6),  # 午时
            (13, 15, 7),  # 未时
            (15, 17, 8),  # 申时
            (17, 19, 9),  # 酉时
            (19, 21, 10), # 戌时
            (21, 23, 11)  # 亥时
        ]

        for start, end, index in time_mapping:
            if start <= hour < end or (start == 23 and hour >= 23):
                return index
        return 6  # 默认午时

    def _analyze_bazi_detailed(self, year_pillar: str, month_pillar: str,
                              day_pillar: str, hour_pillar: str, gender: str, year: int, month: int, day: int) -> dict:
        """详细分析八字"""

        # 1. 五行分析
        wuxing_analysis = self._analyze_wuxing(year_pillar, month_pillar, day_pillar, hour_pillar)

        # 2. 十神分析
        shishen_analysis = self._analyze_shishen(year_pillar, month_pillar, day_pillar, hour_pillar)

        # 3. 日主分析
        day_master = day_pillar[0]  # 日干
        day_master_element = self.wuxing_map[day_master]

        # 4. 格局分析
        pattern_analysis = self._analyze_pattern(wuxing_analysis, day_master_element)

        # 5. 性格特征
        personality = self._analyze_personality(day_master_element, wuxing_analysis)

        # 6. 大运分析
        dayun_analysis = self._analyze_dayun(day_master, gender, year, month, day)

        # 7. 纳音分析
        nayin_analysis = self._analyze_nayin(year_pillar, month_pillar, day_pillar, hour_pillar)

        return {
            "wuxing": wuxing_analysis,
            "shishen": shishen_analysis,
            "day_master": {
                "gan": day_master,
                "element": day_master_element,
                "strength": self._calculate_day_master_strength(day_master_element, wuxing_analysis)
            },
            "pattern": pattern_analysis,
            "personality": personality,
            "dayun": dayun_analysis,
            "nayin": nayin_analysis
        }

    def _analyze_wuxing(self, year_pillar: str, month_pillar: str,
                       day_pillar: str, hour_pillar: str) -> dict:
        """五行分析"""
        wuxing_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
        wuxing_details = {'木': [], '火': [], '土': [], '金': [], '水': []}

        pillars = [year_pillar, month_pillar, day_pillar, hour_pillar]
        pillar_names = ['年', '月', '日', '时']

        for i, pillar in enumerate(pillars):
            # 天干
            tiangan = pillar[0]
            if tiangan in self.wuxing_map:
                element = self.wuxing_map[tiangan]
                wuxing_count[element] += 1
                wuxing_details[element].append(f"{pillar_names[i]}干{tiangan}")

            # 地支
            dizhi = pillar[1]
            if dizhi in self.wuxing_map:
                element = self.wuxing_map[dizhi]
                wuxing_count[element] += 1
                wuxing_details[element].append(f"{pillar_names[i]}支{dizhi}")

            # 地支藏干
            if dizhi in self.dizhi_canggan:
                for canggan in self.dizhi_canggan[dizhi]:
                    if canggan in self.wuxing_map:
                        element = self.wuxing_map[canggan]
                        wuxing_count[element] += 0.5  # 藏干权重减半
                        wuxing_details[element].append(f"{pillar_names[i]}支{dizhi}藏{canggan}")

        # 计算强弱
        total = sum(wuxing_count.values())
        wuxing_strength = {}
        for element, count in wuxing_count.items():
            percentage = (count / total) * 100 if total > 0 else 0
            if percentage >= 30:
                strength = "旺"
            elif percentage >= 20:
                strength = "中"
            elif percentage >= 10:
                strength = "弱"
            else:
                strength = "极弱"
            wuxing_strength[element] = strength

        return {
            "count": wuxing_count,
            "details": wuxing_details,
            "strength": wuxing_strength,
            "total": total
        }

    def _analyze_shishen(self, year_pillar: str, month_pillar: str,
                        day_pillar: str, hour_pillar: str) -> dict:
        """十神分析"""
        day_master = day_pillar[0]  # 日干
        day_master_element = self.wuxing_map[day_master]

        shishen_result = {}
        pillars = [year_pillar, month_pillar, day_pillar, hour_pillar]
        pillar_names = ['年', '月', '日', '时']

        for i, pillar in enumerate(pillars):
            if i == 2:  # 跳过日柱（自己）
                continue

            # 分析天干
            tiangan = pillar[0]
            if tiangan in self.wuxing_map:
                element = self.wuxing_map[tiangan]
                if day_master_element in self.shishen_map and element in self.shishen_map[day_master_element]:
                    shishen = self.shishen_map[day_master_element][element]
                    shishen_result[f"{pillar_names[i]}干{tiangan}"] = shishen

        return shishen_result

    def _analyze_pattern(self, wuxing_analysis: dict, day_master_element: str) -> dict:
        """格局分析"""
        wuxing_count = wuxing_analysis["count"]

        # 简单格局判断
        max_element = max(wuxing_count, key=wuxing_count.get)
        max_count = wuxing_count[max_element]

        if max_element == day_master_element:
            pattern = "身旺格"
        elif max_count >= 3:
            pattern = f"{max_element}旺格"
        else:
            pattern = "平和格"

        return {
            "main_pattern": pattern,
            "description": f"以{max_element}为主导的命格"
        }

    def _analyze_personality(self, day_master_element: str, wuxing_analysis: dict) -> dict:
        """性格分析"""
        element_personality = {
            '木': "仁慈、正直、有条理、善良",
            '火': "热情、积极、礼貌、急躁",
            '土': "诚实、稳重、可靠、固执",
            '金': "坚强、果断、义气、严肃",
            '水': "智慧、灵活、机智、多变"
        }

        base_personality = element_personality.get(day_master_element, "")

        # 根据五行强弱调整
        strength = wuxing_analysis["strength"][day_master_element]
        if strength == "旺":
            modifier = "特质明显，个性突出"
        elif strength == "弱":
            modifier = "特质较弱，需要加强"
        else:
            modifier = "特质平衡"

        return {
            "base_traits": base_personality,
            "strength_modifier": modifier,
            "day_master_element": day_master_element
        }

    def _calculate_day_master_strength(self, day_master_element: str, wuxing_analysis: dict) -> str:
        """计算日主强弱"""
        day_count = wuxing_analysis["count"][day_master_element]
        total = wuxing_analysis["total"]

        percentage = (day_count / total) * 100 if total > 0 else 0

        if percentage >= 30:
            return "身旺"
        elif percentage >= 20:
            return "身中"
        else:
            return "身弱"

    def _analyze_dayun(self, day_master: str, gender: str, birth_year: int, birth_month: int, birth_day: int) -> dict:
        """分析大运"""
        try:
            # 大运起运年龄计算（简化版）
            # 男命阳年、女命阴年顺排，男命阴年、女命阴年逆排
            year_gan = str(birth_year)[-1]  # 取年份个位数
            is_yang_year = int(year_gan) % 2 == 0  # 偶数为阳年

            # 起运年龄（简化计算，实际需要考虑节气）
            start_age = 8 if (gender == "男" and is_yang_year) or (gender == "女" and not is_yang_year) else 7

            # 月柱干支作为大运起点
            month_ganzhi = self._get_month_ganzhi(birth_year, birth_month)

            # 生成10步大运
            dayun_list = []
            current_year = birth_year + start_age

            for i in range(10):
                dayun_ganzhi = self._get_next_ganzhi(month_ganzhi, i + 1)
                dayun_gan = dayun_ganzhi[0]
                dayun_zhi = dayun_ganzhi[1]

                # 计算十神关系
                day_master_element = self.wuxing_map[day_master]
                dayun_element = self.wuxing_map[dayun_gan]
                shishen = self.shishen_map[day_master_element].get(dayun_element, "未知")

                dayun_info = {
                    "ganzhi": dayun_ganzhi,
                    "gan": dayun_gan,
                    "zhi": dayun_zhi,
                    "shishen": shishen,
                    "start_year": current_year,
                    "end_year": current_year + 9,
                    "age_range": f"{start_age + i * 10}-{start_age + i * 10 + 9}岁"
                }

                dayun_list.append(dayun_info)
                current_year += 10

            return {
                "start_age": start_age,
                "dayun_list": dayun_list[:5],  # 只返回前5步大运
                "calculation_method": "简化大运推算"
            }

        except Exception as e:
            return {"error": f"大运推算失败: {str(e)}"}

    def _get_month_ganzhi(self, year: int, month: int) -> str:
        """获取月柱干支（简化版）"""
        # 月支固定：寅卯辰巳午未申酉戌亥子丑
        month_zhi = ["寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥", "子", "丑"][month - 1]

        # 月干根据年干推算（简化）
        # 先获取年干
        gan_list = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        year_gan_index = (year - 4) % 10  # 简化的年干计算
        month_gan_index = (year_gan_index * 2 + month - 1) % 10
        month_gan = gan_list[month_gan_index]

        return month_gan + month_zhi

    def _get_next_ganzhi(self, base_ganzhi: str, step: int) -> str:
        """获取下一个干支"""
        gan_list = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        zhi_list = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]

        base_gan = base_ganzhi[0]
        base_zhi = base_ganzhi[1]

        gan_index = (gan_list.index(base_gan) + step) % 10
        zhi_index = (zhi_list.index(base_zhi) + step) % 12

        return gan_list[gan_index] + zhi_list[zhi_index]

    def _analyze_nayin(self, year_pillar: str, month_pillar: str, day_pillar: str, hour_pillar: str) -> dict:
        """分析纳音五行"""
        # 纳音五行对照表（简化版）
        nayin_map = {
            "甲子": "海中金", "乙丑": "海中金", "丙寅": "炉中火", "丁卯": "炉中火",
            "戊辰": "大林木", "己巳": "大林木", "庚午": "路旁土", "辛未": "路旁土",
            "壬申": "剑锋金", "癸酉": "剑锋金", "甲戌": "山头火", "乙亥": "山头火",
            "丙子": "涧下水", "丁丑": "涧下水", "戊寅": "城头土", "己卯": "城头土",
            "庚辰": "白蜡金", "辛巳": "白蜡金", "壬午": "杨柳木", "癸未": "杨柳木",
            "甲申": "泉中水", "乙酉": "泉中水", "丙戌": "屋上土", "丁亥": "屋上土",
            "戊子": "霹雳火", "己丑": "霹雳火", "庚寅": "松柏木", "辛卯": "松柏木",
            "壬辰": "长流水", "癸巳": "长流水", "甲午": "砂中金", "乙未": "砂中金",
            "丙申": "山下火", "丁酉": "山下火", "戊戌": "平地木", "己亥": "平地木",
            "庚子": "壁上土", "辛丑": "壁上土", "壬寅": "金箔金", "癸卯": "金箔金",
            "甲辰": "覆灯火", "乙巳": "覆灯火", "丙午": "天河水", "丁未": "天河水",
            "戊申": "大驿土", "己酉": "大驿土", "庚戌": "钗钏金", "辛亥": "钗钏金",
            "壬子": "桑柘木", "癸丑": "桑柘木", "甲寅": "大溪水", "乙卯": "大溪水",
            "丙辰": "沙中土", "丁巳": "沙中土", "戊午": "天上火", "己未": "天上火",
            "庚申": "石榴木", "辛酉": "石榴木", "壬戌": "大海水", "癸亥": "大海水"
        }

        pillars = [year_pillar, month_pillar, day_pillar, hour_pillar]
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]

        nayin_result = {}
        for i, pillar in enumerate(pillars):
            nayin = nayin_map.get(pillar, "未知")
            nayin_result[pillar_names[i]] = {
                "ganzhi": pillar,
                "nayin": nayin
            }

        return nayin_result

    def calculate_with_traditional_analysis(self, year: int, month: int, day: int, hour: int, gender: str = "男") -> dict:
        """计算八字并进行传统分析"""
        try:
            # 先获取基础分析
            base_result = self.calculate_enhanced_bazi(year, month, day, hour, gender)

            if not base_result["success"]:
                return base_result

            # 添加传统分析
            analysis = base_result["analysis"]
            traditional_analysis = self._generate_traditional_analysis(analysis, gender)

            # 更新结果
            base_result["traditional_analysis"] = traditional_analysis
            base_result["calculation_type"] = "增强版八字分析+传统分析"

            return base_result

        except Exception as e:
            return {"success": False, "error": f"传统分析失败: {str(e)}"}

    def calculate_with_quantitative_analysis(self, year: int, month: int, day: int, hour: int, gender: str = "男") -> dict:
        """计算八字并进行量化分析"""
        try:
            # 先获取基础分析
            base_result = self.calculate_enhanced_bazi(year, month, day, hour, gender)

            if not base_result["success"]:
                return base_result

            # 添加量化分析
            analysis = base_result["analysis"]
            quantitative_analysis = self._generate_quantitative_analysis(analysis)

            # 更新结果
            base_result["quantitative_analysis"] = quantitative_analysis
            base_result["calculation_type"] = "增强版八字分析+量化分析"

            return base_result

        except Exception as e:
            return {"success": False, "error": f"量化分析失败: {str(e)}"}

    def _generate_traditional_analysis(self, analysis: dict, gender: str) -> dict:
        """生成传统分析"""
        traditional = {}

        # 1. 命格总评
        day_master = analysis["day_master"]
        pattern = analysis["pattern"]
        wuxing = analysis["wuxing"]

        traditional["命格总评"] = {
            "日主": f"{day_master['gan']}({day_master['element']})",
            "强弱": day_master["strength"],
            "格局": pattern["main_pattern"],
            "五行特点": self._summarize_wuxing_features(wuxing)
        }

        # 2. 性格特征
        personality = analysis["personality"]
        traditional["性格特征"] = {
            "基本性格": personality["base_traits"],
            "性格强度": personality["strength_modifier"],
            "主导元素": personality["day_master_element"]
        }

        # 3. 运势分析
        if "dayun" in analysis:
            dayun = analysis["dayun"]
            traditional["运势分析"] = {
                "起运年龄": f"{dayun['start_age']}岁",
                "当前大运": dayun["dayun_list"][0] if dayun["dayun_list"] else "未知",
                "运势特点": self._analyze_dayun_features(dayun)
            }

        # 4. 人生建议
        traditional["人生建议"] = self._generate_life_advice(analysis, gender)

        return traditional

    def _generate_quantitative_analysis(self, analysis: dict) -> dict:
        """生成量化分析"""
        quantitative = {}

        # 1. 五行得分
        wuxing = analysis["wuxing"]
        wuxing_scores = {}
        total_count = wuxing["total"]

        for element, count in wuxing["count"].items():
            score = (count / total_count) * 100 if total_count > 0 else 0
            wuxing_scores[element] = {
                "数量": count,
                "占比": f"{score:.1f}%",
                "得分": int(score * 2),  # 转换为200分制
                "等级": self._get_score_level(score)
            }

        quantitative["五行得分"] = wuxing_scores

        # 2. 综合评分
        day_master_element = analysis["day_master"]["element"]
        day_master_score = wuxing_scores[day_master_element]["得分"]

        quantitative["综合评分"] = {
            "日主得分": day_master_score,
            "平衡度": self._calculate_balance_score(wuxing["count"]),
            "总体评级": self._get_overall_rating(day_master_score)
        }

        # 3. 各方面评分
        quantitative["各方面评分"] = {
            "事业运": self._calculate_career_score(analysis),
            "财运": self._calculate_wealth_score(analysis),
            "感情运": self._calculate_love_score(analysis),
            "健康运": self._calculate_health_score(analysis)
        }

        return quantitative

    def _summarize_wuxing_features(self, wuxing: dict) -> str:
        """总结五行特点"""
        strength = wuxing["strength"]
        features = []

        for element, level in strength.items():
            if level == "旺":
                features.append(f"{element}旺")
            elif level == "极弱":
                features.append(f"{element}弱")

        return "、".join(features) if features else "五行平衡"

    def _analyze_dayun_features(self, dayun: dict) -> str:
        """分析大运特点"""
        if not dayun.get("dayun_list"):
            return "大运信息不完整"

        current_dayun = dayun["dayun_list"][0]
        shishen = current_dayun.get("shishen", "")

        if "比肩" in shishen or "劫财" in shishen:
            return "比劫运，适合合作发展"
        elif "食神" in shishen or "伤官" in shishen:
            return "食伤运，利于创新表达"
        elif "正财" in shishen or "偏财" in shishen:
            return "财运，利于求财发展"
        elif "正官" in shishen or "七杀" in shishen:
            return "官杀运，利于事业发展"
        elif "正印" in shishen or "偏印" in shishen:
            return "印运，利于学习提升"
        else:
            return "运势平稳"

    def _generate_life_advice(self, analysis: dict, gender: str) -> dict:
        """生成人生建议"""
        day_master_element = analysis["day_master"]["element"]
        strength = analysis["day_master"]["strength"]

        advice = {}

        # 基于日主五行的建议
        element_advice = {
            "木": "宜从事文教、环保、医疗等行业",
            "火": "宜从事能源、娱乐、餐饮等行业",
            "土": "宜从事房地产、农业、建筑等行业",
            "金": "宜从事金融、机械、军警等行业",
            "水": "宜从事贸易、运输、水利等行业"
        }

        advice["职业方向"] = element_advice.get(day_master_element, "")

        # 基于强弱的建议
        if strength == "身旺":
            advice["发展策略"] = "身旺宜泄，可多发挥才能，主动出击"
        elif strength == "身弱":
            advice["发展策略"] = "身弱宜扶，需要贵人相助，稳步发展"
        else:
            advice["发展策略"] = "身中平衡，进退有度，随机应变"

        # 基于性别的建议
        if gender == "男":
            advice["人生重点"] = "事业发展、财富积累、家庭责任"
        else:
            advice["人生重点"] = "家庭和谐、子女教育、个人修养"

        return advice

    def _get_score_level(self, percentage: float) -> str:
        """获取得分等级"""
        if percentage >= 30:
            return "很强"
        elif percentage >= 20:
            return "较强"
        elif percentage >= 10:
            return "一般"
        else:
            return "较弱"

    def _calculate_balance_score(self, wuxing_count: dict) -> int:
        """计算五行平衡度得分"""
        counts = list(wuxing_count.values())
        if not counts:
            return 0

        # 计算标准差，越小越平衡
        mean = sum(counts) / len(counts)
        variance = sum((x - mean) ** 2 for x in counts) / len(counts)
        std_dev = variance ** 0.5

        # 转换为得分（标准差越小得分越高）
        balance_score = max(0, 100 - int(std_dev * 20))
        return balance_score

    def _get_overall_rating(self, day_master_score: int) -> str:
        """获取总体评级"""
        if day_master_score >= 60:
            return "优秀"
        elif day_master_score >= 40:
            return "良好"
        elif day_master_score >= 20:
            return "一般"
        else:
            return "需要改善"

    def _calculate_career_score(self, analysis: dict) -> int:
        """计算事业运得分"""
        # 简化计算：基于日主强弱和格局
        day_master = analysis["day_master"]
        if day_master["strength"] == "身旺":
            return 75
        elif day_master["strength"] == "身中":
            return 65
        else:
            return 55

    def _calculate_wealth_score(self, analysis: dict) -> int:
        """计算财运得分"""
        # 简化计算：基于财星强弱
        wuxing = analysis["wuxing"]
        day_master_element = analysis["day_master"]["element"]

        # 找到财星（我克者为财）
        wealth_elements = []
        if day_master_element == "木":
            wealth_elements = ["土"]
        elif day_master_element == "火":
            wealth_elements = ["金"]
        elif day_master_element == "土":
            wealth_elements = ["水"]
        elif day_master_element == "金":
            wealth_elements = ["木"]
        elif day_master_element == "水":
            wealth_elements = ["火"]

        wealth_count = sum(wuxing["count"].get(element, 0) for element in wealth_elements)
        return min(80, int(wealth_count * 20))

    def _calculate_love_score(self, analysis: dict) -> int:
        """计算感情运得分"""
        # 简化计算：基于日主和配偶宫
        return 65  # 默认中等

    def _calculate_health_score(self, analysis: dict) -> int:
        """计算健康运得分"""
        # 简化计算：基于五行平衡度
        wuxing = analysis["wuxing"]
        balance_score = self._calculate_balance_score(wuxing["count"])
        return min(90, balance_score + 20)

def test_enhanced_bazi():
    """测试增强版八字算法"""
    if not IZTRO_AVAILABLE:
        print("❌ py-iztro未安装，无法测试")
        return

    try:
        calc = EnhancedBaziCalculator()

        print("=== 增强版八字算命完整测试 ===")
        print("测试数据：1988年6月1日11时男")

        # 测试1: 基础分析
        print("\n📊 1. 基础分析测试:")
        result1 = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")

        if result1["success"]:
            print("✅ 基础分析成功")

            # 显示新增功能
            analysis = result1["analysis"]

            # 大运分析
            if "dayun" in analysis:
                dayun = analysis["dayun"]
                print(f"  🎯 大运分析: 起运{dayun['start_age']}岁")
                if dayun["dayun_list"]:
                    first_dayun = dayun["dayun_list"][0]
                    print(f"    首步大运: {first_dayun['ganzhi']} ({first_dayun['shishen']}) {first_dayun['age_range']}")

            # 纳音分析
            if "nayin" in analysis:
                nayin = analysis["nayin"]
                print(f"  🎵 纳音五行:")
                for pillar_name, info in nayin.items():
                    print(f"    {pillar_name}: {info['ganzhi']} - {info['nayin']}")

        # 测试2: 传统分析
        print("\n📜 2. 传统分析测试:")
        result2 = calc.calculate_with_traditional_analysis(1988, 6, 1, 11, "男")

        if result2["success"]:
            print("✅ 传统分析成功")
            traditional = result2["traditional_analysis"]

            print(f"  命格总评: {traditional['命格总评']['格局']}")
            print(f"  性格特征: {traditional['性格特征']['基本性格']}")
            print(f"  人生建议: {traditional['人生建议']['职业方向']}")

        # 测试3: 量化分析
        print("\n📊 3. 量化分析测试:")
        result3 = calc.calculate_with_quantitative_analysis(1988, 6, 1, 11, "男")

        if result3["success"]:
            print("✅ 量化分析成功")
            quantitative = result3["quantitative_analysis"]

            print(f"  综合评分:")
            overall = quantitative["综合评分"]
            print(f"    日主得分: {overall['日主得分']}")
            print(f"    平衡度: {overall['平衡度']}")
            print(f"    总体评级: {overall['总体评级']}")

            print(f"  各方面评分:")
            aspects = quantitative["各方面评分"]
            for aspect, score in aspects.items():
                print(f"    {aspect}: {score}分")

        print("\n🎉 所有功能测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_bazi()
