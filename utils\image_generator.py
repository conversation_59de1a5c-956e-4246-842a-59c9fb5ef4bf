#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片生成器 - 统一的图片生成工具，支持紫薇斗数、八字、六爻图片生成
"""

import os
import logging
from typing import Dict, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

def generate_ziwei_chart(chart_data: Dict[str, Any]) -> Optional[str]:
    """
    生成紫薇斗数图片
    
    Args:
        chart_data: 紫薇斗数数据
        
    Returns:
        图片路径或None
    """
    try:
        # 导入现有的紫薇斗数图片生成功能
        import sys
        sys.path.append('.')
        
        from core.fortune_engine import FortuneEngine
        
        # 使用现有的图片生成逻辑
        timestamp = int(datetime.now().timestamp() * 1000)
        image_filename = f"ziwei_chart_{timestamp}.png"
        
        # 确保charts目录存在
        charts_dir = "charts"
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir)
        
        image_path = os.path.join(charts_dir, image_filename)
        
        # 调用现有的图片生成方法
        # 这里需要根据实际的图片生成逻辑进行调整
        success = _generate_ziwei_image_internal(chart_data, image_path)
        
        if success:
            logger.info(f"紫薇斗数图片生成成功: {image_path}")
            return image_path
        else:
            logger.warning("紫薇斗数图片生成失败")
            return None
            
    except Exception as e:
        logger.error(f"紫薇斗数图片生成异常: {e}")
        return None

def generate_bazi_chart(chart_data: Dict[str, Any]) -> Optional[str]:
    """
    生成八字图片
    
    Args:
        chart_data: 八字数据
        
    Returns:
        图片路径或None
    """
    try:
        timestamp = int(datetime.now().timestamp() * 1000)
        image_filename = f"bazi_chart_{timestamp}.png"
        
        # 确保charts目录存在
        charts_dir = "charts"
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir)
        
        image_path = os.path.join(charts_dir, image_filename)
        
        # 调用八字图片生成方法
        success = _generate_bazi_image_internal(chart_data, image_path)
        
        if success:
            logger.info(f"八字图片生成成功: {image_path}")
            return image_path
        else:
            logger.warning("八字图片生成失败")
            return None
            
    except Exception as e:
        logger.error(f"八字图片生成异常: {e}")
        return None

def generate_liuyao_chart(chart_data: Dict[str, Any]) -> Optional[str]:
    """
    生成六爻卦象图片
    
    Args:
        chart_data: 六爻数据
        
    Returns:
        图片路径或None
    """
    try:
        timestamp = int(datetime.now().timestamp() * 1000)
        image_filename = f"liuyao_chart_{timestamp}.png"
        
        # 确保charts目录存在
        charts_dir = "charts"
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir)
        
        image_path = os.path.join(charts_dir, image_filename)
        
        # 调用六爻图片生成方法
        success = _generate_liuyao_image_internal(chart_data, image_path)
        
        if success:
            logger.info(f"六爻卦象图片生成成功: {image_path}")
            return image_path
        else:
            logger.warning("六爻卦象图片生成失败")
            return None
            
    except Exception as e:
        logger.error(f"六爻卦象图片生成异常: {e}")
        return None

def _generate_ziwei_image_internal(chart_data: Dict[str, Any], image_path: str) -> bool:
    """内部紫薇斗数图片生成逻辑"""
    
    try:
        # 这里应该调用现有的紫薇斗数图片生成代码
        # 由于现有代码可能比较复杂，这里先做一个简化的实现
        
        # 导入现有的图片生成模块
        from utils.chart_generator import generate_ziwei_chart_image
        
        # 调用现有方法
        result = generate_ziwei_chart_image(chart_data, image_path)
        return result
        
    except ImportError:
        # 如果现有模块不存在，创建一个简单的占位图片
        logger.warning("现有紫薇斗数图片生成模块不存在，创建占位图片")
        return _create_placeholder_image(image_path, "紫薇斗数命盘")
        
    except Exception as e:
        logger.error(f"紫薇斗数图片生成内部错误: {e}")
        return False

def _generate_bazi_image_internal(chart_data: Dict[str, Any], image_path: str) -> bool:
    """内部八字图片生成逻辑"""
    
    try:
        # 导入现有的图片生成模块
        from utils.chart_generator import generate_bazi_chart_image
        
        # 调用现有方法
        result = generate_bazi_chart_image(chart_data, image_path)
        return result
        
    except ImportError:
        # 如果现有模块不存在，创建一个简单的占位图片
        logger.warning("现有八字图片生成模块不存在，创建占位图片")
        return _create_placeholder_image(image_path, "八字命理分析")
        
    except Exception as e:
        logger.error(f"八字图片生成内部错误: {e}")
        return False

def _generate_liuyao_image_internal(chart_data: Dict[str, Any], image_path: str) -> bool:
    """内部六爻图片生成逻辑"""
    
    try:
        # 导入现有的图片生成模块
        from utils.chart_generator import generate_liuyao_chart_image
        
        # 调用现有方法
        result = generate_liuyao_chart_image(chart_data, image_path)
        return result
        
    except ImportError:
        # 如果现有模块不存在，创建一个简单的占位图片
        logger.warning("现有六爻图片生成模块不存在，创建占位图片")
        return _create_placeholder_image(image_path, "六爻卦象分析")
        
    except Exception as e:
        logger.error(f"六爻图片生成内部错误: {e}")
        return False

def _create_placeholder_image(image_path: str, title: str) -> bool:
    """创建占位图片"""
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建一个简单的占位图片
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 尝试使用系统字体
        try:
            font = ImageFont.truetype("arial.ttf", 40)
        except:
            font = ImageFont.load_default()
        
        # 绘制标题
        text_bbox = draw.textbbox((0, 0), title, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        draw.text((x, y), title, fill='black', font=font)
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            small_font = ImageFont.truetype("arial.ttf", 20)
        except:
            small_font = ImageFont.load_default()
        
        draw.text((10, height - 30), f"生成时间: {timestamp}", fill='gray', font=small_font)
        
        # 保存图片
        image.save(image_path)
        
        logger.info(f"占位图片创建成功: {image_path}")
        return True
        
    except Exception as e:
        logger.error(f"占位图片创建失败: {e}")
        
        # 如果PIL也不可用，创建一个简单的文本文件
        try:
            with open(image_path.replace('.png', '.txt'), 'w', encoding='utf-8') as f:
                f.write(f"{title}\n生成时间: {datetime.now()}\n")
            return True
        except:
            return False

def cleanup_old_charts(max_age_hours: int = 24) -> int:
    """
    清理旧的图片文件
    
    Args:
        max_age_hours: 最大保留时间（小时）
        
    Returns:
        清理的文件数量
    """
    try:
        charts_dir = "charts"
        if not os.path.exists(charts_dir):
            return 0
        
        current_time = datetime.now()
        cleaned_count = 0
        
        for filename in os.listdir(charts_dir):
            file_path = os.path.join(charts_dir, filename)
            
            # 检查文件修改时间
            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            age_hours = (current_time - file_mtime).total_seconds() / 3600
            
            if age_hours > max_age_hours:
                try:
                    os.remove(file_path)
                    cleaned_count += 1
                    logger.debug(f"清理旧图片: {filename}")
                except Exception as e:
                    logger.warning(f"清理文件失败 {filename}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个旧图片文件")
        
        return cleaned_count
        
    except Exception as e:
        logger.error(f"清理旧图片失败: {e}")
        return 0

def get_chart_info(image_path: str) -> Dict[str, Any]:
    """
    获取图片信息
    
    Args:
        image_path: 图片路径
        
    Returns:
        图片信息
    """
    try:
        if not os.path.exists(image_path):
            return {"exists": False}
        
        stat = os.stat(image_path)
        
        return {
            "exists": True,
            "path": image_path,
            "size": stat.st_size,
            "created_time": datetime.fromtimestamp(stat.st_ctime),
            "modified_time": datetime.fromtimestamp(stat.st_mtime)
        }
        
    except Exception as e:
        logger.error(f"获取图片信息失败: {e}")
        return {"exists": False, "error": str(e)}
