#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查八字数据的具体结构
"""

import sys
sys.path.append('.')

import json
from pathlib import Path
from core.agents.fortune_calculator_agent import FortuneCalculatorAgent

def inspect_bazi_data():
    """检查八字数据的具体结构"""
    print("🔍 检查八字数据结构")
    print("=" * 50)
    
    try:
        # 获取缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        cache_file = cache_files[0]
        result_id = cache_file.stem
        cached_result = cache.get_result(result_id)
        
        raw_calculation = cached_result.raw_calculation
        
        # 详细检查八字数据
        if 'bazi' in raw_calculation:
            bazi_data = raw_calculation['bazi']
            print(f"📊 八字数据结构:")
            print(f"  类型: {type(bazi_data)}")
            print(f"  字段: {list(bazi_data.keys())}")
            
            # 检查干支数据
            if '干支' in bazi_data:
                ganzhi_data = bazi_data['干支']
                print(f"\n📋 干支数据:")
                print(f"  类型: {type(ganzhi_data)}")
                if isinstance(ganzhi_data, dict):
                    print(f"  字段: {list(ganzhi_data.keys())}")
                    for key, value in ganzhi_data.items():
                        print(f"    {key}: {value} ({type(value)})")
                else:
                    print(f"  内容: {ganzhi_data}")
                    
            # 检查五行数据
            if '五行' in bazi_data:
                wuxing_data = bazi_data['五行']
                print(f"\n🌟 五行数据:")
                print(f"  类型: {type(wuxing_data)}")
                if isinstance(wuxing_data, dict):
                    print(f"  字段: {list(wuxing_data.keys())}")
                    for key, value in wuxing_data.items():
                        print(f"    {key}: {value} ({type(value)})")
                else:
                    print(f"  内容: {wuxing_data}")
                    
            # 完整打印八字数据用于分析
            print(f"\n📄 完整八字数据:")
            print(json.dumps(bazi_data, ensure_ascii=False, indent=2))
            
        else:
            print("❌ 未找到八字数据")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    inspect_bazi_data()
