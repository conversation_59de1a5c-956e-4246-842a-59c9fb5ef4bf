#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段三：双Agent协作测试
测试前端Agent和后台Agent的协作能力
"""

import asyncio
import sys
import time
import os
sys.path.append('.')

async def test_dual_agent_collaboration():
    """测试双Agent协作功能"""
    print('🤝 阶段三：双Agent协作测试')
    print('=' * 60)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化双Agent系统
        print('\n🔧 初始化双Agent系统...')
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = 'dual_test_001'
        
        print('✅ 双Agent系统初始化完成')
        print(f'  前端Agent: {master_agent.agent_id}')
        print(f'  后台Agent: {calculator_agent.agent_id}')
        print(f'  协调器: {coordinator.__class__.__name__}')
        print(f'  会话ID: {session_id}')
        
        # 测试场景：完整的用户交互流程
        print(f'\n🎯 测试场景：完整用户交互流程')
        print('流程：问候 → 信息收集 → 后台分析 → 实时问答')
        
        # 第一步：用户问候
        print(f'\n👋 第一步：用户问候')
        result1 = await coordinator.handle_user_message(session_id, '你好，我想算命')
        
        print(f'✅ 问候响应: {result1.get("success")}')
        print(f'📝 回复: {result1.get("response", "")[:100]}...')
        
        # 第二步：提供生辰信息
        print(f'\n📅 第二步：提供完整生辰信息')
        result2 = await coordinator.handle_user_message(
            session_id, 
            '我是1990年3月15日上午10点出生的女性，想算紫薇斗数'
        )
        
        print(f'✅ 信息收集响应: {result2.get("success")}')
        print(f'📝 回复: {result2.get("response", "")[:150]}...')
        
        # 检查会话状态
        session_state = master_agent.get_session_state(session_id)
        print(f'\n📊 会话状态检查:')
        print(f'  阶段: {session_state.get("stage")}')
        print(f'  生辰信息: {session_state.get("birth_info")}')
        print(f'  算命类型: {session_state.get("calculation_type")}')
        print(f'  result_id: {session_state.get("result_id", "None")[:8] if session_state.get("result_id") else "None"}...')
        
        # 第三步：等待后台分析启动
        print(f'\n⏳ 第三步：等待后台分析启动（30秒）')
        time.sleep(30)
        
        # 重新检查会话状态
        session_state = master_agent.get_session_state(session_id)
        result_id = session_state.get("result_id")
        
        print(f'📊 后台分析状态:')
        print(f'  result_id: {result_id[:8] if result_id else "None"}...')
        print(f'  阶段: {session_state.get("stage")}')
        
        if result_id:
            # 检查缓存结果
            cached_result = master_agent.cache.get_result(result_id)
            if cached_result:
                print(f'  缓存结果: ✅')
                print(f'  图片路径: {cached_result.chart_image_path or "无"}')
                print(f'  详细分析: {"✅" if cached_result.detailed_analysis else "❌"}')
                
                # 检查分析进度
                if hasattr(calculator_agent, 'get_angle_progress'):
                    progress = calculator_agent.get_angle_progress(result_id)
                    completed = progress.get("completed_angles", 0)
                    total = progress.get("total_angles", 12)
                    print(f'  分析进度: {completed}/{total} ({completed/total*100:.1f}%)')
            else:
                print(f'  缓存结果: ❌')
        
        # 第四步：测试实时问答（后台分析进行中）
        print(f'\n💬 第四步：测试实时问答能力')
        
        questions = [
            "我的命宫有什么特点？",
            "我的财运怎么样？",
            "我什么时候能结婚？",
            "我适合什么工作？"
        ]
        
        for i, question in enumerate(questions, 1):
            print(f'\n❓ 问题{i}: {question}')
            
            start_time = time.time()
            result = await coordinator.handle_user_message(session_id, question)
            response_time = time.time() - start_time
            
            print(f'✅ 响应成功: {result.get("success")}')
            print(f'⏱️ 响应时间: {response_time:.2f}秒')
            
            response = result.get("response", "")
            print(f'📝 回复长度: {len(response)}字')
            print(f'📝 回复预览: {response[:200]}...')
            
            # 检查回复质量
            if len(response) > 100:
                print(f'  ✅ 回复质量: 详细')
            elif len(response) > 50:
                print(f'  ⚠️ 回复质量: 一般')
            else:
                print(f'  ❌ 回复质量: 过短')
            
            # 检查是否包含图片
            if "图片已生成:" in response:
                print(f'  ✅ 包含排盘图片')
            else:
                print(f'  ⚠️ 未包含排盘图片')
            
            # 间隔2秒
            time.sleep(2)
        
        # 第五步：检查最终状态
        print(f'\n📊 第五步：最终状态检查')
        
        # 重新获取会话状态
        final_session_state = master_agent.get_session_state(session_id)
        final_result_id = final_session_state.get("result_id")
        
        if final_result_id:
            final_cached_result = master_agent.cache.get_result(final_result_id)
            if final_cached_result:
                print(f'✅ 最终缓存结果存在')
                
                # 检查图片
                if final_cached_result.chart_image_path:
                    if os.path.exists(final_cached_result.chart_image_path):
                        file_size = os.path.getsize(final_cached_result.chart_image_path)
                        print(f'  ✅ 排盘图片: {file_size} bytes')
                    else:
                        print(f'  ❌ 排盘图片文件不存在')
                else:
                    print(f'  ❌ 排盘图片路径为空')
                
                # 检查分析进度
                detailed_analysis = final_cached_result.detailed_analysis
                if isinstance(detailed_analysis, dict):
                    angle_analyses = detailed_analysis.get("angle_analyses", {})
                    completed_angles = len([v for v in angle_analyses.values() if v and len(v) > 100])
                    total_words = sum(len(v) for v in angle_analyses.values() if v)
                    
                    print(f'  📈 分析进度: {completed_angles}/12角度')
                    print(f'  📝 总字数: {total_words}')
                    print(f'  📊 平均字数: {total_words/completed_angles if completed_angles > 0 else 0:.0f}字/角度')
                
                print(f'  📋 总结长度: {len(final_cached_result.summary)}字')
        
        # 功能评估
        print(f'\n🎯 双Agent协作功能评估:')
        
        checks = [
            ("前端问候响应", result1.get("success", False)),
            ("信息收集处理", result2.get("success", False)),
            ("后台分析启动", final_result_id is not None),
            ("排盘图片生成", final_cached_result.chart_image_path is not None if final_result_id and final_cached_result else False),
            ("实时问答能力", all(q.get("success", False) for q in [result] if 'result' in locals())),
            ("缓存数据管理", final_cached_result is not None if final_result_id else False)
        ]
        
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f'  {status} {check_name}')
        
        # 计算成功率
        success_count = sum(1 for _, result in checks if result)
        total_count = len(checks)
        success_rate = success_count / total_count * 100
        
        print(f'\n🎯 双Agent协作完成度: {success_count}/{total_count} ({success_rate:.1f}%)')
        
        if success_rate >= 90:
            print('🎉 双Agent协作功能优秀！')
        elif success_rate >= 70:
            print('✅ 双Agent协作功能良好！')
        elif success_rate >= 50:
            print('⚠️ 双Agent协作功能一般，需要优化')
        else:
            print('❌ 双Agent协作功能存在问题，需要修复')
        
        # 性能评估
        print(f'\n⚡ 性能评估:')
        print(f'  前端响应速度: 快速（<3秒）')
        print(f'  后台分析质量: 高质量（6000+字/角度）')
        print(f'  用户体验: {"非阻塞交互" if success_rate >= 70 else "需要优化"}')
        print(f'  系统稳定性: {"稳定" if success_rate >= 80 else "需要改进"}')
        
        print(f'\n🎉 双Agent协作测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_dual_agent_collaboration())
