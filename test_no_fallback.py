#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除所有备用算法后的系统行为
确保只有真实算法可用时才提供服务
"""

import sys
import os
sys.path.append('.')

def test_bazi_no_fallback():
    """测试八字工具移除备用算法后的行为"""
    print("测试八字工具 - 无备用算法")
    print("-" * 50)

    try:
        from core.tools.humanized_bazi_tool import HumanizedBaziTool

        # 创建工具实例
        tool = HumanizedBaziTool()

        # 测试数据
        intent = {
            "intent": "bazi",
            "original_message": "我1988年6月1日午时出生，男，想看八字算命",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }

        context = {}

        # 执行测试
        result = tool.execute(intent, context)

        if result.get("success"):
            print("❌ 不应该成功 - 真实算法不可用时应该失败")
            return False
        else:
            print("✅ 正确行为 - 真实算法不可用时拒绝服务")
            print(f"   错误信息: {result.get('error')}")
            print(f"   用户消息: {result.get('message')}")

            # 检查错误信息是否合理
            error_msg = result.get("message", "")
            if "真实算法" in error_msg or "不可用" in error_msg or "需要安装" in error_msg:
                print("✅ 错误信息合理，明确说明需要真实算法")
                return True
            else:
                print("⚠️ 错误信息不够明确")
                return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_liuyao_no_fallback():
    """测试六爻工具移除备用算法后的行为"""
    print("\n测试六爻工具 - 无备用算法")
    print("-" * 50)

    try:
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool

        # 创建工具实例
        tool = HumanizedLiuyaoTool()

        # 测试数据
        intent = {
            "intent": "liuyao",
            "original_message": "我想用六爻占卜我的感情运势",
            "entities": {
                "question": "我的感情运势如何？",
                "method": "time"
            }
        }

        context = {}

        # 执行测试
        result = tool.execute(intent, context)

        if result.get("success"):
            print("❌ 不应该成功 - 真实算法不可用时应该失败")
            return False
        else:
            print("✅ 正确行为 - 真实算法不可用时拒绝服务")
            print(f"   错误信息: {result.get('error')}")
            print(f"   用户消息: {result.get('message')}")

            # 检查错误信息是否合理
            error_msg = result.get("message", "")
            if "真实算法" in error_msg or "不可用" in error_msg or "需要安装" in error_msg:
                print("✅ 错误信息合理，明确说明需要真实算法")
                return True
            else:
                print("⚠️ 错误信息不够明确")
                return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ziwei_no_fallback():
    """测试紫薇工具是否有备用算法"""
    print("\n测试紫薇工具 - 检查是否有备用算法")
    print("-" * 50)

    try:
        from core.tools.tool_selector import ToolSelector

        selector = ToolSelector()

        # 测试意图
        intent_result = {
            "intent": "ziwei",
            "confidence": 0.9,
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }

        context = {}

        # 选择工具
        tool_result = selector.select_tool(intent_result, context)

        if tool_result.get("success"):
            result = tool_result.get("result", {})
            if result.get("success"):
                print("❌ 不应该成功 - 真实算法不可用时应该失败")
                return False
            else:
                print("✅ 正确行为 - 真实算法不可用时拒绝服务")
                print(f"   错误信息: {result.get('message')}")
                return True
        else:
            print("✅ 工具选择失败，符合预期")
            print(f"   错误信息: {tool_result.get('error')}")
            return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_humanized_engine_no_fallback():
    """测试人性化引擎在无真实算法时的行为"""
    print("\n测试人性化引擎 - 无备用算法")
    print("-" * 50)

    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine

        engine = HumanizedFortuneEngine()
        session_id = "test_no_fallback"

        # 测试不同类型的算命请求
        test_messages = [
            "我1988年6月1日午时出生，男，想看八字算命",
            "我想用六爻占卜我的感情运势",
            "请帮我排紫薇斗数命盘"
        ]

        all_failed_correctly = True

        for message in test_messages:
            print(f"\n测试消息: {message}")

            responses = engine.process_user_message(message, session_id + "_" + str(hash(message)))

            # 检查响应类型
            has_error_or_collection = False
            for response in responses:
                response_type = response.get("type", "unknown")
                if response_type in ["error", "info_collection", "general"]:
                    has_error_or_collection = True
                    content = response.get("content", "")
                    print(f"   响应类型: {response_type}")
                    print(f"   内容预览: {content[:100]}...")
                    break

            if has_error_or_collection:
                print("   ✅ 正确行为 - 没有提供算命分析")
            else:
                print("   ❌ 错误行为 - 不应该提供算命分析")
                all_failed_correctly = False

        return all_failed_correctly

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface_no_fallback():
    """测试Web界面在无真实算法时的行为"""
    print("\n测试Web界面 - 无备用算法")
    print("-" * 50)

    try:
        # 检查Web界面是否有备用API调用
        import os
        web_file = "web_demo/prompt_web.py"

        if os.path.exists(web_file):
            with open(web_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否有真正的备用机制（排除说明不使用备用的注释）
            fallback_indicators = [
                "call_api_fallback(",  # 函数调用
                "降级到备用",
                "使用备用分析方案",
                "def.*fallback.*:",  # 函数定义
            ]

            found_fallbacks = []
            for indicator in fallback_indicators:
                if indicator in content:
                    found_fallbacks.append(indicator)

            if found_fallbacks:
                print(f"⚠️ Web界面仍有备用机制: {found_fallbacks}")
                print("   建议移除这些备用机制，确保只使用真实算法")
                return False
            else:
                print("✅ Web界面没有发现备用机制")
                return True
        else:
            print("⚠️ Web界面文件不存在")
            return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("移除备用算法验证测试")
    print("=" * 80)
    print("目标: 确保系统只使用真实算法，拒绝任何备用或模拟数据")
    print("=" * 80)

    # 执行测试
    test_results = []

    # 1. 八字工具无备用算法测试
    test_results.append(("八字工具无备用算法", test_bazi_no_fallback()))

    # 2. 六爻工具无备用算法测试
    test_results.append(("六爻工具无备用算法", test_liuyao_no_fallback()))

    # 3. 紫薇工具无备用算法测试
    test_results.append(("紫薇工具无备用算法", test_ziwei_no_fallback()))

    # 4. 人性化引擎无备用算法测试
    test_results.append(("人性化引擎无备用算法", test_humanized_engine_no_fallback()))

    # 5. Web界面无备用算法测试
    test_results.append(("Web界面无备用算法", test_web_interface_no_fallback()))

    # 汇总结果
    print(f"\n移除备用算法验证测试结果")
    print("=" * 80)

    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有备用算法已成功移除！")
        print("\n✅ 系统现在的行为:")
        print("  🔒 只使用真实的传统算法")
        print("  ❌ 拒绝提供模拟或备用数据")
        print("  📢 明确告知用户需要安装真实算法模块")
        print("  🛡️ 保护传统文化的科学性和准确性")
        print("\n🌟 这确保了:")
        print("  📜 算命结果的真实性和可信度")
        print("  🎯 对传统文化的尊重和保护")
        print("  ⚡ 用户明确知道系统状态")
        print("  🔧 促进用户安装正确的算法模块")
        print("\n📋 符合您的要求：不设置任何备用，确保科学合理！")
    else:
        print("💥 仍有部分备用机制需要清理")
        print("\n🔧 需要进一步检查和移除:")
        print("  - 检查所有工具类的备用方法")
        print("  - 移除Web界面的备用API调用")
        print("  - 确保错误处理不包含模拟数据")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
