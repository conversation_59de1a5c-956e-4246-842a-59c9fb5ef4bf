#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证清理完成状态
检查所有记录是否已被清除
"""

import os
import sys

def check_directory_empty(dir_path, description):
    """检查目录是否为空"""
    try:
        if not os.path.exists(dir_path):
            print(f"⚠️ {description}: 目录不存在 - {dir_path}")
            return True
        
        files = os.listdir(dir_path)
        if not files:
            print(f"✅ {description}: 目录已清空")
            return True
        else:
            print(f"❌ {description}: 仍有 {len(files)} 个文件")
            for file in files[:5]:  # 只显示前5个文件
                print(f"   - {file}")
            if len(files) > 5:
                print(f"   ... 还有 {len(files) - 5} 个文件")
            return False
    except Exception as e:
        print(f"❌ {description}: 检查失败 - {e}")
        return False

def main():
    """主函数"""
    print("🧹 系统清理验证")
    print("=" * 60)
    
    # 检查需要清理的目录
    directories_to_check = [
        ("data/calculation_cache", "计算缓存目录"),
        ("data/sessions/sessions", "会话记录目录"),
        ("charts", "图表目录"),
        ("web_demo/charts", "Web演示图表目录"),
        ("exports", "导出文件目录")
    ]
    
    all_clean = True
    
    for dir_path, description in directories_to_check:
        is_clean = check_directory_empty(dir_path, description)
        if not is_clean:
            all_clean = False
    
    print("\n" + "=" * 60)
    
    if all_clean:
        print("🎉 系统清理完成！")
        print("\n✅ 所有记录已清除:")
        print("  - 计算缓存文件")
        print("  - 会话记录文件")
        print("  - 生成的图表文件")
        print("  - 导出的分析报告")
        print("  - 临时测试文件")
        
        print("\n🚀 系统现在处于干净状态，可以开始新的分析:")
        print("  streamlit run backend_agent_web.py")
        
        return True
    else:
        print("⚠️ 系统清理未完全完成")
        print("  请检查上述目录中的剩余文件")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
