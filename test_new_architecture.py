#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新架构基础组件
"""

import sys
import os
sys.path.append('.')

def test_basic_components():
    """测试基础组件"""
    print("🧪 测试新架构基础组件")
    print("=" * 80)
    
    try:
        # 1. 测试会话管理器
        print("1. 测试会话管理器...")
        from core.chat.session_manager import SessionManager
        
        session_manager = SessionManager()
        session = session_manager.get_session("test_session")
        
        print(f"✅ 会话管理器创建成功")
        print(f"   会话ID: {session['id']}")
        print(f"   创建时间: {session['created_at']}")
        
        # 2. 测试LLM客户端
        print("\n2. 测试LLM客户端...")
        from utils.llm_client import LLMClient, LLMClientFactory
        
        # 创建模拟客户端
        llm_client = LLMClient(
            api_key="test_key",
            base_url="http://localhost:8001/v1",
            model_name="test_model"
        )
        
        print(f"✅ LLM客户端创建成功")
        print(f"   模型: {llm_client.model_name}")
        print(f"   超时: {llm_client.default_timeout}秒")
        
        # 3. 测试工具基类
        print("\n3. 测试工具基类...")
        from core.tools.base_tool import BaseTool
        
        class TestTool(BaseTool):
            def __init__(self):
                super().__init__("test_tool", "测试工具")
            
            def can_handle(self, intent):
                return intent.get("tool_name") == "test"
            
            def execute(self, intent, context):
                return {"success": True, "message": "测试成功"}
        
        test_tool = TestTool()
        print(f"✅ 工具基类测试成功")
        print(f"   工具名称: {test_tool.name}")
        print(f"   工具描述: {test_tool.description}")
        
        # 4. 测试工具注册中心
        print("\n4. 测试工具注册中心...")
        from core.tools.tool_registry import ToolRegistry
        
        tool_registry = ToolRegistry()
        tool_registry.register_tool(test_tool, "test")
        
        tools = tool_registry.list_tools()
        print(f"✅ 工具注册中心测试成功")
        print(f"   注册工具: {tools}")
        
        # 5. 测试意图识别器（模拟）
        print("\n5. 测试意图识别器...")
        
        # 创建模拟提示词管理器
        class MockPromptManager:
            def get_intent_recognition_prompt(self, message, context):
                return f"分析消息: {message}"
        
        # 创建模拟LLM客户端
        class MockLLMClient:
            def chat(self, prompt, temperature=0.1):
                return '{"tool_name": "test", "question_type": "general", "confidence": 0.9}'
            
            def parse_json_response(self, response):
                import json
                try:
                    return json.loads(response)
                except:
                    return None
        
        from core.nlu.intent_recognizer import IntentRecognizer
        
        intent_recognizer = IntentRecognizer(MockLLMClient(), MockPromptManager())
        
        intent = intent_recognizer.recognize_intent("测试消息", {})
        print(f"✅ 意图识别器测试成功")
        print(f"   识别结果: {intent['tool_name']} - {intent['question_type']}")
        
        # 6. 测试对话引擎
        print("\n6. 测试对话引擎...")
        from core.chat.conversation_engine import ConversationEngine
        
        conversation_engine = ConversationEngine(
            session_manager, intent_recognizer, tool_registry
        )
        
        print(f"✅ 对话引擎创建成功")
        
        # 7. 测试完整对话流程
        print("\n7. 测试完整对话流程...")
        
        result = conversation_engine.process_message("test_session", "你好")
        
        print(f"✅ 完整对话流程测试成功")
        print(f"   响应: {result.get('message', '无响应')[:50]}...")
        print(f"   成功: {result.get('success')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_architecture_integration():
    """测试架构集成"""
    print("\n🔗 测试架构集成")
    print("=" * 80)
    
    try:
        # 创建完整的架构实例
        from core.chat.session_manager import SessionManager
        from core.tools.tool_registry import ToolRegistry
        from core.nlu.intent_recognizer import IntentRecognizer
        from core.chat.conversation_engine import ConversationEngine
        
        # 模拟组件
        class MockLLMClient:
            def chat(self, prompt, temperature=0.1):
                if "意图" in prompt or "tool_name" in prompt:
                    return '{"tool_name": "general", "question_type": "general", "confidence": 0.9}'
                return "这是一个测试响应"
            
            def parse_json_response(self, response):
                import json
                try:
                    return json.loads(response)
                except:
                    return None
        
        class MockPromptManager:
            def get_intent_recognition_prompt(self, message, context):
                return f"分析消息: {message}"
        
        # 创建组件
        session_manager = SessionManager()
        tool_registry = ToolRegistry()
        intent_recognizer = IntentRecognizer(MockLLMClient(), MockPromptManager())
        conversation_engine = ConversationEngine(session_manager, intent_recognizer, tool_registry)
        
        # 测试多轮对话
        print("测试多轮对话...")
        
        # 第一轮
        result1 = conversation_engine.process_message("user123", "你好")
        print(f"第1轮 - 成功: {result1.get('success')}")
        
        # 第二轮
        result2 = conversation_engine.process_message("user123", "帮助")
        print(f"第2轮 - 成功: {result2.get('success')}")
        
        # 第三轮
        result3 = conversation_engine.process_message("user123", "状态")
        print(f"第3轮 - 成功: {result3.get('success')}")
        
        # 检查会话状态
        session = session_manager.get_session("user123")
        print(f"会话消息数: {len(session['history'])}")
        
        print("✅ 架构集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 架构集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_system():
    """测试工具系统"""
    print("\n🛠️ 测试工具系统")
    print("=" * 80)
    
    try:
        from core.tools.base_tool import BaseTool
        from core.tools.tool_registry import ToolRegistry
        
        # 创建多个测试工具
        class ZiweiTool(BaseTool):
            def __init__(self):
                super().__init__("ziwei", "紫薇斗数工具")
            
            def can_handle(self, intent):
                return intent.get("tool_name") == "ziwei"
            
            def execute(self, intent, context):
                return {"success": True, "message": "紫薇斗数分析完成"}
            
            def get_required_entities(self):
                return ["birth_info"]
        
        class BaziTool(BaseTool):
            def __init__(self):
                super().__init__("bazi", "八字算命工具")
            
            def can_handle(self, intent):
                return intent.get("tool_name") == "bazi"
            
            def execute(self, intent, context):
                return {"success": True, "message": "八字分析完成"}
            
            def get_required_entities(self):
                return ["birth_info"]
        
        class GeneralTool(BaseTool):
            def __init__(self):
                super().__init__("general", "一般对话工具")
            
            def can_handle(self, intent):
                return intent.get("tool_name") == "general"
            
            def execute(self, intent, context):
                return {"success": True, "message": "一般对话处理完成"}
        
        # 注册工具
        registry = ToolRegistry()
        registry.register_tool(ZiweiTool(), "fortune")
        registry.register_tool(BaziTool(), "fortune")
        registry.register_tool(GeneralTool(), "chat")
        
        print(f"注册工具数: {len(registry.list_tools())}")
        print(f"工具分类: {registry.list_categories()}")
        
        # 测试工具查找
        intent = {"tool_name": "ziwei", "entities": {}}
        suitable_tools = registry.find_tools_for_intent(intent)
        print(f"找到合适工具: {[t.name for t in suitable_tools]}")
        
        # 测试工具执行
        result = registry.execute_tool("general", intent, {}, "test_session")
        print(f"工具执行结果: {result.get('success')}")
        
        # 测试自动选择
        auto_result = registry.auto_select_and_execute(intent, {}, "test_session")
        print(f"自动选择结果: {auto_result.get('success')}")
        
        print("✅ 工具系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 工具系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🏗️ 新架构基础组件测试")
    print("=" * 100)
    
    # 测试基础组件
    basic_success = test_basic_components()
    
    # 测试架构集成
    integration_success = test_architecture_integration()
    
    # 测试工具系统
    tool_success = test_tool_system()
    
    # 总结
    print("\n" + "=" * 100)
    print("🎉 测试结果总结:")
    print(f"  基础组件测试: {'✅ 成功' if basic_success else '❌ 失败'}")
    print(f"  架构集成测试: {'✅ 成功' if integration_success else '❌ 失败'}")
    print(f"  工具系统测试: {'✅ 成功' if tool_success else '❌ 失败'}")
    
    if all([basic_success, integration_success, tool_success]):
        print("\n🎊 所有测试通过！新架构基础组件工作正常！")
        print("\n💡 **架构优势验证:**")
        print("  ✅ 模块化设计 - 每个组件独立工作")
        print("  ✅ 统一接口 - 所有工具使用相同接口")
        print("  ✅ 会话管理 - 支持多轮对话和上下文记忆")
        print("  ✅ 意图识别 - 智能理解用户需求")
        print("  ✅ 工具系统 - 灵活的功能扩展机制")
        print()
        print("🚀 **下一步: 开始迁移现有功能到新架构**")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    main()
