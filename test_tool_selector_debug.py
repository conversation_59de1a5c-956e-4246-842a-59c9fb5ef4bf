#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试工具选择器数据传递问题
"""

import sys
import json
sys.path.append('.')

def test_tool_selector_data_flow():
    """测试工具选择器数据流"""
    print('🔧 工具选择器数据流调试')
    print('=' * 50)
    
    try:
        from core.tools.tool_selector import ToolSelector
        
        tool_selector = ToolSelector()
        
        # 测试数据
        intent = {
            "intent": "ziwei",
            "confidence": 0.9,
            "original_message": "测试紫薇斗数计算",
            "entities": {
                "birth_year": "1988",
                "birth_month": "6",
                "birth_day": "1",
                "birth_hour": "午时",
                "gender": "男"
            }
        }
        
        print(f'📤 输入意图: {intent}')
        
        # 调用工具选择器
        result = tool_selector.select_tool(intent, {})
        
        print(f'\n📥 工具选择器完整结果:')
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 逐层解析结果
        if result.get("success"):
            print(f'\n🔍 逐层解析:')
            print(f'  success: {result.get("success")}')
            print(f'  tool_name: {result.get("tool_name")}')
            
            tool_result = result.get("result", {})
            print(f'  result类型: {type(tool_result)}')
            print(f'  result键: {list(tool_result.keys()) if isinstance(tool_result, dict) else "非字典"}')
            
            if isinstance(tool_result, dict):
                print(f'  result.success: {tool_result.get("success")}')
                print(f'  result.type: {tool_result.get("type")}')
                print(f'  result.message: {tool_result.get("message")}')
                
                # 检查calculation_result
                calc_result = tool_result.get("calculation_result", {})
                print(f'  calculation_result类型: {type(calc_result)}')
                print(f'  calculation_result键: {list(calc_result.keys()) if isinstance(calc_result, dict) else "非字典"}')
                
                if isinstance(calc_result, dict):
                    print(f'  calculation_result.success: {calc_result.get("success")}')
                    
                    # 检查raw_result
                    raw_result = calc_result.get("raw_result", {})
                    print(f'  raw_result类型: {type(raw_result)}')
                    print(f'  raw_result键: {list(raw_result.keys()) if isinstance(raw_result, dict) else "非字典"}')
                    
                    if isinstance(raw_result, dict):
                        palaces = raw_result.get("palaces", {})
                        print(f'  palaces数量: {len(palaces)}')
                        if palaces:
                            print(f'  宫位名称: {list(palaces.keys())}')
                            
                            # 检查命宫
                            ming_gong = palaces.get("命宫", {})
                            if ming_gong:
                                print(f'  命宫数据: {ming_gong}')
                        else:
                            print(f'  ❌ palaces为空')
                    else:
                        print(f'  ❌ raw_result不是字典')
                else:
                    print(f'  ❌ calculation_result不是字典')
            else:
                print(f'  ❌ result不是字典')
        else:
            print(f'❌ 工具选择器失败: {result.get("error")}')
        
        # 保存完整结果用于分析
        with open('debug_tool_selector_full.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f'\n📁 完整结果已保存到: debug_tool_selector_full.json')
        
        # 直接测试人性化工具
        print(f'\n🔮 直接测试人性化紫薇工具:')
        
        from core.tools.humanized_ziwei_tool import HumanizedZiweiTool
        
        ziwei_tool = HumanizedZiweiTool()
        direct_result = ziwei_tool.execute(intent, {})
        
        print(f'  直接工具结果success: {direct_result.get("success")}')
        print(f'  直接工具结果type: {direct_result.get("type")}')
        
        if direct_result.get("success"):
            calc_result = direct_result.get("calculation_result", {})
            if calc_result.get("success"):
                raw_result = calc_result.get("raw_result", {})
                palaces = raw_result.get("palaces", {})
                print(f'  直接工具宫位数量: {len(palaces)}')
                
                # 保存直接工具结果
                with open('debug_direct_tool_result.json', 'w', encoding='utf-8') as f:
                    json.dump(direct_result, f, ensure_ascii=False, indent=2)
                print(f'  📁 直接工具结果已保存到: debug_direct_tool_result.json')
            else:
                print(f'  ❌ 直接工具calculation_result失败: {calc_result.get("error")}')
        else:
            print(f'  ❌ 直接工具失败: {direct_result.get("error")}')
        
        # 对比分析
        print(f'\n📊 对比分析:')
        
        # 工具选择器路径
        selector_palaces = 0
        if result.get("success"):
            tool_result = result.get("result", {})
            if tool_result.get("success"):
                calc_result = tool_result.get("calculation_result", {})
                if calc_result.get("success"):
                    raw_result = calc_result.get("raw_result", {})
                    selector_palaces = len(raw_result.get("palaces", {}))
        
        # 直接工具路径
        direct_palaces = 0
        if direct_result.get("success"):
            calc_result = direct_result.get("calculation_result", {})
            if calc_result.get("success"):
                raw_result = calc_result.get("raw_result", {})
                direct_palaces = len(raw_result.get("palaces", {}))
        
        print(f'  工具选择器宫位数: {selector_palaces}')
        print(f'  直接工具宫位数: {direct_palaces}')
        
        if selector_palaces == direct_palaces and direct_palaces > 0:
            print(f'  ✅ 数据传递正常')
        elif direct_palaces > 0 and selector_palaces == 0:
            print(f'  ❌ 工具选择器数据传递有问题')
        else:
            print(f'  ❌ 两者都有问题')
        
        print(f'\n🎉 工具选择器调试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tool_selector_data_flow()
