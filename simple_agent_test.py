#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Agent调用测试
"""

import asyncio
import sys
import logging
sys.path.append('.')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_direct_agent_call():
    """直接测试Agent调用"""
    print("🔍 直接测试Agent调用")
    print("=" * 50)

    try:
        # 导入Agent
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import agent_registry, AgentMessage, MessageType

        # 创建Agent
        print("1. 创建Agent...")
        master_agent = MasterCustomerAgent("direct_master")
        calculator_agent = FortuneCalculatorAgent("direct_calc")

        # 注册Agent
        print("2. 注册Agent...")
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)

        # 验证注册
        print("3. 验证注册...")
        all_agents = agent_registry.get_all_agents()
        print(f"   注册的Agent数量: {len(all_agents)}")
        for agent in all_agents:
            print(f"   - {agent.agent_id}: {type(agent).__name__}")

        # 测试主控Agent能否找到计算Agent
        print("4. 测试Agent查找...")
        calc_agents = agent_registry.get_agents_by_type("FortuneCalculatorAgent")
        print(f"   找到计算Agent数量: {len(calc_agents)}")

        if len(calc_agents) == 0:
            print("❌ 无法找到计算Agent")
            return False

        # 直接调用主控Agent
        print("5. 直接调用主控Agent...")

        # 构建消息
        from datetime import datetime
        message = AgentMessage(
            message_id="test_001",
            message_type=MessageType.COMMUNICATION_REQUEST,
            sender_id="test_user",
            receiver_id=master_agent.agent_id,
            content={
                "user_message": "我想看紫薇斗数，1988年6月1日午时出生，男性",
                "session_id": "test_session_direct"
            },
            timestamp=datetime.now().isoformat()
        )

        # 获取初始统计
        initial_master_stats = master_agent.get_stats()
        initial_calc_stats = calculator_agent.get_stats()

        print(f"   调用前 - 主控Agent消息数: {initial_master_stats['messages_processed']}")
        print(f"   调用前 - 计算Agent消息数: {initial_calc_stats['messages_processed']}")

        # 调用主控Agent
        print("6. 执行调用...")
        response = await master_agent.process_message(message)

        # 获取调用后统计
        final_master_stats = master_agent.get_stats()
        final_calc_stats = calculator_agent.get_stats()

        print(f"   调用后 - 主控Agent消息数: {final_master_stats['messages_processed']}")
        print(f"   调用后 - 计算Agent消息数: {final_calc_stats['messages_processed']}")

        # 分析结果
        master_increase = final_master_stats['messages_processed'] - initial_master_stats['messages_processed']
        calc_increase = final_calc_stats['messages_processed'] - initial_calc_stats['messages_processed']

        print(f"\n📊 调用分析:")
        print(f"   主控Agent消息增加: {master_increase}")
        print(f"   计算Agent消息增加: {calc_increase}")
        print(f"   响应成功: {response.success}")
        print(f"   响应数据: {len(str(response.data))} 字符")

        # 显示响应内容
        if response.success:
            response_text = response.data.get('response', '')
            print(f"   响应内容: {response_text[:200]}...")
        else:
            print(f"   错误信息: {response.error}")

        # 验证结果
        print(f"\n🎯 验证结果:")

        success_checks = []

        # 检查1: 主控Agent被调用
        if master_increase > 0:
            success_checks.append("✅ 主控Agent被调用")
        else:
            success_checks.append("❌ 主控Agent未被调用")

        # 检查2: 计算Agent被调用
        if calc_increase > 0:
            success_checks.append("✅ 计算Agent被调用")
        else:
            success_checks.append("❌ 计算Agent未被调用")

        # 检查3: 响应成功
        if response.success:
            success_checks.append("✅ 响应处理成功")
        else:
            success_checks.append("❌ 响应处理失败")

        # 检查4: 响应包含内容
        if response.success and len(response.data.get('response', '')) > 100:
            success_checks.append("✅ 响应包含详细内容")
        else:
            success_checks.append("❌ 响应内容不足")

        for check in success_checks:
            print(f"   {check}")

        success_count = sum(1 for check in success_checks if "✅" in check)
        total_count = len(success_checks)

        print(f"\n📈 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")

        if calc_increase > 0:
            print(f"\n🎉 关键成功: 主控Agent确实调用了计算Agent！")
            print(f"✅ Agent间调用链路正常工作")
            return True
        else:
            print(f"\n⚠️  关键问题: 主控Agent没有调用计算Agent")
            print(f"❌ 需要检查调用逻辑")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_step_by_step():
    """分步测试"""
    print(f"\n🔧 分步测试Agent调用逻辑")
    print("-" * 50)

    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import agent_registry

        # 创建Agent
        master_agent = MasterCustomerAgent("step_master")
        calculator_agent = FortuneCalculatorAgent("step_calc")

        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)

        print("1. 测试信息提取...")
        test_message = "我想看紫薇斗数，1988年6月1日午时出生，男性"
        birth_info = master_agent._extract_birth_info(test_message)
        print(f"   提取的信息: {birth_info}")

        print("2. 测试信息完整性检查...")
        is_complete = master_agent._is_birth_info_complete(birth_info)
        print(f"   信息完整: {is_complete}")

        print("3. 测试计算Agent查找...")
        calc_agent = master_agent._get_calculator_agent()
        print(f"   找到计算Agent: {calc_agent.agent_id if calc_agent else 'None'}")

        if calc_agent:
            print("4. 测试直接调用计算Agent...")

            # 模拟会话状态
            session_state = {
                "birth_info": {
                    "year": "1988",
                    "month": "6",
                    "day": "1",
                    "hour": "午时",
                    "gender": "男"
                },
                "calculation_type": "ziwei",
                "session_id": "step_test"
            }

            # 调用计算Agent
            calc_result = await master_agent._call_calculator_agent(session_state)
            print(f"   计算结果: {calc_result}")

            if calc_result and calc_result.get("success"):
                print("✅ 计算Agent调用成功！")
                return True
            else:
                print("❌ 计算Agent调用失败")
                return False
        else:
            print("❌ 无法找到计算Agent")
            return False

    except Exception as e:
        print(f"❌ 分步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧪 简化Agent调用测试")
    print("=" * 80)

    # 测试1: 直接Agent调用
    direct_success = await test_direct_agent_call()

    # 测试2: 分步测试
    step_success = await test_step_by_step()

    # 最终结论
    print(f"\n" + "=" * 80)
    print("🏁 测试结论")
    print("=" * 80)

    if direct_success or step_success:
        print("🎉 Agent调用测试成功！")
        print("✅ 主控Agent能够调用计算Agent")
        print("✅ 双Agent协作链路正常")

        print(f"\n🚀 现在可以在Web界面体验真正的双Agent协作：")
        print(f"   访问: http://localhost:8504")

        return True
    else:
        print("💥 Agent调用测试失败")
        print("❌ 主控Agent无法正确调用计算Agent")
        print("❌ 需要进一步调试")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
