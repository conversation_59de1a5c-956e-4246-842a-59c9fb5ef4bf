#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成真正专业的紫薇斗数图表
完全按照传统排盘样式设计
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_professional_ziwei():
    """生成专业的紫薇斗数图表"""
    print("🎨 生成专业紫薇斗数图表")
    print("=" * 50)

    try:
        # 获取数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")

        if not result.get("success"):
            print(f"❌ 分析失败: {result.get('error')}")
            return False

        # 创建标准大小的画布
        fig, ax = plt.subplots(1, 1, figsize=(16, 16))
        ax.set_xlim(0, 16)
        ax.set_ylim(0, 16)
        ax.set_aspect('equal')
        ax.axis('off')

        # 白色背景
        fig.patch.set_facecolor('white')

        # 1. 绘制标题
        draw_title(ax, result)

        # 2. 绘制标准12宫格
        draw_standard_palaces(ax, result)

        # 3. 在中央绘制八字信息
        draw_center_bazi(ax, result)

        # 保存图片
        output_file = "professional_ziwei.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')

        print(f"✅ 专业图表生成成功: {output_file}")
        plt.show()

        return True

    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def draw_title(ax, result):
    """绘制标题"""
    birth_info = result.get("birth_info", {})

    # 主标题
    ax.text(8, 15, "紫薇斗数命盘", ha='center', va='center',
           fontsize=20, fontweight='bold', color='black')

    # 出生信息
    datetime_str = birth_info.get('datetime', '1988年6月1日11时')
    ax.text(8, 14.3, datetime_str, ha='center', va='center',
           fontsize=14, color='black')

def draw_standard_palaces(ax, result):
    """绘制标准的12宫格"""
    ziwei_data = result.get("ziwei_analysis", {})
    palaces = ziwei_data.get("palaces", {})

    # 标准12宫布局 - 4x4网格，中间4格空出
    palace_positions = {
        # 第一行
        "子女宫": (0, 3), "财帛宫": (1, 3), "疾厄宫": (2, 3), "迁移宫": (3, 3),
        # 第二行
        "夫妻宫": (0, 2), "奴仆宫": (3, 2),
        # 第三行
        "兄弟宫": (0, 1), "官禄宫": (3, 1),
        # 第四行
        "命宫": (0, 0), "父母宫": (1, 0), "福德宫": (2, 0), "田宅宫": (3, 0)
    }

    # 宫格参数
    cell_size = 3.5
    start_x = 1
    start_y = 2

    for palace_name, (col, row) in palace_positions.items():
        x = start_x + col * cell_size
        y = start_y + row * cell_size

        palace_data = palaces.get(palace_name, {})
        draw_palace_cell(ax, x, y, cell_size, palace_name, palace_data)

def draw_palace_cell(ax, x, y, size, palace_name, palace_data):
    """绘制单个宫格"""
    # 宫格边框 - 统一样式
    rect = Rectangle((x, y), size, size,
                    linewidth=2, edgecolor='black',
                    facecolor='white', alpha=1.0)
    ax.add_patch(rect)

    # 宫位名称（左上角）
    ax.text(x + 0.2, y + size - 0.3, palace_name,
           ha='left', va='top', fontsize=11,
           fontweight='bold', color='black')

    # 地支（右上角）
    position = palace_data.get("position", "")
    if position:
        ax.text(x + size - 0.2, y + size - 0.3, f"({position})",
               ha='right', va='top', fontsize=10,
               color='blue')

    # 主星（红色，较大字体）- 改进布局
    major_stars = palace_data.get("major_stars", [])
    if major_stars:
        # 主星区域：从上往下，每行最多2个
        y_start = y + size - 0.8
        for i, star in enumerate(major_stars[:4]):  # 最多显示4个主星
            row = i // 2
            col = i % 2
            star_x = x + 0.3 + col * 1.4
            star_y = y_start - row * 0.35

            ax.text(star_x, star_y, star,
                   ha='left', va='top', fontsize=10,
                   fontweight='bold', color='red')

    # 副星（绿色，小字体）- 改进布局
    minor_stars = palace_data.get("minor_stars", [])
    if minor_stars:
        # 副星区域：中间部分，每行最多3个
        y_start = y + size - 1.8
        for i, star in enumerate(minor_stars[:9]):  # 最多显示9个副星
            row = i // 3
            col = i % 3
            star_x = x + 0.2 + col * 1.0
            star_y = y_start - row * 0.25

            ax.text(star_x, star_y, star,
                   ha='left', va='top', fontsize=8,
                   color='green')

    # 四化（左下角，橙色）
    transformations = palace_data.get("transformations", [])
    if transformations:
        for i, trans in enumerate(transformations[:3]):
            ax.text(x + 0.2, y + 0.8 - i * 0.25, trans,
                   ha='left', va='bottom', fontsize=8,
                   fontweight='bold', color='orange')

    # 添加宫位序号（小圆圈）
    palace_numbers = {
        "命宫": "1", "兄弟宫": "2", "夫妻宫": "3", "子女宫": "4",
        "财帛宫": "5", "疾厄宫": "6", "迁移宫": "7", "奴仆宫": "8",
        "官禄宫": "9", "田宅宫": "10", "福德宫": "11", "父母宫": "12"
    }

    if palace_name in palace_numbers:
        number = palace_numbers[palace_name]
        # 小圆圈背景
        circle = plt.Circle((x + size - 0.5, y + 0.5), 0.15,
                          facecolor='blue', edgecolor='white',
                          linewidth=1)
        ax.add_patch(circle)

        # 数字
        ax.text(x + size - 0.5, y + 0.5, number,
               ha='center', va='center', fontsize=8,
               fontweight='bold', color='white')

    # 添加更多专业细节
    add_professional_details(ax, x, y, size, palace_name, palace_data)

def add_professional_details(ax, x, y, size, palace_name, palace_data):
    """添加专业细节"""
    # 添加宫位强弱标记（右下角）
    major_stars = palace_data.get("major_stars", [])
    minor_stars = palace_data.get("minor_stars", [])

    total_stars = len(major_stars) + len(minor_stars)

    if total_stars >= 5:
        # 星多为旺
        ax.text(x + size - 0.2, y + 0.2, "旺",
               ha='right', va='bottom', fontsize=8,
               fontweight='bold', color='red',
               bbox=dict(boxstyle="round,pad=0.1", facecolor='yellow', alpha=0.7))
    elif total_stars <= 1:
        # 星少为空
        ax.text(x + size - 0.2, y + 0.2, "空",
               ha='right', va='bottom', fontsize=8,
               fontweight='bold', color='gray',
               bbox=dict(boxstyle="round,pad=0.1", facecolor='lightgray', alpha=0.7))

    # 特殊宫位标记
    if palace_name == "命宫":
        # 命宫添加特殊标记
        ax.text(x + 0.2, y + size - 0.7, "★",
               ha='left', va='top', fontsize=12,
               color='gold')
    elif palace_name == "财帛宫":
        # 财帛宫添加金钱符号
        ax.text(x + 0.2, y + size - 0.7, "￥",
               ha='left', va='top', fontsize=10,
               color='green')

def draw_center_bazi(ax, result):
    """在中央绘制八字信息"""
    bazi_data = result.get("bazi_analysis", {})

    # 中央区域
    center_x = 5.5
    center_y = 6.5
    center_width = 5
    center_height = 3

    # 中央背景
    center_rect = Rectangle((center_x, center_y), center_width, center_height,
                           linewidth=2, edgecolor='black',
                           facecolor='lightyellow', alpha=0.8)
    ax.add_patch(center_rect)

    # 八字标题
    ax.text(center_x + center_width/2, center_y + center_height - 0.5,
           "八字命理", ha='center', va='center',
           fontsize=14, fontweight='bold', color='black')

    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")

        # 分割四柱
        pillars = chinese_date.split()
        pillar_names = ["年", "月", "日", "时"]

        for i, (name, pillar) in enumerate(zip(pillar_names, pillars)):
            col_x = center_x + 0.5 + i * 1.2

            # 柱名
            ax.text(col_x, center_y + 2, name, ha='center', va='center',
                   fontsize=10, color='black')

            # 天干
            if len(pillar) >= 1:
                ax.text(col_x, center_y + 1.5, pillar[0], ha='center', va='center',
                       fontsize=12, fontweight='bold', color='red')

            # 地支
            if len(pillar) >= 2:
                ax.text(col_x, center_y + 1, pillar[1], ha='center', va='center',
                       fontsize=12, fontweight='bold', color='blue')

    # 五行分析
    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})

        elements = ["木", "火", "土", "金", "水"]
        for i, element in enumerate(elements):
            count = wuxing_count.get(element, 0)
            ax.text(center_x + 0.3 + i * 0.9, center_y + 0.3,
                   f"{element}:{count:.1f}", ha='center', va='center',
                   fontsize=9, color='purple')

def main():
    """主函数"""
    print("🎨 专业紫薇斗数图表生成器")
    print("=" * 60)

    success = generate_professional_ziwei()

    if success:
        print("\n✅ 专业图表生成成功！")
        print("📁 文件保存为: professional_ziwei.png")
        print("🖼️ 专业特点:")
        print("  - 标准的4x4宫格布局")
        print("  - 统一的宫格大小和边框")
        print("  - 清晰的颜色编码:")
        print("    * 主星：红色")
        print("    * 副星：绿色")
        print("    * 四化：橙色")
        print("    * 地支：蓝色")
        print("  - 宫位序号标记")
        print("  - 中央八字和五行信息")
        print("  - 简洁专业的视觉效果")
    else:
        print("\n❌ 专业图表生成失败")

if __name__ == "__main__":
    main()
