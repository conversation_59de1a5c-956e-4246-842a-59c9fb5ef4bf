#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器 - 简化版
"""

import json
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Any, Optional, Dict
from .simple_logger import get_logger

logger = get_logger()

class CacheManager:
    """简化的缓存管理器"""
    
    def __init__(self, cache_dir: str = "data/cache", ttl_hours: int = 24):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            ttl_hours: 缓存有效期（小时）
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.ttl = timedelta(hours=ttl_hours)
        
        # 缓存索引文件
        self.index_file = self.cache_dir / "index.json"
        self.index = self._load_index()
        
        logger.info(f"缓存管理器初始化完成: {self.cache_dir}")
    
    def _load_index(self) -> Dict[str, Dict]:
        """加载缓存索引"""
        if not self.index_file.exists():
            return {}
        
        try:
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"加载缓存索引失败: {e}")
            return {}
    
    def _save_index(self):
        """保存缓存索引"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {e}")
    
    def _is_expired(self, cache_time: str) -> bool:
        """检查缓存是否过期"""
        try:
            cache_dt = datetime.fromisoformat(cache_time)
            return datetime.now() - cache_dt > self.ttl
        except Exception:
            return True
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if key not in self.index:
            return None
        
        cache_info = self.index[key]
        
        # 检查是否过期
        if self._is_expired(cache_info["created_at"]):
            self.delete(key)
            return None
        
        # 读取缓存文件
        cache_file = self.cache_dir / cache_info["filename"]
        if not cache_file.exists():
            # 文件不存在，清理索引
            del self.index[key]
            self._save_index()
            return None
        
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.debug(f"缓存命中: {key}")
            return data
        except Exception as e:
            logger.error(f"读取缓存失败: {e}")
            self.delete(key)
            return None
    
    def set(self, key: str, data: Any, category: str = "default") -> bool:
        """设置缓存数据"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{category}_{key}_{timestamp}.json"
            cache_file = self.cache_dir / filename
            
            # 保存数据
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 更新索引
            self.index[key] = {
                "filename": filename,
                "category": category,
                "created_at": datetime.now().isoformat(),
                "size": cache_file.stat().st_size
            }
            
            self._save_index()
            logger.debug(f"缓存保存成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if key not in self.index:
            return False
        
        try:
            cache_info = self.index[key]
            cache_file = self.cache_dir / cache_info["filename"]
            
            # 删除文件
            if cache_file.exists():
                cache_file.unlink()
            
            # 删除索引
            del self.index[key]
            self._save_index()
            
            logger.debug(f"缓存删除成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
            return False
    
    def clear_expired(self) -> int:
        """清理过期缓存"""
        expired_keys = []
        
        for key, cache_info in self.index.items():
            if self._is_expired(cache_info["created_at"]):
                expired_keys.append(key)
        
        for key in expired_keys:
            self.delete(key)
        
        logger.info(f"清理过期缓存: {len(expired_keys)}个")
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_size = 0
        categories = {}
        
        for cache_info in self.index.values():
            total_size += cache_info.get("size", 0)
            category = cache_info.get("category", "default")
            categories[category] = categories.get(category, 0) + 1
        
        return {
            "total_items": len(self.index),
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "categories": categories,
            "cache_dir": str(self.cache_dir)
        }

# 全局缓存实例
_cache_manager = CacheManager()

def get_cache() -> CacheManager:
    """获取缓存管理器实例"""
    return _cache_manager
