#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个API Key的异步调用能力
"""

import asyncio
import aiohttp
import time
import json
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# 添加项目路径
sys.path.append('.')

def load_api_config():
    """加载API配置"""
    try:
        from config.settings import get_config
        config = get_config()
        return {
            "api_key": config.llm.api_key,
            "base_url": config.llm.base_url,
            "model": config.llm.model_name
        }
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        # 尝试从环境变量或硬编码获取
        import os
        api_key = os.getenv("SILICONFLOW_API_KEY", "sk-trklwkxjmgnrgbuxhwcanaxkzwtuqevslzhoikwgwajnkqjz")
        if api_key and api_key != "your_api_key_here":
            return {
                "api_key": api_key,
                "base_url": "https://api.siliconflow.cn/v1",
                "model": "deepseek-ai/DeepSeek-V3"
            }
        return None

async def make_async_api_call(session: aiohttp.ClientSession,
                             config: Dict[str, str],
                             prompt: str,
                             call_id: int) -> Dict[str, Any]:
    """异步API调用"""
    start_time = time.time()

    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }

    data = {
        "model": config['model'],
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 500,
        "temperature": 0.7
    }

    try:
        async with session.post(
            f"{config['base_url']}/chat/completions",
            headers=headers,
            json=data,
            timeout=aiohttp.ClientTimeout(total=60)
        ) as response:

            end_time = time.time()
            response_time = end_time - start_time

            if response.status == 200:
                result = await response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

                return {
                    "call_id": call_id,
                    "success": True,
                    "response_time": response_time,
                    "content": content[:100] + "..." if len(content) > 100 else content,
                    "full_content": content,
                    "status_code": response.status
                }
            else:
                error_text = await response.text()
                return {
                    "call_id": call_id,
                    "success": False,
                    "response_time": response_time,
                    "error": f"HTTP {response.status}: {error_text}",
                    "status_code": response.status
                }

    except asyncio.TimeoutError:
        return {
            "call_id": call_id,
            "success": False,
            "response_time": time.time() - start_time,
            "error": "请求超时",
            "status_code": 0
        }
    except Exception as e:
        return {
            "call_id": call_id,
            "success": False,
            "response_time": time.time() - start_time,
            "error": str(e),
            "status_code": 0
        }

async def test_concurrent_calls(config: Dict[str, str], num_calls: int = 5) -> List[Dict[str, Any]]:
    """测试并发调用"""
    print(f"🚀 开始测试 {num_calls} 个并发API调用...")

    # 创建不同的测试提示词
    test_prompts = [
        "你好，请简单介绍一下自己。",
        "什么是人工智能？请简要说明。",
        "请解释一下机器学习的基本概念。",
        "描述一下深度学习的应用场景。",
        "请谈谈自然语言处理的发展趋势。",
        "什么是算命？请简单介绍。",
        "紫薇斗数是什么？",
        "八字算命的基本原理是什么？",
        "六爻占卜如何进行？",
        "请介绍一下中国传统文化。"
    ]

    # 选择提示词
    prompts = test_prompts[:num_calls]

    async with aiohttp.ClientSession() as session:
        # 创建并发任务
        tasks = []
        for i, prompt in enumerate(prompts):
            task = make_async_api_call(session, config, prompt, i + 1)
            tasks.append(task)

        # 并发执行
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time

        print(f"✅ 总耗时: {total_time:.2f}秒")

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "call_id": i + 1,
                    "success": False,
                    "response_time": 0,
                    "error": str(result),
                    "status_code": 0
                })
            else:
                processed_results.append(result)

        return processed_results

async def test_sequential_calls(config: Dict[str, str], num_calls: int = 5) -> List[Dict[str, Any]]:
    """测试顺序调用（对比用）"""
    print(f"🐌 开始测试 {num_calls} 个顺序API调用...")

    test_prompts = [
        "你好，请简单介绍一下自己。",
        "什么是人工智能？请简要说明。",
        "请解释一下机器学习的基本概念。",
        "描述一下深度学习的应用场景。",
        "请谈谈自然语言处理的发展趋势。"
    ]

    results = []
    start_time = time.time()

    async with aiohttp.ClientSession() as session:
        for i, prompt in enumerate(test_prompts[:num_calls]):
            result = await make_async_api_call(session, config, prompt, i + 1)
            results.append(result)

    total_time = time.time() - start_time
    print(f"✅ 总耗时: {total_time:.2f}秒")

    return results

def analyze_results(concurrent_results: List[Dict], sequential_results: List[Dict]):
    """分析测试结果"""
    print("\n" + "="*80)
    print("📊 异步API调用测试结果分析")
    print("="*80)

    # 并发调用分析
    print("\n🚀 并发调用结果:")
    concurrent_success = sum(1 for r in concurrent_results if r['success'])
    concurrent_total_time = sum(r['response_time'] for r in concurrent_results if r['success'])
    concurrent_avg_time = concurrent_total_time / max(concurrent_success, 1)

    print(f"  成功调用: {concurrent_success}/{len(concurrent_results)}")
    print(f"  平均响应时间: {concurrent_avg_time:.2f}秒")
    print(f"  成功率: {concurrent_success/len(concurrent_results)*100:.1f}%")

    # 顺序调用分析
    print("\n🐌 顺序调用结果:")
    sequential_success = sum(1 for r in sequential_results if r['success'])
    sequential_total_time = sum(r['response_time'] for r in sequential_results if r['success'])
    sequential_avg_time = sequential_total_time / max(sequential_success, 1)

    print(f"  成功调用: {sequential_success}/{len(sequential_results)}")
    print(f"  平均响应时间: {sequential_avg_time:.2f}秒")
    print(f"  成功率: {sequential_success/len(sequential_results)*100:.1f}%")

    # 性能对比
    print("\n⚡ 性能对比:")
    if sequential_success > 0 and concurrent_success > 0:
        time_improvement = (sequential_total_time - concurrent_avg_time) / sequential_total_time * 100
        print(f"  时间节省: {time_improvement:.1f}%")
        print(f"  并发优势: {sequential_total_time/concurrent_avg_time:.1f}x 倍速")

    # 详细结果
    print("\n📋 详细调用结果:")
    print("\n并发调用:")
    for result in concurrent_results:
        status = "✅" if result['success'] else "❌"
        print(f"  {status} 调用{result['call_id']}: {result['response_time']:.2f}s")
        if not result['success']:
            print(f"     错误: {result['error']}")
        else:
            print(f"     内容: {result['content']}")

    print("\n顺序调用:")
    for result in sequential_results:
        status = "✅" if result['success'] else "❌"
        print(f"  {status} 调用{result['call_id']}: {result['response_time']:.2f}s")
        if not result['success']:
            print(f"     错误: {result['error']}")

    # 结论
    print("\n" + "="*80)
    print("🎯 测试结论:")

    if concurrent_success >= len(concurrent_results) * 0.8:
        print("✅ 单个API Key支持良好的异步并发调用")
        print("✅ 可以安全实施双Agent架构")
        if concurrent_success == len(concurrent_results):
            print("🌟 所有并发调用都成功，API稳定性优秀")
        return True
    else:
        print("❌ API Key并发调用存在问题")
        print("⚠️  需要考虑限流或使用多个API Key")
        return False

async def test_dual_agent_simulation():
    """模拟双Agent场景测试"""
    print("\n" + "="*80)
    print("🤖 双Agent场景模拟测试")
    print("="*80)

    config = load_api_config()
    if not config:
        return False

    # 模拟沟通Agent和计算Agent的并发调用
    customer_prompt = """
    你是一个专业的算命师客服，用户说："我想看紫薇斗数，1988年6月1日午时出生，男性"
    请回复用户，告诉他你正在为他计算，并简单说明紫薇斗数的特点。要求：
    1. 语言自然亲切
    2. 专业但不晦涩
    3. 给用户信心
    """

    calculator_prompt = """
    你是一个专业的算命计算专家，请分析以下信息：
    出生信息：1988年6月1日午时，男性
    算命类型：紫薇斗数
    请提供：
    1. 基本命盘信息
    2. 性格特点分析
    3. 事业运势概述
    要求：专业准确，结构化输出。
    """

    async with aiohttp.ClientSession() as session:
        print("🗣️  启动沟通Agent...")
        print("🧮 启动计算Agent...")

        # 并发执行两个Agent的任务
        start_time = time.time()

        customer_task = make_async_api_call(session, config, customer_prompt, 1)
        calculator_task = make_async_api_call(session, config, calculator_prompt, 2)

        results = await asyncio.gather(customer_task, calculator_task)

        total_time = time.time() - start_time

        print(f"\n⏱️  双Agent并发执行时间: {total_time:.2f}秒")

        # 分析结果
        customer_result, calculator_result = results

        print("\n🗣️  沟通Agent结果:")
        if customer_result['success']:
            print(f"  ✅ 响应时间: {customer_result['response_time']:.2f}秒")
            print(f"  📝 回复内容: {customer_result['content']}")
        else:
            print(f"  ❌ 失败: {customer_result['error']}")

        print("\n🧮 计算Agent结果:")
        if calculator_result['success']:
            print(f"  ✅ 响应时间: {calculator_result['response_time']:.2f}秒")
            print(f"  📊 分析内容: {calculator_result['content']}")
        else:
            print(f"  ❌ 失败: {calculator_result['error']}")

        # 评估双Agent可行性
        both_success = customer_result['success'] and calculator_result['success']
        max_response_time = max(
            customer_result['response_time'] if customer_result['success'] else 0,
            calculator_result['response_time'] if calculator_result['success'] else 0
        )

        print(f"\n🎯 双Agent评估:")
        print(f"  并发成功: {'✅' if both_success else '❌'}")
        print(f"  最大响应时间: {max_response_time:.2f}秒")
        print(f"  用户感知时间: {customer_result['response_time']:.2f}秒")

        if both_success and customer_result['response_time'] < 5:
            print("🌟 双Agent架构完全可行！")
            return True
        else:
            print("⚠️  双Agent架构需要优化")
            return False

async def main():
    """主测试函数"""
    print("🧪 单个API Key异步调用能力测试")
    print("="*80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 加载配置
    config = load_api_config()
    if not config:
        print("❌ 无法加载API配置，测试终止")
        return False

    print(f"✅ API配置加载成功")
    print(f"   模型: {config['model']}")
    print(f"   基础URL: {config['base_url']}")
    print(f"   API Key: {config['api_key'][:10]}...")

    try:
        # 测试1: 并发调用
        concurrent_results = await test_concurrent_calls(config, 5)

        # 测试2: 顺序调用（对比）
        sequential_results = await test_sequential_calls(config, 5)

        # 分析结果
        api_feasible = analyze_results(concurrent_results, sequential_results)

        # 测试3: 双Agent场景模拟
        dual_agent_feasible = await test_dual_agent_simulation()

        # 最终结论
        print("\n" + "="*80)
        print("🏁 最终测试结论")
        print("="*80)

        if api_feasible and dual_agent_feasible:
            print("🎉 测试通过！单个API Key完全支持双Agent架构")
            print("✅ 可以立即开始双Agent开发")
            print("\n🚀 建议的下一步:")
            print("  1. 开始创建Agent基础框架")
            print("  2. 实现沟通Agent和计算Agent")
            print("  3. 集成现有算命算法")
            print("  4. 优化用户交互体验")
            return True
        else:
            print("⚠️  存在技术限制，需要调整方案")
            if not api_feasible:
                print("  - API并发调用成功率不足")
            if not dual_agent_feasible:
                print("  - 双Agent响应时间过长")
            print("\n🔧 建议的解决方案:")
            print("  1. 考虑使用多个API Key")
            print("  2. 实现请求队列和限流")
            print("  3. 优化提示词长度")
            print("  4. 添加缓存机制")
            return False

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 运行异步测试
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
