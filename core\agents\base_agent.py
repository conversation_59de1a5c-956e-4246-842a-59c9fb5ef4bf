#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent基础框架 - 双Agent协作架构的基础类
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """消息类型枚举"""
    USER_INPUT = "user_input"
    CALCULATION_REQUEST = "calculation_request"
    CALCULATION_RESULT = "calculation_result"
    COMMUNICATION_REQUEST = "communication_request"
    COMMUNICATION_RESPONSE = "communication_response"
    ERROR = "error"
    STATUS_UPDATE = "status_update"
    SYSTEM_COMMAND = "system_command"

class AgentStatus(Enum):
    """Agent状态枚举"""
    IDLE = "idle"
    PROCESSING = "processing"
    WAITING = "waiting"
    ERROR = "error"
    BUSY = "busy"

@dataclass
class AgentMessage:
    """Agent间通信消息"""
    message_id: str
    message_type: MessageType
    sender_id: str
    receiver_id: str
    content: Dict[str, Any]
    timestamp: str
    priority: int = 1  # 1=高优先级, 2=中优先级, 3=低优先级
    timeout: Optional[float] = None
    correlation_id: Optional[str] = None  # 用于关联请求和响应
    
    def __post_init__(self):
        if not self.message_id:
            self.message_id = str(uuid.uuid4())
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

@dataclass
class AgentResponse:
    """Agent响应数据结构"""
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None
    processing_time: Optional[float] = None
    agent_id: Optional[str] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, agent_id: str, name: str, description: str = ""):
        """初始化Agent"""
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.status = AgentStatus.IDLE
        
        # 消息队列
        self.message_queue = asyncio.PriorityQueue()
        self.response_handlers: Dict[str, Callable] = {}
        
        # 状态管理
        self.session_states: Dict[str, Dict[str, Any]] = {}
        self.processing_tasks: Dict[str, asyncio.Task] = {}
        
        # 性能统计
        self.stats = {
            "messages_processed": 0,
            "messages_sent": 0,
            "errors": 0,
            "average_processing_time": 0.0,
            "total_processing_time": 0.0,
            "start_time": datetime.now().isoformat()
        }
        
        # 配置
        self.config = {
            "max_concurrent_tasks": 10,
            "message_timeout": 30.0,
            "max_queue_size": 100,
            "enable_logging": True
        }
        
        logger.info(f"Agent {self.agent_id} ({self.name}) 初始化完成")
    
    @abstractmethod
    async def process_message(self, message: AgentMessage) -> AgentResponse:
        """处理消息的抽象方法"""
        pass
    
    async def send_message(self, target_agent: 'BaseAgent', message_type: MessageType, 
                          content: Dict[str, Any], priority: int = 1,
                          timeout: Optional[float] = None,
                          correlation_id: Optional[str] = None) -> str:
        """发送消息给其他Agent"""
        message = AgentMessage(
            message_id=str(uuid.uuid4()),
            message_type=message_type,
            sender_id=self.agent_id,
            receiver_id=target_agent.agent_id,
            content=content,
            timestamp=datetime.now().isoformat(),
            priority=priority,
            timeout=timeout,
            correlation_id=correlation_id
        )
        
        await target_agent.receive_message(message)
        self.stats["messages_sent"] += 1
        
        if self.config["enable_logging"]:
            logger.debug(f"Agent {self.agent_id} 发送消息 {message.message_id} 给 {target_agent.agent_id}")
        
        return message.message_id
    
    async def receive_message(self, message: AgentMessage):
        """接收消息"""
        # 检查队列大小
        if self.message_queue.qsize() >= self.config["max_queue_size"]:
            logger.warning(f"Agent {self.agent_id} 消息队列已满，丢弃消息 {message.message_id}")
            return
        
        # 将消息放入优先级队列
        await self.message_queue.put((message.priority, time.time(), message))
        
        if self.config["enable_logging"]:
            logger.debug(f"Agent {self.agent_id} 接收消息 {message.message_id} 来自 {message.sender_id}")
    
    async def start_processing(self):
        """开始处理消息循环"""
        logger.info(f"Agent {self.agent_id} 开始处理消息")
        
        while True:
            try:
                # 获取消息（阻塞等待）
                priority, timestamp, message = await self.message_queue.get()
                
                # 检查消息超时
                if message.timeout and (time.time() - timestamp) > message.timeout:
                    logger.warning(f"消息 {message.message_id} 已超时，跳过处理")
                    continue
                
                # 处理消息
                await self._handle_message(message)
                
            except asyncio.CancelledError:
                logger.info(f"Agent {self.agent_id} 处理循环被取消")
                break
            except Exception as e:
                logger.error(f"Agent {self.agent_id} 处理消息时发生错误: {e}")
                self.stats["errors"] += 1
    
    async def _handle_message(self, message: AgentMessage):
        """内部消息处理方法"""
        start_time = time.time()
        
        try:
            self.status = AgentStatus.PROCESSING
            
            # 调用具体的消息处理方法
            response = await self.process_message(message)
            
            # 更新统计信息
            processing_time = time.time() - start_time
            self.stats["messages_processed"] += 1
            self.stats["total_processing_time"] += processing_time
            self.stats["average_processing_time"] = (
                self.stats["total_processing_time"] / self.stats["messages_processed"]
            )
            
            # 设置响应的处理时间
            response.processing_time = processing_time
            response.agent_id = self.agent_id
            
            if self.config["enable_logging"]:
                logger.debug(f"Agent {self.agent_id} 处理消息 {message.message_id} 完成，耗时 {processing_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"Agent {self.agent_id} 处理消息 {message.message_id} 失败: {e}")
            self.stats["errors"] += 1
            response = AgentResponse(
                success=False,
                data={},
                error=str(e),
                processing_time=time.time() - start_time,
                agent_id=self.agent_id
            )
        
        finally:
            self.status = AgentStatus.IDLE
    
    def get_session_state(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        return self.session_states.get(session_id, {})
    
    def set_session_state(self, session_id: str, state: Dict[str, Any]):
        """设置会话状态"""
        self.session_states[session_id] = state
    
    def update_session_state(self, session_id: str, updates: Dict[str, Any]):
        """更新会话状态"""
        if session_id not in self.session_states:
            self.session_states[session_id] = {}
        self.session_states[session_id].update(updates)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取Agent统计信息"""
        return {
            **self.stats,
            "status": self.status.value,
            "queue_size": self.message_queue.qsize(),
            "active_tasks": len(self.processing_tasks),
            "session_count": len(self.session_states)
        }
    
    def configure(self, config: Dict[str, Any]):
        """配置Agent参数"""
        self.config.update(config)
        logger.info(f"Agent {self.agent_id} 配置已更新")
    
    async def shutdown(self):
        """关闭Agent"""
        logger.info(f"Agent {self.agent_id} 开始关闭")
        
        # 取消所有处理任务
        for task in self.processing_tasks.values():
            task.cancel()
        
        # 等待任务完成
        if self.processing_tasks:
            await asyncio.gather(*self.processing_tasks.values(), return_exceptions=True)
        
        # 清理资源
        self.session_states.clear()
        self.processing_tasks.clear()
        
        logger.info(f"Agent {self.agent_id} 已关闭")
    
    def __str__(self):
        return f"Agent({self.agent_id}, {self.name}, {self.status.value})"
    
    def __repr__(self):
        return self.__str__()

class AgentRegistry:
    """Agent注册表"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_types: Dict[str, type] = {}
    
    def register_agent(self, agent: BaseAgent):
        """注册Agent"""
        self.agents[agent.agent_id] = agent
        logger.info(f"Agent {agent.agent_id} 已注册")
    
    def unregister_agent(self, agent_id: str):
        """注销Agent"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            logger.info(f"Agent {agent_id} 已注销")
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """获取Agent"""
        return self.agents.get(agent_id)
    
    def get_all_agents(self) -> List[BaseAgent]:
        """获取所有Agent"""
        return list(self.agents.values())
    
    def get_agents_by_type(self, agent_type: str) -> List[BaseAgent]:
        """根据类型获取Agent"""
        return [agent for agent in self.agents.values() 
                if agent.__class__.__name__ == agent_type]
    
    def get_agent_stats(self) -> Dict[str, Any]:
        """获取所有Agent的统计信息"""
        return {
            agent_id: agent.get_stats() 
            for agent_id, agent in self.agents.items()
        }

# 全局Agent注册表
agent_registry = AgentRegistry()
