#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比原版八字算法vs增强版八字算法的功能
检查缺少的功能
"""

def test_original_bazi_features():
    """测试原版八字算法的功能"""
    print("📜 测试原版八字算法功能")
    print("=" * 60)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        
        calc = RealBaziCalculator()
        
        # 测试基础排盘
        result1 = calc.calculate_bazi(1988, 6, 1, 11, 0, "男")
        
        # 测试传统分析
        result2 = calc.calculate_with_traditional_analysis(1988, 6, 1, 11, 0, "男")
        
        # 测试量化分析
        result3 = calc.calculate_with_quantitative_analysis(1988, 6, 1, 11, 0, "男")
        
        print("✅ 原版八字算法功能:")
        
        original_features = []
        
        if result1.get("success"):
            raw_result = result1.get("raw_result", {})
            print(f"📊 基础排盘功能:")
            
            if "干支" in raw_result:
                original_features.append("四柱干支")
                ganzhi = raw_result["干支"]
                print(f"  ✅ 四柱干支: {ganzhi.get('文本', '')}")
                
                # 检查详细干支信息
                if "年柱" in ganzhi:
                    original_features.append("年柱详情")
                    print(f"  ✅ 年柱: {ganzhi.get('年柱', '')}")
                if "月柱" in ganzhi:
                    original_features.append("月柱详情")
                    print(f"  ✅ 月柱: {ganzhi.get('月柱', '')}")
                if "日柱" in ganzhi:
                    original_features.append("日柱详情")
                    print(f"  ✅ 日柱: {ganzhi.get('日柱', '')}")
                if "时柱" in ganzhi:
                    original_features.append("时柱详情")
                    print(f"  ✅ 时柱: {ganzhi.get('时柱', '')}")
            
            if "五行" in raw_result:
                original_features.append("五行分析")
                wuxing = raw_result["五行"]
                print(f"  ✅ 五行分析: {len(wuxing)}种五行")
                
                # 检查五行详情
                for element, info in wuxing.items():
                    if isinstance(info, dict):
                        if "旺衰" in info:
                            original_features.append(f"{element}旺衰")
                        if "五行数" in info:
                            original_features.append(f"{element}数量")
                        if "得分" in info:
                            original_features.append(f"{element}得分")
            
            if "大运" in raw_result:
                original_features.append("大运推算")
                dayun = raw_result["大运"]
                print(f"  ✅ 大运推算: {len(dayun)}步大运")
                
                # 检查大运详情
                for period, info in list(dayun.items())[:3]:
                    if isinstance(info, dict):
                        if "十神" in info:
                            original_features.append("大运十神")
                        if "年份" in info:
                            original_features.append("大运年份")
                        if "旺衰" in info:
                            original_features.append("大运旺衰")
            
            if "十神" in raw_result:
                original_features.append("十神分析")
                print(f"  ✅ 十神分析")
            
            if "纳音" in raw_result:
                original_features.append("纳音五行")
                print(f"  ✅ 纳音五行")
            
            if "神煞" in raw_result:
                original_features.append("神煞推算")
                print(f"  ✅ 神煞推算")
            
            if "流年" in raw_result:
                original_features.append("流年分析")
                print(f"  ✅ 流年分析")
        
        # 检查传统分析功能
        if result2.get("success"):
            print(f"\n📊 传统分析功能:")
            original_features.append("传统分析")
            print(f"  ✅ 传统分析: {result2.get('analysis_note', '')}")
        
        # 检查量化分析功能
        if result3.get("success"):
            print(f"\n📊 量化分析功能:")
            original_features.append("量化分析")
            print(f"  ✅ 量化分析: {result3.get('analysis_note', '')}")
        
        return original_features
        
    except Exception as e:
        print(f"❌ 原版算法测试失败: {e}")
        return []

def test_enhanced_bazi_features():
    """测试增强版八字算法的功能"""
    print(f"\n🔧 测试增强版八字算法功能")
    print("=" * 60)
    
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        
        calc = EnhancedBaziCalculator()
        
        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")
        
        enhanced_features = []
        
        if result.get("success"):
            print("✅ 增强版八字算法功能:")
            
            bazi_info = result.get("bazi_info", {})
            analysis = result.get("analysis", {})
            
            # 基础信息
            if "chinese_date" in bazi_info:
                enhanced_features.append("四柱干支")
                print(f"  ✅ 四柱干支: {bazi_info['chinese_date']}")
            
            if "year_pillar" in bazi_info:
                enhanced_features.append("年柱详情")
                print(f"  ✅ 年柱: {bazi_info['year_pillar']}")
            if "month_pillar" in bazi_info:
                enhanced_features.append("月柱详情")
                print(f"  ✅ 月柱: {bazi_info['month_pillar']}")
            if "day_pillar" in bazi_info:
                enhanced_features.append("日柱详情")
                print(f"  ✅ 日柱: {bazi_info['day_pillar']}")
            if "hour_pillar" in bazi_info:
                enhanced_features.append("时柱详情")
                print(f"  ✅ 时柱: {bazi_info['hour_pillar']}")
            
            # 五行分析
            if "wuxing" in analysis:
                enhanced_features.append("五行分析")
                wuxing = analysis["wuxing"]
                print(f"  ✅ 五行分析: 包含数量、详情、强弱")
                
                if "count" in wuxing:
                    enhanced_features.append("五行数量")
                if "details" in wuxing:
                    enhanced_features.append("五行详情")
                if "strength" in wuxing:
                    enhanced_features.append("五行强弱")
            
            # 十神分析
            if "shishen" in analysis:
                enhanced_features.append("十神分析")
                print(f"  ✅ 十神分析: {len(analysis['shishen'])}个十神关系")
            
            # 日主分析
            if "day_master" in analysis:
                enhanced_features.append("日主分析")
                day_master = analysis["day_master"]
                print(f"  ✅ 日主分析: {day_master.get('gan', '')} ({day_master.get('element', '')}) - {day_master.get('strength', '')}")
            
            # 格局分析
            if "pattern" in analysis:
                enhanced_features.append("格局分析")
                pattern = analysis["pattern"]
                print(f"  ✅ 格局分析: {pattern.get('main_pattern', '')}")
            
            # 性格分析
            if "personality" in analysis:
                enhanced_features.append("性格分析")
                print(f"  ✅ 性格分析: 基于日主五行")
        
        return enhanced_features
        
    except Exception as e:
        print(f"❌ 增强版算法测试失败: {e}")
        return []

def compare_features():
    """对比功能差异"""
    print(f"\n🔍 功能对比分析")
    print("=" * 60)
    
    # 获取功能列表
    original_features = test_original_bazi_features()
    enhanced_features = test_enhanced_bazi_features()
    
    print(f"\n📊 功能对比:")
    
    # 共同功能
    common_features = set(original_features) & set(enhanced_features)
    print(f"✅ 共同功能 ({len(common_features)}个):")
    for feature in sorted(common_features):
        print(f"  ✅ {feature}")
    
    # 原版独有功能
    original_only = set(original_features) - set(enhanced_features)
    print(f"\n❌ 原版独有功能 ({len(original_only)}个):")
    for feature in sorted(original_only):
        print(f"  ❌ {feature}")
    
    # 增强版独有功能
    enhanced_only = set(enhanced_features) - set(original_features)
    print(f"\n🆕 增强版独有功能 ({len(enhanced_only)}个):")
    for feature in sorted(enhanced_only):
        print(f"  🆕 {feature}")
    
    return original_only, enhanced_only

def identify_missing_features():
    """识别缺失的重要功能"""
    print(f"\n⚠️ 缺失功能分析")
    print("=" * 60)
    
    # 重要的传统八字功能
    important_features = [
        "大运推算",
        "流年分析", 
        "纳音五行",
        "神煞推算",
        "传统分析",
        "量化分析",
        "大运十神",
        "大运年份",
        "大运旺衰"
    ]
    
    # 获取原版和增强版功能
    original_features = test_original_bazi_features()
    enhanced_features = test_enhanced_bazi_features()
    
    missing_features = []
    
    print("🎯 重要功能检查:")
    for feature in important_features:
        if feature in original_features and feature not in enhanced_features:
            missing_features.append(feature)
            print(f"  ❌ 缺失: {feature}")
        elif feature in enhanced_features:
            print(f"  ✅ 已有: {feature}")
        else:
            print(f"  ⚠️ 原版也无: {feature}")
    
    return missing_features

def recommend_improvements():
    """推荐改进方案"""
    print(f"\n💡 改进建议")
    print("=" * 60)
    
    missing_features = identify_missing_features()
    
    if missing_features:
        print("🔧 需要添加的功能:")
        
        priority_features = {
            "大运推算": "高优先级 - 八字分析的核心功能",
            "流年分析": "中优先级 - 预测运势变化",
            "纳音五行": "低优先级 - 传统理论补充",
            "神煞推算": "中优先级 - 吉凶判断",
            "传统分析": "高优先级 - 综合分析能力",
            "量化分析": "中优先级 - 现代化分析方法"
        }
        
        for feature in missing_features:
            priority = priority_features.get(feature, "待评估")
            print(f"  📋 {feature}: {priority}")
        
        print(f"\n🎯 实施建议:")
        print("1. 优先实现大运推算功能")
        print("2. 添加传统分析方法")
        print("3. 完善神煞推算")
        print("4. 补充流年分析")
        print("5. 最后添加纳音五行")
    else:
        print("🎉 功能已经很完整！")
        print("可以考虑优化现有功能的准确性和用户体验")

def main():
    """主函数"""
    print("🧪 八字算法功能完整性对比")
    print("=" * 80)
    
    # 对比功能
    original_only, enhanced_only = compare_features()
    
    # 识别缺失功能
    missing_features = identify_missing_features()
    
    # 推荐改进
    recommend_improvements()
    
    print("\n" + "=" * 80)
    print("🎯 总结:")
    print(f"  原版独有功能: {len(original_only)}个")
    print(f"  增强版独有功能: {len(enhanced_only)}个")
    print(f"  需要补充的重要功能: {len(missing_features)}个")

if __name__ == "__main__":
    main()
