#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个角度分析的字数效果
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_single_angle():
    """测试单个角度分析"""
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        agent = FortuneCalculatorAgent()
        
        # 模拟数据
        raw_data = {
            'palaces': {
                '命宫': {'position': '亥', 'major_stars': ['天相'], 'minor_stars': []},
                '财帛宫': {'position': '未', 'major_stars': ['天府'], 'minor_stars': ['左辅', '右弼']},
                '夫妻宫': {'position': '酉', 'major_stars': ['紫微', '贪狼'], 'minor_stars': []}
            }
        }
        
        birth_info = {'year': '1988', 'month': '6', 'day': '1', 'hour': '午时', 'gender': '男'}
        
        print('🧪 测试单个角度分析效果')
        print('=' * 50)
        
        # 测试财富分析
        print('\n💰 测试财富分析:')
        start_time = time.time()
        
        wealth_result = await agent._analyze_single_angle(
            '财富分析', 'wealth_fortune', '财运状况、理财投资与财富积累',
            raw_data, birth_info, '紫薇斗数'
        )
        
        wealth_time = time.time() - start_time
        
        print(f'✅ 财富分析完成')
        print(f'📊 字数: {len(wealth_result)}')
        print(f'⏱️ 耗时: {wealth_time:.1f}秒')
        print(f'📝 内容预览: {wealth_result[:300]}...')
        
        # 测试婚姻分析
        print('\n💕 测试婚姻分析:')
        start_time = time.time()
        
        marriage_result = await agent._analyze_single_angle(
            '婚姻分析', 'marriage_love', '感情婚姻、桃花运势与配偶关系',
            raw_data, birth_info, '紫薇斗数'
        )
        
        marriage_time = time.time() - start_time
        
        print(f'✅ 婚姻分析完成')
        print(f'📊 字数: {len(marriage_result)}')
        print(f'⏱️ 耗时: {marriage_time:.1f}秒')
        print(f'📝 内容预览: {marriage_result[:300]}...')
        
        # 总结
        print(f'\n📋 测试总结:')
        print(f'  财富分析: {len(wealth_result)}字, {wealth_time:.1f}秒')
        print(f'  婚姻分析: {len(marriage_result)}字, {marriage_time:.1f}秒')
        print(f'  平均字数: {(len(wealth_result) + len(marriage_result)) / 2:.0f}字')
        print(f'  平均耗时: {(wealth_time + marriage_time) / 2:.1f}秒')
        
        # 检查是否达到PRD要求
        avg_words = (len(wealth_result) + len(marriage_result)) / 2
        if avg_words >= 4000:
            print(f'🎉 PRD要求达成! 平均{avg_words:.0f}字 >= 4000字')
        elif avg_words >= 3000:
            print(f'⚠️ 接近PRD要求: 平均{avg_words:.0f}字, 目标4000字')
        else:
            print(f'❌ 未达PRD要求: 平均{avg_words:.0f}字 < 4000字')
        
        # 保存结果用于分析
        with open('test_wealth_analysis.txt', 'w', encoding='utf-8') as f:
            f.write(f"财富分析结果 ({len(wealth_result)}字):\n")
            f.write("=" * 50 + "\n")
            f.write(wealth_result)
        
        with open('test_marriage_analysis.txt', 'w', encoding='utf-8') as f:
            f.write(f"婚姻分析结果 ({len(marriage_result)}字):\n")
            f.write("=" * 50 + "\n")
            f.write(marriage_result)
        
        print(f'\n📁 分析结果已保存到文件')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_single_angle())
