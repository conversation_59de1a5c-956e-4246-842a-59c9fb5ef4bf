#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成紫薇+八字融合图表
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Circle, Rectangle
import numpy as np
from matplotlib import font_manager
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_fusion_chart():
    """生成紫薇+八字融合图表"""
    print("🎨 生成紫薇+八字融合图表")
    print("=" * 50)
    
    try:
        # 获取融合数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        # 创建图表
        fig, ax = plt.subplots(1, 1, figsize=(16, 12))
        ax.set_xlim(-10, 10)
        ax.set_ylim(-8, 8)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 设置背景色
        fig.patch.set_facecolor('#f8f9fa')
        
        # 1. 绘制标题
        birth_info = result.get("birth_info", {})
        title = f"紫薇斗数+八字命理融合分析\n{birth_info.get('datetime', '')} {birth_info.get('gender', '')}"
        plt.suptitle(title, fontsize=20, fontweight='bold', y=0.95)
        
        # 2. 绘制紫薇斗数12宫（中央圆形）
        draw_ziwei_palaces(ax, result)
        
        # 3. 绘制八字信息（周围）
        draw_bazi_info(ax, result)
        
        # 4. 绘制融合分析信息
        draw_fusion_analysis(ax, result)
        
        # 5. 添加图例和说明
        draw_legend(ax)
        
        # 保存图片
        output_file = "fusion_chart.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                   facecolor='#f8f9fa', edgecolor='none')
        
        print(f"✅ 图表生成成功: {output_file}")
        
        # 显示图片
        plt.show()
        
        return True
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def draw_ziwei_palaces(ax, result):
    """绘制紫薇斗数12宫"""
    ziwei_data = result.get("ziwei_analysis", {})
    palaces = ziwei_data.get("palaces", {})
    
    # 12宫位置（圆形排列）
    palace_names = ["命宫", "兄弟宫", "夫妻宫", "子女宫", "财帛宫", "疾厄宫", 
                   "迁移宫", "奴仆宫", "官禄宫", "田宅宫", "福德宫", "父母宫"]
    
    # 计算12宫位置
    radius = 4
    center_x, center_y = 0, 0
    
    for i, palace_name in enumerate(palace_names):
        angle = i * 30 * np.pi / 180  # 每宫30度
        x = center_x + radius * np.cos(angle)
        y = center_y + radius * np.sin(angle)
        
        # 绘制宫位框
        rect = Rectangle((x-0.8, y-0.6), 1.6, 1.2, 
                        linewidth=2, edgecolor='#2c3e50', 
                        facecolor='#ecf0f1', alpha=0.8)
        ax.add_patch(rect)
        
        # 宫位名称
        ax.text(x, y+0.3, palace_name, ha='center', va='center', 
               fontsize=10, fontweight='bold', color='#2c3e50')
        
        # 主星信息
        if palace_name in palaces:
            palace_info = palaces[palace_name]
            major_stars = palace_info.get("major_stars", [])
            if major_stars:
                star_text = "、".join(major_stars[:2])  # 最多显示2个主星
                ax.text(x, y-0.1, star_text, ha='center', va='center', 
                       fontsize=8, color='#e74c3c')
            
            # 地支
            position = palace_info.get("position", "")
            if position:
                ax.text(x, y-0.4, position, ha='center', va='center', 
                       fontsize=8, color='#3498db')
    
    # 绘制中央圆
    center_circle = Circle((center_x, center_y), 1.5, 
                          linewidth=3, edgecolor='#8e44ad', 
                          facecolor='#f8f9fa', alpha=0.9)
    ax.add_patch(center_circle)
    
    # 中央标题
    ax.text(center_x, center_y+0.3, "紫薇斗数", ha='center', va='center', 
           fontsize=14, fontweight='bold', color='#8e44ad')
    
    # 生肖和星座
    birth_info = result.get("birth_info", {})
    zodiac = birth_info.get("zodiac", "")
    sign = birth_info.get("sign", "")
    if zodiac and sign:
        ax.text(center_x, center_y-0.3, f"{zodiac}年 {sign}座", 
               ha='center', va='center', fontsize=10, color='#7f8c8d')

def draw_bazi_info(ax, result):
    """绘制八字信息"""
    bazi_data = result.get("bazi_analysis", {})
    
    # 八字四柱（上方）
    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")
        
        # 分割四柱
        pillars = chinese_date.split()
        pillar_names = ["年柱", "月柱", "日柱", "时柱"]
        
        for i, (name, pillar) in enumerate(zip(pillar_names, pillars)):
            x = -6 + i * 4
            y = 6.5
            
            # 绘制柱子框
            rect = Rectangle((x-0.8, y-0.5), 1.6, 1, 
                           linewidth=2, edgecolor='#d35400', 
                           facecolor='#fdf2e9', alpha=0.8)
            ax.add_patch(rect)
            
            # 柱名
            ax.text(x, y+0.2, name, ha='center', va='center', 
                   fontsize=10, fontweight='bold', color='#d35400')
            
            # 干支
            if len(pillar) == 2:
                ax.text(x, y-0.1, pillar[0], ha='center', va='center', 
                       fontsize=12, fontweight='bold', color='#e74c3c')
                ax.text(x, y-0.3, pillar[1], ha='center', va='center', 
                       fontsize=12, fontweight='bold', color='#3498db')
    
    # 五行分析（右侧）
    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})
        wuxing_strength = wuxing.get("strength", {})
        
        elements = ["木", "火", "土", "金", "水"]
        colors = ["#27ae60", "#e74c3c", "#f39c12", "#95a5a6", "#3498db"]
        
        for i, (element, color) in enumerate(zip(elements, colors)):
            x = 7.5
            y = 4 - i * 1.2
            
            count = wuxing_count.get(element, 0)
            strength = wuxing_strength.get(element, "")
            
            # 五行框
            rect = Rectangle((x-0.6, y-0.4), 1.2, 0.8, 
                           linewidth=2, edgecolor=color, 
                           facecolor=color, alpha=0.3)
            ax.add_patch(rect)
            
            # 五行名称和数量
            ax.text(x, y+0.1, element, ha='center', va='center', 
                   fontsize=12, fontweight='bold', color=color)
            ax.text(x, y-0.2, f"{count}个", ha='center', va='center', 
                   fontsize=10, color=color)
            
            # 强弱标识
            if strength:
                ax.text(x+1.2, y, strength, ha='left', va='center', 
                       fontsize=9, color=color)
    
    # 日主信息（左侧）
    if "analysis" in bazi_data and "day_master" in bazi_data["analysis"]:
        day_master = bazi_data["analysis"]["day_master"]
        
        x = -7.5
        y = 2
        
        # 日主框
        rect = Rectangle((x-0.8, y-1), 1.6, 2, 
                        linewidth=2, edgecolor='#8e44ad', 
                        facecolor='#f4ecf7', alpha=0.8)
        ax.add_patch(rect)
        
        # 日主信息
        ax.text(x, y+0.5, "日主", ha='center', va='center', 
               fontsize=12, fontweight='bold', color='#8e44ad')
        ax.text(x, y, day_master.get("gan", ""), ha='center', va='center', 
               fontsize=16, fontweight='bold', color='#8e44ad')
        ax.text(x, y-0.5, day_master.get("strength", ""), ha='center', va='center', 
               fontsize=10, color='#8e44ad')

def draw_fusion_analysis(ax, result):
    """绘制融合分析信息"""
    fusion = result.get("fusion_analysis", {})
    
    # 交叉验证结果（下方）
    if "cross_validation" in fusion:
        validation = fusion["cross_validation"]
        confidence = validation.get("confidence_level", 0)
        
        x = 0
        y = -6.5
        
        # 验证框
        rect = Rectangle((x-3, y-0.8), 6, 1.6, 
                        linewidth=2, edgecolor='#16a085', 
                        facecolor='#e8f8f5', alpha=0.8)
        ax.add_patch(rect)
        
        # 验证信息
        ax.text(x, y+0.4, "交叉验证", ha='center', va='center', 
               fontsize=12, fontweight='bold', color='#16a085')
        ax.text(x, y, f"一致性得分: {confidence:.0%}", ha='center', va='center', 
               fontsize=11, color='#16a085')
        
        # 验证详情
        consistency = validation.get("consistency_check", {})
        details = []
        for item, check in consistency.items():
            if isinstance(check, dict):
                status = "✓" if check.get("consistent", False) else "✗"
                details.append(f"{item}{status}")
        
        if details:
            ax.text(x, y-0.4, " | ".join(details), ha='center', va='center', 
                   fontsize=9, color='#16a085')

def draw_legend(ax):
    """绘制图例和说明"""
    # 图例位置（左下角）
    legend_x = -9
    legend_y = -7
    
    # 图例框
    rect = Rectangle((legend_x, legend_y), 4, 2, 
                    linewidth=1, edgecolor='#7f8c8d', 
                    facecolor='#ffffff', alpha=0.9)
    ax.add_patch(rect)
    
    # 图例内容
    ax.text(legend_x+2, legend_y+1.5, "图例说明", ha='center', va='center', 
           fontsize=10, fontweight='bold', color='#2c3e50')
    
    legend_items = [
        "● 中央：紫薇12宫圆形布局",
        "● 上方：八字四柱信息", 
        "● 右侧：五行强弱分析",
        "● 下方：交叉验证结果"
    ]
    
    for i, item in enumerate(legend_items):
        ax.text(legend_x+0.1, legend_y+1.1-i*0.25, item, ha='left', va='center', 
               fontsize=8, color='#2c3e50')

def main():
    """主函数"""
    print("🎨 紫薇+八字融合图表生成器")
    print("=" * 60)
    
    success = generate_fusion_chart()
    
    if success:
        print("\n✅ 融合图表生成成功！")
        print("📁 文件保存为: fusion_chart.png")
        print("🖼️ 图表特点:")
        print("  - 1200x900像素高清图片")
        print("  - 中央：紫薇12宫圆形布局")
        print("  - 周围：八字四柱和五行信息")
        print("  - 底部：交叉验证结果")
        print("  - 完整的图例说明")
    else:
        print("\n❌ 融合图表生成失败")

if __name__ == "__main__":
    main()
