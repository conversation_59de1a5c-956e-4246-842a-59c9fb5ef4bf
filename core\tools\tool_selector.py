#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能工具选择器 - 根据意图自动选择合适的算命工具
"""

import logging
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ToolInfo:
    """工具信息"""
    name: str
    description: str
    required_entities: list
    handler: Callable
    confidence_threshold: float = 0.6

class ToolSelector:
    """智能工具选择器 - 根据意图和实体自动选择合适的工具"""

    def __init__(self):
        """初始化工具选择器"""
        self.tools = {}
        self._register_default_tools()
        logger.info("工具选择器初始化完成")

    def _register_default_tools(self):
        """注册默认工具"""
        # 注册紫薇斗数工具
        self.register_tool(
            intent="ziwei",
            tool_info=ToolInfo(
                name="ziwei_calculator",
                description="紫薇斗数命盘分析",
                required_entities=["birth_year", "birth_month", "birth_day", "birth_hour", "gender"],
                handler=self._humanized_ziwei_handler,
                confidence_threshold=0.7
            )
        )

        # 注册八字算命工具
        self.register_tool(
            intent="bazi",
            tool_info=ToolInfo(
                name="bazi_calculator",
                description="八字命理分析",
                required_entities=["birth_year", "birth_month", "birth_day", "birth_hour", "gender"],
                handler=self._humanized_bazi_handler,
                confidence_threshold=0.7
            )
        )

        # 注册六爻占卜工具
        self.register_tool(
            intent="liuyao",
            tool_info=ToolInfo(
                name="liuyao_calculator",
                description="六爻占卜预测",
                required_entities=[],  # 六爻不需要出生信息
                handler=self._humanized_liuyao_handler,
                confidence_threshold=0.6
            )
        )

        # 注册综合分析工具
        self.register_tool(
            intent="general",
            tool_info=ToolInfo(
                name="comprehensive_analysis",
                description="综合算命分析",
                required_entities=["birth_year", "birth_month", "birth_day", "gender"],
                handler=self._comprehensive_handler,
                confidence_threshold=0.5
            )
        )

        # 注册聊天工具
        self.register_tool(
            intent="chat",
            tool_info=ToolInfo(
                name="chat_handler",
                description="普通对话处理",
                required_entities=[],
                handler=self._chat_handler,
                confidence_threshold=0.3
            )
        )

    def register_tool(self, intent: str, tool_info: ToolInfo):
        """
        注册工具

        Args:
            intent: 意图类型
            tool_info: 工具信息
        """
        self.tools[intent] = tool_info
        logger.info(f"注册工具: {intent} -> {tool_info.name}")

    def select_tool(self, intent_result: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        选择合适的工具

        Args:
            intent_result: 意图识别结果
            context: 对话上下文

        Returns:
            工具选择结果
        """
        try:
            intent = intent_result.get("intent", "chat")
            confidence = intent_result.get("confidence", 0.0)
            entities = intent_result.get("entities", {})

            logger.info(f"开始工具选择 - 意图: {intent}, 置信度: {confidence}")

            # 1. 检查意图是否有对应工具
            if intent not in self.tools:
                logger.warning(f"未找到意图 {intent} 对应的工具，使用聊天工具")
                intent = "chat"

            tool_info = self.tools[intent]

            # 2. 检查置信度是否满足要求
            if confidence < tool_info.confidence_threshold:
                logger.warning(f"置信度 {confidence} 低于阈值 {tool_info.confidence_threshold}，降级处理")
                return self._handle_low_confidence(intent_result, context)

            # 3. 检查必需实体是否完整
            missing_entities = self._check_required_entities(entities, tool_info.required_entities, context)

            if missing_entities:
                logger.info(f"缺少必需实体: {missing_entities}")
                return self._handle_missing_entities(intent, missing_entities, entities, context)

            # 4. 执行工具
            logger.info(f"执行工具: {tool_info.name}")
            result = tool_info.handler(intent_result, context)

            return {
                "success": True,
                "tool_name": tool_info.name,
                "tool_description": tool_info.description,
                "result": result,
                "entities_used": entities
            }

        except Exception as e:
            logger.error(f"工具选择失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_response": "抱歉，系统暂时无法处理您的请求，请稍后重试。"
            }

    def _check_required_entities(self, entities: Dict[str, Any], required: list, context: Dict[str, Any] = None) -> list:
        """
        检查必需实体是否完整

        Args:
            entities: 当前实体
            required: 必需实体列表
            context: 对话上下文

        Returns:
            缺少的实体列表
        """
        missing = []

        # 合并当前实体和上下文中的实体
        all_entities = entities.copy()
        if context and context.get("birth_info"):
            birth_info = context["birth_info"]
            # 将birth_info转换为标准实体格式
            entity_mapping = {
                "year": "birth_year",
                "month": "birth_month",
                "day": "birth_day",
                "hour": "birth_hour",
                "gender": "gender"
            }
            for key, entity_key in entity_mapping.items():
                if key in birth_info and birth_info[key]:
                    all_entities[entity_key] = birth_info[key]

        # 检查每个必需实体
        for entity in required:
            if entity not in all_entities or not all_entities[entity]:
                missing.append(entity)

        return missing

    def _handle_low_confidence(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理低置信度情况"""
        return {
            "success": True,
            "tool_name": "clarification_handler",
            "result": {
                "type": "clarification",
                "message": "我不太确定您的具体需求，能否详细说明一下您想要什么样的算命服务？",
                "suggestions": [
                    "紫薇斗数 - 详细的命盘分析",
                    "八字算命 - 生辰八字解读",
                    "六爻占卜 - 占卜预测",
                    "综合分析 - 多角度命运分析"
                ]
            }
        }

    def _handle_missing_entities(self, intent: str, missing: list, entities: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理缺少实体的情况"""
        entity_names = {
            "birth_year": "出生年份",
            "birth_month": "出生月份",
            "birth_day": "出生日期",
            "birth_hour": "出生时辰",
            "gender": "性别"
        }

        missing_names = [entity_names.get(e, e) for e in missing]

        return {
            "success": True,
            "tool_name": "entity_collection_handler",
            "result": {
                "type": "entity_collection",
                "message": f"为了给您提供准确的{self.tools[intent].description}，我还需要了解您的{', '.join(missing_names)}。",
                "missing_entities": missing,
                "current_entities": entities,
                "target_intent": intent
            }
        }

    def _humanized_ziwei_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """人性化紫薇斗数工具处理器"""
        try:
            from core.tools.humanized_ziwei_tool import HumanizedZiweiTool

            # 创建人性化紫薇工具实例
            ziwei_tool = HumanizedZiweiTool()

            # 执行紫薇分析
            result = ziwei_tool.execute(intent_result, context)

            if result.get("success"):
                return result
            else:
                return {
                    "type": "error",
                    "message": result.get("message", "紫薇斗数分析失败")
                }

        except Exception as e:
            logger.error(f"人性化紫薇工具调用失败: {e}")
            return {
                "type": "error",
                "message": f"紫薇斗数分析出现问题: {str(e)}"
            }

    def _ziwei_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """紫薇斗数工具处理器（原版，保留作为备用）"""
        try:
            # 导入紫薇算法
            from algorithms.real_ziwei_calculator import RealZiweiCalculator

            entities = intent_result.get("entities", {})

            # 从上下文获取完整出生信息
            birth_info = self._extract_birth_info(entities, context)

            calculator = RealZiweiCalculator()

            # 使用正确的方法名 calculate_chart
            result = calculator.calculate_chart(
                year=int(birth_info["birth_year"]),
                month=int(birth_info["birth_month"]),
                day=int(birth_info["birth_day"]),
                hour=self._convert_hour_to_int(birth_info["birth_hour"]),
                gender=birth_info["gender"]
            )

            return {
                "type": "ziwei_analysis",
                "calculation_result": result,
                "message": "紫薇斗数命盘分析完成",
                "birth_info": birth_info
            }

        except Exception as e:
            logger.error(f"紫薇斗数计算失败: {e}")
            return {
                "type": "error",
                "message": f"紫薇斗数计算出现问题: {str(e)}"
            }

    def _humanized_bazi_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """人性化八字算命工具处理器"""
        try:
            from core.tools.humanized_bazi_tool import HumanizedBaziTool

            # 创建人性化八字工具实例
            bazi_tool = HumanizedBaziTool()

            # 执行八字分析
            result = bazi_tool.execute(intent_result, context)

            if result.get("success"):
                return result
            else:
                return {
                    "type": "error",
                    "message": result.get("message", "八字分析失败")
                }

        except Exception as e:
            logger.error(f"人性化八字工具调用失败: {e}")
            return {
                "type": "error",
                "message": f"八字分析出现问题: {str(e)}"
            }

    def _bazi_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """八字算命工具处理器（原版，保留作为备用）"""
        try:
            from algorithms.real_bazi_calculator import RealBaziCalculator

            entities = intent_result.get("entities", {})
            birth_info = self._extract_birth_info(entities, context)

            calculator = RealBaziCalculator()

            # 使用正确的方法名 calculate_bazi
            result = calculator.calculate_bazi(
                year=int(birth_info["birth_year"]),
                month=int(birth_info["birth_month"]),
                day=int(birth_info["birth_day"]),
                hour=self._convert_hour_to_int(birth_info["birth_hour"]),
                minute=0,
                gender=birth_info["gender"]
            )

            return {
                "type": "bazi_analysis",
                "calculation_result": result,
                "message": "八字命理分析完成",
                "birth_info": birth_info
            }

        except Exception as e:
            logger.error(f"八字计算失败: {e}")
            return {
                "type": "error",
                "message": f"八字计算出现问题: {str(e)}"
            }

    def _humanized_liuyao_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """人性化六爻占卜工具处理器"""
        try:
            from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool

            # 创建人性化六爻工具实例
            liuyao_tool = HumanizedLiuyaoTool()

            # 执行六爻分析
            result = liuyao_tool.execute(intent_result, context)

            if result.get("success"):
                return result
            else:
                return {
                    "type": "error",
                    "message": result.get("message", "六爻占卜失败")
                }

        except Exception as e:
            logger.error(f"人性化六爻工具调用失败: {e}")
            return {
                "type": "error",
                "message": f"六爻占卜出现问题: {str(e)}"
            }

    def _liuyao_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """六爻占卜工具处理器（原版，保留作为备用）"""
        try:
            from algorithms.liuyao_calculator import LiuyaoCalculator

            calculator = LiuyaoCalculator()
            result = calculator.calculate()

            return {
                "type": "liuyao_analysis",
                "calculation_result": result,
                "message": "六爻占卜完成"
            }

        except Exception as e:
            logger.error(f"六爻占卜失败: {e}")
            return {
                "type": "error",
                "message": f"六爻占卜出现问题: {str(e)}"
            }

    def _comprehensive_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """综合分析工具处理器"""
        return {
            "type": "comprehensive_analysis",
            "message": "综合分析功能开发中，建议选择具体的算命方法",
            "suggestions": ["紫薇斗数", "八字算命", "六爻占卜"]
        }

    def _chat_handler(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """聊天工具处理器"""
        return {
            "type": "chat_response",
            "message": "您好！我是智能算命AI助手，可以为您提供紫薇斗数、八字算命、六爻占卜等服务。请告诉我您的需求。"
        }

    def _extract_birth_info(self, entities: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, str]:
        """提取完整的出生信息"""
        birth_info = {}

        # 从实体中提取
        entity_mapping = {
            "birth_year": "birth_year",
            "birth_month": "birth_month",
            "birth_day": "birth_day",
            "birth_hour": "birth_hour",
            "gender": "gender"
        }

        for entity_key, birth_key in entity_mapping.items():
            if entity_key in entities and entities[entity_key]:
                birth_info[birth_key] = entities[entity_key]

        # 从上下文补充
        if context and context.get("birth_info"):
            context_birth = context["birth_info"]
            context_mapping = {
                "year": "birth_year",
                "month": "birth_month",
                "day": "birth_day",
                "hour": "birth_hour",
                "gender": "gender"
            }

            for context_key, birth_key in context_mapping.items():
                if context_key in context_birth and context_birth[context_key]:
                    if birth_key not in birth_info:
                        birth_info[birth_key] = context_birth[context_key]

        return birth_info

    def _convert_hour_to_int(self, hour_input) -> int:
        """转换时辰为整数小时"""
        if isinstance(hour_input, int):
            return hour_input

        if isinstance(hour_input, str):
            try:
                return int(hour_input)
            except ValueError:
                pass

            time_mapping = {
                "子时": 0, "丑时": 1, "寅时": 3, "卯时": 5,
                "辰时": 7, "巳时": 9, "午时": 11, "未时": 13,
                "申时": 15, "酉时": 17, "戌时": 19, "亥时": 21
            }

            return time_mapping.get(hour_input, 12)

        return 12

    def get_available_tools(self) -> Dict[str, str]:
        """获取可用工具列表"""
        return {intent: tool.description for intent, tool in self.tools.items()}
