#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能算命AI系统 v4.0 - 主应用入口
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 确保在正确的工作目录
os.chdir(project_root)

# 导入配置和服务
from config import config
from utils.simple_logger import get_logger
from services.chart_service import ChartService
from services.analysis_service import AnalysisService

# 导入页面模块
try:
    from web.pages.home import show_home_page
    from web.pages.chart import show_chart_page
    from web.pages.analysis import show_analysis_page
except ImportError:
    # 如果从web目录运行，使用相对导入
    from pages.home import show_home_page
    from pages.chart import show_chart_page
    from pages.analysis import show_analysis_page

logger = get_logger()

def init_app():
    """初始化应用"""
    # 设置页面配置
    st.set_page_config(
        page_title=config.web.title,
        page_icon=config.web.icon,
        layout=config.web.layout,
        initial_sidebar_state="expanded"
    )

    # 设置自定义CSS
    st.markdown("""
    <style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
        border-left: 4px solid #667eea;
    }

    .status-success {
        color: #28a745;
        font-weight: bold;
    }

    .status-error {
        color: #dc3545;
        font-weight: bold;
    }

    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }

    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    }
    </style>
    """, unsafe_allow_html=True)

    # 初始化会话状态
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'home'

    if 'chart_service' not in st.session_state:
        st.session_state.chart_service = ChartService()

    if 'analysis_service' not in st.session_state:
        st.session_state.analysis_service = AnalysisService()

    logger.info("应用初始化完成")

def show_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem;">
            <h2>{config.web.icon} 智能算命AI</h2>
            <p style="color: #666;">v4.0 重构版</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

        # 导航菜单
        st.markdown("### 📋 功能导航")

        if st.button("🏠 首页", use_container_width=True):
            st.session_state.current_page = 'home'
            st.rerun()

        if st.button("📊 生成排盘", use_container_width=True):
            st.session_state.current_page = 'chart'
            st.rerun()

        if st.button("🔍 深度分析", use_container_width=True):
            st.session_state.current_page = 'analysis'
            st.rerun()

        if st.button("🎲 六爻占卜", use_container_width=True):
            st.session_state.current_page = 'liuyao'
            st.rerun()

        if st.button("💑 合婚分析", use_container_width=True):
            st.session_state.current_page = 'compatibility'
            st.rerun()

        st.markdown("---")

        # 系统状态
        st.markdown("### ⚙️ 系统状态")

        # 检查API配置
        if config.llm.api_key:
            st.markdown('<p class="status-success">✅ API已配置</p>', unsafe_allow_html=True)
        else:
            st.markdown('<p class="status-error">❌ API未配置</p>', unsafe_allow_html=True)

        # 检查算法状态
        try:
            chart_service = st.session_state.chart_service
            if chart_service.ziwei_calc and chart_service.bazi_calc:
                st.markdown('<p class="status-success">✅ 算法已就绪</p>', unsafe_allow_html=True)
            else:
                st.markdown('<p class="status-warning">⚠️ 算法部分可用</p>', unsafe_allow_html=True)
        except:
            st.markdown('<p class="status-error">❌ 算法未就绪</p>', unsafe_allow_html=True)

        st.markdown("---")

        # 帮助信息
        with st.expander("❓ 使用帮助"):
            st.markdown("""
            **使用流程：**
            1. 在"生成排盘"页面输入生辰信息
            2. 生成紫薇+八字排盘
            3. 在"深度分析"页面选择分析角度
            4. 查看详细的命理分析结果

            **注意事项：**
            - 请确保生辰信息准确
            - 分析需要一定时间，请耐心等待
            - 结果仅供参考，请理性对待
            """)

def main():
    """主函数"""
    try:
        # 初始化应用
        init_app()

        # 显示侧边栏
        show_sidebar()

        # 根据当前页面显示内容
        current_page = st.session_state.current_page

        if current_page == 'home':
            show_home_page()
        elif current_page == 'chart':
            show_chart_page()
        elif current_page == 'analysis':
            show_analysis_page()
        elif current_page == 'liuyao':
            st.info("🚧 六爻占卜功能开发中...")
        elif current_page == 'compatibility':
            st.info("🚧 合婚分析功能开发中...")
        else:
            show_home_page()

    except Exception as e:
        logger.error(f"应用运行异常: {e}")
        st.error(f"应用运行异常: {e}")
        st.info("请检查配置和依赖是否正确安装")

if __name__ == "__main__":
    main()
