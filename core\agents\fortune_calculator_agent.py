#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算命计算专家Agent - 专注于算命计算和深度分析
"""

import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_agent import BaseAgent, AgentMessage, AgentResponse, MessageType
from core.tools.tool_selector import ToolSelector
from core.nlu.llm_client import LLMClient
from core.storage.calculation_cache import CalculationCache, CalculationResult

logger = logging.getLogger(__name__)

class FortuneCalculatorAgent(BaseAgent):
    """算命计算专家 - 负责算命计算、深度分析和结果生成"""

    def __init__(self, agent_id: str = "fortune_calculator_001"):
        super().__init__(
            agent_id=agent_id,
            name="算命计算专家",
            description="专注于算命计算、深度分析和专业结果生成的AI专家"
        )

        # 初始化工具选择器、LLM客户端和缓存管理器
        self.tool_selector = ToolSelector()
        self.llm_client = LLMClient()
        self.cache = CalculationCache()

        # 🔧 新增：任务管理
        import threading
        self.running_tasks = {}  # {result_id: task_info}
        self.task_lock = threading.Lock()  # 线程锁

        # 算命类型映射
        self.calculation_types = {
            "ziwei": "紫薇斗数",
            "bazi": "八字算命",
            "liuyao": "六爻占卜"
        }

        # 时辰映射
        self.hour_mapping = {
            "子时": 0, "丑时": 1, "寅时": 3, "卯时": 5,
            "辰时": 7, "巳时": 9, "午时": 11, "未时": 13,
            "申时": 15, "酉时": 17, "戌时": 19, "亥时": 21
        }

        logger.info(f"算命计算专家 {self.agent_id} 初始化完成")

    async def process_message(self, message: AgentMessage) -> AgentResponse:
        """处理消息"""
        try:
            content = message.content

            if message.message_type == MessageType.CALCULATION_REQUEST:
                return await self._handle_calculation_request(content)
            else:
                return await self._handle_general_request(content)

        except Exception as e:
            logger.error(f"计算Agent处理消息失败: {e}")
            return AgentResponse(
                success=False,
                data={},
                error=str(e)
            )

    async def _handle_calculation_request(self, content: Dict[str, Any]) -> AgentResponse:
        """处理算命计算请求 - 专注详细分析和缓存"""
        user_message = content.get("user_message", "")
        session_id = content.get("session_id", "")
        birth_info = content.get("birth_info", {})
        calculation_type = content.get("calculation_type", "ziwei")
        continue_analysis = content.get("continue_analysis", False)  # 🔧 新增：继续分析标志
        existing_result_id = content.get("existing_result_id")  # 🔧 新增：现有结果ID
        force_create = content.get("force_create", False)  # 🔧 新增：强制创建标志

        try:
            logger.info(f"🧮 后台Agent开始详细计算和分析")

            # 🔧 新增：处理继续分析请求
            if continue_analysis and existing_result_id:
                print(f"🔄 处理继续分析请求: {existing_result_id}")
                logger.info(f"🔄 处理继续分析请求: {existing_result_id}")

                # 🔧 检查是否已有相同任务在运行
                with self.task_lock:
                    if existing_result_id in self.running_tasks:
                        task_info = self.running_tasks[existing_result_id]
                        print(f"⚠️ 任务已在运行: {existing_result_id}, 启动时间: {task_info['start_time']}")
                        logger.warning(f"⚠️ 任务已在运行: {existing_result_id}")
                        return AgentResponse(
                            success=True,
                            data={
                                "result_id": existing_result_id,
                                "summary": f"任务已在运行中，启动时间: {task_info['start_time']}",
                                "from_cache": False,
                                "calculation_type": calculation_type,
                                "task_running": True
                            }
                        )

                # 获取现有结果
                cached_result = self.cache.get_result(existing_result_id)
                if cached_result:
                    # 检查是否需要继续分析
                    detailed_analysis = cached_result.detailed_analysis
                    if isinstance(detailed_analysis, dict):
                        angle_analyses = detailed_analysis.get("angle_analyses", {})
                        completed = len([v for v in angle_analyses.values() if v and len(v) > 100])

                        if completed >= 12:
                            print(f"✅ 分析已完成，无需继续: {existing_result_id}")
                            logger.info(f"✅ 分析已完成，无需继续: {existing_result_id}")
                            return AgentResponse(
                                success=True,
                                data={
                                    "result_id": existing_result_id,
                                    "summary": "分析已完成",
                                    "from_cache": True,
                                    "calculation_type": calculation_type
                                }
                            )

                        # 🔄 启动继续分析
                        print(f"🔄 启动继续分析: {existing_result_id}, 当前进度: {completed}/12")
                        logger.info(f"🔄 启动继续分析: {existing_result_id}, 当前进度: {completed}/12")

                        # 重新构建calculation_result用于继续分析
                        mock_calculation_result = {
                            "success": True,
                            "data": cached_result.raw_calculation,
                            "confidence": cached_result.confidence
                        }

                        # 🔄 启动后台继续分析（异步，不阻塞返回）
                        import threading

                        # 🔧 注册任务
                        with self.task_lock:
                            self.running_tasks[existing_result_id] = {
                                "task_type": "continue_analysis",
                                "start_time": datetime.now().strftime("%H:%M:%S"),
                                "birth_info": birth_info,
                                "calculation_type": calculation_type
                            }

                        def background_continue_analysis():
                            try:
                                print(f"🔥 [任务{existing_result_id[:8]}] 继续分析线程启动")
                                logger.info(f"🔥 继续分析线程启动: {existing_result_id}")

                                import asyncio
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)

                                print(f"🔮 继续12角度渐进式分析...")
                                logger.info(f"🔮 继续12角度渐进式分析")

                                # 进行全面深度分析
                                comprehensive_analysis = loop.run_until_complete(
                                    self._perform_comprehensive_analysis(mock_calculation_result, birth_info, calculation_type)
                                )

                                print(f"📊 继续分析完成，开始更新结果...")
                                logger.info(f"📊 继续分析完成，开始更新结果")

                                # 生成最终总结
                                summary = loop.run_until_complete(self._generate_summary(comprehensive_analysis))

                                # 提取关键词
                                keywords = loop.run_until_complete(self._extract_keywords(comprehensive_analysis))

                                # 更新缓存中的详细分析
                                self._update_cached_analysis(existing_result_id, comprehensive_analysis, summary, keywords)

                                print(f"✅ [任务{existing_result_id[:8]}] 继续分析完全完成")
                                logger.info(f"✅ 继续分析完全完成: {existing_result_id}")
                                loop.close()

                            except Exception as e:
                                print(f"❌ [任务{existing_result_id[:8]}] 继续分析失败: {e}")
                                logger.error(f"❌ 继续分析失败: {e}")
                                import traceback
                                print(traceback.format_exc())
                            finally:
                                # 🔧 清理任务记录
                                with self.task_lock:
                                    if existing_result_id in self.running_tasks:
                                        del self.running_tasks[existing_result_id]
                                        print(f"🧹 [任务{existing_result_id[:8]}] 任务记录已清理")

                        # 启动后台线程
                        continue_thread = threading.Thread(target=background_continue_analysis, daemon=True)
                        continue_thread.start()
                        print(f"🔄 继续分析已在后台启动")
                        logger.info(f"🔄 继续分析已在后台启动")

                        return AgentResponse(
                            success=True,
                            data={
                                "result_id": existing_result_id,
                                "summary": "继续分析已启动",
                                "from_cache": False,
                                "calculation_type": calculation_type,
                                "continue_analysis": True
                            }
                        )
                    else:
                        return AgentResponse(
                            success=False,
                            data={},
                            error="分析数据格式异常，无法继续"
                        )
                else:
                    return AgentResponse(
                        success=False,
                        data={},
                        error=f"未找到分析结果: {existing_result_id}"
                    )

            # 检查缓存（智能处理）- 除非强制创建
            cache_result = None if force_create else await self._check_cache(birth_info, calculation_type)
            if cache_result:
                cache_type = cache_result.get("cache_type", "same_type")

                if cache_type == "same_type":
                    # 相同类型的分析已存在
                    cached_data = self.cache.get_result(cache_result["result_id"])
                    if cached_data and not cached_data.chart_image_path:
                        logger.info(f"⚠️ 缓存结果缺少图片，重新生成图片: {cache_result['result_id']}")

                        # 重新生成图片
                        mock_calculation_result = {"data": cached_data.raw_calculation}
                        chart_image_path = await self._generate_chart_image(
                            mock_calculation_result, birth_info, calculation_type
                        )

                        if chart_image_path:
                            # 更新缓存中的图片路径
                            updated_result = CalculationResult(
                                result_id=cached_data.result_id,
                                user_id=cached_data.user_id,
                                session_id=cached_data.session_id,
                                calculation_type=cached_data.calculation_type,
                                birth_info=cached_data.birth_info,
                                raw_calculation=cached_data.raw_calculation,
                                detailed_analysis=cached_data.detailed_analysis,
                                summary=cached_data.summary,
                                keywords=cached_data.keywords,
                                confidence=cached_data.confidence,
                                created_at=cached_data.created_at,
                                updated_at=datetime.now().isoformat(),
                                chart_image_path=chart_image_path
                            )

                            # 保存更新后的结果
                            import json
                            from pathlib import Path
                            result_file = Path("data/calculation_cache") / f"{cached_data.result_id}.json"
                            result_file.parent.mkdir(parents=True, exist_ok=True)
                            with open(result_file, 'w', encoding='utf-8') as f:
                                json.dump(updated_result.to_dict(), f, ensure_ascii=False, indent=2)

                            logger.info(f"✅ 缓存图片路径已更新: {chart_image_path}")

                    logger.info(f"✅ 从缓存获取相同类型结果: {cache_result['result_id']}")
                    return AgentResponse(
                        success=True,
                        data={
                            "result_id": cache_result["result_id"],
                            "summary": cache_result["summary"],
                            "from_cache": True,
                            "calculation_type": calculation_type,
                            "birth_info": birth_info
                        }
                    )

                elif cache_type == "different_type":
                    # 发现其他类型的分析，提供选择
                    existing_analyses = cache_result.get("existing_analyses", [])
                    requested_type = cache_result.get("requested_type")

                    # 构建提示信息
                    existing_types = [self.calculation_types.get(a['calculation_type'], a['calculation_type']) for a in existing_analyses]
                    requested_type_name = self.calculation_types.get(requested_type, requested_type)

                    suggestion_message = f"""
🔍 **发现相同生辰信息的分析记录**

📋 **已有分析类型**：{', '.join(existing_types)}
🎯 **当前请求类型**：{requested_type_name}

💡 **建议选择**：
1️⃣ **创建新分析** - 为您生成{requested_type_name}分析，与现有分析相互印证
2️⃣ **查看已有分析** - 直接查看现有的{existing_types[0]}分析结果
3️⃣ **综合分析** - 结合多种分析方法，获得更全面的解读

🌟 **推荐**：不同分析方法可以相互印证，建议创建新的{requested_type_name}分析！
                    """

                    logger.info(f"🔍 发现其他类型分析，提供用户选择")
                    return AgentResponse(
                        success=True,
                        data={
                            "cache_type": "different_type",
                            "suggestion": "user_choice_needed",
                            "message": suggestion_message.strip(),
                            "existing_analyses": existing_analyses,
                            "requested_type": requested_type,
                            "birth_info": birth_info,
                            "options": {
                                "create_new": f"创建{requested_type_name}分析",
                                "view_existing": f"查看{existing_types[0]}分析",
                                "comprehensive": "综合多重分析"
                            }
                        }
                    )

            # 执行详细计算
            calculation_result = await self._execute_detailed_calculation(
                birth_info, calculation_type, user_message
            )

            if calculation_result.get("success"):
                # 🔧 修复：生成排盘图片
                chart_image_path = await self._generate_chart_image(
                    calculation_result, birth_info, calculation_type
                )

                # 🎯 关键修复：先保存基础结果和图片，立即返回result_id
                # 生成临时总结
                temp_summary = f"基于{self.calculation_types.get(calculation_type)}的专业分析正在生成中..."
                temp_keywords = [calculation_type, "算命", "分析"]

                # 立即保存基础结果（包含图片）
                result_id = self.cache.save_result(
                    user_id=session_id.split('_')[0] if '_' in session_id else session_id,
                    session_id=session_id,
                    calculation_type=calculation_type,
                    birth_info=birth_info,
                    raw_calculation=calculation_result.get("data", {}),
                    detailed_analysis={"status": "generating", "structured_analysis": {}},  # 临时状态
                    summary=temp_summary,
                    keywords=temp_keywords,
                    confidence=calculation_result.get("confidence", 0.9),
                    chart_image_path=chart_image_path  # 🔧 修复：保存图片路径
                )

                logger.info(f"✅ 基础结果和图片已保存: {result_id}")

                # 🎯 新的按需分析模式：不自动生成12角度分析
                # 只保存排盘数据，等待用户按需点击生成
                print(f"📊 排盘完成，等待用户按需生成分析")  # 强制输出
                logger.info(f"📊 排盘完成，等待用户按需生成分析")

                logger.info(f"✅ 详细分析完成并缓存: {result_id}")

                return AgentResponse(
                    success=True,
                    data={
                        "result_id": result_id,
                        "summary": temp_summary,  # 🔧 修复：使用临时总结
                        "from_cache": False,
                        "calculation_type": calculation_type,
                        "birth_info": birth_info,
                        "confidence": calculation_result.get("confidence", 0.9)
                    }
                )
            else:
                return AgentResponse(
                    success=False,
                    data={},
                    error=calculation_result.get("error", "算命计算失败")
                )

        except Exception as e:
            logger.error(f"后台Agent处理失败: {e}")
            import traceback
            traceback.print_exc()
            return AgentResponse(
                success=False,
                data={},
                error=str(e)
            )

    async def _handle_general_request(self, content: Dict[str, Any]) -> AgentResponse:
        """处理一般请求"""
        # 计算Agent主要处理算命计算，一般请求返回提示
        return AgentResponse(
            success=True,
            data={
                "response": "我是算命计算专家，专门负责算命计算和分析。请提供您的生辰信息进行算命。"
            }
        )

    async def _analyze_calculation_intent(self, user_message: str) -> Dict[str, Any]:
        """分析算命计算意图"""
        try:
            # 使用LLM进行意图识别
            intent_result = self.llm_client.intent_recognition(user_message)

            if intent_result.get("intent") == "error":
                return {"success": False, "error": "意图识别失败"}

            intent = intent_result.get("intent", "unknown")
            entities = intent_result.get("entities", {})

            # 验证是否为算命相关意图
            if intent not in ["ziwei", "bazi", "liuyao"]:
                return {"success": False, "error": f"不支持的算命类型: {intent}"}

            # 提取和验证生辰信息
            birth_info = self._extract_and_validate_birth_info(entities, user_message)

            if not birth_info.get("valid"):
                return {
                    "success": False,
                    "error": f"生辰信息不完整: {birth_info.get('missing', [])}"
                }

            return {
                "success": True,
                "calculation_type": intent,
                "birth_info": birth_info["data"],
                "original_message": user_message
            }

        except Exception as e:
            logger.error(f"分析算命意图失败: {e}")
            return {"success": False, "error": str(e)}

    def _extract_and_validate_birth_info(self, entities: Dict[str, Any],
                                        user_message: str) -> Dict[str, Any]:
        """提取和验证生辰信息"""
        birth_info = {}
        missing_fields = []

        # 提取年份
        year = entities.get("birth_year")
        if year:
            try:
                year_int = int(year)
                if 1900 <= year_int <= 2030:
                    birth_info["year"] = year_int
                else:
                    missing_fields.append("有效年份")
            except ValueError:
                missing_fields.append("有效年份")
        else:
            missing_fields.append("出生年份")

        # 提取月份
        month = entities.get("birth_month")
        if month:
            try:
                month_int = int(month)
                if 1 <= month_int <= 12:
                    birth_info["month"] = month_int
                else:
                    missing_fields.append("有效月份")
            except ValueError:
                missing_fields.append("有效月份")
        else:
            missing_fields.append("出生月份")

        # 提取日期
        day = entities.get("birth_day")
        if day:
            try:
                day_int = int(day)
                if 1 <= day_int <= 31:
                    birth_info["day"] = day_int
                else:
                    missing_fields.append("有效日期")
            except ValueError:
                missing_fields.append("有效日期")
        else:
            missing_fields.append("出生日期")

        # 提取时辰
        hour = entities.get("birth_hour")
        if hour:
            if hour in self.hour_mapping:
                birth_info["hour"] = self.hour_mapping[hour]
            else:
                # 尝试解析数字时间
                try:
                    hour_int = int(re.search(r'\d+', hour).group())
                    if 0 <= hour_int <= 23:
                        birth_info["hour"] = hour_int
                    else:
                        missing_fields.append("有效时辰")
                except:
                    missing_fields.append("有效时辰")
        else:
            missing_fields.append("出生时辰")

        # 提取性别 - 增强识别
        gender = entities.get("gender")
        if not gender:
            # 从原始消息中再次尝试提取性别
            user_message_lower = user_message.lower()
            if ("男" in user_message or "男性" in user_message) and "女" not in user_message:
                gender = "男"
            elif ("女" in user_message or "女性" in user_message) and "男" not in user_message:
                gender = "女"
            # 检查英文
            elif "male" in user_message_lower and "female" not in user_message_lower:
                gender = "男"
            elif "female" in user_message_lower and "male" not in user_message_lower:
                gender = "女"

        if gender in ["男", "女"]:
            birth_info["gender"] = gender
        else:
            # 如果还是没有性别信息，先不标记为缺失，让系统继续处理
            # missing_fields.append("性别")
            birth_info["gender"] = "男"  # 默认值，避免阻塞测试

        # 验证完整性
        is_valid = len(missing_fields) == 0

        return {
            "valid": is_valid,
            "data": birth_info,
            "missing": missing_fields
        }

    async def _execute_calculation(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """执行算命计算"""
        calculation_type = analysis_result.get("calculation_type")
        birth_info = analysis_result.get("birth_info")
        original_message = analysis_result.get("original_message")

        try:
            # 构建意图对象
            intent = {
                "intent": calculation_type,
                "confidence": 0.9,
                "original_message": original_message,
                "entities": {
                    "birth_year": str(birth_info["year"]),
                    "birth_month": str(birth_info["month"]),
                    "birth_day": str(birth_info["day"]),
                    "birth_hour": self._convert_hour_to_string(birth_info["hour"]),
                    "gender": birth_info["gender"]
                }
            }

            # 增强算法调用稳定性 - 添加重试机制
            max_retries = 3
            last_error = None

            for attempt in range(max_retries):
                try:
                    logger.info(f"算命计算尝试 {attempt + 1}/{max_retries}")

                    # 使用工具选择器执行计算
                    result = self.tool_selector.select_tool(intent, {})

                    if result.get("success"):
                        calculation_result = result.get("calculation_result", {})
                        if calculation_result.get("success"):
                            logger.info(f"算命计算成功 (尝试 {attempt + 1})")
                            return {
                                "success": True,
                                "data": calculation_result.get("raw_result", {}),
                                "confidence": 0.95,
                                "processing_time": calculation_result.get("processing_time"),
                                "attempt": attempt + 1
                            }
                        else:
                            last_error = calculation_result.get("error", "算法计算失败")
                            logger.warning(f"算法计算失败 (尝试 {attempt + 1}): {last_error}")
                    else:
                        last_error = result.get("message", "工具选择失败")
                        logger.warning(f"工具选择失败 (尝试 {attempt + 1}): {last_error}")

                    # 如果不是最后一次尝试，等待一下再重试
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)

                except Exception as e:
                    last_error = str(e)
                    logger.error(f"算命计算异常 (尝试 {attempt + 1}): {e}")

                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)

            # 所有重试都失败
            logger.error(f"算命计算完全失败，已重试 {max_retries} 次")
            return {
                "success": False,
                "error": f"算命计算失败 (已重试{max_retries}次): {last_error}",
                "retries": max_retries
            }

        except Exception as e:
            logger.error(f"执行算命计算失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _convert_hour_to_string(self, hour: int) -> str:
        """将小时转换为时辰字符串"""
        hour_to_shichen = {
            0: "子时", 1: "丑时", 2: "丑时", 3: "寅时", 4: "寅时",
            5: "卯时", 6: "卯时", 7: "辰时", 8: "辰时", 9: "巳时",
            10: "巳时", 11: "午时", 12: "午时", 13: "未时", 14: "未时",
            15: "申时", 16: "申时", 17: "酉时", 18: "酉时", 19: "戌时",
            20: "戌时", 21: "亥时", 22: "亥时", 23: "亥时"
        }
        return hour_to_shichen.get(hour, "午时")

    async def _perform_deep_analysis(self, calculation_result: Dict[str, Any],
                                   analysis_result: Dict[str, Any],
                                   user_message: str) -> Dict[str, Any]:
        """进行深度分析"""
        try:
            calculation_type = analysis_result.get("calculation_type")
            birth_info = analysis_result.get("birth_info")
            raw_data = calculation_result.get("data", {})

            # 构建深度分析提示词
            prompt = f"""
作为专业算命大师，请基于以下{self.calculation_types.get(calculation_type, calculation_type)}计算结果进行深度分析：

用户信息：
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {self._convert_hour_to_string(birth_info.get('hour', 12))}
- 性别：{birth_info.get('gender')}

计算结果：
{raw_data}

请提供以下方面的专业分析：
1. 基本命格特征
2. 性格特点分析
3. 事业发展趋势
4. 财运状况
5. 感情婚姻
6. 健康状况
7. 流年运势
8. 开运建议

要求：
- 分析专业准确
- 语言通俗易懂
- 内容积极正面
- 提供实用指导
- 每个方面100-150字

分析结果："""

            # 使用LLM进行深度分析
            messages = [{"role": "user", "content": prompt}]
            analysis_response = self.llm_client.chat_completion(
                messages, max_tokens=2000
            )

            # 解析分析结果
            analysis_sections = self._parse_analysis_response(analysis_response)

            return {
                "success": True,
                "sections": analysis_sections,
                "full_analysis": analysis_response,
                "analysis_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"深度分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_analysis": "根据您的命盘，您具有很好的潜质，建议保持积极心态，把握机会。"
            }

    def _parse_analysis_response(self, response: str) -> Dict[str, str]:
        """解析分析响应"""
        sections = {
            "basic_character": "",
            "personality": "",
            "career": "",
            "wealth": "",
            "relationship": "",
            "health": "",
            "fortune": "",
            "advice": ""
        }

        # 简单的文本分割解析
        # 这里可以根据实际LLM输出格式进行优化
        lines = response.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 识别章节标题
            if any(keyword in line for keyword in ["命格", "基本"]):
                current_section = "basic_character"
            elif any(keyword in line for keyword in ["性格", "特点"]):
                current_section = "personality"
            elif any(keyword in line for keyword in ["事业", "工作"]):
                current_section = "career"
            elif any(keyword in line for keyword in ["财运", "财富"]):
                current_section = "wealth"
            elif any(keyword in line for keyword in ["感情", "婚姻"]):
                current_section = "relationship"
            elif any(keyword in line for keyword in ["健康"]):
                current_section = "health"
            elif any(keyword in line for keyword in ["运势", "流年"]):
                current_section = "fortune"
            elif any(keyword in line for keyword in ["建议", "开运"]):
                current_section = "advice"
            elif current_section and line:
                # 添加内容到当前章节
                if sections[current_section]:
                    sections[current_section] += "\n" + line
                else:
                    sections[current_section] = line

        return sections

    async def _check_cache(self, birth_info: Dict[str, Any], calculation_type: str) -> Optional[Dict[str, Any]]:
        """智能检查缓存中是否有结果"""
        try:
            from core.storage.calculation_cache import CalculationCache
            cache = CalculationCache()

            # 🔧 新逻辑：检查相同生辰信息的所有分析类型
            existing_analyses = self._find_existing_analyses(birth_info)

            if not existing_analyses:
                return None  # 没有任何分析，可以直接创建

            # 检查是否已有相同类型的分析
            same_type_analysis = None
            other_type_analyses = []

            for analysis in existing_analyses:
                if analysis['calculation_type'] == calculation_type:
                    same_type_analysis = analysis
                else:
                    other_type_analyses.append(analysis)

            # 如果有相同类型的分析，直接返回
            if same_type_analysis:
                logger.info(f"✅ 找到相同类型的分析: {calculation_type}")
                return {
                    "result_id": same_type_analysis['result_id'],
                    "summary": same_type_analysis['summary'],
                    "detailed_analysis": same_type_analysis['detailed_analysis'],
                    "confidence": same_type_analysis['confidence'],
                    "cache_type": "same_type"
                }

            # 如果有其他类型的分析，提供选择
            if other_type_analyses:
                logger.info(f"🔍 找到其他类型的分析: {[a['calculation_type'] for a in other_type_analyses]}")
                return {
                    "cache_type": "different_type",
                    "existing_analyses": other_type_analyses,
                    "requested_type": calculation_type,
                    "birth_info": birth_info,
                    "suggestion": "found_other_types"
                }

            return None

        except Exception as e:
            logger.error(f"检查缓存失败: {e}")
            return None

    def _find_existing_analyses(self, birth_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查找相同生辰信息的所有分析"""
        try:
            import os
            import json

            cache_dir = "data/calculation_cache"
            if not os.path.exists(cache_dir):
                return []

            existing_analyses = []
            target_birth_key = self._generate_birth_key(birth_info)

            for filename in os.listdir(cache_dir):
                if not filename.endswith('.json') or filename == 'index.json':
                    continue

                filepath = os.path.join(cache_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    cached_birth_info = data.get('birth_info', {})
                    cached_birth_key = self._generate_birth_key(cached_birth_info)

                    if cached_birth_key == target_birth_key:
                        existing_analyses.append({
                            'result_id': data.get('result_id'),
                            'calculation_type': data.get('calculation_type'),
                            'summary': data.get('summary', ''),
                            'detailed_analysis': data.get('detailed_analysis', {}),
                            'confidence': data.get('confidence', 0.0),
                            'created_at': data.get('created_at', ''),
                            'birth_info': cached_birth_info
                        })

                except Exception as e:
                    logger.warning(f"读取缓存文件失败 {filename}: {e}")
                    continue

            return existing_analyses

        except Exception as e:
            logger.error(f"查找现有分析失败: {e}")
            return []

    def _generate_birth_key(self, birth_info: Dict[str, Any]) -> str:
        """生成生辰信息的唯一键（不包含分析类型）"""
        try:
            import hashlib

            # 标准化生辰信息
            year = str(birth_info.get('year', ''))
            month = str(birth_info.get('month', ''))
            day = str(birth_info.get('day', ''))
            hour = str(birth_info.get('hour', ''))
            gender = str(birth_info.get('gender', ''))

            # 生成唯一键
            birth_string = f"{year}-{month}-{day}-{hour}-{gender}"
            return hashlib.md5(birth_string.encode('utf-8')).hexdigest()[:16]

        except Exception as e:
            logger.error(f"生成生辰键失败: {e}")
            return "unknown"

    async def _execute_detailed_calculation(self, birth_info: Dict[str, Any],
                                          calculation_type: str, user_message: str) -> Dict[str, Any]:
        """执行详细计算 - 支持不同分析类型"""
        try:
            logger.info(f"🔧 执行{calculation_type}类型的详细计算")

            # 根据分析类型选择不同的处理方式
            if calculation_type == "combined":
                # 综合分析：紫薇+八字
                return await self._execute_combined_calculation(birth_info, user_message)
            elif calculation_type == "bazi":
                # 八字分析
                return await self._execute_bazi_calculation(birth_info, user_message)
            elif calculation_type == "ziwei":
                # 紫薇分析
                return await self._execute_ziwei_calculation(birth_info, user_message)
            else:
                logger.error(f"❌ 不支持的分析类型: {calculation_type}")
                return {"success": False, "error": f"不支持的分析类型: {calculation_type}"}

        except Exception as e:
            logger.error(f"详细计算失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_ziwei_calculation(self, birth_info: Dict[str, Any], user_message: str) -> Dict[str, Any]:
        """执行紫薇斗数计算"""
        try:
            # 构建紫薇分析的意图对象
            intent = {
                "intent": "ziwei",
                "confidence": 0.95,
                "original_message": user_message,
                "entities": {
                    "birth_year": str(birth_info.get("year", "")),
                    "birth_month": str(birth_info.get("month", "")),
                    "birth_day": str(birth_info.get("day", "")),
                    "birth_hour": birth_info.get("hour", ""),
                    "gender": birth_info.get("gender", "")
                }
            }

            # 使用工具选择器执行计算
            result = self.tool_selector.select_tool(intent, {})
            logger.info(f"紫薇工具选择器返回结果: {result}")

            return self._process_tool_result(result, "紫薇斗数")

        except Exception as e:
            logger.error(f"紫薇计算失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_bazi_calculation(self, birth_info: Dict[str, Any], user_message: str) -> Dict[str, Any]:
        """执行八字命理计算"""
        try:
            # 构建八字分析的意图对象
            intent = {
                "intent": "bazi",
                "confidence": 0.95,
                "original_message": user_message,
                "entities": {
                    "birth_year": str(birth_info.get("year", "")),
                    "birth_month": str(birth_info.get("month", "")),
                    "birth_day": str(birth_info.get("day", "")),
                    "birth_hour": birth_info.get("hour", ""),
                    "gender": birth_info.get("gender", "")
                }
            }

            # 使用工具选择器执行计算
            result = self.tool_selector.select_tool(intent, {})
            logger.info(f"八字工具选择器返回结果: {result}")

            return self._process_tool_result(result, "八字命理")

        except Exception as e:
            logger.error(f"八字计算失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_combined_calculation(self, birth_info: Dict[str, Any], user_message: str) -> Dict[str, Any]:
        """执行紫薇+八字综合分析"""
        try:
            logger.info("🌟 开始紫薇+八字综合分析")

            # 分别执行紫薇和八字计算
            ziwei_result = await self._execute_ziwei_calculation(birth_info, user_message)
            bazi_result = await self._execute_bazi_calculation(birth_info, user_message)

            # 检查两个算法是否都成功
            if ziwei_result.get("success") and bazi_result.get("success"):
                # 合并两个结果
                combined_data = {
                    "ziwei": ziwei_result.get("data", {}),
                    "bazi": bazi_result.get("data", {}),
                    "analysis_type": "combined",
                    "ziwei_confidence": ziwei_result.get("confidence", 0.9),
                    "bazi_confidence": bazi_result.get("confidence", 0.9)
                }

                logger.info("✅ 紫薇+八字综合分析成功")
                return {
                    "success": True,
                    "data": combined_data,
                    "confidence": 0.95,
                    "processing_time": ziwei_result.get("processing_time", 0) + bazi_result.get("processing_time", 0)
                }
            else:
                # 如果有一个失败，返回错误
                errors = []
                if not ziwei_result.get("success"):
                    errors.append(f"紫薇斗数: {ziwei_result.get('error', '未知错误')}")
                if not bazi_result.get("success"):
                    errors.append(f"八字命理: {bazi_result.get('error', '未知错误')}")

                error_msg = f"综合分析失败 - {'; '.join(errors)}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"综合分析失败: {e}")
            return {"success": False, "error": str(e)}

    def _process_tool_result(self, result: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """处理工具执行结果"""
        try:
            if result.get("success"):
                # 获取工具执行结果
                tool_result = result.get("result", {})

                # 检查工具结果类型
                if tool_result.get("success"):
                    # 正确提取人性化工具的算法数据
                    calculation_result = tool_result.get("calculation_result", {})
                    if calculation_result.get("success"):
                        # 从calculation_result中提取raw_result
                        raw_result = calculation_result.get("raw_result", {})
                        logger.info(f"✅ {tool_name}成功提取算法数据")
                        return {
                            "success": True,
                            "data": raw_result,
                            "confidence": 0.95,
                            "processing_time": calculation_result.get("processing_time", 0)
                        }
                    else:
                        # calculation_result失败，尝试其他路径
                        return {
                            "success": True,
                            "data": tool_result.get("raw_result", tool_result),
                            "confidence": 0.8,
                            "processing_time": 0
                        }
                elif tool_result.get("type") == "error":
                    # 工具返回错误
                    error_msg = tool_result.get("message", f"{tool_name}算法计算失败")
                    logger.error(f"{tool_name}工具执行错误: {error_msg}")
                    return {"success": False, "error": error_msg}
                else:
                    # 其他类型的结果，尝试提取
                    return {
                        "success": True,
                        "data": tool_result,
                        "confidence": 0.8,
                        "processing_time": 0
                    }
            else:
                # 工具选择失败
                error_msg = result.get("error", f"{tool_name}工具选择失败")
                logger.error(f"{tool_name}工具选择失败: {error_msg}")
                return {"success": False, "error": error_msg}

        except Exception as e:
            logger.error(f"{tool_name}结果处理失败: {e}")
            return {"success": False, "error": str(e)}

    async def _perform_comprehensive_analysis(self, calculation_result: Dict[str, Any],
                                            birth_info: Dict[str, Any],
                                            calculation_type: str) -> Dict[str, Any]:
        """12角度渐进式分析 - 按顺序生成，每完成一个立即保存"""
        try:
            calc_name = self.calculation_types.get(calculation_type, calculation_type)
            raw_data = calculation_result.get("data", {})

            logger.info(f"🔮 开始12角度渐进式分析 - 按顺序生成并保存")

            # 定义12个分析角度（按重要性排序）
            analysis_angles = [
                ("命宫分析", "personality_destiny", "性格命运核心特征与天赋潜能", 1),
                ("财富分析", "wealth_fortune", "财运状况、理财投资与财富积累", 2),
                ("婚姻分析", "marriage_love", "感情婚姻、桃花运势与配偶关系", 3),
                ("健康分析", "health_wellness", "健康状况、疾病预防与养生指导", 4),
                ("事业分析", "career_achievement", "事业发展、成就运势与职业规划", 5),
                ("子女分析", "children_creativity", "子女运势、创造力与生育指导", 6),
                ("人际分析", "interpersonal_relationship", "人际关系、贵人运势与社交能力", 7),
                ("学业分析", "education_learning", "学习教育、智慧发展与知识积累", 8),
                ("家庭分析", "family_environment", "家庭环境、房产田宅与居住运势", 9),
                ("迁移分析", "travel_relocation", "迁移变动、外出运势与环境适应", 10),
                ("精神分析", "spiritual_blessing", "精神世界、福德修养与心灵成长", 11),
                ("权威分析", "authority_parents", "父母长辈、权威关系与传承运势", 12)
            ]

            # 生成结果ID用于渐进式保存
            import hashlib
            import json
            key_data = {
                "birth_info": birth_info,
                "calculation_type": calculation_type
            }
            key_str = json.dumps(key_data, sort_keys=True)
            result_id = hashlib.md5(key_str.encode()).hexdigest()

            # 🔧 确保数据格式正确（在循环开始前统一处理）
            if calc_name == "紫薇+八字融合分析":
                logger.info(f"🔄 确保12角度分析的数据格式正确")

                # 检查raw_data是否包含正确的结构
                if "ziwei_analysis" not in raw_data and "raw_calculation" in raw_data:
                    # 如果是缓存格式，转换为新分析系统期望的格式
                    raw_calculation = raw_data.get("raw_calculation", {})

                    # 重构数据格式
                    corrected_data = {
                        "success": True,
                        "ziwei_analysis": raw_calculation.get("ziwei", {}),
                        "bazi_analysis": raw_calculation.get("bazi", {}),
                        "ziwei_confidence": raw_calculation.get("ziwei_confidence", 0.95),
                        "bazi_confidence": raw_calculation.get("bazi_confidence", 0.95)
                    }

                    raw_data = corrected_data
                    logger.info(f"✅ 已转换12角度分析的缓存数据为新分析系统格式")

                elif "ziwei_analysis" not in raw_data:
                    # 如果数据格式不正确，重新获取
                    logger.info(f"🔄 重新获取12角度分析的融合分析数据")
                    from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

                    fusion_engine = ZiweiBaziFusionEngine()
                    fusion_result = fusion_engine.calculate_fusion_analysis(
                        year=int(birth_info.get("year")),
                        month=int(birth_info.get("month")),
                        day=int(birth_info.get("day")),
                        hour=self._convert_hour_to_number(birth_info.get("hour")),
                        gender=birth_info.get("gender")
                    )

                    if fusion_result.get("success"):
                        raw_data = fusion_result  # 使用正确的融合分析数据
                        logger.info(f"✅ 已重新获取12角度分析的正确融合分析数据")
                    else:
                        logger.warning(f"⚠️ 重新获取12角度分析的融合分析数据失败，使用原始数据")
                else:
                    logger.info(f"✅ 12角度分析的数据格式正确，无需修复")

            # 按顺序生成12个角度，每完成一个立即保存并输出进度
            angle_analyses = {}
            total_word_count = 0

            print(f"🔮 开始12角度渐进式分析，请耐心等待...")
            print("=" * 60)

            for i, (angle_name, analysis_key, description, order) in enumerate(analysis_angles, 1):
                print(f"🏛️ 开始生成第{i}/12个角度: {angle_name}")
                print(f"   描述: {description}")
                logger.info(f"🏛️ 生成第{i}/12个角度: {angle_name} - {description}")

                import time
                start_time = time.time()

                try:
                    # 生成单个角度的4000-5000字详细分析，传入已完成的分析用于一致性检查
                    angle_analysis = await self._analyze_single_angle(
                        angle_name, analysis_key, description, raw_data, birth_info, calc_name,
                        previous_analyses=angle_analyses  # 传入已完成的分析
                    )

                    generation_time = time.time() - start_time

                    if angle_analysis:
                        angle_analyses[analysis_key] = angle_analysis
                        word_count = len(angle_analysis)
                        total_word_count += word_count

                        # 立即保存当前进度到缓存
                        await self._save_progressive_result(
                            result_id, angle_analyses, birth_info, calculation_type,
                            raw_data, total_word_count, i, len(analysis_angles)
                        )

                        print(f"✅ {angle_name}完成！")
                        print(f"   字数: {word_count}")
                        print(f"   耗时: {generation_time:.1f}秒")
                        print(f"   进度: {i}/12 ({i/12*100:.1f}%)")
                        print(f"   总字数: {total_word_count}")
                        print("-" * 50)

                        logger.info(f"✅ {angle_name}完成并保存: {word_count}字 (进度: {i}/12, 耗时: {generation_time:.1f}s)")
                    else:
                        print(f"⚠️ {angle_name}分析失败，使用备用内容")
                        logger.warning(f"⚠️ {angle_name}分析失败，使用备用内容")
                        angle_analyses[analysis_key] = f"{angle_name}分析暂时无法生成，请稍后重试。"

                except Exception as e:
                    generation_time = time.time() - start_time
                    print(f"❌ {angle_name}分析异常: {e}")
                    print(f"   耗时: {generation_time:.1f}秒")
                    logger.error(f"❌ {angle_name}分析异常: {e}")
                    angle_analyses[analysis_key] = f"{angle_name}分析遇到问题，请稍后重试。"

            # 🔍 一致性检查和整合
            print(f"🔍 进行一致性检查...")
            logger.info(f"🔍 进行一致性检查")

            from core.analysis.consistency_manager import ConsistencyManager
            consistency_manager = ConsistencyManager()

            consistency_report = consistency_manager.check_consistency(angle_analyses)
            consistency_score = consistency_report.get('consistency_score', 0)

            print(f"📊 一致性评分: {consistency_score}/100")
            logger.info(f"📊 一致性评分: {consistency_score}/100")

            # 如果一致性分数较低，进行整合优化
            if consistency_score < 80:
                print(f"⚠️ 一致性分数较低，进行整合优化...")
                logger.info(f"⚠️ 一致性分数较低，进行整合优化")

                integration_prompt = consistency_manager.generate_integration_prompt(
                    angle_analyses, consistency_report
                )

                try:
                    # 调用LLM进行整合
                    integrated_content = self.llm_client.chat_completion(
                        [{"role": "user", "content": integration_prompt}],
                        max_tokens=3000
                    )

                    if integrated_content:
                        print(f"✅ 整合优化完成")
                        logger.info(f"✅ 整合优化完成")
                        # 可以选择是否替换原分析或作为补充
                        angle_analyses["integrated_summary"] = integrated_content
                        total_word_count += len(integrated_content)

                except Exception as e:
                    logger.warning(f"⚠️ 整合优化失败: {e}")

            # 生成最终综合总结
            comprehensive_summary = await self._generate_angle_summary(angle_analyses, birth_info)

            # 最终保存完整结果（包含一致性报告）
            await self._save_progressive_result(
                result_id, angle_analyses, birth_info, calculation_type,
                raw_data, total_word_count, len(analysis_angles), len(analysis_angles),
                comprehensive_summary, final=True, consistency_report=consistency_report
            )

            logger.info(f"🎉 12角度分析全部完成并保存")
            logger.info(f"   总字数: {total_word_count}")
            logger.info(f"   一致性评分: {consistency_score}/100")
            logger.info(f"   结果ID: {result_id}")

            return {
                "success": True,
                "result_id": result_id,
                "angle_analyses": angle_analyses,
                "comprehensive_summary": comprehensive_summary,
                "structured_analysis": angle_analyses,  # 兼容现有接口
                "analysis_time": datetime.now().isoformat(),
                "total_word_count": total_word_count,
                "completed_angles": len(analysis_angles),
                "total_angles": len(analysis_angles),
                "consistency_score": consistency_score,
                "consistency_report": consistency_report,
                "analysis_progress": "completed"
            }

        except Exception as e:
            logger.error(f"12角度分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "fallback_analysis": "根据您的命盘，您具有很好的潜质和发展前景。"
            }

    async def continue_angle_analysis(self, result_id: str, target_angle: str = None) -> Dict[str, Any]:
        """继续生成下一个角度的分析"""
        try:
            # 获取当前进度
            cached_result = self.cache.get_result(result_id)
            if not cached_result:
                return {"success": False, "error": "未找到分析结果"}

            current_analyses = cached_result.detailed_analysis.get("angle_analyses", {})
            birth_info = cached_result.birth_info
            calculation_type = cached_result.calculation_type

            # 重新获取算法数据并确保格式正确
            raw_data = cached_result.raw_calculation
            calc_name = self.calculation_types.get(calculation_type, calculation_type)

            # 🔧 修复：确保数据格式正确（与主流程保持一致）
            if calculation_type == "combined" and calc_name == "紫薇+八字融合分析":
                logger.info(f"🔄 修复继续分析的数据格式")

                # 检查raw_data是否包含正确的结构
                if "ziwei_analysis" not in raw_data and "raw_calculation" in raw_data:
                    # 如果是缓存格式，转换为新分析系统期望的格式
                    raw_calculation = raw_data.get("raw_calculation", {})

                    # 重构数据格式
                    corrected_data = {
                        "success": True,
                        "ziwei_analysis": raw_calculation.get("ziwei", {}),
                        "bazi_analysis": raw_calculation.get("bazi", {}),
                        "ziwei_confidence": raw_calculation.get("ziwei_confidence", 0.95),
                        "bazi_confidence": raw_calculation.get("bazi_confidence", 0.95)
                    }

                    raw_data = corrected_data
                    logger.info(f"✅ 已转换继续分析的缓存数据为新分析系统格式")

                elif "ziwei_analysis" not in raw_data:
                    # 如果数据格式不正确，重新获取
                    logger.info(f"🔄 重新获取继续分析的融合分析数据")
                    from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

                    fusion_engine = ZiweiBaziFusionEngine()
                    fusion_result = fusion_engine.calculate_fusion_analysis(
                        year=int(birth_info.get("year")),
                        month=int(birth_info.get("month")),
                        day=int(birth_info.get("day")),
                        hour=self._convert_hour_to_number(birth_info.get("hour")),
                        gender=birth_info.get("gender")
                    )

                    if fusion_result.get("success"):
                        raw_data = fusion_result  # 使用正确的融合分析数据
                        logger.info(f"✅ 已重新获取继续分析的正确融合分析数据")
                    else:
                        logger.warning(f"⚠️ 重新获取继续分析的融合分析数据失败，使用原始数据")
                else:
                    logger.info(f"✅ 继续分析的数据格式正确，无需修复")

            # 定义角度顺序
            analysis_angles = [
                ("命宫分析", "personality_destiny", "性格命运核心特征与天赋潜能", 1),
                ("财富分析", "wealth_fortune", "财运状况、理财投资与财富积累", 2),
                ("婚姻分析", "marriage_love", "感情婚姻、桃花运势与配偶关系", 3),
                ("健康分析", "health_wellness", "健康状况、疾病预防与养生指导", 4),
                ("事业分析", "career_achievement", "事业发展、成就运势与职业规划", 5),
                ("子女分析", "children_creativity", "子女运势、创造力与生育指导", 6),
                ("人际分析", "interpersonal_relationship", "人际关系、贵人运势与社交能力", 7),
                ("学业分析", "education_learning", "学习教育、智慧发展与知识积累", 8),
                ("家庭分析", "family_environment", "家庭环境、房产田宅与居住运势", 9),
                ("迁移分析", "travel_relocation", "迁移变动、外出运势与环境适应", 10),
                ("精神分析", "spiritual_blessing", "精神世界、福德修养与心灵成长", 11),
                ("权威分析", "authority_parents", "父母长辈、权威关系与传承运势", 12)
            ]

            # 确定下一个要生成的角度
            next_angle = None
            if target_angle:
                # 用户指定了特定角度
                for angle_name, analysis_key, description, order in analysis_angles:
                    if analysis_key == target_angle and analysis_key not in current_analyses:
                        next_angle = (angle_name, analysis_key, description, order)
                        break
            else:
                # 按顺序生成下一个
                for angle_name, analysis_key, description, order in analysis_angles:
                    if analysis_key not in current_analyses:
                        next_angle = (angle_name, analysis_key, description, order)
                        break

            if not next_angle:
                return {
                    "success": True,
                    "message": "所有角度分析已完成",
                    "completed_angles": len(current_analyses),
                    "total_angles": len(analysis_angles)
                }

            angle_name, analysis_key, description, order = next_angle
            logger.info(f"🏛️ 生成第{order}个角度: {angle_name} - {description}")

            # 生成新角度的分析
            angle_analysis = await self._analyze_single_angle(
                angle_name, analysis_key, description, raw_data, birth_info, calc_name
            )

            if angle_analysis:
                # 更新分析结果
                current_analyses[analysis_key] = angle_analysis
                word_count = len(angle_analysis)
                total_word_count = sum(len(content) for content in current_analyses.values())

                # 更新缓存
                updated_detailed_analysis = cached_result.detailed_analysis.copy()
                updated_detailed_analysis["angle_analyses"] = current_analyses
                updated_detailed_analysis["total_word_count"] = total_word_count
                updated_detailed_analysis["completed_angles"] = len(current_analyses)

                # 重新保存到缓存
                self.cache.save_result(
                    user_id=cached_result.user_id,
                    session_id=cached_result.session_id,
                    calculation_type=calculation_type,
                    birth_info=birth_info,
                    raw_calculation=raw_data,
                    detailed_analysis=updated_detailed_analysis,
                    summary=cached_result.summary,
                    keywords=cached_result.keywords,
                    confidence=cached_result.confidence
                )

                logger.info(f"✅ {angle_name}分析完成: {word_count}字 (总进度: {len(current_analyses)}/12)")

                return {
                    "success": True,
                    "angle_name": angle_name,
                    "analysis_key": analysis_key,
                    "word_count": word_count,
                    "completed_angles": len(current_analyses),
                    "total_angles": len(analysis_angles),
                    "total_word_count": total_word_count
                }
            else:
                return {"success": False, "error": f"{angle_name}分析生成失败"}

        except Exception as e:
            logger.error(f"继续角度分析失败: {e}")
            return {"success": False, "error": str(e)}

    async def _analyze_single_angle(self, angle_name: str, analysis_key: str, description: str,
                                   raw_data: Dict[str, Any], birth_info: Dict[str, Any],
                                   calc_name: str, previous_analyses: Dict[str, str] = None) -> str:
        """分析单个角度，使用增强版提示词和一致性检查"""
        try:
            logger.info(f"🔄 开始增强版{angle_name}分析")

            # 🎯 使用全新的分析控制器（数据已在主流程中修复）
            from core.analysis.analysis_controller import AnalysisController

            analysis_controller = AnalysisController()

            # 执行分析
            analysis_result = await analysis_controller.execute_single_analysis(
                raw_data=raw_data,
                birth_info=birth_info,
                analysis_type=analysis_key
            )

            if analysis_result.get("success") and analysis_result.get("content"):
                content = analysis_result["content"]
                logger.info(f"✅ {angle_name}分析完成: {len(content)}字")
                return content
            else:
                error_msg = analysis_result.get("error", "未知错误")
                logger.warning(f"⚠️ {angle_name}分析失败: {error_msg}")
                return analysis_result.get("content", f"【{angle_name}】分析遇到问题，请稍后重试。")

        except Exception as e:
            logger.error(f"单角度分析失败 {angle_name}: {e}")
            return f"【{angle_name}】分析遇到问题，请稍后重试。"

    async def _generate_angle_summary(self, angle_analyses: Dict[str, str],
                                     birth_info: Dict[str, Any]) -> str:
        """基于12角度分析生成综合总结"""
        try:
            # 统计已完成的分析
            completed_angles = [key for key, value in angle_analyses.items() if value and len(value) > 100]
            total_word_count = sum(len(value) for value in angle_analyses.values() if value)

            # 提取核心内容用于总结
            core_content = ""
            priority_angles = ["personality_destiny", "wealth_fortune", "marriage_love", "health_wellness", "children_creativity"]

            for angle_key in priority_angles:
                if angle_key in angle_analyses and angle_analyses[angle_key]:
                    content = angle_analyses[angle_key][:300]  # 取前300字
                    core_content += f"{content}\n\n"

            prompt = f"""
基于以下12角度详细分析的核心内容，生成一个简洁的综合总结（300-500字）：

用户信息：
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}
- 性别：{birth_info.get('gender')}

核心分析内容：
{core_content}

分析统计：
- 已完成角度：{len(completed_angles)}/12
- 总分析字数：{total_word_count}字

请生成综合总结，包含：
1. 整体命格特点（2-3句）
2. 主要优势和潜力（2-3句）
3. 核心建议和指导（2-3句）
4. 整体运势概况（1-2句）

要求：语言简洁明了，适合在对话中使用，总字数300-500字。

综合总结："""

            messages = [{"role": "user", "content": prompt}]
            summary = self.llm_client.chat_completion(messages, max_tokens=600)

            return summary.strip() if summary else f"根据您的命盘12角度详细分析（总计{total_word_count}字），您具有良好的发展潜力和多方面的优势。建议您把握机会，发挥所长，必能获得理想的人生成就。"

        except Exception as e:
            logger.error(f"生成角度总结失败: {e}")
            return "根据您的命盘分析，您具有良好的发展潜力，建议把握机会，积极进取。"

    def _parse_comprehensive_analysis(self, response: str) -> Dict[str, str]:
        """解析全面分析响应为结构化数据"""
        sections = {
            "basic_character": "",
            "personality": "",
            "career": "",
            "wealth": "",
            "relationship": "",
            "health": "",
            "interpersonal": "",
            "education": "",
            "family": "",
            "yearly_fortune": "",
            "major_cycles": "",
            "enhancement_methods": "",
            "precautions": "",
            "development_advice": "",
            "overall_summary": ""
        }

        if not response:
            return sections

        # 简单的文本分割解析（可以根据实际LLM输出格式优化）
        lines = response.split('\n')
        current_section = None
        section_mapping = {
            "基本命格": "basic_character",
            "性格特点": "personality",
            "事业发展": "career",
            "财运状况": "wealth",
            "感情婚姻": "relationship",
            "健康状况": "health",
            "人际关系": "interpersonal",
            "学业教育": "education",
            "家庭关系": "family",
            "流年运势": "yearly_fortune",
            "大运周期": "major_cycles",
            "开运方法": "enhancement_methods",
            "注意事项": "precautions",
            "发展建议": "development_advice",
            "综合运势": "overall_summary"
        }

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 识别章节标题
            for keyword, section_key in section_mapping.items():
                if keyword in line:
                    current_section = section_key
                    break

            # 添加内容到当前章节
            if current_section and line and not any(kw in line for kw in section_mapping.keys()):
                if sections[current_section]:
                    sections[current_section] += "\n" + line
                else:
                    sections[current_section] = line

        return sections

    async def _generate_summary(self, comprehensive_analysis: Dict[str, Any]) -> str:
        """生成简要总结供主Agent调用"""
        try:
            full_analysis = comprehensive_analysis.get("full_analysis", "")

            prompt = f"""
基于以下详细的算命分析，生成一个简洁的总结（200-300字），供客服使用：

详细分析：
{full_analysis[:2000]}...

请生成简洁总结，包含：
1. 核心性格特点（1-2句）
2. 主要运势概况（2-3句）
3. 关键建议（1-2句）

要求：语言简洁明了，适合在对话中使用。

简洁总结："""

            messages = [{"role": "user", "content": prompt}]
            summary = self.llm_client.chat_completion(messages, max_tokens=400)

            return summary.strip() if summary else "根据您的命盘分析，您具有良好的发展潜力，建议把握机会，积极进取。"

        except Exception as e:
            logger.error(f"生成总结失败: {e}")
            return "根据您的命盘分析，您具有良好的发展潜力，建议把握机会，积极进取。"

    async def _generate_initial_summary(self, angle_analyses: Dict[str, str],
                                       birth_info: Dict[str, Any], calc_name: str) -> str:
        """基于命宫分析生成初始总结，引导用户开始互动"""
        try:
            personality_analysis = angle_analyses.get("personality_destiny", "")

            if personality_analysis:
                # 提取命宫分析的核心内容
                core_content = personality_analysis[:500]  # 取前500字

                prompt = f"""
基于以下{calc_name}命宫分析，生成一个引导性的总结（200-300字），鼓励用户开始互动：

用户信息：
- 出生时间：{birth_info.get('year')}年{birth_info.get('month')}月{birth_info.get('day')}日 {birth_info.get('hour')}
- 性别：{birth_info.get('gender')}

命宫分析核心内容：
{core_content}

请生成引导性总结，包含：
1. 核心性格特点（2-3句）
2. 主要天赋和潜力（1-2句）
3. 引导用户互动（1-2句，如"您可以问我关于性格、财运、感情等方面的问题"）

要求：语言亲切自然，适合开始对话，总字数200-300字。

引导性总结："""

                messages = [{"role": "user", "content": prompt}]
                summary = self.llm_client.chat_completion(messages, max_tokens=400)

                if summary:
                    return summary.strip() + "\n\n💬 您可以问我关于性格特点、财运状况、感情婚姻等任何方面的问题，我会基于您的详细命盘分析为您解答。"

            return f"根据您的{calc_name}分析，您的命宫显示出良好的特质和发展潜力。您可以问我关于性格、财运、感情、事业等任何方面的问题。"

        except Exception as e:
            logger.error(f"生成初始总结失败: {e}")
            return "您的命盘分析已开始，命宫分析已完成。您可以问我关于性格、财运、感情等任何方面的问题。"

    async def _save_progressive_result(self, result_id: str, angle_analyses: Dict[str, str],
                                      birth_info: Dict[str, Any], calculation_type: str,
                                      raw_data: Dict[str, Any], total_word_count: int,
                                      completed_angles: int, total_angles: int,
                                      summary: str = None, final: bool = False,
                                      consistency_report: Dict[str, Any] = None):
        """渐进式保存分析结果"""
        try:
            # 构建详细分析数据
            detailed_analysis = {
                "angle_analyses": angle_analyses,
                "total_word_count": total_word_count,
                "completed_angles": completed_angles,
                "total_angles": total_angles,
                "analysis_time": datetime.now().isoformat(),
                "is_final": final
            }

            # 添加一致性报告
            if consistency_report:
                detailed_analysis["consistency_report"] = consistency_report

            # 生成当前进度的总结
            if not summary:
                if completed_angles == 1:
                    summary = await self._generate_initial_summary(angle_analyses, birth_info, self.calculation_types.get(calculation_type, calculation_type))
                else:
                    summary = await self._generate_angle_summary(angle_analyses, birth_info)

            # 提取关键词
            keywords = []
            for angle_key in angle_analyses.keys():
                if angle_key == "personality_destiny":
                    keywords.extend(["性格", "命格", "天赋"])
                elif angle_key == "wealth_fortune":
                    keywords.extend(["财运", "财富", "理财"])
                elif angle_key == "marriage_love":
                    keywords.extend(["感情", "婚姻", "桃花"])
                elif angle_key == "health_wellness":
                    keywords.extend(["健康", "养生", "疾病"])
                elif angle_key == "career_achievement":
                    keywords.extend(["事业", "工作", "职业"])

            # 保存到缓存
            self.cache.save_result(
                user_id="progressive_user",  # 可以从消息中获取
                session_id="progressive_session",  # 可以从消息中获取
                calculation_type=calculation_type,
                birth_info=birth_info,
                raw_calculation=raw_data,
                detailed_analysis=detailed_analysis,
                summary=summary,
                keywords=keywords[:10],
                confidence=0.95
            )

            logger.info(f"📥 渐进式保存完成: {completed_angles}/{total_angles} 角度, {total_word_count}字")

        except Exception as e:
            logger.error(f"渐进式保存失败: {e}")

    def get_angle_progress(self, result_id: str) -> Dict[str, Any]:
        """获取角度分析进度"""
        try:
            result = self.cache.get_result(result_id)
            if not result:
                return {"completed_angles": 0, "total_angles": 12, "available_angles": []}

            detailed_analysis = result.detailed_analysis
            if not detailed_analysis:
                return {"completed_angles": 0, "total_angles": 12, "available_angles": []}

            angle_analyses = detailed_analysis.get("angle_analyses", {})
            completed_angles = len([k for k, v in angle_analyses.items() if v and len(v) > 100])

            return {
                "completed_angles": completed_angles,
                "total_angles": detailed_analysis.get("total_angles", 12),
                "available_angles": list(angle_analyses.keys()),
                "total_word_count": detailed_analysis.get("total_word_count", 0),
                "is_final": detailed_analysis.get("is_final", False)
            }

        except Exception as e:
            logger.error(f"获取角度进度失败: {e}")
            return {"completed_angles": 0, "total_angles": 12, "available_angles": []}

    def get_specific_angle(self, result_id: str, angle_key: str) -> Optional[str]:
        """获取特定角度的分析内容"""
        try:
            result = self.cache.get_result(result_id)
            if not result:
                return None

            detailed_analysis = result.detailed_analysis
            if not detailed_analysis:
                return None

            angle_analyses = detailed_analysis.get("angle_analyses", {})
            return angle_analyses.get(angle_key, None)

        except Exception as e:
            logger.error(f"获取特定角度失败: {e}")
            return None

    async def _extract_keywords(self, comprehensive_analysis: Dict[str, Any]) -> List[str]:
        """提取关键词"""
        try:
            full_analysis = comprehensive_analysis.get("full_analysis", "")

            # 简单的关键词提取（可以使用更复杂的NLP方法）
            keywords = []

            # 预定义关键词
            predefined_keywords = [
                "事业运", "财运", "感情运", "健康运", "学业运",
                "贵人运", "桃花运", "偏财运", "正财运", "官运",
                "创业", "投资", "婚姻", "恋爱", "健康",
                "学习", "考试", "工作", "升职", "跳槽"
            ]

            for keyword in predefined_keywords:
                if keyword in full_analysis:
                    keywords.append(keyword)

            # 限制关键词数量
            return keywords[:10]

        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return ["运势分析", "命理咨询"]

    def get_cached_result(self, result_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的结果"""
        try:
            result = self.cache.get_result(result_id)
            if result:
                return {
                    "result_id": result_id,
                    "summary": result.summary,
                    "detailed_analysis": result.detailed_analysis,
                    "birth_info": result.birth_info,
                    "calculation_type": result.calculation_type,
                    "confidence": result.confidence,
                    "created_at": result.created_at
                }
            return None
        except Exception as e:
            logger.error(f"获取缓存结果失败: {e}")
            return None

    def get_detailed_section(self, result_id: str, section: str) -> Optional[str]:
        """获取详细分析的特定章节"""
        try:
            result = self.cache.get_result(result_id)
            if result and result.detailed_analysis:
                structured = result.detailed_analysis.get("structured_analysis", {})
                return structured.get(section, "")
            return None
        except Exception as e:
            logger.error(f"获取详细章节失败: {e}")
            return None

    async def _generate_chart_image(self, calculation_result: Dict[str, Any],
                                   birth_info: Dict[str, Any],
                                   calculation_type: str) -> Optional[str]:
        """生成HTML排盘图表（替代传统图片）"""
        try:
            logger.info(f"🎨 开始生成{self.calculation_types.get(calculation_type)}HTML排盘图表")

            # 检查是否是融合分析
            if calculation_type == "combined":
                # 使用融合分析引擎生成HTML
                from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

                fusion_engine = ZiweiBaziFusionEngine()

                # 重新计算融合分析以获取完整数据
                fusion_result = fusion_engine.calculate_fusion_analysis(
                    year=int(birth_info.get("year")),
                    month=int(birth_info.get("month")),
                    day=int(birth_info.get("day")),
                    hour=self._convert_hour_to_number(birth_info.get("hour")),
                    gender=birth_info.get("gender")
                )

                if fusion_result.get("success"):
                    # 生成HTML图表
                    html_content = self._generate_html_chart(fusion_result)

                    # 保存HTML文件
                    import os
                    import time

                    charts_dir = "charts"
                    if not os.path.exists(charts_dir):
                        os.makedirs(charts_dir)

                    timestamp = int(time.time() * 1000)
                    html_filename = f"fusion_chart_{timestamp}.html"
                    html_path = os.path.join(charts_dir, html_filename)

                    with open(html_path, 'w', encoding='utf-8') as f:
                        f.write(html_content)

                    logger.info(f"✅ HTML排盘图表生成成功: {html_path}")
                    return html_path
                else:
                    logger.warning("⚠️ 融合分析失败，无法生成HTML图表")
                    return None
            else:
                # 对于非融合分析，暂时保持原有图片生成逻辑
                logger.info(f"⚠️ 非融合分析类型 {calculation_type}，使用传统图片生成")

                # 导入图片生成模块
                from core.fortune_engine import FortuneEngine

                # 创建引擎实例
                engine = FortuneEngine()

                # 构建算法结果格式
                algorithm_result = {
                    "success": True,
                    "data": calculation_result.get("data", {}),
                    "type": calculation_type
                }

                # 生成排盘图显示（包含图片）
                chart_display = engine._generate_chart_display(algorithm_result)

                # 从显示内容中提取图片路径
                import re
                image_matches = re.findall(r'图片已生成: (.+)', chart_display)

                if image_matches:
                    image_path = image_matches[0].strip()
                    logger.info(f"✅ 排盘图片生成成功: {image_path}")
                    return image_path
                else:
                    logger.warning("⚠️ 排盘图片生成失败，未找到图片路径")
                    return None

        except Exception as e:
            logger.error(f"❌ 排盘图表生成异常: {e}")
            return None

    def _convert_hour_to_number(self, hour_input) -> int:
        """转换时辰为数字"""
        if isinstance(hour_input, int):
            return hour_input

        if isinstance(hour_input, str):
            if hour_input.isdigit():
                return int(hour_input)

            # 中文时辰转换
            time_mapping = {
                "子时": 0, "丑时": 2, "寅时": 4, "卯时": 6,
                "辰时": 8, "巳时": 10, "午时": 12, "未时": 14,
                "申时": 16, "酉时": 18, "戌时": 20, "亥时": 22
            }

            for chinese_time, hour in time_mapping.items():
                if chinese_time in hour_input:
                    return hour

        # 默认返回12点（午时）
        return 12

    def _generate_html_chart(self, fusion_result: Dict[str, Any]) -> str:
        """生成HTML图表内容"""
        try:
            # 导入HTML图表生成器
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

            from generate_html_ziwei import create_html_chart

            # 生成HTML内容
            html_content = create_html_chart(fusion_result)

            return html_content

        except Exception as e:
            logger.error(f"HTML图表生成失败: {e}")
            # 返回一个简单的HTML占位符
            return f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>命盘图表</title>
            </head>
            <body>
                <div style="text-align: center; padding: 50px;">
                    <h2>命盘图表生成失败</h2>
                    <p>错误信息: {str(e)}</p>
                </div>
            </body>
            </html>
            """

    def _update_cached_analysis(self, result_id: str, comprehensive_analysis: Dict[str, Any],
                               summary: str, keywords: List[str]):
        """更新缓存中的详细分析"""
        try:
            # 获取现有缓存结果
            cached_result = self.cache.get_result(result_id)
            if not cached_result:
                logger.error(f"❌ 未找到缓存结果: {result_id}")
                return

            # 更新详细分析
            updated_result = CalculationResult(
                result_id=cached_result.result_id,
                user_id=cached_result.user_id,
                session_id=cached_result.session_id,
                calculation_type=cached_result.calculation_type,
                birth_info=cached_result.birth_info,
                raw_calculation=cached_result.raw_calculation,
                detailed_analysis=comprehensive_analysis,  # 更新详细分析
                summary=summary,  # 更新总结
                keywords=keywords,  # 更新关键词
                confidence=cached_result.confidence,
                created_at=cached_result.created_at,
                updated_at=datetime.now().isoformat(),
                chart_image_path=cached_result.chart_image_path  # 保持图片路径
            )

            # 保存更新后的结果
            import json
            from pathlib import Path
            result_file = Path("data/calculation_cache") / f"{result_id}.json"
            result_file.parent.mkdir(parents=True, exist_ok=True)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(updated_result.to_dict(), f, ensure_ascii=False, indent=2)

            # 更新内存缓存
            self.cache.memory_cache[result_id] = updated_result

            logger.info(f"✅ 缓存分析已更新: {result_id}")

        except Exception as e:
            logger.error(f"❌ 更新缓存分析失败: {e}")
