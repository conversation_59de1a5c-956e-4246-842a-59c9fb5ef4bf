#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试web端图片显示功能
"""

def test_web_modifications():
    """测试web端修改"""
    print("🌐 测试web端图片显示修改")
    print("=" * 40)
    
    try:
        # 检查web端文件
        with open("web_demo/prompt_web.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查导入
        if "from PIL import Image" in content:
            print("✅ PIL Image导入已添加")
        else:
            print("❌ PIL Image导入缺失")
            return False
        
        # 检查图片显示函数
        if "def display_chart_images" in content:
            print("✅ 图片显示函数已添加")
        else:
            print("❌ 图片显示函数缺失")
            return False
        
        # 检查函数调用
        if "display_chart_images(ai_response)" in content:
            print("✅ 图片显示函数调用已添加")
        else:
            print("❌ 图片显示函数调用缺失")
            return False
        
        # 检查Streamlit图片显示
        if "st.image(" in content:
            print("✅ Streamlit图片显示已添加")
        else:
            print("❌ Streamlit图片显示缺失")
            return False
        
        # 检查下载功能
        if "st.download_button" in content:
            print("✅ 图片下载功能已添加")
        else:
            print("❌ 图片下载功能缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ web端检查失败: {e}")
        return False

def test_image_path_detection():
    """测试图片路径检测"""
    print("\n🔍 测试图片路径检测")
    print("=" * 30)
    
    # 模拟AI响应
    test_response = """
# 🔮 紫薇斗数命理分析报告

## 📊 【命盘排盘图】

📊 **整合命理排盘图片**
图片已生成: charts/integrated_chart_1308.png

╔═══════════════════════════════════════════════════════════════════════════════╗
║                           紫薇斗数命盘                                        ║
╚═══════════════════════════════════════════════════════════════════════════════╝

## 📋 【核心要点 - 紧凑版】
...
"""
    
    # 测试正则表达式
    import re
    image_pattern = r'图片已生成:\s*([^\s\n]+\.png)'
    matches = re.findall(image_pattern, test_response)
    
    if matches:
        print(f"✅ 成功检测到图片路径: {matches[0]}")
        return True
    else:
        print("❌ 图片路径检测失败")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构")
    print("=" * 30)
    
    import os
    
    # 检查charts目录
    if os.path.exists("charts"):
        print("✅ charts目录存在")
        
        # 检查图片文件
        chart_files = [f for f in os.listdir("charts") if f.endswith('.png')]
        if chart_files:
            print(f"✅ 找到{len(chart_files)}个图片文件:")
            for file in chart_files[:3]:  # 只显示前3个
                print(f"  - {file}")
            if len(chart_files) > 3:
                print(f"  ... 还有{len(chart_files)-3}个文件")
        else:
            print("⚠️ charts目录为空")
    else:
        print("❌ charts目录不存在")
        return False
    
    # 检查web_demo目录
    if os.path.exists("web_demo"):
        print("✅ web_demo目录存在")
        
        if os.path.exists("web_demo/prompt_web.py"):
            print("✅ prompt_web.py文件存在")
        else:
            print("❌ prompt_web.py文件不存在")
            return False
    else:
        print("❌ web_demo目录不存在")
        return False
    
    return True

def show_web_usage_guide():
    """显示web端使用指南"""
    print("\n📖 Web端使用指南")
    print("=" * 30)
    
    print("🚀 启动web端:")
    print("  cd web_demo")
    print("  streamlit run prompt_web.py")
    print()
    
    print("🖼️ 图片显示流程:")
    print("  1. 用户输入算命问题")
    print("  2. 系统调用智能API生成分析")
    print("  3. 自动检测响应中的图片路径")
    print("  4. 在web页面显示排盘图片")
    print("  5. 提供图片下载功能")
    print()
    
    print("📊 支持的图片类型:")
    print("  - 紫薇斗数排盘图")
    print("  - 八字命理分析图")
    print("  - 整合双屏排盘图")
    print()
    
    print("💡 用户体验:")
    print("  - 图片自动显示在分析结果上方")
    print("  - 支持图片下载保存")
    print("  - 响应式布局，适配不同屏幕")
    print("  - 错误处理，图片加载失败时显示提示")

def show_technical_details():
    """显示技术细节"""
    print("\n🔧 技术实现细节")
    print("=" * 30)
    
    print("📝 代码修改:")
    print("  1. 添加PIL Image导入")
    print("  2. 新增display_chart_images()函数")
    print("  3. 在AI响应处理中调用图片显示")
    print("  4. 使用正则表达式检测图片路径")
    print("  5. Streamlit st.image()显示图片")
    print("  6. st.download_button()提供下载")
    print()
    
    print("🔍 路径处理:")
    print("  - 支持相对路径和绝对路径")
    print("  - 自动处理web_demo目录的相对路径")
    print("  - 文件存在性检查")
    print("  - 错误处理和用户提示")
    print()
    
    print("🎨 用户界面:")
    print("  - 图片标题: '📊 命盘排盘图'")
    print("  - 图片说明: 显示文件名")
    print("  - 下载按钮: '📥 下载排盘图'")
    print("  - 错误提示: 友好的错误信息")

def main():
    """主测试函数"""
    print("🌐 Web端图片显示功能测试")
    print("=" * 50)
    
    # 测试1: web端修改
    web_success = test_web_modifications()
    
    # 测试2: 图片路径检测
    path_success = test_image_path_detection()
    
    # 测试3: 文件结构
    structure_success = test_file_structure()
    
    # 显示指南
    show_web_usage_guide()
    show_technical_details()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎉 Web端图片显示测试总结:")
    print(f"  Web端修改: {'✅' if web_success else '❌'}")
    print(f"  路径检测: {'✅' if path_success else '❌'}")
    print(f"  文件结构: {'✅' if structure_success else '❌'}")
    
    if all([web_success, path_success, structure_success]):
        print("\n🎊 Web端图片显示功能完成！")
        print("\n📝 现在的功能:")
        print("  1. ✅ 自动检测AI响应中的图片路径")
        print("  2. ✅ 在web页面显示排盘图片")
        print("  3. ✅ 提供图片下载功能")
        print("  4. ✅ 友好的错误处理")
        print("  5. ✅ 响应式图片显示")
        
        print("\n🚀 启动命令:")
        print("  cd web_demo")
        print("  streamlit run prompt_web.py")
        
        print("\n🎯 现在用户可以:")
        print("  - 在web页面看到精美的排盘图")
        print("  - 下载图片保存到本地")
        print("  - 获得完整的视觉体验")
    else:
        print("\n⚠️ 部分功能需要进一步完善")

if __name__ == "__main__":
    main()
