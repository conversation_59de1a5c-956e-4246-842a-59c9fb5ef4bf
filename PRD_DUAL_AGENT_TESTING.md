# 🎯 双Agent系统功能测试PRD v1.0

## 📋 项目概述

### 项目背景
当前智能算命系统采用双Agent架构：前端沟通Agent负责用户交互，后台计算Agent负责算命分析。为确保系统稳定性和用户体验，需要对双Agent系统进行系统性的功能测试和优化。

### 项目目标
- **验证双Agent架构的功能完整性**
- **确保前后端Agent独立工作能力**
- **验证Agent间协作机制的可靠性**
- **识别并修复系统架构问题**
- **建立完整的测试体系和标准**

### 项目范围
- ✅ 前端Agent独立功能测试
- ✅ 后台Agent独立功能测试  
- ✅ 双Agent协作机制测试
- ✅ 真实用户场景测试
- ❌ 性能压力测试（不在此次范围）
- ❌ 安全性测试（不在此次范围）

## 🏗️ 系统架构分析

### 当前架构概览
```
用户请求 → 主控沟通Agent → 信息收集/状态管理 → 调用后台Agent → 详细分析缓存 → 智能问答
```

### Agent职责分工

#### 前端Agent（MasterCustomerAgent）
**核心职责**：
- 用户交互和对话流程控制
- 生辰信息收集和验证
- 意图识别和问题分类
- 会话状态管理
- 基于后台结果的智能问答
- 进度反馈和用户体验

**关键能力**：
- 多轮对话管理
- 智能信息提取
- 上下文记忆
- 错误处理和恢复

#### 后台Agent（FortuneCalculatorAgent）
**核心职责**：
- 真实算法计算（紫薇/八字/六爻）
- 12角度详细分析生成
- 结果缓存和存储管理
- 分析进度跟踪
- 按需角度生成

**关键能力**：
- 算法计算准确性
- 大规模文本生成
- 数据缓存管理
- 渐进式分析

### 通信机制
- **异步消息传递**：AgentMessage/AgentResponse
- **独立线程处理**：后台计算不阻塞前端
- **缓存共享**：CalculationCache统一管理
- **状态同步**：实时进度更新

## 🧪 测试计划详细设计

### 阶段一：前端Agent独立测试

#### 1.1 信息收集能力测试
**测试目标**：验证前端Agent的信息提取和验证能力

**测试用例**：
```python
test_cases = [
    # 完整信息测试
    "我是1988年6月1日午时出生的男命，想算命",
    
    # 无信息测试
    "我想算命",
    
    # 部分信息测试
    "我是男的，1988年出生",
    "6月1日午时出生，想看紫薇",
    
    # 错误信息测试
    "我是1988年13月32日出生的",
    "我是25时出生的",
    
    # 格式变化测试
    "1988.6.1 12:00 男",
    "88年6月1号中午男",
    "戊辰年午月午日午时男命",
]
```

**验收标准**：
- ✅ 完整信息一次性正确提取率 > 95%
- ✅ 部分信息正确合并率 > 90%
- ✅ 错误信息正确识别率 > 95%
- ✅ 信息完整性验证准确率 100%

#### 1.2 意图识别能力测试
**测试目标**：验证前端Agent的用户意图识别准确性

**测试用例**：
```python
intent_cases = [
    # 算命类型识别
    ("我想看紫薇斗数", "fortune_telling", "ziwei"),
    ("帮我算八字", "fortune_telling", "bazi"),
    ("我想占卜一下", "fortune_telling", "liuyao"),
    
    # 问答类型识别
    ("我的财运如何？", "question", "wealth"),
    ("我的命宫有什么特点？", "question", "personality"),
    ("感情方面怎么样？", "question", "relationship"),
    
    # 聊天类型识别
    ("你好", "chat", None),
    ("谢谢", "chat", None),
    ("再见", "chat", None),
]
```

**验收标准**：
- ✅ 算命类型识别准确率 > 95%
- ✅ 问答类型识别准确率 > 90%
- ✅ 聊天类型识别准确率 > 85%
- ✅ 边界情况处理正确率 > 80%

#### 1.3 对话流程控制测试
**测试目标**：验证前端Agent的多轮对话管理能力

**测试场景**：
```python
conversation_scenarios = [
    # 分步信息收集
    ["我想算命", "我是1988年出生的", "6月1日", "午时", "男"],
    
    # 信息修正
    ["我是1988年6月1日出生", "不对，是6月2日", "午时男"],
    
    # 中途提问
    ["我想算命", "我的财运如何？", "我是1988年6月1日午时男"],
    
    # 中途放弃
    ["算命", "我不想说生日", "那算了"],
]
```

**验收标准**：
- ✅ 会话状态正确维护率 100%
- ✅ 上下文记忆准确率 > 95%
- ✅ 流程跳转处理正确率 > 90%
- ✅ 异常恢复成功率 > 85%

### 阶段二：后台Agent独立测试

#### 2.1 算法计算能力测试
**测试目标**：验证后台Agent的算命算法准确性

**测试用例**：
```python
calculation_cases = [
    # 紫薇斗数测试
    {"year": "1988", "month": "6", "day": "1", "hour": "午时", "gender": "男", "type": "ziwei"},
    {"year": "1990", "month": "12", "day": "25", "hour": "子时", "gender": "女", "type": "ziwei"},
    
    # 八字算命测试
    {"year": "1985", "month": "3", "day": "15", "hour": "申时", "gender": "男", "type": "bazi"},
    {"year": "1992", "month": "8", "day": "8", "hour": "戌时", "gender": "女", "type": "bazi"},
    
    # 六爻占卜测试
    {"year": "2000", "month": "1", "day": "1", "hour": "寅时", "gender": "男", "type": "liuyao"},
    
    # 边界情况测试
    {"year": "1900", "month": "1", "day": "1", "hour": "子时", "gender": "男", "type": "ziwei"},
    {"year": "2100", "month": "12", "day": "31", "hour": "亥时", "gender": "女", "type": "bazi"},
]
```

**验收标准**：
- ✅ 算法计算成功率 100%
- ✅ 结果格式正确率 100%
- ✅ 专业术语使用准确率 > 95%
- ✅ 边界数据处理成功率 > 90%

#### 2.2 分析生成能力测试
**测试目标**：验证后台Agent的详细分析生成质量

**测试维度**：
```python
analysis_dimensions = [
    # 字数要求
    "单角度字数 4000-5000字",
    "12角度总字数 48000-60000字",
    
    # 内容质量
    "专业术语使用丰富度",
    "分析逻辑连贯性",
    "建议实用性",
    
    # 生成效率
    "单角度生成时间 < 60秒",
    "12角度完成时间 < 15分钟",
]
```

**验收标准**：
- ✅ 字数达标率 > 95%
- ✅ 专业术语覆盖率 > 90%
- ✅ 内容逻辑连贯性 > 85%
- ✅ 生成成功率 100%

#### 2.3 缓存管理能力测试
**测试目标**：验证后台Agent的数据管理能力

**测试场景**：
```python
cache_scenarios = [
    "结果存储和检索",
    "分角度独立存储",
    "进度实时更新",
    "数据完整性验证",
    "重复请求处理",
    "缓存清理机制",
]
```

**验收标准**：
- ✅ 数据存储成功率 100%
- ✅ 数据检索准确率 100%
- ✅ 进度更新及时性 100%
- ✅ 数据一致性 100%

### 阶段三：双Agent协作测试

#### 3.1 异步通信测试
**测试目标**：验证Agent间消息传递的可靠性

**测试场景**：
```python
communication_scenarios = [
    "正常消息传递",
    "大数据量传递",
    "异常消息处理",
    "超时处理机制",
    "错误传播机制",
]
```

**验收标准**：
- ✅ 消息传递成功率 100%
- ✅ 数据完整性 100%
- ✅ 异常处理正确率 > 95%
- ✅ 超时恢复成功率 > 90%

#### 3.2 状态同步测试
**测试目标**：验证前后端状态同步的准确性

**测试维度**：
```python
sync_dimensions = [
    "前端状态实时更新",
    "后台进度同步",
    "会话隔离性",
    "数据一致性",
    "并发安全性",
]
```

**验收标准**：
- ✅ 状态同步准确率 100%
- ✅ 进度更新及时性 > 95%
- ✅ 会话隔离成功率 100%
- ✅ 并发处理正确率 > 90%

#### 3.3 端到端场景测试
**测试目标**：验证完整用户场景的流畅性

**测试场景**：
```python
e2e_scenarios = [
    # 标准流程
    "用户提供完整信息 → 后台分析 → 用户提问 → 基于结果回答",
    
    # 渐进式流程
    "用户分步提供信息 → 信息收集 → 立即提问 → 部分结果回答",
    
    # 中断恢复流程
    "用户提供信息 → 后台分析中断 → 错误处理 → 重新分析",
    
    # 多用户并发
    "多个用户同时使用 → 独立会话 → 不互相干扰",
]
```

**验收标准**：
- ✅ 标准流程成功率 100%
- ✅ 渐进式体验流畅度 > 95%
- ✅ 错误恢复成功率 > 90%
- ✅ 多用户隔离正确率 100%

## 🛠️ 测试工具设计

### 测试框架架构
```python
# 测试工具类设计
class DualAgentTestSuite:
    def __init__(self):
        self.frontend_tester = FrontendAgentTester()
        self.backend_tester = BackendAgentTester()
        self.collaboration_tester = CollaborationTester()
    
    def run_all_tests(self):
        # 执行完整测试套件
        pass

class FrontendAgentTester:
    def test_info_collection(self)      # 信息收集测试
    def test_intent_recognition(self)   # 意图识别测试
    def test_conversation_flow(self)    # 对话流程测试
    def test_user_experience(self)      # 用户体验测试

class BackendAgentTester:
    def test_calculation_accuracy(self) # 算法准确性测试
    def test_analysis_generation(self)  # 分析生成测试
    def test_cache_management(self)     # 缓存管理测试

class CollaborationTester:
    def test_async_communication(self)  # 异步通信测试
    def test_state_synchronization(self) # 状态同步测试
    def test_end_to_end_scenarios(self) # 端到端场景测试
```

### 测试报告格式
```python
# 测试报告结构
test_report = {
    "test_summary": {
        "total_tests": 100,
        "passed": 95,
        "failed": 5,
        "success_rate": "95%"
    },
    "frontend_tests": {...},
    "backend_tests": {...},
    "collaboration_tests": {...},
    "issues_found": [...],
    "recommendations": [...]
}
```

## 📋 执行计划

### 实施阶段
| 阶段 | 任务 | 输出物 | 验收标准 |
|------|------|--------|----------|
| 1 | 前端Agent独立测试 | 前端功能测试报告 | 所有核心功能测试通过 |
| 2 | 后台Agent独立测试 | 后台功能测试报告 | 算法和缓存功能正常 |
| 3 | 双Agent协作测试 | 协作功能测试报告 | 通信和同步机制可靠 |
| 4 | 问题修复和优化 | 系统优化方案 | 发现问题全部修复 |

### 成功标准
- ✅ **功能完整性**：所有核心功能正常工作
- ✅ **协作可靠性**：Agent间通信稳定可靠
- ✅ **用户体验**：用户使用流程顺畅自然
- ✅ **系统稳定性**：异常情况正确处理

## 🎯 预期成果

### 直接成果
1. **完整的测试体系**：覆盖双Agent系统各个方面
2. **详细的测试报告**：功能、协作、用户体验全方位评估
3. **问题清单和修复方案**：发现的问题和对应解决方案
4. **优化建议**：系统架构和实现的改进建议

### 长期价值
1. **系统可靠性提升**：经过充分测试的稳定系统
2. **开发效率提升**：建立标准化的测试流程
3. **用户体验优化**：基于测试结果的体验改进
4. **技术债务清理**：识别并解决架构问题

---

## 📝 附录

### 风险控制
- **测试环境隔离**：避免影响生产环境
- **数据备份**：测试前备份重要数据
- **回滚机制**：确保可以快速回滚变更

### 资源需求
- **开发时间**：预计3-4天完成全部测试
- **测试环境**：独立的测试环境
- **测试数据**：准备充分的测试用例

---

**🎉 双Agent系统功能测试PRD制定完成！准备开始系统性测试和优化！**
