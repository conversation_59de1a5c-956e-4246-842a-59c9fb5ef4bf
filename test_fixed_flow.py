#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的流程和中断机制
"""

def test_fixed_flow():
    """测试修复后的流程"""
    try:
        from core.fortune_engine import FortuneEngine
        
        print("🔮 测试修复后的算命流程")
        print("=" * 50)
        
        # 创建算命引擎
        engine = FortuneEngine()
        
        # 测试用例：1985年4月23日亥时女命
        user_input = "我是1985年4月23日亥时出生的女性，请帮我算命"
        
        print(f"📝 用户输入: {user_input}")
        print()
        print("🚀 开始处理...")
        print("-" * 30)
        
        # 处理用户请求
        result = engine.process_user_request(user_input)
        
        if result.get("success"):
            print("✅ 处理成功！")
            print()
            
            # 显示解析信息
            parsed_info = result.get("parsed_info", {})
            print("📋 解析信息:")
            print(f"  算命类型: {parsed_info.get('fortune_type', '')}")
            print(f"  问题类型: {parsed_info.get('question_type', '')}")
            print(f"  出生信息: {parsed_info.get('birth_info', {})}")
            print()
            
            # 显示算法结果
            algorithm_result = result.get("algorithm_result", {})
            if algorithm_result.get("success"):
                data = algorithm_result.get("data", {})
                birth_info = data.get("birth_info", {})
                
                print("🧮 算法结果:")
                print(f"  出生信息: {birth_info.get('solar', '')}")
                print(f"  农历: {birth_info.get('lunar', '')}")
                print(f"  八字: {birth_info.get('chinese_date', '')}")
                print(f"  生肖: {data.get('zodiac', '')}")
                print(f"  星座: {data.get('sign', '')}")
                print()
                
                # 显示关键宫位
                palaces = data.get("palaces", {})
                ming_gong = palaces.get("命宫", {})
                print("🏰 关键宫位:")
                print(f"  命宫: {ming_gong.get('position', '')}宫 {ming_gong.get('major_stars', [])}")
                
                for palace_name, palace_info in palaces.items():
                    if palace_info.get("is_body_palace"):
                        print(f"  身宫: {palace_name}({palace_info.get('position', '')}) {palace_info.get('major_stars', [])}")
                        break
                print()
            
            # 显示AI分析结果
            ai_analysis = result.get("ai_analysis", "")
            print("🤖 AI分析结果:")
            print("-" * 20)
            
            # 只显示前500字符，避免输出过长
            if len(ai_analysis) > 500:
                print(ai_analysis[:500] + "...")
                print(f"\n[完整分析共{len(ai_analysis)}字符]")
            else:
                print(ai_analysis)
            
            print()
            print("🎯 流程验证:")
            print("-" * 15)
            
            # 验证流程是否正确
            if "【核心要点 - 紧凑版】" in ai_analysis and "【深度解读 - 详细版】" in ai_analysis:
                print("✅ 双版本输出格式正确")
            else:
                print("❌ 双版本输出格式错误")
            
            if "本分析基于真实的紫薇斗数" in ai_analysis:
                print("✅ 包含真实算法标识")
            else:
                print("❌ 缺少真实算法标识")
            
            # 检查是否有JSON格式错误
            if "{" in ai_analysis and "}" in ai_analysis and '"analysis"' in ai_analysis:
                print("❌ 仍然包含JSON格式输出")
            else:
                print("✅ 没有JSON格式错误")
                
        else:
            print("❌ 处理失败")
            print(f"错误信息: {result.get('message', '未知错误')}")
        
        return result.get("success", False)
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interrupt_mechanism():
    """测试中断机制（模拟）"""
    print("\n🛑 中断机制测试")
    print("=" * 30)
    print("注意：实际中断需要在运行过程中按 Ctrl+C")
    print("这里只是验证中断处理代码是否存在")
    
    try:
        from core.fortune_engine import FortuneEngine
        engine = FortuneEngine()
        
        # 检查是否有中断处理代码
        import inspect
        
        # 检查generate_ai_analysis方法
        source = inspect.getsource(engine.generate_ai_analysis)
        if "KeyboardInterrupt" in source:
            print("✅ generate_ai_analysis 包含中断处理")
        else:
            print("❌ generate_ai_analysis 缺少中断处理")
        
        # 检查_generate_detailed_analysis方法
        source = inspect.getsource(engine._generate_detailed_analysis)
        if "KeyboardInterrupt" in source:
            print("✅ _generate_detailed_analysis 包含中断处理")
        else:
            print("❌ _generate_detailed_analysis 缺少中断处理")
        
        print("✅ 中断机制代码检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 中断机制检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 算命系统修复验证测试")
    print("=" * 60)
    
    # 测试1：修复后的流程
    print("\n【测试1：修复后的流程】")
    flow_success = test_fixed_flow()
    
    # 测试2：中断机制
    print("\n【测试2：中断机制】")
    interrupt_success = test_interrupt_mechanism()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 测试总结:")
    print(f"  流程修复: {'✅ 成功' if flow_success else '❌ 失败'}")
    print(f"  中断机制: {'✅ 成功' if interrupt_success else '❌ 失败'}")
    
    if flow_success and interrupt_success:
        print("\n🎊 所有修复都成功！")
        print("现在的流程是：排盘 → 4角度分析 → 智能合并 → 双版本输出")
        print("用户可以随时按 Ctrl+C 中断分析过程")
    else:
        print("\n⚠️ 部分修复需要进一步调整")

if __name__ == "__main__":
    main()
