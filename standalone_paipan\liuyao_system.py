#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六爻占卜系统 - 完全重新设计
确保排盘的正确性，包括正确的变卦纳甲计算
"""

import random
from datetime import datetime
from typing import List, Dict, Any, Tuple

class LiuyaoSystem:
    """六爻占卜系统"""
    
    def __init__(self):
        """初始化六爻系统"""
        # 八卦基本信息
        self.bagua_info = {
            0: {"name": "坤", "symbol": "☷", "element": "土", "nature": "阴"},
            1: {"name": "震", "symbol": "☳", "element": "木", "nature": "阳"},
            2: {"name": "坎", "symbol": "☵", "element": "水", "nature": "阳"},
            3: {"name": "兑", "symbol": "☱", "element": "金", "nature": "阴"},
            4: {"name": "艮", "symbol": "☶", "element": "土", "nature": "阳"},
            5: {"name": "离", "symbol": "☲", "element": "火", "nature": "阴"},
            6: {"name": "巽", "symbol": "☴", "element": "木", "nature": "阴"},
            7: {"name": "乾", "symbol": "☰", "element": "金", "nature": "阳"}
        }
        
        # 六十四卦名称
        self.hexagram_names = {
            (7, 7): "乾为天", (7, 6): "天泽履", (7, 5): "天火同人", (7, 4): "天山遁",
            (7, 3): "天水讼", (7, 2): "天风小畜", (7, 1): "天雷无妄", (7, 0): "天地否",
            (6, 7): "泽天夬", (6, 6): "兑为泽", (6, 5): "泽火革", (6, 4): "泽山咸",
            (6, 3): "泽水困", (6, 2): "泽风大过", (6, 1): "泽雷随", (6, 0): "泽地萃",
            (5, 7): "火天大有", (5, 6): "火泽睽", (5, 5): "离为火", (5, 4): "火山旅",
            (5, 3): "火水未济", (5, 2): "火风鼎", (5, 1): "火雷噬嗑", (5, 0): "火地晋",
            (4, 7): "山天大畜", (4, 6): "山泽损", (4, 5): "山火贲", (4, 4): "艮为山",
            (4, 3): "山水蒙", (4, 2): "山风蛊", (4, 1): "山雷颐", (4, 0): "山地剥",
            (3, 7): "水天需", (3, 6): "水泽节", (3, 5): "水火既济", (3, 4): "水山蹇",
            (3, 3): "坎为水", (3, 2): "水风井", (3, 1): "水雷屯", (3, 0): "水地比",
            (2, 7): "风天小畜", (2, 6): "风泽中孚", (2, 5): "风火家人", (2, 4): "风山渐",
            (2, 3): "风水涣", (2, 2): "巽为风", (2, 1): "风雷益", (2, 0): "风地观",
            (1, 7): "雷天大壮", (1, 6): "雷泽归妹", (1, 5): "雷火丰", (1, 4): "雷山小过",
            (1, 3): "雷水解", (1, 2): "雷风恒", (1, 1): "震为雷", (1, 0): "雷地豫",
            (0, 7): "地天泰", (0, 6): "地泽临", (0, 5): "地火明夷", (0, 4): "地山谦",
            (0, 3): "地水师", (0, 2): "地风升", (0, 1): "地雷复", (0, 0): "坤为地"
        }
        
        # 传统纳甲地支配置（京房纳甲法）
        self.najia_dizhi = {
            # 乾卦：内卦子寅辰，外卦午申戌
            7: {"inner": ["子", "寅", "辰"], "outer": ["午", "申", "戌"]},
            # 兑卦：内卦巳卯丑，外卦亥酉未  
            6: {"inner": ["巳", "卯", "丑"], "outer": ["亥", "酉", "未"]},
            # 离卦：内卦卯丑亥，外卦酉未巳
            5: {"inner": ["卯", "丑", "亥"], "outer": ["酉", "未", "巳"]},
            # 震卦：内卦子寅辰，外卦午申戌
            4: {"inner": ["子", "寅", "辰"], "outer": ["午", "申", "戌"]},
            # 巽卦：内卦丑亥酉，外卦未巳卯
            3: {"inner": ["丑", "亥", "酉"], "outer": ["未", "巳", "卯"]},
            # 坎卦：内卦寅辰午，外卦申戌子
            2: {"inner": ["寅", "辰", "午"], "outer": ["申", "戌", "子"]},
            # 艮卦：内卦辰午申，外卦戌子寅
            1: {"inner": ["辰", "午", "申"], "outer": ["戌", "子", "寅"]},
            # 坤卦：内卦未巳卯，外卦丑亥酉
            0: {"inner": ["未", "巳", "卯"], "outer": ["丑", "亥", "酉"]}
        }
        
        # 地支五行
        self.dizhi_wuxing = {
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", "巳": "火",
            "午": "火", "未": "土", "申": "金", "酉": "金", "戌": "土", "亥": "水"
        }
        
        # 六神配置（根据日干）
        self.liushen = ["青龙", "朱雀", "勾陈", "腾蛇", "白虎", "玄武"]
        self.liushen_start = {
            "甲": 0, "乙": 0,  # 甲乙日起青龙
            "丙": 1, "丁": 1,  # 丙丁日起朱雀
            "戊": 2,           # 戊日起勾陈
            "己": 3,           # 己日起腾蛇
            "庚": 4, "辛": 4,  # 庚辛日起白虎
            "壬": 5, "癸": 5   # 壬癸日起玄武
        }
        
        # 天干地支
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
    
    def throw_coins(self, manual_results: List[str] = None) -> List[Dict]:
        """
        投掷硬币起卦
        
        Args:
            manual_results: 手动指定的投掷结果，如 ["正正反", "反反反", ...]
            
        Returns:
            投掷结果列表
        """
        coin_throws = []
        
        for i in range(6):
            if manual_results and i < len(manual_results):
                # 使用手动指定的结果
                result = manual_results[i]
                heads_count = result.count('正')
                tails_count = result.count('反')
            else:
                # 随机投掷3枚硬币
                throws = [random.choice(['正', '反']) for _ in range(3)]
                result = ''.join(throws)
                heads_count = result.count('正')
                tails_count = result.count('反')
            
            # 确定爻的类型
            if heads_count == 3:
                yao_type = "老阳"  # 动爻，阳变阴
                yao_symbol = "━━━━━━━━━"
                is_moving = True
                changed_symbol = "━━━　━━━"
            elif heads_count == 2:
                yao_type = "少阴"  # 静爻
                yao_symbol = "━━━　━━━"
                is_moving = False
                changed_symbol = "━━━　━━━"
            elif heads_count == 1:
                yao_type = "少阳"  # 静爻
                yao_symbol = "━━━━━━━━━"
                is_moving = False
                changed_symbol = "━━━━━━━━━"
            else:  # heads_count == 0
                yao_type = "老阴"  # 动爻，阴变阳
                yao_symbol = "━━━　━━━"
                is_moving = True
                changed_symbol = "━━━━━━━━━"
            
            coin_throws.append({
                "throw_number": i + 1,
                "throw_result": result,
                "heads_count": heads_count,
                "tails_count": tails_count,
                "yao_type": yao_type,
                "yao_symbol": yao_symbol,
                "is_moving": is_moving,
                "changed_symbol": changed_symbol
            })
        
        return coin_throws
    
    def calculate_trigram_value(self, yao_list: List[str]) -> int:
        """
        计算三爻卦的数值
        
        Args:
            yao_list: 三个爻的符号列表
            
        Returns:
            三爻卦的数值 (0-7)
        """
        value = 0
        for i, yao in enumerate(yao_list):
            if "━━━━━━━━━" in yao:  # 阳爻
                value += 2 ** i
        return value
    
    def get_current_ganzhi(self) -> Tuple[str, str, str]:
        """
        获取当前日期的干支
        
        Returns:
            (天干, 地支, 干支日)
        """
        # 简化版本，使用当前日期计算
        now = datetime.now()
        
        # 基准日期：2000年1月1日为庚辰日
        base_date = datetime(2000, 1, 1)
        days_diff = (now - base_date).days
        
        # 计算天干地支
        tiangan_index = (days_diff + 6) % 10  # 庚为第6个天干
        dizhi_index = (days_diff + 4) % 12    # 辰为第4个地支
        
        tiangan = self.tiangan[tiangan_index]
        dizhi = self.dizhi[dizhi_index]
        ganzhi_day = tiangan + dizhi
        
        return tiangan, dizhi, ganzhi_day
    
    def calculate_xunkong(self, ganzhi_day: str) -> Tuple[str, str]:
        """
        计算旬空
        
        Args:
            ganzhi_day: 干支日
            
        Returns:
            (旬空地支1, 旬空地支2)
        """
        # 构建60甲子表
        ganzhi_60 = []
        for i in range(60):
            tg = self.tiangan[i % 10]
            dz = self.dizhi[i % 12]
            ganzhi_60.append(tg + dz)
        
        try:
            current_index = ganzhi_60.index(ganzhi_day)
        except ValueError:
            return "戌", "亥"  # 默认值
        
        # 找到当前旬的起始位置
        xun_start = (current_index // 10) * 10
        
        # 每旬的旬空地支
        xunkong_map = {
            0: ("戌", "亥"),   # 甲子旬
            10: ("申", "酉"),  # 甲戌旬
            20: ("午", "未"),  # 甲申旬
            30: ("辰", "巳"),  # 甲午旬
            40: ("寅", "卯"),  # 甲辰旬
            50: ("子", "丑")   # 甲寅旬
        }
        
        return xunkong_map.get(xun_start, ("戌", "亥"))
    
    def calculate_liuqin(self, gua_gong_wuxing: str, yao_wuxing: str) -> str:
        """
        计算六亲关系
        
        Args:
            gua_gong_wuxing: 卦宫五行
            yao_wuxing: 爻的五行
            
        Returns:
            六亲名称
        """
        # 五行生克关系
        if yao_wuxing == gua_gong_wuxing:
            return "兄弟"
        
        # 生我者父母
        if (gua_gong_wuxing == "金" and yao_wuxing == "土") or \
           (gua_gong_wuxing == "水" and yao_wuxing == "金") or \
           (gua_gong_wuxing == "木" and yao_wuxing == "水") or \
           (gua_gong_wuxing == "火" and yao_wuxing == "木") or \
           (gua_gong_wuxing == "土" and yao_wuxing == "火"):
            return "父母"
        
        # 我生者子孙
        if (gua_gong_wuxing == "土" and yao_wuxing == "金") or \
           (gua_gong_wuxing == "金" and yao_wuxing == "水") or \
           (gua_gong_wuxing == "水" and yao_wuxing == "木") or \
           (gua_gong_wuxing == "木" and yao_wuxing == "火") or \
           (gua_gong_wuxing == "火" and yao_wuxing == "土"):
            return "子孙"
        
        # 克我者官鬼
        if (gua_gong_wuxing == "金" and yao_wuxing == "火") or \
           (gua_gong_wuxing == "木" and yao_wuxing == "金") or \
           (gua_gong_wuxing == "土" and yao_wuxing == "木") or \
           (gua_gong_wuxing == "水" and yao_wuxing == "土") or \
           (gua_gong_wuxing == "火" and yao_wuxing == "水"):
            return "官鬼"
        
        # 我克者妻财
        return "妻财"

    def build_hexagram(self, coin_throws: List[Dict]) -> Dict[str, Any]:
        """
        构建完整的卦象信息

        Args:
            coin_throws: 投掷硬币结果

        Returns:
            完整的卦象数据
        """
        # 获取当前日期干支
        day_tiangan, day_dizhi, ganzhi_day = self.get_current_ganzhi()

        # 计算旬空
        xunkong1, xunkong2 = self.calculate_xunkong(ganzhi_day)

        # 构建本卦爻线
        main_yao_lines = []
        changed_yao_lines = []
        moving_lines = []

        for i, throw in enumerate(coin_throws):
            main_yao_lines.append(throw["yao_symbol"])
            changed_yao_lines.append(throw["changed_symbol"])
            if throw["is_moving"]:
                moving_lines.append(i + 1)

        # 计算三爻卦值
        lower_trigram = self.calculate_trigram_value(main_yao_lines[0:3])
        upper_trigram = self.calculate_trigram_value(main_yao_lines[3:6])

        # 获取卦名
        main_hexagram_name = self.hexagram_names.get((upper_trigram, lower_trigram), "未知卦")

        # 计算变卦（如果有动爻）
        changed_hexagram_name = None
        if moving_lines:
            changed_lower_trigram = self.calculate_trigram_value(changed_yao_lines[0:3])
            changed_upper_trigram = self.calculate_trigram_value(changed_yao_lines[3:6])
            changed_hexagram_name = self.hexagram_names.get((changed_upper_trigram, changed_lower_trigram), "未知卦")

        # 获取卦宫信息（以下卦为准）
        lower_trigram_info = self.bagua_info[lower_trigram]
        gua_gong = lower_trigram_info["name"]
        gua_gong_wuxing = lower_trigram_info["element"]

        # 计算本卦纳甲
        main_najia = self.calculate_najia(upper_trigram, lower_trigram, gua_gong_wuxing, day_tiangan)

        # 计算变卦纳甲（如果有动爻）
        changed_najia = None
        if moving_lines:
            # 变卦作为独立卦象，完全重新装卦
            changed_lower_trigram_info = self.bagua_info[changed_lower_trigram]
            changed_gua_gong_wuxing = changed_lower_trigram_info["element"]
            # 变卦的六神也重新配置，使用变卦的天干
            changed_tiangan = self._get_trigram_tiangan(changed_lower_trigram)
            changed_najia = self.calculate_najia(changed_upper_trigram, changed_lower_trigram, changed_gua_gong_wuxing, changed_tiangan)

        return {
            "main_hexagram": {
                "name": main_hexagram_name,
                "upper_trigram": self.bagua_info[upper_trigram]["name"],
                "lower_trigram": self.bagua_info[lower_trigram]["name"],
                "lines": main_yao_lines,
                "najia": main_najia
            },
            "changed_hexagram": {
                "name": changed_hexagram_name,
                "lines": changed_yao_lines,
                "najia": changed_najia
            } if moving_lines else None,
            "moving_lines": moving_lines,
            "gua_gong": gua_gong,
            "gua_gong_wuxing": gua_gong_wuxing,
            "day_ganzhi": ganzhi_day,
            "day_tiangan": day_tiangan,
            "day_dizhi": day_dizhi,
            "xunkong": [xunkong1, xunkong2],
            "coin_throws": coin_throws
        }

    def calculate_najia(self, upper_trigram: int, lower_trigram: int, gua_gong_wuxing: str, liushen_tiangan: str) -> List[Dict]:
        """
        计算纳甲信息

        Args:
            upper_trigram: 上卦数值
            lower_trigram: 下卦数值
            gua_gong_wuxing: 卦宫五行
            liushen_tiangan: 用于配置六神的天干（本卦用日干，变卦用卦干）

        Returns:
            六爻纳甲信息列表
        """
        # 获取地支配置
        lower_dizhi = self.najia_dizhi[lower_trigram]["inner"]
        upper_dizhi = self.najia_dizhi[upper_trigram]["outer"]
        all_dizhi = lower_dizhi + upper_dizhi

        # 计算六神起始位置
        liushen_start_index = self.liushen_start.get(liushen_tiangan, 0)

        najia_info = []
        for i in range(6):
            dizhi = all_dizhi[i]
            wuxing = self.dizhi_wuxing[dizhi]
            liuqin = self.calculate_liuqin(gua_gong_wuxing, wuxing)
            liushen_index = (liushen_start_index + i) % 6
            liushen = self.liushen[liushen_index]

            najia_info.append({
                "position": i + 1,
                "dizhi": dizhi,
                "wuxing": wuxing,
                "liuqin": liuqin,
                "liushen": liushen
            })

        return najia_info

    def _get_trigram_tiangan(self, trigram_value: int) -> str:
        """
        根据三爻卦值获取对应的天干（用于变卦六神配置）

        Args:
            trigram_value: 三爻卦值

        Returns:
            对应的天干
        """
        # 传统纳甲天干配置
        trigram_tiangan_map = {
            7: "甲",  # 乾纳甲
            0: "乙",  # 坤纳乙
            5: "己",  # 离纳己
            2: "戊",  # 坎纳戊
            4: "庚",  # 震纳庚
            3: "辛",  # 巽纳辛
            1: "丙",  # 艮纳丙
            6: "丁"   # 兑纳丁
        }
        return trigram_tiangan_map.get(trigram_value, "甲")

    def format_hexagram_display(self, hexagram_data: Dict[str, Any]) -> str:
        """
        格式化卦象显示

        Args:
            hexagram_data: 卦象数据

        Returns:
            格式化的显示文本
        """
        output = []
        output.append("═" * 50)
        output.append("            六爻占卜结果")
        output.append("═" * 50)
        output.append(f"起卦时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output.append("")

        # 卦象基本信息
        main_hex = hexagram_data["main_hexagram"]
        output.append("【卦象信息】")
        output.append(f"主卦: {main_hex['name']} ({main_hex['upper_trigram']}上{main_hex['lower_trigram']}下)")

        if hexagram_data["changed_hexagram"]:
            changed_hex = hexagram_data["changed_hexagram"]
            output.append(f"变卦: {changed_hex['name']}")
            output.append(f"动爻: 第{', '.join(map(str, hexagram_data['moving_lines']))}爻")

        output.append(f"卦宫: {hexagram_data['gua_gong']}宫 ({hexagram_data['gua_gong_wuxing']})")
        output.append(f"日期: {hexagram_data['day_ganzhi']}日")
        output.append(f"旬空: {', '.join(hexagram_data['xunkong'])}")
        output.append("")

        # 卦象图形
        if hexagram_data["changed_hexagram"]:
            output.append("【卦象图形】")
            output.append("┌─────────────────┬─────────────────┐")
            output.append("│      本卦       │      变卦       │")
            output.append("├─────────────────┼─────────────────┤")

            for i in range(5, -1, -1):  # 从上爻到初爻
                main_line = main_hex["lines"][i]
                changed_line = hexagram_data["changed_hexagram"]["lines"][i]
                is_moving = (i + 1) in hexagram_data["moving_lines"]

                main_display = main_line.replace("━━━━━━━━━", "━━━━━━━━━").replace("━━━　━━━", "━━━　━━━")
                changed_display = changed_line.replace("━━━━━━━━━", "━━━━━━━━━").replace("━━━　━━━", "━━━　━━━")

                if is_moving:
                    main_display += " ○"
                else:
                    main_display += "  "

                output.append(f"│ {main_display} │ {changed_display}   │")

            output.append("└─────────────────┴─────────────────┘")
        else:
            output.append("【卦象图形】")
            output.append("┌─────────────────┐")
            output.append("│      本卦       │")
            output.append("├─────────────────┤")

            for i in range(5, -1, -1):  # 从上爻到初爻
                line = main_hex["lines"][i]
                display = line.replace("━━━━━━━━━", "━━━━━━━━━").replace("━━━　━━━", "━━━　━━━")
                output.append(f"│ {display}   │")

            output.append("└─────────────────┘")

        output.append("")

        # 本卦详细信息
        output.append("【本卦详细】")
        output.append("┌──────┬─────────────┬────┬────┬────┬────┬────┐")
        output.append("│ 爻位 │    卦象     │地支│五行│六亲│六神│动静│")
        output.append("├──────┼─────────────┼────┼────┼────┼────┼────┤")

        for i in range(5, -1, -1):  # 从上爻到初爻
            line_num = i + 1
            line = main_hex["lines"][i]
            najia = main_hex["najia"][i]
            is_moving = line_num in hexagram_data["moving_lines"]

            line_display = line.replace("━━━━━━━━━", "━━━━━━━━━").replace("━━━　━━━", "━━━　━━━")
            dong_jing = "动○" if is_moving else "静"

            output.append(f"│第{line_num}爻│ {line_display} │ {najia['dizhi']} │ {najia['wuxing']} │ {najia['liuqin']} │ {najia['liushen']} │ {dong_jing} │")

        output.append("└──────┴─────────────┴────┴────┴────┴────┴────┘")

        # 变卦详细信息（如果有动爻）
        if hexagram_data["changed_hexagram"]:
            output.append("")
            output.append("【变卦详细】")
            output.append("┌──────┬─────────────┬────┬────┬────┬────┬────┐")
            output.append("│ 爻位 │    卦象     │地支│五行│六亲│六神│动静│")
            output.append("├──────┼─────────────┼────┼────┼────┼────┼────┤")

            changed_hex = hexagram_data["changed_hexagram"]
            for i in range(5, -1, -1):  # 从上爻到初爻
                line_num = i + 1
                line = changed_hex["lines"][i]
                najia = changed_hex["najia"][i]

                line_display = line.replace("━━━━━━━━━", "━━━━━━━━━").replace("━━━　━━━", "━━━　━━━")

                output.append(f"│第{line_num}爻│ {line_display} │ {najia['dizhi']} │ {najia['wuxing']} │ {najia['liuqin']} │ {najia['liushen']} │ 静 │")

            output.append("└──────┴─────────────┴────┴────┴────┴────┴────┘")

        # 硬币投掷详情
        output.append("")
        output.append("【硬币投掷详情】")
        for throw in hexagram_data["coin_throws"]:
            output.append(f"第{throw['throw_number']}次: {throw['throw_result']} → {throw['yao_type']}")

        output.append("")
        output.append("═" * 50)

        return "\n".join(output)

    def divine_with_coins(self, question: str, manual_results: List[str] = None) -> Dict[str, Any]:
        """
        完整的硬币起卦流程

        Args:
            question: 占卜问题
            manual_results: 手动指定的硬币结果

        Returns:
            完整的占卜结果
        """
        try:
            # 投掷硬币
            coin_throws = self.throw_coins(manual_results)

            # 构建卦象
            hexagram_data = self.build_hexagram(coin_throws)

            # 格式化显示
            formatted_output = self.format_hexagram_display(hexagram_data)

            return {
                "success": True,
                "method": "硬币起卦",
                "question": question,
                "hexagram_data": hexagram_data,
                "formatted_output": formatted_output,
                "divination_type": "六爻占卜"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "method": "硬币起卦",
                "question": question
            }
