#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的双Agent系统
目标：验证主Agent持续聊天 + 后台Agent详细分析缓存
"""

import asyncio
import sys
import time
from datetime import datetime
sys.path.append('.')

async def test_enhanced_dual_agent():
    """测试增强的双Agent系统"""
    print("🚀 测试增强的双Agent系统")
    print("=" * 80)
    print("目标1: 主Agent持续聊天互动，收集必要信息")
    print("目标2: 后台Agent精准算法，详细分析，结果缓存")
    print("=" * 80)
    
    try:
        # 导入增强的组件
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        from core.storage.calculation_cache import CalculationCache
        
        # 创建系统
        master_agent = MasterCustomerAgent("enhanced_master")
        calculator_agent = FortuneCalculatorAgent("enhanced_calc")
        coordinator = SimpleCoordinator()
        cache = CalculationCache()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print("✅ 增强双Agent系统初始化完成")
        print(f"   主控沟通Agent: {master_agent.agent_id}")
        print(f"   后台计算Agent: {calculator_agent.agent_id}")
        print(f"   缓存管理器: 已启用")
        
        # 测试完整对话流程
        session_id = "enhanced_test_session"
        
        print(f"\n🎭 开始测试完整对话流程")
        print("=" * 60)
        
        # 阶段1: 初始问候
        print(f"\n1️⃣ 阶段1: 初始问候")
        print("-" * 40)
        
        result1 = await coordinator.handle_user_message(session_id, "你好，我想算命")
        print(f"👤 用户: 你好，我想算命")
        print(f"🤖 AI: {result1.get('response', '')[:150]}...")
        print(f"📊 阶段: {result1.get('stage', 'unknown')}")
        
        # 阶段2: 逐步提供信息
        print(f"\n2️⃣ 阶段2: 逐步提供信息")
        print("-" * 40)
        
        result2 = await coordinator.handle_user_message(session_id, "我是1990年出生的")
        print(f"👤 用户: 我是1990年出生的")
        print(f"🤖 AI: {result2.get('response', '')[:150]}...")
        print(f"📊 阶段: {result2.get('stage', 'unknown')}")
        
        # 阶段3: 继续补充信息
        print(f"\n3️⃣ 阶段3: 继续补充信息")
        print("-" * 40)
        
        result3 = await coordinator.handle_user_message(session_id, "5月15日下午3点，女性")
        print(f"👤 用户: 5月15日下午3点，女性")
        print(f"🤖 AI: {result3.get('response', '')[:200]}...")
        print(f"📊 阶段: {result3.get('stage', 'unknown')}")
        
        # 检查是否触发了后台计算
        if "分析已完成" in result3.get('response', ''):
            print("✅ 后台Agent被成功调用！")
            
            # 阶段4: 基于结果的问答
            print(f"\n4️⃣ 阶段4: 基于结果的问答")
            print("-" * 40)
            
            questions = [
                "我的事业运势怎么样？",
                "感情方面有什么建议？",
                "今年财运如何？",
                "健康方面需要注意什么？"
            ]
            
            for i, question in enumerate(questions, 1):
                print(f"\n4.{i} 问题: {question}")
                result = await coordinator.handle_user_message(session_id, question)
                print(f"🤖 AI: {result.get('response', '')[:200]}...")
                
                # 短暂等待
                await asyncio.sleep(0.5)
            
            # 阶段5: 继续聊天
            print(f"\n5️⃣ 阶段5: 继续自然聊天")
            print("-" * 40)
            
            chat_messages = [
                "谢谢你的分析",
                "我还想了解一下学业运势",
                "有什么开运的方法吗？"
            ]
            
            for i, message in enumerate(chat_messages, 1):
                print(f"\n5.{i} 聊天: {message}")
                result = await coordinator.handle_user_message(session_id, message)
                print(f"🤖 AI: {result.get('response', '')[:150]}...")
                
                await asyncio.sleep(0.5)
        
        # 检查缓存状态
        print(f"\n📊 检查缓存状态")
        print("-" * 40)
        
        cache_stats = cache.get_cache_stats()
        print(f"缓存统计: {cache_stats}")
        
        # 测试缓存查询
        if cache_stats['total_results'] > 0:
            print("✅ 发现缓存结果，测试查询功能")
            
            # 获取结果ID
            results = cache.search_results(session_id=session_id)
            if results:
                result_id = results[0]
                print(f"   结果ID: {result_id}")
                
                # 测试获取简要总结
                summary = cache.get_summary(result_id)
                print(f"   简要总结: {summary[:100] if summary else 'None'}...")
                
                # 测试获取详细分析
                detailed = cache.get_detailed_analysis(result_id)
                print(f"   详细分析: {'已缓存' if detailed else '未找到'}")
        
        # 性能统计
        print(f"\n📈 性能统计")
        print("-" * 40)
        
        coord_stats = coordinator.get_stats()
        master_stats = master_agent.get_stats()
        calc_stats = calculator_agent.get_stats()
        
        print(f"协调器:")
        print(f"  总请求: {coord_stats['total_requests']}")
        print(f"  成功率: {coord_stats.get('success_rate', 0)*100:.1f}%")
        print(f"  平均响应: {coord_stats['average_response_time']:.2f}秒")
        
        print(f"主控Agent:")
        print(f"  处理消息: {master_stats['messages_processed']}")
        print(f"  平均耗时: {master_stats['average_processing_time']:.2f}秒")
        
        print(f"后台Agent:")
        print(f"  处理消息: {calc_stats['messages_processed']}")
        print(f"  平均耗时: {calc_stats['average_processing_time']:.2f}秒")
        
        # 评估结果
        print(f"\n🎯 系统评估")
        print("=" * 60)
        
        success_indicators = []
        
        # 检查1: 主Agent持续聊天
        if coord_stats['total_requests'] >= 5:
            success_indicators.append("✅ 主Agent持续聊天互动正常")
        else:
            success_indicators.append("❌ 主Agent聊天互动不足")
        
        # 检查2: 后台Agent被调用
        if calc_stats['messages_processed'] > 0:
            success_indicators.append("✅ 后台Agent被成功调用")
        else:
            success_indicators.append("❌ 后台Agent未被调用")
        
        # 检查3: 结果缓存
        if cache_stats['total_results'] > 0:
            success_indicators.append("✅ 结果成功缓存")
        else:
            success_indicators.append("❌ 结果缓存失败")
        
        # 检查4: 系统稳定性
        if coord_stats.get('success_rate', 0) >= 0.8:
            success_indicators.append("✅ 系统稳定性良好")
        else:
            success_indicators.append("❌ 系统稳定性需要改进")
        
        for indicator in success_indicators:
            print(f"   {indicator}")
        
        success_count = sum(1 for indicator in success_indicators if "✅" in indicator)
        total_count = len(success_indicators)
        
        print(f"\n📊 总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count >= total_count * 0.75:
            print(f"\n🎉 增强双Agent系统测试成功！")
            print(f"✅ 主Agent持续聊天互动能力正常")
            print(f"✅ 后台Agent精准算法和缓存机制正常")
            print(f"✅ 系统架构满足设计目标")
            return True
        else:
            print(f"\n⚠️  增强双Agent系统需要优化")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧪 增强双Agent系统完整测试")
    print("=" * 80)
    print("验证目标:")
    print("1. 主Agent持续聊天互动，收集必要信息")
    print("2. 后台Agent精准算法，详细分析，结果缓存")
    print("3. 智能查询缓存，避免资源浪费")
    print("=" * 80)
    
    # 执行测试
    success = await test_enhanced_dual_agent()
    
    # 最终结论
    print(f"\n" + "=" * 80)
    print("🏁 最终测试结论")
    print("=" * 80)
    
    if success:
        print("🎉 增强双Agent系统开发成功！")
        print("\n💪 实现的核心功能:")
        print("   ✅ 主Agent持续聊天互动")
        print("   ✅ 智能信息收集和状态管理")
        print("   ✅ 后台Agent按需调用")
        print("   ✅ 详细分析和结果缓存")
        print("   ✅ 智能查询和资源优化")
        
        print("\n🌟 技术亮点:")
        print("   🗣️ 主Agent专注用户体验")
        print("   🧮 后台Agent专注算法精度")
        print("   💾 智能缓存避免重复计算")
        print("   🔍 按需查询详细分析")
        print("   ⚡ 资源优化和性能提升")
        
        print("\n🚀 现在可以在Web界面体验完整功能：")
        print("   访问: http://localhost:8504")
        
        return True
    else:
        print("💥 增强双Agent系统存在问题")
        print("需要进一步调试和优化")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
