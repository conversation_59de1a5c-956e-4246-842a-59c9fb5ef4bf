#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双Agent架构API服务
基于您设计的前后端独立架构
"""

import logging
import json
import uuid
import time
from datetime import datetime
from typing import Dict, Any
from flask import Flask, request, jsonify, Response
from flask_cors import CORS

# 导入双Agent架构
from core.agents.master_customer_agent import MasterCustomerAgent
from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
from core.agents.simple_coordinator import SimpleCoordinator
from core.agents.base_agent import agent_registry

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DualAgentAPI:
    """双Agent架构API服务"""

    def __init__(self, api_key: str):
        """初始化API服务"""
        self.app = Flask(__name__)
        CORS(self.app)  # 支持跨域

        # 清除Agent注册
        agent_registry.agents.clear()

        # 创建双Agent系统
        self.master_agent = MasterCustomerAgent("api_master")
        self.calculator_agent = FortuneCalculatorAgent("api_calc")
        self.coordinator = SimpleCoordinator()

        # 注册Agent
        agent_registry.register_agent(self.master_agent)
        agent_registry.register_agent(self.calculator_agent)

        # 注册路由
        self._register_routes()

        logger.info("双Agent API服务初始化完成")

    def _register_routes(self):
        """注册API路由"""

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "dual_agent_v1.0",
                "architecture": "前后端独立双Agent"
            })

        @self.app.route('/chat', methods=['POST'])
        def chat():
            """聊天接口"""
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._handle_chat())
            finally:
                loop.close()

        @self.app.route('/session/<session_id>/progress', methods=['GET'])
        def get_progress(session_id):
            """获取分析进度"""
            return self._handle_get_progress(session_id)

        @self.app.route('/session/<session_id>/status', methods=['GET'])
        def get_status(session_id):
            """获取会话状态"""
            return self._handle_get_status(session_id)

        @self.app.route('/stats', methods=['GET'])
        def get_stats():
            """获取系统统计"""
            return self._handle_get_stats()

    async def _handle_chat(self) -> Response:
        """处理聊天请求"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "无效的请求数据"}), 400

            # 提取参数
            message = data.get("message", "").strip()
            session_id = data.get("session_id") or str(uuid.uuid4())

            if not message:
                return jsonify({"error": "消息不能为空"}), 400

            logger.info(f"收到聊天请求: session={session_id[:8]}, message={message[:50]}...")

            # 调用双Agent系统
            start_time = time.time()
            result = await self.coordinator.handle_user_message(session_id, message)
            processing_time = time.time() - start_time

            if result.get('success'):
                response_data = {
                    "success": True,
                    "session_id": session_id,
                    "message": result.get('response', ''),
                    "processing_time": round(processing_time, 2),
                    "timestamp": datetime.now().isoformat()
                }

                # 添加分析进度信息
                session_state = self.master_agent.get_session_state(session_id)
                if session_state and session_state.get("result_id"):
                    progress = self.master_agent.get_analysis_progress(session_state["result_id"])
                    response_data["analysis_progress"] = progress

                logger.info(f"聊天响应成功: {processing_time:.2f}s")
                return jsonify(response_data)
            else:
                logger.error(f"聊天处理失败: {result.get('error')}")
                return jsonify({
                    "success": False,
                    "error": result.get('error', '处理失败'),
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat()
                }), 500

        except Exception as e:
            logger.error(f"聊天请求异常: {e}")
            return jsonify({
                "success": False,
                "error": str(e),
                "message": "服务器内部错误"
            }), 500

    def _handle_get_progress(self, session_id: str) -> Response:
        """获取分析进度"""
        try:
            session_state = self.master_agent.get_session_state(session_id)
            if not session_state:
                return jsonify({"error": "会话不存在"}), 404

            result_id = session_state.get("result_id")
            if not result_id:
                return jsonify({
                    "session_id": session_id,
                    "analysis_started": False,
                    "message": "分析尚未开始"
                })

            progress = self.master_agent.get_analysis_progress(result_id)

            return jsonify({
                "session_id": session_id,
                "analysis_started": True,
                "progress": progress,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"获取进度失败: {e}")
            return jsonify({"error": str(e)}), 500

    def _handle_get_status(self, session_id: str) -> Response:
        """获取会话状态"""
        try:
            session_state = self.master_agent.get_session_state(session_id)
            if not session_state:
                return jsonify({"error": "会话不存在"}), 404

            status_data = {
                "session_id": session_id,
                "stage": session_state.get("stage", "unknown"),
                "birth_info_complete": session_state.get("birth_info_complete", False),
                "calculation_type": session_state.get("calculation_type"),
                "result_id": session_state.get("result_id"),
                "timestamp": datetime.now().isoformat()
            }

            # 如果有结果ID，添加分析进度
            if session_state.get("result_id"):
                progress = self.master_agent.get_analysis_progress(session_state["result_id"])
                status_data["analysis_progress"] = progress

            return jsonify(status_data)

        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return jsonify({"error": str(e)}), 500

    def _handle_get_stats(self) -> Response:
        """获取系统统计"""
        try:
            coordinator_stats = self.coordinator.get_stats()

            stats_data = {
                "coordinator_stats": coordinator_stats,
                "agents": {
                    "master_agent": self.master_agent.agent_id,
                    "calculator_agent": self.calculator_agent.agent_id
                },
                "architecture": "双Agent独立架构",
                "timestamp": datetime.now().isoformat()
            }

            return jsonify(stats_data)

        except Exception as e:
            logger.error(f"获取统计失败: {e}")
            return jsonify({"error": str(e)}), 500

    def run(self, host: str = "0.0.0.0", port: int = 8003, debug: bool = False):
        """启动API服务"""
        logger.info(f"启动双Agent API服务 - {host}:{port}")
        logger.info("架构: 前端Agent + 后端Agent 独立协作")
        self.app.run(host=host, port=port, debug=debug, threaded=True)

# 异步支持
import asyncio
from functools import wraps

def async_route(f):
    """异步路由装饰器"""
    @wraps(f)
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(f(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

# 修改聊天处理为异步
def create_async_api(api_key: str) -> DualAgentAPI:
    """创建异步API"""
    api = DualAgentAPI(api_key)

    # 重新装饰聊天路由为异步
    original_handle_chat = api._handle_chat
    api._handle_chat = async_route(original_handle_chat)

    return api

if __name__ == "__main__":
    # 使用您的API密钥
    API_KEY = "sk-trklwkxjmgnrgbuxhwcanaxkzwtuqevslzhoikwgwajnkqjz"

    # 创建并启动API
    api = create_async_api(API_KEY)

    print("🚀 双Agent API服务启动")
    print("=" * 50)
    print("架构: 前端Agent + 后端Agent 独立协作")
    print("端口: http://localhost:8003")
    print("=" * 50)
    print("API接口:")
    print("  POST /chat - 聊天接口")
    print("  GET  /session/{id}/progress - 获取分析进度")
    print("  GET  /session/{id}/status - 获取会话状态")
    print("  GET  /health - 健康检查")
    print("  GET  /stats - 系统统计")
    print("=" * 50)

    api.run(port=8003, debug=True)
