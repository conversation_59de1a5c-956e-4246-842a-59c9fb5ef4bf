#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的系统
"""

import asyncio

async def test_fixed_system():
    """测试修复后的系统"""
    print("🧪 测试修复后的系统")
    print("=" * 50)
    
    try:
        # 1. 测试一致性管理器
        print("1️⃣ 测试一致性管理器")
        from core.analysis.consistency_manager import ConsistencyManager
        
        consistency_manager = ConsistencyManager()
        
        # 模拟分析数据
        mock_analyses = {
            "personality_destiny": "命宫分析内容...",
            "wealth_fortune": "财富分析内容...",
            "marriage_love": "婚姻分析内容..."
        }
        
        consistency_report = consistency_manager.check_consistency(mock_analyses)
        print(f"✅ 一致性检查成功: {consistency_report.get('consistency_score', 0):.1f}")
        
        # 测试整合提示词
        integration_prompt = consistency_manager.generate_integration_prompt(
            mock_analyses, consistency_report
        )
        print(f"✅ 整合提示词生成成功: {len(integration_prompt)}字符")
        
        # 2. 测试新分析系统
        print("\n2️⃣ 测试新分析系统")
        from core.analysis.analysis_controller import AnalysisController
        
        controller = AnalysisController()
        print("✅ 分析控制器初始化成功")
        
        # 3. 测试融合引擎
        print("\n3️⃣ 测试融合引擎")
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        print("✅ 融合引擎初始化成功")
        
        # 4. 测试简单计算
        print("\n4️⃣ 测试简单计算")
        result = engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if result.get("success"):
            print("✅ 融合分析计算成功")
            
            # 检查数据结构
            ziwei_data = result.get("ziwei_analysis", {})
            if "palaces" in ziwei_data:
                palaces = ziwei_data["palaces"]
                print(f"✅ 宫位数据正常: {len(palaces)}个宫位")
                
                if "命宫" in palaces:
                    mingong = palaces["命宫"]
                    position = mingong.get("position", "")
                    major_stars = mingong.get("major_stars", [])
                    print(f"✅ 命宫数据: {position}宫, 主星{major_stars}")
                else:
                    print("❌ 缺少命宫数据")
            else:
                print("❌ 缺少宫位数据")
        else:
            print(f"❌ 融合分析失败: {result.get('error')}")
        
        print("\n🎯 系统状态总结:")
        print("✅ 一致性管理器: 正常")
        print("✅ 新分析系统: 正常")
        print("✅ 融合引擎: 正常")
        print("✅ Web服务: 运行中 (http://localhost:8501)")
        
        print("\n💡 建议:")
        print("1. 访问 http://localhost:8501 测试Web界面")
        print("2. 清理缓存后重新进行分析")
        print("3. 验证新系统的数据准确性")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_fixed_system())
