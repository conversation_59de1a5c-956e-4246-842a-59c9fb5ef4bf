#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算命结果缓存管理器
负责保存、查询、管理后台Agent的详细计算结果
"""

import json
import logging
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CalculationResult:
    """算命计算结果"""
    result_id: str
    user_id: str
    session_id: str
    calculation_type: str  # ziwei, bazi, liuyao
    birth_info: Dict[str, Any]
    raw_calculation: Dict[str, Any]  # 原始算法结果
    detailed_analysis: Dict[str, Any]  # 详细分析
    summary: str  # 简要总结，供主Agent快速调用
    keywords: List[str]  # 关键词，便于查询
    confidence: float
    created_at: str
    updated_at: str
    chart_image_path: Optional[str] = None  # 🔧 修复：排盘图片路径
    access_count: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CalculationResult':
        """从字典创建"""
        return cls(**data)

class CalculationCache:
    """算命结果缓存管理器"""

    def __init__(self, cache_dir: str = "data/calculation_cache"):
        """初始化缓存管理器"""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 内存缓存
        self.memory_cache: Dict[str, CalculationResult] = {}
        self.max_memory_cache = 100

        # 索引文件
        self.index_file = self.cache_dir / "index.json"
        self.index = self._load_index()

        logger.info(f"算命结果缓存管理器初始化完成 - 缓存目录: {self.cache_dir}")

    def _load_index(self) -> Dict[str, Any]:
        """加载索引"""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载索引失败: {e}")

        return {
            "results": {},  # result_id -> metadata
            "user_results": {},  # user_id -> [result_ids]
            "session_results": {},  # session_id -> [result_ids]
            "type_results": {}  # calculation_type -> [result_ids]
        }

    def _save_index(self):
        """保存索引"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存索引失败: {e}")

    def _generate_result_id(self, birth_info: Dict[str, Any], calculation_type: str) -> str:
        """生成结果ID"""
        # 基于生辰信息和计算类型生成唯一ID
        key_data = {
            "birth_info": birth_info,
            "calculation_type": calculation_type
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()

    def save_result(self, user_id: str, session_id: str, calculation_type: str,
                   birth_info: Dict[str, Any], raw_calculation: Dict[str, Any],
                   detailed_analysis: Dict[str, Any], summary: str,
                   keywords: List[str], confidence: float,
                   chart_image_path: Optional[str] = None) -> str:
        """保存计算结果"""
        try:
            # 生成结果ID
            result_id = self._generate_result_id(birth_info, calculation_type)

            # 创建结果对象
            result = CalculationResult(
                result_id=result_id,
                user_id=user_id,
                session_id=session_id,
                calculation_type=calculation_type,
                birth_info=birth_info,
                raw_calculation=raw_calculation,
                detailed_analysis=detailed_analysis,
                summary=summary,
                keywords=keywords,
                confidence=confidence,
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat(),
                chart_image_path=chart_image_path  # 🔧 修复：保存图片路径
            )

            # 保存到文件
            result_file = self.cache_dir / f"{result_id}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, ensure_ascii=False, indent=2)

            # 更新索引
            self._update_index(result)

            # 添加到内存缓存
            self.memory_cache[result_id] = result
            self._cleanup_memory_cache()

            logger.info(f"算命结果已保存: {result_id}")
            return result_id

        except Exception as e:
            logger.error(f"保存算命结果失败: {e}")
            raise

    def _update_index(self, result: CalculationResult):
        """更新索引"""
        result_id = result.result_id

        # 更新结果元数据
        self.index["results"][result_id] = {
            "user_id": result.user_id,
            "session_id": result.session_id,
            "calculation_type": result.calculation_type,
            "created_at": result.created_at,
            "keywords": result.keywords,
            "confidence": result.confidence
        }

        # 更新用户索引
        if result.user_id not in self.index["user_results"]:
            self.index["user_results"][result.user_id] = []
        if result_id not in self.index["user_results"][result.user_id]:
            self.index["user_results"][result.user_id].append(result_id)

        # 更新会话索引
        if result.session_id not in self.index["session_results"]:
            self.index["session_results"][result.session_id] = []
        if result_id not in self.index["session_results"][result.session_id]:
            self.index["session_results"][result.session_id].append(result_id)

        # 更新类型索引
        if result.calculation_type not in self.index["type_results"]:
            self.index["type_results"][result.calculation_type] = []
        if result_id not in self.index["type_results"][result.calculation_type]:
            self.index["type_results"][result.calculation_type].append(result_id)

        # 保存索引
        self._save_index()

    def get_result(self, result_id: str) -> Optional[CalculationResult]:
        """获取计算结果"""
        try:
            # 先检查内存缓存
            if result_id in self.memory_cache:
                result = self.memory_cache[result_id]
                result.access_count += 1
                return result

            # 从文件加载
            result_file = self.cache_dir / f"{result_id}.json"
            if result_file.exists():
                with open(result_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                result = CalculationResult.from_dict(data)
                result.access_count += 1

                # 添加到内存缓存
                self.memory_cache[result_id] = result
                self._cleanup_memory_cache()

                return result

            return None

        except Exception as e:
            logger.error(f"获取算命结果失败: {e}")
            return None

    def get_summary(self, result_id: str) -> Optional[str]:
        """获取结果简要总结"""
        result = self.get_result(result_id)
        return result.summary if result else None

    def get_detailed_analysis(self, result_id: str) -> Optional[Dict[str, Any]]:
        """获取详细分析"""
        result = self.get_result(result_id)
        return result.detailed_analysis if result else None

    def search_results(self, user_id: Optional[str] = None,
                      session_id: Optional[str] = None,
                      calculation_type: Optional[str] = None,
                      keywords: Optional[List[str]] = None) -> List[str]:
        """搜索结果"""
        result_ids = set()

        # 按用户搜索
        if user_id and user_id in self.index["user_results"]:
            result_ids.update(self.index["user_results"][user_id])

        # 按会话搜索
        if session_id and session_id in self.index["session_results"]:
            if result_ids:
                result_ids &= set(self.index["session_results"][session_id])
            else:
                result_ids.update(self.index["session_results"][session_id])

        # 按类型搜索
        if calculation_type and calculation_type in self.index["type_results"]:
            if result_ids:
                result_ids &= set(self.index["type_results"][calculation_type])
            else:
                result_ids.update(self.index["type_results"][calculation_type])

        # 按关键词搜索
        if keywords:
            keyword_results = set()
            for result_id, metadata in self.index["results"].items():
                result_keywords = metadata.get("keywords", [])
                if any(kw in result_keywords for kw in keywords):
                    keyword_results.add(result_id)

            if result_ids:
                result_ids &= keyword_results
            else:
                result_ids.update(keyword_results)

        # 如果没有任何条件，返回所有结果
        if not any([user_id, session_id, calculation_type, keywords]):
            result_ids.update(self.index["results"].keys())

        return list(result_ids)

    def _cleanup_memory_cache(self):
        """清理内存缓存"""
        if len(self.memory_cache) > self.max_memory_cache:
            # 按访问次数排序，移除最少访问的
            sorted_items = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].access_count
            )

            # 移除最少访问的一半
            remove_count = len(self.memory_cache) - self.max_memory_cache // 2
            for i in range(remove_count):
                result_id = sorted_items[i][0]
                del self.memory_cache[result_id]

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "total_results": len(self.index["results"]),
            "memory_cache_size": len(self.memory_cache),
            "users_count": len(self.index["user_results"]),
            "sessions_count": len(self.index["session_results"]),
            "calculation_types": list(self.index["type_results"].keys())
        }
