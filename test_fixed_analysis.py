#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的分析系统
验证八字数据在LLM分析中的准确性
"""

import asyncio

async def test_fixed_analysis():
    """测试修复后的分析系统"""
    print("🧪 测试修复后的分析系统")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("test_calc")
        
        # 测试生辰信息
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"📅 测试生辰: {birth_info['year']}年{birth_info['month']}月{birth_info['day']}日{birth_info['hour']}时 {birth_info['gender']}")
        
        # 执行单角度分析
        print(f"\n🎯 执行命宫分析...")
        
        # 先获取融合分析数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=birth_info["year"],
            month=birth_info["month"],
            day=birth_info["day"],
            hour=birth_info["hour"],
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            print("✅ 融合分析数据获取成功")
            
            # 执行单角度分析
            analysis_result = await calculator_agent._analyze_single_angle(
                "命宫分析", "personality_destiny", "性格命运核心特征与天赋潜能",
                raw_data, birth_info, "紫薇+八字融合分析"
            )
            
            if analysis_result:
                print(f"✅ 分析完成，字数: {len(analysis_result)}")
                
                # 检查分析内容中的八字信息
                if "戊辰 丁巳 丁亥 丙午" in analysis_result:
                    print("✅ 分析内容包含正确八字")
                elif "戊午" in analysis_result:
                    print("❌ 分析内容包含错误八字")
                else:
                    print("⚠️ 分析内容中未明确提及八字")
                
                # 检查五行分析
                if "火旺" in analysis_result or "火" in analysis_result:
                    print("✅ 分析内容包含五行信息")
                else:
                    print("⚠️ 分析内容中未明确提及五行")
                
                # 显示分析内容的前500字
                print(f"\n📝 分析内容预览:")
                print("-" * 50)
                print(analysis_result[:500] + "..." if len(analysis_result) > 500 else analysis_result)
                print("-" * 50)
                
                return True
            else:
                print("❌ 分析失败")
                return False
        else:
            print(f"❌ 融合分析失败: {raw_data.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_web_interface():
    """测试Web界面的分析"""
    print("\n🌐 测试Web界面分析")
    print("=" * 40)
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 创建系统
        master_agent = MasterCustomerAgent("test_master")
        calculator_agent = FortuneCalculatorAgent("test_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = "test_session"
        
        # 模拟用户请求
        user_message = "我想看紫薇斗数，1988年6月1日午时出生，男性"
        print(f"👤 用户: {user_message}")
        
        # 处理请求
        response = await coordinator.process_message(
            user_message=user_message,
            session_id=session_id,
            user_id="test_user"
        )
        
        if response.get("success"):
            print("✅ Web界面处理成功")
            
            # 检查响应内容
            content = response.get("content", "")
            if "戊辰 丁巳 丁亥 丙午" in content:
                print("✅ 响应包含正确八字")
            elif "戊午" in content:
                print("❌ 响应包含错误八字")
            else:
                print("⚠️ 响应中未明确提及八字")
            
            print(f"📝 响应内容预览:")
            print("-" * 50)
            print(content[:300] + "..." if len(content) > 300 else content)
            print("-" * 50)
            
            return True
        else:
            print(f"❌ Web界面处理失败: {response.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧪 修复后分析系统完整测试")
    print("=" * 70)
    
    # 1. 测试单角度分析
    success1 = await test_fixed_analysis()
    
    # 2. 测试Web界面
    success2 = await test_web_interface()
    
    print("\n" + "=" * 70)
    print("🎯 测试结果总结:")
    
    if success1:
        print("✅ 单角度分析系统正常")
    else:
        print("❌ 单角度分析系统异常")
    
    if success2:
        print("✅ Web界面分析系统正常")
    else:
        print("❌ Web界面分析系统异常")
    
    if success1 and success2:
        print("\n🎉 修复成功！八字数据现在能正确传递给LLM分析了！")
    else:
        print("\n⚠️ 还有问题需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
