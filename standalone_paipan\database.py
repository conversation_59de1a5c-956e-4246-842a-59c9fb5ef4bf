#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地SQLite数据库管理模块
用于存储和管理排盘数据
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

class PaipanDatabase:
    """排盘数据库管理类"""

    def __init__(self, db_path: str = "paipan_data.db"):
        """
        初始化数据库

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建排盘记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS paipan_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        birth_year INTEGER NOT NULL,
                        birth_month INTEGER NOT NULL,
                        birth_day INTEGER NOT NULL,
                        birth_hour INTEGER NOT NULL,
                        gender TEXT NOT NULL,
                        birth_datetime TEXT NOT NULL,
                        solar_date TEXT,
                        lunar_date TEXT,
                        zodiac TEXT,
                        constellation TEXT,
                        calculation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        success BOOLEAN NOT NULL,
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建紫薇斗数数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ziwei_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        record_id INTEGER NOT NULL,
                        palace_name TEXT NOT NULL,
                        position TEXT,
                        major_stars TEXT,  -- JSON格式存储
                        minor_stars TEXT,  -- JSON格式存储
                        adjective_stars TEXT,  -- JSON格式存储
                        transformations TEXT,  -- JSON格式存储
                        is_body_palace BOOLEAN DEFAULT FALSE,
                        palace_attributes TEXT,
                        strength_analysis TEXT,
                        FOREIGN KEY (record_id) REFERENCES paipan_records (id)
                    )
                ''')

                # 创建八字数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bazi_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        record_id INTEGER NOT NULL,
                        year_pillar_gan TEXT,
                        year_pillar_zhi TEXT,
                        month_pillar_gan TEXT,
                        month_pillar_zhi TEXT,
                        day_pillar_gan TEXT,
                        day_pillar_zhi TEXT,
                        hour_pillar_gan TEXT,
                        hour_pillar_zhi TEXT,
                        day_master_gan TEXT,
                        day_master_element TEXT,
                        day_master_strength TEXT,
                        wuxing_analysis TEXT,  -- JSON格式存储
                        shishen_analysis TEXT,  -- JSON格式存储
                        nayin_analysis TEXT,  -- JSON格式存储
                        FOREIGN KEY (record_id) REFERENCES paipan_records (id)
                    )
                ''')

                # 创建完整数据表（存储原始JSON）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS raw_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        record_id INTEGER NOT NULL,
                        raw_json TEXT NOT NULL,  -- 完整的原始JSON数据
                        formatted_output TEXT,   -- 格式化输出
                        file_paths TEXT,         -- JSON格式存储文件路径
                        FOREIGN KEY (record_id) REFERENCES paipan_records (id)
                    )
                ''')

                # 创建12角度分析表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS angle_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        record_id INTEGER NOT NULL,
                        angle_key TEXT NOT NULL,     -- 角度标识符
                        angle_name TEXT NOT NULL,    -- 角度中文名称
                        analysis_content TEXT,       -- 分析内容
                        word_count INTEGER DEFAULT 0,
                        analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        success BOOLEAN DEFAULT TRUE,
                        error_message TEXT,
                        retry_count INTEGER DEFAULT 0,
                        FOREIGN KEY (record_id) REFERENCES paipan_records (id),
                        UNIQUE(record_id, angle_key)
                    )
                ''')

                # 创建聊天记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS chat_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        record_id INTEGER NOT NULL,
                        session_id TEXT,
                        user_message TEXT NOT NULL,
                        assistant_response TEXT,
                        chat_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        response_time_ms INTEGER,
                        knowledge_used TEXT,  -- JSON格式存储使用的知识
                        FOREIGN KEY (record_id) REFERENCES paipan_records (id)
                    )
                ''')

                # 创建合盘分析记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS compatibility_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        compatibility_id TEXT UNIQUE NOT NULL,
                        person_a_name TEXT NOT NULL,
                        person_a_gender TEXT NOT NULL,
                        person_a_year INTEGER NOT NULL,
                        person_a_month INTEGER NOT NULL,
                        person_a_day INTEGER NOT NULL,
                        person_a_hour TEXT NOT NULL,
                        person_b_name TEXT NOT NULL,
                        person_b_gender TEXT NOT NULL,
                        person_b_year INTEGER NOT NULL,
                        person_b_month INTEGER NOT NULL,
                        person_b_day INTEGER NOT NULL,
                        person_b_hour TEXT NOT NULL,
                        analysis_dimension TEXT NOT NULL,
                        relationship_type TEXT,
                        status TEXT DEFAULT 'processing',  -- processing, completed, failed
                        analysis_content TEXT,
                        word_count INTEGER DEFAULT 0,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_time TIMESTAMP,
                        error_message TEXT,
                        person_a_record_id INTEGER,  -- 关联到个人排盘记录
                        person_b_record_id INTEGER,  -- 关联到个人排盘记录
                        raw_compatibility_data TEXT  -- JSON格式存储完整合盘数据
                    )
                ''')

                # 创建合盘分析详细结果表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS compatibility_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        compatibility_record_id INTEGER NOT NULL,
                        analysis_type TEXT NOT NULL,  -- personality, emotional, wealth, etc.
                        analysis_content TEXT,
                        score INTEGER,  -- 匹配度评分 0-100
                        analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (compatibility_record_id) REFERENCES compatibility_records (id)
                    )
                ''')

                # 创建六爻占卜记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS liuyao_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        liuyao_id TEXT UNIQUE NOT NULL,
                        question TEXT NOT NULL,  -- 占卜问题
                        divination_method TEXT NOT NULL,  -- 起卦方式: time, numbers, coins
                        divination_data TEXT,  -- JSON格式存储起卦数据（时间、数字、硬币结果等）
                        hexagram_data TEXT,  -- JSON格式存储卦象数据
                        analysis_content TEXT,  -- 分析内容
                        status TEXT DEFAULT 'processing',  -- processing, completed, failed
                        word_count INTEGER DEFAULT 0,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_time TIMESTAMP,
                        error_message TEXT
                    )
                ''')

                # 创建六爻卦象详细数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS liuyao_hexagram_details (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        liuyao_record_id INTEGER NOT NULL,
                        main_hexagram TEXT,  -- 主卦名称
                        changed_hexagram TEXT,  -- 变卦名称
                        upper_trigram TEXT,  -- 上卦
                        lower_trigram TEXT,  -- 下卦
                        moving_lines TEXT,  -- JSON格式存储动爻信息
                        six_relatives TEXT,  -- JSON格式存储六亲信息
                        six_spirits TEXT,  -- JSON格式存储六神信息
                        elements TEXT,  -- JSON格式存储五行信息
                        formatted_output TEXT,  -- 格式化的卦象显示
                        FOREIGN KEY (liuyao_record_id) REFERENCES liuyao_records (id)
                    )
                ''')

                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_birth_date ON paipan_records (birth_year, birth_month, birth_day)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_gender ON paipan_records (gender)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_calculation_time ON paipan_records (calculation_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_palace_name ON ziwei_data (palace_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_liuyao_id ON liuyao_records (liuyao_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_liuyao_created_time ON liuyao_records (created_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_liuyao_method ON liuyao_records (divination_method)')

                conn.commit()
                print("✅ 数据库初始化成功")

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise e

    def save_paipan_result(self, result_data: Dict[str, Any]) -> int:
        """
        保存排盘结果到数据库

        Args:
            result_data: 排盘结果数据

        Returns:
            记录ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 提取基本信息
                birth_info = result_data.get('birth_info', {})
                ziwei_data = result_data.get('ziwei_analysis', {})
                bazi_data = result_data.get('bazi_analysis', {})

                # 解析出生时间
                datetime_str = birth_info.get('datetime', '')
                # 这里需要解析datetime_str来获取年月日时
                # 假设格式是 "1990年3月15日8时"
                year, month, day, hour = self._parse_datetime_string(datetime_str)

                # 插入主记录
                cursor.execute('''
                    INSERT INTO paipan_records (
                        birth_year, birth_month, birth_day, birth_hour, gender,
                        birth_datetime, solar_date, lunar_date, zodiac, constellation,
                        calculation_time, success, error_message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    year, month, day, hour,
                    birth_info.get('gender', ''),
                    datetime_str,
                    birth_info.get('solar', ''),
                    birth_info.get('lunar', ''),
                    birth_info.get('zodiac', ''),
                    birth_info.get('sign', ''),
                    result_data.get('calculation_time', datetime.now().isoformat()),
                    result_data.get('success', False),
                    result_data.get('error', '')
                ))

                record_id = cursor.lastrowid

                # 保存紫薇斗数数据
                if 'palaces' in ziwei_data:
                    self._save_ziwei_data(cursor, record_id, ziwei_data['palaces'])

                # 保存八字数据
                if 'bazi_info' in bazi_data:
                    self._save_bazi_data(cursor, record_id, bazi_data)

                # 保存原始数据
                cursor.execute('''
                    INSERT INTO raw_data (record_id, raw_json, formatted_output, file_paths)
                    VALUES (?, ?, ?, ?)
                ''', (
                    record_id,
                    json.dumps(result_data, ensure_ascii=False, indent=2),
                    result_data.get('formatted_output', ''),
                    json.dumps(result_data.get('saved_files', {}), ensure_ascii=False)
                ))

                conn.commit()
                print(f"✅ 排盘数据已保存到数据库，记录ID: {record_id}")
                return record_id

        except Exception as e:
            print(f"❌ 保存排盘数据失败: {e}")
            raise e

    def _parse_datetime_string(self, datetime_str: str) -> tuple:
        """解析时间字符串"""
        try:
            # 处理格式如 "1990年3月15日8时"
            import re
            pattern = r'(\d{4})年(\d{1,2})月(\d{1,2})日(\d{1,2})时'
            match = re.match(pattern, datetime_str)
            if match:
                return int(match.group(1)), int(match.group(2)), int(match.group(3)), int(match.group(4))
            else:
                # 默认值
                return 1990, 1, 1, 0
        except:
            return 1990, 1, 1, 0

    def _save_ziwei_data(self, cursor, record_id: int, palaces: Dict[str, Any]):
        """保存紫薇斗数数据"""
        for palace_name, palace_data in palaces.items():
            cursor.execute('''
                INSERT INTO ziwei_data (
                    record_id, palace_name, position, major_stars, minor_stars,
                    adjective_stars, transformations, is_body_palace, palace_attributes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record_id,
                palace_name,
                palace_data.get('position', ''),
                json.dumps(palace_data.get('major_stars', []), ensure_ascii=False),
                json.dumps(palace_data.get('minor_stars', []), ensure_ascii=False),
                json.dumps(palace_data.get('adjective_stars', []), ensure_ascii=False),
                json.dumps(palace_data.get('transformations', []), ensure_ascii=False),
                palace_data.get('is_body_palace', False),
                palace_data.get('palace_name', '')
            ))

    def _save_bazi_data(self, cursor, record_id: int, bazi_data: Dict[str, Any]):
        """保存八字数据"""
        bazi_info = bazi_data.get('bazi_info', {})
        analysis = bazi_data.get('analysis', {})

        # 提取四柱信息
        year_pillar = bazi_info.get('year_pillar', ['', ''])
        month_pillar = bazi_info.get('month_pillar', ['', ''])
        day_pillar = bazi_info.get('day_pillar', ['', ''])
        hour_pillar = bazi_info.get('hour_pillar', ['', ''])

        day_master = analysis.get('day_master', {})

        cursor.execute('''
            INSERT INTO bazi_data (
                record_id, year_pillar_gan, year_pillar_zhi, month_pillar_gan, month_pillar_zhi,
                day_pillar_gan, day_pillar_zhi, hour_pillar_gan, hour_pillar_zhi,
                day_master_gan, day_master_element, day_master_strength,
                wuxing_analysis, shishen_analysis, nayin_analysis
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            record_id,
            year_pillar[0] if len(year_pillar) > 0 else '',
            year_pillar[1] if len(year_pillar) > 1 else '',
            month_pillar[0] if len(month_pillar) > 0 else '',
            month_pillar[1] if len(month_pillar) > 1 else '',
            day_pillar[0] if len(day_pillar) > 0 else '',
            day_pillar[1] if len(day_pillar) > 1 else '',
            hour_pillar[0] if len(hour_pillar) > 0 else '',
            hour_pillar[1] if len(hour_pillar) > 1 else '',
            day_master.get('gan', ''),
            day_master.get('element', ''),
            day_master.get('strength', ''),
            json.dumps(analysis.get('wuxing', {}), ensure_ascii=False),
            json.dumps(analysis.get('shishen', {}), ensure_ascii=False),
            json.dumps(analysis.get('nayin', {}), ensure_ascii=False)
        ))

    def get_paipan_record(self, record_id: int) -> Optional[Dict[str, Any]]:
        """
        根据记录ID获取排盘数据

        Args:
            record_id: 记录ID

        Returns:
            排盘数据字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
                cursor = conn.cursor()

                # 获取原始数据
                cursor.execute('SELECT raw_json FROM raw_data WHERE record_id = ?', (record_id,))
                row = cursor.fetchone()

                if row:
                    return json.loads(row['raw_json'])
                else:
                    return None

        except Exception as e:
            print(f"❌ 获取排盘记录失败: {e}")
            return None

    def search_records(self, **kwargs) -> List[Dict[str, Any]]:
        """
        搜索排盘记录

        Args:
            **kwargs: 搜索条件

        Returns:
            记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                params = []

                if 'gender' in kwargs:
                    conditions.append('gender = ?')
                    params.append(kwargs['gender'])

                if 'birth_year' in kwargs:
                    conditions.append('birth_year = ?')
                    params.append(kwargs['birth_year'])

                if 'success' in kwargs:
                    conditions.append('success = ?')
                    params.append(kwargs['success'])

                where_clause = ' AND '.join(conditions) if conditions else '1=1'

                # 处理limit参数
                limit = kwargs.get('limit', 100)

                cursor.execute(f'''
                    SELECT * FROM paipan_records
                    WHERE {where_clause}
                    ORDER BY calculation_time DESC
                    LIMIT ?
                ''', params + [limit])

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            print(f"❌ 搜索记录失败: {e}")
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总记录数
                cursor.execute('SELECT COUNT(*) FROM paipan_records')
                total_records = cursor.fetchone()[0]

                # 成功记录数
                cursor.execute('SELECT COUNT(*) FROM paipan_records WHERE success = 1')
                success_records = cursor.fetchone()[0]

                # 性别统计
                cursor.execute('SELECT gender, COUNT(*) FROM paipan_records GROUP BY gender')
                gender_stats = dict(cursor.fetchall())

                # 最近记录
                cursor.execute('SELECT birth_datetime, gender, calculation_time FROM paipan_records ORDER BY calculation_time DESC LIMIT 5')
                recent_records = cursor.fetchall()

                # 分析统计
                cursor.execute('SELECT COUNT(*) FROM angle_analysis')
                total_analyses = cursor.fetchone()[0]

                cursor.execute('SELECT COUNT(DISTINCT record_id) FROM angle_analysis')
                analyzed_records = cursor.fetchone()[0]

                return {
                    'total_records': total_records,
                    'success_records': success_records,
                    'success_rate': success_records / total_records if total_records > 0 else 0,
                    'gender_stats': gender_stats,
                    'recent_records': recent_records,
                    'total_analyses': total_analyses,
                    'analyzed_records': analyzed_records
                }

        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {}

    def save_angle_analysis(self, record_id: int, angle_key: str, angle_name: str,
                           analysis_content: str, word_count: int = 0, success: bool = True,
                           error_message: str = "", retry_count: int = 0) -> bool:
        """
        保存角度分析结果

        Args:
            record_id: 记录ID
            angle_key: 角度标识符
            angle_name: 角度中文名称
            analysis_content: 分析内容
            word_count: 字数
            success: 是否成功
            error_message: 错误信息
            retry_count: 重试次数

        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 使用INSERT OR REPLACE来处理重复分析
                cursor.execute('''
                    INSERT OR REPLACE INTO angle_analysis (
                        record_id, angle_key, angle_name, analysis_content,
                        word_count, success, error_message, retry_count
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record_id, angle_key, angle_name, analysis_content,
                    word_count, success, error_message, retry_count
                ))

                conn.commit()
                print(f"✅ 角度分析已保存: {angle_name} ({word_count}字)")
                return True

        except Exception as e:
            print(f"❌ 保存角度分析失败: {e}")
            return False

    def get_angle_analyses(self, record_id: int) -> List[Dict[str, Any]]:
        """
        获取记录的所有角度分析

        Args:
            record_id: 记录ID

        Returns:
            角度分析列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM angle_analysis
                    WHERE record_id = ?
                    ORDER BY analysis_time ASC
                ''', (record_id,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            print(f"❌ 获取角度分析失败: {e}")
            return []

    def get_angle_analysis_status(self, record_id: int) -> Dict[str, Any]:
        """
        获取记录的分析状态

        Args:
            record_id: 记录ID

        Returns:
            分析状态信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取已完成的角度
                cursor.execute('''
                    SELECT angle_key, angle_name, success, word_count
                    FROM angle_analysis
                    WHERE record_id = ? AND success = 1
                ''', (record_id,))
                completed_angles = cursor.fetchall()

                # 获取失败的角度
                cursor.execute('''
                    SELECT angle_key, angle_name, error_message, retry_count
                    FROM angle_analysis
                    WHERE record_id = ? AND success = 0
                ''', (record_id,))
                failed_angles = cursor.fetchall()

                # 定义所有12个角度
                all_angles = [
                    ("personality_destiny", "命宫分析"),
                    ("wealth_fortune", "财富分析"),
                    ("marriage_love", "婚姻分析"),
                    ("health_wellness", "健康分析"),
                    ("career_achievement", "事业分析"),
                    ("children_creativity", "子女分析"),
                    ("interpersonal_relationship", "人际分析"),
                    ("education_learning", "学业分析"),
                    ("family_environment", "家庭分析"),
                    ("travel_relocation", "迁移分析"),
                    ("spiritual_blessing", "精神分析"),
                    ("authority_parents", "权威分析")
                ]

                completed_keys = [angle[0] for angle in completed_angles]
                pending_angles = [(key, name) for key, name in all_angles if key not in completed_keys]

                return {
                    'total_angles': len(all_angles),
                    'completed_count': len(completed_angles),
                    'pending_count': len(pending_angles),
                    'failed_count': len(failed_angles),
                    'completed_angles': completed_angles,
                    'pending_angles': pending_angles,
                    'failed_angles': failed_angles,
                    'completion_rate': len(completed_angles) / len(all_angles) * 100
                }

        except Exception as e:
            print(f"❌ 获取分析状态失败: {e}")
            return {}

    def save_chat_message(self, record_id: int, session_id: str, user_message: str,
                         assistant_response: str, response_time_ms: int = 0,
                         knowledge_used: Dict[str, Any] = None) -> bool:
        """
        保存聊天记录

        Args:
            record_id: 记录ID
            session_id: 会话ID
            user_message: 用户消息
            assistant_response: 助手回复
            response_time_ms: 响应时间(毫秒)
            knowledge_used: 使用的知识

        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO chat_history (
                        record_id, session_id, user_message, assistant_response,
                        response_time_ms, knowledge_used
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    record_id, session_id, user_message, assistant_response,
                    response_time_ms, json.dumps(knowledge_used or {}, ensure_ascii=False)
                ))

                conn.commit()
                return True

        except Exception as e:
            print(f"❌ 保存聊天记录失败: {e}")
            return False

    def get_chat_history(self, record_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取聊天历史

        Args:
            record_id: 记录ID
            limit: 限制数量

        Returns:
            聊天历史列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM chat_history
                    WHERE record_id = ?
                    ORDER BY chat_time DESC
                    LIMIT ?
                ''', (record_id, limit))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            print(f"❌ 获取聊天历史失败: {e}")
            return []

    def save_compatibility_record(self, compatibility_data: Dict[str, Any]) -> str:
        """
        保存合盘分析记录

        Args:
            compatibility_data: 合盘分析数据

        Returns:
            合盘记录ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                person_a = compatibility_data.get('person_a', {})
                person_b = compatibility_data.get('person_b', {})

                # 生成唯一的合盘ID
                compatibility_id = f"comp_{int(datetime.now().timestamp())}"

                cursor.execute('''
                    INSERT INTO compatibility_records (
                        compatibility_id, person_a_name, person_a_gender, person_a_year,
                        person_a_month, person_a_day, person_a_hour,
                        person_b_name, person_b_gender, person_b_year,
                        person_b_month, person_b_day, person_b_hour,
                        analysis_dimension, status, raw_compatibility_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    compatibility_id,
                    person_a.get('name', ''),
                    person_a.get('gender', ''),
                    int(person_a.get('year', 0)),
                    int(person_a.get('month', 0)),
                    int(person_a.get('day', 0)),
                    person_a.get('hour', ''),
                    person_b.get('name', ''),
                    person_b.get('gender', ''),
                    int(person_b.get('year', 0)),
                    int(person_b.get('month', 0)),
                    int(person_b.get('day', 0)),
                    person_b.get('hour', ''),
                    compatibility_data.get('analysis_dimension', ''),
                    'processing',
                    json.dumps(compatibility_data, ensure_ascii=False)
                ))

                conn.commit()
                print(f"✅ 合盘记录保存成功: {compatibility_id}")
                return compatibility_id

        except Exception as e:
            print(f"❌ 保存合盘记录失败: {e}")
            return ""

    def get_compatibility_records(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取合盘记录列表

        Args:
            limit: 返回记录数量限制

        Returns:
            合盘记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM compatibility_records
                    ORDER BY created_time DESC
                    LIMIT ?
                ''', (limit,))

                records = []
                for row in cursor.fetchall():
                    record = dict(row)
                    # 重构person_a和person_b数据
                    record['person_a'] = {
                        'name': record['person_a_name'],
                        'gender': record['person_a_gender'],
                        'year': str(record['person_a_year']),
                        'month': str(record['person_a_month']),
                        'day': str(record['person_a_day']),
                        'hour': record['person_a_hour']
                    }
                    record['person_b'] = {
                        'name': record['person_b_name'],
                        'gender': record['person_b_gender'],
                        'year': str(record['person_b_year']),
                        'month': str(record['person_b_month']),
                        'day': str(record['person_b_day']),
                        'hour': record['person_b_hour']
                    }
                    records.append(record)

                return records

        except Exception as e:
            print(f"❌ 获取合盘记录失败: {e}")
            return []

    def get_compatibility_record(self, compatibility_id: str) -> Optional[Dict[str, Any]]:
        """
        获取单个合盘记录

        Args:
            compatibility_id: 合盘记录ID

        Returns:
            合盘记录数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM compatibility_records
                    WHERE compatibility_id = ?
                ''', (compatibility_id,))

                row = cursor.fetchone()
                if row:
                    record = dict(row)
                    # 重构person_a和person_b数据
                    record['person_a'] = {
                        'name': record['person_a_name'],
                        'gender': record['person_a_gender'],
                        'year': str(record['person_a_year']),
                        'month': str(record['person_a_month']),
                        'day': str(record['person_a_day']),
                        'hour': record['person_a_hour']
                    }
                    record['person_b'] = {
                        'name': record['person_b_name'],
                        'gender': record['person_b_gender'],
                        'year': str(record['person_b_year']),
                        'month': str(record['person_b_month']),
                        'day': str(record['person_b_day']),
                        'hour': record['person_b_hour']
                    }
                    return record
                return None

        except Exception as e:
            print(f"❌ 获取合盘记录失败: {e}")
            return None

    def update_compatibility_result(self, compatibility_id: str, analysis_content: str,
                                  status: str = 'completed') -> bool:
        """
        更新合盘分析结果

        Args:
            compatibility_id: 合盘记录ID
            analysis_content: 分析内容
            status: 状态

        Returns:
            是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE compatibility_records
                    SET analysis_content = ?, status = ?, completed_time = ?, word_count = ?
                    WHERE compatibility_id = ?
                ''', (
                    analysis_content,
                    status,
                    datetime.now().isoformat(),
                    len(analysis_content),
                    compatibility_id
                ))

                conn.commit()
                print(f"✅ 合盘结果更新成功: {compatibility_id}")
                return True

        except Exception as e:
            print(f"❌ 更新合盘结果失败: {e}")
            return False

    def delete_compatibility_record(self, compatibility_id: str) -> bool:
        """
        删除合盘记录

        Args:
            compatibility_id: 合盘记录ID

        Returns:
            是否删除成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除相关的分析记录
                cursor.execute('''
                    DELETE FROM compatibility_analysis
                    WHERE compatibility_record_id IN (
                        SELECT id FROM compatibility_records WHERE compatibility_id = ?
                    )
                ''', (compatibility_id,))

                # 删除主记录
                cursor.execute('''
                    DELETE FROM compatibility_records
                    WHERE compatibility_id = ?
                ''', (compatibility_id,))

                conn.commit()
                print(f"✅ 合盘记录删除成功: {compatibility_id}")
                return True

        except Exception as e:
            print(f"❌ 删除合盘记录失败: {e}")
            return False

    def save_liuyao_record(self, liuyao_data: Dict[str, Any]) -> str:
        """
        保存六爻占卜记录

        Args:
            liuyao_data: 六爻占卜数据

        Returns:
            六爻记录ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 生成唯一的六爻ID
                liuyao_id = f"liuyao_{int(datetime.now().timestamp())}"

                cursor.execute('''
                    INSERT INTO liuyao_records (
                        liuyao_id, question, divination_method, divination_data,
                        hexagram_data, status
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    liuyao_id,
                    liuyao_data.get('question', ''),
                    liuyao_data.get('method', ''),
                    json.dumps(liuyao_data.get('divination_data', {}), ensure_ascii=False),
                    json.dumps(liuyao_data.get('hexagram_data', {}), ensure_ascii=False),
                    'processing'
                ))

                conn.commit()
                print(f"✅ 六爻记录保存成功: {liuyao_id}")
                return liuyao_id

        except Exception as e:
            print(f"❌ 保存六爻记录失败: {e}")
            return ""

    def get_liuyao_records(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取六爻记录列表

        Args:
            limit: 返回记录数量限制

        Returns:
            六爻记录列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM liuyao_records
                    ORDER BY created_time DESC
                    LIMIT ?
                ''', (limit,))

                records = []
                for row in cursor.fetchall():
                    record = dict(row)
                    # 解析JSON数据
                    try:
                        record['divination_data'] = json.loads(record['divination_data']) if record['divination_data'] else {}
                        record['hexagram_data'] = json.loads(record['hexagram_data']) if record['hexagram_data'] else {}
                    except json.JSONDecodeError:
                        record['divination_data'] = {}
                        record['hexagram_data'] = {}
                    records.append(record)

                return records

        except Exception as e:
            print(f"❌ 获取六爻记录失败: {e}")
            return []

    def get_liuyao_record(self, liuyao_id: str) -> Optional[Dict[str, Any]]:
        """
        获取单个六爻记录

        Args:
            liuyao_id: 六爻记录ID

        Returns:
            六爻记录数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM liuyao_records
                    WHERE liuyao_id = ?
                ''', (liuyao_id,))

                row = cursor.fetchone()
                if row:
                    record = dict(row)
                    # 解析JSON数据
                    try:
                        record['divination_data'] = json.loads(record['divination_data']) if record['divination_data'] else {}
                        record['hexagram_data'] = json.loads(record['hexagram_data']) if record['hexagram_data'] else {}
                    except json.JSONDecodeError:
                        record['divination_data'] = {}
                        record['hexagram_data'] = {}
                    return record
                return None

        except Exception as e:
            print(f"❌ 获取六爻记录失败: {e}")
            return None

    def update_liuyao_result(self, liuyao_id: str, analysis_content: str,
                           status: str = 'completed') -> bool:
        """
        更新六爻分析结果

        Args:
            liuyao_id: 六爻记录ID
            analysis_content: 分析内容
            status: 状态

        Returns:
            是否更新成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE liuyao_records
                    SET analysis_content = ?, status = ?, completed_time = ?, word_count = ?
                    WHERE liuyao_id = ?
                ''', (
                    analysis_content,
                    status,
                    datetime.now().isoformat(),
                    len(analysis_content),
                    liuyao_id
                ))

                conn.commit()
                print(f"✅ 六爻结果更新成功: {liuyao_id}")
                return True

        except Exception as e:
            print(f"❌ 更新六爻结果失败: {e}")
            return False

    def delete_liuyao_record(self, liuyao_id: str) -> bool:
        """
        删除六爻记录

        Args:
            liuyao_id: 六爻记录ID

        Returns:
            是否删除成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除相关的卦象详细数据
                cursor.execute('''
                    DELETE FROM liuyao_hexagram_details
                    WHERE liuyao_record_id IN (
                        SELECT id FROM liuyao_records WHERE liuyao_id = ?
                    )
                ''', (liuyao_id,))

                # 删除主记录
                cursor.execute('''
                    DELETE FROM liuyao_records
                    WHERE liuyao_id = ?
                ''', (liuyao_id,))

                conn.commit()
                print(f"✅ 六爻记录删除成功: {liuyao_id}")
                return True

        except Exception as e:
            print(f"❌ 删除六爻记录失败: {e}")
            return False

def test_database():
    """测试数据库功能"""
    print("🧪 测试数据库功能")

    db = PaipanDatabase("test_paipan.db")

    # 测试数据
    test_data = {
        "success": True,
        "calculation_time": "2025-06-25T02:00:00",
        "birth_info": {
            "datetime": "1990年3月15日8时",
            "gender": "女",
            "solar": "1990年3月15日8时",
            "lunar": "一九九零年二月十九",
            "zodiac": "马",
            "sign": "双鱼座"
        },
        "ziwei_analysis": {
            "palaces": {
                "命宫": {
                    "position": "亥",
                    "major_stars": ["太阴"],
                    "minor_stars": ["天魁"],
                    "adjective_stars": ["天才"],
                    "is_body_palace": False
                }
            }
        },
        "bazi_analysis": {
            "bazi_info": {
                "year_pillar": ["庚", "午"],
                "month_pillar": ["己", "卯"],
                "day_pillar": ["壬", "寅"],
                "hour_pillar": ["癸", "卯"]
            },
            "analysis": {
                "day_master": {
                    "gan": "壬",
                    "element": "水",
                    "strength": "偏弱"
                }
            }
        }
    }

    # 保存测试数据
    record_id = db.save_paipan_result(test_data)
    print(f"✅ 测试数据保存成功，ID: {record_id}")

    # 读取测试数据
    retrieved_data = db.get_paipan_record(record_id)
    if retrieved_data:
        print("✅ 测试数据读取成功")
    else:
        print("❌ 测试数据读取失败")

    # 获取统计信息
    stats = db.get_statistics()
    print(f"📊 数据库统计: {stats}")

if __name__ == "__main__":
    test_database()
