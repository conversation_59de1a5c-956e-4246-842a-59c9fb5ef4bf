#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试火元素计算差异
"""

def debug_fire_calculation():
    """调试火元素计算差异"""
    print("🔍 调试火元素计算差异")
    print("=" * 50)
    
    bazi = "戊辰 丁巳 丁亥 丙午"
    print(f"八字: {bazi}")
    
    # 手动逐个计算
    print(f"\n📊 逐个元素计算:")
    
    fire_elements = []
    
    # 年柱：戊辰
    print("年柱 戊辰:")
    print("  戊(天干) = 土")
    print("  辰(地支) = 土") 
    print("  辰藏干: 戊(土)、乙(木)、癸(水)")
    print("  → 火元素: 0个")
    
    # 月柱：丁巳
    print("月柱 丁巳:")
    print("  丁(天干) = 火 ✓")
    fire_elements.append("月干丁")
    print("  巳(地支) = 火 ✓")
    fire_elements.append("月支巳")
    print("  巳藏干: 丙(火)、戊(土)、庚(金)")
    print("    丙(火) ✓ (权重0.5)")
    fire_elements.append("月支巳藏丙(0.5)")
    print("  → 火元素: 2.5个")
    
    # 日柱：丁亥
    print("日柱 丁亥:")
    print("  丁(天干) = 火 ✓")
    fire_elements.append("日干丁")
    print("  亥(地支) = 水")
    print("  亥藏干: 壬(水)、甲(木)")
    print("  → 火元素: 1个")
    
    # 时柱：丙午
    print("时柱 丙午:")
    print("  丙(天干) = 火 ✓")
    fire_elements.append("时干丙")
    print("  午(地支) = 火 ✓")
    fire_elements.append("时支午")
    print("  午藏干: 丁(火)、己(土)")
    print("    丁(火) ✓ (权重0.5)")
    fire_elements.append("时支午藏丁(0.5)")
    print("  → 火元素: 2.5个")
    
    print(f"\n🔥 手动计算火元素:")
    total_fire = 0
    for element in fire_elements:
        if "(0.5)" in element:
            total_fire += 0.5
            print(f"  {element}")
        else:
            total_fire += 1
            print(f"  {element}")
    
    print(f"  总计: {total_fire}个")
    
    # 检查算法计算
    print(f"\n🔧 检查算法计算:")
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()
        
        result = calc.calculate_enhanced_bazi(1988, 6, 1, 11, "男")
        
        if result["success"]:
            analysis = result["analysis"]
            wuxing = analysis["wuxing"]
            algo_fire_count = wuxing["count"]["火"]
            algo_fire_details = wuxing["details"]["火"]
            
            print(f"  算法火元素: {algo_fire_count}个")
            print(f"  算法来源:")
            for detail in algo_fire_details:
                print(f"    {detail}")
            
            # 找出差异
            print(f"\n🔍 差异分析:")
            print(f"  手动计算: {total_fire}个")
            print(f"  算法计算: {algo_fire_count}个")
            print(f"  差异: {algo_fire_count - total_fire}个")
            
            # 检查是否有重复计算
            manual_set = set(fire_elements)
            algo_set = set(algo_fire_details)
            
            print(f"\n📊 详细对比:")
            print(f"  手动来源: {manual_set}")
            print(f"  算法来源: {algo_set}")
            
            extra_in_algo = algo_set - manual_set
            missing_in_algo = manual_set - algo_set
            
            if extra_in_algo:
                print(f"  算法多算: {extra_in_algo}")
            if missing_in_algo:
                print(f"  算法漏算: {missing_in_algo}")
            
            return total_fire, algo_fire_count
        else:
            print(f"❌ 算法执行失败")
            return total_fire, 0
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return total_fire, 0

def check_algorithm_logic():
    """检查算法逻辑是否有问题"""
    print(f"\n🔍 检查算法逻辑")
    print("=" * 50)
    
    try:
        from algorithms.enhanced_bazi_calculator import EnhancedBaziCalculator
        calc = EnhancedBaziCalculator()
        
        # 模拟算法执行过程
        year_pillar, month_pillar, day_pillar, hour_pillar = "戊辰", "丁巳", "丁亥", "丙午"
        
        wuxing_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
        wuxing_details = {'木': [], '火': [], '土': [], '金': [], '水': []}
        
        pillars = [year_pillar, month_pillar, day_pillar, hour_pillar]
        pillar_names = ['年', '月', '日', '时']
        
        print("🔧 逐步模拟算法:")
        
        for i, pillar in enumerate(pillars):
            print(f"\n处理{pillar_names[i]}柱 {pillar}:")
            
            # 天干
            tiangan = pillar[0]
            if tiangan in calc.wuxing_map:
                element = calc.wuxing_map[tiangan]
                wuxing_count[element] += 1
                detail = f"{pillar_names[i]}干{tiangan}"
                wuxing_details[element].append(detail)
                print(f"  天干{tiangan} = {element} → {detail}")
            
            # 地支
            dizhi = pillar[1]
            if dizhi in calc.wuxing_map:
                element = calc.wuxing_map[dizhi]
                wuxing_count[element] += 1
                detail = f"{pillar_names[i]}支{dizhi}"
                wuxing_details[element].append(detail)
                print(f"  地支{dizhi} = {element} → {detail}")
            
            # 地支藏干
            if dizhi in calc.dizhi_canggan:
                print(f"  {dizhi}藏干:")
                for canggan in calc.dizhi_canggan[dizhi]:
                    if canggan in calc.wuxing_map:
                        element = calc.wuxing_map[canggan]
                        wuxing_count[element] += 0.5  # 藏干权重减半
                        detail = f"{pillar_names[i]}支{dizhi}藏{canggan}"
                        wuxing_details[element].append(detail)
                        print(f"    {canggan} = {element} (权重0.5) → {detail}")
        
        print(f"\n📊 模拟结果:")
        print(f"  火元素: {wuxing_count['火']}个")
        print(f"  火来源: {wuxing_details['火']}")
        
        return wuxing_count['火'], wuxing_details['火']
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return 0, []

def main():
    """主函数"""
    print("🧪 火元素计算差异调试")
    print("=" * 80)
    
    # 手动计算
    manual_fire, algo_fire = debug_fire_calculation()
    
    # 算法逻辑检查
    simulated_fire, simulated_details = check_algorithm_logic()
    
    print("\n" + "=" * 80)
    print("🎯 调试结论:")
    print(f"  手动计算: {manual_fire}个")
    print(f"  算法计算: {algo_fire}个")
    print(f"  模拟计算: {simulated_fire}个")
    
    if manual_fire == algo_fire:
        print("✅ 算法计算正确")
    else:
        print("❌ 算法计算有误，需要修正")
        print("可能原因:")
        print("1. 重复计算某个元素")
        print("2. 权重计算错误")
        print("3. 逻辑判断有误")

if __name__ == "__main__":
    main()
