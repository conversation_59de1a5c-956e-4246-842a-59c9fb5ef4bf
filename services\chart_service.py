#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排盘服务 - 统一管理紫薇和八字排盘
"""

from datetime import datetime
from typing import Optional
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.birth_info import BirthInfo
from models.chart_data import ChartData, ZiweiChart, BaziChart
from utils.simple_logger import get_logger
from utils.cache_manager import get_cache
try:
    from algorithms import get_ziwei_calculator, get_bazi_calculator
except ImportError:
    get_ziwei_calculator = None
    get_bazi_calculator = None

logger = get_logger()
cache = get_cache()

class ChartService:
    """排盘服务"""

    def __init__(self):
        """初始化排盘服务"""
        self.ziwei_calc = None
        self.bazi_calc = None
        self._init_calculators()

    def _init_calculators(self):
        """初始化算法计算器"""
        try:
            if get_ziwei_calculator:
                self.ziwei_calc = get_ziwei_calculator()
                logger.info("✅ 紫薇算法初始化成功")
            else:
                self.ziwei_calc = None
                logger.warning("⚠️ 紫薇算法不可用")
        except Exception as e:
            logger.error(f"❌ 紫薇算法初始化失败: {e}")
            self.ziwei_calc = None

        try:
            if get_bazi_calculator:
                self.bazi_calc = get_bazi_calculator()
                logger.info("✅ 八字算法初始化成功")
            else:
                self.bazi_calc = None
                logger.warning("⚠️ 八字算法不可用")
        except Exception as e:
            logger.error(f"❌ 八字算法初始化失败: {e}")
            self.bazi_calc = None

    def generate_chart(self, birth_info: BirthInfo) -> ChartData:
        """
        生成完整排盘（紫薇+八字）

        Args:
            birth_info: 生辰信息

        Returns:
            完整排盘数据
        """
        logger.info(f"🔮 开始生成排盘: {birth_info.to_display_string()}")

        # 检查缓存
        cache_key = birth_info.get_cache_key()
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info("📋 使用缓存数据")
            return ChartData.from_dict(cached_data)

        # 创建排盘数据对象
        chart_data = ChartData(birth_info=birth_info)

        try:
            # 1. 生成紫薇排盘
            ziwei_chart = self._generate_ziwei_chart(birth_info)
            if ziwei_chart:
                chart_data.ziwei_chart = ziwei_chart
                logger.info("✅ 紫薇排盘生成成功")
            else:
                logger.error("❌ 紫薇排盘生成失败")
                chart_data.success = False
                chart_data.error_message = "紫薇排盘生成失败"
                return chart_data

            # 2. 生成八字排盘
            bazi_chart = self._generate_bazi_chart(birth_info)
            if bazi_chart:
                chart_data.bazi_chart = bazi_chart
                logger.info("✅ 八字排盘生成成功")
            else:
                logger.error("❌ 八字排盘生成失败")
                chart_data.success = False
                chart_data.error_message = "八字排盘生成失败"
                return chart_data

            # 3. 数据一致性检查
            if self._validate_consistency(ziwei_chart, bazi_chart):
                logger.info("✅ 数据一致性检查通过")
            else:
                logger.warning("⚠️ 数据一致性检查未通过，但继续使用")

            # 4. 保存到缓存
            cache.set(cache_key, chart_data.to_dict(), "chart")
            logger.info("💾 排盘数据已缓存")

            logger.info("🎉 排盘生成完成")
            return chart_data

        except Exception as e:
            logger.error(f"❌ 排盘生成异常: {e}")
            chart_data.success = False
            chart_data.error_message = f"排盘生成异常: {str(e)}"
            return chart_data

    def _generate_ziwei_chart(self, birth_info: BirthInfo) -> Optional[ZiweiChart]:
        """生成紫薇排盘"""
        if not self.ziwei_calc:
            logger.error("紫薇算法未初始化")
            return None

        try:
            result = self.ziwei_calc.calculate_chart(
                birth_info.year,
                birth_info.month,
                birth_info.day,
                birth_info.hour,
                birth_info.gender
            )

            if isinstance(result, dict) and not result.get("error"):
                return ZiweiChart(
                    palaces=result.get("palaces", {}),
                    stars=result.get("stars", {}),
                    birth_info=result.get("birth_info", {}),
                    lunar_date=result.get("lunar_date", {})
                )
            else:
                logger.error(f"紫薇算法返回错误: {result.get('error', '未知错误')}")
                return None

        except Exception as e:
            logger.error(f"紫薇算法调用异常: {e}")
            return None

    def _generate_bazi_chart(self, birth_info: BirthInfo) -> Optional[BaziChart]:
        """生成八字排盘"""
        if not self.bazi_calc:
            logger.error("八字算法未初始化")
            return None

        try:
            result = self.bazi_calc.calculate_bazi(
                birth_info.year,
                birth_info.month,
                birth_info.day,
                birth_info.hour,
                birth_info.gender
            )

            if isinstance(result, dict) and result.get("success"):
                data = result.get("data", {})
                return BaziChart(
                    four_pillars=data.get("four_pillars", {}),
                    ten_gods=data.get("ten_gods", {}),
                    dayun=data.get("dayun", []),
                    liunian=data.get("liunian", []),
                    wuxing=data.get("wuxing", {})
                )
            else:
                logger.error(f"八字算法返回错误: {result.get('error', '未知错误')}")
                return None

        except Exception as e:
            logger.error(f"八字算法调用异常: {e}")
            return None

    def _validate_consistency(self, ziwei_chart: ZiweiChart, bazi_chart: BaziChart) -> bool:
        """验证紫薇和八字数据的一致性"""
        try:
            # 检查基本数据是否存在
            if not ziwei_chart.birth_info or not bazi_chart.four_pillars:
                return False

            # 可以添加更多一致性检查逻辑
            # 比如检查农历日期、五行分布等

            return True

        except Exception as e:
            logger.error(f"一致性检查异常: {e}")
            return False

    def get_chart_summary(self, chart_data: ChartData) -> str:
        """获取排盘摘要"""
        if not chart_data.success:
            return f"排盘失败: {chart_data.error_message}"

        summary_parts = [chart_data.birth_info.to_display_string()]

        if chart_data.ziwei_chart:
            main_palace = chart_data.ziwei_chart.get_main_palace()
            if main_palace:
                summary_parts.append(f"命宫: {main_palace.get('宫位', '未知')}")

        if chart_data.bazi_chart:
            wuxing_summary = chart_data.bazi_chart.get_wuxing_summary()
            summary_parts.append(f"五行: {wuxing_summary}")

        return " | ".join(summary_parts)
