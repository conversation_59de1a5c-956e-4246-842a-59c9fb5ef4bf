#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持久化功能
"""

import sys
import os
import json
import time
sys.path.append('.')

def test_session_manager():
    """测试会话管理器"""
    print("持久化功能测试")
    print("=" * 50)
    
    print("\n1. 测试会话管理器")
    print("-" * 30)
    
    try:
        from core.storage.session_manager import SessionManager, UserSettings, ChatMessage
        
        # 创建会话管理器
        session_manager = SessionManager()
        print("✅ 会话管理器创建成功")
        
        # 创建测试会话
        user_id = "test_user_001"
        settings = UserSettings(
            analysis_depth="detailed",
            quality_level="premium",
            focus_positive=True
        )
        
        session_data = session_manager.create_session(user_id, settings)
        print(f"✅ 创建会话成功: {session_data.session_id}")
        
        # 添加测试消息
        session_manager.add_message(
            session_data.session_id,
            "user",
            "我想看紫薇斗数",
            "text"
        )
        
        session_manager.add_message(
            session_data.session_id,
            "assistant", 
            "好的，我来为您分析紫薇斗数...",
            "analysis",
            {"response_count": 5}
        )
        
        print("✅ 添加消息成功")
        
        # 加载会话
        loaded_session = session_manager.load_session(session_data.session_id)
        if loaded_session:
            print(f"✅ 加载会话成功，包含 {len(loaded_session.messages)} 条消息")
            print(f"   设置: {loaded_session.settings.analysis_depth}, {loaded_session.settings.quality_level}")
        else:
            print("❌ 加载会话失败")
            return False
        
        # 更新设置
        new_settings = UserSettings(
            analysis_depth="comprehensive",
            quality_level="professional"
        )
        session_manager.update_settings(session_data.session_id, new_settings)
        print("✅ 更新设置成功")
        
        # 获取用户会话列表
        user_sessions = session_manager.get_user_sessions(user_id)
        print(f"✅ 获取用户会话列表: {len(user_sessions)} 个会话")
        
        return True
        
    except Exception as e:
        print(f"❌ 会话管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_persistence():
    """测试数据持久化"""
    print("\n2. 测试数据持久化")
    print("-" * 30)
    
    try:
        from core.storage.session_manager import SessionManager, UserSettings
        
        session_manager = SessionManager()
        
        # 创建会话并添加数据
        user_id = "persistence_test_user"
        session_data = session_manager.create_session(user_id)
        session_id = session_data.session_id
        
        # 添加多条消息
        messages = [
            ("user", "你好"),
            ("assistant", "您好！我是智能算命AI"),
            ("user", "我想看八字算命"),
            ("assistant", "好的，请提供您的出生信息...")
        ]
        
        for role, content in messages:
            session_manager.add_message(session_id, role, content)
        
        print(f"✅ 添加了 {len(messages)} 条消息")
        
        # 检查文件是否存在
        session_file = f"data/sessions/sessions/{session_id}.json"
        if os.path.exists(session_file):
            print("✅ 会话文件已创建")
            
            # 检查文件内容
            with open(session_file, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            print(f"✅ 文件包含 {len(data['messages'])} 条消息")
            print(f"✅ 用户ID: {data['user_id']}")
            print(f"✅ 创建时间: {data['created_at']}")
            
        else:
            print("❌ 会话文件未创建")
            return False
        
        # 测试重新加载
        new_session_manager = SessionManager()
        reloaded_session = new_session_manager.load_session(session_id)
        
        if reloaded_session and len(reloaded_session.messages) == len(messages):
            print("✅ 数据持久化成功，重新加载正常")
        else:
            print("❌ 数据持久化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据持久化测试失败: {e}")
        return False

def test_settings_persistence():
    """测试设置持久化"""
    print("\n3. 测试设置持久化")
    print("-" * 30)
    
    try:
        from core.storage.session_manager import SessionManager, UserSettings
        
        session_manager = SessionManager()
        
        # 创建会话
        user_id = "settings_test_user"
        initial_settings = UserSettings(
            analysis_depth="brief",
            quality_level="basic",
            focus_positive=False,
            include_timing=False,
            practical_advice=False
        )
        
        session_data = session_manager.create_session(user_id, initial_settings)
        session_id = session_data.session_id
        
        print("✅ 创建会话，初始设置为基础版")
        
        # 更新设置
        updated_settings = UserSettings(
            analysis_depth="comprehensive",
            quality_level="premium",
            focus_positive=True,
            include_timing=True,
            practical_advice=True
        )
        
        session_manager.update_settings(session_id, updated_settings)
        print("✅ 更新设置为高级版")
        
        # 重新加载验证
        reloaded_session = session_manager.load_session(session_id)
        if reloaded_session:
            settings = reloaded_session.settings
            if (settings.analysis_depth == "comprehensive" and
                settings.quality_level == "premium" and
                settings.focus_positive == True):
                print("✅ 设置持久化成功")
                return True
            else:
                print("❌ 设置持久化失败，数据不匹配")
                return False
        else:
            print("❌ 重新加载会话失败")
            return False
        
    except Exception as e:
        print(f"❌ 设置持久化测试失败: {e}")
        return False

def test_web_integration():
    """测试Web界面集成"""
    print("\n4. 测试Web界面集成")
    print("-" * 30)
    
    try:
        # 检查增强版Web文件
        with open("web_demo/enhanced_web.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键功能
        integration_features = [
            "SessionManager",
            "UserSettings", 
            "ChatMessage",
            "display_session_history_panel",
            "add_message",
            "load_session",
            "export_session"
        ]
        
        for feature in integration_features:
            if feature in content:
                print(f"✅ {feature} 已集成")
            else:
                print(f"❌ {feature} 未集成")
        
        # 检查持久化相关代码
        persistence_code = [
            "session_manager.add_message",
            "session_manager.load_session",
            "session_manager.create_session",
            "session_manager.update_settings"
        ]
        
        found_count = 0
        for code in persistence_code:
            if code in content:
                found_count += 1
                print(f"✅ {code} 调用存在")
            else:
                print(f"❌ {code} 调用缺失")
        
        integration_rate = found_count / len(persistence_code)
        print(f"\n集成完成度: {found_count}/{len(persistence_code)} ({integration_rate:.1%})")
        
        return integration_rate >= 0.8
        
    except Exception as e:
        print(f"❌ Web界面集成测试失败: {e}")
        return False

def test_storage_directory():
    """测试存储目录结构"""
    print("\n5. 测试存储目录结构")
    print("-" * 30)
    
    try:
        from core.storage.session_manager import SessionManager
        
        session_manager = SessionManager()
        
        # 检查目录结构
        base_dir = "data/sessions"
        subdirs = ["sessions", "settings", "backups"]
        
        for subdir in subdirs:
            dir_path = os.path.join(base_dir, subdir)
            if os.path.exists(dir_path):
                print(f"✅ 目录存在: {dir_path}")
            else:
                print(f"❌ 目录缺失: {dir_path}")
        
        # 获取存储统计
        stats = session_manager.get_storage_stats()
        print(f"\n存储统计:")
        print(f"  总会话数: {stats.get('total_sessions', 0)}")
        print(f"  总大小: {stats.get('total_size_mb', 0)} MB")
        print(f"  缓存大小: {stats.get('cache_size', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 存储目录测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("持久化功能开发测试")
    print("=" * 80)
    print("目标: 实现聊天历史和用户设置的持久化存储")
    print("=" * 80)
    
    # 执行测试
    test_results = []
    
    # 1. 会话管理器测试
    test_results.append(("会话管理器", test_session_manager()))
    
    # 2. 数据持久化测试
    test_results.append(("数据持久化", test_data_persistence()))
    
    # 3. 设置持久化测试
    test_results.append(("设置持久化", test_settings_persistence()))
    
    # 4. Web界面集成测试
    test_results.append(("Web界面集成", test_web_integration()))
    
    # 5. 存储目录测试
    test_results.append(("存储目录结构", test_storage_directory()))
    
    # 汇总结果
    print(f"\n持久化功能测试结果")
    print("=" * 80)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 持久化功能开发成功！")
        print("\n💾 实现的核心功能:")
        print("  ✅ 聊天历史持久化 - 消息自动保存和恢复")
        print("  ✅ 用户设置持久化 - 偏好设置记忆功能")
        print("  ✅ 会话管理 - 多会话支持和切换")
        print("  ✅ 数据导出 - JSON格式会话导出")
        print("  ✅ 自动清理 - 旧会话定期清理")
        print("\n🌟 技术特色:")
        print("  📁 文件存储 - JSON格式，易于查看和备份")
        print("  🚀 内存缓存 - 提升访问性能")
        print("  🔄 自动同步 - 实时保存用户操作")
        print("  📊 统计监控 - 存储使用情况统计")
        print("  🛡️ 错误处理 - 完善的异常处理机制")
        print("\n🎯 用户体验提升:")
        print("  🔄 刷新不丢失 - 页面刷新后数据完整保留")
        print("  📚 历史回顾 - 可查看和恢复历史会话")
        print("  ⚙️ 设置记忆 - 个人偏好自动保存")
        print("  📤 数据导出 - 支持会话数据导出备份")
        print("\n🚀 现在可以放心刷新页面，数据不会丢失！")
    else:
        print("💥 部分功能存在问题，需要修复后再继续")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
