#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的数据处理器 - 支持原始紫薇数据格式
"""

import asyncio

async def test_process_message_format():
    """测试process_message格式的数据处理"""
    print("🔧 测试process_message格式的数据处理")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import AgentMessage, MessageType
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("process_format_test")
        
        # 测试生辰信息
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"📅 测试生辰信息: {birth_info}")
        
        # 使用process_message方法
        content = {
            "calculation_type": "ziwei",
            "birth_info": birth_info,
            "user_message": "测试原始格式数据处理",
            "session_id": "process_format_session"
        }
        
        message = AgentMessage(
            message_id="process_format_001",
            message_type=MessageType.CALCULATION_REQUEST,
            sender_id="process_format_user",
            receiver_id=calculator_agent.agent_id,
            content=content,
            timestamp=""
        )
        
        print("\n🔄 调用process_message方法...")
        result = await calculator_agent.process_message(message)
        
        if result.success:
            result_id = result.data.get("result_id")
            print(f"✅ 分析记录创建成功: {result_id}")
            
            # 获取缓存结果
            cached_result = calculator_agent.cache.get_result(result_id)
            if cached_result:
                raw_data = cached_result.raw_calculation
                birth_info = cached_result.birth_info
                
                print(f"\n🎯 测试按需生成分析...")
                
                # 测试生成单个角度分析
                analysis_result = await calculator_agent._analyze_single_angle(
                    "命宫分析",
                    "personality_destiny",
                    "性格命运核心特征",
                    raw_data,
                    birth_info,
                    "紫薇+八字融合分析"
                )
                
                if analysis_result and len(analysis_result) > 100:
                    print(f"✅ 按需生成成功: {len(analysis_result)}字")
                    print(f"📝 分析预览: {analysis_result[:200]}...")
                    return True
                else:
                    print(f"❌ 按需生成失败: {analysis_result}")
                    return False
            else:
                print("❌ 无法获取缓存结果")
                return False
        else:
            print(f"❌ process_message失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_fusion_format():
    """测试融合引擎格式的数据处理"""
    print(f"\n🔧 测试融合引擎格式的数据处理")
    print("=" * 40)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("fusion_format_test")
        
        # 测试生辰信息
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        print(f"📅 测试生辰信息: {birth_info}")
        
        # 使用融合引擎
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=int(birth_info["year"]),
            month=int(birth_info["month"]),
            day=int(birth_info["day"]),
            hour=11,
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            print("✅ 融合引擎数据生成成功")
            
            print(f"\n🎯 测试按需生成分析...")
            
            # 测试生成单个角度分析
            analysis_result = await calculator_agent._analyze_single_angle(
                "命宫分析",
                "personality_destiny",
                "性格命运核心特征",
                raw_data,
                birth_info,
                "紫薇+八字融合分析"
            )
            
            if analysis_result and len(analysis_result) > 100:
                print(f"✅ 按需生成成功: {len(analysis_result)}字")
                print(f"📝 分析预览: {analysis_result[:200]}...")
                return True
            else:
                print(f"❌ 按需生成失败: {analysis_result}")
                return False
        else:
            print("❌ 融合引擎数据生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_web_integration_fixed():
    """测试修复后的Web界面集成"""
    print(f"\n💻 测试修复后的Web界面集成")
    print("=" * 40)
    
    try:
        # 模拟streamlit session_state
        class MockSessionState:
            def __init__(self):
                self.data = {}
                self.global_calculator_agent = None
            
            def __getitem__(self, key):
                return self.data.get(key)
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __contains__(self, key):
                return key in self.data
        
        # 创建模拟的streamlit环境
        import sys
        if 'streamlit' not in sys.modules:
            class MockStreamlit:
                session_state = MockSessionState()
            sys.modules['streamlit'] = MockStreamlit()
        
        import streamlit as st
        if not hasattr(st, 'session_state'):
            st.session_state = MockSessionState()
        
        from backend_agent_web import generate_single_analysis
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.base_agent import AgentMessage, MessageType
        
        # 创建全局计算代理
        st.session_state.global_calculator_agent = FortuneCalculatorAgent("web_fixed_test")
        calculator_agent = st.session_state.global_calculator_agent
        
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        # 使用process_message方法（模拟Web界面的实际流程）
        content = {
            "calculation_type": "ziwei",
            "birth_info": birth_info,
            "user_message": "Web界面修复测试",
            "session_id": "web_fixed_session"
        }
        
        message = AgentMessage(
            message_id="web_fixed_001",
            message_type=MessageType.CALCULATION_REQUEST,
            sender_id="web_fixed_user",
            receiver_id=calculator_agent.agent_id,
            content=content,
            timestamp=""
        )
        
        result = await calculator_agent.process_message(message)
        
        if not result.success:
            print("❌ 排盘数据生成失败")
            return False
        
        result_id = result.data.get("result_id")
        print(f"📊 缓存结果ID: {result_id}")
        
        # 测试Web界面的单个分析生成
        print("\n🎯 测试Web界面生成命宫分析...")
        success = generate_single_analysis(result_id, "personality_destiny", "命宫分析 - 性格命运核心特征")
        
        if success:
            print("✅ Web界面单个分析生成成功")
            
            # 验证分析是否保存
            updated_result = calculator_agent.cache.get_result(result_id)
            if updated_result and updated_result.detailed_analysis:
                angle_analyses = updated_result.detailed_analysis.get("angle_analyses", {})
                if "personality_destiny" in angle_analyses:
                    analysis_content = angle_analyses["personality_destiny"]
                    if analysis_content and len(analysis_content) > 100:
                        print(f"✅ 分析内容已保存: {len(analysis_content)}字")
                        return True
                    else:
                        print("❌ 分析内容为空或过短")
                        return False
                else:
                    print("❌ 分析结果未保存到缓存")
                    return False
            else:
                print("❌ 无法获取更新后的缓存结果")
                return False
        else:
            print("❌ Web界面单个分析生成失败")
            return False
            
    except Exception as e:
        print(f"❌ Web界面集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🎯 数据处理器修复最终验证")
    print("=" * 70)
    
    # 1. 测试process_message格式
    process_success = await test_process_message_format()
    
    # 2. 测试融合引擎格式
    fusion_success = await test_fusion_format()
    
    # 3. 测试Web界面集成
    web_success = await test_web_integration_fixed()
    
    print("\n" + "=" * 70)
    print("🎯 数据处理器修复最终验证结果:")
    
    if process_success:
        print("✅ process_message格式数据处理正常")
    else:
        print("❌ process_message格式数据处理异常")
    
    if fusion_success:
        print("✅ 融合引擎格式数据处理正常")
    else:
        print("❌ 融合引擎格式数据处理异常")
    
    if web_success:
        print("✅ Web界面集成功能正常")
    else:
        print("❌ Web界面集成功能异常")
    
    if process_success and fusion_success and web_success:
        print("\n🎉 🎉 🎉 所有数据格式问题修复完成！🎉 🎉 🎉")
        print("💡 修复成果:")
        print("  1. ✅ 支持process_message的原始紫薇数据格式")
        print("  2. ✅ 支持融合引擎的完整数据格式")
        print("  3. ✅ 数据处理器兼容性增强")
        print("  4. ✅ Web界面按需生成功能完全正常")
        print("  5. ✅ 缓存保存和读取功能正常")
        print("\n🚀 现在可以启动Web界面享受完美的用户体验！")
        print("   启动命令: streamlit run backend_agent_web.py")
        print("\n🌟 用户体验流程:")
        print("   1. 输入生辰信息 → 快速生成排盘")
        print("   2. 查看12个分析按钮 → 按需点击生成")
        print("   3. 每个分析可重试 → 确保质量满意")
        print("   4. 即时聊天互动 → 随时提问解答")
    else:
        print("\n⚠️ 还有功能需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
