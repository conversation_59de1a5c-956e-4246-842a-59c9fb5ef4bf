#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复编码问题
"""

import sys
import os
import locale

def fix_encoding_issues():
    """修复编码问题"""
    print("🔧 修复编码问题")
    print("=" * 50)
    
    try:
        # 1. 设置环境变量
        print("1️⃣ 设置环境变量...")
        
        # 设置Python编码
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LANG'] = 'zh_CN.UTF-8'
        os.environ['LC_ALL'] = 'zh_CN.UTF-8'
        
        # 设置Streamlit编码
        os.environ['STREAMLIT_SERVER_ENABLE_STATIC_SERVING'] = 'true'
        os.environ['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
        
        print("✅ 环境变量设置完成")
        
        # 2. 检查系统编码
        print("\n2️⃣ 检查系统编码...")
        
        print(f"  系统默认编码: {sys.getdefaultencoding()}")
        print(f"  文件系统编码: {sys.getfilesystemencoding()}")
        print(f"  标准输出编码: {sys.stdout.encoding}")
        
        try:
            print(f"  本地化设置: {locale.getpreferredencoding()}")
        except:
            print("  本地化设置: 无法获取")
        
        # 3. 修复backend_agent_web.py中的编码问题
        print("\n3️⃣ 修复backend_agent_web.py...")
        
        # 读取文件
        with open("backend_agent_web.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否需要修复
        fixes_needed = []
        
        # 检查文件操作是否指定了编码
        if 'open(' in content and 'encoding=' not in content:
            fixes_needed.append("文件操作未指定编码")
        
        # 检查JSON操作是否设置了ensure_ascii=False
        if 'json.dump(' in content and 'ensure_ascii=False' not in content:
            fixes_needed.append("JSON操作未设置ensure_ascii=False")
        
        if fixes_needed:
            print(f"  发现需要修复的问题: {len(fixes_needed)}个")
            for issue in fixes_needed:
                print(f"    - {issue}")
        else:
            print("  ✅ 未发现编码问题")
        
        # 4. 创建启动脚本
        print("\n4️⃣ 创建安全启动脚本...")
        
        startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全启动脚本 - 解决编码问题
"""

import os
import sys
import locale

def setup_encoding():
    """设置编码环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['LANG'] = 'zh_CN.UTF-8'
    os.environ['LC_ALL'] = 'zh_CN.UTF-8'
    
    # 设置Streamlit环境
    os.environ['STREAMLIT_SERVER_ENABLE_STATIC_SERVING'] = 'true'
    os.environ['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
    os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
    os.environ['STREAMLIT_SERVER_RUN_ON_SAVE'] = 'false'
    
    # 强制设置标准输出编码
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8')

def main():
    """主函数"""
    print("🚀 启动紫薇+八字融合分析系统")
    print("=" * 50)
    
    # 设置编码
    setup_encoding()
    
    # 检查编码设置
    print(f"✅ Python编码: {sys.getdefaultencoding()}")
    print(f"✅ 文件系统编码: {sys.getfilesystemencoding()}")
    print(f"✅ 标准输出编码: {sys.stdout.encoding}")
    
    # 启动Streamlit应用
    import subprocess
    
    try:
        print("\\n🌟 正在启动Web界面...")
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "backend_agent_web.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ], check=True)
    except KeyboardInterrupt:
        print("\\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"\\n❌ 启动失败: {e}")
        print("💡 请尝试手动运行: streamlit run backend_agent_web.py")

if __name__ == "__main__":
    main()
'''
        
        with open("start_safe.py", "w", encoding="utf-8") as f:
            f.write(startup_script)
        
        print("  ✅ 安全启动脚本创建完成: start_safe.py")
        
        # 5. 创建批处理文件（Windows）
        print("\n5️⃣ 创建Windows批处理文件...")
        
        batch_script = '''@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set LANG=zh_CN.UTF-8
set LC_ALL=zh_CN.UTF-8

echo 🚀 启动紫薇+八字融合分析系统
echo ================================================

python start_safe.py

pause
'''
        
        with open("start_safe.bat", "w", encoding="utf-8") as f:
            f.write(batch_script)
        
        print("  ✅ Windows批处理文件创建完成: start_safe.bat")
        
        # 6. 创建Shell脚本（Linux/Mac）
        print("\n6️⃣ 创建Shell脚本...")
        
        shell_script = '''#!/bin/bash
export PYTHONIOENCODING=utf-8
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

echo "🚀 启动紫薇+八字融合分析系统"
echo "================================================"

python3 start_safe.py
'''
        
        with open("start_safe.sh", "w", encoding="utf-8") as f:
            f.write(shell_script)
        
        # 设置执行权限
        try:
            os.chmod("start_safe.sh", 0o755)
            print("  ✅ Shell脚本创建完成: start_safe.sh")
        except:
            print("  ✅ Shell脚本创建完成: start_safe.sh (请手动设置执行权限)")
        
        # 7. 测试emoji字符
        print("\n7️⃣ 测试emoji字符...")
        
        test_emojis = ["🔮", "🎯", "📊", "✅", "❌", "⚠️", "💡", "🚀"]
        
        for emoji in test_emojis:
            try:
                # 测试编码
                encoded = emoji.encode('utf-8')
                decoded = encoded.decode('utf-8')
                if emoji == decoded:
                    print(f"  ✅ {emoji} - 编码正常")
                else:
                    print(f"  ❌ {emoji} - 编码异常")
            except Exception as e:
                print(f"  ❌ {emoji} - 编码错误: {e}")
        
        print("\n🎉 编码问题修复完成！")
        print("\n📋 使用说明:")
        print("  Windows用户: 双击 start_safe.bat")
        print("  Linux/Mac用户: 运行 ./start_safe.sh")
        print("  或者直接运行: python start_safe.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_encoding_issues()
