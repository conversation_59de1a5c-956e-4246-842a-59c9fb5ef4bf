#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试卦象格式输出
"""

from coin_divination import CoinDivination

def test_hexagram_format():
    """测试卦象格式输出"""
    divination = CoinDivination()
    
    # 创建一个测试占卜
    result = divination.divine_with_coins("测试问题")
    
    if result.get("success"):
        print("=== 原始输出格式 ===")
        print(result.get("formatted_output", ""))
        print("\n=== 分析输出格式 ===")
        
        # 分析每一行
        lines = result.get("formatted_output", "").split('\n')
        for i, line in enumerate(lines):
            if line.strip():
                print(f"第{i+1}行: {repr(line)}")
                if '━━━' in line:
                    print(f"  -> 包含卦象符号")
                if '│' in line:
                    print(f"  -> 包含表格分隔符")
                if '○' in line:
                    print(f"  -> 包含动爻标记")
    else:
        print("占卜失败:", result.get("error"))

if __name__ == "__main__":
    test_hexagram_format()
