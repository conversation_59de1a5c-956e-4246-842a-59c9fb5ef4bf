# 独立排盘模块

这是一个从主项目提炼出来的独立紫薇斗数+八字命理排盘模块，专门用于处理单人的排盘计算，确保不影响主项目的稳定运行。

## 功能特点

- ✅ **直接提炼**: 直接使用主项目的 `ZiweiBaziFusionEngine` 融合引擎
- ✅ **算法准确**: 使用主项目验证过的紫薇斗数和八字算法
- ✅ **简单易用**: 提供简洁的输入输出接口
- ✅ **结果保存**: 自动保存计算结果到文件
- ✅ **多种格式**: 支持JSON、TXT、摘要等多种输出格式
- ✅ **完整数据**: 包含融合分析、交叉验证等完整功能

## 文件结构

```
standalone_paipan/
├── __init__.py              # 模块初始化
├── core_calculator.py       # 核心排盘计算器
├── result_saver.py         # 结果保存模块
├── simple_interface.py     # 简单使用接口
├── test_standalone.py      # 测试脚本
├── README.md              # 说明文档
└── paipan_outputs/        # 输出目录（自动创建）
```

## 快速开始

### 1. 快速演示

```bash
cd standalone_paipan
python quick_demo.py
```

### 2. 命令行交互模式

```bash
cd standalone_paipan
python simple_interface.py
```

然后按照提示输入出生信息即可。

### 3. 编程接口使用

```python
from simple_interface import SimplePaipanInterface

# 创建接口
interface = SimplePaipanInterface()

# 方式1: 直接输入数值
result = interface.calculate_and_save(1990, 3, 15, 8, "女")

# 方式2: 字符串快速输入
result = interface.quick_calculate("1990年3月15日8时", "女")

# 打印结果
interface.print_result(result)
```

### 4. 仅使用核心计算器

```python
from core_calculator import StandalonePaipanCalculator

# 创建计算器（基于融合引擎）
calc = StandalonePaipanCalculator()

# 计算排盘
result = calc.calculate_complete_paipan(1990, 3, 15, 8, "女")

# 格式化输出
formatted_text = calc.format_output(result)
print(formatted_text)
```

## 输入格式

### 数值输入
- **年份**: 1900-2100之间的整数
- **月份**: 1-12
- **日期**: 1-31
- **小时**: 0-23 (24小时制)
- **性别**: "男" 或 "女"

### 字符串输入
支持以下格式：
- `1990-3-15-8`
- `1990年3月15日8时`
- `1990/3/15/8`

## 输出内容

### 1. 紫薇斗数部分
- 阳历、农历、干支信息
- 生肖、星座
- 十二宫星曜分布
- 命宫、身宫标识

### 2. 八字命理部分
- 出生时间信息
- 四柱八字（年月日时柱）
- 五行分析

### 3. 保存文件
- **JSON文件**: 完整的计算数据
- **TXT文件**: 格式化的可读输出
- **摘要文件**: 简化的关键信息

## 测试

运行测试脚本验证功能：

```bash
cd standalone_paipan
python test_standalone.py
```

测试内容包括：
- 基本排盘计算
- 快速计算功能
- 错误处理机制
- 文件操作功能

## 示例输出

```
============================================================
紫薇斗数+八字命理 完整排盘
============================================================
出生时间: 1990年3月15日8时
性别: 女
计算时间: 2024-06-25T01:00:00

【紫薇斗数排盘】
----------------------------------------
阳历: 1990年3月15日8时
农历: 庚午年二月十九日辰时
生肖: 马
星座: 双鱼座

十二宫星曜分布:
  命宫(寅): 主星: 紫微, 天府 | 辅星: 左辅, 右弼
  兄弟宫(丑): 主星: 武曲, 七杀
  夫妻宫(子): 主星: 太阳, 太阴
  ...

【八字命理排盘】
----------------------------------------
出生时间: 1990年3月15日8时0分
八字: 庚午 己卯 丁酉 甲辰
年柱: 庚午
月柱: 己卯
日柱: 丁酉
时柱: 甲辰

五行分析:
  金: 2
  木: 2
  水: 0
  火: 2
  土: 2
============================================================
```

## 注意事项

1. **依赖关系**: 本模块依赖主项目的算法模块，确保主项目的 `algorithms/` 目录可访问
2. **输出目录**: 默认输出到 `paipan_outputs/` 目录，会自动创建
3. **错误处理**: 如果某个算法失败，会继续执行另一个算法，并在结果中标明错误信息
4. **文件编码**: 所有输出文件使用UTF-8编码，确保中文显示正常

## 故障排除

### 常见问题

1. **算法初始化失败**
   - 检查主项目的 `algorithms/` 目录是否存在
   - 确认 `py-iztro` 等依赖包已安装

2. **文件保存失败**
   - 检查输出目录的写入权限
   - 确认磁盘空间充足

3. **计算结果异常**
   - 验证输入的出生时间是否合理
   - 检查时区设置是否正确

### 调试模式

在代码中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 版本信息

- **版本**: 1.0.0
- **创建时间**: 2024-06-25
- **兼容性**: Python 3.7+
- **依赖**: 主项目的算法模块
