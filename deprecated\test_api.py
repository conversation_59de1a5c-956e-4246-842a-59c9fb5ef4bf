#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇算命API测试脚本
测试SiliconFlow DeepSeek-R1模型的集成
"""

import requests
import json

def test_local_api():
    """测试本地API服务"""
    print("🔮 测试紫薇算命API服务...")
    print("=" * 50)

    base_url = "http://127.0.0.1:8001"

    # 测试模型列表
    print("1. 测试模型列表:")
    try:
        response = requests.get(f"{base_url}/v1/models")
        if response.status_code == 200:
            models = response.json()
            print(f"✅ 可用模型: {models['data'][0]['id']}")
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return

    print("\n2. 测试算命对话:")

    # 测试算命问题
    test_questions = [
        "请帮我算一下今天的运势如何？",
        "我想知道我的事业发展前景",
        "请为我占卜一下感情运势"
    ]

    for i, question in enumerate(test_questions, 1):
        print(f"\n问题 {i}: {question}")
        print("-" * 30)

        messages = [
            {
                "role": "system",
                "content": "你是一位专业的紫薇算命师，拥有深厚的占卜知识和丰富的经验。请提供专业、详细且富有神秘色彩的占卜解答。"
            },
            {
                "role": "user",
                "content": question
            }
        ]

        data = {
            "model": "deepseek-ai/DeepSeek-V3",
            "messages": messages,
            "stream": False,
            "max_tokens": 500,
            "temperature": 0.8,
            "top_p": 0.8
        }

        try:
            print("⏳ 正在等待DeepSeek-V3响应...")
            response = requests.post(f"{base_url}/v1/chat/completions", json=data, timeout=60)
            if response.status_code == 200:
                result = response.json()
                answer = result["choices"][0]["message"]["content"]
                print(f"✅ 回答: {answer[:200]}...")
                print(f"📊 Token使用: {result.get('usage', {})}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def test_direct_api():
    """直接测试SiliconFlow API"""
    print("\n🌐 直接测试SiliconFlow API...")
    print("=" * 50)

    headers = {
        "Authorization": "Bearer sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk",
        "Content-Type": "application/json"
    }

    data = {
        "model": "deepseek-ai/DeepSeek-V3",
        "messages": [
            {
                "role": "system",
                "content": "你是一位专业的紫薇算命师。"
            },
            {
                "role": "user",
                "content": "请简单介绍一下紫薇斗数"
            }
        ],
        "max_tokens": 300,
        "temperature": 0.7
    }

    try:
        print("⏳ 正在等待DeepSeek-V3响应...")
        response = requests.post(
            "https://api.siliconflow.cn/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=60  # DeepSeek-V3响应较快
        )

        if response.status_code == 200:
            result = response.json()
            answer = result["choices"][0]["message"]["content"]
            print(f"✅ 直接API调用成功!")
            print(f"回答: {answer[:200]}...")
        else:
            print(f"❌ 直接API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 直接API调用异常: {e}")

if __name__ == "__main__":
    print("🔮✨ 紫薇算命大师 - API测试工具 ✨🔮")
    print("基于SiliconFlow DeepSeek-V3模型")
    print("=" * 60)

    # 测试本地API服务
    test_local_api()

    # 测试直接API调用
    test_direct_api()

    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    print("💡 如果本地API测试成功，可以访问 http://localhost:8501 使用Web界面")
    print("💡 如果需要停止服务，请在对应终端按 Ctrl+C")
