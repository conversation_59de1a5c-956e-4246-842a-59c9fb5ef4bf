#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web接口 - 基于新架构的Streamlit Web界面
"""

import streamlit as st
import requests
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class WebInterface:
    """Web接口 - Streamlit界面"""

    def __init__(self, api_url: str = "http://localhost:8002"):
        """
        初始化Web接口

        Args:
            api_url: API服务器地址
        """
        self.api_url = api_url
        self.session_id = self._get_session_id()

        # 配置页面
        st.set_page_config(
            page_title="智能算命AI助手",
            page_icon="🔮",
            layout="wide",
            initial_sidebar_state="expanded"
        )

    def _get_session_id(self) -> str:
        """获取或创建会话ID"""
        if "session_id" not in st.session_state:
            st.session_state.session_id = str(uuid.uuid4())
        return st.session_state.session_id

    def _call_api(self, endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict[str, Any]:
        """调用API"""

        try:
            url = f"{self.api_url}{endpoint}"

            if method == "POST":
                response = requests.post(url, json=data, timeout=60)
            elif method == "DELETE":
                response = requests.delete(url, timeout=10)
            else:
                response = requests.get(url, timeout=10)

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"API调用失败: {e}")
            return {"error": f"API调用失败: {str(e)}"}
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return {"error": f"未知错误: {str(e)}"}

    def render_sidebar(self):
        """渲染侧边栏"""

        with st.sidebar:
            st.title("🔮 智能算命AI")
            st.markdown("---")

            # 功能介绍
            st.subheader("📋 支持功能")
            st.markdown("""
            • **紫薇+八字融合分析** - 双重算法相互印证
            • **HTML可视化图表** - 现代化命盘展示
            • **12角度详细分析** - 专业深度解读
            • **智能对话** - 多轮交互
            """)

            st.markdown("---")

            # 会话管理
            st.subheader("💬 会话管理")

            if st.button("🗑️ 清除会话", use_container_width=True):
                result = self._call_api(f"/v2/session/{self.session_id}", "DELETE")
                if result.get("success"):
                    st.success("会话已清除")
                    st.session_state.messages = []
                    st.rerun()
                else:
                    st.error(f"清除失败: {result.get('error', '未知错误')}")

            # 会话信息
            session_info = self._call_api(f"/v2/session/{self.session_id}")
            if not session_info.get("error"):
                st.info(f"消息数: {session_info.get('message_count', 0)}")

            st.markdown("---")

            # 系统状态
            st.subheader("📊 系统状态")

            if st.button("🔄 刷新状态", use_container_width=True):
                st.rerun()

            # 获取系统统计
            stats = self._call_api("/v2/stats")
            if not stats.get("error"):
                session_stats = stats.get("session_stats", {})
                tool_stats = stats.get("tool_stats", {})

                st.metric("活跃会话", session_stats.get("active_sessions", 0))
                st.metric("总执行次数", tool_stats.get("total_executions", 0))
                st.metric("成功率", tool_stats.get("success_rate", "0%"))

    def render_main_content(self):
        """渲染主要内容"""

        st.title("🔮 智能算命AI助手")
        st.markdown("基于紫薇+八字融合算法的专业算命系统，双重印证确保准确性")

        # 初始化消息历史
        if "messages" not in st.session_state:
            st.session_state.messages = []

        # 显示消息历史
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])

                # 显示HTML图表
                if message.get("chart_html"):
                    st.components.v1.html(message["chart_html"], height=800, scrolling=True)

                # 显示传统图片（兼容）
                elif message.get("image"):
                    st.image(message["image"], caption="算命图表")

                # 显示工具信息
                if message.get("tool_used"):
                    st.info(f"使用工具: {message['tool_used']}")

        # 聊天输入
        if prompt := st.chat_input("请输入您的问题或出生信息..."):
            # 添加用户消息
            st.session_state.messages.append({"role": "user", "content": prompt})

            with st.chat_message("user"):
                st.markdown(prompt)

            # 调用API获取回复
            with st.chat_message("assistant"):
                with st.spinner("正在分析中..."):
                    response = self._call_api("/v2/chat", "POST", {
                        "message": prompt,
                        "session_id": self.session_id
                    })

                if response.get("success"):
                    message_content = response.get("message", "")
                    st.markdown(message_content)

                    # 准备助手消息
                    assistant_message = {
                        "role": "assistant",
                        "content": message_content
                    }

                    # 显示HTML图表
                    if response.get("chart_html"):
                        try:
                            st.components.v1.html(response["chart_html"], height=800, scrolling=True)
                            assistant_message["chart_html"] = response["chart_html"]
                        except Exception as e:
                            st.warning(f"HTML图表显示失败: {e}")

                    # 显示传统图片（兼容）
                    elif response.get("chart_image"):
                        try:
                            st.image(response["chart_image"], caption="算命图表")
                            assistant_message["image"] = response["chart_image"]
                        except Exception as e:
                            st.warning(f"图片显示失败: {e}")

                    # 显示工具信息
                    if response.get("tool_used"):
                        st.info(f"使用工具: {response['tool_used']}")
                        assistant_message["tool_used"] = response["tool_used"]

                    # 添加助手消息
                    st.session_state.messages.append(assistant_message)

                else:
                    error_message = f"❌ 处理失败: {response.get('error', '未知错误')}"
                    st.error(error_message)
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": error_message
                    })

    def render_examples(self):
        """渲染示例"""

        st.markdown("---")

        with st.expander("💡 使用示例", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                st.subheader("🌟 紫薇+八字融合分析")
                st.markdown("""
                **示例输入:**
                - "我想看命盘分析"
                - "1988年6月1日午时男，帮我分析"
                - "分析我的事业运势"
                - "看看我的财运和婚姻"
                """)

            with col2:
                st.subheader("🎯 专业功能")
                st.markdown("""
                **系统特色:**
                - 紫薇斗数+八字双重印证
                - HTML可视化命盘图表
                - 12角度详细分析
                - 性别年龄差异化分析
                """)

    def render_api_status(self):
        """渲染API状态"""

        # 检查API健康状态
        health = self._call_api("/health")

        if health.get("status") == "healthy":
            st.success("🟢 API服务正常")
        else:
            st.error("🔴 API服务异常")
            st.json(health)

    def run(self):
        """运行Web界面"""

        # 渲染侧边栏
        self.render_sidebar()

        # 渲染主要内容
        self.render_main_content()

        # 渲染示例
        self.render_examples()

        # 底部状态
        with st.container():
            st.markdown("---")
            col1, col2 = st.columns([3, 1])

            with col1:
                st.markdown("**🎯 智能算命AI助手 v2.0** - 基于新架构的智能对话系统")

            with col2:
                self.render_api_status()

def main():
    """主函数"""

    # 配置日志
    logging.basicConfig(level=logging.INFO)

    # 创建并运行Web界面
    web_interface = WebInterface()
    web_interface.run()

if __name__ == "__main__":
    main()
