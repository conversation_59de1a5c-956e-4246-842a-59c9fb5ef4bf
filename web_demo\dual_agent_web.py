#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双Agent协作Web界面 - 智能算命AI系统
"""

import streamlit as st
import asyncio
import time
import json
from datetime import datetime
from typing import Dict, Any, List
import sys
import os

# 添加项目路径
sys.path.append('.')

# 页面配置
st.set_page_config(
    page_title="智能算命AI - 双Agent协作版",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 导入正确的双Agent组件
try:
    from core.agents.master_customer_agent import MasterCustomerAgent
    from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
    from core.agents.simple_coordinator import SimpleCoordinator
    from core.agents.base_agent import agent_registry
    from core.storage.session_manager import SessionManager, UserSettings
    AGENTS_AVAILABLE = True
except ImportError as e:
    st.error(f"双Agent组件导入失败: {e}")
    AGENTS_AVAILABLE = False

# CSS样式
st.markdown("""
<style>
/* 双Agent主题样式 */
.dual-agent-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
    border-radius: 15px;
    color: white;
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.agent-status-card {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin: 0.5rem 0;
    backdrop-filter: blur(10px);
}

.agent-working {
    border-left: 4px solid #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.agent-idle {
    border-left: 4px solid #6c757d;
    background: rgba(108, 117, 125, 0.1);
}

.agent-error {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.processing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    margin: 0.5rem 0;
}

.response-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.stat-card {
    background: rgba(255, 255, 255, 0.08);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}
</style>
""", unsafe_allow_html=True)

# 初始化双Agent系统
@st.cache_resource
def init_dual_agent_system():
    """初始化正确的双Agent协作系统"""
    if not AGENTS_AVAILABLE:
        return None, None, None, None

    try:
        # 创建正确的Agent实例
        master_agent = MasterCustomerAgent()  # 主控沟通Agent
        calculator_agent = FortuneCalculatorAgent()  # 计算Agent
        coordinator = SimpleCoordinator()  # 简化协调器
        session_manager = SessionManager()

        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)

        st.success("✅ 正确的双Agent协作系统初始化成功")
        st.info("🎯 架构: 沟通Agent主导 → 收集信息 → 按需调用计算Agent")
        return master_agent, calculator_agent, coordinator, session_manager
    except Exception as e:
        st.error(f"双Agent系统初始化失败: {e}")
        return None, None, None, None

def display_agent_status(master_agent, calculator_agent, coordinator):
    """显示Agent状态"""
    st.sidebar.markdown("## 🤖 Agent状态监控")

    # 主控沟通Agent状态
    master_stats = master_agent.get_stats() if master_agent else {}
    master_status = master_stats.get("status", "idle")

    status_class = "agent-working" if master_status == "processing" else "agent-idle"
    st.sidebar.markdown(f"""
    <div class="agent-status-card {status_class}">
        <h4>🗣️ 主控沟通Agent</h4>
        <p>状态: {master_status}</p>
        <p>处理消息: {master_stats.get('messages_processed', 0)}</p>
        <p>平均耗时: {master_stats.get('average_processing_time', 0):.2f}s</p>
        <p>角色: 主导对话流程</p>
    </div>
    """, unsafe_allow_html=True)

    # 计算Agent状态
    calculator_stats = calculator_agent.get_stats() if calculator_agent else {}
    calculator_status = calculator_stats.get("status", "standby")

    status_class = "agent-working" if calculator_status == "processing" else "agent-idle"
    st.sidebar.markdown(f"""
    <div class="agent-status-card {status_class}">
        <h4>🧮 计算Agent</h4>
        <p>状态: {calculator_status}</p>
        <p>处理消息: {calculator_stats.get('messages_processed', 0)}</p>
        <p>平均耗时: {calculator_stats.get('average_processing_time', 0):.2f}s</p>
        <p>角色: 按需计算服务</p>
    </div>
    """, unsafe_allow_html=True)

    # 简化协调器状态
    coordinator_stats = coordinator.get_stats() if coordinator else {}
    success_rate = coordinator_stats.get('success_rate', 0) * 100
    st.sidebar.markdown(f"""
    <div class="agent-status-card">
        <h4>🎯 简化协调器</h4>
        <p>总请求: {coordinator_stats.get('total_requests', 0)}</p>
        <p>成功: {coordinator_stats.get('successful_requests', 0)}</p>
        <p>成功率: {success_rate:.1f}%</p>
        <p>平均耗时: {coordinator_stats.get('average_response_time', 0):.2f}s</p>
        <p>角色: 路由到主控Agent</p>
    </div>
    """, unsafe_allow_html=True)

def display_processing_indicator(message: str):
    """显示处理指示器"""
    st.markdown(f"""
    <div class="processing-indicator">
        <div style="width: 20px; height: 20px; border: 2px solid #667eea; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        <span>{message}</span>
    </div>
    <style>
    @keyframes spin {{
        0% {{ transform: rotate(0deg); }}
        100% {{ transform: rotate(360deg); }}
    }}
    </style>
    """, unsafe_allow_html=True)

async def process_user_message_async(coordinator, session_id: str, user_message: str):
    """异步处理用户消息"""
    try:
        # 确保session_id正确传递
        result = await coordinator.handle_user_message(session_id, user_message)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response": "抱歉，处理您的消息时遇到了问题。"
        }

def run_async_task(coro):
    """运行异步任务"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return loop.run_until_complete(coro)

def main():
    """主函数"""
    # 页面标题
    st.markdown("""
    <div class="dual-agent-header">
        <h1>🤖 智能算命AI - 双Agent协作版</h1>
        <p>沟通Agent + 计算Agent = 专业分工，极致体验</p>
    </div>
    """, unsafe_allow_html=True)

    # 初始化系统
    agents = init_dual_agent_system()

    if not all(agents):
        st.error("❌ 双Agent系统初始化失败，无法提供服务")
        st.info("请检查系统配置和依赖")
        return

    master_agent, calculator_agent, coordinator, session_manager = agents

    # 初始化会话（持久化）
    if "session_id" not in st.session_state:
        user_id = f"user_{int(datetime.now().timestamp())}"
        user_settings = UserSettings()
        session_data = session_manager.create_session(user_id, user_settings)
        st.session_state.session_id = session_data.session_id
        st.session_state.user_id = session_data.user_id
        st.session_state.messages = []

        # 在主控Agent中初始化会话状态
        master_agent.set_session_state(st.session_state.session_id, {
            "stage": "greeting",
            "birth_info": {},
            "calculation_type": None,
            "calculation_result": None,
            "conversation_history": [],
            "session_id": st.session_state.session_id
        })

        st.info(f"🆔 会话ID: {st.session_state.session_id[:8]}... (已初始化)")

    # 显示Agent状态
    display_agent_status(master_agent, calculator_agent, coordinator)

    # 侧边栏控制
    with st.sidebar:
        st.markdown("---")
        st.markdown("## ⚙️ 系统控制")

        if st.button("🔄 重启系统", type="secondary"):
            st.cache_resource.clear()
            st.rerun()

        if st.button("🗑️ 新会话", type="primary"):
            for key in ["session_id", "messages"]:
                if key in st.session_state:
                    del st.session_state[key]
            st.rerun()

        # 系统统计
        st.markdown("---")
        st.markdown("## 📊 系统统计")

        if coordinator:
            stats = coordinator.get_stats()
            success_rate = stats.get('success_rate', 0) * 100
            st.metric("总请求数", stats.get('total_requests', 0))
            st.metric("成功率", f"{success_rate:.1f}%")
            st.metric("平均响应时间", f"{stats.get('average_response_time', 0):.2f}s")

    # 主界面
    st.markdown("## 💬 智能对话")

    # 显示欢迎信息
    if not st.session_state.messages:
        st.markdown("""
        <div class="response-section">
            <h3>🌟 欢迎体验正确的双Agent协作系统</h3>
            <p>我们的系统采用了正确的双Agent协作架构：</p>
            <ul>
                <li>🗣️ <strong>主控沟通Agent</strong>：主导对话流程，智能收集信息</li>
                <li>🧮 <strong>计算Agent</strong>：按需提供精确的算命计算服务</li>
                <li>🎯 <strong>简化协调</strong>：直接路由，避免复杂性</li>
            </ul>
            <p><strong>正确流程</strong>: 沟通 → 收集信息 → 调用计算 → 解释结果</p>
            <p>请开始对话，体验自然流畅的算命服务！</p>
        </div>
        """, unsafe_allow_html=True)

    # 显示聊天历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

            # 显示Agent信息
            if message["role"] == "assistant" and "agent_info" in message:
                agent_info = message["agent_info"]
                st.caption(f"🤖 协作Agent: {agent_info.get('customer_agent', 'N/A')} + {agent_info.get('calculator_agent', 'N/A')}")

    # 用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        # 显示用户消息
        with st.chat_message("user"):
            st.markdown(prompt)

        # 添加到历史
        st.session_state.messages.append({"role": "user", "content": prompt})

        # 保存用户消息
        session_manager.add_message(
            st.session_state.session_id,
            "user",
            prompt,
            "text"
        )

        # 显示处理指示器
        with st.chat_message("assistant"):
            processing_placeholder = st.empty()

            with processing_placeholder:
                display_processing_indicator("🤖 双Agent协作处理中...")

            # 异步处理用户消息（不阻塞界面）
            start_time = time.time()

            try:
                # 快速调用双Agent系统
                result = run_async_task(
                    process_user_message_async(coordinator, st.session_state.session_id, prompt)
                )

                processing_time = time.time() - start_time

                # 清除处理指示器
                processing_placeholder.empty()

                if result.get("success"):
                    response = result.get("response", "")

                    # 显示响应
                    st.markdown(response)

                    # 检查是否启动了后台分析
                    session_state = master_agent.get_session_state(st.session_state.session_id)
                    analysis_started = session_state and session_state.get("stage") == "analysis_started"

                    # 显示处理信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.caption(f"⏱️ 响应时间: {processing_time:.2f}s")
                    with col2:
                        if analysis_started:
                            st.caption("🔮 后台分析已启动")
                        else:
                            st.caption("💬 对话回复")
                    with col3:
                        if session_state and session_state.get("result_id"):
                            result_id = session_state["result_id"]
                            st.caption(f"📋 分析ID: {result_id[:8]}...")
                        else:
                            st.caption("🎯 会话进行中")

                    # 如果启动了后台分析，显示进度
                    if analysis_started and session_state.get("result_id"):
                        with st.expander("📊 分析进度（实时更新）", expanded=False):
                            progress_placeholder = st.empty()

                            # 显示当前进度
                            progress = master_agent.get_analysis_progress(session_state["result_id"])
                            completed = progress.get("completed_angles", 0)
                            total = progress.get("total_angles", 12)
                            total_words = progress.get("total_word_count", 0)

                            progress_placeholder.markdown(f"""
                            **后台分析进度**: {completed}/{total} 个角度完成

                            **总字数**: {total_words}

                            **状态**: 您可以随时提问，我会基于已完成的分析回答
                            """)

                    # 添加到历史
                    message_data = {
                        "role": "assistant",
                        "content": response,
                        "processing_time": processing_time,
                        "analysis_started": analysis_started
                    }

                    st.session_state.messages.append(message_data)

                    # 保存助手回复
                    session_manager.add_message(
                        st.session_state.session_id,
                        "assistant",
                        response,
                        "analysis" if "calculation_result" in result else "text",
                        {
                            "processing_time": processing_time,
                            "task_id": result.get("task_id"),
                            "agent_info": result.get("agent_info", {})
                        }
                    )

                else:
                    error_msg = result.get("error", "未知错误")
                    response = result.get("response", "抱歉，处理您的请求时遇到了问题。")

                    st.error(f"处理失败: {error_msg}")
                    st.markdown(response)

                    # 添加到历史
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": response,
                        "error": error_msg
                    })

            except Exception as e:
                processing_placeholder.empty()
                st.error(f"系统错误: {e}")
                st.markdown("抱歉，系统遇到了问题，请稍后再试。")

if __name__ == "__main__":
    main()
