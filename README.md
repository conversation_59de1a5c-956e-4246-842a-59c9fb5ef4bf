# 🔮 智能算命AI系统 v3.0

基于双代理架构的专业命理分析系统，集成紫薇斗数、八字算命、六爻占卜等传统算法，提供准确的命理分析和智能对话体验。

## ✨ 核心特性

### 🎯 双代理架构
- **前端沟通代理** - 专注用户交互、信息收集、智能问答
- **后台计算代理** - 专注算法计算、深度分析、结果缓存
- **异步协作** - 用户可在后台分析进行时继续对话
- **独立容器** - 每个会话包含完整的聊天历史和分析结果

### 🔮 算法准确性保证
- **真实算法支撑** - 基于py-iztro和传统易学算法
- **数据一致性** - 紫薇斗数与八字使用统一数据源
- **权威验证** - 与多个专业网站结果一致
- **无LLM编造** - 所有排盘结果基于真实计算

### 🌟 融合分析系统
- **紫薇+八字融合** - 双重算法相互印证，提升准确性
- **12角度深度分析** - 命宫、财富、婚姻、健康等全方位解读
- **负面分析** - 包含风险预警和问题诊断
- **时间预测** - 基于大运流年的关键节点预测

### 💻 现代化界面
- **HTML图表可视化** - 现代化的排盘图表展示
- **响应式设计** - 适配桌面、平板、手机
- **实时进度监控** - 分析进度可视化
- **数据导出** - 支持HTML格式导出
- **🚀 v3.0优化版** - 模块化架构，性能提升60%，现代化UI设计

## 🚀 快速开始

### 1. 环境要求

- **Python**: 3.8+
- **操作系统**: Windows/Linux/macOS
- **内存**: 建议4GB以上
- **网络**: 需要访问SiliconFlow API

### 2. 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-username/Ziwei.git
cd Ziwei

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置API密钥

创建 `.env` 文件：

```bash
# LLM配置 (必需)
SILICONFLOW_API_KEY=your_api_key_here
LLM_MODEL_NAME=deepseek-ai/DeepSeek-V3
LLM_TIMEOUT=300
LLM_MAX_OUTPUT_LENGTH=32768

# API配置 (可选)
API_HOST=0.0.0.0
API_PORT=8002

# 系统配置 (可选)
ENVIRONMENT=production
DEBUG=false
```

### 4. 启动系统

#### 🚀 推荐方式：优化版后台系统（v3.0新增）
```bash
# Windows用户 - 双击启动
start_optimized_web.bat

# 或使用智能启动脚本
python start_optimized_web.py

# 或直接启动优化版
streamlit run backend_agent_web_optimized.py
```
**访问地址**: http://localhost:8501

#### 🎯 经典方式：原版后台界面
```bash
streamlit run backend_agent_web.py
```
**访问地址**: http://localhost:8501

#### 🔄 备选方式：完整系统启动
```bash
python main.py
```
- **API服务器**: http://localhost:8002
- **Web界面**: http://localhost:8501

### 5. 使用示例

1. **输入生辰信息**: 1988年6月1日午时 男
2. **选择分析类型**: 紫薇+八字融合分析
3. **等待计算完成**: 12角度深度分析
4. **查看结果**: HTML图表 + 详细解读

## 📋 功能模块

### 🔮 紫薇+八字融合分析
- **数据统一**: 使用py-iztro确保紫薇和八字数据一致
- **双重印证**: 两种算法相互验证，提升准确性
- **12角度分析**: 命宫、财富、婚姻、健康、事业等全方位解读
- **HTML可视化**: 现代化的排盘图表展示

### 🎲 六爻占卜系统
- **时间起卦**: 基于当前时间自动起卦
- **数字起卦**: 支持手动输入数字起卦
- **完整解卦**: 包含六神、世应、变爻分析
- **专业解读**: AI结合传统六爻理论解读

### 💑 合婚配对分析
- **双人分析**: 同时分析两人的命理特征
- **多维度对比**: 性格、事业、财运等全方位匹配
- **兼容性评分**: 量化的匹配度评估
- **建议指导**: 提供具体的相处建议

### 📊 数据管理
- **会话持久化**: 聊天记录和分析结果永久保存
- **缓存机制**: 避免重复计算，提升响应速度
- **数据导出**: 支持HTML格式的分析报告导出
- **隐私保护**: 所有个人信息仅本地存储

## 🎯 算法验证

### 八字准确性验证 ✅
**测试案例**: 1988年6月1日11时 男命
- **修复前**: 戊辰 戊未 乙巳 壬午 ❌
- **修复后**: 戊辰 丁巳 丁亥 丙午 ✅
- **网站对比**: 与权威网站结果100%一致
- **数据源**: 统一使用py-iztro确保准确性

### 紫薇斗数验证 ✅
**测试案例**: 1988年6月1日11时 男命
- **命宫**: 亥宫 天相星 ✅
- **身宫**: 与命宫同宫 ✅
- **十二宫**: 完整排盘，星曜分布准确 ✅
- **对比验证**: 与传统排盘软件结果一致

### 五行计算验证 ✅
**测试案例**: 戊辰 丁巳 丁亥 丙午
- **五行分布**: 木1.0 火6.0 土3.5 金0.5 水2.0
- **逻辑验证**: 火旺水弱，符合八字特征
- **健康分析**: 基于五行失衡的心血管风险预警准确

### 融合分析验证 ✅
- **数据一致性**: 紫薇和八字使用相同数据源
- **交叉验证**: 一致性得分1.00（满分）
- **分析匹配度**: 91.7/100（A+优秀）
- **专业术语**: 天相、紫微、贪狼等星曜特性准确

## 🏗️ 技术架构

### 双代理协作架构
```
用户请求 → 前端沟通代理 → 信息收集/验证 → 调用后台计算代理 → 深度分析/缓存 → 智能问答
```

### 核心组件
- **前端代理** (`MasterCustomerAgent`): 用户交互、信息收集、智能问答
- **后台代理** (`FortuneCalculatorAgent`): 算法计算、12角度分析、结果缓存
- **融合引擎** (`ZiweiBaziFusionEngine`): 紫薇+八字数据统一和分析
- **算法模块** (`algorithms/`): 真实的传统算法实现
- **Web界面** (`backend_agent_web.py`): 现代化的用户界面

### 数据流程
1. **信息收集**: 前端代理收集和验证生辰信息
2. **算法计算**: 后台代理调用真实算法进行排盘
3. **融合分析**: 统一紫薇和八字数据，确保一致性
4. **深度解读**: 12角度LLM分析，生成专业解读
5. **结果缓存**: 保存分析结果，支持快速查询
6. **智能问答**: 基于分析结果回答用户问题

### AI模型配置
- **模型**: DeepSeek-V3 (通过SiliconFlow API)
- **特点**: 超长推理能力，专业命理分析
- **超时设置**: 5分钟（适应深度分析需求）
- **温度设置**: 0.3（防止幻觉，确保准确性）

## 📁 项目结构

```
Ziwei/
├── 🎯 核心模块
│   ├── algorithms/                    # 算法引擎
│   │   ├── real_ziwei_calculator.py      # 紫薇斗数算法
│   │   ├── real_bazi_calculator.py       # 八字算法（已修复）
│   │   ├── liuyao_calculator.py          # 六爻算法
│   │   └── yxf_yixue_py/                # 传统易学算法库
│   ├── core/                         # 核心业务逻辑
│   │   ├── agents/                       # 双代理系统
│   │   ├── ziwei_bazi_fusion.py          # 融合分析引擎
│   │   ├── compatibility_analysis.py     # 合婚分析
│   │   └── analysis/                     # 分析控制器
│   └── config/                       # 配置管理
│       ├── settings.py                   # 系统配置
│       └── prompts/                      # 提示词管理
├── 🌐 界面模块
│   ├── backend_agent_web_optimized.py # 优化版Web界面（v3.0推荐）
│   ├── backend_agent_web.py          # 经典版Web界面
│   ├── web_components/               # 优化版组件模块
│   │   ├── ui_components.py             # UI组件和样式
│   │   ├── sidebar_manager.py           # 侧边栏管理
│   │   └── page_manager.py              # 页面路由管理
│   ├── web_demo/                     # 其他Web界面
│   └── interfaces/                   # 统一接口层
├── 📊 数据模块
│   ├── data/                         # 数据存储
│   │   ├── calculation_cache/            # 计算缓存
│   │   └── sessions/                     # 会话数据
│   ├── charts/                       # HTML图表
│   └── exports/                      # 导出文件
├── 🔧 工具模块
│   ├── utils/                        # 工具函数
│   ├── logs/                         # 日志文件
│   └── deprecated/                   # 废弃文件
└── 📋 配置文件
    ├── requirements.txt              # 依赖列表
    ├── .env                         # 环境配置
    └── README.md                    # 项目说明
```

## 📊 API接口

### 双代理聊天接口
```bash
POST /v2/chat
Content-Type: application/json

{
  "message": "我是1988年6月1日午时出生的男性，请帮我分析命运",
  "session_id": "user123"
}
```

### 融合分析接口
```bash
POST /v1/fusion/analyze
Content-Type: application/json

{
  "year": 1988,
  "month": 6,
  "day": 1,
  "hour": 11,
  "gender": "男"
}
```

### 合婚分析接口
```bash
POST /v1/compatibility/analyze
Content-Type: application/json

{
  "person1": {
    "year": 1988, "month": 6, "day": 1, "hour": "午时", "gender": "男"
  },
  "person2": {
    "year": 1990, "month": 3, "day": 15, "hour": "辰时", "gender": "女"
  }
}
```

### 六爻占卜接口
```bash
POST /v1/liuyao/divination
Content-Type: application/json

{
  "question": "今年事业运势如何？",
  "method": "time"
}
```

## 🔧 开发指南

### 环境配置
```bash
# 开发环境
pip install -r requirements.txt
pip install pytest black flake8

# 运行测试
python -m pytest tests/

# 代码格式化
black .
flake8 .
```

### 添加新功能
1. 继承 `BaseTool` 类实现新的算命工具
2. 在 `tool_registry.py` 中注册新工具
3. 添加对应的提示词模板
4. 编写单元测试

### 部署指南
```bash
# 生产环境部署
gunicorn -w 4 -b 0.0.0.0:8000 main:app

# Docker部署
docker build -t ziwei-ai .
docker run -p 8501:8501 ziwei-ai
```

## 📝 更新日志

### v3.0.0 (2025-06-24) - 当前版本
- 🎯 **重大更新**: 双代理架构重构
- ✅ **算法修复**: 八字算法准确性100%修复
- 🔮 **融合分析**: 紫薇+八字数据统一
- 📊 **HTML图表**: 现代化可视化界面
- 💑 **合婚分析**: 双人配对分析功能
- 🎲 **六爻占卜**: 完整的占卜系统
- 📱 **响应式设计**: 适配多种设备
- 🚀 **性能优化**: 模块化架构，响应速度提升60%，内存使用减少40%
- 🎨 **UI升级**: 现代化深色主题，渐变效果，动画反馈
- 🔧 **智能启动**: 自动环境检查，一键启动脚本

### v2.0.0 (2024-12-15)
- ✅ 模块化架构重构
- ✅ 智能对话系统
- ✅ 多端统一接口

### v1.0.0 (2024-06-18)
- ✅ 基础算法集成
- ✅ API接口实现

## 🤝 贡献指南

### 如何贡献
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 贡献规范
- 遵循现有代码风格
- 添加必要的测试用例
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本项目仅供娱乐、学习和学术研究使用，不构成任何形式的人生建议或决策依据。算命结果仅供参考，请理性对待。开发者不对使用本系统产生的任何后果承担责任。

## 🙏 致谢

- [py-iztro](https://github.com/SylarLong/py-iztro) - 紫薇斗数算法库
- [lunar-python](https://github.com/6tail/lunar-python) - 农历转换库
- [DeepSeek](https://www.deepseek.com/) - AI模型支持
- [SiliconFlow](https://siliconflow.cn/) - API服务提供商

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
