#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版合盘分析
"""

import asyncio
import time
from datetime import datetime

def test_enhanced_compatibility_analysis():
    """测试增强版合盘分析"""
    print("🔮 测试增强版合盘分析")

    try:
        # 1. 导入必要模块
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        from core.storage.calculation_cache import CalculationCache

        # 2. 测试数据
        person_a_info = {
            "name": "增强测试男",
            "year": "1988",
            "month": "6",
            "day": "15",
            "hour": "午时",
            "gender": "男"
        }

        person_b_info = {
            "name": "增强测试女",
            "year": "1990",
            "month": "10",
            "day": "8",
            "hour": "酉时",
            "gender": "女"
        }

        analysis_dimension = "personality_compatibility"

        print(f"📊 测试数据:")
        print(f"   A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"   B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        print(f"   维度: {analysis_dimension}")

        # 3. 初始化引擎
        print("🔧 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()

        # 4. 计算合盘数据
        print("📊 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)

        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False

        print(f"✅ 合盘数据计算成功")
        print(f"   关系类型: {compatibility_data.get('relationship_type')}")

        # 5. 执行增强版合盘分析
        print(f"🤖 执行增强版{analysis_dimension}分析...")

        async def run_enhanced_analysis():
            result = await compatibility_engine.execute_compatibility_analysis(
                compatibility_data, analysis_dimension
            )
            return result

        # 运行异步分析
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        result = loop.run_until_complete(run_enhanced_analysis())
        loop.close()

        if not result.get("success"):
            print(f"❌ 增强版合盘分析失败: {result.get('error')}")
            return False

        print(f"✅ 增强版合盘分析成功")
        print(f"   分析内容长度: {len(result.get('content', ''))}字符")
        print(f"   提示词长度: {result.get('prompt_length', 0)}字符")
        print(f"   验证评分: {result.get('validation_report', {}).get('score', 0)}")
        print(f"   是否为合盘: {result.get('is_compatibility', False)}")

        # 6. 分析内容质量检查
        content = result.get('content', '')
        print(f"\n📋 内容质量分析:")
        print(f"   总字符数: {len(content)}")
        lines_count = len(content.split('\n'))
        print(f"   总行数: {lines_count}")

        # 检查关键词
        keywords = ["对比", "冲突", "互补", "建议", "预测", "风险", "问题"]
        found_keywords = [kw for kw in keywords if kw in content]
        print(f"   包含关键词: {found_keywords}")

        # 检查是否包含双方信息
        has_person_a = person_a_info['name'] in content or "A" in content
        has_person_b = person_b_info['name'] in content or "B" in content
        print(f"   包含双方信息: A={has_person_a}, B={has_person_b}")

        # 检查专业术语
        professional_terms = ["紫薇斗数", "八字", "命盘", "宫位", "星曜", "五行"]
        found_terms = [term for term in professional_terms if term in content]
        print(f"   专业术语: {found_terms}")

        # 7. 保存增强版结果
        print("💾 保存增强版合盘结果...")
        cache = CalculationCache()

        # 构建合盘专用的birth_info
        compatibility_birth_info = {
            "person_a": f"{person_a_info['name']}({person_a_info['year']}-{person_a_info['month']}-{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']})",
            "person_b": f"{person_b_info['name']}({person_b_info['year']}-{person_b_info['month']}-{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']})",
            "analysis_dimension": analysis_dimension,
            "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
            "timestamp": datetime.now().isoformat(),
            "enhanced": True  # 标记为增强版
        }

        # 保存结果
        result_id = cache.save_result(
            user_id=f"enhanced_compatibility_{int(time.time())}",
            session_id=f"enhanced_{person_a_info['name']}_{person_b_info['name']}_{int(time.time())}",
            calculation_type="compatibility",
            birth_info=compatibility_birth_info,
            raw_calculation=compatibility_data,
            detailed_analysis={
                "compatibility_analysis": result.get("content", ""),
                "analysis_dimension": analysis_dimension,
                "person_a_info": person_a_info,
                "person_b_info": person_b_info,
                "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
                "compatibility_result": result,
                "enhanced_version": True,
                "prompt_length": result.get('prompt_length', 0)
            },
            summary=f"增强版合盘分析：{person_a_info['name']} & {person_b_info['name']} - {analysis_dimension}",
            keywords=["增强版", "合盘", "匹配度", analysis_dimension, compatibility_data.get('relationship_type', '未知关系')],
            confidence=0.95,
            chart_image_path=""
        )

        print(f"✅ 增强版合盘结果保存成功")
        print(f"   结果ID: {result_id}")

        # 8. 验证缓存读取
        print("🔍 验证缓存读取...")
        cached_result = cache.get_result(result_id)

        if cached_result:
            print(f"✅ 缓存读取成功")
            print(f"   计算类型: {cached_result.calculation_type}")
            print(f"   分析维度: {cached_result.detailed_analysis.get('analysis_dimension')}")
            print(f"   分析内容: {len(cached_result.detailed_analysis.get('compatibility_analysis', ''))}字符")
            print(f"   增强版标记: {cached_result.detailed_analysis.get('enhanced_version', False)}")
        else:
            print(f"❌ 缓存读取失败")
            return False

        # 9. 显示分析内容摘要
        print(f"\n📄 分析内容摘要（前500字符）:")
        print(f"{content[:500]}...")

        print("🎉 增强版合盘分析测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_compatibility_analysis()
    if success:
        print("\n✅ 增强版合盘分析测试通过！")
        print("🌟 主要改进:")
        print("   1. 使用专用的详细提示词构建器")
        print("   2. 直接调用LLM分析器，绕过通用控制器")
        print("   3. 增强的合盘分析验证器")
        print("   4. 更高的字数要求和质量标准")
        print("   5. 专业的合盘分析结构和内容")
        print("\n🌐 现在可以在Web界面中查看增强版合盘分析了：http://localhost:8501")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
