#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话存储管理器 - 持久化聊天历史和用户设置
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class ChatMessage:
    """聊天消息数据结构"""
    role: str  # "user" 或 "assistant"
    content: str
    timestamp: str
    message_type: str = "text"  # "text", "analysis", "chart"
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class UserSettings:
    """用户设置数据结构"""
    analysis_depth: str = "standard"
    quality_level: str = "professional"
    focus_positive: bool = True
    include_timing: bool = True
    practical_advice: bool = True
    theme: str = "auto"
    language: str = "zh"

@dataclass
class SessionData:
    """会话数据结构"""
    session_id: str
    user_id: str
    messages: List[ChatMessage]
    settings: UserSettings
    created_at: str
    updated_at: str
    stats: Dict[str, Any] = None

    def __post_init__(self):
        if self.stats is None:
            self.stats = {
                "total_messages": 0,
                "total_analyses": 0,
                "favorite_type": None,
                "session_duration": 0
            }

class SessionManager:
    """会话存储管理器"""

    def __init__(self, storage_dir: str = "data/sessions"):
        """初始化会话管理器"""
        self.storage_dir = storage_dir
        self.ensure_storage_dir()

        # 内存缓存
        self.cache = {}
        self.cache_timeout = 3600  # 1小时缓存超时

        logger.info(f"会话管理器初始化完成，存储目录: {storage_dir}")

    def ensure_storage_dir(self):
        """确保存储目录存在"""
        os.makedirs(self.storage_dir, exist_ok=True)

        # 创建子目录
        subdirs = ["sessions", "settings", "backups"]
        for subdir in subdirs:
            os.makedirs(os.path.join(self.storage_dir, subdir), exist_ok=True)

    def generate_session_id(self, user_id: str = None) -> str:
        """生成会话ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if user_id:
            # 基于用户ID生成一致的会话ID前缀
            user_hash = hashlib.md5(user_id.encode()).hexdigest()[:8]
            return f"{user_hash}_{timestamp}"
        else:
            return f"guest_{timestamp}"

    def generate_user_id(self) -> str:
        """生成用户ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"user_{timestamp}"

    def save_session(self, session_data: SessionData) -> bool:
        """保存会话数据"""
        try:
            session_data.updated_at = datetime.now().isoformat()

            # 转换为字典
            data_dict = asdict(session_data)

            # 保存到文件
            session_file = os.path.join(
                self.storage_dir, "sessions", f"{session_data.session_id}.json"
            )

            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(data_dict, f, ensure_ascii=False, indent=2)

            # 更新缓存
            self.cache[session_data.session_id] = {
                "data": session_data,
                "timestamp": datetime.now()
            }

            logger.debug(f"会话已保存: {session_data.session_id}")
            return True

        except Exception as e:
            logger.error(f"保存会话失败: {e}")
            return False

    def load_session(self, session_id: str) -> Optional[SessionData]:
        """加载会话数据"""
        try:
            # 检查缓存
            if session_id in self.cache:
                cache_entry = self.cache[session_id]
                if datetime.now() - cache_entry["timestamp"] < timedelta(seconds=self.cache_timeout):
                    logger.debug(f"从缓存加载会话: {session_id}")
                    return cache_entry["data"]

            # 从文件加载
            session_file = os.path.join(
                self.storage_dir, "sessions", f"{session_id}.json"
            )

            if not os.path.exists(session_file):
                logger.debug(f"会话文件不存在: {session_id}")
                return None

            with open(session_file, 'r', encoding='utf-8') as f:
                data_dict = json.load(f)

            # 转换消息列表
            messages = []
            for msg_dict in data_dict.get("messages", []):
                messages.append(ChatMessage(**msg_dict))

            # 转换设置
            settings_dict = data_dict.get("settings", {})
            settings = UserSettings(**settings_dict)

            # 创建会话数据
            session_data = SessionData(
                session_id=data_dict["session_id"],
                user_id=data_dict["user_id"],
                messages=messages,
                settings=settings,
                created_at=data_dict["created_at"],
                updated_at=data_dict["updated_at"],
                stats=data_dict.get("stats", {})
            )

            # 更新缓存
            self.cache[session_id] = {
                "data": session_data,
                "timestamp": datetime.now()
            }

            logger.debug(f"会话已加载: {session_id}")
            return session_data

        except Exception as e:
            logger.error(f"加载会话失败: {e}")
            return None

    def create_new_session(self, user_id: str = None) -> SessionData:
        """创建新会话"""
        if not user_id:
            user_id = self.generate_user_id()

        session_id = self.generate_session_id(user_id)

        session_data = SessionData(
            session_id=session_id,
            user_id=user_id,
            messages=[],
            settings=UserSettings(),
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 保存新会话
        self.save_session(session_data)

        logger.info(f"新会话已创建: {session_id}")
        return session_data

    def create_session(self, user_id: str = None, settings: UserSettings = None) -> SessionData:
        """创建会话（兼容性方法）"""
        if not user_id:
            user_id = self.generate_user_id()

        session_id = self.generate_session_id(user_id)

        if not settings:
            settings = UserSettings()

        session_data = SessionData(
            session_id=session_id,
            user_id=user_id,
            messages=[],
            settings=settings,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 保存新会话
        self.save_session(session_data)

        logger.info(f"新会话已创建: {session_id}")
        return session_data

    def add_message(self, session_id: str, role: str, content: str,
                   message_type: str = "text", metadata: Dict[str, Any] = None) -> bool:
        """添加消息到会话"""
        try:
            session_data = self.load_session(session_id)
            if not session_data:
                logger.error(f"会话不存在: {session_id}")
                return False

            message = ChatMessage(
                role=role,
                content=content,
                timestamp=datetime.now().isoformat(),
                message_type=message_type,
                metadata=metadata or {}
            )

            session_data.messages.append(message)

            # 更新统计
            session_data.stats["total_messages"] += 1
            if role == "assistant" and message_type == "analysis":
                session_data.stats["total_analyses"] += 1

            # 保存会话
            return self.save_session(session_data)

        except Exception as e:
            logger.error(f"添加消息失败: {e}")
            return False

    def update_settings(self, session_id: str, settings) -> bool:
        """更新用户设置"""
        try:
            session_data = self.load_session(session_id)
            if not session_data:
                logger.error(f"会话不存在: {session_id}")
                return False

            # 处理不同类型的设置输入
            if isinstance(settings, UserSettings):
                # 如果是UserSettings对象，直接替换
                session_data.settings = settings
            elif isinstance(settings, dict):
                # 如果是字典，逐个更新属性
                for key, value in settings.items():
                    if hasattr(session_data.settings, key):
                        setattr(session_data.settings, key, value)
            else:
                logger.error(f"不支持的设置类型: {type(settings)}")
                return False

            # 保存会话
            return self.save_session(session_data)

        except Exception as e:
            logger.error(f"更新设置失败: {e}")
            return False

    def get_user_sessions(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户的会话列表"""
        try:
            sessions = []
            session_dir = os.path.join(self.storage_dir, "sessions")

            if not os.path.exists(session_dir):
                return sessions

            # 遍历会话文件
            for filename in os.listdir(session_dir):
                if not filename.endswith('.json'):
                    continue

                session_file = os.path.join(session_dir, filename)
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    if data.get("user_id") == user_id:
                        sessions.append({
                            "session_id": data["session_id"],
                            "created_at": data["created_at"],
                            "updated_at": data["updated_at"],
                            "message_count": len(data.get("messages", [])),
                            "stats": data.get("stats", {})
                        })

                except Exception as e:
                    logger.warning(f"读取会话文件失败: {filename}, {e}")
                    continue

            # 按更新时间排序
            sessions.sort(key=lambda x: x["updated_at"], reverse=True)

            return sessions[:limit]

        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            return []

    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            session_file = os.path.join(
                self.storage_dir, "sessions", f"{session_id}.json"
            )

            if os.path.exists(session_file):
                os.remove(session_file)

            # 从缓存中移除
            if session_id in self.cache:
                del self.cache[session_id]

            logger.info(f"会话已删除: {session_id}")
            return True

        except Exception as e:
            logger.error(f"删除会话失败: {e}")
            return False

    def cleanup_old_sessions(self, days: int = 30) -> int:
        """清理旧会话"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0

            session_dir = os.path.join(self.storage_dir, "sessions")
            if not os.path.exists(session_dir):
                return 0

            for filename in os.listdir(session_dir):
                if not filename.endswith('.json'):
                    continue

                session_file = os.path.join(session_dir, filename)
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    updated_at = datetime.fromisoformat(data["updated_at"])
                    if updated_at < cutoff_date:
                        os.remove(session_file)
                        deleted_count += 1

                except Exception as e:
                    logger.warning(f"清理会话文件失败: {filename}, {e}")
                    continue

            logger.info(f"清理了 {deleted_count} 个旧会话")
            return deleted_count

        except Exception as e:
            logger.error(f"清理旧会话失败: {e}")
            return 0

    def export_session(self, session_id: str, format: str = "json") -> Optional[str]:
        """导出会话数据"""
        try:
            session_data = self.load_session(session_id)
            if not session_data:
                return None

            if format == "json":
                return json.dumps(asdict(session_data), ensure_ascii=False, indent=2)
            elif format == "txt":
                lines = [f"会话ID: {session_data.session_id}"]
                lines.append(f"创建时间: {session_data.created_at}")
                lines.append(f"更新时间: {session_data.updated_at}")
                lines.append("=" * 50)

                for msg in session_data.messages:
                    role_name = "用户" if msg.role == "user" else "AI助手"
                    lines.append(f"\n[{msg.timestamp}] {role_name}:")
                    lines.append(msg.content)
                    lines.append("-" * 30)

                return "\n".join(lines)

            return None

        except Exception as e:
            logger.error(f"导出会话失败: {e}")
            return None

    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            session_dir = os.path.join(self.storage_dir, "sessions")

            if not os.path.exists(session_dir):
                return {
                    "total_sessions": 0,
                    "total_size_bytes": 0,
                    "total_size_mb": 0,
                    "cache_size": len(self.cache),
                    "storage_dir": self.storage_dir
                }

            session_files = [f for f in os.listdir(session_dir) if f.endswith('.json')]
            total_size = 0

            for filename in session_files:
                file_path = os.path.join(session_dir, filename)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)

            return {
                "total_sessions": len(session_files),
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "cache_size": len(self.cache),
                "storage_dir": self.storage_dir
            }

        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {
                "total_sessions": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "cache_size": 0,
                "storage_dir": self.storage_dir
            }
