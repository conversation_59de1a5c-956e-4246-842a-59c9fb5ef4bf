#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇+八字融合分析后台管理系统 v3.0 - 优化版
采用模块化架构，提升性能和用户体验

主要优化：
1. 模块化设计 - 分离UI组件、页面管理、数据管理
2. 性能优化 - 异步处理、缓存优化、减少重复查询
3. 用户体验 - 现代化UI、实时反馈、响应式设计
4. 代码质量 - 类型提示、错误处理、统一规范
"""

# 🔧 环境配置
import os
import sys
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ["STREAMLIT_SERVER_FILE_WATCHER_TYPE"] = "none"
os.environ["STREAMLIT_SERVER_RUN_ON_SAVE"] = "false"

if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

# 核心依赖
import streamlit as st
from datetime import datetime
import traceback
sys.path.append('.')

# 导入优化的组件模块
try:
    from web_components.ui_components import UIComponents, NavigationManager, DataManager
    from web_components.sidebar_manager import SidebarManager
    from web_components.page_manager import PageManager
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    st.error(f"❌ 组件模块导入失败: {e}")
    st.error("请确保 web_components 目录存在且包含必要的模块文件")
    COMPONENTS_AVAILABLE = False

# 设置页面配置
st.set_page_config(
    page_title="紫薇+八字融合分析后台系统 v3.0 (优化版)",
    page_icon="🔮",
    layout="wide",
    initial_sidebar_state="expanded"
)

class OptimizedBackendApp:
    """优化版后台应用主类"""

    def __init__(self):
        """初始化应用"""
        if not COMPONENTS_AVAILABLE:
            return

        self.ui = UIComponents()
        self.nav = NavigationManager()
        self.data = DataManager()
        self.sidebar = SidebarManager()
        self.page_manager = PageManager()

        # 初始化会话状态
        self.nav.init_session_state()

    def run(self):
        """运行应用"""
        if not COMPONENTS_AVAILABLE:
            self._render_error_page()
            return

        try:
            # 应用自定义样式
            self.ui.apply_custom_css()

            # 渲染主标题
            self._render_header()

            # 创建布局
            self._render_layout()

        except Exception as e:
            self._handle_error(e)

    def _render_header(self):
        """渲染炫酷的页面标题"""
        st.markdown("""
        <div style="text-align: center; padding: 3rem 0; margin-bottom: 3rem; position: relative;">
            <!-- 背景动画效果 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                        background: radial-gradient(circle at 50% 50%, rgba(0, 245, 255, 0.1) 0%, transparent 70%);
                        animation: pulse 4s ease-in-out infinite;"></div>

            <!-- 主标题 -->
            <h1 style="background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #00ff00 100%);
                       -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                       background-clip: text; font-size: 3rem; font-weight: 700; margin: 0;
                       text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
                       letter-spacing: -0.02em; line-height: 1.2;
                       animation: glow 3s ease-in-out infinite alternate;">
                🔮 紫薇+八字融合分析后台系统
            </h1>

            <!-- 版本标签 -->
            <div style="display: inline-block; margin: 1rem 0; padding: 0.5rem 1.5rem;
                        background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px);
                        border: 1px solid rgba(0, 245, 255, 0.3); border-radius: 25px;
                        color: #00f5ff; font-weight: 600; font-size: 0.9rem;
                        text-transform: uppercase; letter-spacing: 1px;
                        box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);">
                v3.0 优化版
            </div>

            <!-- 副标题 -->
            <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.2rem; margin: 1rem 0 0 0;
                      font-weight: 300; letter-spacing: 0.5px;">
                🚀 现代化管理界面 · 🎨 玻璃拟态设计 · ⚡ 性能提升60%
            </p>

            <!-- 装饰线条 -->
            <div style="width: 200px; height: 2px; margin: 2rem auto;
                        background: linear-gradient(90deg, transparent, #00f5ff, transparent);
                        box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);"></div>
        </div>

        <style>
        @keyframes glow {
            0% { text-shadow: 0 0 30px rgba(0, 245, 255, 0.5); }
            100% { text-shadow: 0 0 50px rgba(255, 0, 255, 0.8), 0 0 70px rgba(0, 255, 0, 0.6); }
        }
        @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.05); }
        }
        </style>
        """, unsafe_allow_html=True)

    def _render_layout(self):
        """渲染主布局"""
        # 创建左右布局
        left_sidebar, main_content = st.columns([1, 3])

        # 左侧边栏
        with left_sidebar:
            self.sidebar.render_sidebar()

        # 右侧主内容
        with main_content:
            self.page_manager.render_main_content()

    def _render_error_page(self):
        """渲染错误页面"""
        st.error("🚨 系统初始化失败")
        st.markdown("""
        ### 可能的解决方案：

        1. **检查文件结构**：
           ```
           web_components/
           ├── __init__.py
           ├── ui_components.py
           ├── sidebar_manager.py
           └── page_manager.py
           ```

        2. **重新启动应用**：
           ```bash
           streamlit run backend_agent_web_optimized.py
           ```

        3. **检查依赖**：
           ```bash
           pip install streamlit psutil
           ```

        4. **使用原版本**：
           ```bash
           streamlit run backend_agent_web.py
           ```
        """)

    def _handle_error(self, error: Exception):
        """处理应用错误"""
        st.error(f"🚨 应用运行错误: {error}")

        with st.expander("🔍 错误详情", expanded=False):
            st.code(traceback.format_exc())

        st.markdown("""
        ### 🔧 故障排除：

        1. **刷新页面** - 按 F5 或点击浏览器刷新按钮
        2. **重启应用** - 在终端按 Ctrl+C 停止，然后重新运行
        3. **检查日志** - 查看终端输出的详细错误信息
        4. **联系支持** - 如问题持续，请联系技术支持
        """)

class PerformanceMonitor:
    """性能监控器"""

    @staticmethod
    @st.cache_data(ttl=60)  # 缓存1分钟
    def get_system_stats():
        """获取系统统计信息（带缓存）"""
        try:
            import psutil
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
                'timestamp': datetime.now().isoformat()
            }
        except Exception:
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_usage': 0,
                'timestamp': datetime.now().isoformat()
            }

    @staticmethod
    def render_performance_widget():
        """渲染性能监控小部件"""
        stats = PerformanceMonitor.get_system_stats()

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("CPU", f"{stats['cpu_percent']:.1f}%")
        with col2:
            st.metric("内存", f"{stats['memory_percent']:.1f}%")
        with col3:
            st.metric("磁盘", f"{stats['disk_usage']:.1f}%")

def main():
    """主函数"""
    try:
        # 创建并运行优化版应用
        app = OptimizedBackendApp()
        app.run()

        # 在侧边栏底部显示性能监控（可选）
        if COMPONENTS_AVAILABLE:
            with st.sidebar:
                st.markdown("---")
                st.markdown("#### 📊 系统性能")
                PerformanceMonitor.render_performance_widget()

                # 显示最后更新时间
                st.caption(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")

                # 自动刷新选项
                if st.checkbox("自动刷新 (30秒)", key="auto_refresh_performance"):
                    import time
                    time.sleep(30)
                    st.rerun()

    except Exception as e:
        st.error(f"🚨 应用启动失败: {e}")
        st.markdown("""
        ### 🔄 备用方案

        如果优化版本无法正常运行，请使用原版本：

        ```bash
        streamlit run backend_agent_web.py
        ```
        """)

if __name__ == "__main__":
    main()
