#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试紫薇+八字融合分析
"""

import asyncio
import json

async def test_ziwei_bazi_fusion():
    """测试紫薇+八字融合分析"""
    print("🔍 测试紫薇+八字融合分析")
    print("=" * 60)
    
    try:
        # 1. 测试融合引擎
        print("1️⃣ 测试融合引擎")
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 12, "男")
        
        if not result.get("success"):
            print(f"❌ 融合分析失败: {result.get('error')}")
            return False
        
        print("✅ 融合分析成功")
        
        # 2. 检查数据结构
        print("\n2️⃣ 检查数据结构")
        print(f"📋 顶级键: {list(result.keys())}")
        
        # 检查紫薇数据
        ziwei_analysis = result.get("ziwei_analysis", {})
        print(f"\n🌟 紫薇斗数数据:")
        print(f"  是否存在: {'是' if ziwei_analysis else '否'}")
        if ziwei_analysis:
            print(f"  紫薇键: {list(ziwei_analysis.keys())}")
            palaces = ziwei_analysis.get("palaces", {})
            if palaces:
                print(f"  宫位数量: {len(palaces)}")
                print(f"  命宫: {palaces.get('命宫', {}).get('major_stars', [])}")
        
        # 检查八字数据
        bazi_analysis = result.get("bazi_analysis", {})
        print(f"\n🔮 八字分析数据:")
        print(f"  是否存在: {'是' if bazi_analysis else '否'}")
        if bazi_analysis:
            print(f"  八字键: {list(bazi_analysis.keys())}")
            
            # 检查四柱
            pillars = ["年柱", "月柱", "日柱", "时柱"]
            for pillar in pillars:
                if pillar in bazi_analysis:
                    print(f"  {pillar}: {bazi_analysis[pillar]}")
            
            # 检查五行分析
            wuxing = bazi_analysis.get("五行分析", {})
            if wuxing:
                print(f"  五行分析: {list(wuxing.keys())}")
                for element, info in wuxing.items():
                    if isinstance(info, dict) and "数量" in info:
                        print(f"    {element}: {info['数量']}个")
            
            # 检查十神分析
            shishen = bazi_analysis.get("十神分析", {})
            if shishen:
                print(f"  十神分析: {list(shishen.keys())}")
        
        # 3. 测试数据处理器
        print("\n3️⃣ 测试数据处理器")
        from core.analysis.data_processor import DataProcessor
        
        processor = DataProcessor()
        analysis_data = processor.extract_analysis_data(result, "personality_destiny")
        
        print(f"📊 数据处理结果:")
        print(f"  分析类型: {analysis_data.get('analysis_type')}")
        
        # 检查提取的紫薇数据
        ziwei_extracted = analysis_data.get("ziwei", {})
        print(f"  紫薇数据: {'有' if ziwei_extracted else '无'}")
        
        # 检查提取的八字数据
        bazi_extracted = analysis_data.get("bazi", {})
        print(f"  八字数据: {'有' if bazi_extracted else '无'}")
        
        if bazi_extracted:
            print(f"  八字内容: {list(bazi_extracted.keys())}")
            
            # 检查四柱
            for pillar in ["年柱", "月柱", "日柱", "时柱"]:
                if pillar in bazi_extracted:
                    print(f"    {pillar}: {bazi_extracted[pillar]}")
            
            # 检查五行
            wuxing_extracted = bazi_extracted.get("五行", {})
            if wuxing_extracted:
                print(f"    五行: {list(wuxing_extracted.keys())}")
        
        # 4. 测试提示词构建
        print("\n4️⃣ 测试提示词构建")
        from core.analysis.prompt_builder import PromptBuilder
        
        birth_info = {
            "year": "1988",
            "month": "6",
            "day": "1", 
            "hour": "午时",
            "gender": "男"
        }
        
        prompt_builder = PromptBuilder()
        prompt = prompt_builder.build_analysis_prompt(
            analysis_data, birth_info, "personality_destiny"
        )
        
        print(f"📝 提示词信息:")
        print(f"  长度: {len(prompt)} 字符")
        
        # 检查是否包含紫薇和八字信息
        has_ziwei = "紫薇斗数" in prompt
        has_bazi = "八字" in prompt
        has_wuxing = "五行" in prompt
        has_shishen = "十神" in prompt
        
        print(f"  包含紫薇信息: {'✅' if has_ziwei else '❌'}")
        print(f"  包含八字信息: {'✅' if has_bazi else '❌'}")
        print(f"  包含五行信息: {'✅' if has_wuxing else '❌'}")
        print(f"  包含十神信息: {'✅' if has_shishen else '❌'}")
        
        # 5. 保存详细信息
        print("\n5️⃣ 保存详细信息")
        
        debug_info = {
            "fusion_result": result,
            "analysis_data": analysis_data,
            "prompt_length": len(prompt),
            "has_ziwei": has_ziwei,
            "has_bazi": has_bazi,
            "has_wuxing": has_wuxing,
            "has_shishen": has_shishen
        }
        
        with open("debug_fusion_analysis.json", "w", encoding="utf-8") as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        
        with open("debug_fusion_prompt.txt", "w", encoding="utf-8") as f:
            f.write("紫薇+八字融合分析提示词:\n")
            f.write("=" * 60 + "\n\n")
            f.write(prompt)
        
        print("💾 调试信息已保存:")
        print("  - debug_fusion_analysis.json")
        print("  - debug_fusion_prompt.txt")
        
        # 6. 结论
        print("\n6️⃣ 融合分析状态")
        
        if has_ziwei and has_bazi:
            print("🎉 紫薇+八字融合分析正常！")
            print("✅ 紫薇斗数数据完整")
            print("✅ 八字分析数据完整")
            print("✅ 提示词包含两套数据")
            return True
        elif has_ziwei and not has_bazi:
            print("⚠️ 只有紫薇斗数，缺少八字分析！")
            print("✅ 紫薇斗数数据完整")
            print("❌ 八字分析数据缺失")
            return False
        else:
            print("❌ 融合分析数据不完整！")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_ziwei_bazi_fusion())
