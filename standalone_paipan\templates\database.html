<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理 - 紫薇斗数排盘</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .content {
            padding: 30px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border-left: 5px solid #667eea;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1em;
        }

        .search-form {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .records-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .records-table th {
            background: #667eea;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .records-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e1e5e9;
        }

        .records-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .recent-list {
            list-style: none;
        }

        .recent-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .recent-item .datetime {
            font-weight: bold;
            color: #333;
        }

        .recent-item .info {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .records-table {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💾 数据库管理</h1>
            <p>排盘记录存储与查询</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('statistics')">📊 统计概览</button>
            <button class="nav-tab" onclick="showTab('records')">📋 记录查询</button>
            <button class="nav-tab" onclick="showTab('recent')">🕒 最近记录</button>
        </div>

        <div class="content">
            <!-- 统计概览标签页 -->
            <div id="statistics" class="tab-content active">
                <div class="stats-grid" id="statsGrid">
                    <div class="loading">
                        <div class="spinner"></div>
                        加载统计信息中...
                    </div>
                </div>
            </div>

            <!-- 记录查询标签页 -->
            <div id="records" class="tab-content">
                <div class="search-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="searchGender">性别</label>
                            <select id="searchGender" class="form-control">
                                <option value="">全部</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="searchYear">出生年份</label>
                            <input type="number" id="searchYear" class="form-control" placeholder="如：1990" min="1900" max="2030">
                        </div>
                        <div class="form-group">
                            <label for="searchSuccess">状态</label>
                            <select id="searchSuccess" class="form-control">
                                <option value="true">仅成功记录</option>
                                <option value="false">仅失败记录</option>
                                <option value="">全部记录</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button class="btn" onclick="searchRecords()">🔍 搜索</button>
                        </div>
                    </div>
                </div>

                <div id="recordsResult">
                    <div class="loading">
                        <div class="spinner"></div>
                        加载记录中...
                    </div>
                </div>
            </div>

            <!-- 最近记录标签页 -->
            <div id="recent" class="tab-content">
                <ul class="recent-list" id="recentList">
                    <li class="loading">
                        <div class="spinner"></div>
                        加载最近记录中...
                    </li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; padding: 20px;">
            <a href="/" class="btn" style="text-decoration: none;">🏠 返回首页</a>
        </div>
    </div>

    <script>
        let currentTab = 'statistics';

        // 页面加载时初始化
        window.addEventListener('load', function() {
            loadStatistics();
            loadRecords();
        });

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            currentTab = tabName;

            // 根据标签页加载相应数据
            if (tabName === 'statistics' && !document.getElementById('statsGrid').querySelector('.stat-card')) {
                loadStatistics();
            } else if (tabName === 'records' && !document.getElementById('recordsResult').querySelector('.records-table')) {
                loadRecords();
            }
        }

        // 加载统计信息
        function loadStatistics() {
            fetch('/api/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayStatistics(data.statistics);
                    } else {
                        showError('statsGrid', '加载统计信息失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('statsGrid', '网络错误: ' + error.message);
                });
        }

        // 显示统计信息
        function displayStatistics(stats) {
            const grid = document.getElementById('statsGrid');
            const successRate = (stats.success_rate * 100).toFixed(1);

            grid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_records}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.success_records}</div>
                    <div class="stat-label">成功记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${successRate}%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.gender_stats['男'] || 0}</div>
                    <div class="stat-label">男性记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.gender_stats['女'] || 0}</div>
                    <div class="stat-label">女性记录</div>
                </div>
            `;

            // 显示最近记录
            if (stats.recent_records && stats.recent_records.length > 0) {
                const recentList = document.getElementById('recentList');
                recentList.innerHTML = stats.recent_records.map(record => `
                    <li class="recent-item">
                        <div class="datetime">${record[0]} (${record[1]})</div>
                        <div class="info">计算时间: ${new Date(record[2]).toLocaleString()}</div>
                    </li>
                `).join('');
            }
        }

        // 加载记录列表
        function loadRecords() {
            const params = new URLSearchParams({
                success_only: 'true'
            });

            fetch('/api/records?' + params)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayRecords(data.records);
                    } else {
                        showError('recordsResult', '加载记录失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('recordsResult', '网络错误: ' + error.message);
                });
        }

        // 搜索记录
        function searchRecords() {
            const gender = document.getElementById('searchGender').value;
            const year = document.getElementById('searchYear').value;
            const success = document.getElementById('searchSuccess').value;

            const params = new URLSearchParams();
            if (gender) params.append('gender', gender);
            if (year) params.append('birth_year', year);
            if (success) params.append('success_only', success);

            document.getElementById('recordsResult').innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    搜索中...
                </div>
            `;

            fetch('/api/records?' + params)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayRecords(data.records);
                    } else {
                        showError('recordsResult', '搜索失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('recordsResult', '网络错误: ' + error.message);
                });
        }

        // 显示记录列表
        function displayRecords(records) {
            const container = document.getElementById('recordsResult');

            if (records.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">未找到匹配的记录</div>';
                return;
            }

            const tableHtml = `
                <table class="records-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>出生信息</th>
                            <th>性别</th>
                            <th>计算时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${records.map(record => `
                            <tr>
                                <td>${record.id}</td>
                                <td>${record.birth_datetime}</td>
                                <td>${record.gender}</td>
                                <td>${new Date(record.calculation_time).toLocaleString()}</td>
                                <td>
                                    <span class="status-badge ${record.success ? 'status-success' : 'status-error'}">
                                        ${record.success ? '成功' : '失败'}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn" onclick="viewRecord(${record.id})" style="padding: 5px 10px; font-size: 0.9em;">查看</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHtml;
        }

        // 查看具体记录
        function viewRecord(recordId) {
            fetch(`/api/record/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 将数据保存到sessionStorage并跳转到结果页面
                        sessionStorage.setItem('paipanResult', JSON.stringify({
                            success: true,
                            data: data.data
                        }));
                        window.location.href = '/result';
                    } else {
                        alert('获取记录失败: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('网络错误: ' + error.message);
                });
        }

        // 显示错误信息
        function showError(containerId, message) {
            document.getElementById(containerId).innerHTML = `
                <div style="text-align: center; padding: 50px; color: #e74c3c;">
                    ❌ ${message}
                </div>
            `;
        }
    </script>
</body>
</html>
