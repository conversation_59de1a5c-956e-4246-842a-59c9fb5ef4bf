<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独立排盘系统 - 紫薇斗数+八字命理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .form-row input {
            flex: 1;
        }

        input[type="number"], select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        input[type="number"]:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .gender-group {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .gender-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 10px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            transition: all 0.3s ease;
            background: white;
        }

        .gender-option:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .gender-option input[type="radio"] {
            margin: 0;
        }

        .gender-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            font-weight: 600;
        }

        .quick-input {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .quick-input h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .quick-input input {
            margin-bottom: 10px;
        }

        .quick-input .example {
            color: #666;
            font-size: 0.9em;
            font-style: italic;
        }

        .time-helper {
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            border-left: 3px solid #667eea;
            font-size: 0.9em;
            color: #667eea;
        }

        .time-helper.active {
            background: rgba(102, 126, 234, 0.15);
            color: #5a67d8;
        }

        .time-table {
            display: none;
            margin-top: 15px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .time-table h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1em;
        }

        .time-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }

        .time-item {
            background: white;
            padding: 8px;
            border-radius: 6px;
            text-align: center;
            font-size: 0.85em;
            border: 1px solid #e1e5e9;
        }

        .time-item.current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .time-item .time-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .time-item .time-range {
            color: #666;
            font-size: 0.9em;
        }

        .time-item.current .time-range {
            color: rgba(255, 255, 255, 0.9);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: white;
            padding: 0 20px;
            color: #666;
            font-weight: 600;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .gender-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 独立排盘系统</h1>
            <p>紫薇斗数 + 八字命理 完整排盘</p>
        </div>

        <div class="error" id="errorMsg"></div>

        <!-- 快速输入 -->
        <div class="quick-input">
            <h3>📝 快速输入</h3>
            <input type="text" id="quickInput" placeholder="输入出生时间，如：1990-3-15-8 或 1990年3月15日8时">
            <div class="example">示例：1990-3-15-8 或 1990年3月15日8时</div>
        </div>

        <div class="divider">
            <span>或者详细输入</span>
        </div>

        <!-- 详细输入表单 -->
        <form id="paipanForm">
            <div class="form-group">
                <label>📅 出生日期</label>
                <div class="form-row">
                    <input type="number" id="year" name="year" placeholder="年份" min="1900" max="2100" required>
                    <span>年</span>
                    <input type="number" id="month" name="month" placeholder="月" min="1" max="12" required>
                    <span>月</span>
                    <input type="number" id="day" name="day" placeholder="日" min="1" max="31" required>
                    <span>日</span>
                </div>
            </div>

            <div class="form-group">
                <label>🕐 出生时间</label>
                <div class="form-row">
                    <input type="number" id="hour" name="hour" placeholder="小时" min="0" max="23" required>
                    <span>时 (24小时制)</span>
                </div>
                <div class="time-helper" id="timeHelper">
                    <small>选择小时后将显示对应时辰</small>
                </div>
            </div>

            <div class="form-group">
                <label>👤 性别</label>
                <div class="gender-group">
                    <label class="gender-option" for="male">
                        <input type="radio" id="male" name="gender" value="男" checked>
                        <span>👨 男</span>
                    </label>
                    <label class="gender-option selected" for="female">
                        <input type="radio" id="female" name="gender" value="女">
                        <span>👩 女</span>
                    </label>
                </div>
            </div>

            <button type="submit" class="btn" id="calculateBtn">
                🔮 开始排盘
            </button>

            <button type="button" class="btn btn-secondary" id="quickBtn">
                ⚡ 快速排盘
            </button>

            <a href="/database" class="btn" style="text-decoration: none; display: block; text-align: center; margin-top: 15px; background: linear-gradient(45deg, #27ae60, #2ecc71);">
                💾 数据库管理
            </a>

            <a href="/admin" class="btn" style="text-decoration: none; display: block; text-align: center; margin-top: 10px; background: linear-gradient(45deg, #e74c3c, #c0392b);">
                🛠️ 后台管理
            </a>
        </form>

        <!-- 时辰对照表 -->
        <div class="time-table" id="timeTable">
            <h4>🕐 十二时辰对照表</h4>
            <div class="time-grid">
                <div class="time-item" data-hours="23,0">
                    <div class="time-name">子时</div>
                    <div class="time-range">23:00-01:00</div>
                </div>
                <div class="time-item" data-hours="1,2">
                    <div class="time-name">丑时</div>
                    <div class="time-range">01:00-03:00</div>
                </div>
                <div class="time-item" data-hours="3,4">
                    <div class="time-name">寅时</div>
                    <div class="time-range">03:00-05:00</div>
                </div>
                <div class="time-item" data-hours="5,6">
                    <div class="time-name">卯时</div>
                    <div class="time-range">05:00-07:00</div>
                </div>
                <div class="time-item" data-hours="7,8">
                    <div class="time-name">辰时</div>
                    <div class="time-range">07:00-09:00</div>
                </div>
                <div class="time-item" data-hours="9,10">
                    <div class="time-name">巳时</div>
                    <div class="time-range">09:00-11:00</div>
                </div>
                <div class="time-item" data-hours="11,12">
                    <div class="time-name">午时</div>
                    <div class="time-range">11:00-13:00</div>
                </div>
                <div class="time-item" data-hours="13,14">
                    <div class="time-name">未时</div>
                    <div class="time-range">13:00-15:00</div>
                </div>
                <div class="time-item" data-hours="15,16">
                    <div class="time-name">申时</div>
                    <div class="time-range">15:00-17:00</div>
                </div>
                <div class="time-item" data-hours="17,18">
                    <div class="time-name">酉时</div>
                    <div class="time-range">17:00-19:00</div>
                </div>
                <div class="time-item" data-hours="19,20">
                    <div class="time-name">戌时</div>
                    <div class="time-range">19:00-21:00</div>
                </div>
                <div class="time-item" data-hours="21,22">
                    <div class="time-name">亥时</div>
                    <div class="time-range">21:00-23:00</div>
                </div>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在计算排盘，请稍候...</p>
        </div>
    </div>

    <script>
        // 性别选择
        document.querySelectorAll('input[name="gender"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.querySelectorAll('.gender-option').forEach(option => {
                    option.classList.remove('selected');
                });
                this.closest('.gender-option').classList.add('selected');
            });
        });

        // 小时输入监听
        document.getElementById('hour').addEventListener('input', function() {
            const hour = parseInt(this.value);
            updateTimeHelper(hour);
        });

        // 页面加载时检查小时值
        document.addEventListener('DOMContentLoaded', function() {
            const hourInput = document.getElementById('hour');
            if (hourInput.value) {
                updateTimeHelper(parseInt(hourInput.value));
            }
        });

        // 时辰对照表
        const timeMap = {
            23: '子时 (23:00-01:00)', 0: '子时 (23:00-01:00)',
            1: '丑时 (01:00-03:00)', 2: '丑时 (01:00-03:00)',
            3: '寅时 (03:00-05:00)', 4: '寅时 (03:00-05:00)',
            5: '卯时 (05:00-07:00)', 6: '卯时 (05:00-07:00)',
            7: '辰时 (07:00-09:00)', 8: '辰时 (07:00-09:00)',
            9: '巳时 (09:00-11:00)', 10: '巳时 (09:00-11:00)',
            11: '午时 (11:00-13:00)', 12: '午时 (11:00-13:00)',
            13: '未时 (13:00-15:00)', 14: '未时 (13:00-15:00)',
            15: '申时 (15:00-17:00)', 16: '申时 (15:00-17:00)',
            17: '酉时 (17:00-19:00)', 18: '酉时 (17:00-19:00)',
            19: '戌时 (19:00-21:00)', 20: '戌时 (19:00-21:00)',
            21: '亥时 (21:00-23:00)', 22: '亥时 (21:00-23:00)'
        };

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMsg');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // 更新时辰显示
        function updateTimeHelper(hour) {
            const helper = document.getElementById('timeHelper');
            const timeTable = document.getElementById('timeTable');

            if (hour >= 0 && hour <= 23) {
                const timeName = timeMap[hour];
                helper.innerHTML = `<strong>对应时辰：${timeName}</strong>`;
                helper.classList.add('active');

                // 显示时辰对照表
                timeTable.style.display = 'block';

                // 高亮当前时辰
                document.querySelectorAll('.time-item').forEach(item => {
                    item.classList.remove('current');
                    const hours = item.dataset.hours.split(',').map(h => parseInt(h));
                    if (hours.includes(hour)) {
                        item.classList.add('current');
                    }
                });
            } else {
                helper.innerHTML = '<small>选择小时后将显示对应时辰</small>';
                helper.classList.remove('active');
                timeTable.style.display = 'none';
            }
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('calculateBtn').disabled = show;
            document.getElementById('quickBtn').disabled = show;
        }

        // 详细表单提交
        document.getElementById('paipanForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            showLoading(true);

            // 添加超时处理
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

            fetch('/calculate', {
                method: 'POST',
                body: formData,
                signal: controller.signal
            })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                showLoading(false);
                if (data.success) {
                    if (data.record_id) {
                        // 通过record_id跳转到结果页面
                        window.location.href = `/result/${data.record_id}`;
                    } else if (data.temp_data) {
                        // 临时数据，使用原来的方式
                        sessionStorage.setItem('paipanResult', JSON.stringify(data));
                        window.location.href = '/result';
                    } else {
                        showError('数据保存异常，请重试');
                    }
                } else {
                    showError(data.error || '计算失败，请检查输入信息');
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                showLoading(false);
                if (error.name === 'AbortError') {
                    showError('请求超时，请重试或检查网络连接');
                } else {
                    showError('网络错误：' + error.message + '。请刷新页面重试');
                }
                console.error('计算错误:', error);
            });
        });

        // 快速输入
        document.getElementById('quickBtn').addEventListener('click', function() {
            const quickInput = document.getElementById('quickInput').value.trim();
            if (!quickInput) {
                showError('请输入出生时间，如：1990-3-15-8 或 1990年3月15日8时');
                return;
            }

            const gender = document.querySelector('input[name="gender"]:checked').value;
            showLoading(true);

            // 添加超时处理
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

            fetch('/api/quick_calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    birth_string: quickInput,
                    gender: gender
                }),
                signal: controller.signal
            })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                showLoading(false);
                if (data.success) {
                    // 保存结果到sessionStorage
                    sessionStorage.setItem('paipanResult', JSON.stringify(data));
                    // 跳转到结果页面
                    window.location.href = '/result';
                } else {
                    showError(data.error || '时间格式错误，请使用如：1990-3-15-8 的格式');
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                showLoading(false);
                if (error.name === 'AbortError') {
                    showError('请求超时，请重试或检查网络连接');
                } else {
                    showError('网络错误：' + error.message + '。请刷新页面重试');
                }
                console.error('快速计算错误:', error);
            });
        });

        // 快速输入回车键支持
        document.getElementById('quickInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('quickBtn').click();
            }
        });
    </script>
</body>
</html>
