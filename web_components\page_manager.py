#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面管理器 - 统一的页面渲染和路由管理
"""

import streamlit as st
from typing import Dict, List, Optional
from datetime import datetime, date
import asyncio
import time
from .ui_components import UIComponents, NavigationManager, DataManager

class PageManager:
    """页面管理器"""

    def __init__(self):
        self.ui = UIComponents()
        self.nav = NavigationManager()
        self.data = DataManager()

    def render_main_content(self):
        """渲染主内容区域"""
        current_view = self.nav.get_current_view()

        # 页面路由
        page_handlers = {
            'overview': self.render_overview,
            'records': self.render_records,
            'create': self.render_create,
            'compatibility': self.render_compatibility,
            'liuyao': self.render_liuyao,
            'monitor': self.render_monitor,
            'detail': self.render_detail,
            'export': self.render_export,
            'clean_cache': self.render_clean_cache,
            'settings': self.render_settings
        }

        handler = page_handlers.get(current_view, self.render_overview)
        handler()

    def render_overview(self):
        """渲染系统概览页面"""
        st.markdown("## 📊 系统概览")

        cache_records = self.data.get_cache_records()

        if cache_records:
            self._render_overview_metrics(cache_records)
            self._render_recent_completed(cache_records)
        else:
            self._render_welcome_message()

    def _render_overview_metrics(self, records: List[Dict]):
        """渲染炫酷的概览指标"""
        # 计算统计数据
        stats = self._calculate_overview_stats(records)

        # 使用新的仪表盘网格组件
        metrics = [
            {
                'title': '总记录数',
                'value': str(stats['total']),
                'icon': '🗂️',
                'color': '#00f5ff'
            },
            {
                'title': '已完成',
                'value': str(stats['completed']),
                'icon': '✨',
                'color': '#00ff88'
            },
            {
                'title': '进行中',
                'value': str(stats['in_progress']),
                'icon': '⚡',
                'color': '#ffaa00'
            },
            {
                'title': '待处理',
                'value': str(stats['pending']),
                'icon': '⏳',
                'color': '#ff6b6b'
            }
        ]

        self.ui.render_dashboard_grid(metrics)

    def _render_recent_completed(self, records: List[Dict]):
        """渲染最近完成的分析"""
        st.markdown("### 📋 最近完成的分析")

        completed_records = [r for r in records if self._is_completed(r)]

        if completed_records:
            for record in completed_records[:5]:
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.markdown(f"**{record['birth_info']}** - {record['total_words']:,}字")
                with col2:
                    if st.button("查看", key=f"overview_view_{record['result_id']}",
                               use_container_width=True):
                        self.nav.navigate_to('detail', record['result_id'])
        else:
            self.ui.render_alert("暂无已完成的分析", "info", "📝")

    def _render_welcome_message(self):
        """渲染炫酷的欢迎信息"""
        # 欢迎标题
        st.markdown("""
        <div style="text-align: center; padding: 2rem 0; margin-bottom: 2rem;">
            <h2 style="background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 100%);
                       -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                       font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;
                       text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);">
                🎯 欢迎进入未来算命时代
            </h2>
            <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.2rem; margin: 0;">
                AI + 传统算法 = 前所未有的精准体验
            </p>
        </div>
        """, unsafe_allow_html=True)

        # 系统特色卡片
        features = [
            {
                'title': '紫薇+八字融合分析',
                'content': '双重算法相互印证，确保分析准确性。结合紫薇斗数的星曜分析和八字命理的五行推演，为您提供最全面的命运解读。',
                'icon': '🔮',
                'color': '#00f5ff'
            },
            {
                'title': '六爻占卜预测',
                'content': '独立的占卜系统，基于传统六爻理论，结合现代AI算法，为您的疑问提供精准的预测和指导。',
                'icon': '🎯',
                'color': '#ff00ff'
            },
            {
                'title': 'HTML可视化图表',
                'content': '现代化的命盘展示，采用响应式设计，支持多设备查看。美观的图表让复杂的命理信息一目了然。',
                'icon': '📊',
                'color': '#00ff88'
            },
            {
                'title': '12角度深度分析',
                'content': '专业的命理解读，涵盖命宫、财富、婚姻、健康等12个人生重要方面，每个角度都有详细的分析和建议。',
                'icon': '🎨',
                'color': '#ffaa00'
            }
        ]

        for feature in features:
            self.ui.render_cyber_card(
                title=feature['title'],
                content=feature['content'],
                icon=feature['icon'],
                accent_color=feature['color']
            )

        # 开始使用提示
        st.markdown("""
        <div style="text-align: center; padding: 2rem; margin: 2rem 0;
                    background: rgba(255, 255, 255, 0.02); backdrop-filter: blur(20px);
                    border: 1px solid rgba(0, 245, 255, 0.3); border-radius: 20px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);">
            <h3 style="color: #00f5ff; margin-bottom: 1rem; font-weight: 600;">
                🚀 开始您的命运探索之旅
            </h3>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.1rem; margin-bottom: 1.5rem;">
                当前系统中暂无分析记录，点击左侧导航开始您的第一次分析
            </p>
            <div style="display: flex; justify-content: center; gap: 1rem; flex-wrap: wrap;">
                <span style="padding: 0.5rem 1rem; background: rgba(0, 245, 255, 0.1);
                           border: 1px solid rgba(0, 245, 255, 0.3); border-radius: 15px;
                           color: #00f5ff; font-size: 0.9rem;">🆕 创建分析</span>
                <span style="padding: 0.5rem 1rem; background: rgba(255, 0, 255, 0.1);
                           border: 1px solid rgba(255, 0, 255, 0.3); border-radius: 15px;
                           color: #ff00ff; font-size: 0.9rem;">💕 合盘分析</span>
                <span style="padding: 0.5rem 1rem; background: rgba(0, 255, 136, 0.1);
                           border: 1px solid rgba(0, 255, 136, 0.3); border-radius: 15px;
                           color: #00ff88; font-size: 0.9rem;">🔮 六爻占卜</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

    def render_records(self):
        """渲染记录列表页面"""
        st.markdown("## 📋 分析记录管理")

        cache_records = self.data.get_cache_records()

        if cache_records:
            self._render_records_filter(cache_records)
        else:
            self.ui.render_alert("暂无分析记录", "info", "📝")

    def _render_records_filter(self, records: List[Dict]):
        """渲染记录筛选和列表"""
        # 筛选控件
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            search_term = st.text_input("🔍 搜索", placeholder="输入生辰信息或关键词...")
        with col2:
            status_filter = st.selectbox("状态筛选", ["全部", "已完成", "进行中", "待处理"])
        with col3:
            type_filter = st.selectbox("类型筛选", ["全部", "命理分析", "合盘分析", "六爻占卜"])

        # 应用筛选
        filtered_records = self._apply_filters(records, search_term, status_filter, type_filter)

        st.markdown(f"### 找到 {len(filtered_records)} 条记录")

        # 记录列表
        for record in filtered_records:
            self._render_record_item(record)

    def _render_record_item(self, record: Dict):
        """渲染单个记录项"""
        calculation_type = record.get('calculation_type', 'ziwei')
        is_completed = self._is_completed(record)

        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            # 状态图标和信息
            if calculation_type == 'compatibility':
                icon = "💕"
                status_text = "合盘完成" if is_completed else "分析中..."
            elif calculation_type == 'liuyao':
                icon = "🔮"
                status_text = "占卜完成" if is_completed else "解卦中..."
            else:
                icon = "✅" if is_completed else "🔄"
                status_text = f"{record['completed_angles']}/12 角度"

            st.markdown(f"{icon} **{record['birth_info']}**")
            st.caption(f"{status_text} - {record['total_words']:,}字")

        with col2:
            # 进度显示
            if calculation_type in ['compatibility', 'liuyao']:
                progress_text = "✅ 完成" if is_completed else "🔄 处理中"
            else:
                progress_text = f"{record['completed_angles']}/12"
            st.markdown(f"**{progress_text}**")

        with col3:
            # 操作按钮
            if is_completed:
                if st.button("查看详情", key=f"records_view_{record['result_id']}",
                           use_container_width=True):
                    self.nav.navigate_to('detail', record['result_id'])
            else:
                st.markdown("*处理中...*")

        st.markdown("---")

    def render_create(self):
        """渲染创建分析页面"""
        st.markdown("## 🆕 创建新分析")

        with st.form("create_analysis_form"):
            self._render_birth_info_form()
            self._render_analysis_options()

            submitted = st.form_submit_button("🚀 开始分析", use_container_width=True)

            if submitted:
                self._handle_create_submission()

    def _render_birth_info_form(self):
        """渲染生辰信息表单"""
        st.markdown("### 📋 生辰信息")

        col1, col2 = st.columns(2)

        with col1:
            st.session_state.form_year = st.text_input("出生年份", value="1990")
            st.session_state.form_day = st.text_input("出生日", value="15")
            st.session_state.form_gender = st.selectbox("性别", ["女", "男"])

        with col2:
            st.session_state.form_month = st.text_input("出生月份", value="3")

            hour_options = [
                "子时 (23:00-01:00)", "丑时 (01:00-03:00)", "寅时 (03:00-05:00)",
                "卯时 (05:00-07:00)", "辰时 (07:00-09:00)", "巳时 (09:00-11:00)",
                "午时 (11:00-13:00)", "未时 (13:00-15:00)", "申时 (15:00-17:00)",
                "酉时 (17:00-19:00)", "戌时 (19:00-21:00)", "亥时 (21:00-23:00)"
            ]
            st.session_state.form_hour = st.selectbox("出生时辰", hour_options, index=4)

    def _render_analysis_options(self):
        """渲染分析选项"""
        st.markdown("### 🎯 分析类型")

        st.session_state.form_analysis_type = st.selectbox(
            "选择分析方式",
            ["⚡ 紫薇+八字 - 相互印证综合分析"],
            index=0,
            help="紫薇斗数+八字算命双重印证，确保分析准确性"
        )

        self.ui.render_alert(
            "本系统专注于紫薇+八字融合分析，双重算法相互印证，确保准确性",
            "info",
            "🌟"
        )

    def _handle_create_submission(self):
        """处理创建提交"""
        try:
            # 验证输入
            year = int(st.session_state.form_year)
            month = int(st.session_state.form_month)
            day = int(st.session_state.form_day)

            if not (1900 <= year <= 2100) or not (1 <= month <= 12) or not (1 <= day <= 31):
                st.error("❌ 请输入有效的日期")
                return

            # 构建生辰信息
            birth_info = {
                "year": str(year),
                "month": str(month),
                "day": str(day),
                "hour": st.session_state.form_hour.split(" ")[0],
                "gender": st.session_state.form_gender
            }

            # 创建分析
            result_id = self._create_analysis(birth_info, "combined")
            if result_id:
                self.nav.navigate_to('monitor')
                st.session_state.monitoring_task = result_id

        except ValueError:
            st.error("❌ 请输入有效的数字")
        except Exception as e:
            st.error(f"❌ 创建分析失败: {e}")

    def render_compatibility(self):
        """渲染合盘分析页面"""
        st.markdown("## 💕 合盘分析")
        st.markdown("### 👫 分析两人的命盘匹配度和相互适应性")

        self.ui.render_alert(
            "合盘分析功能：分析情侣夫妻、合作伙伴、家庭关系等多维度匹配度",
            "info",
            "💡"
        )

        with st.form("compatibility_form"):
            self._render_compatibility_form()

            submitted = st.form_submit_button("💕 开始合盘分析", use_container_width=True)

            if submitted:
                self._handle_compatibility_submission()

    def _render_compatibility_form(self):
        """渲染合盘分析表单"""
        st.markdown("### 👤 双方基本信息")

        col1, col2 = st.columns(2)

        # A的信息
        with col1:
            st.markdown("#### 👤 A的信息")
            st.session_state.comp_name_a = st.text_input("姓名/称呼", value="张三", key="name_a")
            st.session_state.comp_date_a = st.date_input(
                "出生日期",
                value=date(1995, 3, 15),
                key="date_a"
            )

            col_a1, col_a2 = st.columns(2)
            with col_a1:
                st.session_state.comp_gender_a = st.selectbox("性别", ["男", "女"], key="gender_a")
            with col_a2:
                st.session_state.comp_hour_a = st.selectbox(
                    "出生时辰",
                    ["子时(23-01)", "丑时(01-03)", "寅时(03-05)", "卯时(05-07)",
                     "辰时(07-09)", "巳时(09-11)", "午时(11-13)", "未时(13-15)",
                     "申时(15-17)", "酉时(17-19)", "戌时(19-21)", "亥时(21-23)"],
                    index=4, key="hour_a"
                )

        # B的信息
        with col2:
            st.markdown("#### 👤 B的信息")
            st.session_state.comp_name_b = st.text_input("姓名/称呼", value="李四", key="name_b")
            st.session_state.comp_date_b = st.date_input(
                "出生日期",
                value=date(1996, 8, 20),
                key="date_b"
            )

            col_b1, col_b2 = st.columns(2)
            with col_b1:
                st.session_state.comp_gender_b = st.selectbox("性别", ["女", "男"], key="gender_b")
            with col_b2:
                st.session_state.comp_hour_b = st.selectbox(
                    "出生时辰",
                    ["子时(23-01)", "丑时(01-03)", "寅时(03-05)", "卯时(05-07)",
                     "辰时(07-09)", "巳时(09-11)", "午时(11-13)", "未时(13-15)",
                     "申时(15-17)", "酉时(17-19)", "戌时(19-21)", "亥时(21-23)"],
                    index=9, key="hour_b"
                )

        # 分析维度
        st.markdown("### 🎯 选择分析维度")

        dimensions = {
            "personality_compatibility": "💭 性格互补性",
            "emotional_harmony": "💕 感情和谐度",
            "wealth_cooperation": "💰 财运配合度",
            "career_partnership": "💼 事业合作潜力",
            "family_harmony": "🏠 家庭和睦度",
            "overall_compatibility": "🌟 综合匹配度"
        }

        st.session_state.comp_dimension = st.selectbox(
            "选择分析重点",
            list(dimensions.keys()),
            format_func=lambda x: dimensions[x],
            index=0
        )

    def _handle_compatibility_submission(self):
        """处理合盘分析提交"""
        try:
            # 验证输入
            if not st.session_state.comp_name_a.strip() or not st.session_state.comp_name_b.strip():
                st.error("❌ 请输入双方的姓名或称呼")
                return

            # 构建双方信息
            person_a = {
                "name": st.session_state.comp_name_a.strip(),
                "year": str(st.session_state.comp_date_a.year),
                "month": str(st.session_state.comp_date_a.month),
                "day": str(st.session_state.comp_date_a.day),
                "hour": st.session_state.comp_hour_a.split("(")[0],
                "gender": st.session_state.comp_gender_a
            }

            person_b = {
                "name": st.session_state.comp_name_b.strip(),
                "year": str(st.session_state.comp_date_b.year),
                "month": str(st.session_state.comp_date_b.month),
                "day": str(st.session_state.comp_date_b.day),
                "hour": st.session_state.comp_hour_b.split("(")[0],
                "gender": st.session_state.comp_gender_b
            }

            # 创建合盘分析
            result_id = self._create_compatibility_analysis(
                person_a, person_b, st.session_state.comp_dimension
            )

            if result_id:
                self.nav.navigate_to('monitor')
                st.session_state.monitoring_task = result_id

        except Exception as e:
            st.error(f"❌ 创建合盘分析失败: {e}")

    def render_monitor(self):
        """渲染监控页面"""
        st.markdown("## 📈 分析进度监控")

        cache_records = self.data.get_cache_records()

        # 分类任务
        active_tasks = []
        completed_tasks = []

        for record in cache_records:
            if self._is_completed(record):
                completed_tasks.append(record)
            elif record['has_analysis']:
                active_tasks.append(record)

        # 显示任务
        if active_tasks:
            st.markdown("### 🔄 正在进行的分析")
            for task in active_tasks:
                self._render_task_progress(task)

        if completed_tasks[:3]:
            st.markdown("### ✅ 最近完成的分析")
            for task in completed_tasks[:3]:
                self._render_task_progress(task, completed=True)

        if not active_tasks and not completed_tasks:
            self.ui.render_alert("当前没有分析任务", "info", "📝")

            if st.button("🆕 创建新分析", use_container_width=True):
                self.nav.navigate_to('create')

    def _render_task_progress(self, task: Dict, completed: bool = False):
        """渲染任务进度"""
        calculation_type = task.get('calculation_type', 'ziwei')

        # 进度计算
        if calculation_type in ['compatibility', 'liuyao']:
            progress = 1.0 if completed else 0.5
            progress_text = "完成" if completed else "处理中..."
        else:
            progress = task['completed_angles'] / 12
            progress_text = f"{task['completed_angles']}/12 ({progress*100:.1f}%)"

        # 渲染进度卡片
        self.ui.render_modern_card(
            task['birth_info'],
            f"进度: {progress_text} | 字数: {task['total_words']:,}",
            "🔮" if calculation_type == 'liuyao' else "💕" if calculation_type == 'compatibility' else "📊"
        )

        # 进度条
        self.ui.render_progress_bar(progress, progress_text)

        # 操作按钮
        col1, col2, col3 = st.columns([2, 1, 1])

        with col2:
            if completed:
                st.success("✅ 完成")
            else:
                st.info("🔄 处理中")

        with col3:
            if completed:
                if st.button("查看结果", key=f"monitor_view_{task['result_id']}",
                           use_container_width=True):
                    self.nav.navigate_to('detail', task['result_id'])
            else:
                if st.button("刷新", key=f"monitor_refresh_{task['result_id']}",
                           use_container_width=True):
                    st.rerun()

    # 辅助方法
    def _calculate_overview_stats(self, records: List[Dict]) -> Dict:
        """计算概览统计"""
        total = len(records)
        completed = sum(1 for r in records if self._is_completed(r))
        in_progress = sum(1 for r in records if r['has_analysis'] and not self._is_completed(r))
        pending = total - completed - in_progress

        return {
            'total': total,
            'completed': completed,
            'in_progress': in_progress,
            'pending': pending
        }

    def _is_completed(self, record: Dict) -> bool:
        """判断记录是否完成"""
        calculation_type = record.get('calculation_type', 'ziwei')

        if calculation_type in ['compatibility', 'liuyao']:
            return record['completed_angles'] > 0 and record['total_words'] > 0
        else:
            return record['completed_angles'] >= 12

    def _apply_filters(self, records: List[Dict], search: str, status: str, type_filter: str) -> List[Dict]:
        """应用筛选条件"""
        filtered = records

        # 搜索筛选
        if search:
            filtered = [r for r in filtered if search.lower() in r['birth_info'].lower()]

        # 状态筛选
        if status == "已完成":
            filtered = [r for r in filtered if self._is_completed(r)]
        elif status == "进行中":
            filtered = [r for r in filtered if r['has_analysis'] and not self._is_completed(r)]
        elif status == "待处理":
            filtered = [r for r in filtered if not r['has_analysis']]

        # 类型筛选
        if type_filter != "全部":
            type_map = {
                "命理分析": "ziwei",
                "合盘分析": "compatibility",
                "六爻占卜": "liuyao"
            }
            target_type = type_map.get(type_filter)
            if target_type:
                filtered = [r for r in filtered if r.get('calculation_type') == target_type]

        return filtered

    def _create_analysis(self, birth_info: Dict, analysis_type: str) -> Optional[str]:
        """创建分析任务"""
        # 这里应该调用后台Agent创建分析
        # 暂时返回模拟的result_id
        return f"analysis_{int(time.time())}"

    def _create_compatibility_analysis(self, person_a: Dict, person_b: Dict, dimension: str) -> Optional[str]:
        """创建合盘分析任务"""
        # 这里应该调用合盘分析引擎
        # 暂时返回模拟的result_id
        return f"compatibility_{int(time.time())}"

    # 其他页面渲染方法将在后续添加
    def render_liuyao(self):
        """渲染六爻占卜页面"""
        st.markdown("## 🔮 六爻占卜")
        st.info("六爻占卜功能开发中...")

    def render_detail(self):
        """渲染详情页面"""
        st.markdown("## 📋 分析详情")
        st.info("详情页面开发中...")

    def render_export(self):
        """渲染导出页面"""
        st.markdown("## 📊 数据导出")
        st.info("导出功能开发中...")

    def render_clean_cache(self):
        """渲染清理缓存页面"""
        st.markdown("## 🧹 清理缓存")
        st.info("缓存清理功能开发中...")

    def render_settings(self):
        """渲染设置页面"""
        st.markdown("## ⚙️ 系统设置")
        st.info("设置页面开发中...")
