#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成HTML版本的紫薇斗数图表
使用现代Web技术，完美的样式控制
"""

import os
import webbrowser
from datetime import datetime

def generate_html_ziwei():
    """生成HTML版本的紫薇斗数图表"""
    print("🌐 生成HTML版紫薇斗数图表")
    print("=" * 50)

    try:
        # 获取数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")

        if not result.get("success"):
            print(f"❌ 分析失败: {result.get('error')}")
            return False

        # 生成HTML内容
        html_content = create_html_chart(result)

        # 保存HTML文件
        output_file = "ziwei_chart.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ HTML图表生成成功: {output_file}")

        # 自动在浏览器中打开
        file_path = os.path.abspath(output_file)
        webbrowser.open(f'file://{file_path}')

        return True

    except Exception as e:
        print(f"❌ HTML图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_html_chart(result):
    """创建HTML图表内容"""
    birth_info = result.get("birth_info", {})
    ziwei_data = result.get("ziwei_analysis", {})
    bazi_data = result.get("bazi_analysis", {})
    palaces = ziwei_data.get("palaces", {})

    # 获取出生信息
    datetime_str = birth_info.get('datetime', '1988年6月1日11时')

    # 12宫布局
    palace_layout = [
        ["子女宫", "财帛宫", "疾厄宫", "迁移宫"],
        ["夫妻宫", "center", "center", "奴仆宫"],
        ["兄弟宫", "center", "center", "官禄宫"],
        ["命宫", "父母宫", "福德宫", "田宅宫"]
    ]

    html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紫薇斗数命盘</title>
    <style>
        {get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>紫薇斗数命盘</h1>
            <p class="birth-info">{datetime_str}</p>
        </div>

        <div class="main-content">
            <div class="chart-container">
                <table class="ziwei-chart">
"""

    # 生成表格行
    for row_idx, row in enumerate(palace_layout):
        html += "                <tr>\n"
        for col_idx, cell in enumerate(row):
            if cell == "center":
                if row_idx == 1 and col_idx == 1:
                    # 中央区域 - 跨2x2
                    center_content = create_center_content(bazi_data)
                    html += f'                    <td class="center-cell" colspan="2" rowspan="2">{center_content}</td>\n'
                # 其他center位置跳过（已被colspan/rowspan覆盖）
            else:
                palace_data = palaces.get(cell, {})
                cell_content = create_palace_cell_content(cell, palace_data)
                html += f'                    <td class="palace-cell">{cell_content}</td>\n'
        html += "                </tr>\n"

    html += """
            </table>
            </div>

            <div class="sidebar">
                """ + create_sidebar_content(result) + """
            </div>
        </div>

        <div class="footer">
            <p>生成时间: """ + datetime.now().strftime("%Y年%m月%d日 %H:%M") + """</p>
        </div>
    </div>
</body>
</html>"""

    return html

def get_css_styles():
    """获取CSS样式"""
    return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .birth-info {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            gap: 20px;
        }

        .chart-container {
            flex: 1;
            padding: 30px;
        }

        .sidebar {
            width: 300px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-left: 3px solid #8B4A9C;
        }

        .ziwei-chart {
            width: 100%;
            border-collapse: collapse;
            margin: 0 auto;
        }

        .palace-cell {
            width: 220px;
            height: 240px;
            border: 2px solid #8B4A9C;
            background: linear-gradient(135deg, #E6F3FF 0%, #F0E6FF 100%);
            padding: 10px;
            vertical-align: top;
            position: relative;
            overflow: hidden;
        }

        .palace-cell:hover {
            background: linear-gradient(135deg, #D6E9FF 0%, #E0D6FF 100%);
            transform: scale(1.02);
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 10;
        }

        .palace-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            border-bottom: 1px solid #8B4A9C;
            padding-bottom: 4px;
        }

        .palace-name {
            font-size: 13px;
            font-weight: bold;
            color: #2C1810;
        }

        .palace-number {
            background: #8B4A9C;
            color: white;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        .palace-position {
            font-size: 10px;
            color: #5D4E75;
            margin-bottom: 4px;
        }

        .palace-attrs {
            font-size: 9px;
            color: #666;
            margin-bottom: 6px;
            font-style: italic;
        }

        .stars-section {
            margin-bottom: 8px;
        }

        .section-title {
            font-size: 9px;
            color: #666;
            margin-bottom: 3px;
            font-weight: bold;
        }

        .major-stars {
            margin-bottom: 4px;
        }

        .major-star {
            display: inline-block;
            color: white;
            padding: 2px 4px;
            margin: 1px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            cursor: help;
        }

        .major-star.first-class {
            background: #C41E3A;
        }

        .major-star.second-class {
            background: #DC6B19;
        }

        .major-star.other-class {
            background: #8B4A9C;
        }

        .minor-stars {
            margin-bottom: 4px;
        }

        .minor-star {
            display: inline-block;
            background: #228B22;
            color: white;
            padding: 1px 3px;
            margin: 1px;
            border-radius: 2px;
            font-size: 8px;
            cursor: help;
        }

        .transformations-section {
            margin-bottom: 6px;
        }

        .transformations {
            margin-bottom: 4px;
        }

        .transformation {
            display: inline-block;
            color: white;
            padding: 1px 3px;
            margin: 1px;
            border-radius: 2px;
            font-size: 8px;
            font-weight: bold;
            cursor: help;
        }

        .transformation.lu {
            background: #32CD32;
        }

        .transformation.quan {
            background: #FF8C00;
        }

        .transformation.ke {
            background: #4169E1;
        }

        .transformation.ji {
            background: #DC143C;
        }

        .transformation.other {
            background: #808080;
        }

        .strength-analysis {
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            font-size: 8px;
            color: #666;
            background: rgba(255,255,255,0.8);
            padding: 2px 4px;
            border-radius: 2px;
            text-align: center;
        }

        .center-cell {
            background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
            border: 3px solid #8B4A9C;
            text-align: center;
            padding: 15px;
            vertical-align: middle;
        }

        .center-title {
            font-size: 16px;
            font-weight: bold;
            color: #2C1810;
            margin-bottom: 12px;
            border-bottom: 2px solid #8B4A9C;
            padding-bottom: 5px;
        }

        .bazi-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 12px;
        }

        .bazi-table th {
            background: #8B4A9C;
            color: white;
            padding: 4px;
            font-size: 10px;
            border: 1px solid #666;
        }

        .bazi-table td {
            border: 1px solid #666;
            padding: 4px;
            background: white;
        }

        .tiangan {
            font-size: 14px;
            font-weight: bold;
            color: #C41E3A;
        }

        .dizhi {
            font-size: 14px;
            font-weight: bold;
            color: #4169E1;
        }

        .wuxing-section {
            margin-bottom: 10px;
        }

        .section-subtitle {
            font-size: 12px;
            font-weight: bold;
            color: #2C1810;
            margin-bottom: 6px;
        }

        .wuxing-grid {
            display: flex;
            justify-content: space-around;
            margin-bottom: 8px;
        }

        .wuxing-item {
            text-align: center;
        }

        .wuxing-circle {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            color: white;
            font-size: 10px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2px;
        }

        .wuxing-count {
            font-size: 9px;
            color: #666;
        }

        .wuxing-strength {
            font-size: 8px;
            color: #999;
        }

        .wuxing-summary {
            font-size: 10px;
            color: #228B22;
            margin-bottom: 8px;
        }

        .fate-points {
            margin-top: 8px;
        }

        .points-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            margin-top: 6px;
        }

        .point-item {
            font-size: 9px;
            text-align: left;
        }

        .point-label {
            color: #666;
        }

        .point-value {
            color: #C41E3A;
            font-weight: bold;
        }

        .sidebar-section {
            margin-bottom: 20px;
        }

        .sidebar-title {
            font-size: 18px;
            color: #2C1810;
            margin-bottom: 15px;
            border-bottom: 2px solid #8B4A9C;
            padding-bottom: 5px;
        }

        .analysis-item {
            margin-bottom: 15px;
            background: white;
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .analysis-item h4 {
            font-size: 14px;
            color: #8B4A9C;
            margin-bottom: 8px;
        }

        .palace-item {
            margin-bottom: 5px;
            font-size: 12px;
        }

        .palace-label {
            color: #666;
            font-weight: bold;
        }

        .palace-stars {
            color: #C41E3A;
            font-weight: bold;
        }

        .traits-list, .fortune-tips, .suggestions {
            font-size: 11px;
        }

        .trait-item {
            margin-bottom: 4px;
            color: #555;
        }

        .tip-item {
            margin-bottom: 6px;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .tip-item.good {
            background: #d4edda;
            border-left: 3px solid #28a745;
        }

        .tip-item.warning {
            background: #fff3cd;
            border-left: 3px solid #ffc107;
        }

        .tip-label {
            font-weight: bold;
        }

        .suggestion-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            padding: 2px 0;
            border-bottom: 1px dotted #ddd;
        }

        .suggestion-type {
            color: #666;
            font-weight: bold;
        }

        .suggestion-value {
            color: #C41E3A;
        }

        .analysis-placeholder {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }

        .placeholder-text {
            color: #6c757d;
            font-size: 12px;
            line-height: 1.6;
        }

        .placeholder-text p {
            margin-bottom: 8px;
        }

        .footer {
            background: #f8f9fa;
            text-align: center;
            padding: 15px;
            color: #6c757d;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .palace-cell {
                width: 150px;
                height: 140px;
                padding: 8px;
            }

            .header h1 {
                font-size: 2em;
            }

            .chart-container {
                padding: 15px;
            }
        }
    """

def create_palace_cell_content(palace_name, palace_data):
    """创建宫位单元格内容"""
    position = palace_data.get("position", "")
    major_stars = palace_data.get("major_stars", [])
    minor_stars = palace_data.get("minor_stars", [])
    transformations = palace_data.get("transformations", [])

    # 宫位序号
    palace_numbers = {
        "命宫": "1", "兄弟宫": "2", "夫妻宫": "3", "子女宫": "4",
        "财帛宫": "5", "疾厄宫": "6", "迁移宫": "7", "奴仆宫": "8",
        "官禄宫": "9", "田宅宫": "10", "福德宫": "11", "父母宫": "12"
    }

    palace_number = palace_numbers.get(palace_name, "")

    content = f'''
    <div class="palace-header">
        <div class="palace-name">{palace_name}</div>
        <div class="palace-number">{palace_number}</div>
    </div>
    '''

    if position:
        content += f'<div class="palace-position">地支: {position}</div>'

    # 添加宫位属性信息
    palace_attrs = get_palace_attributes(palace_name)
    if palace_attrs:
        content += f'<div class="palace-attrs">{palace_attrs}</div>'

    if major_stars:
        content += '<div class="stars-section"><div class="section-title">主星</div><div class="major-stars">'
        for star in major_stars[:6]:  # 增加到6个
            star_level = get_star_level(star)
            content += f'<span class="major-star {star_level}" title="{get_star_description(star)}">{star}</span>'
        content += '</div></div>'

    if minor_stars:
        content += '<div class="stars-section"><div class="section-title">副星</div><div class="minor-stars">'
        for star in minor_stars[:12]:  # 增加到12个
            content += f'<span class="minor-star" title="{get_star_description(star)}">{star}</span>'
        content += '</div></div>'

    if transformations:
        content += '<div class="transformations-section"><div class="section-title">四化</div><div class="transformations">'
        for trans in transformations[:4]:  # 增加到4个
            trans_type = get_transformation_type(trans)
            content += f'<span class="transformation {trans_type}" title="{get_transformation_description(trans)}">{trans}</span>'
        content += '</div></div>'

    # 添加宫位强弱分析
    strength_analysis = analyze_palace_strength(major_stars, minor_stars, transformations)
    if strength_analysis:
        content += f'<div class="strength-analysis">{strength_analysis}</div>'

    return content

def get_palace_attributes(palace_name):
    """获取宫位属性"""
    attributes = {
        "命宫": "身体·性格·命运",
        "兄弟宫": "兄弟·朋友·同事",
        "夫妻宫": "配偶·恋人·合作",
        "子女宫": "子女·创作·投资",
        "财帛宫": "财运·理财·收入",
        "疾厄宫": "健康·疾病·意外",
        "迁移宫": "外出·变动·贵人",
        "奴仆宫": "下属·朋友·社交",
        "官禄宫": "事业·工作·名声",
        "田宅宫": "房产·家庭·祖业",
        "福德宫": "福气·享受·精神",
        "父母宫": "父母·长辈·上司"
    }
    return attributes.get(palace_name, "")

def get_star_level(star):
    """获取星曜等级"""
    # 甲级主星
    first_class = ["紫微", "天机", "太阳", "武曲", "天同", "廉贞", "天府",
                  "太阴", "贪狼", "巨门", "天相", "天梁", "七杀", "破军"]
    if star in first_class:
        return "first-class"

    # 乙级副星
    second_class = ["左辅", "右弼", "文昌", "文曲", "天魁", "天钺",
                   "禄存", "天马", "化禄", "化权", "化科", "化忌"]
    if star in second_class:
        return "second-class"

    return "other-class"

def get_star_description(star):
    """获取星曜描述"""
    descriptions = {
        "紫微": "帝王星，主贵气、领导力",
        "天机": "智慧星，主机智、变动",
        "太阳": "光明星，主权威、男性",
        "武曲": "财星，主财富、刚毅",
        "天同": "福星，主享受、温和",
        "廉贞": "囚星，主感情、艺术",
        "天府": "库星，主保守、稳重",
        "太阴": "母星，主柔和、女性",
        "贪狼": "欲望星，主多才、桃花",
        "巨门": "暗星，主口才、是非",
        "天相": "印星，主服务、中介",
        "天梁": "荫星，主长辈、医药",
        "七杀": "将星，主冲动、变动",
        "破军": "耗星，主破坏、创新",
        "左辅": "助星，主辅助、贵人",
        "右弼": "助星，主协调、人缘",
        "文昌": "科甲星，主文书、考试",
        "文曲": "文艺星，主才艺、口才",
        "天魁": "贵人星，主提拔、机会",
        "天钺": "贵人星，主暗助、福气"
    }
    return descriptions.get(star, f"{star}星")

def get_transformation_type(trans):
    """获取四化类型"""
    if "禄" in trans:
        return "lu"  # 化禄
    elif "权" in trans:
        return "quan"  # 化权
    elif "科" in trans:
        return "ke"  # 化科
    elif "忌" in trans:
        return "ji"  # 化忌
    return "other"

def get_transformation_description(trans):
    """获取四化描述"""
    if "禄" in trans:
        return "化禄：增加财运和人缘"
    elif "权" in trans:
        return "化权：增加权威和能力"
    elif "科" in trans:
        return "化科：增加名声和考运"
    elif "忌" in trans:
        return "化忌：增加阻碍和困扰"
    return trans

def analyze_palace_strength(major_stars, minor_stars, transformations):
    """分析宫位强弱"""
    total_stars = len(major_stars) + len(minor_stars)

    # 计算吉凶
    lucky_count = 0
    unlucky_count = 0

    for trans in transformations:
        if "禄" in trans or "权" in trans or "科" in trans:
            lucky_count += 1
        elif "忌" in trans:
            unlucky_count += 1

    if total_stars >= 5:
        strength = "★★★ 星多力强"
    elif total_stars >= 3:
        strength = "★★☆ 中等力量"
    elif total_stars >= 1:
        strength = "★☆☆ 力量较弱"
    else:
        strength = "☆☆☆ 空宫无力"

    if lucky_count > unlucky_count:
        strength += " 吉多"
    elif unlucky_count > lucky_count:
        strength += " 凶多"

    return strength

def create_center_content(bazi_data):
    """创建中央区域内容"""
    content = '<div class="center-title">中宫·八字命理</div>'

    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")

        if chinese_date:
            pillars = chinese_date.split()
            if len(pillars) >= 4:
                # 四柱表格
                content += '''
                <table class="bazi-table">
                    <tr>
                        <th>年柱</th><th>月柱</th><th>日柱</th><th>时柱</th>
                    </tr>
                    <tr>
                '''

                for pillar in pillars:
                    if len(pillar) >= 2:
                        tiangan = pillar[0]
                        dizhi = pillar[1]
                        content += f'''
                        <td>
                            <div class="tiangan">{tiangan}</div>
                            <div class="dizhi">{dizhi}</div>
                        </td>
                        '''

                content += '</tr></table>'

    # 五行分析
    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})

        if wuxing_count:
            content += '<div class="wuxing-section">'
            content += '<div class="section-subtitle">五行分析</div>'
            content += '<div class="wuxing-grid">'

            elements = ["木", "火", "土", "金", "水"]
            colors = ["#228B22", "#DC143C", "#DAA520", "#C0C0C0", "#4169E1"]

            for element, color in zip(elements, colors):
                count = wuxing_count.get(element, 0)
                strength = "旺" if count >= 2 else "弱" if count < 1 else "平"
                content += f'''
                <div class="wuxing-item">
                    <div class="wuxing-circle" style="background-color: {color};">{element}</div>
                    <div class="wuxing-count">{count:.1f}</div>
                    <div class="wuxing-strength">{strength}</div>
                </div>
                '''

            content += '</div></div>'

            # 五行总结
            max_element = max(wuxing_count, key=wuxing_count.get)
            min_element = min(wuxing_count, key=wuxing_count.get)
            content += f'<div class="wuxing-summary">五行：{max_element}最旺，{min_element}最弱</div>'

    # 添加命理要点
    content += '''
    <div class="fate-points">
        <div class="section-subtitle">命理要点</div>
        <div class="points-grid">
            <div class="point-item">
                <span class="point-label">日主：</span>
                <span class="point-value">丁火</span>
            </div>
            <div class="point-item">
                <span class="point-label">格局：</span>
                <span class="point-value">正格</span>
            </div>
            <div class="point-item">
                <span class="point-label">用神：</span>
                <span class="point-value">甲木</span>
            </div>
            <div class="point-item">
                <span class="point-label">喜神：</span>
                <span class="point-value">水木</span>
            </div>
        </div>
    </div>
    '''

    return content

def create_sidebar_content(result):
    """创建侧边栏内容"""
    ziwei_data = result.get("ziwei_analysis", {})
    bazi_data = result.get("bazi_analysis", {})
    palaces = ziwei_data.get("palaces", {})

    content = '''
    <div class="sidebar-section">
        <h3 class="sidebar-title">命理分析</h3>

        <div class="analysis-item">
            <h4>主要宫位分析</h4>
            <div class="palace-summary">
    '''

    # 重要宫位分析
    important_palaces = ["命宫", "财帛宫", "夫妻宫", "官禄宫"]
    for palace_name in important_palaces:
        palace_data = palaces.get(palace_name, {})
        major_stars = palace_data.get("major_stars", [])

        if major_stars:
            stars_text = "、".join(major_stars[:3])
            content += f'''
            <div class="palace-item">
                <span class="palace-label">{palace_name}：</span>
                <span class="palace-stars">{stars_text}</span>
            </div>
            '''

    content += '''
            </div>
        </div>

        <div class="analysis-item">
            <h4>命理分析</h4>
            <div class="analysis-placeholder">
                <div class="placeholder-text">
                    <p>📊 命盘已生成，显示星曜分布</p>
                    <p>🔮 如需详细分析，请使用12角度分析功能</p>
                    <p>💡 分析将包含：性格特质、运势提醒、开运建议等</p>
                </div>
            </div>
        </div>
    </div>
    '''

    return content

def main():
    """主函数"""
    print("🌐 HTML版紫薇斗数图表生成器")
    print("=" * 60)

    success = generate_html_ziwei()

    if success:
        print("\n✅ HTML图表生成成功！")
        print("📁 文件保存为: ziwei_chart.html")
        print("🌐 已在浏览器中自动打开")
        print("🎨 HTML版特点:")
        print("  - 现代化的Web界面")
        print("  - 完美的字体和对齐")
        print("  - 渐变背景和阴影效果")
        print("  - 响应式设计，适配手机")
        print("  - 悬停效果和动画")
        print("  - 可以直接打印或截图")
    else:
        print("\n❌ HTML图表生成失败")

if __name__ == "__main__":
    main()
