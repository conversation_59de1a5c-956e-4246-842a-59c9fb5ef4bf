#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的渐进式架构
验证：后台按顺序生成12个角度并渐进式保存，前端智能查询
"""

import asyncio
import sys
import time
import shutil
import os
from datetime import datetime
sys.path.append('.')

async def test_optimized_progressive():
    """测试优化后的渐进式架构"""
    print("🚀 测试优化后的渐进式架构")
    print("=" * 80)
    print("架构验证:")
    print("1. 前端Agent收集信息，调用后端Agent")
    print("2. 后端Agent按顺序生成12个角度，每完成一个立即保存")
    print("3. 前端Agent继续与用户互动，智能查询已完成的角度")
    print("4. 用户问财运时，如果财富角度已完成，立即基于4000字回答")
    print("=" * 80)
    
    try:
        # 清除缓存
        print("1️⃣ 清除缓存...")
        cache_dir = "data/calculation_cache"
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            print("✅ 缓存已清除")
        
        # 创建系统
        print("\n2️⃣ 创建优化后的双Agent系统...")
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 清除Agent注册
        agent_registry.agents.clear()
        
        # 创建新的Agent实例
        master_agent = MasterCustomerAgent("optimized_master")
        calculator_agent = FortuneCalculatorAgent("optimized_calc")
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print("✅ 优化系统创建完成")
        
        # 第一步：用户提供信息，触发后台12角度分析
        print("\n3️⃣ 第一步：触发后台12角度渐进式分析...")
        session_id = "optimized_test_session"
        
        user_message = "我想看紫薇斗数，1992年10月8日酉时出生，男性"
        print(f"👤 用户: {user_message}")
        
        start_time = time.time()
        result1 = await coordinator.handle_user_message(session_id, user_message)
        time1 = time.time() - start_time
        
        if result1.get('success'):
            response1 = result1.get('response', '')
            print(f"🤖 AI ({time1:.1f}s): {response1[:300]}...")
            
            if "分析已完成" in response1 or "专业解读" in response1:
                print("✅ 后台12角度分析已完成")
                
                # 获取结果ID
                session_state = master_agent.get_session_state(session_id)
                result_id = session_state.get("result_id") if session_state else None
                
                if result_id:
                    print(f"📋 结果ID: {result_id[:8]}...")
                    
                    # 检查分析进度
                    progress = master_agent.get_analysis_progress(result_id)
                    print(f"📊 分析进度: {progress}")
                    
                    completed_angles = progress.get("completed_angles", 0)
                    total_word_count = progress.get("total_word_count", 0)
                    
                    print(f"   已完成角度: {completed_angles}/12")
                    print(f"   总字数: {total_word_count}")
                    
                    if completed_angles >= 8 and total_word_count >= 30000:
                        print("✅ 12角度渐进式分析成功！")
                        
                        # 第二步：测试智能查询
                        print(f"\n4️⃣ 第二步：测试智能查询...")
                        
                        test_questions = [
                            ("我的性格有什么特点？", "personality_destiny", "命宫分析"),
                            ("我的财运怎么样？", "wealth_fortune", "财富分析"),
                            ("感情方面有什么建议？", "marriage_love", "婚姻分析"),
                            ("健康需要注意什么？", "health_wellness", "健康分析"),
                            ("事业发展如何？", "career_achievement", "事业分析")
                        ]
                        
                        successful_queries = 0
                        
                        for i, (question, angle_key, angle_name) in enumerate(test_questions, 1):
                            print(f"\n4.{i} 👤 用户: {question}")
                            
                            # 检查角度是否可用
                            angle_available = await master_agent._check_angle_availability(result_id, angle_key)
                            print(f"   📋 {angle_name}可用性: {'✅ 可用' if angle_available else '❌ 不可用'}")
                            
                            if angle_available:
                                # 测试基于角度的回答
                                qa_start = time.time()
                                qa_result = await coordinator.handle_user_message(session_id, question)
                                qa_time = time.time() - qa_start
                                
                                if qa_result.get('success'):
                                    qa_response = qa_result.get('response', '')
                                    print(f"   🤖 AI ({qa_time:.1f}s): {qa_response[:200]}...")
                                    
                                    # 检查回答质量
                                    if len(qa_response) > 300 and "根据您的" in qa_response:
                                        print(f"   ✅ 基于{angle_name}的详细回答")
                                        successful_queries += 1
                                    else:
                                        print(f"   ⚠️  回答质量一般")
                                else:
                                    print(f"   ❌ 回答失败: {qa_result.get('error')}")
                            else:
                                print(f"   ⚠️  {angle_name}尚未完成，跳过测试")
                        
                        # 评估结果
                        print(f"\n🎯 优化架构测试评估")
                        print("=" * 60)
                        
                        print(f"📊 测试结果:")
                        print(f"   后台分析完成度: {completed_angles}/12 ({completed_angles/12*100:.1f}%)")
                        print(f"   总分析字数: {total_word_count}")
                        print(f"   成功查询数: {successful_queries}/{len(test_questions)}")
                        
                        if completed_angles >= 10 and successful_queries >= 3:
                            print(f"\n🎉 优化架构测试成功！")
                            print(f"\n✅ 验证的功能:")
                            print(f"   - 后台Agent按顺序生成12个角度")
                            print(f"   - 每完成一个角度立即保存到缓存")
                            print(f"   - 前端Agent智能查询已完成的角度")
                            print(f"   - 基于4000字详细分析回答用户问题")
                            print(f"   - 前后端独立但相互交互")
                            
                            print(f"\n🌟 架构优势:")
                            print(f"   🗣️ 前端专注用户体验和智能查询")
                            print(f"   🧮 后端专注算法分析和渐进式保存")
                            print(f"   💾 每个角度4000-5000字专业内容")
                            print(f"   ⚡ 用户可以立即基于已完成角度互动")
                            
                            return True
                        else:
                            print(f"\n⚠️  优化架构需要进一步调整")
                            print(f"   问题可能在于:")
                            print(f"   - 角度生成数量不足")
                            print(f"   - 智能查询逻辑")
                            print(f"   - 缓存保存机制")
                            return False
                    else:
                        print(f"❌ 12角度分析不足")
                        print(f"   完成角度: {completed_angles}/12")
                        print(f"   总字数: {total_word_count}")
                        return False
                else:
                    print(f"❌ 未获取到结果ID")
                    return False
            else:
                print(f"❌ 后台分析可能失败")
                return False
        else:
            print(f"❌ 系统调用失败: {result1.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 优化架构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 优化后的渐进式架构测试")
    print("=" * 80)
    print("验证您的架构要求:")
    print("1. 前端Agent收集信息，调用后端Agent")
    print("2. 后端Agent按顺序生成12个角度，渐进式保存")
    print("3. 前端Agent智能查询，基于已完成角度回答")
    print("4. 前后端独立但相互交互")
    print("=" * 80)
    
    success = await test_optimized_progressive()
    
    if success:
        print(f"\n🎉 恭喜！优化后的渐进式架构完美实现！")
        print(f"\n✅ 您的架构需求完全满足:")
        print(f"   - 前端Agent专注用户交互和智能查询")
        print(f"   - 后端Agent专注算法分析和渐进式保存")
        print(f"   - 12个角度按顺序生成，每个4000-5000字")
        print(f"   - 用户可以立即基于已完成角度深度互动")
        print(f"   - 前后端独立工作但数据实时同步")
        
        print(f"\n🌟 用户体验:")
        print(f"   1. 提供生辰信息 → 后台开始12角度分析")
        print(f"   2. 问性格特点 → 基于命宫4000字详细回答")
        print(f"   3. 问财运状况 → 基于财富4000字专业分析")
        print(f"   4. 问感情运势 → 基于婚姻4000字深度解读")
        print(f"   5. 真正的专业大师级算命体验")
        
        print(f"\n🚀 现在可以体验优化后的完整功能：")
        print(f"   访问: http://localhost:8505")
    else:
        print(f"\n💥 优化架构需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
