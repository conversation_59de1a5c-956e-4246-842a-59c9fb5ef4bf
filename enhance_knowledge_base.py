#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强知识库 - 添加更多数据源
"""

import sys
sys.path.append('.')

from core.chat.knowledge_base import ChatKnowledgeBase

def add_comprehensive_knowledge_extraction():
    """添加更全面的知识提取"""
    
    # 在知识库类中添加更多提取方法
    additional_methods = """
    def _load_comprehensive_knowledge(self, cached_result):
        \"\"\"加载全面的知识\"\"\"
        try:
            # 1. 基本信息
            self._load_basic_info(cached_result.birth_info)
            
            # 2. 排盘数据
            raw_calculation = cached_result.raw_calculation
            self._load_ziwei_knowledge(raw_calculation)
            self._load_bazi_knowledge(raw_calculation)
            
            # 3. 分析结果
            self._load_analysis_knowledge(cached_result.detailed_analysis)
            
            # 4. 新增：图表信息
            self._load_chart_knowledge(cached_result)
            
            # 5. 新增：置信度信息
            self._load_confidence_knowledge(raw_calculation)
            
            # 6. 新增：时间相关信息
            self._load_temporal_knowledge(cached_result.birth_info)
            
        except Exception as e:
            logger.error(f"全面知识加载失败: {e}")
    
    def _load_chart_knowledge(self, cached_result):
        \"\"\"加载图表相关知识\"\"\"
        try:
            if hasattr(cached_result, 'chart_image_path') and cached_result.chart_image_path:
                self.add_knowledge("chart_info", "排盘图路径", cached_result.chart_image_path, source="chart")
                
            # 从raw_calculation中提取图表相关信息
            raw_calc = cached_result.raw_calculation
            
            # 紫薇相关
            if 'ziwei' in raw_calc:
                ziwei_data = raw_calc['ziwei']
                if 'zodiac' in ziwei_data:
                    self.add_knowledge("chart_info", "生肖", ziwei_data['zodiac'], source="ziwei")
                if 'sign' in ziwei_data:
                    self.add_knowledge("chart_info", "星座", ziwei_data['sign'], source="ziwei")
                    
        except Exception as e:
            logger.error(f"图表知识加载失败: {e}")
    
    def _load_confidence_knowledge(self, raw_calculation):
        \"\"\"加载置信度信息\"\"\"
        try:
            if 'ziwei_confidence' in raw_calculation:
                confidence = raw_calculation['ziwei_confidence']
                self.add_knowledge("system_info", "紫薇置信度", f"{confidence:.2f}", source="system")
                
            if 'bazi_confidence' in raw_calculation:
                confidence = raw_calculation['bazi_confidence']
                self.add_knowledge("system_info", "八字置信度", f"{confidence:.2f}", source="system")
                
            if 'analysis_type' in raw_calculation:
                analysis_type = raw_calculation['analysis_type']
                self.add_knowledge("system_info", "分析类型", analysis_type, source="system")
                
        except Exception as e:
            logger.error(f"置信度知识加载失败: {e}")
    
    def _load_temporal_knowledge(self, birth_info):
        \"\"\"加载时间相关知识\"\"\"
        try:
            from datetime import datetime
            
            # 计算年龄
            birth_year = int(birth_info.get('year', 0))
            current_year = datetime.now().year
            age = current_year - birth_year
            
            self.add_knowledge("temporal_info", "当前年龄", f"{age}岁", source="calculated")
            
            # 生肖计算
            zodiac_animals = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
            zodiac_index = (birth_year - 1900) % 12
            zodiac = zodiac_animals[zodiac_index]
            self.add_knowledge("temporal_info", "生肖", zodiac, source="calculated")
            
            # 时辰详细信息
            hour_mapping = {
                "子时": "23:00-01:00",
                "丑时": "01:00-03:00", 
                "寅时": "03:00-05:00",
                "卯时": "05:00-07:00",
                "辰时": "07:00-09:00",
                "巳时": "09:00-11:00",
                "午时": "11:00-13:00",
                "未时": "13:00-15:00",
                "申时": "15:00-17:00",
                "酉时": "17:00-19:00",
                "戌时": "19:00-21:00",
                "亥时": "21:00-23:00"
            }
            
            hour = birth_info.get('hour', '')
            if hour in hour_mapping:
                self.add_knowledge("temporal_info", "时辰时间", hour_mapping[hour], source="calculated")
                
        except Exception as e:
            logger.error(f"时间知识加载失败: {e}")
    
    def _enhance_analysis_knowledge(self, detailed_analysis):
        \"\"\"增强分析知识提取\"\"\"
        try:
            angle_analyses = detailed_analysis.get('angle_analyses', {})
            
            # 提取每个分析的关键词
            for angle_key, content in angle_analyses.items():
                if content and len(content) > 100:
                    # 提取关键信息
                    keywords = self._extract_keywords_from_analysis(content)
                    if keywords:
                        angle_name = self._get_angle_chinese_name(angle_key)
                        self.add_knowledge("analysis_keywords", f"{angle_name}_关键词", ", ".join(keywords), source="analysis")
                        
        except Exception as e:
            logger.error(f"分析知识增强失败: {e}")
    
    def _extract_keywords_from_analysis(self, content):
        \"\"\"从分析内容中提取关键词\"\"\"
        import re
        
        # 简单的关键词提取
        keywords = []
        
        # 星曜关键词
        star_pattern = r'(天[相府机同梁破贪杀]|武曲|廉贞|太[阳阴]|巨门|紫微|七杀|贪狼|破军)'
        stars = re.findall(star_pattern, content)
        keywords.extend(stars[:3])  # 最多3个星曜
        
        # 特征关键词
        trait_pattern = r'(稳重|聪明|勤奋|谨慎|积极|温和|坚强|灵活|务实|理性)'
        traits = re.findall(trait_pattern, content)
        keywords.extend(traits[:2])  # 最多2个特征
        
        return list(set(keywords))  # 去重
    
    def _get_angle_chinese_name(self, angle_key):
        \"\"\"获取角度的中文名称\"\"\"
        angle_names = {
            "personality_destiny": "命宫",
            "wealth_fortune": "财富",
            "marriage_love": "婚姻",
            "health_wellness": "健康",
            "career_achievement": "事业",
            "children_creativity": "子女",
            "interpersonal_relationship": "人际",
            "education_learning": "学业",
            "family_environment": "家庭",
            "travel_relocation": "迁移",
            "spiritual_blessing": "精神",
            "authority_parents": "权威"
        }
        return angle_names.get(angle_key, angle_key)
    """
    
    return additional_methods

def test_enhanced_knowledge_base():
    """测试增强后的知识库"""
    print("🧪 测试增强后的知识库")
    print("=" * 50)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        
        # 获取缓存结果
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        cache_dir = cache.cache_dir
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        if not cache_files:
            print("❌ 没有找到缓存文件")
            return False
            
        cache_file = cache_files[0]
        result_id = cache_file.stem
        cached_result = cache.get_result(result_id)
        
        # 创建知识库
        knowledge_base = ChatKnowledgeBase()
        
        # 使用修复后的加载方法
        knowledge_base.load_from_cache_result(cached_result)
        
        # 获取统计
        stats = knowledge_base.get_stats()
        print(f"📊 增强后的知识库统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
            
        # 显示各类别的知识项
        print(f"\n📋 各类别知识详情:")
        for category, category_name in knowledge_base.categories.items():
            items = knowledge_base.get_knowledge_by_category(category)
            if items:
                print(f"\n{category_name} ({len(items)}项):")
                for item in items:
                    print(f"  - {item.key}: {item.value[:50]}{'...' if len(item.value) > 50 else ''}")
                    
        # 测试格式化输出
        formatted_text = knowledge_base.format_for_llm(max_length=2000)
        print(f"\n📝 格式化输出长度: {len(formatted_text)} 字符")
        
        # 评估完整性
        completeness_score = 0
        if stats['basic_info_count'] >= 5:
            completeness_score += 1
        if stats['ziwei_palace_count'] >= 10:
            completeness_score += 1
        if stats['bazi_pillar_count'] >= 3:
            completeness_score += 1
        if stats['bazi_elements_count'] >= 1:
            completeness_score += 1
        if stats['analysis_result_count'] >= 1:
            completeness_score += 1
            
        print(f"\n🎯 知识库完整性评分: {completeness_score}/5")
        
        if completeness_score >= 4:
            print("🎉 知识库数据非常完整！")
            return True
        elif completeness_score >= 3:
            print("✅ 知识库数据基本完整")
            return True
        else:
            print("❌ 知识库数据不够完整")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 增强知识库测试")
    print("=" * 60)
    
    # 显示增强方案
    print("📋 知识库增强方案:")
    print("  1. ✅ 修复八字数据提取")
    print("  2. 🔄 添加图表信息提取")
    print("  3. 🔄 添加置信度信息")
    print("  4. 🔄 添加时间计算信息")
    print("  5. 🔄 添加分析关键词提取")
    
    # 测试当前状态
    success = test_enhanced_knowledge_base()
    
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 需要优化'}")
    
    if success:
        print("💡 建议: 知识库已经基本完整，可以进行下一步优化")
    else:
        print("💡 建议: 需要继续完善数据提取逻辑")
