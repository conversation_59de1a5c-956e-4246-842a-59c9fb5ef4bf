#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试排盘图改进
"""

def test_chart_format():
    """测试排盘图格式改进"""
    print("📊 第2步：排盘图显示改进验证")
    print("=" * 40)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        calc = RealZiweiCalculator()
        result = calc.calculate_chart(1985, 4, 23, 22, "女")
        
        if "error" in result:
            print(f"❌ 算法失败: {result['error']}")
            return False
        
        # 创建引擎实例
        engine = FortuneEngine()
        
        # 测试排盘图生成
        chart_display = engine._generate_chart_display({"data": result, "success": True})
        
        print("✅ 排盘图生成成功")
        print("\n📊 改进后的排盘图预览:")
        print("-" * 50)
        
        # 显示前几行作为预览
        lines = chart_display.split('\n')
        for i, line in enumerate(lines[:15]):  # 只显示前15行
            print(line)
        
        if len(lines) > 15:
            print("... (更多内容)")
        
        print("-" * 50)
        
        # 检查改进要点
        improvements_check = {
            "双线框架": "╔═══" in chart_display and "║" in chart_display,
            "详细宫位": "┌─────────────┬" in chart_display,
            "星曜显示": any(star in chart_display for star in ["紫薇", "天机", "太阳", "武曲", "天同", "廉贞"]),
            "身宫标识": "[身]" in chart_display or "身宫" in chart_display,
            "十二宫详情": "【十二宫详细信息】" in chart_display
        }
        
        print("\n✅ 改进要点检查:")
        for feature, status in improvements_check.items():
            print(f"  {feature}: {'✅' if status else '❌'}")
        
        return all(improvements_check.values())
        
    except Exception as e:
        print(f"❌ 排盘图测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_comparison():
    """对比改进前后的排盘图"""
    print("\n📋 排盘图改进对比")
    print("=" * 40)
    
    print("❌ 改进前的问题:")
    print("  - 排盘图过于简约")
    print("  - 缺少详细的星曜信息")
    print("  - 没有完整的十二宫显示")
    print("  - 视觉效果不够专业")
    
    print("\n✅ 改进后的特点:")
    print("  - 使用双线框架，更加美观")
    print("  - 完整显示十二宫位置")
    print("  - 每个宫位显示主星和辅星")
    print("  - 明确标识身宫位置")
    print("  - 添加详细的宫位信息列表")
    print("  - 符合传统紫薇斗数排盘格式")
    
    return True

def test_chart_features():
    """测试排盘图功能特性"""
    print("\n🎯 排盘图功能特性")
    print("=" * 30)
    
    features = [
        "✅ 传统十二宫布局 (子丑寅卯...)",
        "✅ 双线边框设计 (╔═══╗)",
        "✅ 宫位名称显示 (命宫、财帛宫等)",
        "✅ 主星配置显示 (紫薇、天机等)",
        "✅ 辅星信息显示 (左辅、右弼等)",
        "✅ 身宫标识显示 ([身]标记)",
        "✅ 出生信息显示 (阳历、农历、八字)",
        "✅ 详细信息列表 (十二宫完整信息)"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n🎨 视觉改进:")
    print("  - 使用Unicode字符绘制精美边框")
    print("  - 合理的空间布局和对齐")
    print("  - 清晰的信息层次结构")
    print("  - 专业的传统命理风格")
    
    return True

def test_integration_effect():
    """测试整合效果"""
    print("\n🔗 整合效果预期")
    print("=" * 30)
    
    print("📊 完整输出结构:")
    print("  1. 📊 【命盘排盘图】")
    print("     - 精美的传统排盘图")
    print("     - 完整的十二宫信息")
    print("     - 清晰的星曜配置")
    
    print("  2. 📋 【核心要点 - 紧凑版】")
    print("     - 基于排盘图的精华总结")
    print("     - 重点突出关键信息")
    
    print("  3. 📚 【深度解读 - 详细版】")
    print("     - 深入的4角度分析")
    print("     - 客观的优劣势分析")
    print("     - 明确的指导建议")
    
    print("\n🎯 用户体验提升:")
    print("  - 有直观的排盘图参照")
    print("  - 能看到真实的星曜配置")
    print("  - 理解分析的理论依据")
    print("  - 获得专业的命理体验")
    
    return True

def main():
    """主测试函数"""
    print("📊 第2步：排盘图显示改进")
    print("=" * 50)
    
    # 测试1: 排盘图格式
    format_success = test_chart_format()
    
    # 测试2: 改进对比
    comparison_success = test_chart_comparison()
    
    # 测试3: 功能特性
    features_success = test_chart_features()
    
    # 测试4: 整合效果
    integration_success = test_integration_effect()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎉 第2步改进总结:")
    print(f"  排盘图格式: {'✅' if format_success else '❌'}")
    print(f"  改进对比: {'✅' if comparison_success else '❌'}")
    print(f"  功能特性: {'✅' if features_success else '❌'}")
    print(f"  整合效果: {'✅' if integration_success else '❌'}")
    
    if all([format_success, comparison_success, features_success, integration_success]):
        print("\n🎊 第2步改进完成！")
        print("\n📝 改进内容:")
        print("  1. ✅ 使用双线框架，视觉更美观")
        print("  2. ✅ 完整显示十二宫位和星曜")
        print("  3. ✅ 添加详细的宫位信息列表")
        print("  4. ✅ 符合传统紫薇斗数格式")
        
        print("\n🚀 现在可以进行第3步：完整流程测试")
    else:
        print("\n⚠️ 第2步改进需要进一步完善")

if __name__ == "__main__":
    main()
