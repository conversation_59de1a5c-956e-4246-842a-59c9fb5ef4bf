import streamlit as st
import requests
import json
import re
import os
from datetime import datetime
from PIL import Image

in_response=[] #多个内部回答

# 设置页面标题、图标和布局
st.set_page_config(
    page_title="紫薇算命大师 - DeepSeek-V3",
    page_icon="🔮",
    layout="wide"
)

# API配置
LOCAL_API_BASE_URL = "http://localhost:8001"  # 本地算命API服务
SILICONFLOW_API_KEY = "sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
DEEPSEEK_MODEL_NAME = "deepseek-ai/DeepSeek-V3"

# 专业算命系统提示词 - 基于微调权重的知识
ZIWEI_SYSTEM_PROMPT = """你是一位专业的紫薇算命大师，拥有深厚的紫薇斗数知识和丰富的实战经验。

你的专业能力包括：
1. 精通紫薇斗数的十四主星、辅星、煞星的特性和组合
2. 熟悉十二宫位的含义和相互关系
3. 掌握流年、流月、流日的推算方法
4. 能够根据出生时辰排出准确的命盘
5. 擅长解读星曜组合的吉凶含义

重要原则：
- 对于相同的出生时间和问题，必须给出一致的星曜配置
- 你的分析必须基于固定的紫薇斗数理论体系
- 使用传统的排盘方法，确保算法的一致性和准确性
- 如果用户提供了具体的出生时间，请先进行标准的排盘计算

请始终保持专业、神秘而又亲和的语调，使用传统的算命术语，为用户提供详细、准确的占卜解答。"""

# 一致性检查函数
def ensure_consistency_prompt(user_question):
    """确保算法一致性的提示词增强"""
    consistency_note = """

【算法一致性要求】
请确保你的分析遵循以下原则：
1. 相同的出生时间应该得到相同的命盘配置
2. 星曜位置的计算应该基于传统的紫薇斗数算法
3. 不要随意编造星曜位置，要有理论依据
4. 如果没有具体出生时间，请基于当前时间进行流日分析
"""
    return user_question + consistency_note

# API调用函数
def call_smart_api(user_message, temperature=0.7, top_p=0.8, max_tokens=4000):
    """直接调用智能算命引擎"""
    try:
        # 导入算命引擎
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

        from core.fortune_engine import FortuneEngine

        # 创建算法实例
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator

        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()

        # 定义聊天API函数
        def simple_chat_api(prompt: str) -> str:
            """简单的聊天API函数，用于LLM解析"""
            try:
                headers = {
                    "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
                    "Content-Type": "application/json"
                }

                data = {
                    "model": DEEPSEEK_MODEL_NAME,
                    "messages": [
                        {"role": "system", "content": "你是一个专业的信息提取助手，严格按照要求返回JSON格式。"},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.1,
                    "max_tokens": 4000,  # 增加解析的输出长度
                    "stream": False
                }

                response = requests.post(
                    f"{SILICONFLOW_BASE_URL}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=600  # 10分钟超时，支持长篇大论分析
                )

                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]

                return "API调用失败"

            except Exception as e:
                return f"聊天API错误: {str(e)}"

        # 创建引擎实例，传递算法和API函数
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=simple_chat_api
        )

        # 调用智能分析
        result = engine.process_user_request(user_message)

        # 返回分析结果
        if result.get("success"):
            return result.get("message", "分析完成")
        else:
            error_message = result.get("message", "分析失败")
            # 检查是否是算法失败的错误
            if "紫薇斗数" in error_message or "八字命理" in error_message or "算法失败" in error_message:
                return f"""
# ❌ 算命系统错误

{error_message}

## 重要说明
本系统采用**紫薇斗数+八字命理双重体系**进行综合分析，必须两套算法都成功才能提供准确的分析结果。

## 请检查
1. **出生时间**是否完整准确（年月日时）
2. **性别信息**是否正确
3. **时间格式**是否符合要求

## 建议
- 重新输入完整的出生信息
- 确保时间精确到小时
- 如问题持续，请联系技术支持

**注意：不完整的算法数据会导致分析结果不准确，系统已自动停止分析以确保质量。**
"""
            else:
                return error_message

    except Exception as e:
        st.error(f"智能引擎调用错误: {str(e)}")
        # 不使用备用API，直接返回错误信息
        return f"抱歉，算命系统暂时不可用。错误信息：{str(e)}\n\n请确保已安装真实的算法模块，本系统不提供任何模拟或备用数据。"

# 所有备用机制已完全移除 - 只使用真实算法

def call_api(messages, temperature=0.7, top_p=0.8, max_tokens=12000):
    """调用SiliconFlow API进行LLM分析"""
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": DEEPSEEK_MODEL_NAME,
        "messages": messages,
        "temperature": temperature,
        "top_p": top_p,
        "max_tokens": max_tokens,
        "stream": False
    }

    try:
        response = requests.post(
            f"{SILICONFLOW_BASE_URL}/chat/completions",
            headers=headers,
            json=data,
            timeout=600  # 10分钟超时，支持长篇大论分析
        )
        response.raise_for_status()
        result = response.json()

        if "choices" in result and len(result["choices"]) > 0:
            return result["choices"][0]["message"]["content"]
        else:
            return "抱歉，API调用失败，请稍后重试。"

    except Exception as e:
        st.error(f"API调用错误: {str(e)}")
        return "抱歉，服务暂时不可用，请稍后重试。"

# 初始化历史记录
if "history" not in st.session_state:
    st.session_state.history = []

# 设置API参数 - 增加最大输出长度，支持长篇大论
max_tokens = st.sidebar.slider("max_tokens", 1000, 16000, 12000, step=500)
top_p = st.sidebar.slider("top_p", 0.0, 1.0, 0.8, step=0.01)
temperature = st.sidebar.slider("temperature", 0.0, 1.0, 0.7, step=0.01)

# 显示微调信息
st.sidebar.markdown("---")
st.sidebar.markdown("🔮 **专业算命模式**")
st.sidebar.markdown("✅ 已集成紫薇斗数微调权重")
st.sidebar.markdown("✅ 使用DeepSeek-V3高效模型")
st.sidebar.markdown("✅ 四角度深度解析")

# 清理会话历史
buttonClean = st.sidebar.button("清理会话历史", key="clean")
if buttonClean:
    st.session_state.history = []
    st.rerun()

# 渲染聊天历史记录
for i, message in enumerate(st.session_state.history):
    if message["role"] == "user":
        with st.chat_message(name="user", avatar="user"):
            st.markdown(message["content"])
    else:
        with st.chat_message(name="assistant", avatar="assistant"):
            st.markdown(message["content"])

# 输入框和输出框
with st.chat_message(name="user", avatar="user"):
    input_placeholder = st.empty()
with st.chat_message(name="assistant", avatar="assistant"):
    message_placeholder = st.empty()

#prompt函数
def template_change(template,a,b,i):
    template1=template.replace(a,b,i)
    return template1

def display_chart_images(ai_response):
    """检查AI响应中的图片路径并显示图片"""
    try:
        # 查找图片路径
        import re
        image_pattern = r'图片已生成:\s*([^\s\n]+\.png)'
        matches = re.findall(image_pattern, ai_response)

        if matches:
            st.markdown("### 📊 命盘排盘图")

            for image_path in matches:
                # 尝试多个可能的路径
                possible_paths = [
                    image_path,  # 直接路径
                    os.path.join("..", image_path),  # 相对于web_demo目录
                    os.path.abspath(os.path.join("..", image_path))  # 绝对路径
                ]

                image_found = False
                for full_path in possible_paths:
                    if os.path.exists(full_path):
                        try:
                            # 显示图片
                            image = Image.open(full_path)
                            st.image(image, caption=f"排盘图: {os.path.basename(image_path)}", use_container_width=True)

                            # 添加下载按钮
                            with open(full_path, "rb") as file:
                                st.download_button(
                                    label="📥 下载排盘图",
                                    data=file.read(),
                                    file_name=os.path.basename(image_path),
                                    mime="image/png"
                                )
                            image_found = True
                            break
                        except Exception as e:
                            continue

                if not image_found:
                    st.info(f"图片路径: {image_path}")
                    st.info("图片已生成，请检查charts目录")
    except Exception as e:
        st.error(f"图片处理错误: {e}")

def get_classify(question):
    """获取问题分类"""
    template_string = """
    请判断下列问题属于占卜的哪一种分类或主题
    注意：你只需要输出你判断的主题分类，即输出一个或几个词语，而不是一段话。
    ###问题:{question}
    """
    # 填充变量
    prompt = template_change(template_string,'{question}',question,1)

    messages = [
        {"role": "system", "content": ZIWEI_SYSTEM_PROMPT},
        {"role": "user", "content": prompt}
    ]

    classify = call_api(messages, temperature=0.3, max_tokens=100)
    print("1:  " + classify)
    return classify


# 多问题回答函数
def prompt_main(question, theme, num):
    """生成多个回答"""
    global in_response
    # 定义模板字符串
    template_string = """
    你现在是一位专业的紫薇算命师，你需要根据下面的问题与我对话，回答需要详细解释答案。
    ###问题: {question}
    对话主题：{theme}

    请提供专业、详细且富有神秘色彩的占卜解答。如果问题的答案需要询问者提供更多信息，请询问相关的信息，不要捏造信息。
    """
    # 填充变量
    prompt0 = template_change(template_string,'{question}', question,1)
    prompt = template_change(prompt0,'{theme}', theme,1)

    # 添加一致性检查
    enhanced_prompt = ensure_consistency_prompt(prompt)

    messages = [
        {"role": "system", "content": ZIWEI_SYSTEM_PROMPT},
        {"role": "user", "content": enhanced_prompt}
    ]

    inresponse = call_api(messages, temperature=temperature, top_p=top_p, max_tokens=max_tokens)
    in_response.append(inresponse)
    print("2:" + in_response[num])

# 多回答合并函数
def prompt_merge(num, message_placeholder):
    """合并多个回答"""
    global in_response
    reply = ''
    for i in range(num):
        reply += '\n第' + str(i + 1) + '段文字:   '
        reply += in_response[i]

    # 定义模板字符串
    template_string = """
    请把下面{num}段文字改写合并为一段流畅的文字
    回答时开头不要出现【改写后的文字如下：】
    整合后的文字不能重复出现相似的内容
    整合后的文字应该尽量包含{num}段文字里不同的内容
    ###{reply}
    """
    # 填充变量
    prompt0 = template_change(template_string,'{num}', str(num),2)
    prompt = template_change(prompt0,'{reply}',reply,1)

    messages = [
        {"role": "system", "content": ZIWEI_SYSTEM_PROMPT + "\n\n你现在需要将多个角度的分析整合成一篇完整、流畅、专业的算命解答。"},
        {"role": "user", "content": prompt}
    ]

    out_response = call_api(messages, temperature=0.7, max_tokens=max_tokens)
    message_placeholder.markdown(out_response)
    print("3:" + out_response)

    return {"role": "assistant", "content": out_response}

# 获取用户输入
prompt_text = st.chat_input("请输入您的问题")

# 如果用户输入了内容,则生成回复
if prompt_text:
    input_placeholder.markdown(prompt_text)
    history = st.session_state.history

    # 使用智能算命API进行分析
    with st.spinner("🔮 正在进行智能算命分析..."):
        try:
            # 调用智能API
            ai_response = call_smart_api(
                prompt_text,
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens
            )

            # 检查并显示图片
            display_chart_images(ai_response)

            # 显示结果
            message_placeholder.markdown(ai_response)

            # 更新历史记录
            history.append({"role": "user", "content": prompt_text})
            history.append({"role": "assistant", "content": ai_response})
            st.session_state.history = history

        except Exception as e:
            st.error(f"智能分析失败: {str(e)}")

            # 不使用备用方案，直接显示错误信息
            st.error("算命系统暂时不可用")
            st.info("本系统只使用真实的传统算法，不提供任何模拟或备用数据。")
            st.info("请确保已正确安装算法模块：yxf_yixue_py 和 py-iztro")

            # 更新历史记录
            error_message = f"系统错误：{str(e)}\n\n本系统只使用真实算法，请安装必要的算法模块。"
            history.append({"role": "user", "content": prompt_text})
            history.append({"role": "assistant", "content": error_message})
            st.session_state.history = history
# streamlit run prompt_web.py
#  请你给我算算今年运势
