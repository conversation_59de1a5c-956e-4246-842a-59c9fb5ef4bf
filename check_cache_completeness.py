#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查缓存数据完整性
"""

import sys
sys.path.append('.')

import os
import json
from pathlib import Path
from core.agents.fortune_calculator_agent import FortuneCalculatorAgent

def check_cache_completeness():
    """检查缓存数据的完整性"""
    print("🔍 检查缓存数据完整性")
    print("=" * 50)
    
    try:
        # 初始化Agent
        calculator_agent = FortuneCalculatorAgent()
        cache = calculator_agent.cache
        
        # 获取缓存统计
        cache_stats = cache.get_cache_stats()
        print(f"📊 缓存统计:")
        print(f"  总结果数: {cache_stats['total_results']}")
        print(f"  用户数: {cache_stats['users_count']}")
        print(f"  会话数: {cache_stats['sessions_count']}")
        print(f"  算命类型: {cache_stats['calculation_types']}")
        
        # 检查缓存目录
        cache_dir = Path("data/calculation_cache")
        if not cache_dir.exists():
            print("❌ 缓存目录不存在")
            return False
            
        # 获取所有缓存文件
        cache_files = list(cache_dir.glob("*.json"))
        cache_files = [f for f in cache_files if f.name != "index.json"]
        
        print(f"\n📁 缓存文件检查:")
        print(f"  缓存文件数: {len(cache_files)}")
        
        if not cache_files:
            print("⚠️  没有找到缓存文件")
            return True
            
        # 检查最近的几个缓存文件
        recent_files = sorted(cache_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
        
        for i, cache_file in enumerate(recent_files):
            print(f"\n📄 检查文件 {i+1}: {cache_file.name}")
            
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查基本字段
                required_fields = ['result_id', 'birth_info', 'raw_calculation', 'detailed_analysis']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    print(f"  ❌ 缺少字段: {missing_fields}")
                    continue
                
                print(f"  ✅ 基本字段完整")
                
                # 检查生辰信息
                birth_info = data.get('birth_info', {})
                birth_fields = ['year', 'month', 'day', 'hour', 'gender']
                birth_complete = all(field in birth_info for field in birth_fields)
                print(f"  生辰信息: {'✅ 完整' if birth_complete else '❌ 不完整'}")
                
                # 检查排盘数据
                raw_calculation = data.get('raw_calculation', {})
                print(f"  排盘数据字段: {list(raw_calculation.keys())}")
                
                # 检查是否有紫薇和八字数据
                has_ziwei = 'ziwei_analysis' in raw_calculation
                has_bazi = 'bazi_analysis' in raw_calculation
                print(f"  紫薇数据: {'✅ 存在' if has_ziwei else '❌ 缺失'}")
                print(f"  八字数据: {'✅ 存在' if has_bazi else '❌ 缺失'}")
                
                # 检查详细分析
                detailed_analysis = data.get('detailed_analysis', {})
                angle_analyses = detailed_analysis.get('angle_analyses', {})
                print(f"  已生成分析数: {len(angle_analyses)}/12")
                
                # 检查分析内容长度
                if angle_analyses:
                    print(f"  分析内容检查:")
                    for angle_key, content in angle_analyses.items():
                        content_length = len(content) if isinstance(content, str) else 0
                        status = "✅ 正常" if content_length > 100 else "⚠️  过短" if content_length > 0 else "❌ 空"
                        print(f"    {angle_key}: {content_length}字 {status}")
                
                # 检查图片路径
                chart_image_path = data.get('chart_image_path')
                if chart_image_path:
                    image_exists = os.path.exists(chart_image_path)
                    print(f"  排盘图片: {'✅ 存在' if image_exists else '❌ 文件不存在'} ({chart_image_path})")
                else:
                    print(f"  排盘图片: ❌ 路径未设置")
                
            except Exception as e:
                print(f"  ❌ 文件读取失败: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_specific_result(result_id):
    """检查特定结果的完整性"""
    print(f"\n🔍 检查特定结果: {result_id}")
    print("-" * 30)
    
    try:
        calculator_agent = FortuneCalculatorAgent()
        cached_result = calculator_agent.cache.get_result(result_id)
        
        if not cached_result:
            print("❌ 结果不存在")
            return False
            
        print(f"✅ 结果存在")
        print(f"  创建时间: {cached_result.created_at}")
        print(f"  更新时间: {cached_result.updated_at}")
        print(f"  计算类型: {cached_result.calculation_type}")
        
        # 检查排盘数据
        raw_calc = cached_result.raw_calculation
        print(f"  排盘数据字段: {list(raw_calc.keys())}")
        
        # 检查分析数据
        detailed = cached_result.detailed_analysis
        angle_analyses = detailed.get('angle_analyses', {})
        print(f"  已生成分析: {len(angle_analyses)}/12")
        
        for angle_key, content in angle_analyses.items():
            content_length = len(content) if isinstance(content, str) else 0
            print(f"    {angle_key}: {content_length}字")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    success = check_cache_completeness()
    
    # 如果有命令行参数，检查特定结果
    if len(sys.argv) > 1:
        result_id = sys.argv[1]
        check_specific_result(result_id)
