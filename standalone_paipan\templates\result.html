<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排盘结果 - 独立排盘系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .birth-info {
            font-size: 1.3em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e5e9;
        }

        .nav-tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 1.1em;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .nav-tab:hover {
            background: rgba(102, 126, 234, 0.05);
            color: #667eea;
        }

        .content {
            padding: 30px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid #667eea;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-card .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e1e5e9;
        }

        .info-card .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-card .label {
            font-weight: 600;
            color: #666;
        }

        .info-card .value {
            color: #333;
            font-weight: 500;
        }

        .palace-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .palace-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .palace-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .palace-card.body-palace {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.05);
        }

        .palace-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .palace-name {
            font-weight: 700;
            font-size: 1.1em;
            color: #333;
        }

        .palace-position {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.9em;
        }

        .body-palace-badge {
            background: #ff6b6b;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-left: 5px;
        }

        .stars-section {
            margin-bottom: 8px;
        }

        .stars-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
        }

        .stars-list {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .star {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.85em;
            color: #333;
        }

        .star.major {
            background: #667eea;
            color: white;
        }

        .star.minor {
            background: #95a5a6;
            color: white;
        }

        .star.adjective {
            background: #e74c3c;
            color: white;
        }

        .formatted-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 25px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.95em;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-top: 20px;
        }

        .actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin: 20px;
        }

        /* 命盘图表样式 */
        .chart-container {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            margin: 20px 0;
        }

        .ziwei-chart {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .palace-cell {
            width: 200px;
            height: 200px;
            border: 2px solid #667eea;
            background: linear-gradient(135deg, #E6F3FF 0%, #F0E6FF 100%);
            padding: 8px;
            vertical-align: top;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .palace-cell:hover {
            background: linear-gradient(135deg, #D6E9FF 0%, #E0D6FF 100%);
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 10;
        }

        .palace-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            border-bottom: 1px solid #667eea;
            padding-bottom: 4px;
        }

        .palace-name {
            font-size: 12px;
            font-weight: bold;
            color: #2C1810;
        }

        .palace-number {
            background: #667eea;
            color: white;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            font-weight: bold;
        }

        .palace-position {
            font-size: 9px;
            color: #5D4E75;
            margin-bottom: 4px;
        }

        .palace-attrs {
            font-size: 8px;
            color: #666;
            margin-bottom: 6px;
            font-style: italic;
        }

        .stars-section {
            margin-bottom: 6px;
        }

        .section-title {
            font-size: 8px;
            color: #666;
            margin-bottom: 2px;
            font-weight: bold;
        }

        .major-star {
            display: inline-block;
            color: white;
            padding: 1px 3px;
            margin: 1px;
            border-radius: 2px;
            font-size: 8px;
            font-weight: bold;
            cursor: help;
        }

        .major-star.first-class {
            background: #C41E3A;
        }

        .major-star.second-class {
            background: #DC6B19;
        }

        .major-star.other-class {
            background: #667eea;
        }

        .minor-star {
            display: inline-block;
            background: #228B22;
            color: white;
            padding: 1px 2px;
            margin: 1px;
            border-radius: 2px;
            font-size: 7px;
            cursor: help;
        }

        .adjective-star {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 1px 2px;
            margin: 1px;
            border-radius: 2px;
            font-size: 7px;
            cursor: help;
        }

        .center-cell {
            background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
            border: 3px solid #667eea;
            text-align: center;
            padding: 15px;
            vertical-align: middle;
        }

        .center-title {
            font-size: 14px;
            font-weight: bold;
            color: #2C1810;
            margin-bottom: 10px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .bazi-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }

        .bazi-table th {
            background: #667eea;
            color: white;
            padding: 3px;
            font-size: 9px;
            border: 1px solid #666;
        }

        .bazi-table td {
            border: 1px solid #666;
            padding: 3px;
            background: white;
            font-size: 10px;
            text-align: center;
        }

        .tiangan {
            font-weight: bold;
            color: #C41E3A;
        }

        .dizhi {
            font-weight: bold;
            color: #4169E1;
        }

        .body-palace-mark {
            position: absolute;
            top: 2px;
            right: 2px;
            background: #ff6b6b;
            color: white;
            padding: 1px 3px;
            border-radius: 3px;
            font-size: 7px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .content {
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .palace-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 排盘结果</h1>
            <div class="birth-info" id="birthInfo">加载中...</div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('chart')">🔮 命盘图表</button>
            <button class="nav-tab" onclick="showTab('overview')">📊 概览</button>
            <button class="nav-tab" onclick="showTab('ziwei')">🌟 紫薇斗数</button>
            <button class="nav-tab" onclick="showTab('bazi')">🎯 八字命理</button>
            <button class="nav-tab" onclick="showTab('formatted')">📄 完整报告</button>
        </div>

        <div class="content">
            <!-- 命盘图表标签页 -->
            <div id="chart" class="tab-content active">
                <div class="chart-container">
                    <table class="ziwei-chart" id="ziweiChart">
                        <!-- 动态生成命盘表格 -->
                    </table>
                </div>
            </div>

            <!-- 概览标签页 -->
            <div id="overview" class="tab-content">
                <div class="info-grid" id="overviewGrid">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 紫薇斗数标签页 -->
            <div id="ziwei" class="tab-content">
                <div class="info-grid" id="ziweiInfo">
                    <!-- 动态生成 -->
                </div>
                <div class="palace-grid" id="palaceGrid">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 八字命理标签页 -->
            <div id="bazi" class="tab-content">
                <div class="info-grid" id="baziInfo">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 完整报告标签页 -->
            <div id="formatted" class="tab-content">
                <div class="formatted-output" id="formattedOutput">
                    加载中...
                </div>
            </div>

            <div class="actions">
                <a href="/" class="btn btn-primary">🔄 重新排盘</a>
                <button class="btn btn-success" onclick="downloadResult()">💾 下载结果</button>
                <button class="btn btn-secondary" onclick="copyResult()">📋 复制结果</button>
            </div>
        </div>
    </div>

    <div class="loading" id="loading" style="display: none;">
        <h2>加载中...</h2>
        <p>正在处理排盘结果</p>
    </div>

    <div class="error" id="error" style="display: none;">
        <h2>加载失败</h2>
        <p id="errorMessage">未找到排盘结果</p>
        <a href="/" class="btn btn-primary" style="margin-top: 15px;">返回首页</a>
    </div>

    <script>
        let resultData = null;

        // 页面加载时获取结果
        window.addEventListener('load', function() {
            // 检查是否有record_id参数
            const pathParts = window.location.pathname.split('/');
            const recordId = pathParts[pathParts.length - 1];

            if (recordId && !isNaN(recordId)) {
                // 通过API从数据库获取数据
                console.log('从数据库加载记录ID:', recordId);
                loadFromDatabase(recordId);
            } else {
                // 兼容旧版本，从sessionStorage获取
                const storedResult = sessionStorage.getItem('paipanResult');
                console.log('从sessionStorage获取的原始数据:', storedResult);

                if (storedResult) {
                    try {
                        resultData = JSON.parse(storedResult);
                        console.log('解析后的数据:', resultData);
                        displayResult(resultData);
                    } catch (e) {
                        console.error('数据解析失败:', e);
                        showError('数据解析失败');
                    }
                } else {
                    showError('未找到排盘结果，请重新计算');
                }
            }
        });

        // 从数据库加载数据
        function loadFromDatabase(recordId) {
            fetch(`/api/record/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('从数据库获取的数据:', data.data);
                        resultData = {
                            success: true,
                            data: data.data
                        };
                        displayResult(resultData);
                    } else {
                        showError('获取排盘记录失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('数据库加载失败:', error);
                    showError('网络错误: ' + error.message);
                });
        }

        // 显示错误
        function showError(message) {
            document.getElementById('error').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
            document.querySelector('.container').style.display = 'none';
        }

        // 显示结果
        function displayResult(data) {
            console.log('显示结果数据:', data); // 调试日志

            // 修正数据访问路径 - Web应用返回的结构是 {success: true, data: {...}}
            const result = data.data || data;
            console.log('提取的result数据:', result); // 调试日志

            const birthInfo = result.birth_info || result.input_info || {};
            console.log('出生信息:', birthInfo); // 调试日志

            // 更新标题信息
            const birthInfoText = `${birthInfo.datetime || birthInfo.datetime_str || ''} ${birthInfo.gender || ''}`;
            document.getElementById('birthInfo').textContent = birthInfoText;

            // 生成命盘图表
            generateChart(result);

            // 生成概览
            generateOverview(result);

            // 生成紫薇斗数
            generateZiwei(result);

            // 生成八字命理
            generateBazi(result);

            // 显示完整报告
            document.getElementById('formattedOutput').textContent = data.formatted_output || '暂无格式化输出';
        }

        // 生成命盘图表
        function generateChart(result) {
            console.log('生成命盘图表，数据:', result); // 调试日志

            const ziwei = result.ziwei_analysis || {};
            const bazi = result.bazi_analysis || {};
            const palaces = ziwei.palaces || {};

            console.log('紫薇数据:', ziwei); // 调试日志
            console.log('宫位数据:', palaces); // 调试日志

            // 12宫布局 (按照传统紫薇斗数排列)
            const palaceLayout = [
                ["子女宫", "财帛宫", "疾厄宫", "迁移宫"],
                ["夫妻宫", "center", "center", "奴仆宫"],
                ["兄弟宫", "center", "center", "官禄宫"],
                ["命宫", "父母宫", "福德宫", "田宅宫"]
            ];

            const chart = document.getElementById('ziweiChart');
            let chartHtml = '';

            // 生成表格行
            for (let rowIdx = 0; rowIdx < palaceLayout.length; rowIdx++) {
                chartHtml += '<tr>';
                for (let colIdx = 0; colIdx < palaceLayout[rowIdx].length; colIdx++) {
                    const cell = palaceLayout[rowIdx][colIdx];

                    if (cell === "center") {
                        if (rowIdx === 1 && colIdx === 1) {
                            // 中央区域 - 跨2x2，显示八字信息
                            const centerContent = createCenterContent(bazi);
                            chartHtml += `<td class="center-cell" colspan="2" rowspan="2">${centerContent}</td>`;
                        }
                        // 其他center位置跳过（已被colspan/rowspan覆盖）
                    } else {
                        const palaceData = palaces[cell] || {};
                        const cellContent = createPalaceCellContent(cell, palaceData);
                        chartHtml += `<td class="palace-cell">${cellContent}</td>`;
                    }
                }
                chartHtml += '</tr>';
            }

            chart.innerHTML = chartHtml;
        }

        // 创建宫位单元格内容
        function createPalaceCellContent(palaceName, palaceData) {
            console.log(`生成宫位 ${palaceName}:`, palaceData); // 调试日志

            const position = palaceData.position || '';
            const majorStars = palaceData.major_stars || [];
            const minorStars = palaceData.minor_stars || [];
            const adjectiveStars = palaceData.adjective_stars || [];
            const isBodyPalace = palaceData.is_body_palace;

            // 宫位序号
            const palaceNumbers = {
                "命宫": "1", "兄弟宫": "2", "夫妻宫": "3", "子女宫": "4",
                "财帛宫": "5", "疾厄宫": "6", "迁移宫": "7", "奴仆宫": "8",
                "官禄宫": "9", "田宅宫": "10", "福德宫": "11", "父母宫": "12"
            };

            const palaceNumber = palaceNumbers[palaceName] || '';

            let content = `
                <div class="palace-header">
                    <div class="palace-name">${palaceName}</div>
                    <div class="palace-number">${palaceNumber}</div>
                </div>
            `;

            if (isBodyPalace) {
                content += '<div class="body-palace-mark">身宫</div>';
            }

            if (position) {
                content += `<div class="palace-position">地支: ${position}</div>`;
            }

            // 添加宫位属性信息
            const palaceAttrs = getPalaceAttributes(palaceName);
            if (palaceAttrs) {
                content += `<div class="palace-attrs">${palaceAttrs}</div>`;
            }

            // 主星
            if (majorStars.length > 0) {
                content += '<div class="stars-section"><div class="section-title">主星</div>';
                for (const star of majorStars.slice(0, 6)) {
                    const starLevel = getStarLevel(star);
                    content += `<span class="major-star ${starLevel}" title="${getStarDescription(star)}">${star}</span>`;
                }
                content += '</div>';
            }

            // 辅星
            if (minorStars.length > 0) {
                content += '<div class="stars-section"><div class="section-title">辅星</div>';
                for (const star of minorStars.slice(0, 8)) {
                    content += `<span class="minor-star" title="${getStarDescription(star)}">${star}</span>`;
                }
                content += '</div>';
            }

            // 煞星 (这是我们保留的更全面的信息)
            if (adjectiveStars.length > 0) {
                content += '<div class="stars-section"><div class="section-title">煞星</div>';
                for (const star of adjectiveStars.slice(0, 6)) {
                    content += `<span class="adjective-star" title="${getStarDescription(star)}">${star}</span>`;
                }
                content += '</div>';
            }

            return content;
        }

        // 创建中央区域内容（八字信息）
        function createCenterContent(baziData) {
            const baziInfo = baziData.bazi_info || {};
            const analysis = baziData.analysis || {};

            let content = '<div class="center-title">八字命理</div>';

            if (baziInfo.chinese_date) {
                content += `
                    <table class="bazi-table">
                        <tr>
                            <th>年柱</th>
                            <th>月柱</th>
                            <th>日柱</th>
                            <th>时柱</th>
                        </tr>
                        <tr>
                            <td>
                                <div class="tiangan">${baziInfo.year_pillar ? baziInfo.year_pillar[0] : ''}</div>
                                <div class="dizhi">${baziInfo.year_pillar ? baziInfo.year_pillar[1] : ''}</div>
                            </td>
                            <td>
                                <div class="tiangan">${baziInfo.month_pillar ? baziInfo.month_pillar[0] : ''}</div>
                                <div class="dizhi">${baziInfo.month_pillar ? baziInfo.month_pillar[1] : ''}</div>
                            </td>
                            <td>
                                <div class="tiangan">${baziInfo.day_pillar ? baziInfo.day_pillar[0] : ''}</div>
                                <div class="dizhi">${baziInfo.day_pillar ? baziInfo.day_pillar[1] : ''}</div>
                            </td>
                            <td>
                                <div class="tiangan">${baziInfo.hour_pillar ? baziInfo.hour_pillar[0] : ''}</div>
                                <div class="dizhi">${baziInfo.hour_pillar ? baziInfo.hour_pillar[1] : ''}</div>
                            </td>
                        </tr>
                    </table>
                `;
            }

            // 日主信息
            const dayMaster = analysis.day_master || {};
            if (dayMaster.gan) {
                content += `<div style="font-size: 11px; margin-top: 8px;">
                    <strong>日主: ${dayMaster.gan} (${dayMaster.element || ''})</strong><br>
                    身强弱: ${dayMaster.strength || ''}
                </div>`;
            }

            return content;
        }

        // 获取宫位属性
        function getPalaceAttributes(palaceName) {
            const attributes = {
                "命宫": "身体·性格·命运",
                "兄弟宫": "兄弟·朋友·同事",
                "夫妻宫": "配偶·恋人·合作",
                "子女宫": "子女·创作·投资",
                "财帛宫": "财运·理财·收入",
                "疾厄宫": "健康·疾病·意外",
                "迁移宫": "外出·变动·贵人",
                "奴仆宫": "下属·朋友·社交",
                "官禄宫": "事业·工作·名声",
                "田宅宫": "房产·家庭·祖业",
                "福德宫": "福气·享受·精神",
                "父母宫": "父母·长辈·上司"
            };
            return attributes[palaceName] || "";
        }

        // 获取星曜等级
        function getStarLevel(star) {
            // 甲级主星
            const firstClass = ["紫微", "天机", "太阳", "武曲", "天同", "廉贞", "天府",
                              "太阴", "贪狼", "巨门", "天相", "天梁", "七杀", "破军"];
            if (firstClass.includes(star)) {
                return "first-class";
            }

            // 乙级副星
            const secondClass = ["左辅", "右弼", "文昌", "文曲", "天魁", "天钺",
                               "禄存", "天马", "化禄", "化权", "化科", "化忌"];
            if (secondClass.includes(star)) {
                return "second-class";
            }

            return "other-class";
        }

        // 获取星曜描述
        function getStarDescription(star) {
            const descriptions = {
                "紫微": "帝王星，主贵气、领导力",
                "天机": "智慧星，主机智、变动",
                "太阳": "光明星，主权威、男性",
                "武曲": "财星，主财富、刚毅",
                "天同": "福星，主享受、温和",
                "廉贞": "囚星，主感情、艺术",
                "天府": "库星，主保守、稳重",
                "太阴": "母星，主柔和、女性",
                "贪狼": "欲望星，主多才、桃花",
                "巨门": "暗星，主口才、是非",
                "天相": "印星，主服务、中介",
                "天梁": "荫星，主长辈、医药",
                "七杀": "将星，主冲动、变动",
                "破军": "耗星，主破坏、创新",
                "左辅": "助星，主辅助、贵人",
                "右弼": "助星，主协调、人缘",
                "文昌": "科甲星，主文书、考试",
                "文曲": "文艺星，主才艺、口才",
                "天魁": "贵人星，主提拔、机会",
                "天钺": "贵人星，主暗助、福气"
            };
            return descriptions[star] || `${star}星`;
        }

        // 生成概览
        function generateOverview(result) {
            const birthInfo = result.birth_info || result.input_info || {};
            const grid = document.getElementById('overviewGrid');

            const basicInfo = `
                <div class="info-card">
                    <h3>📅 基本信息</h3>
                    <div class="info-item">
                        <span class="label">出生时间</span>
                        <span class="value">${birthInfo.datetime || birthInfo.datetime_str || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">农历</span>
                        <span class="value">${birthInfo.lunar || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">性别</span>
                        <span class="value">${birthInfo.gender || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">生肖</span>
                        <span class="value">${birthInfo.zodiac || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">星座</span>
                        <span class="value">${birthInfo.sign || ''}</span>
                    </div>
                </div>
            `;

            const statusInfo = `
                <div class="info-card">
                    <h3>✅ 计算状态</h3>
                    <div class="info-item">
                        <span class="label">计算状态</span>
                        <span class="value">${result.success ? '成功' : '失败'}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">计算时间</span>
                        <span class="value">${result.calculation_time || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">紫薇斗数</span>
                        <span class="value">${result.ziwei_analysis ? '✅ 成功' : '❌ 失败'}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">八字命理</span>
                        <span class="value">${result.bazi_analysis ? '✅ 成功' : '❌ 失败'}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">融合分析</span>
                        <span class="value">${result.fusion_analysis ? '✅ 成功' : '❌ 失败'}</span>
                    </div>
                </div>
            `;

            grid.innerHTML = basicInfo + statusInfo;
        }

        // 生成紫薇斗数
        function generateZiwei(result) {
            const ziwei = result.ziwei_analysis || {};
            const palaces = ziwei.palaces || {};

            // 基本信息
            const info = document.getElementById('ziweiInfo');
            const ziweiInfo = `
                <div class="info-card">
                    <h3>🌟 紫薇斗数信息</h3>
                    <div class="info-item">
                        <span class="label">命宫</span>
                        <span class="value">${getMainPalace(palaces)}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">身宫</span>
                        <span class="value">${getBodyPalace(palaces)}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">宫位数量</span>
                        <span class="value">${Object.keys(palaces).length}</span>
                    </div>
                </div>
            `;
            info.innerHTML = ziweiInfo;

            // 十二宫
            const grid = document.getElementById('palaceGrid');
            let palaceHtml = '';

            for (const [name, palace] of Object.entries(palaces)) {
                const isBodyPalace = palace.is_body_palace;
                const majorStars = palace.major_stars || [];
                const minorStars = palace.minor_stars || [];
                const adjectiveStars = palace.adjective_stars || [];

                palaceHtml += `
                    <div class="palace-card ${isBodyPalace ? 'body-palace' : ''}">
                        <div class="palace-header">
                            <span class="palace-name">
                                ${name}
                                ${isBodyPalace ? '<span class="body-palace-badge">身宫</span>' : ''}
                            </span>
                            <span class="palace-position">${palace.position || ''}</span>
                        </div>

                        ${majorStars.length > 0 ? `
                        <div class="stars-section">
                            <div class="stars-label">主星</div>
                            <div class="stars-list">
                                ${majorStars.map(star => `<span class="star major">${star}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${minorStars.length > 0 ? `
                        <div class="stars-section">
                            <div class="stars-label">辅星</div>
                            <div class="stars-list">
                                ${minorStars.map(star => `<span class="star minor">${star}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${adjectiveStars.length > 0 ? `
                        <div class="stars-section">
                            <div class="stars-label">煞星</div>
                            <div class="stars-list">
                                ${adjectiveStars.map(star => `<span class="star adjective">${star}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `;
            }

            grid.innerHTML = palaceHtml;
        }

        // 生成八字命理
        function generateBazi(result) {
            const bazi = result.bazi_analysis || {};
            const baziInfo = bazi.bazi_info || {};
            const analysis = bazi.analysis || {};

            const info = document.getElementById('baziInfo');

            const baziCard = `
                <div class="info-card">
                    <h3>🎯 八字信息</h3>
                    <div class="info-item">
                        <span class="label">八字</span>
                        <span class="value">${baziInfo.chinese_date || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">年柱</span>
                        <span class="value">${baziInfo.year_pillar || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">月柱</span>
                        <span class="value">${baziInfo.month_pillar || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">日柱</span>
                        <span class="value">${baziInfo.day_pillar || ''}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">时柱</span>
                        <span class="value">${baziInfo.hour_pillar || ''}</span>
                    </div>
                </div>
            `;

            const wuxing = analysis.wuxing || {};
            const wuxingCount = wuxing.count || {};
            const wuxingStrength = wuxing.strength || {};

            const wuxingCard = `
                <div class="info-card">
                    <h3>🔥 五行分析</h3>
                    ${Object.entries(wuxingCount).map(([element, count]) => `
                        <div class="info-item">
                            <span class="label">${element}</span>
                            <span class="value">${count} (${wuxingStrength[element] || ''})</span>
                        </div>
                    `).join('')}
                </div>
            `;

            const dayMaster = analysis.day_master || {};
            const dayMasterCard = `
                <div class="info-card">
                    <h3>☯️ 日主分析</h3>
                    <div class="info-item">
                        <span class="label">日主</span>
                        <span class="value">${dayMaster.gan || ''} (${dayMaster.element || ''})</span>
                    </div>
                    <div class="info-item">
                        <span class="label">身强弱</span>
                        <span class="value">${dayMaster.strength || ''}</span>
                    </div>
                </div>
            `;

            info.innerHTML = baziCard + wuxingCard + dayMasterCard;
        }

        // 获取命宫信息
        function getMainPalace(palaces) {
            const mingGong = palaces['命宫'];
            if (!mingGong) return '未知';

            const position = mingGong.position || '';
            const majorStars = mingGong.major_stars || [];
            return `${position}宫 (${majorStars.join(', ') || '无主星'})`;
        }

        // 获取身宫信息
        function getBodyPalace(palaces) {
            for (const [name, palace] of Object.entries(palaces)) {
                if (palace.is_body_palace) {
                    return `${name} (${palace.position || ''}宫)`;
                }
            }
            return '未知';
        }

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 下载结果
        function downloadResult() {
            if (!resultData) return;

            const content = resultData.formatted_output || JSON.stringify(resultData.data, null, 2);
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `排盘结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 复制结果
        function copyResult() {
            if (!resultData) return;

            const content = resultData.formatted_output || JSON.stringify(resultData.data, null, 2);
            navigator.clipboard.writeText(content).then(() => {
                alert('结果已复制到剪贴板');
            }).catch(() => {
                alert('复制失败，请手动复制');
            });
        }
    </script>
</body>
</html>
