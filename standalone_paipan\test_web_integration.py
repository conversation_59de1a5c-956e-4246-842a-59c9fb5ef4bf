#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Web集成的六爻功能
"""

import requests
import json
import time

def test_liuyao_web_integration():
    """测试六爻Web集成"""
    base_url = "http://localhost:5000"
    
    print("=== 测试六爻Web集成 ===")
    
    # 1. 测试创建六爻占卜
    print("\n1. 测试创建六爻占卜...")
    create_data = {
        "question": "测试六神和神煞显示是否正确",
        "method": "time"
    }
    
    try:
        response = requests.post(f"{base_url}/api/liuyao/create", 
                               json=create_data, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                liuyao_id = result.get('liuyao_id')
                print(f"✅ 六爻占卜创建成功: {liuyao_id}")
                
                # 2. 等待分析完成
                print("\n2. 等待分析完成...")
                max_wait = 60  # 最多等待60秒
                wait_time = 0
                
                while wait_time < max_wait:
                    time.sleep(5)
                    wait_time += 5
                    
                    # 检查状态
                    status_response = requests.get(f"{base_url}/api/liuyao/{liuyao_id}/status")
                    if status_response.status_code == 200:
                        status_result = status_response.json()
                        if status_result.get('success'):
                            status = status_result.get('status')
                            print(f"📊 当前状态: {status}")
                            
                            if status == 'completed':
                                print("✅ 分析完成!")
                                break
                            elif status == 'failed':
                                print("❌ 分析失败!")
                                return
                        else:
                            print(f"❌ 获取状态失败: {status_result.get('error')}")
                    else:
                        print(f"❌ 状态请求失败: {status_response.status_code}")
                
                # 3. 获取完整结果
                print("\n3. 获取分析结果...")
                result_response = requests.get(f"{base_url}/api/liuyao/{liuyao_id}")
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    if result_data.get('success'):
                        analysis_content = result_data.get('analysis_content', '')
                        print(f"✅ 获取结果成功，内容长度: {len(analysis_content)}字")
                        
                        # 检查是否包含我们修复的内容
                        print("\n4. 检查修复内容...")
                        checks = {
                            "乙丑日": "日期显示" in analysis_content and "乙丑" in analysis_content,
                            "六神": any(spirit in analysis_content for spirit in ["青龙", "朱雀", "勾陈", "腾蛇", "白虎", "玄武"]),
                            "旬空": "旬空" in analysis_content,
                            "神煞": any(sha in analysis_content for sha in ["驿马", "桃花", "天乙贵人"]),
                            "本卦变卦": "本卦" in analysis_content and "变卦" in analysis_content
                        }
                        
                        print("检查结果:")
                        for item, passed in checks.items():
                            status = "✅" if passed else "❌"
                            print(f"  {status} {item}: {'通过' if passed else '未通过'}")
                        
                        # 显示部分内容
                        print(f"\n5. 分析内容预览:")
                        print("=" * 50)
                        print(analysis_content[:500] + "..." if len(analysis_content) > 500 else analysis_content)
                        print("=" * 50)
                        
                    else:
                        print(f"❌ 获取结果失败: {result_data.get('error')}")
                else:
                    print(f"❌ 结果请求失败: {result_response.status_code}")
                    
            else:
                print(f"❌ 创建失败: {result.get('error')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_liuyao_web_integration()
