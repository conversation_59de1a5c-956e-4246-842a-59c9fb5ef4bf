#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话隔离测试 - 验证每个会话是否独立
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_session_isolation():
    print('🔒 会话隔离测试...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        print('\n🎭 测试场景：多个独立会话')
        
        # 会话1：用户A
        session_a = 'session_user_a'
        print(f'\n👤 会话A ({session_a})：用户A的算命')
        
        result_a1 = await coordinator.handle_user_message(
            session_a, 
            '我是1988年6月1日午时出生的男命，想算命'
        )
        print(f'A1: {result_a1.get("response", "")[:100]}...')
        
        # 会话2：用户B
        session_b = 'session_user_b'
        print(f'\n👤 会话B ({session_b})：用户B的算命')
        
        result_b1 = await coordinator.handle_user_message(
            session_b, 
            '我是1990年12月25日子时出生的女命，想算紫薇'
        )
        print(f'B1: {result_b1.get("response", "")[:100]}...')
        
        # 等待后台分析启动
        print('\n⏳ 等待5秒让后台分析启动...')
        time.sleep(5)
        
        # 检查会话状态隔离
        print('\n📊 检查会话状态隔离:')
        
        state_a = master_agent.get_session_state(session_a)
        state_b = master_agent.get_session_state(session_b)
        
        print(f'会话A状态:')
        print(f'  阶段: {state_a.get("stage")}')
        print(f'  生辰: {state_a.get("birth_info")}')
        print(f'  算命类型: {state_a.get("calculation_type")}')
        print(f'  result_id: {state_a.get("result_id", "None")[:8] if state_a.get("result_id") else "None"}...')
        print(f'  聊天记录数: {len(state_a.get("conversation_history", []))}')
        
        print(f'会话B状态:')
        print(f'  阶段: {state_b.get("stage")}')
        print(f'  生辰: {state_b.get("birth_info")}')
        print(f'  算命类型: {state_b.get("calculation_type")}')
        print(f'  result_id: {state_b.get("result_id", "None")[:8] if state_b.get("result_id") else "None"}...')
        print(f'  聊天记录数: {len(state_b.get("conversation_history", []))}')
        
        # 继续对话测试
        print('\n💬 继续对话测试:')
        
        # 会话A继续
        result_a2 = await coordinator.handle_user_message(session_a, '我的财运如何？')
        print(f'A2 (财运): {result_a2.get("response", "")[:80]}...')
        
        # 会话B继续
        result_b2 = await coordinator.handle_user_message(session_b, '我的感情运势怎么样？')
        print(f'B2 (感情): {result_b2.get("response", "")[:80]}...')
        
        # 再次检查状态
        print('\n📊 对话后的状态检查:')
        
        state_a_after = master_agent.get_session_state(session_a)
        state_b_after = master_agent.get_session_state(session_b)
        
        print(f'会话A聊天记录数: {len(state_a_after.get("conversation_history", []))}')
        print(f'会话B聊天记录数: {len(state_b_after.get("conversation_history", []))}')
        
        # 验证隔离性
        print('\n🔍 隔离性验证:')
        
        # 1. 生辰信息隔离
        birth_a = state_a.get("birth_info", {})
        birth_b = state_b.get("birth_info", {})
        
        if birth_a.get("year") != birth_b.get("year"):
            print('✅ 生辰信息正确隔离')
        else:
            print('❌ 生辰信息可能混淆')
        
        # 2. 算命类型隔离
        type_a = state_a.get("calculation_type")
        type_b = state_b.get("calculation_type")
        
        if type_a != type_b:
            print('✅ 算命类型正确隔离')
        else:
            print('❌ 算命类型可能混淆')
        
        # 3. result_id隔离
        result_id_a = state_a.get("result_id")
        result_id_b = state_b.get("result_id")
        
        if result_id_a != result_id_b and result_id_a and result_id_b:
            print('✅ 分析结果ID正确隔离')
        elif not result_id_a or not result_id_b:
            print('⚠️ 部分分析结果ID未生成')
        else:
            print('❌ 分析结果ID可能混淆')
        
        # 4. 聊天记录隔离
        history_a = state_a_after.get("conversation_history", [])
        history_b = state_b_after.get("conversation_history", [])
        
        # 检查是否有交叉污染
        a_messages = [msg.get("content", "") for msg in history_a if msg.get("role") == "user"]
        b_messages = [msg.get("content", "") for msg in history_b if msg.get("role") == "user"]
        
        cross_contamination = False
        for a_msg in a_messages:
            if any("1990年" in a_msg or "女命" in a_msg for a_msg in a_messages):
                cross_contamination = True
                break
        
        for b_msg in b_messages:
            if any("1988年" in b_msg or "男命" in b_msg for b_msg in b_messages):
                cross_contamination = True
                break
        
        if not cross_contamination:
            print('✅ 聊天记录正确隔离')
        else:
            print('❌ 聊天记录可能交叉污染')
        
        # 5. 测试会话切换
        print('\n🔄 测试会话切换:')
        
        # 创建第三个会话
        session_c = 'session_user_c'
        result_c1 = await coordinator.handle_user_message(session_c, '你好')
        
        # 检查新会话是否干净
        state_c = master_agent.get_session_state(session_c)
        
        if not state_c.get("birth_info") and state_c.get("stage") == "greeting":
            print('✅ 新会话状态干净')
        else:
            print('❌ 新会话状态可能被污染')
        
        # 总结
        print('\n📋 会话隔离测试总结:')
        print(f'  总会话数: {len(master_agent.session_states)}')
        print(f'  会话A记录数: {len(state_a_after.get("conversation_history", []))}')
        print(f'  会话B记录数: {len(state_b_after.get("conversation_history", []))}')
        print(f'  会话C记录数: {len(state_c.get("conversation_history", []))}')
        
        print('\n🎉 会话隔离测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_session_isolation())
