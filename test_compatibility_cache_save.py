#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合盘分析缓存保存
"""

import asyncio
import time
from datetime import datetime

def test_compatibility_cache_save():
    """测试合盘分析缓存保存"""
    print("🔮 测试合盘分析缓存保存")
    
    try:
        # 1. 导入必要模块
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        from core.storage.calculation_cache import CalculationCache
        
        # 2. 测试数据
        person_a_info = {
            "name": "测试男A",
            "year": "1990",
            "month": "5", 
            "day": "20",
            "hour": "午时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "测试女B",
            "year": "1992",
            "month": "9",
            "day": "15", 
            "hour": "酉时",
            "gender": "女"
        }
        
        analysis_dimension = "emotional_harmony"
        
        print(f"📊 测试数据:")
        print(f"   A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"   B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        print(f"   维度: {analysis_dimension}")
        
        # 3. 初始化引擎
        print("🔧 初始化合盘分析引擎...")
        compatibility_engine = CompatibilityAnalysisEngine()
        
        # 4. 计算合盘数据
        print("📊 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)
        
        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False
        
        print(f"✅ 合盘数据计算成功")
        
        # 5. 执行合盘分析
        print(f"🤖 执行{analysis_dimension}分析...")
        
        async def run_analysis():
            result = await compatibility_engine.execute_compatibility_analysis(
                compatibility_data, analysis_dimension
            )
            return result
        
        # 运行异步分析
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(run_analysis())
        loop.close()
        
        if not result.get("success"):
            print(f"❌ 合盘分析失败: {result.get('error')}")
            return False
        
        print(f"✅ 合盘分析成功")
        print(f"   分析内容长度: {len(result.get('content', ''))}字符")
        print(f"   验证评分: {result.get('validation_report', {}).get('score', 0)}")
        
        # 6. 测试缓存保存
        print("💾 测试缓存保存...")
        cache = CalculationCache()
        
        # 构建合盘专用的birth_info
        compatibility_birth_info = {
            "person_a": f"{person_a_info['name']}({person_a_info['year']}-{person_a_info['month']}-{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']})",
            "person_b": f"{person_b_info['name']}({person_b_info['year']}-{person_b_info['month']}-{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']})",
            "analysis_dimension": analysis_dimension,
            "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"   birth_info: {compatibility_birth_info}")
        
        # 保存结果
        try:
            result_id = cache.save_result(
                user_id=f"test_compatibility_{int(time.time())}",
                session_id=f"test_{person_a_info['name']}_{person_b_info['name']}_{int(time.time())}",
                calculation_type="compatibility",
                birth_info=compatibility_birth_info,
                raw_calculation=compatibility_data,
                detailed_analysis={
                    "compatibility_analysis": result.get("content", ""),
                    "analysis_dimension": analysis_dimension,
                    "person_a_info": person_a_info,
                    "person_b_info": person_b_info,
                    "relationship_type": compatibility_data.get('relationship_type', '未知关系'),
                    "compatibility_result": result
                },
                summary=f"合盘分析：{person_a_info['name']} & {person_b_info['name']} - {analysis_dimension}",
                keywords=["合盘", "匹配度", analysis_dimension, compatibility_data.get('relationship_type', '未知关系')],
                confidence=0.9,
                chart_image_path=""
            )
            
            print(f"✅ 缓存保存成功")
            print(f"   结果ID: {result_id}")
            
        except Exception as save_error:
            print(f"❌ 缓存保存失败: {save_error}")
            import traceback
            traceback.print_exc()
            return False
        
        # 7. 验证缓存读取
        print("🔍 验证缓存读取...")
        try:
            cached_result = cache.get_result(result_id)
            
            if cached_result:
                print(f"✅ 缓存读取成功")
                print(f"   计算类型: {cached_result.calculation_type}")
                print(f"   分析维度: {cached_result.detailed_analysis.get('analysis_dimension')}")
                print(f"   分析内容: {len(cached_result.detailed_analysis.get('compatibility_analysis', ''))}字符")
                print(f"   创建时间: {cached_result.created_at}")
            else:
                print(f"❌ 缓存读取失败")
                return False
                
        except Exception as read_error:
            print(f"❌ 缓存读取失败: {read_error}")
            import traceback
            traceback.print_exc()
            return False
        
        # 8. 测试Web界面记录获取
        print("🌐 测试Web界面记录获取...")
        try:
            from backend_agent_web import get_all_cache_records
            
            records = get_all_cache_records()
            compatibility_records = [r for r in records if r.get('calculation_type') == 'compatibility']
            
            print(f"   总记录数: {len(records)}")
            print(f"   合盘记录数: {len(compatibility_records)}")
            
            if compatibility_records:
                latest_record = compatibility_records[-1]  # 最新的记录
                print(f"   最新合盘记录:")
                print(f"     ID: {latest_record['result_id'][:12]}...")
                print(f"     信息: {latest_record['birth_info']}")
                print(f"     完成度: {latest_record['completed_angles']}")
                print(f"     字数: {latest_record['total_words']}")
                print(f"     状态: {'已完成' if latest_record['completed_angles'] > 0 and latest_record['total_words'] > 0 else '进行中'}")
            else:
                print(f"   ⚠️ 没有找到合盘记录")
                
        except Exception as web_error:
            print(f"❌ Web界面记录获取失败: {web_error}")
            import traceback
            traceback.print_exc()
            return False
        
        print("🎉 合盘分析缓存保存测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_compatibility_cache_save()
    if success:
        print("\n✅ 合盘分析缓存保存测试通过！")
        print("🌐 现在可以在Web界面中查看合盘记录了：http://localhost:8501")
        print("📋 检查项目:")
        print("   1. 首页快速统计应该显示合盘记录")
        print("   2. 分析记录页面应该显示合盘分析")
        print("   3. 实时监控页面应该显示合盘进度")
        print("   4. 可以查看合盘分析详情")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
