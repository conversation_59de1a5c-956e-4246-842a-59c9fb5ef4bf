#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全启动脚本 - 解决编码问题
"""

import os
import sys
import locale

def setup_encoding():
    """设置编码环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['LANG'] = 'zh_CN.UTF-8'
    os.environ['LC_ALL'] = 'zh_CN.UTF-8'
    
    # 设置Streamlit环境
    os.environ['STREAMLIT_SERVER_ENABLE_STATIC_SERVING'] = 'true'
    os.environ['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'
    os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
    os.environ['STREAMLIT_SERVER_RUN_ON_SAVE'] = 'false'
    
    # 强制设置标准输出编码
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8')

def main():
    """主函数"""
    print("🚀 启动紫薇+八字融合分析系统")
    print("=" * 50)
    
    # 设置编码
    setup_encoding()
    
    # 检查编码设置
    print(f"✅ Python编码: {sys.getdefaultencoding()}")
    print(f"✅ 文件系统编码: {sys.getfilesystemencoding()}")
    print(f"✅ 标准输出编码: {sys.stdout.encoding}")
    
    # 启动Streamlit应用
    import subprocess
    
    try:
        print("\n🌟 正在启动Web界面...")
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "backend_agent_web.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请尝试手动运行: streamlit run backend_agent_web.py")

if __name__ == "__main__":
    main()
