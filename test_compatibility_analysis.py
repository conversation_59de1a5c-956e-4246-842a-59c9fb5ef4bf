#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合盘分析功能
"""

import asyncio

async def test_compatibility_analysis():
    """测试合盘分析功能"""
    print("💕 测试合盘分析功能")
    print("=" * 60)
    
    try:
        # 1. 测试合盘分析引擎初始化
        print("1️⃣ 测试合盘分析引擎初始化")
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        
        compatibility_engine = CompatibilityAnalysisEngine()
        print("✅ 合盘分析引擎初始化成功")
        
        # 2. 准备测试数据
        print("\n2️⃣ 准备测试数据")
        person_a_info = {
            "name": "张三",
            "year": "1988",
            "month": "6",
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "李四",
            "year": "1990",
            "month": "8",
            "day": "15",
            "hour": "酉时",
            "gender": "女"
        }
        
        print(f"👤 A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"👤 B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        
        # 3. 测试合盘数据计算
        print("\n3️⃣ 测试合盘数据计算")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)
        
        if compatibility_data.get("success"):
            print("✅ 合盘数据计算成功")
            print(f"🔗 关系类型: {compatibility_data.get('relationship_type')}")
            print(f"📊 分析维度数量: {len(compatibility_data.get('compatibility_dimensions', []))}")
            
            # 检查双方数据
            person_a_data = compatibility_data.get("person_a", {})
            person_b_data = compatibility_data.get("person_b", {})
            
            if person_a_data and person_b_data:
                print(f"✅ {person_a_info['name']}数据完整")
                print(f"✅ {person_b_info['name']}数据完整")
            else:
                print(f"❌ 双方数据不完整")
                return False
        else:
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False
        
        # 4. 测试性格互补性分析
        print("\n4️⃣ 测试性格互补性分析")
        analysis_dimension = "personality_compatibility"
        
        result = await compatibility_engine.execute_compatibility_analysis(compatibility_data, analysis_dimension)
        
        if result.get("success"):
            content = result.get("content", "")
            word_count = len(content)
            print(f"✅ 性格互补性分析成功: {word_count} 字符")
            
            # 检查内容质量
            quality_checks = {
                "包含双方姓名": person_a_info['name'] in content and person_b_info['name'] in content,
                "包含性格分析": any(keyword in content for keyword in ["性格", "互补", "匹配", "相处"]),
                "包含紫薇信息": any(keyword in content for keyword in ["紫薇", "命宫", "宫位"]),
                "包含八字信息": any(keyword in content for keyword in ["八字", "四柱", "五行"]),
                "包含负面分析": any(keyword in content for keyword in ["冲突", "问题", "风险", "挑战"]),
                "内容充实": word_count >= 1000
            }
            
            passed_checks = sum(quality_checks.values())
            total_checks = len(quality_checks)
            
            print(f"    内容质量: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
            for check_name, result_check in quality_checks.items():
                print(f"      {check_name}: {'✅' if result_check else '❌'}")
            
            # 保存测试内容
            with open("test_compatibility_result.txt", "w", encoding="utf-8") as f:
                f.write(f"合盘分析测试结果:\n")
                f.write("=" * 60 + "\n\n")
                f.write(f"👤 A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}\n")
                f.write(f"👤 B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}\n")
                f.write(f"🔗 关系类型: {compatibility_data.get('relationship_type')}\n")
                f.write(f"🎯 分析维度: {analysis_dimension}\n")
                f.write(f"📊 字符数: {word_count}\n")
                f.write(f"📈 质量评分: {passed_checks}/{total_checks}\n")
                f.write("\n" + "="*60 + "\n")
                f.write("分析内容:\n")
                f.write("="*60 + "\n\n")
                f.write(content)
            
            print(f"    💾 内容已保存到 test_compatibility_result.txt")
            
            return True
            
        else:
            print(f"❌ 性格互补性分析失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_dimensions():
    """测试多个合盘分析维度"""
    print("\n💕 测试多个合盘分析维度")
    print("=" * 60)
    
    try:
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        
        compatibility_engine = CompatibilityAnalysisEngine()
        
        # 测试数据
        person_a_info = {
            "name": "王五",
            "year": "1985",
            "month": "3",
            "day": "20",
            "hour": "辰时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "赵六",
            "year": "1987",
            "month": "11",
            "day": "8",
            "hour": "未时",
            "gender": "女"
        }
        
        # 计算合盘数据
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)
        
        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False
        
        # 测试多个维度
        test_dimensions = [
            "personality_compatibility",  # 性格互补性
            "emotional_harmony",          # 感情和谐度
            "wealth_cooperation"          # 财运配合度
        ]
        
        results = {}
        
        for dimension in test_dimensions:
            print(f"\n🔍 测试 {dimension}")
            
            try:
                result = await compatibility_engine.execute_compatibility_analysis(compatibility_data, dimension)
                
                if result.get("success"):
                    content = result.get("content", "")
                    word_count = len(content)
                    results[dimension] = {
                        "success": True,
                        "word_count": word_count,
                        "content": content[:200] + "..." if len(content) > 200 else content
                    }
                    print(f"  ✅ {dimension}: {word_count} 字符")
                else:
                    results[dimension] = {
                        "success": False,
                        "error": result.get("error", "未知错误")
                    }
                    print(f"  ❌ {dimension}: {result.get('error')}")
                    
            except Exception as e:
                results[dimension] = {
                    "success": False,
                    "error": str(e)
                }
                print(f"  ❌ {dimension}: 异常 - {e}")
        
        # 统计结果
        successful_dimensions = [dim for dim, result in results.items() if result.get("success")]
        total_word_count = sum(result.get("word_count", 0) for result in results.values() if result.get("success"))
        
        print(f"\n📊 多维度测试结果:")
        print(f"  成功维度: {len(successful_dimensions)}/{len(test_dimensions)}")
        print(f"  总字符数: {total_word_count:,}")
        print(f"  平均字符数: {total_word_count//len(successful_dimensions) if successful_dimensions else 0:,}")
        
        # 保存多维度测试结果
        with open("test_multiple_compatibility_dimensions.txt", "w", encoding="utf-8") as f:
            f.write("多维度合盘分析测试结果:\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"👤 A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}\n")
            f.write(f"👤 B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}\n")
            f.write(f"🔗 关系类型: {compatibility_data.get('relationship_type')}\n\n")
            
            for dimension, result in results.items():
                f.write(f"🎯 {dimension}:\n")
                if result.get("success"):
                    f.write(f"  ✅ 成功: {result.get('word_count')} 字符\n")
                    f.write(f"  📝 内容预览: {result.get('content')}\n")
                else:
                    f.write(f"  ❌ 失败: {result.get('error')}\n")
                f.write("\n")
        
        print(f"💾 多维度测试结果已保存到 test_multiple_compatibility_dimensions.txt")
        
        return len(successful_dimensions) >= 2  # 至少2个维度成功
        
    except Exception as e:
        print(f"❌ 多维度测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始合盘分析功能测试")
    print("=" * 80)
    
    # 运行基础测试
    basic_result = asyncio.run(test_compatibility_analysis())
    
    # 运行多维度测试
    multi_result = asyncio.run(test_multiple_dimensions())
    
    print("\n" + "=" * 80)
    print("🏁 测试总结")
    print("=" * 80)
    
    if basic_result and multi_result:
        print("🎉 合盘分析功能测试全部通过！")
        print("✅ 基础功能正常")
        print("✅ 多维度分析正常")
        print("✅ 数据传递正确")
        print("✅ 内容质量良好")
        print("\n🌟 合盘功能已准备就绪，可以在Web界面中使用！")
    elif basic_result:
        print("⚠️ 合盘分析功能部分通过")
        print("✅ 基础功能正常")
        print("❌ 多维度分析存在问题")
    else:
        print("❌ 合盘分析功能测试失败")
        print("❌ 基础功能存在问题")
