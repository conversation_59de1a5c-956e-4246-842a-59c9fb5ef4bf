#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试六爻算法问题
"""

def test_liuyao_direct():
    """直接测试六爻算法"""
    print("🔧 直接测试六爻算法")
    print("=" * 40)
    
    try:
        # 1. 测试导入
        print("1. 测试六爻算法导入...")
        import sys
        sys.path.append('algorithms')
        
        from liuyao_calculator import LiuyaoCalculator
        print("✅ 六爻算法导入成功")
        
        # 2. 测试初始化
        print("2. 测试六爻算法初始化...")
        calc = LiuyaoCalculator()
        print("✅ 六爻算法初始化成功")
        
        # 3. 测试功能
        print("3. 测试六爻算法功能...")
        result = calc.divine_by_time(2025, 6, 19, 9, 0)
        print(f"功能测试结果: {result}")
        
        if result.get("success"):
            print("✅ 六爻算法功能正常")
            return calc
        else:
            print(f"❌ 六爻算法功能异常: {result.get('error')}")
            return None
            
    except Exception as e:
        print(f"❌ 六爻算法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_fortune_engine_direct():
    """直接测试FortuneEngine"""
    print("\n🔮 直接测试FortuneEngine")
    print("=" * 40)
    
    try:
        # 1. 获取六爻算法实例
        calc = test_liuyao_direct()
        if not calc:
            print("❌ 无法获取六爻算法实例")
            return False
        
        # 2. 测试FortuneEngine
        print("4. 测试FortuneEngine导入...")
        import sys
        sys.path.append('core')
        
        from fortune_engine import FortuneEngine
        print("✅ FortuneEngine导入成功")
        
        # 3. 创建FortuneEngine实例
        print("5. 创建FortuneEngine实例...")
        
        def mock_api(prompt):
            return "liuyao"
        
        engine = FortuneEngine(
            ziwei_calc=None,
            bazi_calc=None,
            liuyao_calc=calc,  # 传入六爻算法实例
            chat_api_func=mock_api
        )
        print("✅ FortuneEngine创建成功")
        
        # 4. 检查六爻算法是否正确传入
        print("6. 检查六爻算法是否正确传入...")
        print(f"engine.liuyao_calc存在: {engine.liuyao_calc is not None}")
        print(f"engine.liuyao_calc类型: {type(engine.liuyao_calc)}")
        
        if not engine.liuyao_calc:
            print("❌ 六爻算法未正确传入FortuneEngine")
            return False
        
        # 5. 测试六爻算法调用
        print("7. 测试FortuneEngine中的六爻算法调用...")
        birth_info = {
            "year": 2025,
            "month": 6,
            "day": 19,
            "hour": 9
        }
        
        result = engine._call_liuyao_api(birth_info)
        print(f"六爻算法调用结果: {result}")
        
        if result.get("success"):
            print("✅ FortuneEngine中的六爻算法调用成功")
            return True
        else:
            print(f"❌ FortuneEngine中的六爻算法调用失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ FortuneEngine测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 六爻算法问题诊断")
    print("=" * 60)
    
    # 测试1: 直接测试六爻算法
    liuyao_success = test_liuyao_direct()
    
    # 测试2: 测试FortuneEngine
    if liuyao_success:
        engine_success = test_fortune_engine_direct()
    else:
        engine_success = False
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 六爻算法问题诊断结果:")
    print(f"  六爻算法直接测试: {'✅ 成功' if liuyao_success else '❌ 失败'}")
    print(f"  FortuneEngine集成: {'✅ 成功' if engine_success else '❌ 失败'}")
    
    if liuyao_success and engine_success:
        print("\n🎊 所有测试通过！")
        print("\n💡 **问题分析:**")
        print("  如果这里的测试都通过了，但API服务器仍然报错，")
        print("  那么问题在于API服务器启动时的六爻算法初始化。")
        print()
        print("🔧 **解决方案:**")
        print("  1. 重启API服务器")
        print("  2. 查看启动日志中的六爻算法初始化信息")
        print("  3. 访问调试端点: http://localhost:8000/v1/debug/liuyao")
        print("  4. 确认六爻算法实例正确传递给FortuneEngine")
    else:
        print("\n⚠️ 部分测试失败")
        print("\n🔧 **需要检查:**")
        if not liuyao_success:
            print("  - 六爻算法模块安装和导入")
        if not engine_success:
            print("  - FortuneEngine与六爻算法的集成")

if __name__ == "__main__":
    main()
