#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化LLM客户端
"""

import requests
import json
import time
from typing import Dict, Any, Optional
from config import config
from .simple_logger import get_logger

logger = get_logger()

class SimpleLLMClient:
    """简化的LLM客户端"""
    
    def __init__(self):
        """初始化LLM客户端"""
        self.api_key = config.llm.api_key
        self.base_url = config.llm.base_url
        self.model_name = config.llm.model_name
        self.timeout = config.llm.timeout
        self.temperature = config.llm.temperature
        self.max_tokens = config.llm.max_tokens
        self.max_retries = config.llm.max_retries
        
        if not self.api_key:
            logger.error("LLM API密钥未配置")
            raise ValueError("LLM API密钥未配置")
        
        logger.info(f"LLM客户端初始化完成: {self.model_name}")
    
    def chat(self, prompt: str, system_prompt: Optional[str] = None) -> Optional[str]:
        """
        发送聊天请求
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            
        Returns:
            LLM回复内容
        """
        messages = []
        
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        
        messages.append({
            "role": "user", 
            "content": prompt
        })
        
        return self._send_request(messages)
    
    def _send_request(self, messages: list) -> Optional[str]:
        """发送请求到LLM API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model_name,
            "messages": messages,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "stream": False
        }
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"发送LLM请求 (尝试 {attempt + 1}/{self.max_retries + 1})")
                
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    logger.debug(f"LLM请求成功，返回内容长度: {len(content)}")
                    return content
                else:
                    logger.error(f"LLM API错误: {response.status_code} - {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"LLM请求超时 (尝试 {attempt + 1})")
            except requests.exceptions.RequestException as e:
                logger.error(f"LLM请求异常: {e}")
            except Exception as e:
                logger.error(f"LLM处理异常: {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries:
                wait_time = (attempt + 1) * 2  # 递增等待时间
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        logger.error("LLM请求失败，已达到最大重试次数")
        return None

# 全局LLM客户端实例
_llm_client = None

def get_llm_client() -> SimpleLLMClient:
    """获取LLM客户端实例"""
    global _llm_client
    if _llm_client is None:
        _llm_client = SimpleLLMClient()
    return _llm_client
