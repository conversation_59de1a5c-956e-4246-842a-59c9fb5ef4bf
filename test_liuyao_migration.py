#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试六爻工具迁移到人性化交互系统
"""

import sys
import os
sys.path.append('.')

def test_humanized_liuyao_tool():
    """测试人性化六爻工具"""
    print("测试人性化六爻工具")
    print("-" * 50)

    try:
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool

        # 创建工具实例
        tool = HumanizedLiuyaoTool()

        # 测试数据
        intent = {
            "intent": "liuyao",
            "original_message": "我想问问我的事业发展如何？",
            "entities": {
                "question": "我的事业发展如何？",
                "method": "time"
            }
        }

        context = {}

        # 执行测试
        result = tool.execute(intent, context)

        if result.get("success"):
            print("✅ 人性化六爻工具执行成功")
            print(f"   类型: {result.get('type')}")
            print(f"   消息: {result.get('message')}")

            # 检查计算结果
            calc_result = result.get("calculation_result", {})
            if calc_result.get("success"):
                print("✅ 六爻计算成功")
                method = calc_result.get("method", "未知")
                datetime_str = calc_result.get("datetime", "未知")
                print(f"   起卦方法: {method}")
                print(f"   起卦时间: {datetime_str}")

                raw_result = calc_result.get("raw_result", {})
                if "主卦" in raw_result:
                    print(f"   主卦: {raw_result.get('主卦', '未知')}")
                if "变卦" in raw_result:
                    print(f"   变卦: {raw_result.get('变卦', '未知')}")
                if "动爻" in raw_result:
                    print(f"   动爻: {raw_result.get('动爻', [])}")
            else:
                print(f"❌ 六爻计算失败: {calc_result.get('error')}")
        else:
            print(f"❌ 人性化六爻工具执行失败: {result.get('error')}")

        return result.get("success", False)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selector_liuyao():
    """测试工具选择器的六爻处理"""
    print("\n测试工具选择器的六爻处理")
    print("-" * 50)

    try:
        from core.tools.tool_selector import ToolSelector

        selector = ToolSelector()

        # 测试意图
        intent_result = {
            "intent": "liuyao",
            "confidence": 0.9,
            "original_message": "我想问问我的财运如何？",
            "entities": {
                "question": "我的财运如何？"
            }
        }

        context = {}

        # 选择工具
        tool_result = selector.select_tool(intent_result, context)

        if tool_result.get("success"):
            print("✅ 工具选择成功")
            print(f"   工具名称: {tool_result.get('tool_name')}")
            print(f"   工具描述: {tool_result.get('tool_description')}")

            result = tool_result.get("result", {})
            if result.get("success"):
                print("✅ 六爻分析执行成功")
                print(f"   分析类型: {result.get('type')}")
                print(f"   消息: {result.get('message')}")
            else:
                print(f"❌ 六爻分析执行失败: {result.get('error')}")
        else:
            print(f"❌ 工具选择失败: {tool_result.get('error')}")

        return tool_result.get("success", False)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_humanized_engine_liuyao():
    """测试人性化引擎的六爻分析"""
    print("\n测试人性化引擎的六爻分析")
    print("-" * 50)

    try:
        from core.conversation.humanized_fortune_engine import HumanizedFortuneEngine

        engine = HumanizedFortuneEngine()
        session_id = "test_liuyao_migration"

        # 测试六爻占卜请求（使用明确的六爻关键词）
        message = "我想用六爻占卜我的感情运势如何？"

        print(f"用户消息: {message}")

        responses = engine.process_user_message(message, session_id)

        print(f"总响应数: {len(responses)}")

        # 分析响应类型
        response_types = {}
        liuyao_analysis_count = 0

        for response in responses:
            response_type = response.get("type", "unknown")
            response_types[response_type] = response_types.get(response_type, 0) + 1

            if response_type == "detailed_analysis":
                liuyao_analysis_count += 1
                content = response.get("content", "")
                print(f"\n六爻分析 {liuyao_analysis_count}:")
                print(f"  长度: {len(content)} 字符")
                print(f"  预览: {content[:100]}...")

                # 检查是否包含六爻特征
                liuyao_features = ["卦象", "六爻", "主卦", "变卦", "动爻"]
                found_features = [f for f in liuyao_features if f in content]
                print(f"  六爻特征: {len(found_features)}/{len(liuyao_features)} ({found_features})")

        print(f"\n响应类型分布:")
        for response_type, count in response_types.items():
            print(f"  {response_type}: {count} 个")

        # 检查关键响应类型
        required_types = ["chart_presentation", "analysis_intro", "detailed_analysis", "synthesis"]
        missing_types = [t for t in required_types if t not in response_types]

        if missing_types:
            print(f"\n⚠️ 缺少关键响应类型: {missing_types}")
        else:
            print(f"\n✅ 所有关键响应类型都存在")

        success = (
            len(responses) >= 10 and  # 至少10个响应
            liuyao_analysis_count >= 4 and  # 至少4个详细分析
            len(missing_types) == 0  # 没有缺少的类型
        )

        print(f"\n人性化六爻分析: {'✅ 成功' if success else '⚠️ 需要改进'}")

        return success

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_liuyao_fewshot_integration():
    """测试六爻Few-shot Learning集成"""
    print("\n测试六爻Few-shot Learning集成")
    print("-" * 50)

    try:
        from core.nlu.llm_client import LLMClient

        client = LLMClient()

        # 测试六爻相关的Few-shot调用
        test_cases = [
            ("我想问问我的事业发展如何？", "career"),
            ("六爻占卜我的财运状况如何？", "wealth"),
            ("请用六爻帮我看看感情运势", "love"),
            ("六爻算卦看我的整体运势", "general")
        ]

        success_count = 0

        for message, category in test_cases:
            print(f"\n测试: {message}")

            response = client.fewshot_chat(
                user_message=message,
                category=category,
                temperature=0.7,
                max_tokens=500
            )

            if response and len(response) > 100:
                print(f"✅ 响应成功: {len(response)} 字符")

                # 检查六爻专业术语
                liuyao_terms = ["根据", "卦象", "六爻", "建议", "通过", "将会"]
                found_terms = [term for term in liuyao_terms if term in response]

                print(f"   专业术语: {len(found_terms)}/{len(liuyao_terms)}")
                print(f"   内容预览: {response[:80]}...")

                if len(found_terms) >= 4:
                    success_count += 1
                    print(f"   质量: ✅ 优秀")
                else:
                    print(f"   质量: ⚠️ 一般")
            else:
                print(f"❌ 响应失败或过短")

        print(f"\nFew-shot六爻集成: {success_count}/{len(test_cases)} 优秀")

        return success_count >= len(test_cases) * 0.75

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_liuyao_question_types():
    """测试六爻问题类型识别"""
    print("\n测试六爻问题类型识别")
    print("-" * 50)

    try:
        from core.tools.humanized_liuyao_tool import HumanizedLiuyaoTool

        tool = HumanizedLiuyaoTool()

        # 测试不同类型的问题
        test_questions = [
            ("我的事业发展如何？", "career"),
            ("我的财运状况怎么样？", "wealth"),
            ("我的感情运势如何？", "love"),
            ("我的身体健康状况如何？", "health"),
            ("我的考试能通过吗？", "study"),
            ("我最近运势如何？", "general")
        ]

        success_count = 0

        for question, expected_type in test_questions:
            question_type = tool._determine_question_type(question)

            if question_type == expected_type:
                print(f"✅ {question} -> {question_type}")
                success_count += 1
            else:
                print(f"❌ {question} -> {question_type} (期望: {expected_type})")

        print(f"\n问题类型识别: {success_count}/{len(test_questions)} 正确")

        return success_count >= len(test_questions) * 0.8

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("阶段3.4：六爻工具迁移测试")
    print("=" * 80)
    print("目标: 将六爻占卜功能迁移到人性化交互系统")
    print("=" * 80)

    # 执行测试
    test_results = []

    # 1. 人性化六爻工具测试
    test_results.append(("人性化六爻工具", test_humanized_liuyao_tool()))

    # 2. 工具选择器六爻处理测试
    test_results.append(("工具选择器六爻处理", test_tool_selector_liuyao()))

    # 3. 人性化引擎六爻分析测试
    test_results.append(("人性化引擎六爻分析", test_humanized_engine_liuyao()))

    # 4. 六爻Few-shot集成测试
    test_results.append(("六爻Few-shot集成", test_liuyao_fewshot_integration()))

    # 5. 六爻问题类型识别测试
    test_results.append(("六爻问题类型识别", test_liuyao_question_types()))

    # 汇总结果
    print(f"\n阶段3.4六爻工具迁移测试结果")
    print("=" * 80)

    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 阶段3.4六爻工具迁移成功！")
        print("\n🎯 实现的核心功能:")
        print("  ✅ 人性化六爻工具 - 统一接口设计")
        print("  ✅ 工具选择器集成 - 智能路由到六爻占卜")
        print("  ✅ 人性化交互体验 - 15段式六爻分析")
        print("  ✅ Few-shot Learning - 专业六爻话术")
        print("  ✅ 问题类型识别 - 智能分类占卜问题")
        print("\n🌟 六爻占卜特色:")
        print("  🔮 真实六爻算法 - 时间起卦、数字起卦")
        print("  💬 专业话术融入 - 基于微调模型训练数据")
        print("  🔄 分段式交互 - 卦象展示、详细分析")
        print("  🎯 智能占卜 - 结合真实卦象数据的专业解读")
        print("  📊 多种起卦方式 - 时间起卦、数字起卦")
        print("\n📋 PRD阶段3.4目标达成！")
    else:
        print("💥 部分功能存在问题，需要修复后再继续")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
