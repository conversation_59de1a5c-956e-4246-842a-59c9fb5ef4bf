#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合盘分析引擎 - 分析两个人的命盘匹配度和相互适应性
"""

import logging
from typing import Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class CompatibilityAnalysisEngine:
    """合盘分析引擎"""

    def __init__(self):
        """初始化合盘分析引擎"""
        self.analysis_dimensions = [
            ("personality_compatibility", "性格互补性", "分析两人性格的匹配度和互补性"),
            ("wealth_cooperation", "财运配合度", "分析两人在财富方面的配合和影响"),
            ("emotional_harmony", "感情和谐度", "分析两人感情关系的和谐程度"),
            ("career_partnership", "事业合作潜力", "分析两人在事业方面的合作潜力"),
            ("health_influence", "健康相互影响", "分析两人健康方面的相互影响"),
            ("family_harmony", "家庭和睦度", "分析两人组建家庭的和睦程度"),
            ("children_fortune", "子女缘分", "分析两人的子女运势和教育配合"),
            ("overall_compatibility", "综合匹配度", "综合评估两人的整体匹配度")
        ]

        logger.info("✅ 合盘分析引擎初始化完成")

    def calculate_compatibility(self, person_a_info: Dict[str, Any], person_b_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算两人的合盘分析

        Args:
            person_a_info: A的出生信息 {"year": "1988", "month": "6", "day": "1", "hour": "午时", "gender": "男", "name": "张三"}
            person_b_info: B的出生信息 {"year": "1990", "month": "8", "day": "15", "hour": "酉时", "gender": "女", "name": "李四"}

        Returns:
            合盘分析结果
        """
        try:
            logger.info(f"🔮 开始合盘分析: {person_a_info.get('name', 'A')} & {person_b_info.get('name', 'B')}")

            # 1. 获取两人的个人命盘数据
            person_a_data = self._get_individual_analysis(person_a_info)
            person_b_data = self._get_individual_analysis(person_b_info)

            if not person_a_data.get("success") or not person_b_data.get("success"):
                return {
                    "success": False,
                    "error": "个人命盘分析失败",
                    "person_a_data": person_a_data,
                    "person_b_data": person_b_data
                }

            # 2. 构建合盘分析数据
            compatibility_data = {
                "success": True,
                "analysis_type": "compatibility_analysis",
                "timestamp": datetime.now().isoformat(),
                "person_a": {
                    "info": person_a_info,
                    "analysis": person_a_data
                },
                "person_b": {
                    "info": person_b_info,
                    "analysis": person_b_data
                },
                "compatibility_dimensions": self.analysis_dimensions,
                "relationship_type": self._determine_relationship_type(person_a_info, person_b_info)
            }

            logger.info(f"✅ 合盘数据构建完成")
            return compatibility_data

        except Exception as e:
            logger.error(f"❌ 合盘分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _get_individual_analysis(self, person_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取个人的紫薇+八字分析数据"""
        try:
            from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

            fusion_engine = ZiweiBaziFusionEngine()

            # 转换时辰
            hour_number = self._convert_hour_to_number(person_info.get("hour", "午时"))

            # 获取融合分析
            result = fusion_engine.calculate_fusion_analysis(
                year=int(person_info.get("year")),
                month=int(person_info.get("month")),
                day=int(person_info.get("day")),
                hour=hour_number,
                gender=person_info.get("gender")
            )

            return result

        except Exception as e:
            logger.error(f"❌ 获取个人分析数据失败: {e}")
            return {"success": False, "error": str(e)}

    def _convert_hour_to_number(self, hour_str: str) -> int:
        """转换时辰字符串为数字"""
        hour_mapping = {
            "子时": 0, "丑时": 2, "寅时": 4, "卯时": 6,
            "辰时": 8, "巳时": 10, "午时": 12, "未时": 14,
            "申时": 16, "酉时": 18, "戌时": 20, "亥时": 22
        }
        return hour_mapping.get(hour_str, 12)

    def _determine_relationship_type(self, person_a_info: Dict[str, Any], person_b_info: Dict[str, Any]) -> str:
        """判断关系类型"""
        gender_a = person_a_info.get("gender", "")
        gender_b = person_b_info.get("gender", "")

        if gender_a == "男" and gender_b == "女":
            return "异性情侣"
        elif gender_a == "女" and gender_b == "男":
            return "异性情侣"
        elif gender_a == "男" and gender_b == "男":
            return "同性合作"
        elif gender_a == "女" and gender_b == "女":
            return "同性合作"
        else:
            return "未知关系"

    async def execute_compatibility_analysis(self, compatibility_data: Dict[str, Any], analysis_dimension: str) -> Dict[str, Any]:
        """
        执行特定维度的合盘分析

        Args:
            compatibility_data: 合盘基础数据
            analysis_dimension: 分析维度 (如 "personality_compatibility")

        Returns:
            分析结果
        """
        try:
            logger.info(f"🔄 开始执行合盘分析维度: {analysis_dimension}")

            # 使用专用的合盘提示词构建器和LLM分析器（确保详细度）
            from core.analysis.compatibility_prompt_builder import CompatibilityPromptBuilder
            from core.analysis.llm_analyzer import LLMAnalyzer
            from core.analysis.result_validator import ResultValidator

            # 1. 构建详细提示词（5000字以上要求）
            prompt_builder = CompatibilityPromptBuilder()
            prompt = prompt_builder.build_compatibility_prompt(compatibility_data, analysis_dimension)

            logger.info(f"✅ 合盘详细提示词构建完成，长度: {len(prompt)}字符")

            # 2. 执行LLM分析（合盘专用高token限制）
            llm_analyzer = LLMAnalyzer()
            analysis_content = await self._execute_compatibility_llm_analysis(prompt, analysis_dimension)

            if not analysis_content:
                logger.error(f"❌ LLM分析失败: 返回内容为空")
                return {
                    "success": False,
                    "error": "LLM分析返回内容为空",
                    "analysis_type": analysis_dimension
                }

            logger.info(f"✅ LLM分析完成，内容长度: {len(analysis_content)}字符")

            # 3. 验证分析结果
            validator = ResultValidator()
            validation_result = validator.validate_analysis_result(
                analysis_content,
                analysis_dimension,
                is_compatibility=True  # 标记为合盘分析
            )

            # 4. 构建最终结果
            final_result = {
                "success": True,
                "analysis_type": analysis_dimension,
                "content": analysis_content,
                "validation_report": validation_result,
                "timestamp": datetime.now().isoformat(),
                "prompt_length": len(prompt),
                "is_compatibility": True
            }

            logger.info(f"✅ 合盘分析维度 {analysis_dimension} 完成")
            return final_result

        except Exception as e:
            logger.error(f"❌ 合盘分析维度 {analysis_dimension} 失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis_type": analysis_dimension
            }

    async def _execute_compatibility_llm_analysis(self, prompt: str, analysis_dimension: str) -> str:
        """执行合盘专用的LLM分析（高token限制）"""
        try:
            from core.nlu.llm_client import LLMClient
            import asyncio

            llm_client = LLMClient()

            # 合盘分析专用的强化提示词
            enhanced_prompt = f"""
{prompt}

【重要】这是合盘分析任务，必须严格按照以下要求执行：

🚨 【强制要求】
1. 必须生成3000字以上的详细分析内容
2. 禁止生成"因篇幅限制"、"精简框架"等简化说明
3. 必须包含完整的6个章节内容
4. 每个章节都要详细展开，不能省略
5. 必须包含具体的数据、时间、建议

🎯 【输出标准】
- 最少3000字，目标4000-5000字
- 包含具体的年份预测（2024-2030年）
- 包含具体的数值评估（百分比、评分）
- 包含可操作的实用建议
- 包含详细的风险预警

请立即开始生成完整的3000字以上合盘分析报告：
"""

            messages = [{"role": "user", "content": enhanced_prompt}]

            # 多次重试，在API限制内优化
            token_limits = [6000, 8000, 8192]  # 最大不超过API限制

            for attempt, max_tokens in enumerate(token_limits, 1):
                try:
                    logger.info(f"🔄 合盘分析第{attempt}次尝试，max_tokens: {max_tokens}")

                    result = await asyncio.wait_for(
                        asyncio.to_thread(
                            llm_client.chat_completion,
                            messages,
                            temperature=0.1,  # 极低温度确保详细度
                            max_tokens=max_tokens
                        ),
                        timeout=300  # 5分钟超时
                    )

                    if result and len(result) >= 2000:  # 降低要求到2000字符
                        logger.info(f"✅ 合盘分析成功: {len(result)}字符")
                        return result
                    else:
                        logger.warning(f"⚠️ 第{attempt}次尝试结果过短: {len(result) if result else 0}字符")

                        # 如果结果过短，修改提示词强调
                        if attempt < len(token_limits):
                            enhanced_prompt += f"\n\n【再次强调】上次生成的内容只有{len(result) if result else 0}字符，不够详细！请生成更完整的分析内容，包含更多具体细节和实用建议！"
                            messages = [{"role": "user", "content": enhanced_prompt}]

                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ 第{attempt}次尝试超时")
                except Exception as e:
                    logger.warning(f"⚠️ 第{attempt}次尝试异常: {e}")

                # 等待后重试
                if attempt < len(token_limits):
                    await asyncio.sleep(3)

            # 如果所有尝试都失败，返回错误信息
            logger.error("❌ 合盘分析所有尝试都失败")
            return "合盘分析生成失败，请稍后重试。"

        except Exception as e:
            logger.error(f"❌ 合盘LLM分析异常: {e}")
            return f"合盘分析异常: {str(e)}"

    def _merge_analysis_data(self, compatibility_data: Dict[str, Any], analysis_dimension: str) -> Dict[str, Any]:
        """合并两人的分析数据用于合盘分析"""
        person_a_data = compatibility_data["person_a"]["analysis"]
        person_b_data = compatibility_data["person_b"]["analysis"]

        # 构建合盘专用的数据结构
        merged_data = {
            "success": True,
            "analysis_type": "compatibility_analysis",
            "compatibility_dimension": analysis_dimension,
            "relationship_type": compatibility_data["relationship_type"],
            "person_a": {
                "info": compatibility_data["person_a"]["info"],
                "ziwei_analysis": person_a_data.get("ziwei_analysis", {}),
                "bazi_analysis": person_a_data.get("bazi_analysis", {})
            },
            "person_b": {
                "info": compatibility_data["person_b"]["info"],
                "ziwei_analysis": person_b_data.get("ziwei_analysis", {}),
                "bazi_analysis": person_b_data.get("bazi_analysis", {})
            }
        }

        return merged_data

    def _merge_birth_info(self, compatibility_data: Dict[str, Any]) -> Dict[str, Any]:
        """合并两人的出生信息"""
        person_a_info = compatibility_data["person_a"]["info"]
        person_b_info = compatibility_data["person_b"]["info"]

        merged_info = {
            "analysis_type": "compatibility_analysis",
            "relationship_type": compatibility_data["relationship_type"],
            "person_a": person_a_info,
            "person_b": person_b_info
        }

        return merged_info
