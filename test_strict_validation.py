#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试严格验证 - 确保紫薇斗数+八字都成功才继续分析
"""

def test_normal_case():
    """测试正常情况 - 两个算法都成功"""
    print("✅ 测试1：正常情况 - 紫薇斗数+八字都成功")
    print("=" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from algorithms.real_bazi_calculator import RealBaziCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        bazi_calc = RealBaziCalculator()
        
        # 定义简单的API函数
        def test_chat_api(prompt: str) -> str:
            return "测试分析结果"
        
        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=test_chat_api
        )
        
        # 测试数据
        birth_info = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试数据: {birth_info}")
        
        # 测试综合分析
        result = engine._call_comprehensive_api(birth_info)
        
        if result.get("success"):
            results = result.get("results", {})
            print(f"✅ 综合分析成功，包含: {list(results.keys())}")
            
            if len(results) == 2 and "ziwei" in results and "bazi" in results:
                print("✅ 包含完整的紫薇斗数+八字数据")
                return True
            else:
                print(f"❌ 数据不完整: {list(results.keys())}")
                return False
        else:
            print(f"❌ 综合分析失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ziwei_failure():
    """测试紫薇斗数失败的情况"""
    print("\n❌ 测试2：紫薇斗数失败情况")
    print("=" * 50)
    
    try:
        from algorithms.real_bazi_calculator import RealBaziCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建一个会失败的紫薇算法模拟
        class FailingZiweiCalculator:
            def calculate_chart(self, year, month, day, hour, gender):
                return {"error": "紫薇算法模拟失败"}
        
        # 创建算法实例
        ziwei_calc = FailingZiweiCalculator()
        bazi_calc = RealBaziCalculator()
        
        # 定义简单的API函数
        def test_chat_api(prompt: str) -> str:
            return "测试分析结果"
        
        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=test_chat_api
        )
        
        # 测试数据
        birth_info = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试数据: {birth_info}")
        
        # 测试综合分析
        result = engine._call_comprehensive_api(birth_info)
        
        if not result.get("success"):
            error_msg = result.get("error", "")
            print(f"✅ 正确阻止了分析: {error_msg}")
            if "紫薇斗数" in error_msg:
                print("✅ 错误信息包含紫薇斗数失败信息")
                return True
            else:
                print("❌ 错误信息不够明确")
                return False
        else:
            print("❌ 应该失败但却成功了")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bazi_failure():
    """测试八字失败的情况"""
    print("\n❌ 测试3：八字算命失败情况")
    print("=" * 50)
    
    try:
        from algorithms.real_ziwei_calculator import RealZiweiCalculator
        from core.fortune_engine import FortuneEngine
        
        # 创建一个会失败的八字算法模拟
        class FailingBaziCalculator:
            def calculate_bazi(self, year, month, day, hour, minute, gender):
                return {"error": "八字算法模拟失败"}
        
        # 创建算法实例
        ziwei_calc = RealZiweiCalculator()
        bazi_calc = FailingBaziCalculator()
        
        # 定义简单的API函数
        def test_chat_api(prompt: str) -> str:
            return "测试分析结果"
        
        # 创建引擎实例
        engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=None,
            chat_api_func=test_chat_api
        )
        
        # 测试数据
        birth_info = {
            "year": 1985,
            "month": 4,
            "day": 23,
            "hour": 22,
            "gender": "女"
        }
        
        print(f"📊 测试数据: {birth_info}")
        
        # 测试综合分析
        result = engine._call_comprehensive_api(birth_info)
        
        if not result.get("success"):
            error_msg = result.get("error", "")
            print(f"✅ 正确阻止了分析: {error_msg}")
            if "八字算命" in error_msg:
                print("✅ 错误信息包含八字算命失败信息")
                return True
            else:
                print("❌ 错误信息不够明确")
                return False
        else:
            print("❌ 应该失败但却成功了")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_analysis_validation():
    """测试AI分析的验证逻辑"""
    print("\n🤖 测试4：AI分析验证逻辑")
    print("=" * 50)
    
    try:
        from core.fortune_engine import FortuneEngine
        
        # 定义简单的API函数
        def test_chat_api(prompt: str) -> str:
            return "测试分析结果"
        
        # 创建引擎实例
        engine = FortuneEngine(chat_api_func=test_chat_api)
        
        # 测试1: 算法失败的情况
        failed_result = {
            "success": False,
            "error": "紫薇斗数算法失败: 测试错误"
        }
        
        analysis = engine.generate_ai_analysis(failed_result, "测试问题", "general")
        
        if "❌ 算命分析失败" in analysis:
            print("✅ 算法失败时正确阻止分析")
        else:
            print("❌ 算法失败时未正确阻止分析")
            return False
        
        # 测试2: 数据不完整的情况
        incomplete_result = {
            "success": True,
            "type": "comprehensive",
            "results": {
                "bazi": {"success": True, "data": {}}
                # 缺少ziwei数据
            }
        }
        
        analysis = engine.generate_ai_analysis(incomplete_result, "测试问题", "general")
        
        if "❌ 算命分析数据不完整" in analysis:
            print("✅ 数据不完整时正确阻止分析")
        else:
            print("❌ 数据不完整时未正确阻止分析")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_validation_rules():
    """显示验证规则"""
    print("\n📋 严格验证规则")
    print("=" * 30)
    
    print("🔒 **必须满足的条件:**")
    print("  1. 紫薇斗数算法必须成功")
    print("  2. 八字命理算法必须成功")
    print("  3. 两套数据都完整才能继续分析")
    print()
    
    print("❌ **阻止分析的情况:**")
    print("  - 紫薇斗数算法失败")
    print("  - 八字命理算法失败")
    print("  - 任何一套数据缺失")
    print("  - 数据格式不正确")
    print()
    
    print("✅ **用户友好的错误提示:**")
    print("  - 明确指出哪个算法失败")
    print("  - 提供具体的解决建议")
    print("  - 说明系统要求和原因")
    print("  - 引导用户正确操作")
    print()
    
    print("🎯 **确保分析准确性:**")
    print("  - 不允许部分数据分析")
    print("  - 避免不准确的结果")
    print("  - 维护系统专业性")
    print("  - 保证用户体验质量")

def main():
    """主测试函数"""
    print("🔒 严格验证测试 - 确保紫薇斗数+八字都成功")
    print("=" * 60)
    
    # 测试1: 正常情况
    normal_success = test_normal_case()
    
    # 测试2: 紫薇失败
    ziwei_failure_success = test_ziwei_failure()
    
    # 测试3: 八字失败
    bazi_failure_success = test_bazi_failure()
    
    # 测试4: AI分析验证
    ai_validation_success = test_ai_analysis_validation()
    
    # 显示验证规则
    show_validation_rules()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 严格验证测试结果:")
    print(f"  正常情况测试: {'✅ 通过' if normal_success else '❌ 失败'}")
    print(f"  紫薇失败测试: {'✅ 通过' if ziwei_failure_success else '❌ 失败'}")
    print(f"  八字失败测试: {'✅ 通过' if bazi_failure_success else '❌ 失败'}")
    print(f"  AI验证测试: {'✅ 通过' if ai_validation_success else '❌ 失败'}")
    
    all_success = all([normal_success, ziwei_failure_success, bazi_failure_success, ai_validation_success])
    
    if all_success:
        print("\n🎊 所有验证测试通过！")
        print("\n📝 验证机制已完善:")
        print("  1. ✅ 紫薇斗数+八字必须都成功")
        print("  2. ✅ 任何算法失败都会阻止分析")
        print("  3. ✅ 提供清晰的错误信息")
        print("  4. ✅ 用户友好的错误提示")
        print("  5. ✅ 确保分析结果准确性")
        
        print("\n🎯 现在的系统特点:")
        print("  - 严格的数据验证")
        print("  - 准确的分析结果")
        print("  - 专业的错误处理")
        print("  - 用户友好的提示")
        
        print("\n💡 **重要提醒:**")
        print("  系统现在会严格要求紫薇斗数+八字都成功")
        print("  任何算法失败都会停止分析并显示明确错误")
        print("  这确保了分析结果的准确性和专业性")
    else:
        print("\n⚠️ 部分验证测试失败，需要进一步完善")

if __name__ == "__main__":
    main()
