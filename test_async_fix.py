#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的异步处理功能
"""

import asyncio
import time
import threading

def test_file_based_status():
    """测试基于文件的状态传递"""
    print("🔧 测试基于文件的状态传递")
    print("=" * 50)
    
    try:
        import json
        import os
        import glob
        
        # 模拟后台线程写入状态文件
        def background_task(angle_key, result_id):
            time.sleep(2)  # 模拟耗时操作
            
            # 写入状态文件
            status_file = f"temp_status_{angle_key}_{result_id}.json"
            status_data = {
                "status": "completed",
                "end_time": time.time(),
                "angle_name": "测试分析"
            }
            
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f)
            
            print(f"✅ 后台任务完成，状态文件已写入: {status_file}")
        
        # 模拟主线程检查状态文件
        def check_status_files():
            status_files = glob.glob("temp_status_*.json")
            
            for status_file in status_files:
                try:
                    # 从文件名提取信息
                    filename = os.path.basename(status_file)
                    if filename.startswith("temp_status_") and filename.endswith(".json"):
                        parts = filename[12:-5].split("_")
                        if len(parts) >= 2:
                            angle_key = parts[0]
                            
                            # 读取状态文件
                            with open(status_file, 'r', encoding='utf-8') as f:
                                status_data = json.load(f)
                            
                            print(f"📊 检测到状态更新: {angle_key} -> {status_data['status']}")
                            
                            # 删除临时文件
                            os.remove(status_file)
                            return True
                            
                except Exception as e:
                    print(f"处理状态文件时出错: {e}")
                    try:
                        os.remove(status_file)
                    except:
                        pass
            
            return False
        
        # 启动后台任务
        angle_key = "personality_destiny"
        result_id = "test_result_123"
        
        thread = threading.Thread(target=background_task, args=(angle_key, result_id), daemon=True)
        thread.start()
        
        print(f"🚀 后台任务已启动: {angle_key}")
        
        # 主线程检查状态
        max_checks = 10
        check_count = 0
        
        while check_count < max_checks:
            time.sleep(0.5)
            check_count += 1
            
            if check_status_files():
                print(f"✅ 状态检查成功，检查次数: {check_count}")
                return True
            
            print(f"⏳ 检查状态中... ({check_count}/{max_checks})")
        
        print("❌ 状态检查超时")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_concurrent_tasks():
    """测试并发任务处理"""
    print(f"\n🔧 测试并发任务处理")
    print("=" * 30)
    
    try:
        import json
        import os
        import glob
        
        # 模拟多个后台任务
        def background_task(angle_key, result_id, delay):
            time.sleep(delay)
            
            status_file = f"temp_status_{angle_key}_{result_id}.json"
            status_data = {
                "status": "completed",
                "end_time": time.time(),
                "angle_name": f"{angle_key}分析",
                "delay": delay
            }
            
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f)
            
            print(f"✅ 任务完成: {angle_key} (耗时{delay}秒)")
        
        # 启动多个任务
        tasks = [
            ("personality_destiny", "test_123", 1),
            ("wealth_fortune", "test_123", 2),
            ("marriage_love", "test_123", 1.5)
        ]
        
        threads = []
        for angle_key, result_id, delay in tasks:
            thread = threading.Thread(target=background_task, args=(angle_key, result_id, delay), daemon=True)
            thread.start()
            threads.append(thread)
            print(f"🚀 启动任务: {angle_key}")
        
        # 检查状态
        completed_tasks = []
        max_checks = 20
        check_count = 0
        
        while len(completed_tasks) < len(tasks) and check_count < max_checks:
            time.sleep(0.3)
            check_count += 1
            
            status_files = glob.glob("temp_status_*.json")
            
            for status_file in status_files:
                try:
                    filename = os.path.basename(status_file)
                    if filename.startswith("temp_status_") and filename.endswith(".json"):
                        parts = filename[12:-5].split("_")
                        if len(parts) >= 2:
                            angle_key = parts[0]
                            
                            if angle_key not in completed_tasks:
                                with open(status_file, 'r', encoding='utf-8') as f:
                                    status_data = json.load(f)
                                
                                print(f"📊 任务完成: {angle_key} -> {status_data['status']}")
                                completed_tasks.append(angle_key)
                                
                                os.remove(status_file)
                                
                except Exception as e:
                    print(f"处理状态文件时出错: {e}")
                    try:
                        os.remove(status_file)
                    except:
                        pass
            
            if len(completed_tasks) < len(tasks):
                print(f"⏳ 等待任务完成... ({len(completed_tasks)}/{len(tasks)})")
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=1)
        
        if len(completed_tasks) == len(tasks):
            print(f"✅ 所有并发任务完成: {completed_tasks}")
            return True
        else:
            print(f"❌ 部分任务未完成: {len(completed_tasks)}/{len(tasks)}")
            return False
        
    except Exception as e:
        print(f"❌ 并发测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_temp_files():
    """清理临时文件"""
    try:
        import glob
        import os
        
        temp_files = glob.glob("temp_status_*.json")
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"🗑️ 清理临时文件: {temp_file}")
            except:
                pass
        
        if temp_files:
            print(f"✅ 清理了 {len(temp_files)} 个临时文件")
        else:
            print("✅ 没有需要清理的临时文件")
            
    except Exception as e:
        print(f"⚠️ 清理临时文件失败: {e}")

def main():
    """主函数"""
    print("🎯 异步处理修复验证")
    print("=" * 60)
    
    # 清理之前的临时文件
    cleanup_temp_files()
    
    # 1. 测试基于文件的状态传递
    file_status_success = test_file_based_status()
    
    # 2. 测试并发任务处理
    concurrent_success = test_concurrent_tasks()
    
    # 清理测试产生的临时文件
    cleanup_temp_files()
    
    print("\n" + "=" * 60)
    print("🎯 异步处理修复验证结果:")
    
    if file_status_success:
        print("✅ 基于文件的状态传递正常")
    else:
        print("❌ 基于文件的状态传递异常")
    
    if concurrent_success:
        print("✅ 并发任务处理正常")
    else:
        print("❌ 并发任务处理异常")
    
    if file_status_success and concurrent_success:
        print("\n🎉 🎉 🎉 异步处理修复成功！🎉 🎉 🎉")
        print("💡 修复成果:")
        print("  1. ✅ 后台线程不再直接访问session_state")
        print("  2. ✅ 使用临时文件传递状态信息")
        print("  3. ✅ 支持多个并发任务")
        print("  4. ✅ 自动清理临时文件")
        print("  5. ✅ 错误处理机制完善")
        print("\n🚀 现在Web界面的异步生成功能应该完全正常！")
        print("   不会再出现session_state访问错误")
    else:
        print("\n⚠️ 异步处理还有问题需要修复")

if __name__ == "__main__":
    main()
