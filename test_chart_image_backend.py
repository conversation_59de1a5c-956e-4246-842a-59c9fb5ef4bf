#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后台Agent排盘图片功能测试
"""

import asyncio
import sys
import time
import os
sys.path.append('.')

async def test_backend_chart_generation():
    print('🎨 后台Agent排盘图片功能测试...')

    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry

        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()

        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)

        session_id = 'test_chart_001'

        print('\n🎯 测试场景：完整的排盘图片生成流程')

        # 第一步：提供完整信息，启动后台分析
        print('\n📝 第一步：提供完整生辰信息')
        result1 = await coordinator.handle_user_message(
            session_id,
            '我是1988年6月1日午时出生的男命，想算紫薇斗数'
        )

        print(f'✅ 第一步响应: {result1.get("success")}')
        print(f'响应预览: {result1.get("response", "")[:150]}...')

        # 等待后台分析启动和第一个角度完成
        print('\n⏳ 等待30秒让后台分析和图片生成完成...')
        time.sleep(30)

        # 检查会话状态
        session_state = master_agent.get_session_state(session_id)
        result_id = session_state.get("result_id")

        print(f'\n📊 会话状态检查:')
        print(f'  阶段: {session_state.get("stage")}')
        print(f'  result_id: {result_id[:8] if result_id else "None"}...')

        cached_result = None
        if result_id:
            # 检查缓存中的结果
            cached_result = master_agent.cache.get_result(result_id)
            if cached_result:
                print(f'  缓存结果存在: ✅')
                print(f'  图片路径: {cached_result.chart_image_path}')

                # 检查图片文件是否存在
                if cached_result.chart_image_path:
                    if os.path.exists(cached_result.chart_image_path):
                        print(f'  图片文件存在: ✅')
                        print(f'  图片大小: {os.path.getsize(cached_result.chart_image_path)} bytes')
                    else:
                        print(f'  图片文件不存在: ❌')
                else:
                    print(f'  图片路径为空: ❌')
            else:
                print(f'  缓存结果不存在: ❌')
        else:
            print(f'  result_id为空，后台分析还在进行中')

        # 第二步：询问命宫，看是否包含图片
        print('\n🏛️ 第二步：询问命宫特点（应该包含图片）')
        result2 = await coordinator.handle_user_message(session_id, '我的命宫有什么特点？')

        print(f'✅ 第二步响应: {result2.get("success")}')
        response2 = result2.get("response", "")
        print(f'响应长度: {len(response2)}字')

        # 检查是否包含图片信息
        if "图片已生成:" in response2:
            print('🎨 ✅ 回答中包含排盘图片')
            # 提取图片路径
            import re
            image_matches = re.findall(r'图片已生成: (.+)', response2)
            if image_matches:
                image_path = image_matches[0].strip()
                print(f'  图片路径: {image_path}')

                # 检查图片文件
                if os.path.exists(image_path):
                    print(f'  图片文件存在: ✅')
                    print(f'  图片大小: {os.path.getsize(image_path)} bytes')
                else:
                    print(f'  图片文件不存在: ❌')
        else:
            print('🎨 ❌ 回答中不包含排盘图片')

        # 第三步：测试图片生成功能
        print('\n🔧 第三步：直接测试图片生成功能')

        try:
            # 直接调用后台Agent的图片生成方法
            birth_info = session_state.get("birth_info", {})
            calculation_type = session_state.get("calculation_type", "ziwei")

            # 模拟计算结果
            mock_calculation_result = {
                "data": {
                    "palaces": {
                        "命宫": {"position": "午", "major_stars": ["紫微", "天府"], "minor_stars": ["左辅", "右弼"]},
                        "兄弟宫": {"position": "未", "major_stars": ["太阳"], "minor_stars": []},
                        "夫妻宫": {"position": "申", "major_stars": ["武曲", "七杀"], "minor_stars": []},
                    },
                    "birth_info": birth_info
                }
            }

            image_path = await calculator_agent._generate_chart_image(
                mock_calculation_result, birth_info, calculation_type
            )

            if image_path:
                print(f'✅ 直接图片生成成功: {image_path}')
                if os.path.exists(image_path):
                    print(f'  图片文件存在: ✅')
                    print(f'  图片大小: {os.path.getsize(image_path)} bytes')
                else:
                    print(f'  图片文件不存在: ❌')
            else:
                print('❌ 直接图片生成失败')

        except Exception as e:
            print(f'❌ 直接图片生成异常: {e}')

        # 检查charts目录
        print('\n📁 检查charts目录:')
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            files = os.listdir(charts_dir)
            png_files = [f for f in files if f.endswith('.png')]
            print(f'  charts目录存在: ✅')
            print(f'  PNG文件数量: {len(png_files)}')
            if png_files:
                print(f'  最新文件: {png_files[-1]}')
        else:
            print(f'  charts目录不存在: ❌')

        print('\n📊 排盘图片功能测试总结:')

        # 功能检查清单
        checks = [
            ("后台分析启动", result_id is not None),
            ("缓存结果保存", cached_result is not None if result_id else False),
            ("图片路径保存", cached_result.chart_image_path is not None if cached_result else False),
            ("图片文件生成", os.path.exists(cached_result.chart_image_path) if cached_result and cached_result.chart_image_path else False),
            ("前端图片显示", "图片已生成:" in response2),
            ("charts目录存在", os.path.exists("charts"))
        ]

        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f'  {status} {check_name}')

        # 计算成功率
        success_count = sum(1 for _, result in checks if result)
        total_count = len(checks)
        success_rate = success_count / total_count * 100

        print(f'\n🎯 功能完成度: {success_count}/{total_count} ({success_rate:.1f}%)')

        if success_rate >= 80:
            print('🎉 排盘图片功能基本正常！')
        elif success_rate >= 50:
            print('⚠️ 排盘图片功能部分正常，需要优化')
        else:
            print('❌ 排盘图片功能存在严重问题，需要修复')

        print('\n🎉 排盘图片功能测试完成！')

    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_backend_chart_generation())
