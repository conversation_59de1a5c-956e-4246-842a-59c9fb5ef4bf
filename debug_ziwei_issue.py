#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试紫薇斗数问题
"""

def debug_ziwei_calculator():
    """调试紫薇斗数计算器"""
    print("🔍 调试紫薇斗数计算器")
    print("=" * 50)
    
    try:
        # 导入紫薇斗数算法
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'algorithms'))
        from real_ziwei_calculator import RealZiweiCalculator
        
        calc = RealZiweiCalculator()
        print("✅ 紫薇斗数算法导入成功")
        
        # 检查可用方法
        methods = [method for method in dir(calc) if not method.startswith('_')]
        print(f"📋 可用方法: {methods}")
        
        # 测试不同的调用方式
        test_data = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"\n📊 测试数据: {test_data}")
        
        # 方式1: calculate_chart
        print(f"\n🧪 测试方式1: calculate_chart")
        try:
            result1 = calc.calculate_chart(test_data["year"], test_data["month"], 
                                         test_data["day"], test_data["hour"], test_data["gender"])
            print(f"  结果: {type(result1)}")
            print(f"  成功: {result1.get('success', False) if isinstance(result1, dict) else 'Not dict'}")
            if isinstance(result1, dict):
                print(f"  键: {list(result1.keys())}")
                if not result1.get("success", False):
                    print(f"  错误: {result1.get('error', '未知错误')}")
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        # 方式2: get_palace_analysis
        print(f"\n🧪 测试方式2: get_palace_analysis")
        try:
            result2 = calc.get_palace_analysis()
            print(f"  结果: {type(result2)}")
            print(f"  内容: {result2}")
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        # 检查算法内部状态
        print(f"\n🔍 检查算法内部状态:")
        if hasattr(calc, 'astrolabe'):
            print(f"  astrolabe: {calc.astrolabe}")
        if hasattr(calc, 'iztro_available'):
            print(f"  iztro_available: {calc.iztro_available}")
        
        return calc
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_direct_iztro():
    """直接测试py-iztro"""
    print(f"\n🔍 直接测试py-iztro")
    print("=" * 50)
    
    try:
        import py_iztro
        
        astro = py_iztro.Astro()
        print("✅ py-iztro导入成功")
        
        # 测试紫薇斗数排盘
        astrolabe = astro.by_solar("1988-6-1", 6, "男", "zh-CN")
        print("✅ 紫薇斗数排盘成功")
        
        # 检查结果
        print(f"📊 排盘结果:")
        print(f"  农历: {astrolabe.lunar_date}")
        print(f"  八字: {astrolabe.chinese_date}")
        print(f"  生肖: {astrolabe.zodiac}")
        print(f"  星座: {astrolabe.sign}")
        
        # 检查宫位信息
        if hasattr(astrolabe, 'palaces'):
            print(f"  宫位: {len(astrolabe.palaces)}个")
        else:
            print(f"  ❌ 没有宫位信息")
        
        # 检查可用属性
        attrs = [attr for attr in dir(astrolabe) if not attr.startswith('_')]
        print(f"  可用属性: {len(attrs)}个")
        print(f"  主要属性: {attrs[:10]}")
        
        return astrolabe
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_ziwei_algorithm_file():
    """检查紫薇斗数算法文件"""
    print(f"\n🔍 检查紫薇斗数算法文件")
    print("=" * 50)
    
    try:
        import os
        
        # 检查文件是否存在
        ziwei_file = os.path.join("algorithms", "real_ziwei_calculator.py")
        if os.path.exists(ziwei_file):
            print(f"✅ 文件存在: {ziwei_file}")
            
            # 读取文件内容
            with open(ziwei_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📊 文件信息:")
            print(f"  文件大小: {len(content)}字符")
            print(f"  行数: {len(content.splitlines())}行")
            
            # 检查关键方法
            if "calculate_chart" in content:
                print(f"  ✅ 包含calculate_chart方法")
            else:
                print(f"  ❌ 缺少calculate_chart方法")
            
            if "get_palace_analysis" in content:
                print(f"  ✅ 包含get_palace_analysis方法")
            else:
                print(f"  ❌ 缺少get_palace_analysis方法")
            
            # 检查py-iztro导入
            if "py_iztro" in content:
                print(f"  ✅ 包含py-iztro导入")
            else:
                print(f"  ❌ 缺少py-iztro导入")
            
        else:
            print(f"❌ 文件不存在: {ziwei_file}")
            
            # 列出algorithms目录内容
            algorithms_dir = "algorithms"
            if os.path.exists(algorithms_dir):
                files = os.listdir(algorithms_dir)
                print(f"📁 algorithms目录内容: {files}")
            else:
                print(f"❌ algorithms目录不存在")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🧪 紫薇斗数问题全面调试")
    print("=" * 80)
    
    # 1. 检查算法文件
    check_ziwei_algorithm_file()
    
    # 2. 直接测试py-iztro
    astrolabe = test_direct_iztro()
    
    # 3. 调试紫薇斗数计算器
    calc = debug_ziwei_calculator()
    
    print("\n" + "=" * 80)
    print("🎯 调试总结:")
    
    if astrolabe:
        print("✅ py-iztro核心功能正常")
    else:
        print("❌ py-iztro核心功能异常")
    
    if calc:
        print("✅ 紫薇斗数算法可导入")
    else:
        print("❌ 紫薇斗数算法导入失败")
    
    print("\n💡 下一步:")
    print("1. 确保py-iztro正常工作")
    print("2. 修复紫薇斗数算法的接口问题")
    print("3. 重新测试融合系统")

if __name__ == "__main__":
    main()
