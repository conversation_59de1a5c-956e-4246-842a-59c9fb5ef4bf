#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天专用知识库 - 将排盘和分析结果转换为结构化知识
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class KnowledgeItem:
    """知识项"""
    category: str  # 类别：basic_info, ziwei_palace, bazi_pillar, analysis_result
    key: str       # 键名
    value: str     # 值
    confidence: float = 1.0  # 置信度
    source: str = ""  # 来源

class ChatKnowledgeBase:
    """聊天专用知识库"""

    def __init__(self):
        """初始化知识库"""
        self.knowledge_items: List[KnowledgeItem] = []
        self.categories = {
            "basic_info": "基本信息",
            "ziwei_palace": "紫薇宫位",
            "bazi_pillar": "八字四柱",
            "bazi_elements": "五行分析",
            "bazi_dayun": "大运流年",
            "analysis_result": "分析结果",
            "chart_info": "图表信息",
            "temporal_info": "时间信息",
            "system_info": "系统信息"
        }

    def clear(self):
        """清空知识库"""
        self.knowledge_items.clear()

    def add_knowledge(self, category: str, key: str, value: str,
                     confidence: float = 1.0, source: str = ""):
        """添加知识项"""
        if not value or not value.strip():
            return

        item = KnowledgeItem(
            category=category,
            key=key,
            value=str(value).strip(),
            confidence=confidence,
            source=source
        )
        self.knowledge_items.append(item)

    def load_from_cache_result(self, cached_result):
        """从缓存结果加载知识 - 增强版"""
        try:
            self.clear()

            # 1. 加载基本信息
            birth_info = cached_result.birth_info
            self._load_basic_info(birth_info)

            # 2. 加载排盘数据
            raw_calculation = cached_result.raw_calculation
            self._load_ziwei_knowledge(raw_calculation)
            self._load_bazi_knowledge(raw_calculation)

            # 3. 加载分析结果
            detailed_analysis = cached_result.detailed_analysis
            self._load_analysis_knowledge(detailed_analysis)

            # 4. 加载图表信息
            self._load_chart_knowledge(cached_result)

            # 5. 加载系统信息
            self._load_system_knowledge(raw_calculation)

            # 6. 加载时间信息
            self._load_temporal_knowledge(birth_info)

            logger.info(f"知识库加载完成，共 {len(self.knowledge_items)} 个知识项")

        except Exception as e:
            logger.error(f"知识库加载失败: {e}")

    def _load_basic_info(self, birth_info):
        """加载基本信息"""
        self.add_knowledge("basic_info", "出生年份", birth_info.get('year', ''), source="birth_info")
        self.add_knowledge("basic_info", "出生月份", birth_info.get('month', ''), source="birth_info")
        self.add_knowledge("basic_info", "出生日期", birth_info.get('day', ''), source="birth_info")
        self.add_knowledge("basic_info", "出生时辰", birth_info.get('hour', ''), source="birth_info")
        self.add_knowledge("basic_info", "性别", birth_info.get('gender', ''), source="birth_info")

    def _load_ziwei_knowledge(self, raw_calculation: Dict[str, Any]):
        """加载紫薇斗数知识"""
        try:
            # 尝试多种可能的数据结构
            ziwei_data = None

            # 方式1: 直接从ziwei字段获取
            if 'ziwei' in raw_calculation:
                ziwei_data = raw_calculation['ziwei']
            # 方式2: 从ziwei_analysis字段获取
            elif 'ziwei_analysis' in raw_calculation:
                ziwei_data = raw_calculation['ziwei_analysis']

            if not ziwei_data:
                return

            # 提取宫位信息
            palaces = ziwei_data.get('palaces', {}) or ziwei_data.get('宫位', {})

            if palaces:
                important_palaces = [
                    ("命宫", "personality_core"),
                    ("财帛宫", "wealth_palace"),
                    ("夫妻宫", "marriage_palace"),
                    ("事业宫", "career_palace"),
                    ("迁移宫", "travel_palace"),
                    ("疾厄宫", "health_palace"),
                    ("子女宫", "children_palace"),
                    ("奴仆宫", "friendship_palace"),
                    ("田宅宫", "property_palace"),
                    ("福德宫", "blessing_palace"),
                    ("父母宫", "parents_palace"),
                    ("兄弟宫", "siblings_palace")
                ]

                for palace_name, palace_key in important_palaces:
                    if palace_name in palaces:
                        palace_data = palaces[palace_name]
                        if isinstance(palace_data, dict):
                            # 主星
                            major_stars = palace_data.get("主星", []) or palace_data.get("major_stars", [])
                            if major_stars:
                                self.add_knowledge("ziwei_palace", f"{palace_name}_主星",
                                                 ", ".join(major_stars), source="ziwei")

                            # 辅星
                            minor_stars = palace_data.get("辅星", []) or palace_data.get("minor_stars", [])
                            if minor_stars:
                                self.add_knowledge("ziwei_palace", f"{palace_name}_辅星",
                                                 ", ".join(minor_stars[:5]), source="ziwei")  # 只取前5个

        except Exception as e:
            logger.error(f"加载紫薇知识失败: {e}")

    def _load_bazi_knowledge(self, raw_calculation: Dict[str, Any]):
        """加载八字知识"""
        try:
            # 尝试多种可能的数据结构
            bazi_data = None

            # 方式1: 直接从bazi字段获取
            if 'bazi' in raw_calculation:
                bazi_data = raw_calculation['bazi']
            # 方式2: 从bazi_analysis字段获取
            elif 'bazi_analysis' in raw_calculation:
                bazi_data = raw_calculation['bazi_analysis']
            # 方式3: 检查是否在其他字段中
            else:
                # 打印可用字段用于调试
                logger.info(f"八字数据字段: {list(raw_calculation.keys())}")
                return

            if not bazi_data:
                logger.warning("未找到八字数据")
                return

            # 提取八字信息 - 适配新的数据结构
            # 方式1: 检查是否有干支数据
            if '干支' in bazi_data:
                ganzhi_data = bazi_data['干支']
                if isinstance(ganzhi_data, dict):
                    # 提取四柱信息
                    pillar_mapping = {
                        '年柱': 'year_pillar',
                        '月柱': 'month_pillar',
                        '日柱': 'day_pillar',
                        '时柱': 'hour_pillar'
                    }

                    for chinese_name, english_key in pillar_mapping.items():
                        if chinese_name in ganzhi_data:
                            self.add_knowledge("bazi_pillar", chinese_name, ganzhi_data[chinese_name], source="bazi")

                    # 添加完整八字文本
                    if '文本' in ganzhi_data:
                        self.add_knowledge("bazi_pillar", "八字四柱", ganzhi_data['文本'], source="bazi")

                    # 提取日主（日柱的天干）
                    if '日柱' in ganzhi_data:
                        day_pillar = ganzhi_data['日柱']
                        if day_pillar and len(day_pillar) >= 1:
                            day_master = day_pillar[0]  # 日柱的第一个字是日主
                            self.add_knowledge("bazi_pillar", "日主", day_master, source="bazi")

            # 方式2: 检查是否有五行数据
            if '五行' in bazi_data:
                wuxing_data = bazi_data['五行']
                if isinstance(wuxing_data, dict):
                    # 提取五行分布
                    element_info = []
                    element_strength = []

                    for element, info in wuxing_data.items():
                        if isinstance(info, dict):
                            count = info.get('五行数', '0')
                            strength = info.get('旺衰', '')
                            element_info.append(f"{element}:{count}")
                            if strength:
                                element_strength.append(f"{element}{strength}")

                    if element_info:
                        self.add_knowledge("bazi_elements", "五行分布", ", ".join(element_info), source="bazi")

                    if element_strength:
                        self.add_knowledge("bazi_elements", "五行旺衰", ", ".join(element_strength), source="bazi")

            # 方式3: 兼容旧格式的bazi_info
            bazi_info = bazi_data.get('bazi_info', {})
            if bazi_info:
                # 四柱
                pillars = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar']
                pillar_names = ['年柱', '月柱', '日柱', '时柱']

                for pillar, name in zip(pillars, pillar_names):
                    if pillar in bazi_info:
                        self.add_knowledge("bazi_pillar", name, bazi_info[pillar], source="bazi")

                # 日主
                if 'day_master' in bazi_info:
                    self.add_knowledge("bazi_pillar", "日主", bazi_info['day_master'], source="bazi")

                # 五行分析
                elements = bazi_info.get('elements', {})
                if elements:
                    element_str = ", ".join([f"{k}:{v}" for k, v in elements.items()])
                    self.add_knowledge("bazi_elements", "五行分布", element_str, source="bazi")

                # 大运
                dayun = bazi_info.get('dayun', [])
                if dayun:
                    # 找当前大运
                    current_dayun = None
                    for dy in dayun:
                        if isinstance(dy, dict) and dy.get('start_age', 0) <= 36 <= dy.get('end_age', 100):
                            current_dayun = dy
                            break
                    if current_dayun:
                        dayun_info = f"{current_dayun.get('pillar', '')} ({current_dayun.get('start_age', '')}-{current_dayun.get('end_age', '')}岁)"
                        self.add_knowledge("bazi_dayun", "当前大运", dayun_info, source="bazi")

                # 流年
                liunian = bazi_info.get('liunian', {})
                if liunian:
                    current_year = liunian.get('2024', '') or liunian.get('2025', '')
                    if current_year:
                        self.add_knowledge("bazi_dayun", "近期流年", current_year, source="bazi")

        except Exception as e:
            logger.error(f"加载八字知识失败: {e}")

    def _load_analysis_knowledge(self, detailed_analysis: Dict[str, Any]):
        """加载分析结果知识"""
        try:
            angle_analyses = detailed_analysis.get('angle_analyses', {})

            angle_names = {
                "personality_destiny": "命宫分析",
                "wealth_fortune": "财富分析",
                "marriage_love": "婚姻分析",
                "health_wellness": "健康分析",
                "career_achievement": "事业分析",
                "children_creativity": "子女分析",
                "interpersonal_relationship": "人际分析",
                "education_learning": "学业分析",
                "family_environment": "家庭分析",
                "travel_relocation": "迁移分析",
                "spiritual_blessing": "精神分析",
                "authority_parents": "权威分析"
            }

            for angle_key, angle_name in angle_names.items():
                if angle_key in angle_analyses:
                    content = angle_analyses[angle_key]
                    if content and len(content) > 50:  # 只保存有实质内容的分析
                        # 提取关键信息（前200字作为摘要）
                        summary = content[:200] + "..." if len(content) > 200 else content
                        self.add_knowledge("analysis_result", angle_name, summary, source="analysis")

        except Exception as e:
            logger.error(f"加载分析知识失败: {e}")

    def _load_chart_knowledge(self, cached_result):
        """加载图表相关知识"""
        try:
            if hasattr(cached_result, 'chart_image_path') and cached_result.chart_image_path:
                self.add_knowledge("chart_info", "排盘图路径", cached_result.chart_image_path, source="chart")

            # 从raw_calculation中提取图表相关信息
            raw_calc = cached_result.raw_calculation

            # 紫薇相关
            if 'ziwei' in raw_calc:
                ziwei_data = raw_calc['ziwei']
                if 'zodiac' in ziwei_data:
                    self.add_knowledge("chart_info", "生肖", ziwei_data['zodiac'], source="ziwei")
                if 'sign' in ziwei_data:
                    self.add_knowledge("chart_info", "星座", ziwei_data['sign'], source="ziwei")

        except Exception as e:
            logger.error(f"图表知识加载失败: {e}")

    def _load_system_knowledge(self, raw_calculation):
        """加载系统信息"""
        try:
            if 'ziwei_confidence' in raw_calculation:
                confidence = raw_calculation['ziwei_confidence']
                self.add_knowledge("system_info", "紫薇置信度", f"{confidence:.2f}", source="system")

            if 'bazi_confidence' in raw_calculation:
                confidence = raw_calculation['bazi_confidence']
                self.add_knowledge("system_info", "八字置信度", f"{confidence:.2f}", source="system")

            if 'analysis_type' in raw_calculation:
                analysis_type = raw_calculation['analysis_type']
                self.add_knowledge("system_info", "分析类型", analysis_type, source="system")

        except Exception as e:
            logger.error(f"系统知识加载失败: {e}")

    def _load_temporal_knowledge(self, birth_info):
        """加载时间相关知识"""
        try:
            from datetime import datetime

            # 计算年龄
            birth_year = int(birth_info.get('year', 0))
            current_year = datetime.now().year
            age = current_year - birth_year

            self.add_knowledge("temporal_info", "当前年龄", f"{age}岁", source="calculated")

            # 生肖计算
            zodiac_animals = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
            zodiac_index = (birth_year - 1900) % 12
            zodiac = zodiac_animals[zodiac_index]
            self.add_knowledge("temporal_info", "生肖", zodiac, source="calculated")

            # 时辰详细信息
            hour_mapping = {
                "子时": "23:00-01:00",
                "丑时": "01:00-03:00",
                "寅时": "03:00-05:00",
                "卯时": "05:00-07:00",
                "辰时": "07:00-09:00",
                "巳时": "09:00-11:00",
                "午时": "11:00-13:00",
                "未时": "13:00-15:00",
                "申时": "15:00-17:00",
                "酉时": "17:00-19:00",
                "戌时": "19:00-21:00",
                "亥时": "21:00-23:00"
            }

            hour = birth_info.get('hour', '')
            if hour in hour_mapping:
                self.add_knowledge("temporal_info", "时辰时间", hour_mapping[hour], source="calculated")

        except Exception as e:
            logger.error(f"时间知识加载失败: {e}")

    def get_knowledge_by_category(self, category: str) -> List[KnowledgeItem]:
        """按类别获取知识"""
        return [item for item in self.knowledge_items if item.category == category]

    def search_knowledge(self, keywords: List[str]) -> List[KnowledgeItem]:
        """搜索相关知识"""
        results = []
        for item in self.knowledge_items:
            for keyword in keywords:
                if keyword in item.key or keyword in item.value:
                    results.append(item)
                    break
        return results

    def format_for_llm(self, max_length: int = 2000) -> str:
        """格式化为LLM可用的知识文本 - 优化版"""
        try:
            formatted_text = "【专属知识库 - 请充分使用以下信息回答】\n"

            # 按重要性排序类别
            priority_categories = [
                ("basic_info", "基本信息"),
                ("ziwei_palace", "紫薇宫位"),
                ("bazi_pillar", "八字四柱"),
                ("bazi_elements", "五行分析"),
                ("analysis_result", "已有分析"),
                ("temporal_info", "时间信息"),
                ("chart_info", "图表信息"),
                ("bazi_dayun", "大运流年"),
                ("system_info", "系统信息")
            ]

            # 按优先级组织知识
            for category_key, category_name in priority_categories:
                items = self.get_knowledge_by_category(category_key)
                if items:
                    formatted_text += f"\n【{category_name}】\n"
                    for item in items:
                        # 强调重要信息
                        if category_key in ['ziwei_palace', 'bazi_pillar', 'bazi_elements']:
                            formatted_text += f"★ {item.key}: {item.value}\n"
                        else:
                            formatted_text += f"- {item.key}: {item.value}\n"

            # 添加使用提示
            formatted_text += "\n【重要提示】\n"
            formatted_text += "- 请在回答中引用上述具体信息\n"
            formatted_text += "- 特别关注★标记的核心命理配置\n"
            formatted_text += "- 结合紫薇宫位和八字五行进行综合分析\n"

            # 如果超长，智能截断
            if len(formatted_text) > max_length:
                # 保留重要类别，截断次要信息
                essential_text = "【专属知识库 - 请充分使用以下信息回答】\n"
                for category_key, category_name in priority_categories[:5]:  # 只保留前5个重要类别
                    items = self.get_knowledge_by_category(category_key)
                    if items:
                        essential_text += f"\n【{category_name}】\n"
                        for item in items:
                            if category_key in ['ziwei_palace', 'bazi_pillar', 'bazi_elements']:
                                essential_text += f"★ {item.key}: {item.value}\n"
                            else:
                                essential_text += f"- {item.key}: {item.value}\n"

                essential_text += "\n【重要提示】请在回答中引用上述具体信息\n"
                formatted_text = essential_text

            return formatted_text

        except Exception as e:
            logger.error(f"格式化知识库失败: {e}")
            return "【专属知识库】\n知识库加载失败"

    def get_stats(self) -> Dict[str, Any]:
        """获取知识库统计"""
        stats = {"total_items": len(self.knowledge_items)}

        for category in self.categories.keys():
            count = len(self.get_knowledge_by_category(category))
            stats[f"{category}_count"] = count

        return stats
