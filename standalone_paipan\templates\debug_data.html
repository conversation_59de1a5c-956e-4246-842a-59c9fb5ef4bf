<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据调试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 排盘数据调试页面</h1>
        
        <div class="debug-section">
            <div class="debug-title">📦 SessionStorage 原始数据</div>
            <pre id="rawData">加载中...</pre>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">📊 解析后的数据结构</div>
            <pre id="parsedData">加载中...</pre>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🏛️ 紫薇斗数宫位数据</div>
            <pre id="palaceData">加载中...</pre>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🎯 八字数据</div>
            <pre id="baziData">加载中...</pre>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">ℹ️ 出生信息</div>
            <pre id="birthData">加载中...</pre>
        </div>
        
        <div style="margin-top: 20px;">
            <a href="/" class="btn btn-primary">返回首页</a>
            <a href="/result" class="btn btn-success">返回结果页</a>
        </div>
    </div>

    <script>
        window.addEventListener('load', function() {
            const rawDataElement = document.getElementById('rawData');
            const parsedDataElement = document.getElementById('parsedData');
            const palaceDataElement = document.getElementById('palaceData');
            const baziDataElement = document.getElementById('baziData');
            const birthDataElement = document.getElementById('birthData');
            
            try {
                // 获取原始数据
                const storedResult = sessionStorage.getItem('paipanResult');
                if (!storedResult) {
                    rawDataElement.innerHTML = '<span class="error">❌ 未找到 sessionStorage 中的数据</span>';
                    parsedDataElement.innerHTML = '<span class="error">❌ 无数据可解析</span>';
                    palaceDataElement.innerHTML = '<span class="error">❌ 无宫位数据</span>';
                    baziDataElement.innerHTML = '<span class="error">❌ 无八字数据</span>';
                    birthDataElement.innerHTML = '<span class="error">❌ 无出生信息</span>';
                    return;
                }
                
                // 显示原始数据
                rawDataElement.innerHTML = `<span class="success">✅ 找到数据 (${storedResult.length} 字符)</span>\n\n` + storedResult;
                
                // 解析数据
                const data = JSON.parse(storedResult);
                parsedDataElement.innerHTML = '<span class="success">✅ 数据解析成功</span>\n\n' + JSON.stringify(data, null, 2);
                
                // 提取具体数据
                const result = data.data || data;
                
                // 紫薇斗数数据
                const ziwei = result.ziwei_analysis || {};
                const palaces = ziwei.palaces || {};
                if (Object.keys(palaces).length > 0) {
                    palaceDataElement.innerHTML = `<span class="success">✅ 找到 ${Object.keys(palaces).length} 个宫位</span>\n\n` + JSON.stringify(palaces, null, 2);
                } else {
                    palaceDataElement.innerHTML = '<span class="error">❌ 未找到宫位数据</span>\n\n紫薇分析数据:\n' + JSON.stringify(ziwei, null, 2);
                }
                
                // 八字数据
                const bazi = result.bazi_analysis || {};
                if (Object.keys(bazi).length > 0) {
                    baziDataElement.innerHTML = '<span class="success">✅ 找到八字数据</span>\n\n' + JSON.stringify(bazi, null, 2);
                } else {
                    baziDataElement.innerHTML = '<span class="error">❌ 未找到八字数据</span>';
                }
                
                // 出生信息
                const birthInfo = result.birth_info || result.input_info || {};
                if (Object.keys(birthInfo).length > 0) {
                    birthDataElement.innerHTML = '<span class="success">✅ 找到出生信息</span>\n\n' + JSON.stringify(birthInfo, null, 2);
                } else {
                    birthDataElement.innerHTML = '<span class="error">❌ 未找到出生信息</span>';
                }
                
            } catch (e) {
                rawDataElement.innerHTML = '<span class="error">❌ 数据读取失败: ' + e.message + '</span>';
                parsedDataElement.innerHTML = '<span class="error">❌ 数据解析失败: ' + e.message + '</span>';
                palaceDataElement.innerHTML = '<span class="error">❌ 无法提取宫位数据</span>';
                baziDataElement.innerHTML = '<span class="error">❌ 无法提取八字数据</span>';
                birthDataElement.innerHTML = '<span class="error">❌ 无法提取出生信息</span>';
                console.error('调试页面错误:', e);
            }
        });
    </script>
</body>
</html>
