#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇斗数功能专项测试
"""

import asyncio
import sys
import time
sys.path.append('.')

async def test_ziwei_fortune():
    print('🔮 紫薇斗数功能专项测试...')
    
    try:
        from core.agents.master_customer_agent import MasterCustomerAgent
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.agents.simple_coordinator import SimpleCoordinator
        from core.agents.base_agent import agent_registry
        
        # 初始化系统
        master_agent = MasterCustomerAgent()
        calculator_agent = FortuneCalculatorAgent()
        coordinator = SimpleCoordinator()
        
        # 注册Agent
        agent_registry.register_agent(master_agent)
        agent_registry.register_agent(calculator_agent)
        
        session_id = 'test_ziwei_001'
        
        print('\n🎯 测试场景：完整的紫薇斗数分析流程')
        
        # 第一步：明确指定紫薇斗数
        print('\n📝 第一步：指定紫薇斗数算命')
        result1 = await coordinator.handle_user_message(
            session_id, 
            '我想看紫薇斗数，我是1988年6月1日午时出生的男命'
        )
        
        print(f'✅ 第一步响应: {result1.get("success")}')
        print(f'阶段: {result1.get("stage")}')
        print(f'响应预览: {result1.get("response", "")[:200]}...')
        
        # 检查会话状态
        session_state = master_agent.get_session_state(session_id)
        print(f'\n📊 会话状态:')
        print(f'  阶段: {session_state.get("stage")}')
        print(f'  算命类型: {session_state.get("calculation_type")}')
        print(f'  生辰信息: {session_state.get("birth_info")}')
        print(f'  result_id: {session_state.get("result_id") is not None}')
        
        # 等待一下让后台分析启动
        print('\n⏳ 等待5秒让后台分析启动...')
        time.sleep(5)
        
        # 检查更新后的状态
        session_state = master_agent.get_session_state(session_id)
        result_id = session_state.get("result_id")
        print(f'📊 5秒后状态: 阶段={session_state.get("stage")}, result_id={result_id is not None}')
        
        if result_id:
            print(f'🎉 result_id已设置: {result_id[:8]}...')
            
            # 检查分析进度
            progress = master_agent.get_analysis_progress(result_id)
            print(f'📈 分析进度: {progress}')
        
        # 第二步：询问紫薇斗数相关问题
        print('\n💫 第二步：询问紫薇斗数命宫特点')
        result2 = await coordinator.handle_user_message(
            session_id, 
            '我的命宫有什么特点？'
        )
        
        print(f'✅ 第二步响应: {result2.get("success")}')
        response2 = result2.get("response", "")
        print(f'响应长度: {len(response2)}字')
        print(f'响应预览: {response2[:300]}...')
        
        # 检查是否包含紫薇斗数专业术语
        ziwei_terms = ["命宫", "紫薇", "天府", "天相", "太阳", "太阴", "贪狼", "巨门", "天同", "廉贞", "天梁", "七杀", "破军"]
        found_terms = [term for term in ziwei_terms if term in response2]
        print(f'🔍 包含紫薇术语: {found_terms}')
        
        # 第三步：询问财运
        print('\n💰 第三步：询问财运状况')
        result3 = await coordinator.handle_user_message(
            session_id, 
            '我的财运如何？什么时候财运最好？'
        )
        
        print(f'✅ 第三步响应: {result3.get("success")}')
        response3 = result3.get("response", "")
        print(f'响应长度: {len(response3)}字')
        print(f'响应预览: {response3[:300]}...')
        
        # 检查财运分析质量
        wealth_terms = ["财帛宫", "正财", "偏财", "财星", "禄存", "化禄", "流年", "大限"]
        found_wealth_terms = [term for term in wealth_terms if term in response3]
        print(f'🔍 包含财运术语: {found_wealth_terms}')
        
        # 第四步：询问感情
        print('\n💕 第四步：询问感情婚姻')
        result4 = await coordinator.handle_user_message(
            session_id, 
            '我的感情运势怎么样？什么时候会遇到正缘？'
        )
        
        print(f'✅ 第四步响应: {result4.get("success")}')
        response4 = result4.get("response", "")
        print(f'响应长度: {len(response4)}字')
        print(f'响应预览: {response4[:300]}...')
        
        # 检查感情分析质量
        love_terms = ["夫妻宫", "桃花", "红鸾", "天喜", "咸池", "天姚", "正缘", "姻缘"]
        found_love_terms = [term for term in love_terms if term in response4]
        print(f'🔍 包含感情术语: {found_love_terms}')
        
        # 最终状态检查
        print('\n📊 最终测试总结:')
        final_state = master_agent.get_session_state(session_id)
        print(f'  算命类型: {final_state.get("calculation_type")}')
        print(f'  分析阶段: {final_state.get("stage")}')
        print(f'  result_id: {final_state.get("result_id") is not None}')
        
        if final_state.get("result_id"):
            final_progress = master_agent.get_analysis_progress(final_state["result_id"])
            print(f'  最终进度: {final_progress}')
        
        # 质量评估
        print('\n🎯 紫薇斗数功能质量评估:')
        print(f'  ✅ 算命类型识别: {"紫薇" if final_state.get("calculation_type") == "ziwei" else "❌"}')
        print(f'  ✅ 后台分析启动: {"是" if final_state.get("result_id") else "❌"}')
        print(f'  ✅ 专业术语使用: {"丰富" if len(found_terms) >= 3 else "一般"}')
        print(f'  ✅ 财运分析质量: {"专业" if len(found_wealth_terms) >= 2 else "一般"}')
        print(f'  ✅ 感情分析质量: {"专业" if len(found_love_terms) >= 2 else "一般"}')
        
        print('\n🎉 紫薇斗数功能测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ziwei_fortune())
