#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实互动测试 - 命令行版本
让我们像真实客户一样与系统对话
"""

import asyncio
import sys
import time
import shutil
import os
sys.path.append('.')

async def real_interaction_test():
    """真实互动测试"""
    print("🎭 真实客户互动测试")
    print("=" * 60)
    print("现在我们来模拟真实客户与系统的对话")
    print("验证前后端独立性和渐进式互动")
    print("=" * 60)
    
    # 清除缓存
    cache_dir = "data/calculation_cache"
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print("✅ 缓存已清除，开始全新测试")
    
    # 创建系统
    from core.agents.master_customer_agent import MasterCustomerAgent
    from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
    from core.agents.simple_coordinator import SimpleCoordinator
    from core.agents.base_agent import agent_registry
    
    # 清除Agent注册
    agent_registry.agents.clear()
    
    # 创建Agent实例
    master_agent = MasterCustomerAgent("real_master")
    calculator_agent = FortuneCalculatorAgent("real_calc")
    coordinator = SimpleCoordinator()
    
    # 注册Agent
    agent_registry.register_agent(master_agent)
    agent_registry.register_agent(calculator_agent)
    
    session_id = "real_interaction_session"
    
    print("\n🤖 系统: 您好！我是您的专属算命师，很高兴为您服务！")
    print("=" * 60)
    
    # 模拟真实对话流程
    conversations = [
        {
            "user": "你好，我想算命",
            "description": "第1轮：初次咨询"
        },
        {
            "user": "我是1985年7月20日午时出生的女性，想看紫薇斗数",
            "description": "第2轮：提供生辰信息，触发后端12角度分析"
        },
        {
            "user": "我的性格怎么样？",
            "description": "第3轮：问性格（测试命宫分析）"
        },
        {
            "user": "我的财运如何？",
            "description": "第4轮：问财运（测试财富分析）"
        },
        {
            "user": "感情方面呢？什么时候能遇到真爱？",
            "description": "第5轮：问感情（测试婚姻分析）"
        },
        {
            "user": "我适合什么工作？事业发展怎么样？",
            "description": "第6轮：问事业（测试事业分析）"
        },
        {
            "user": "健康方面需要注意什么？",
            "description": "第7轮：问健康（测试健康分析）"
        },
        {
            "user": "能再详细说说我的财运吗？有什么具体建议？",
            "description": "第8轮：深度询问财运（测试详细分析）"
        }
    ]
    
    result_id = None
    
    for i, conv in enumerate(conversations, 1):
        user_msg = conv["user"]
        desc = conv["description"]
        
        print(f"\n{desc}")
        print(f"👤 客户: {user_msg}")
        
        start_time = time.time()
        
        try:
            result = await coordinator.handle_user_message(session_id, user_msg)
            response_time = time.time() - start_time
            
            if result.get('success'):
                response = result.get('response', '')
                print(f"🤖 AI ({response_time:.1f}s): {response}")
                
                # 获取结果ID（从第2轮开始）
                if i == 2:
                    session_state = master_agent.get_session_state(session_id)
                    result_id = session_state.get("result_id") if session_state else None
                    if result_id:
                        print(f"📋 后端分析已启动: {result_id[:8]}...")
                
                # 检查后端进度（从第3轮开始）
                if i >= 3 and result_id:
                    progress = master_agent.get_analysis_progress(result_id)
                    completed = progress.get("completed_angles", 0)
                    total_words = progress.get("total_word_count", 0)
                    print(f"📊 后端进度: {completed}/12 角度完成, {total_words}字")
                
                # 评估回答质量
                if len(response) > 200:
                    print(f"✅ 回答详细专业")
                elif len(response) > 100:
                    print(f"⚠️  回答中等")
                else:
                    print(f"❌ 回答较简短")
                    
            else:
                print(f"❌ 系统错误: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 对话失败: {e}")
        
        print("-" * 60)
        
        # 适当延迟，模拟真实对话
        if i <= 2:
            await asyncio.sleep(2)  # 前两轮稍慢
        else:
            await asyncio.sleep(1)  # 后续轮次快一些
    
    # 最终评估
    print(f"\n🎯 真实互动测试评估")
    print("=" * 60)
    
    if result_id:
        final_progress = master_agent.get_analysis_progress(result_id)
        final_completed = final_progress.get("completed_angles", 0)
        final_words = final_progress.get("total_word_count", 0)
        
        print(f"📊 最终结果:")
        print(f"   对话轮次: {len(conversations)}")
        print(f"   后端完成角度: {final_completed}/12")
        print(f"   总分析字数: {final_words}")
        
        if final_completed >= 3 and final_words >= 5000:
            print(f"\n🎉 真实互动测试成功！")
            print(f"\n✅ 验证的功能:")
            print(f"   - 前端立即响应用户")
            print(f"   - 后端独立进行分析")
            print(f"   - 渐进式互动体验")
            print(f"   - 智能查询机制")
            
            print(f"\n🌟 用户体验:")
            print(f"   - 对话自然流畅")
            print(f"   - 回答专业详细")
            print(f"   - 无需等待后端完成")
            print(f"   - 真正的大师级体验")
            
            return True
        else:
            print(f"\n⚠️  系统性能需要优化")
            return False
    else:
        print(f"\n❌ 后端分析未启动")
        return False

async def main():
    """主函数"""
    print("🎭 开始真实客户互动测试")
    print("模拟真实客户与算命师的完整对话流程")
    print("验证前后端独立性和用户体验")
    
    success = await real_interaction_test()
    
    if success:
        print(f"\n🎉 恭喜！真实互动测试完全成功！")
        print(f"您的双Agent架构完美实现了:")
        print(f"- 前后端独立但协作")
        print(f"- 渐进式互动体验")
        print(f"- 专业大师级服务")
    else:
        print(f"\n💥 需要进一步优化")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
