# 🏗️ 项目架构重构方案

## 🎯 **重构目标**

### 核心需求
1. **聊天互动能力** - 支持多轮对话，上下文记忆
2. **智能语义识别** - 基于LLM的真实语义理解，自动调用工具
3. **自动提示词切换** - 根据功能自动选择合适的LLM提示词
4. **功能隔离** - 为后期新功能扩展做好技术准备
5. **多端支持** - Web、API、微信等聊天工具统一接口

### 设计原则
- **不重写，只重组** - 保留现有可用代码
- **模块化设计** - 每个功能独立，便于扩展
- **统一接口** - 所有调用方式使用相同的聊天接口
- **向后兼容** - 现有功能不受影响

## 🏛️ **新架构设计**

### 架构层次
```
┌─────────────────────────────────────────────────────────────┐
│                    接入层 (Access Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  Web UI  │  API接口  │  微信接口  │  其他聊天工具接口        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   聊天引擎层 (Chat Engine)                   │
├─────────────────────────────────────────────────────────────┤
│  • 会话管理 (Session Management)                            │
│  • 上下文记忆 (Context Memory)                              │
│  • 多轮对话 (Multi-turn Conversation)                       │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  语义理解层 (NLU Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  • 意图识别 (Intent Recognition)                            │
│  • 实体提取 (Entity Extraction)                             │
│  • 工具选择 (Tool Selection)                                │
│  • 提示词管理 (Prompt Management)                           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   工具调度层 (Tool Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  紫薇斗数  │  八字算命  │  六爻算卦  │  [新功能预留]         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   算法引擎层 (Algorithm Layer)               │
├─────────────────────────────────────────────────────────────┤
│  • 紫薇算法 (Ziwei Calculator)                              │
│  • 八字算法 (Bazi Calculator)                               │
│  • 六爻算法 (Liuyao Calculator)                             │
│  • [新算法预留]                                             │
└─────────────────────────────────────────────────────────────┘
```

## 📁 **新目录结构**

```
Ziwei-Chatglm3-6B/
├── core/                           # 核心引擎层
│   ├── chat/                       # 聊天引擎
│   │   ├── __init__.py
│   │   ├── session_manager.py      # 会话管理
│   │   ├── context_memory.py       # 上下文记忆
│   │   └── conversation_engine.py  # 对话引擎
│   ├── nlu/                        # 语义理解层
│   │   ├── __init__.py
│   │   ├── intent_recognizer.py    # 意图识别
│   │   ├── entity_extractor.py     # 实体提取
│   │   ├── tool_selector.py        # 工具选择
│   │   └── prompt_manager.py       # 提示词管理
│   ├── tools/                      # 工具层
│   │   ├── __init__.py
│   │   ├── base_tool.py            # 工具基类
│   │   ├── ziwei_tool.py           # 紫薇斗数工具
│   │   ├── bazi_tool.py            # 八字算命工具
│   │   ├── liuyao_tool.py          # 六爻算卦工具
│   │   └── tool_registry.py        # 工具注册中心
│   └── fortune_engine.py           # [保留] 现有引擎，逐步迁移
├── algorithms/                     # [保留] 算法层
├── interfaces/                     # 接入层
│   ├── __init__.py
│   ├── web_interface.py            # Web接口
│   ├── api_interface.py            # API接口
│   ├── wechat_interface.py         # 微信接口 [新增]
│   └── base_interface.py           # 接口基类
├── config/                         # 配置层
│   ├── __init__.py
│   ├── prompts/                    # 提示词配置
│   │   ├── ziwei_prompts.py
│   │   ├── bazi_prompts.py
│   │   ├── liuyao_prompts.py
│   │   └── general_prompts.py
│   └── settings.py                 # 系统配置
├── utils/                          # 工具类
│   ├── __init__.py
│   ├── llm_client.py               # LLM客户端
│   └── image_generator.py          # 图片生成器
├── web_demo/                       # [保留] Web演示
├── openai_api/                     # [保留] 现有API，逐步迁移
└── deprecated/                     # [保留] 废弃文件
```

## 🔧 **重构实施计划**

### 阶段1：基础架构搭建 (不影响现有功能)
1. **创建新目录结构**
2. **实现聊天引擎基础框架**
3. **实现语义理解基础框架**
4. **实现工具层基础框架**
5. **配置管理系统**

### 阶段2：功能迁移 (逐步替换)
1. **迁移紫薇斗数功能到新架构**
2. **迁移八字算命功能到新架构**
3. **迁移六爻算卦功能到新架构**
4. **统一接口层**

### 阶段3：增强功能 (新特性)
1. **多轮对话支持**
2. **上下文记忆**
3. **智能提示词切换**
4. **微信接口支持**

### 阶段4：优化完善 (性能提升)
1. **性能优化**
2. **错误处理完善**
3. **日志系统完善**
4. **测试覆盖**

## 🎯 **核心组件设计**

### 1. 聊天引擎 (Chat Engine)
```python
class ConversationEngine:
    """对话引擎 - 支持多轮对话和上下文记忆"""
    
    def process_message(self, session_id: str, message: str) -> str:
        """处理用户消息"""
        # 1. 获取会话上下文
        # 2. 语义理解
        # 3. 工具调用
        # 4. 生成回复
        # 5. 更新上下文
        pass
```

### 2. 语义理解 (NLU)
```python
class IntentRecognizer:
    """意图识别 - 基于LLM的智能语义理解"""
    
    def recognize_intent(self, message: str, context: dict) -> dict:
        """识别用户意图"""
        # 1. 分析用户消息
        # 2. 结合上下文
        # 3. 识别意图和实体
        # 4. 选择合适的工具
        pass
```

### 3. 工具系统 (Tool System)
```python
class BaseTool:
    """工具基类 - 所有功能工具的基础"""
    
    def can_handle(self, intent: dict) -> bool:
        """判断是否能处理该意图"""
        pass
    
    def execute(self, intent: dict, context: dict) -> dict:
        """执行工具功能"""
        pass
```

### 4. 提示词管理 (Prompt Management)
```python
class PromptManager:
    """提示词管理 - 自动选择合适的提示词"""
    
    def get_prompt(self, tool_name: str, task_type: str) -> str:
        """获取合适的提示词"""
        pass
```

## 🚀 **实施优势**

### 技术优势
1. **模块化设计** - 每个功能独立，便于维护和扩展
2. **统一接口** - 所有调用方式使用相同的聊天接口
3. **智能语义** - 基于LLM的真实语义理解
4. **自动切换** - 根据功能自动选择合适的提示词

### 业务优势
1. **多端支持** - Web、API、微信等统一体验
2. **对话体验** - 支持多轮对话和上下文记忆
3. **易于扩展** - 新功能只需实现工具接口
4. **向后兼容** - 现有功能不受影响

## 📝 **下一步行动**

1. **确认重构方案** - 与您确认架构设计
2. **开始实施** - 按阶段逐步实施重构
3. **功能测试** - 确保每个阶段功能正常
4. **文档更新** - 更新技术文档和使用说明

---

**🎯 重构目标：打造一个可扩展、智能化、多端统一的算命聊天系统！**
APIKEY：sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk