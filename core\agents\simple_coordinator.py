#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化协调器 - 正确的双Agent协作模式
只负责路由到主控沟通Agent
"""

import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

from .base_agent import agent_registry

logger = logging.getLogger(__name__)

class SimpleCoordinator:
    """简化协调器 - 只负责路由到主控Agent"""
    
    def __init__(self):
        """初始化协调器"""
        self.coordinator_id = "simple_coordinator_001"
        
        # 性能统计
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "total_response_time": 0.0
        }
        
        logger.info("简化协调器初始化完成")
    
    async def handle_user_message(self, session_id: str, user_message: str, 
                                 user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理用户消息 - 直接路由到主控Agent"""
        start_time = time.time()
        
        try:
            self.stats["total_requests"] += 1
            
            # 获取主控沟通Agent
            master_agent = self._get_master_customer_agent()
            if not master_agent:
                return {
                    "success": False,
                    "error": "主控Agent不可用",
                    "response": "抱歉，系统暂时无法提供服务，请稍后再试。"
                }
            
            # 构建消息
            from .base_agent import AgentMessage, MessageType
            message = AgentMessage(
                message_id=f"msg_{int(time.time())}",
                message_type=MessageType.COMMUNICATION_REQUEST,
                sender_id=self.coordinator_id,
                receiver_id=master_agent.agent_id,
                content={
                    "user_message": user_message,
                    "session_id": session_id,
                    "user_context": user_context or {}
                },
                timestamp=datetime.now().isoformat()
            )
            
            # 发送给主控Agent处理
            response = await master_agent.process_message(message)
            
            # 计算处理时间
            processing_time = time.time() - start_time
            self.stats["total_response_time"] += processing_time
            
            if response.success:
                self.stats["successful_requests"] += 1
                
                # 更新平均响应时间
                self.stats["average_response_time"] = (
                    self.stats["total_response_time"] / self.stats["total_requests"]
                )
                
                return {
                    "success": True,
                    "response": response.data.get("response", ""),
                    "stage": response.data.get("stage", "unknown"),
                    "session_state": response.data.get("session_state", {}),
                    "processing_time": processing_time,
                    "agent_id": master_agent.agent_id
                }
            else:
                self.stats["failed_requests"] += 1
                return {
                    "success": False,
                    "error": response.error,
                    "response": response.data.get("response", "抱歉，处理您的请求时遇到了问题。"),
                    "processing_time": processing_time
                }
                
        except Exception as e:
            self.stats["failed_requests"] += 1
            processing_time = time.time() - start_time
            
            logger.error(f"协调器处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": "抱歉，系统处理您的请求时遇到了问题，请稍后再试。",
                "processing_time": processing_time
            }
    
    def _get_master_customer_agent(self):
        """获取主控沟通Agent"""
        agents = agent_registry.get_agents_by_type("MasterCustomerAgent")
        return agents[0] if agents else None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取协调器统计信息"""
        success_rate = 0
        if self.stats["total_requests"] > 0:
            success_rate = self.stats["successful_requests"] / self.stats["total_requests"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "coordinator_id": self.coordinator_id
        }
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        try:
            master_agent = self._get_master_customer_agent()
            if not master_agent:
                return {"status": "agent_unavailable"}
            
            # 调用主控Agent的状态查询方法
            return await master_agent.get_calculation_status(session_id)
            
        except Exception as e:
            logger.error(f"获取会话状态失败: {e}")
            return {"status": "error", "error": str(e)}
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "total_response_time": 0.0
        }
        logger.info("协调器统计信息已重置")
    
    async def shutdown(self):
        """关闭协调器"""
        logger.info("简化协调器开始关闭")
        # 简化协调器没有需要清理的资源
        logger.info("简化协调器已关闭")
