#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
《增删卜易》知识库模块
基于野鹤老人的经典六爻著作，为六爻分析提供传统理论支持
"""

class ZengshanBuyiKnowledge:
    """《增删卜易》知识库"""
    
    def __init__(self):
        """初始化知识库"""
        self.knowledge_base = self._build_knowledge_base()
    
    def _build_knowledge_base(self):
        """构建《增删卜易》知识库"""
        return {
            "基础理论": {
                "六爻基本概念": {
                    "用神": "所占之事的代表爻，如占财用妻财爻，占官用官鬼爻，占父母用父母爻等",
                    "原神": "生用神之爻，为用神之源泉，最喜旺相发动",
                    "忌神": "克用神之爻，最怕旺相发动",
                    "仇神": "克原神或生忌神之爻",
                    "进神": "爻值日辰或动而化进，主事情向前发展",
                    "退神": "爻值日辰或动而化退，主事情倒退或延迟"
                },
                "六亲含义": {
                    "父母爻": "代表父母、长辈、房屋、文书、衣服、雨具等",
                    "兄弟爻": "代表兄弟姐妹、朋友、同事、劫财、竞争对手等",
                    "子孙爻": "代表子女、晚辈、医生、药物、僧道、享乐等",
                    "妻财爻": "代表妻子、财物、货物、身体（女命）等",
                    "官鬼爻": "代表官府、鬼神、疾病、丈夫（女命）、盗贼等"
                },
                "六神含义": {
                    "青龙": "主喜庆、文书、酒色、正直之事",
                    "朱雀": "主文书、口舌、信息、火光、血光",
                    "勾陈": "主田土、牢狱、迟滞、中央之事",
                    "腾蛇": "主虚惊、怪异、变化、弯曲之事",
                    "白虎": "主凶丧、疾病、道路、金属、西方",
                    "玄武": "主盗贼、暗昧、水患、北方、奸邪"
                }
            },
            "占卜原则": {
                "取用神法": {
                    "占财运": "以妻财爻为用神",
                    "占官运": "以官鬼爻为用神",
                    "占父母": "以父母爻为用神",
                    "占子女": "以子孙爻为用神",
                    "占兄弟": "以兄弟爻为用神",
                    "占疾病": "以官鬼爻为用神",
                    "占出行": "以世爻为用神，看其旺衰"
                },
                "判断吉凶": {
                    "用神旺相": "得日月生扶，或得动爻生扶，主吉",
                    "用神休囚": "被日月克制，或被动爻克制，主凶",
                    "用神发动": "用神自动，主事情有变化，吉凶看其旺衰",
                    "用神安静": "用神不动，主事情平稳，吉凶看日月作用"
                }
            },
            "应期理论": {
                "生旺应期": "用神生旺之时应吉",
                "死绝应期": "用神死绝之时应凶",
                "冲合应期": "用神逢冲合之时应事",
                "值日应期": "用神值日之时应事",
                "动爻应期": "动爻所化之爻值日时应事"
            },
            "特殊格局": {
                "反吟": "卦与变卦天地相反，主反复不定",
                "伏吟": "卦与变卦相同，主呻吟不决",
                "游魂": "主心神不定，事情不稳",
                "归魂": "主归家、回归、稳定",
                "六冲": "主散乱、冲突、快速",
                "六合": "主和谐、合作、缓慢"
            },
            "实战要诀": {
                "野鹤心法": [
                    "卦以用神为主，用神旺则吉，衰则凶",
                    "动爻为事情之变化，静爻为事情之现状",
                    "日月为天地之主宰，最有权威",
                    "原神发动能救用神，忌神发动必伤用神",
                    "空亡如无，月破如死，不可不察",
                    "六神配合爻位，能断事情之详细",
                    "世应相生则和，相克则争",
                    "化进神主进，化退神主退"
                ],
                "断卦步骤": [
                    "一看用神在何爻位",
                    "二看用神旺衰如何",
                    "三看原神忌神动静",
                    "四看日月对用神作用",
                    "五看动爻对用神影响",
                    "六看六神配合取象",
                    "七定吉凶及应期"
                ]
            },
            "经典案例": {
                "占财运": {
                    "卦例": "某人占财运，得雷天大壮，妻财戌土持世",
                    "分析": "妻财持世，自身即是财，但戌土月破，财运不佳",
                    "结论": "当月财运不利，待土旺之月方可转好"
                },
                "占疾病": {
                    "卦例": "某人占病，得水雷屯，官鬼子水发动",
                    "分析": "官鬼发动主病重，但化出子孙，子孙为医药",
                    "结论": "病虽重但有救，遇医得药可愈"
                }
            },
            "禁忌事项": {
                "起卦禁忌": [
                    "不可一事多占",
                    "不可无事乱占",
                    "不可心不诚占",
                    "不可醉酒时占"
                ],
                "断卦禁忌": [
                    "不可强不知以为知",
                    "不可执一理而不变通",
                    "不可不明卦理而妄断",
                    "不可不察时令而断事"
                ]
            }
        }
    
    def get_knowledge_by_category(self, category: str) -> dict:
        """根据分类获取知识"""
        return self.knowledge_base.get(category, {})
    
    def search_knowledge(self, keyword: str) -> dict:
        """搜索相关知识"""
        results = {}
        for category, content in self.knowledge_base.items():
            category_results = {}
            for subcategory, subcontent in content.items():
                if isinstance(subcontent, dict):
                    for key, value in subcontent.items():
                        if keyword in key or keyword in str(value):
                            if subcategory not in category_results:
                                category_results[subcategory] = {}
                            category_results[subcategory][key] = value
                elif isinstance(subcontent, list):
                    matching_items = [item for item in subcontent if keyword in item]
                    if matching_items:
                        category_results[subcategory] = matching_items
                else:
                    if keyword in str(subcontent):
                        category_results[subcategory] = subcontent
            
            if category_results:
                results[category] = category_results
        
        return results
    
    def get_analysis_guidance(self, question_type: str, hexagram_info: dict) -> str:
        """根据问题类型和卦象信息获取分析指导"""
        guidance = []
        
        # 根据问题类型给出用神指导
        if "财" in question_type or "钱" in question_type:
            guidance.append("【用神取法】以妻财爻为用神")
            guidance.append("【分析要点】观察妻财爻的旺衰，原神兄弟爻是否发动克财")
        elif "官" in question_type or "工作" in question_type or "事业" in question_type:
            guidance.append("【用神取法】以官鬼爻为用神")
            guidance.append("【分析要点】观察官鬼爻旺衰，子孙爻是否发动克官")
        elif "病" in question_type or "健康" in question_type:
            guidance.append("【用神取法】以官鬼爻为用神（代表疾病）")
            guidance.append("【分析要点】官鬼旺则病重，子孙发动则有医药之救")
        elif "感情" in question_type or "婚姻" in question_type:
            guidance.append("【用神取法】男占以妻财为用神，女占以官鬼为用神")
            guidance.append("【分析要点】观察用神旺衰及世应相生相克关系")
        
        # 添加通用分析要点
        guidance.extend([
            "【野鹤心法】卦以用神为主，用神旺则吉，衰则凶",
            "【重要提醒】日月为天地主宰，最有权威，不可不察其对用神之作用",
            "【动静之理】动爻为变化之象，静爻为现状之象",
            "【应期判断】生旺应吉，死绝应凶，冲合值日皆为应期"
        ])
        
        return "\n".join(guidance)
    
    def format_knowledge_for_llm(self, question: str, hexagram_data: dict) -> str:
        """为LLM分析格式化知识库内容"""
        # 提取问题关键词
        keywords = []
        if any(word in question for word in ["财", "钱", "收入", "投资"]):
            keywords.append("财运")
        if any(word in question for word in ["官", "工作", "事业", "职业"]):
            keywords.append("官运")
        if any(word in question for word in ["病", "健康", "身体"]):
            keywords.append("疾病")
        if any(word in question for word in ["感情", "婚姻", "恋爱"]):
            keywords.append("感情")
        
        # 构建相关知识
        knowledge_text = "【《增删卜易》传统理论指导】\n\n"
        
        # 基础理论
        knowledge_text += "★ 六爻基本概念：\n"
        basic_concepts = self.knowledge_base["基础理论"]["六爻基本概念"]
        for concept, meaning in basic_concepts.items():
            knowledge_text += f"• {concept}：{meaning}\n"
        
        knowledge_text += "\n★ 六亲含义：\n"
        liuqin = self.knowledge_base["基础理论"]["六亲含义"]
        for qin, meaning in liuqin.items():
            knowledge_text += f"• {qin}：{meaning}\n"
        
        # 根据问题类型添加特定指导
        if keywords:
            knowledge_text += f"\n★ 针对{'/'.join(keywords)}的专门指导：\n"
            take_yongshen = self.knowledge_base["占卜原则"]["取用神法"]
            for key, value in take_yongshen.items():
                if any(keyword in key for keyword in keywords):
                    knowledge_text += f"• {key}：{value}\n"
        
        # 野鹤心法
        knowledge_text += "\n★ 野鹤老人心法要诀：\n"
        xinfa = self.knowledge_base["实战要诀"]["野鹤心法"]
        for i, rule in enumerate(xinfa[:5], 1):  # 只取前5条，避免过长
            knowledge_text += f"{i}. {rule}\n"
        
        # 断卦步骤
        knowledge_text += "\n★ 传统断卦步骤：\n"
        steps = self.knowledge_base["实战要诀"]["断卦步骤"]
        for step in steps:
            knowledge_text += f"• {step}\n"
        
        knowledge_text += "\n【重要提醒】以上理论来自《增删卜易》，为野鹤老人四十年占验心得，请严格按此理论进行分析。"
        
        return knowledge_text


def test_knowledge_base():
    """测试知识库功能"""
    kb = ZengshanBuyiKnowledge()
    
    print("=== 《增删卜易》知识库测试 ===")
    
    # 测试搜索功能
    print("\n1. 搜索'用神'相关知识:")
    results = kb.search_knowledge("用神")
    for category, content in results.items():
        print(f"【{category}】")
        for subcategory, subcontent in content.items():
            print(f"  {subcategory}: {subcontent}")
    
    # 测试分析指导
    print("\n2. 财运问题分析指导:")
    guidance = kb.get_analysis_guidance("财运如何", {})
    print(guidance)
    
    # 测试LLM格式化
    print("\n3. LLM知识格式化:")
    formatted = kb.format_knowledge_for_llm("我的财运如何？", {})
    print(formatted[:500] + "...")


if __name__ == "__main__":
    test_knowledge_base()
