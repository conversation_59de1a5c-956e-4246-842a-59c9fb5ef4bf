#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紫薇斗数工具 - 将现有紫薇斗数功能包装为新架构工具
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from .base_tool import BaseTool

logger = logging.getLogger(__name__)

class ZiweiTool(BaseTool):
    """紫薇斗数工具 - 基于现有FortuneEngine的紫薇斗数功能"""
    
    def __init__(self, ziwei_calc, llm_client, prompt_manager):
        """
        初始化紫薇斗数工具
        
        Args:
            ziwei_calc: 紫薇斗数算法实例
            llm_client: LLM客户端
            prompt_manager: 提示词管理器
        """
        super().__init__(
            name="ziwei",
            description="紫薇斗数算命 - 基于出生信息进行详细的命盘分析",
            version="2.0.0"
        )
        
        self.ziwei_calc = ziwei_calc
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
        
        logger.info("紫薇斗数工具初始化完成")
    
    def can_handle(self, intent: Dict[str, Any]) -> bool:
        """
        判断是否能处理紫薇斗数相关请求
        
        Args:
            intent: 意图信息
            
        Returns:
            是否能处理
        """
        tool_name = intent.get("tool_name", "").lower()
        
        # 直接指定紫薇斗数
        if tool_name == "ziwei":
            return True
        
        # 综合分析也可以使用紫薇斗数
        if tool_name == "comprehensive":
            return True
        
        # 检查关键词
        raw_response = intent.get("raw_response", "").lower()
        ziwei_keywords = ["紫薇", "斗数", "命宫", "星曜", "排盘"]
        
        return any(keyword in raw_response for keyword in ziwei_keywords)
    
    def get_required_entities(self) -> List[str]:
        """获取紫薇斗数所需的实体"""
        return ["birth_info"]
    
    def execute(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行紫薇斗数分析
        
        Args:
            intent: 意图信息
            context: 上下文信息
            
        Returns:
            分析结果
        """
        start_time = time.time()
        
        try:
            logger.info("开始执行紫薇斗数分析")
            
            # 1. 获取出生信息
            birth_info = self._extract_birth_info(intent, context)
            if not birth_info:
                return {
                    "success": False,
                    "error": "缺少出生信息",
                    "message": "请提供您的出生信息（年月日时和性别），例如：1988年6月1日午时男"
                }
            
            # 2. 调用紫薇斗数算法
            algorithm_result = self._call_ziwei_algorithm(birth_info)
            if not algorithm_result.get("success"):
                return {
                    "success": False,
                    "error": algorithm_result.get("error", "算法调用失败"),
                    "message": "紫薇斗数排盘失败，请检查出生信息是否正确"
                }
            
            # 3. 生成AI分析
            analysis_result = self._generate_ai_analysis(
                algorithm_result, intent, context, birth_info
            )
            
            # 4. 生成图片
            chart_image = self._generate_chart_image(algorithm_result)
            
            execution_time = time.time() - start_time
            logger.info(f"紫薇斗数分析完成 - 耗时: {execution_time:.2f}秒")
            
            return {
                "success": True,
                "tool_name": "ziwei",
                "analysis": analysis_result,
                "chart_data": algorithm_result,
                "chart_image": chart_image,
                "birth_info": birth_info,
                "execution_time": execution_time,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"紫薇斗数分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "紫薇斗数分析过程中出现错误，请稍后重试"
            }
    
    def _extract_birth_info(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取出生信息"""
        
        # 从意图中获取
        birth_info = intent.get("entities", {}).get("birth_info")
        if birth_info:
            return birth_info
        
        # 从上下文中获取
        birth_info = context.get("birth_info")
        if birth_info:
            return birth_info
        
        return None
    
    def _call_ziwei_algorithm(self, birth_info: Dict[str, Any]) -> Dict[str, Any]:
        """调用紫薇斗数算法"""
        
        try:
            if not self.ziwei_calc:
                return {"success": False, "error": "紫薇斗数算法未初始化"}
            
            result = self.ziwei_calc.calculate_chart(
                year=birth_info["year"],
                month=birth_info["month"],
                day=birth_info["day"],
                hour=birth_info["hour"],
                gender=birth_info.get("gender", "男")
            )
            
            if result.get("success"):
                return result
            else:
                return {
                    "success": False,
                    "error": result.get("error", "算法计算失败")
                }
                
        except Exception as e:
            logger.error(f"紫薇斗数算法调用失败: {e}")
            return {
                "success": False,
                "error": f"算法调用异常: {str(e)}"
            }
    
    def _generate_ai_analysis(self, algorithm_result: Dict[str, Any], 
                            intent: Dict[str, Any], context: Dict[str, Any],
                            birth_info: Dict[str, Any]) -> str:
        """生成AI分析"""
        
        try:
            # 获取问题类型
            question_type = intent.get("question_type", "general")
            
            # 构建分析提示词
            prompt = self.prompt_manager.get_ziwei_analysis_prompt(
                algorithm_result=algorithm_result,
                question_type=question_type,
                birth_info=birth_info,
                context=context
            )
            
            # 调用LLM生成分析
            analysis = self.llm_client.chat(
                prompt=prompt,
                system_prompt="你是专业的紫薇斗数大师，必须基于提供的真实排盘数据进行分析，不得编造任何信息。",
                temperature=0.7
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"AI分析生成失败: {e}")
            return f"分析生成失败: {str(e)}"
    
    def _generate_chart_image(self, algorithm_result: Dict[str, Any]) -> Optional[str]:
        """生成紫薇斗数图片"""
        
        try:
            # 导入图片生成器
            import sys
            sys.path.append('utils')
            from image_generator import generate_ziwei_chart
            
            chart_data = algorithm_result.get("data", {})
            if not chart_data:
                return None
            
            # 生成图片
            image_path = generate_ziwei_chart(chart_data)
            return image_path
            
        except Exception as e:
            logger.warning(f"紫薇斗数图片生成失败: {e}")
            return None
    
    def get_prompt_template(self, task_type: str) -> Optional[str]:
        """获取紫薇斗数专用提示词模板"""
        
        templates = {
            "career": "请重点分析事业宫、官禄宫的星曜配置...",
            "love": "请重点分析夫妻宫、桃花星的情况...",
            "wealth": "请重点分析财帛宫、禄存星的配置...",
            "health": "请重点分析疾厄宫、化忌星的影响...",
            "fortune": "请重点分析命宫、流年运势...",
            "general": "请进行全面的紫薇斗数分析..."
        }
        
        return templates.get(task_type, templates["general"])
    
    def validate_input(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """验证紫薇斗数输入"""
        
        # 基础验证
        base_validation = super().validate_input(intent, context)
        if not base_validation["valid"]:
            return base_validation
        
        # 出生信息验证
        birth_info = self._extract_birth_info(intent, context)
        if not birth_info:
            return {
                "valid": False,
                "message": "紫薇斗数需要完整的出生信息",
                "missing_entities": ["birth_info"]
            }
        
        # 验证出生信息完整性
        required_fields = ["year", "month", "day", "hour", "gender"]
        missing_fields = []
        
        for field in required_fields:
            if field not in birth_info:
                missing_fields.append(field)
        
        if missing_fields:
            return {
                "valid": False,
                "message": f"出生信息不完整，缺少: {', '.join(missing_fields)}",
                "missing_entities": missing_fields
            }
        
        return {
            "valid": True,
            "message": "紫薇斗数输入验证通过",
            "missing_entities": []
        }
    
    def get_info(self) -> Dict[str, Any]:
        """获取紫薇斗数工具信息"""
        
        base_info = super().get_info()
        base_info.update({
            "supported_questions": ["career", "love", "wealth", "health", "fortune", "general"],
            "required_birth_info": ["year", "month", "day", "hour", "gender"],
            "features": [
                "详细命盘分析",
                "四角度专业解读",
                "图片化命盘展示",
                "个性化问题分析"
            ],
            "algorithm_status": "已连接" if self.ziwei_calc else "未连接"
        })
        
        return base_info
