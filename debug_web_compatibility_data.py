#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Web界面合盘数据
"""

import asyncio
import json

def debug_web_compatibility_data():
    """调试Web界面合盘数据"""
    print("🔍 调试Web界面合盘数据")
    
    try:
        # 1. 模拟Web界面的数据格式
        person_a_info = {
            "name": "A",  # Web界面可能使用默认名称
            "year": "1988",
            "month": "6", 
            "day": "1",
            "hour": "午时",
            "gender": "男"
        }
        
        person_b_info = {
            "name": "B",  # Web界面可能使用默认名称
            "year": "1990",
            "month": "8",
            "day": "15", 
            "hour": "酉时",
            "gender": "女"
        }
        
        analysis_dimension = "emotional_harmony"
        
        print(f"📊 模拟Web数据:")
        print(f"   A: {person_a_info['name']} - {person_a_info['year']}/{person_a_info['month']}/{person_a_info['day']} {person_a_info['hour']} {person_a_info['gender']}")
        print(f"   B: {person_b_info['name']} - {person_b_info['year']}/{person_b_info['month']}/{person_b_info['day']} {person_b_info['hour']} {person_b_info['gender']}")
        
        # 2. 初始化引擎并计算数据
        print("🔧 初始化合盘分析引擎...")
        from core.compatibility_analysis import CompatibilityAnalysisEngine
        compatibility_engine = CompatibilityAnalysisEngine()
        
        print("📊 计算合盘数据...")
        compatibility_data = compatibility_engine.calculate_compatibility(person_a_info, person_b_info)
        
        if not compatibility_data.get("success"):
            print(f"❌ 合盘数据计算失败: {compatibility_data.get('error')}")
            return False
        
        print(f"✅ 合盘数据计算成功")
        
        # 3. 构建提示词并检查
        print(f"\n🔨 构建提示词...")
        from core.analysis.compatibility_prompt_builder import CompatibilityPromptBuilder
        
        prompt_builder = CompatibilityPromptBuilder()
        prompt = prompt_builder.build_compatibility_prompt(compatibility_data, analysis_dimension)
        
        print(f"   提示词长度: {len(prompt)}字符")
        
        # 检查提示词中的数据缺失情况
        if "数据缺失" in prompt:
            print(f"   ⚠️ 提示词中包含'数据缺失'")
            
            # 分析哪些部分缺失
            lines = prompt.split('\n')
            missing_sections = []
            
            for i, line in enumerate(lines):
                if "数据缺失" in line:
                    # 找到缺失的部分
                    context_start = max(0, i-2)
                    context_end = min(len(lines), i+3)
                    context = lines[context_start:context_end]
                    missing_sections.append({
                        "line_number": i+1,
                        "missing_line": line.strip(),
                        "context": context
                    })
            
            print(f"   发现 {len(missing_sections)} 处数据缺失:")
            for section in missing_sections[:3]:  # 显示前3个
                print(f"     第{section['line_number']}行: {section['missing_line']}")
                print(f"     上下文: {' | '.join([l.strip() for l in section['context'] if l.strip()])}")
                print()
        else:
            print(f"   ✅ 提示词中包含实际数据")
        
        # 4. 执行LLM分析
        print(f"\n🤖 执行LLM分析...")
        
        async def run_analysis():
            result = await compatibility_engine.execute_compatibility_analysis(
                compatibility_data, analysis_dimension
            )
            return result
        
        # 运行异步分析
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(run_analysis())
        loop.close()
        
        print(f"   分析结果: {result.get('success')}")
        print(f"   内容长度: {len(result.get('content', ''))}字符")
        print(f"   验证评分: {result.get('validation_report', {}).get('score', 0)}")
        
        # 5. 分析结果内容
        content = result.get('content', '')
        print(f"\n📄 分析结果内容检查:")
        
        if "数据缺失" in content:
            print(f"   ⚠️ LLM分析结果中提到数据缺失")
            # 找出LLM提到的具体缺失内容
            content_lines = content.split('\n')
            missing_mentions = [line for line in content_lines if "数据缺失" in line or "缺失" in line]
            for mention in missing_mentions[:5]:  # 显示前5个
                print(f"     {mention.strip()}")
        else:
            print(f"   ✅ LLM分析结果正常")
        
        # 6. 保存完整调试信息
        print(f"\n💾 保存调试信息...")
        
        debug_info = {
            "person_a_info": person_a_info,
            "person_b_info": person_b_info,
            "analysis_dimension": analysis_dimension,
            "compatibility_data_success": compatibility_data.get("success"),
            "prompt_length": len(prompt),
            "prompt_contains_missing": "数据缺失" in prompt,
            "analysis_success": result.get("success"),
            "analysis_content_length": len(content),
            "analysis_contains_missing": "数据缺失" in content,
            "validation_score": result.get('validation_report', {}).get('score', 0)
        }
        
        # 保存提示词样本
        with open("web_compatibility_prompt_debug.txt", 'w', encoding='utf-8') as f:
            f.write(f"Web界面合盘提示词调试\n")
            f.write(f"生成时间: {compatibility_data.get('timestamp')}\n")
            f.write(f"分析维度: {analysis_dimension}\n")
            f.write(f"提示词长度: {len(prompt)}字符\n")
            f.write(f"包含数据缺失: {'是' if '数据缺失' in prompt else '否'}\n")
            f.write(f"{'='*60}\n\n")
            f.write(prompt)
        
        # 保存分析结果样本
        with open("web_compatibility_result_debug.txt", 'w', encoding='utf-8') as f:
            f.write(f"Web界面合盘分析结果调试\n")
            f.write(f"分析成功: {result.get('success')}\n")
            f.write(f"内容长度: {len(content)}字符\n")
            f.write(f"验证评分: {result.get('validation_report', {}).get('score', 0)}\n")
            f.write(f"包含数据缺失: {'是' if '数据缺失' in content else '否'}\n")
            f.write(f"{'='*60}\n\n")
            f.write(content)
        
        with open("web_compatibility_debug_info.json", 'w', encoding='utf-8') as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 调试信息已保存")
        
        print("🎉 Web界面合盘数据调试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_web_compatibility_data()
    if success:
        print("\n✅ Web界面合盘数据调试完成！")
        print("📋 请检查生成的文件了解详细信息:")
        print("   - web_compatibility_prompt_debug.txt (提示词)")
        print("   - web_compatibility_result_debug.txt (分析结果)")
        print("   - web_compatibility_debug_info.json (调试信息)")
    else:
        print("\n❌ 调试失败！请检查错误信息。")
