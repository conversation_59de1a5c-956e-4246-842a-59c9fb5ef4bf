# coding=utf-8
# Implements API for ChatGLM3-6B in OpenAI's format. (https://platform.openai.com/docs/api-reference/chat)
# Usage: python openai_api.py
# Visit http://localhost:8000/docs for documents.

# 在OpenAI的API中，max_tokens 等价于 HuggingFace 的 max_new_tokens 而不是 max_length，。
# 例如，对于6b模型，设置max_tokens = 8192，则会报错，因为扣除历史记录和提示词后，模型不能输出那么多的tokens。

import time
import json
import asyncio
import sys
import os
from contextlib import asynccontextmanager
from typing import List, Literal, Optional, Union

import requests
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from pydantic import BaseModel, Field
from sse_starlette.sse import EventSourceResponse

# 添加算法模块路径
algorithms_path = os.path.join(os.path.dirname(__file__), '..', 'algorithms')
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
sys.path.append(algorithms_path)
sys.path.append(core_path)

# 尝试导入算法模块
try:
    from real_ziwei_calculator import RealZiweiCalculator
    ZIWEI_AVAILABLE = True
except ImportError as e:
    print(f"❌ 紫薇斗数算法导入失败: {e}")
    RealZiweiCalculator = None
    ZIWEI_AVAILABLE = False

try:
    from real_bazi_calculator import RealBaziCalculator
    BAZI_AVAILABLE = True
except ImportError as e:
    print(f"❌ 八字算法导入失败: {e}")
    RealBaziCalculator = None
    BAZI_AVAILABLE = False

try:
    from liuyao_calculator import LiuyaoCalculator
    LIUYAO_AVAILABLE = True
except ImportError as e:
    print(f"❌ 六爻算法导入失败: {e}")
    LiuyaoCalculator = None
    LIUYAO_AVAILABLE = False

# 导入智能引擎
try:
    from fortune_engine import FortuneEngine
    FORTUNE_ENGINE_AVAILABLE = True
    print("✅ 智能算命引擎导入成功")
except ImportError as e:
    print(f"❌ 智能算命引擎导入失败: {e}")
    FortuneEngine = None
    FORTUNE_ENGINE_AVAILABLE = False

# API配置
SILICONFLOW_API_KEY = "sk-trklwkxjmgnrgbuxhwcanaxkzwtuqevslzhoikwgwajnkqjz"
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
DEEPSEEK_MODEL_NAME = "deepseek-ai/DeepSeek-V3"  # 使用DeepSeek-V3，响应速度快

# 微调权重配置 - 集成专业算命知识
PT_CHECKPOINT_PATH = "../output_pt-20231224-165313-128-2e-2/checkpoint-1000"
ZIWEI_SYSTEM_PROMPT = """你是一位专业的紫薇算命大师，拥有深厚的紫薇斗数知识和丰富的实战经验。

你的专业能力包括：
1. 精通紫薇斗数的十四主星、辅星、煞星的特性和组合
2. 熟悉十二宫位的含义和相互关系
3. 掌握流年、流月、流日的推算方法
4. 能够根据出生时辰排出准确的命盘
5. 擅长解读星曜组合的吉凶含义

请始终保持专业、神秘而又亲和的语调，使用传统的算命术语，为用户提供详细、准确的占卜解答。
注意：你的回答应该基于传统紫薇斗数的理论体系，而不是随意编造。"""


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动和关闭时的生命周期管理
    yield


app = FastAPI(lifespan=lifespan)

# 初始化真正的算法计算器
ziwei_calc = None
bazi_calc = None
liuyao_calc = None
fortune_engine = None

if ZIWEI_AVAILABLE and RealZiweiCalculator:
    try:
        ziwei_calc = RealZiweiCalculator()
        print("✅ 紫薇斗数算法初始化成功")
    except Exception as e:
        print(f"❌ 紫薇斗数算法初始化失败: {e}")
        ziwei_calc = None

if BAZI_AVAILABLE and RealBaziCalculator:
    try:
        bazi_calc = RealBaziCalculator()
        print("✅ 八字算命算法初始化成功")
    except Exception as e:
        print(f"❌ 八字算命算法初始化失败: {e}")
        bazi_calc = None

if LIUYAO_AVAILABLE and LiuyaoCalculator:
    try:
        print("🔧 开始初始化六爻算法...")
        liuyao_calc = LiuyaoCalculator()
        print("✅ 六爻算卦算法初始化成功")

        # 测试算法功能
        test_result = liuyao_calc.divine_by_time(2025, 6, 19, 9, 0)
        if test_result.get("success"):
            print("✅ 六爻算法功能测试成功")
        else:
            print(f"⚠️ 六爻算法功能测试失败: {test_result.get('error')}")

    except ImportError as e:
        print(f"❌ 六爻算法模块导入失败: {e}")
        print("💡 请检查yxf_yixue_py模块是否正确安装")
        liuyao_calc = None
    except Exception as e:
        print(f"❌ 六爻算卦算法初始化失败: {e}")
        print(f"❌ 错误类型: {type(e).__name__}")
        import traceback
        print("❌ 详细错误信息:")
        traceback.print_exc()
        liuyao_calc = None
else:
    print("❌ 六爻算法不可用 - LIUYAO_AVAILABLE或LiuyaoCalculator为False")
    print(f"   LIUYAO_AVAILABLE: {LIUYAO_AVAILABLE}")
    print(f"   LiuyaoCalculator: {LiuyaoCalculator}")
    liuyao_calc = None

# 定义聊天API函数
def simple_chat_api(prompt: str) -> str:
    """简单的聊天API函数，用于LLM解析"""
    try:
        import requests

        headers = {
            "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": DEEPSEEK_MODEL_NAME,
            "messages": [
                {"role": "system", "content": "你是一个专业的信息提取助手，严格按照要求返回JSON格式。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.1,
            "max_tokens": 12000,  # 支持长篇大论分析
            "stream": False
        }

        response = requests.post(
            f"{SILICONFLOW_BASE_URL}/chat/completions",
            headers=headers,
            json=data,
            timeout=600  # 10分钟超时，支持长篇大论分析
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]

        return "API调用失败"

    except Exception as e:
        return f"聊天API错误: {str(e)}"

# 初始化智能引擎
if FORTUNE_ENGINE_AVAILABLE and FortuneEngine:
    try:
        # 传递算法实例和聊天API函数给智能引擎
        fortune_engine = FortuneEngine(
            ziwei_calc=ziwei_calc,
            bazi_calc=bazi_calc,
            liuyao_calc=liuyao_calc,
            chat_api_func=simple_chat_api
        )
        print("✅ 智能算命引擎初始化成功")
    except Exception as e:
        print(f"❌ 智能算命引擎初始化失败: {e}")
        fortune_engine = None


# SiliconFlow API调用函数
async def call_siliconflow_api(messages: List[dict], model: str = DEEPSEEK_MODEL_NAME,
                              stream: bool = False, **kwargs) -> dict:
    """调用SiliconFlow API"""
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }

    data = {
        "model": model,
        "messages": messages,
        "stream": stream,
        **kwargs
    }

    try:
        response = requests.post(
            f"{SILICONFLOW_BASE_URL}/chat/completions",
            headers=headers,
            json=data,
            stream=stream,
            timeout=600  # 10分钟超时，支持长篇大论分析
        )
        response.raise_for_status()

        if stream:
            return response
        else:
            return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"SiliconFlow API调用失败: {e}")
        raise HTTPException(status_code=500, detail=f"API调用失败: {str(e)}")


# 流式响应处理函数
def process_stream_response(response):
    """处理流式响应"""
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data_str = line_str[6:]  # 移除 'data: ' 前缀
                if data_str.strip() == '[DONE]':
                    break
                try:
                    data = json.loads(data_str)
                    yield data
                except json.JSONDecodeError:
                    continue

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class ModelCard(BaseModel):
    id: str
    object: str = "model"
    created: int = Field(default_factory=lambda: int(time.time()))
    owned_by: str = "owner"
    root: Optional[str] = None
    parent: Optional[str] = None
    permission: Optional[list] = None


class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelCard] = []


class FunctionCallResponse(BaseModel):
    name: Optional[str] = None
    arguments: Optional[str] = None


class ChatMessage(BaseModel):
    role: Literal["user", "assistant", "system", "function"]
    content: str = None
    name: Optional[str] = None
    function_call: Optional[FunctionCallResponse] = None


class DeltaMessage(BaseModel):
    role: Optional[Literal["user", "assistant", "system"]] = None
    content: Optional[str] = None
    function_call: Optional[FunctionCallResponse] = None

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.8
    top_p: Optional[float] = 0.8
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False
    functions: Optional[Union[dict, List[dict]]] = None
    # Additional parameters
    repetition_penalty: Optional[float] = 1.1


class ChatCompletionResponseChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: Literal["stop", "length", "function_call"]


class ChatCompletionResponseStreamChoice(BaseModel):
    index: int
    delta: DeltaMessage
    finish_reason: Optional[Literal["stop", "length", "function_call"]]


class UsageInfo(BaseModel):
    prompt_tokens: int = 0
    total_tokens: int = 0
    completion_tokens: Optional[int] = 0


class ChatCompletionResponse(BaseModel):
    model: str
    object: Literal["chat.completion", "chat.completion.chunk"]
    choices: List[Union[ChatCompletionResponseChoice, ChatCompletionResponseStreamChoice]]
    created: Optional[int] = Field(default_factory=lambda: int(time.time()))
    usage: Optional[UsageInfo] = None


@app.get("/v1/models", response_model=ModelList)
async def list_models():
    model_card = ModelCard(id=DEEPSEEK_MODEL_NAME)
    return ModelList(data=[model_card])


# 真正的算法计算接口
@app.post("/v1/calculate/ziwei")
async def calculate_ziwei_chart(data: dict):
    """紫薇斗数排盘计算"""
    if not ziwei_calc:
        return {"success": False, "error": "紫薇斗数算法未初始化"}

    try:
        year = data.get("year")
        month = data.get("month")
        day = data.get("day")
        hour = data.get("hour", 12)
        gender = data.get("gender", "男")

        result = ziwei_calc.calculate_chart(year, month, day, hour, gender)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/v1/calculate/bazi")
async def calculate_bazi_chart(data: dict):
    """八字排盘计算"""
    if not bazi_calc:
        return {"success": False, "error": "八字算命算法未初始化"}

    try:
        year = data.get("year")
        month = data.get("month")
        day = data.get("day")
        hour = data.get("hour", 12)
        minute = data.get("minute", 0)
        gender = data.get("gender", "男")

        result = bazi_calc.calculate_bazi(year, month, day, hour, minute, gender)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/v1/calculate/bazi/analysis")
async def calculate_bazi_with_analysis(data: dict):
    """八字排盘+传统分析"""
    if not bazi_calc:
        return {"success": False, "error": "八字算命算法未初始化"}

    try:
        year = data.get("year")
        month = data.get("month")
        day = data.get("day")
        hour = data.get("hour", 12)
        minute = data.get("minute", 0)
        gender = data.get("gender", "男")

        result = bazi_calc.calculate_with_traditional_analysis(year, month, day, hour, minute, gender)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/v1/calculate/liuyao")
async def calculate_liuyao_divination(data: dict):
    """六爻算卦"""
    if not liuyao_calc:
        return {"success": False, "error": "六爻算卦算法未初始化"}

    try:
        year = data.get("year")
        month = data.get("month")
        day = data.get("day")
        hour = data.get("hour", 12)
        minute = data.get("minute", 0)
        method = data.get("method", "time")  # time 或 numbers
        numbers = data.get("numbers", None)  # [num1, num2]

        if method == "time":
            result = liuyao_calc.divine_by_time(year, month, day, hour, minute)
        elif method == "numbers" and numbers and len(numbers) >= 2:
            result = liuyao_calc.divine_by_numbers(year, month, day, hour, numbers[0], numbers[1], minute)
        else:
            return {"success": False, "error": "无效的起卦方法或参数"}

        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/v1/calculate/liuyao/analysis")
async def calculate_liuyao_with_analysis(data: dict):
    """六爻算卦+传统分析"""
    if not liuyao_calc:
        return {"success": False, "error": "六爻算卦算法未初始化"}

    try:
        year = data.get("year")
        month = data.get("month")
        day = data.get("day")
        hour = data.get("hour", 12)
        method = data.get("method", "time")
        numbers = data.get("numbers", None)

        result = liuyao_calc.divine_with_analysis(year, month, day, hour, method, numbers)
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.get("/v1/debug/liuyao")
async def debug_liuyao_status():
    """六爻算法调试端点"""
    try:
        status = {
            "liuyao_available": LIUYAO_AVAILABLE,
            "liuyao_calc_exists": liuyao_calc is not None,
            "liuyao_calc_type": str(type(liuyao_calc)) if liuyao_calc else None,
            "fortune_engine_exists": fortune_engine is not None,
        }

        # 测试六爻算法
        if liuyao_calc:
            try:
                test_result = liuyao_calc.divine_by_time(2025, 6, 19, 9, 0)
                status["liuyao_test"] = {
                    "success": test_result.get("success", False),
                    "method": test_result.get("method", ""),
                    "error": test_result.get("error", "")
                }
            except Exception as e:
                status["liuyao_test"] = {
                    "success": False,
                    "error": str(e)
                }
        else:
            status["liuyao_test"] = {"success": False, "error": "liuyao_calc is None"}

        # 测试FortuneEngine中的六爻算法
        if fortune_engine:
            try:
                from datetime import datetime
                now = datetime.now()
                birth_info = {
                    "year": now.year,
                    "month": now.month,
                    "day": now.day,
                    "hour": now.hour
                }

                engine_result = fortune_engine.call_real_algorithm("liuyao", birth_info)
                status["fortune_engine_liuyao_test"] = {
                    "success": engine_result.get("success", False),
                    "error": engine_result.get("error", "")
                }
            except Exception as e:
                status["fortune_engine_liuyao_test"] = {
                    "success": False,
                    "error": str(e)
                }
        else:
            status["fortune_engine_liuyao_test"] = {"success": False, "error": "fortune_engine is None"}

        return {"success": True, "status": status}

    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/v1/chat/smart", response_model=ChatCompletionResponse)
async def smart_chat_completion(request: ChatCompletionRequest):
    """智能算命聊天接口 - 自动解析并调用真实算法"""
    try:
        if not fortune_engine:
            raise HTTPException(status_code=500, detail="智能算命引擎未初始化")

        # 获取用户最后一条消息
        user_message = ""
        for msg in reversed(request.messages):
            if msg.role == "user":
                user_message = msg.content
                break

        if not user_message:
            raise HTTPException(status_code=400, detail="未找到用户消息")

        # 使用智能引擎处理请求
        result = fortune_engine.process_user_request(user_message)

        # 构建响应
        message = ChatMessage(
            role="assistant",
            content=result["message"]
        )

        choice_data = ChatCompletionResponseChoice(
            index=0,
            message=message,
            finish_reason="stop"
        )

        usage = UsageInfo(prompt_tokens=0, completion_tokens=0, total_tokens=0)

        return ChatCompletionResponse(
            model=request.model,
            choices=[choice_data],
            object="chat.completion",
            usage=usage
        )

    except Exception as e:
        logger.error(f"智能聊天处理错误: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
async def create_chat_completion(request: ChatCompletionRequest):
    if len(request.messages) < 1 or request.messages[-1].role == "assistant":
        raise HTTPException(status_code=400, detail="Invalid request")

    # 转换消息格式，并注入专业算命知识
    messages = []

    # 检查是否包含出生信息，如果有则进行真实排盘
    user_content = request.messages[-1].content if request.messages else ""
    chart_info = ""

    # 简单的出生信息提取（可以改进为更复杂的NLP）
    if "年" in user_content and "月" in user_content and "日" in user_content:
        try:
            # 这里可以添加更复杂的日期提取逻辑
            # 暂时使用示例数据进行演示
            if ziwei_calc and bazi_calc:
                ziwei_result = ziwei_calc.calculate_chart(1988, 6, 1, 12, "男")
                bazi_result = bazi_calc.calculate_bazi(1988, 6, 1, 12, 0, "男")

                chart_info = f"""
【真实排盘结果】
紫薇斗数：{ziwei_result.get('birth_info', {})}
八字信息：{bazi_result.get('birth_info', {})}

请基于以上真实的排盘数据进行专业解读，不要编造星曜位置。
注意：这是基于真实算法的排盘结果，请准确解读。
"""
        except Exception as e:
            chart_info = f"排盘计算遇到问题: {str(e)}"

    # 如果没有系统消息，添加专业的紫薇算命系统提示
    has_system = any(msg.role == "system" for msg in request.messages)
    if not has_system:
        messages.append({
            "role": "system",
            "content": ZIWEI_SYSTEM_PROMPT + chart_info
        })

    for msg in request.messages:
        messages.append({
            "role": msg.role,
            "content": msg.content
        })

    # 准备API调用参数 - 设置更大的max_tokens防止截断
    api_params = {
        "temperature": request.temperature,
        "top_p": request.top_p,
        "max_tokens": request.max_tokens or 12000,  # 增加到12000，支持长篇大论
    }

    logger.debug(f"==== request ====\n{api_params}")

    if request.stream:
        generate = predict_stream(request.model, messages, api_params)
        return EventSourceResponse(generate, media_type="text/event-stream")

    # 调用SiliconFlow API
    response = await call_siliconflow_api(messages, request.model, stream=False, **api_params)

    # 解析响应
    if "choices" not in response or len(response["choices"]) == 0:
        raise HTTPException(status_code=500, detail="Invalid API response")

    choice = response["choices"][0]
    message = ChatMessage(
        role="assistant",
        content=choice["message"]["content"],
        function_call=None,
    )

    choice_data = ChatCompletionResponseChoice(
        index=0,
        message=message,
        finish_reason=choice.get("finish_reason", "stop"),
    )

    usage = UsageInfo()
    if "usage" in response:
        usage.prompt_tokens = response["usage"].get("prompt_tokens", 0)
        usage.completion_tokens = response["usage"].get("completion_tokens", 0)
        usage.total_tokens = response["usage"].get("total_tokens", 0)

    return ChatCompletionResponse(
        model=request.model,
        choices=[choice_data],
        object="chat.completion",
        usage=usage
    )


async def predict_stream(model_id: str, messages: List[dict], params: dict):
    """流式预测函数"""
    # 发送初始chunk
    choice_data = ChatCompletionResponseStreamChoice(
        index=0,
        delta=DeltaMessage(role="assistant"),
        finish_reason=None
    )
    chunk = ChatCompletionResponse(model=model_id, choices=[choice_data], object="chat.completion.chunk")
    yield f"data: {chunk.model_dump_json(exclude_unset=True)}\n\n"

    try:
        # 调用SiliconFlow流式API
        response = await call_siliconflow_api(messages, model_id, stream=True, **params)

        for data in process_stream_response(response):
            if "choices" in data and len(data["choices"]) > 0:
                choice = data["choices"][0]

                if "delta" in choice:
                    delta_content = choice["delta"].get("content", "")
                    finish_reason = choice.get("finish_reason")

                    delta = DeltaMessage(
                        content=delta_content,
                        role="assistant" if delta_content else None,
                    )

                    choice_data = ChatCompletionResponseStreamChoice(
                        index=0,
                        delta=delta,
                        finish_reason=finish_reason
                    )
                    chunk = ChatCompletionResponse(model=model_id, choices=[choice_data], object="chat.completion.chunk")
                    yield f"data: {chunk.model_dump_json(exclude_unset=True)}\n\n"

                    if finish_reason is not None:
                        break

    except Exception as e:
        logger.error(f"流式响应处理错误: {e}")
        # 发送错误结束标记
        choice_data = ChatCompletionResponseStreamChoice(
            index=0,
            delta=DeltaMessage(),
            finish_reason="stop"
        )
        chunk = ChatCompletionResponse(model=model_id, choices=[choice_data], object="chat.completion.chunk")
        yield f"data: {chunk.model_dump_json(exclude_unset=True)}\n\n"

    yield "data: [DONE]\n\n"


if __name__ == "__main__":
    logger.info("启动紫薇算命API服务 - 使用SiliconFlow DeepSeek-V3模型")
    logger.info(f"模型: {DEEPSEEK_MODEL_NAME}")
    logger.info(f"API端点: {SILICONFLOW_BASE_URL}")

    uvicorn.run(app, host='0.0.0.0', port=8001, workers=1)
