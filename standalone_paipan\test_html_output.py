#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试HTML输出
"""

import requests
import json
import re
from bs4 import BeautifulSoup

def test_html_output():
    """测试HTML输出"""
    base_url = "http://localhost:5000"
    
    print("=== 测试HTML输出 ===")
    
    try:
        # 获取最新的六爻记录
        response = requests.get(f"{base_url}/api/liuyao/list", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('records'):
                record = result['records'][0]
                liuyao_id = record['liuyao_id']
                
                # 获取详细结果
                detail_response = requests.get(f"{base_url}/api/liuyao/{liuyao_id}")
                if detail_response.status_code == 200:
                    detail_result = detail_response.json()
                    if detail_result.get('success'):
                        analysis_content = detail_result.get('analysis_content', '')
                        
                        # 提取详细表格
                        detail_match = re.search(r'【详细卦象】(.*?)【硬币投掷详情】', analysis_content, re.DOTALL)
                        if detail_match:
                            detail_content = detail_match.group(1).strip()
                            
                            # 模拟JavaScript函数
                            def formatHexagramTable(detailContent):
                                lines = [line for line in detailContent.split('\n') if line.strip() and '─' not in line and '┌' not in line and '└' not in line]
                                html = '<table class="hexagram-detail-table">'
                                
                                headerProcessed = False
                                
                                for index, line in enumerate(lines):
                                    # 跳过分隔线
                                    if '├' in line or '┼' in line:
                                        continue
                                    
                                    # 处理表头
                                    if not headerProcessed and '爻位' in line and '卦象' in line:
                                        headerMatch = re.match(r'│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│', line)
                                        if headerMatch:
                                            html += '<thead><tr>'
                                            for i in range(1, 8):
                                                html += f'<th>{headerMatch.group(i).strip()}</th>'
                                            html += '</tr></thead><tbody>'
                                            headerProcessed = True
                                        continue
                                    
                                    # 处理数据行
                                    if headerProcessed and '│' in line:
                                        tableMatch = re.match(r'│([^│]+)│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│\s*([^│]+)\s*│', line)
                                        
                                        if tableMatch:
                                            cells = [tableMatch.group(i).strip() for i in range(1, 8)]
                                            
                                            html += '<tr>'
                                            for cellIndex, cell in enumerate(cells):
                                                cellClass = ''
                                                cellContent = cell
                                                
                                                if cellIndex == 0:  # 爻位列
                                                    cellClass = 'yao-position'
                                                elif cellIndex == 1:  # 卦象列
                                                    cellClass = 'yao-symbol'
                                                    if '━━━━━━━━━' in cell:
                                                        cellContent = '<span class="yang-line">━━━━━━━━━</span>'
                                                    elif '━━━　━━━' in cell or '━━━ ━━━' in cell:
                                                        cellContent = '<span class="yin-line">━━━　━━━</span>'
                                                elif cellIndex == 2:  # 地支列
                                                    cellClass = 'dizhi'
                                                elif cellIndex == 3:  # 五行列
                                                    cellClass = 'wuxing'
                                                elif cellIndex == 4:  # 六亲列
                                                    cellClass = 'liuqin'
                                                elif cellIndex == 5:  # 六神列
                                                    cellClass = 'liushen'
                                                elif cellIndex == 6:  # 动静列
                                                    cellClass = 'moving-status'
                                                    if '动○' in cell or '动' in cell:
                                                        cellContent = '<span class="moving">动○</span>'
                                                    else:
                                                        cellContent = '<span class="static">静</span>'
                                                
                                                html += f'<td class="{cellClass}">{cellContent}</td>'
                                            html += '</tr>'
                                
                                html += '</tbody></table>'
                                return html
                            
                            # 生成HTML
                            html_output = formatHexagramTable(detail_content)
                            
                            print("生成的HTML:")
                            print("=" * 80)
                            print(html_output)
                            print("=" * 80)
                            
                            # 解析HTML检查结构
                            soup = BeautifulSoup(html_output, 'html.parser')
                            table = soup.find('table')
                            
                            if table:
                                print("\n表格结构分析:")
                                
                                # 检查表头
                                thead = table.find('thead')
                                if thead:
                                    headers = [th.get_text().strip() for th in thead.find_all('th')]
                                    print(f"表头: {headers}")
                                    print(f"表头列数: {len(headers)}")
                                
                                # 检查数据行
                                tbody = table.find('tbody')
                                if tbody:
                                    rows = tbody.find_all('tr')
                                    print(f"数据行数: {len(rows)}")
                                    
                                    for i, row in enumerate(rows):
                                        cells = [td.get_text().strip() for td in row.find_all('td')]
                                        print(f"第{i+1}行: {cells}")
                                        print(f"  列数: {len(cells)}")
                                        if len(cells) != 7:
                                            print(f"  ❌ 列数不匹配！期望7列，实际{len(cells)}列")
                                        else:
                                            print(f"  ✅ 列数正确")
                            else:
                                print("❌ 未找到表格")
                        else:
                            print("❌ 未找到详细卦象表格")
                    else:
                        print(f"❌ 获取详细结果失败: {detail_result.get('error')}")
                else:
                    print(f"❌ 详细结果请求失败: {detail_response.status_code}")
            else:
                print("❌ 没有找到六爻记录")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_html_output()
