#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置管理 - 统一管理所有配置项
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

@dataclass
class LLMConfig:
    """LLM配置"""
    api_key: str = ""
    base_url: str = "https://api.siliconflow.cn/v1"
    model_name: str = "deepseek-ai/DeepSeek-V3"
    timeout: int = 300  # 5分钟超时
    max_retries: int = 3
    temperature: float = 0.7
    max_tokens: int = 4096  # 降低到4096避免API限制导致截断

@dataclass
class SessionConfig:
    """会话配置"""
    max_history: int = 20
    session_timeout: int = 3600  # 1小时
    cleanup_interval: int = 300  # 5分钟清理一次
    max_sessions: int = 1000

@dataclass
class APIConfig:
    """API配置"""
    host: str = "0.0.0.0"
    port: int = 8002
    debug: bool = False
    cors_enabled: bool = True
    rate_limit: int = 100  # 每分钟请求数
    request_timeout: int = 60

@dataclass
class WebConfig:
    """Web界面配置"""
    title: str = "智能算命AI助手"
    icon: str = "🔮"
    layout: str = "wide"
    api_url: str = "http://localhost:8002"

@dataclass
class WeChatConfig:
    """微信配置"""
    token: str = ""
    app_id: str = ""
    app_secret: str = ""
    encoding_aes_key: str = ""
    port: int = 8003

@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/app.log"
    max_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

@dataclass
class SystemConfig:
    """系统总配置"""
    llm: LLMConfig
    session: SessionConfig
    api: APIConfig
    web: WebConfig
    wechat: WeChatConfig
    log: LogConfig

    # 系统设置
    environment: str = "development"  # development, production
    debug: bool = True
    version: str = "2.0.0"

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
        """
        # 加载.env文件
        load_dotenv()

        self.config_file = config_file or "config/config.json"
        self.config = self._load_config()

        logger.info("配置管理器初始化完成")

    def _load_config(self) -> SystemConfig:
        """加载配置"""

        # 默认配置
        default_config = SystemConfig(
            llm=LLMConfig(),
            session=SessionConfig(),
            api=APIConfig(),
            web=WebConfig(),
            wechat=WeChatConfig(),
            log=LogConfig()
        )

        # 尝试从文件加载
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 更新配置
                self._update_config_from_dict(default_config, config_data)
                logger.info(f"从文件加载配置: {self.config_file}")

            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")

        # 从环境变量加载
        self._load_from_env(default_config)

        return default_config

    def _update_config_from_dict(self, config: SystemConfig, data: Dict[str, Any]):
        """从字典更新配置"""

        for section_name, section_data in data.items():
            if hasattr(config, section_name) and isinstance(section_data, dict):
                section = getattr(config, section_name)
                for key, value in section_data.items():
                    if hasattr(section, key):
                        setattr(section, key, value)

    def _load_from_env(self, config: SystemConfig):
        """从环境变量加载配置"""

        # LLM配置
        if os.getenv("SILICONFLOW_API_KEY"):
            config.llm.api_key = os.getenv("SILICONFLOW_API_KEY")

        if os.getenv("LLM_MODEL_NAME"):
            config.llm.model_name = os.getenv("LLM_MODEL_NAME")

        if os.getenv("LLM_TIMEOUT"):
            config.llm.timeout = int(os.getenv("LLM_TIMEOUT"))

        # API配置
        if os.getenv("API_PORT"):
            config.api.port = int(os.getenv("API_PORT"))

        if os.getenv("API_HOST"):
            config.api.host = os.getenv("API_HOST")

        # 微信配置
        if os.getenv("WECHAT_TOKEN"):
            config.wechat.token = os.getenv("WECHAT_TOKEN")

        if os.getenv("WECHAT_APP_ID"):
            config.wechat.app_id = os.getenv("WECHAT_APP_ID")

        if os.getenv("WECHAT_APP_SECRET"):
            config.wechat.app_secret = os.getenv("WECHAT_APP_SECRET")

        # 环境设置
        if os.getenv("ENVIRONMENT"):
            config.environment = os.getenv("ENVIRONMENT")

        if os.getenv("DEBUG"):
            config.debug = os.getenv("DEBUG").lower() == "true"

    def save_config(self):
        """保存配置到文件"""

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

            # 转换为字典
            config_dict = asdict(self.config)

            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)

            logger.info(f"配置已保存到: {self.config_file}")

        except Exception as e:
            logger.error(f"保存配置失败: {e}")

    def get_config(self) -> SystemConfig:
        """获取配置"""
        return self.config

    def update_config(self, section: str, key: str, value: Any):
        """更新配置项"""

        if hasattr(self.config, section):
            section_obj = getattr(self.config, section)
            if hasattr(section_obj, key):
                setattr(section_obj, key, value)
                logger.info(f"配置已更新: {section}.{key} = {value}")
            else:
                logger.warning(f"配置项不存在: {section}.{key}")
        else:
            logger.warning(f"配置节不存在: {section}")

    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""

        issues = []

        # 验证LLM配置
        if not self.config.llm.api_key:
            issues.append("LLM API密钥未配置")

        if self.config.llm.timeout < 60:
            issues.append("LLM超时时间过短，建议至少60秒")

        # 验证端口配置
        if not (1024 <= self.config.api.port <= 65535):
            issues.append("API端口号无效")

        if not (1024 <= self.config.wechat.port <= 65535):
            issues.append("微信端口号无效")

        # 验证微信配置（如果启用）
        if self.config.wechat.token and not self.config.wechat.app_id:
            issues.append("微信Token已配置但缺少App ID")

        return {
            "valid": len(issues) == 0,
            "issues": issues
        }

    def get_env_template(self) -> str:
        """获取环境变量模板"""

        template = """# 智能算命AI系统环境变量配置

# LLM配置
SILICONFLOW_API_KEY=your_api_key_here
LLM_MODEL_NAME=deepseek-ai/DeepSeek-V3
LLM_TIMEOUT=300

# API配置
API_HOST=0.0.0.0
API_PORT=8002

# 微信配置（可选）
WECHAT_TOKEN=your_wechat_token
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# 系统配置
ENVIRONMENT=production
DEBUG=false
"""
        return template

    def create_env_file(self, file_path: str = ".env"):
        """创建环境变量文件"""

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.get_env_template())

            logger.info(f"环境变量模板已创建: {file_path}")

        except Exception as e:
            logger.error(f"创建环境变量文件失败: {e}")

# 全局配置实例
config_manager = ConfigManager()
config = config_manager.get_config()

def get_config() -> SystemConfig:
    """获取全局配置"""
    return config

def update_config(section: str, key: str, value: Any):
    """更新全局配置"""
    config_manager.update_config(section, key, value)

def validate_config() -> Dict[str, Any]:
    """验证全局配置"""
    return config_manager.validate_config()

if __name__ == "__main__":
    # 测试配置管理器
    print("配置管理器测试")
    print("=" * 50)

    # 显示当前配置
    print(f"LLM模型: {config.llm.model_name}")
    print(f"API端口: {config.api.port}")
    print(f"环境: {config.environment}")

    # 验证配置
    validation = validate_config()
    print(f"配置验证: {'通过' if validation['valid'] else '失败'}")
    if validation['issues']:
        for issue in validation['issues']:
            print(f"  - {issue}")

    # 创建环境变量模板
    config_manager.create_env_file()
    print("环境变量模板已创建")
