#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的排盘计算器
添加超时控制和错误隔离，防止系统崩溃
"""

import sys
import os
import json
import multiprocessing
import time
from typing import Dict, Any, Optional

# 添加当前模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def safe_calculate_worker(year: int, month: int, day: int, hour: int, gender: str, result_queue):
    """
    安全的计算工作进程
    在独立进程中执行计算，避免主进程崩溃
    """
    try:
        # 导入并执行计算
        from simple_interface import SimplePaipanInterface
        interface = SimplePaipanInterface()

        print(f"🔄 工作进程开始计算: {year}年{month}月{day}日{hour}时 {gender}")
        result = interface.calculate_and_save(year, month, day, hour, gender)

        print(f"✅ 工作进程计算完成: {result.get('success', False)}")
        result_queue.put(('success', result))

    except Exception as e:
        print(f"❌ 工作进程异常: {e}")
        import traceback
        traceback.print_exc()
        result_queue.put(('error', {'success': False, 'error': f'计算异常: {str(e)}'}))

class SafePaipanCalculator:
    """安全的排盘计算器"""

    def __init__(self):
        """初始化"""
        self.timeout = 90  # 总超时时间90秒
        print("🛡️ 安全排盘计算器已初始化")

    def calculate_with_timeout(self, year: int, month: int, day: int,
                              hour: int, gender: str = "男") -> Dict[str, Any]:
        """
        带超时的安全计算

        Args:
            year: 出生年份
            month: 出生月份
            day: 出生日期
            hour: 出生小时
            gender: 性别

        Returns:
            计算结果
        """
        print(f"🛡️ 安全计算开始: {year}年{month}月{day}日{hour}时 {gender}")

        try:
            # 创建结果队列
            result_queue = multiprocessing.Queue()

            # 创建工作进程
            worker_process = multiprocessing.Process(
                target=safe_calculate_worker,
                args=(year, month, day, hour, gender, result_queue)
            )

            # 启动进程
            worker_process.start()
            print("🔄 工作进程已启动")

            # 等待结果或超时
            worker_process.join(timeout=self.timeout)

            if worker_process.is_alive():
                # 进程仍在运行，强制终止
                print("⏰ 进程超时，强制终止")
                worker_process.terminate()
                worker_process.join(timeout=5)

                if worker_process.is_alive():
                    # 强制杀死进程
                    worker_process.kill()
                    worker_process.join()

                return {
                    'success': False,
                    'error': f'计算超时（{self.timeout}秒），请重试或检查输入数据'
                }

            # 获取结果
            if not result_queue.empty():
                status, result = result_queue.get()
                print(f"✅ 获取到结果: {status}")
                return result
            else:
                print("❌ 未获取到结果")
                return {
                    'success': False,
                    'error': '计算进程异常退出，未返回结果'
                }

        except Exception as e:
            print(f"❌ 安全计算异常: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': f'安全计算异常: {str(e)}'
            }

    def quick_calculate_with_timeout(self, birth_string: str, gender: str = "男") -> Dict[str, Any]:
        """
        快速计算的安全版本

        Args:
            birth_string: 出生时间字符串
            gender: 性别

        Returns:
            计算结果
        """
        try:
            # 解析时间字符串
            from simple_interface import SimplePaipanInterface
            interface = SimplePaipanInterface()
            year, month, day, hour = interface._parse_birth_string(birth_string)

            # 调用安全计算
            return self.calculate_with_timeout(year, month, day, hour, gender)

        except Exception as e:
            print(f"❌ 快速计算解析失败: {e}")
            return {
                'success': False,
                'error': f'时间格式解析失败: {str(e)}'
            }

def test_safe_calculator():
    """测试安全计算器"""
    print("🧪 测试安全计算器")

    calc = SafePaipanCalculator()

    # 测试正常计算
    result = calc.calculate_with_timeout(1990, 3, 15, 8, "女")
    print(f"测试结果: {result.get('success', False)}")

    if result.get('success'):
        print("✅ 安全计算器测试成功")
    else:
        print(f"❌ 安全计算器测试失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    test_safe_calculator()
