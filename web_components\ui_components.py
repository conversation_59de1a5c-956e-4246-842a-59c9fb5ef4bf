#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI组件模块 - 统一的界面组件和样式
"""

import streamlit as st
from typing import Dict, List, Optional
from datetime import datetime
import os
import json

class UIComponents:
    """UI组件类 - 提供统一的界面组件"""

    @staticmethod
    def apply_custom_css():
        """应用全新的现代化CSS样式"""
        st.markdown("""
        <style>
            /* 导入现代字体 */
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

            /* 全局样式重置 */
            * {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            /* 主体背景 - 现代渐变深色主题 */
            .main {
                background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
                color: #ffffff;
                padding: 0;
                min-height: 100vh;
            }
            .stApp {
                background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            }

            /* 主容器样式 */
            .block-container {
                padding: 1rem 2rem;
                max-width: none;
            }

            /* 标题样式 - 现代化设计 */
            h1 {
                background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #00ff00 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                font-size: 2.5rem !important;
                font-weight: 700 !important;
                margin: 1.5rem 0 !important;
                text-align: center;
                letter-spacing: -0.02em;
                line-height: 1.2;
            }
            h2 {
                color: #00f5ff;
                font-size: 1.8rem !important;
                font-weight: 600 !important;
                margin: 1.2rem 0 0.8rem 0 !important;
                position: relative;
                padding-left: 1.5rem;
            }
            h2::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 100%;
                background: linear-gradient(135deg, #00f5ff, #ff00ff);
                border-radius: 2px;
            }
            h3 {
                color: #ff6b6b;
                font-size: 1.3rem !important;
                font-weight: 500 !important;
                margin: 1rem 0 0.6rem 0 !important;
            }

            /* 卡片样式 - 玻璃拟态设计 */
            .modern-card {
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                padding: 2rem;
                margin: 1.5rem 0;
                box-shadow:
                    0 8px 32px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }
            .modern-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            }
            .modern-card:hover {
                transform: translateY(-8px) scale(1.02);
                box-shadow:
                    0 20px 60px rgba(0, 0, 0, 0.4),
                    0 0 40px rgba(0, 245, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                border-color: rgba(0, 245, 255, 0.3);
            }

            /* 状态标签 - 霓虹灯效果 */
            .status-badge {
                padding: 0.5rem 1rem;
                border-radius: 25px;
                font-size: 0.85rem;
                font-weight: 600;
                margin: 0 0.5rem;
                display: inline-block;
                position: relative;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;
            }
            .status-success {
                background: linear-gradient(135deg, #00ff88, #00cc6a);
                color: #000;
                box-shadow:
                    0 0 20px rgba(0, 255, 136, 0.5),
                    0 4px 15px rgba(0, 255, 136, 0.3);
            }
            .status-success:hover {
                box-shadow:
                    0 0 30px rgba(0, 255, 136, 0.8),
                    0 6px 20px rgba(0, 255, 136, 0.4);
                transform: translateY(-2px);
            }
            .status-warning {
                background: linear-gradient(135deg, #ffaa00, #ff8800);
                color: #000;
                box-shadow:
                    0 0 20px rgba(255, 170, 0, 0.5),
                    0 4px 15px rgba(255, 170, 0, 0.3);
            }
            .status-error {
                background: linear-gradient(135deg, #ff4757, #ff3742);
                color: #fff;
                box-shadow:
                    0 0 20px rgba(255, 71, 87, 0.5),
                    0 4px 15px rgba(255, 71, 87, 0.3);
            }
            .status-info {
                background: linear-gradient(135deg, #00f5ff, #0099cc);
                color: #000;
                box-shadow:
                    0 0 20px rgba(0, 245, 255, 0.5),
                    0 4px 15px rgba(0, 245, 255, 0.3);
            }

            /* 按钮样式 - 未来科技感 */
            .stButton > button {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 15px;
                padding: 0.8rem 2rem;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow:
                    0 8px 25px rgba(102, 126, 234, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
                position: relative;
                overflow: hidden;
            }
            .stButton > button::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }
            .stButton > button:hover::before {
                left: 100%;
            }
            .stButton > button:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-3px) scale(1.05);
                box-shadow:
                    0 15px 40px rgba(102, 126, 234, 0.4),
                    0 0 30px rgba(102, 126, 234, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            .stButton > button:active {
                transform: translateY(-1px) scale(1.02);
            }

            /* 输入框样式 - 玻璃拟态 */
            .stTextInput > div > div > input,
            .stSelectbox > div > div > select,
            .stTextArea > div > div > textarea,
            .stNumberInput > div > div > input {
                background: rgba(255, 255, 255, 0.05) !important;
                backdrop-filter: blur(10px) !important;
                color: #ffffff !important;
                border: 2px solid rgba(255, 255, 255, 0.1) !important;
                border-radius: 15px !important;
                padding: 1rem !important;
                font-size: 1rem !important;
                font-weight: 500 !important;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
                box-shadow:
                    0 4px 15px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
            }
            .stTextInput > div > div > input:focus,
            .stSelectbox > div > div > select:focus,
            .stTextArea > div > div > textarea:focus,
            .stNumberInput > div > div > input:focus {
                border-color: rgba(0, 245, 255, 0.6) !important;
                box-shadow:
                    0 0 30px rgba(0, 245, 255, 0.3),
                    0 8px 25px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
                background: rgba(255, 255, 255, 0.08) !important;
                transform: translateY(-2px);
            }
            .stTextInput > div > div > input::placeholder,
            .stTextArea > div > div > textarea::placeholder {
                color: rgba(255, 255, 255, 0.5) !important;
            }

            /* 进度条样式 - 霓虹灯效果 */
            .modern-progress {
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 25px;
                height: 30px;
                margin: 1rem 0;
                overflow: hidden;
                box-shadow:
                    inset 0 2px 10px rgba(0, 0, 0, 0.3),
                    0 4px 15px rgba(0, 0, 0, 0.2);
                position: relative;
            }
            .modern-progress::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            }
            .modern-progress-bar {
                background: linear-gradient(90deg, #00f5ff 0%, #ff00ff 50%, #00ff00 100%);
                height: 100%;
                text-align: center;
                line-height: 30px;
                color: #000;
                font-size: 0.9rem;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 1px;
                transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow:
                    0 0 20px rgba(0, 245, 255, 0.5),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                position: relative;
                overflow: hidden;
            }
            .modern-progress-bar::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                animation: shimmer 2s infinite;
            }
            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }

            /* 侧边栏样式 - 玻璃拟态 */
            .sidebar-container {
                background: rgba(255, 255, 255, 0.03);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.08);
                border-radius: 20px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow:
                    0 8px 32px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.05);
                transition: all 0.3s ease;
            }
            .sidebar-container:hover {
                border-color: rgba(0, 245, 255, 0.2);
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.4),
                    0 0 20px rgba(0, 245, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
            }

            /* 滚动条样式 */
            ::-webkit-scrollbar {
                width: 8px;
            }
            ::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 10px;
            }
            ::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, #00f5ff, #ff00ff);
                border-radius: 10px;
                box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
            }
            ::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(135deg, #00ccff, #cc00cc);
            }

            /* 隐藏Streamlit默认元素 */
            .stDeployButton { display: none !important; }
            .stDecoration { display: none !important; }
            header[data-testid="stHeader"] { display: none !important; }
            .stMainBlockContainer { padding-top: 1rem !important; }

            /* 响应式设计 */
            @media (max-width: 1200px) {
                .block-container { padding: 1rem; }
                h1 { font-size: 2rem !important; }
                h2 { font-size: 1.5rem !important; }
                .modern-card { padding: 1.5rem; margin: 1rem 0; }
            }
            @media (max-width: 768px) {
                .block-container { padding: 0.5rem; }
                h1 { font-size: 1.8rem !important; }
                h2 { font-size: 1.3rem !important; }
                .modern-card { padding: 1rem; margin: 0.5rem 0; }
                .sidebar-container { padding: 1rem; margin: 0.5rem 0; }
            }
            @media (max-width: 480px) {
                h1 { font-size: 1.5rem !important; }
                h2 { font-size: 1.1rem !important; }
                .modern-card { padding: 0.8rem; }
                .stButton > button { padding: 0.6rem 1.5rem; font-size: 0.9rem; }
            }

            /* 动画效果 */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-50px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(50px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            .modern-card {
                animation: fadeInUp 0.6s ease-out;
            }
            .sidebar-container {
                animation: slideInLeft 0.8s ease-out;
            }

            /* 加载动画 */
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            @keyframes glow-pulse {
                0%, 100% {
                    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
                }
                50% {
                    box-shadow: 0 0 40px rgba(0, 245, 255, 0.6), 0 0 60px rgba(255, 0, 255, 0.4);
                }
            }
            .loading-pulse {
                animation: pulse 2s infinite;
            }
            .glow-effect {
                animation: glow-pulse 3s ease-in-out infinite;
            }

            /* 粒子效果背景 */
            .particle-bg {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: -1;
            }
            .particle {
                position: absolute;
                width: 2px;
                height: 2px;
                background: #00f5ff;
                border-radius: 50%;
                animation: float 6s ease-in-out infinite;
                opacity: 0.6;
            }
            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(180deg); }
            }
        </style>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_status_badge(status: str, text: str) -> str:
        """渲染状态标签"""
        status_class = f"status-{status}"
        return f'<span class="status-badge {status_class}">{text}</span>'

    @staticmethod
    def render_modern_card(title: str, content: str, icon: str = "📋") -> None:
        """渲染现代化玻璃拟态卡片"""
        st.markdown(f"""
        <div class="modern-card">
            <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                <span style="font-size: 1.5rem; margin-right: 0.8rem;
                           filter: drop-shadow(0 0 10px rgba(0, 245, 255, 0.5));">{icon}</span>
                <h3 style="margin: 0; color: #00f5ff; font-weight: 600;">{title}</h3>
            </div>
            <div style="color: rgba(255, 255, 255, 0.9); line-height: 1.6;">{content}</div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_cyber_card(title: str, content: str, icon: str = "📋", accent_color: str = "#00f5ff") -> None:
        """渲染赛博朋克风格卡片"""
        st.markdown(f"""
        <div style="background: rgba(255, 255, 255, 0.02); backdrop-filter: blur(20px);
                    border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 20px;
                    padding: 2rem; margin: 1.5rem 0; position: relative; overflow: hidden;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);"
             onmouseover="this.style.transform='translateY(-8px) scale(1.02)';
                         this.style.boxShadow='0 20px 60px rgba(0, 0, 0, 0.4), 0 0 40px {accent_color}20, inset 0 1px 0 rgba(255, 255, 255, 0.2)';
                         this.style.borderColor='{accent_color}50';"
             onmouseout="this.style.transform='translateY(0) scale(1)';
                        this.style.boxShadow='0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)';
                        this.style.borderColor='rgba(255, 255, 255, 0.1)';">

            <!-- 顶部光线效果 -->
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 1px;
                        background: linear-gradient(90deg, transparent, {accent_color}80, transparent);"></div>

            <!-- 标题区域 -->
            <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                <span style="font-size: 2rem; margin-right: 1rem;
                           filter: drop-shadow(0 0 15px {accent_color}80);">{icon}</span>
                <h3 style="margin: 0; color: {accent_color}; font-weight: 700; font-size: 1.3rem;
                           text-transform: uppercase; letter-spacing: 1px;">{title}</h3>
            </div>

            <!-- 内容区域 -->
            <div style="color: rgba(255, 255, 255, 0.9); line-height: 1.8; font-size: 1rem;">
                {content}
            </div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_progress_bar(progress: float, text: str = "") -> None:
        """渲染现代化进度条"""
        progress_percent = min(100, max(0, progress * 100))
        display_text = text or f"{progress_percent:.1f}%"

        st.markdown(f"""
        <div class="modern-progress">
            <div class="modern-progress-bar" style="width: {progress_percent}%">
                {display_text}
            </div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_metric_card(title: str, value: str, icon: str = "📊", color: str = "#00f5ff") -> None:
        """渲染炫酷的指标卡片"""
        st.markdown(f"""
        <div style="background: rgba(255, 255, 255, 0.03); backdrop-filter: blur(20px);
                    border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 20px;
                    padding: 2rem; margin: 1rem 0; text-align: center; position: relative;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); overflow: hidden;"
             onmouseover="this.style.transform='translateY(-5px) scale(1.05)';
                         this.style.boxShadow='0 15px 40px rgba(0, 0, 0, 0.4), 0 0 30px {color}30';
                         this.style.borderColor='{color}50';"
             onmouseout="this.style.transform='translateY(0) scale(1)';
                        this.style.boxShadow='0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)';
                        this.style.borderColor='rgba(255, 255, 255, 0.1)';">

            <!-- 背景光效 -->
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                        width: 100px; height: 100px; border-radius: 50%;
                        background: radial-gradient(circle, {color}20 0%, transparent 70%);
                        animation: pulse 3s ease-in-out infinite;"></div>

            <!-- 图标 -->
            <div style="font-size: 3rem; margin-bottom: 1rem; position: relative; z-index: 1;
                        filter: drop-shadow(0 0 20px {color}80);">{icon}</div>

            <!-- 数值 -->
            <div style="font-size: 2.5rem; color: {color}; font-weight: 700;
                        margin-bottom: 0.5rem; position: relative; z-index: 1;
                        text-shadow: 0 0 20px {color}50;">{value}</div>

            <!-- 标题 -->
            <div style="font-size: 1rem; color: rgba(255, 255, 255, 0.7);
                        font-weight: 500; text-transform: uppercase;
                        letter-spacing: 1px; position: relative; z-index: 1;">{title}</div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_dashboard_grid(metrics: List[Dict]) -> None:
        """渲染仪表盘网格"""
        cols = st.columns(len(metrics))

        for i, metric in enumerate(metrics):
            with cols[i]:
                UIComponents.render_metric_card(
                    title=metric.get('title', ''),
                    value=metric.get('value', '0'),
                    icon=metric.get('icon', '📊'),
                    color=metric.get('color', '#00f5ff')
                )

    @staticmethod
    def render_loading_spinner(text: str = "处理中...") -> None:
        """渲染加载动画"""
        st.markdown(f"""
        <div style="text-align: center; padding: 2rem;">
            <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #4CAF50; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <div style="margin-top: 1rem; color: #4CAF50; font-weight: bold;">{text}</div>
        </div>
        <style>
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        </style>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_alert(message: str, alert_type: str = "info", icon: str = "ℹ️") -> None:
        """渲染炫酷的警告框"""
        colors = {
            "success": "#00ff88",
            "warning": "#ffaa00",
            "error": "#ff6b6b",
            "info": "#00f5ff"
        }
        color = colors.get(alert_type, "#00f5ff")

        st.markdown(f"""
        <div style="background: rgba(255, 255, 255, 0.03); backdrop-filter: blur(20px);
                    border: 1px solid {color}50; border-left: 4px solid {color};
                    border-radius: 15px; padding: 1.5rem; margin: 1.5rem 0;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px {color}20;
                    transition: all 0.3s ease;">
            <div style="display: flex; align-items: center;">
                <span style="font-size: 1.5rem; margin-right: 1rem;
                           filter: drop-shadow(0 0 10px {color}80);">{icon}</span>
                <span style="color: {color}; font-weight: 600; font-size: 1.1rem;">{message}</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_particle_background() -> None:
        """渲染粒子背景效果"""
        st.markdown("""
        <div class="particle-bg">
            <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
            <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
            <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
            <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
            <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
            <div class="particle" style="left: 70%; animation-delay: 1.5s;"></div>
            <div class="particle" style="left: 80%; animation-delay: 2.5s;"></div>
            <div class="particle" style="left: 90%; animation-delay: 3.5s;"></div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_cyber_button(text: str, key: str, color: str = "#00f5ff") -> bool:
        """渲染赛博朋克风格按钮"""
        button_html = f"""
        <div style="margin: 1rem 0;">
            <button id="cyber-btn-{key}"
                    style="background: linear-gradient(135deg, {color}20, {color}10);
                           border: 2px solid {color}50; color: {color};
                           padding: 1rem 2rem; border-radius: 15px;
                           font-size: 1rem; font-weight: 600;
                           text-transform: uppercase; letter-spacing: 1px;
                           cursor: pointer; transition: all 0.3s ease;
                           box-shadow: 0 0 20px {color}30;
                           backdrop-filter: blur(10px);"
                    onmouseover="this.style.background='linear-gradient(135deg, {color}40, {color}20)';
                                this.style.boxShadow='0 0 30px {color}60';
                                this.style.transform='translateY(-2px) scale(1.05)';"
                    onmouseout="this.style.background='linear-gradient(135deg, {color}20, {color}10)';
                               this.style.boxShadow='0 0 20px {color}30';
                               this.style.transform='translateY(0) scale(1)';">
                {text}
            </button>
        </div>
        """
        st.markdown(button_html, unsafe_allow_html=True)
        return st.button(text, key=key, help="点击执行操作")

    @staticmethod
    def render_main_title() -> None:
        """渲染主标题"""
        st.markdown("""
        <div style="text-align: center; margin: 2rem 0 3rem 0;">
            <h1 style="background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #00ff00 100%);
                       -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                       background-clip: text; font-size: 3.5rem; font-weight: 800;
                       margin: 0; letter-spacing: -0.02em; line-height: 1.1;
                       text-shadow: 0 0 50px rgba(0, 245, 255, 0.5);
                       animation: glow-pulse 3s ease-in-out infinite;">
                🔮 紫薇斗数智能分析系统
            </h1>
            <div style="margin-top: 1rem;">
                <span class="status-badge status-success">AI驱动</span>
                <span class="status-badge status-info">实时分析</span>
                <span class="status-badge status-warning">专业算法</span>
            </div>
            <p style="color: rgba(255, 255, 255, 0.7); font-size: 1.2rem; margin-top: 1.5rem;
                      font-weight: 300; letter-spacing: 0.5px;">
                基于传统紫薇斗数与现代AI技术的智能命理分析平台
            </p>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_sidebar() -> None:
        """渲染侧边栏"""
        st.markdown("""
        <div class="sidebar-container">
            <h3 style="color: #00f5ff; margin-bottom: 1.5rem; text-align: center;
                       font-size: 1.3rem; font-weight: 600;">
                🛠️ 智能控制中心
            </h3>
        </div>
        """, unsafe_allow_html=True)

        # 功能导航
        st.markdown("#### 📋 功能导航")

        nav_options = [
            ("📊", "系统概览"),
            ("🆕", "创建分析"),
            ("📋", "分析记录"),
            ("💕", "合盘分析"),
            ("🔮", "六爻占卜"),
            ("📈", "实时监控"),
            ("📊", "数据导出"),
            ("⚙️", "系统设置")
        ]

        for icon, label in nav_options:
            if st.button(f"{icon} {label}", use_container_width=True):
                st.info(f"功能 {label} 正在开发中...")

        st.markdown("---")

        # 系统状态
        st.markdown("#### 📈 系统状态")
        UIComponents.render_metric_card("在线用户", "1", "👤", "#00ff88")
        UIComponents.render_metric_card("今日分析", "0", "📊", "#ffaa00")

        st.markdown("---")

        # 快速操作
        st.markdown("#### ⚡ 快速操作")
        if st.button("🔄 刷新数据", use_container_width=True):
            st.success("数据已刷新")
        if st.button("🧹 清理缓存", use_container_width=True):
            st.success("缓存已清理")

    @staticmethod
    def render_main_content() -> None:
        """渲染主内容区域"""
        # 系统概览仪表盘
        st.markdown("## 📊 系统概览")

        # 指标卡片
        metrics = [
            {"title": "系统状态", "value": "正常", "icon": "🟢", "color": "#00ff88"},
            {"title": "算法引擎", "value": "就绪", "icon": "🔮", "color": "#00f5ff"},
            {"title": "数据库", "value": "连接", "icon": "💾", "color": "#ffaa00"},
            {"title": "API服务", "value": "运行", "icon": "🚀", "color": "#ff6b6b"}
        ]

        UIComponents.render_dashboard_grid(metrics)

        # 功能介绍
        st.markdown("---")
        st.markdown("## 🎯 核心功能")

        col1, col2 = st.columns(2)

        with col1:
            UIComponents.render_cyber_card(
                "紫薇斗数分析",
                "基于传统紫薇斗数理论，结合现代AI技术，提供精准的命理分析。支持十二宫位详细解读，星曜组合分析，流年运势预测。",
                "🔮",
                "#00f5ff"
            )

        with col2:
            UIComponents.render_cyber_card(
                "八字命理分析",
                "运用传统八字命理学说，分析五行生克制化，十神关系，大运流年，为用户提供全面的命运指导。",
                "📜",
                "#ff00ff"
            )

        col3, col4 = st.columns(2)

        with col3:
            UIComponents.render_cyber_card(
                "合盘分析",
                "专业的双人命盘对比分析，评估感情匹配度，事业合作潜力，为人际关系提供科学指导。",
                "💕",
                "#00ff88"
            )

        with col4:
            UIComponents.render_cyber_card(
                "六爻占卜",
                "传统六爻占卜系统，针对具体问题进行专业卦象分析，提供决策参考和趋势预测。",
                "🎯",
                "#ffaa00"
            )

class NavigationManager:
    """导航管理器"""

    @staticmethod
    def init_session_state():
        """初始化会话状态"""
        if 'current_view' not in st.session_state:
            st.session_state.current_view = 'overview'
        if 'selected_record' not in st.session_state:
            st.session_state.selected_record = None
        if 'last_refresh' not in st.session_state:
            st.session_state.last_refresh = datetime.now()

    @staticmethod
    def navigate_to(view: str, record_id: str = None):
        """导航到指定页面"""
        st.session_state.current_view = view
        if record_id:
            st.session_state.selected_record = record_id
        st.rerun()

    @staticmethod
    def get_current_view() -> str:
        """获取当前视图"""
        return st.session_state.get('current_view', 'overview')

class DataManager:
    """数据管理器 - 统一的数据访问接口"""

    @staticmethod
    def get_cache_records() -> List[Dict]:
        """获取缓存记录 - 优化版本"""
        try:
            cache_dir = 'data/calculation_cache'
            if not os.path.exists(cache_dir):
                return []

            records = []
            for filename in os.listdir(cache_dir):
                if filename.endswith('.json') and filename != 'index.json':
                    filepath = os.path.join(cache_dir, filename)
                    try:
                        record = DataManager._parse_cache_file(filepath, filename)
                        if record:
                            records.append(record)
                    except Exception:
                        continue

            # 按创建时间排序
            records.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return records

        except Exception:
            return []

    @staticmethod
    def _parse_cache_file(filepath: str, filename: str) -> Optional[Dict]:
        """解析缓存文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            birth_info = data.get('birth_info', {})
            detailed_analysis = data.get('detailed_analysis', {})
            calculation_type = data.get('calculation_type', 'ziwei')

            # 计算完成状态
            completed_angles, total_words = DataManager._calculate_completion(
                detailed_analysis, calculation_type
            )

            # 构建显示信息
            birth_display = DataManager._format_birth_display(
                birth_info, calculation_type
            )

            return {
                'result_id': data.get('result_id', filename[:-5]),
                'birth_info': birth_display,
                'created_at': data.get('created_at', '未知')[:19],
                'has_analysis': completed_angles > 0,
                'completed_angles': completed_angles,
                'total_words': total_words,
                'chart_exists': DataManager._check_chart_exists(data),
                'raw_birth_info': birth_info,
                'calculation_type': calculation_type
            }

        except Exception:
            return None

    @staticmethod
    def _calculate_completion(detailed_analysis: Dict, calculation_type: str) -> tuple:
        """计算完成状态"""
        if calculation_type in ['compatibility', 'liuyao']:
            # 合盘分析和六爻占卜
            if calculation_type == 'compatibility':
                content = detailed_analysis.get('compatibility_analysis', '')
            else:
                content = detailed_analysis.get('liuyao_analysis', '')

            if content and content.strip():
                return 1, len(content)
            return 0, 0
        else:
            # 命理分析
            if isinstance(detailed_analysis, dict):
                angle_analyses = detailed_analysis.get('angle_analyses', {})
                completed = len([v for v in angle_analyses.values() if v and len(v) > 100])
                total_words = sum(len(v) for v in angle_analyses.values() if v)
                return completed, total_words
            return 0, 0

    @staticmethod
    def _format_birth_display(birth_info: Dict, calculation_type: str) -> str:
        """格式化生辰显示"""
        if calculation_type == 'compatibility':
            person_a = birth_info.get('person_a', '')
            person_b = birth_info.get('person_b', '')
            analysis_dimension = birth_info.get('analysis_dimension', '')

            name_a = person_a.split('(')[0] if person_a else 'A'
            name_b = person_b.split('(')[0] if person_b else 'B'

            display = f"💕 {name_a} & {name_b} - {analysis_dimension}"
            return display[:40] + "..." if len(display) > 40 else display

        elif calculation_type == 'liuyao':
            question = birth_info.get('question', '未知问题')
            gender = birth_info.get('gender', '未知')

            if len(question) > 20:
                question = question[:20] + "..."

            return f"🔮 {question} ({gender})"
        else:
            return f"{birth_info.get('year', '?')}年{birth_info.get('month', '?')}月{birth_info.get('day', '?')}日 {birth_info.get('hour', '?')} {birth_info.get('gender', '?')}命"

    @staticmethod
    def _check_chart_exists(data: Dict) -> bool:
        """检查图表是否存在"""
        chart_path = data.get('chart_image_path')
        if chart_path and os.path.exists(chart_path):
            return True

        # 检查charts目录
        charts_dir = "charts"
        if os.path.exists(charts_dir):
            chart_files = [f for f in os.listdir(charts_dir)
                          if f.startswith("integrated_chart_") and f.endswith(".png")]
            return len(chart_files) > 0

        return False
