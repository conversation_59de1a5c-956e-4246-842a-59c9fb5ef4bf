#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
意图识别器 - 基于LLM的智能语义理解
"""

import json
import re
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class IntentRecognizer:
    """意图识别器 - 智能理解用户意图并提取关键信息"""
    
    def __init__(self, llm_client, prompt_manager):
        """
        初始化意图识别器
        
        Args:
            llm_client: LLM客户端
            prompt_manager: 提示词管理器
        """
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
        
        # 支持的工具类型
        self.supported_tools = ["ziwei", "bazi", "liuyao", "comprehensive", "general"]
        
        # 支持的问题类型
        self.supported_questions = ["career", "love", "wealth", "health", "fortune", "general"]
        
        logger.info("意图识别器初始化完成")
    
    def recognize_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        识别用户意图
        
        Args:
            message: 用户消息
            context: 对话上下文
            
        Returns:
            意图识别结果
        """
        logger.debug(f"开始意图识别 - 消息: {message[:50]}...")
        
        try:
            # 1. 基础意图识别
            basic_intent = self._recognize_basic_intent(message, context)
            
            # 2. 提取实体信息
            entities = self._extract_entities(message, context)
            
            # 3. 上下文增强
            enhanced_intent = self._enhance_with_context(basic_intent, entities, context)
            
            # 4. 验证和修正
            final_intent = self._validate_and_correct(enhanced_intent, context)
            
            logger.debug(f"意图识别完成 - 工具: {final_intent.get('tool_name')}, 类型: {final_intent.get('question_type')}")
            
            return final_intent
            
        except Exception as e:
            logger.error(f"意图识别失败: {e}")
            return self._create_fallback_intent(message, context)
    
    def _recognize_basic_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """基础意图识别"""
        
        # 构建上下文感知的提示词
        prompt = self._build_intent_recognition_prompt(message, context)
        
        # 调用LLM进行意图识别
        response = self.llm_client.chat(prompt, temperature=0.1)
        
        # 解析LLM响应
        intent = self._parse_intent_response(response)
        
        return intent
    
    def _build_intent_recognition_prompt(self, message: str, context: Dict[str, Any]) -> str:
        """构建意图识别提示词"""
        
        # 获取对话历史
        recent_history = context.get("recent_history", [])
        last_analysis_type = context.get("last_analysis_type")
        tools_used = context.get("tools_used", [])
        
        prompt = f"""请分析用户消息并识别意图，返回JSON格式结果。

【用户消息】
{message}

【对话上下文】
- 历史消息数: {len(recent_history)}
- 上次分析类型: {last_analysis_type or '无'}
- 已使用工具: {', '.join(tools_used) if tools_used else '无'}

【工具类型说明】
- ziwei: 紫薇斗数 (关键词: 紫薇、斗数、命宫、星曜、排盘)
- bazi: 八字算命 (关键词: 八字、四柱、天干、地支、合婚)
- liuyao: 六爻算卦 (关键词: 算卦、占卜、起卦、卦象、六爻)
- comprehensive: 综合分析 (关键词: 运势、命运、算命、综合)
- general: 一般对话 (问候、闲聊、咨询等)

【问题类型说明】
- career: 事业工作 (关键词: 事业、工作、职业、升职)
- love: 感情婚姻 (关键词: 感情、婚姻、恋爱、配偶)
- wealth: 财运投资 (关键词: 财运、财富、投资、赚钱)
- health: 健康身体 (关键词: 健康、身体、疾病)
- fortune: 运势流年 (关键词: 运势、今年、明年、流年)
- general: 综合分析 (其他或不明确)

【特殊情况处理】
1. 如果用户说"继续"、"换个角度"等，工具类型使用上次的: {last_analysis_type or 'comprehensive'}
2. 如果用户提供出生信息，优先识别为算命相关工具
3. 如果用户问卦相关，优先识别为liuyao

请返回JSON格式：
{{
  "tool_name": "工具名称",
  "question_type": "问题类型", 
  "confidence": 0.95,
  "reasoning": "识别理由"
}}"""

        return prompt
    
    def _parse_intent_response(self, response: str) -> Dict[str, Any]:
        """解析意图识别响应"""
        
        # 尝试解析JSON
        json_result = self.llm_client.parse_json_response(response)
        if json_result:
            tool_name = json_result.get("tool_name", "general")
            question_type = json_result.get("question_type", "general")
            
            # 验证工具名称
            if tool_name not in self.supported_tools:
                tool_name = "general"
            
            # 验证问题类型
            if question_type not in self.supported_questions:
                question_type = "general"
            
            return {
                "tool_name": tool_name,
                "question_type": question_type,
                "confidence": json_result.get("confidence", 0.8),
                "reasoning": json_result.get("reasoning", "LLM识别"),
                "raw_response": response
            }
        
        # 备用关键词匹配
        return self._fallback_keyword_matching(response)
    
    def _fallback_keyword_matching(self, text: str) -> Dict[str, Any]:
        """备用关键词匹配"""
        
        text_lower = text.lower()
        
        # 工具类型匹配
        tool_name = "general"
        if any(keyword in text_lower for keyword in ["紫薇", "斗数", "命宫", "星曜"]):
            tool_name = "ziwei"
        elif any(keyword in text_lower for keyword in ["八字", "四柱", "天干", "地支"]):
            tool_name = "bazi"
        elif any(keyword in text_lower for keyword in ["算卦", "占卜", "起卦", "卦象", "六爻"]):
            tool_name = "liuyao"
        elif any(keyword in text_lower for keyword in ["运势", "命运", "算命"]):
            tool_name = "comprehensive"
        
        # 问题类型匹配
        question_type = "general"
        if any(keyword in text_lower for keyword in ["事业", "工作", "职业"]):
            question_type = "career"
        elif any(keyword in text_lower for keyword in ["感情", "婚姻", "恋爱"]):
            question_type = "love"
        elif any(keyword in text_lower for keyword in ["财运", "财富", "投资"]):
            question_type = "wealth"
        elif any(keyword in text_lower for keyword in ["健康", "身体"]):
            question_type = "health"
        elif any(keyword in text_lower for keyword in ["运势", "今年", "明年"]):
            question_type = "fortune"
        
        return {
            "tool_name": tool_name,
            "question_type": question_type,
            "confidence": 0.6,
            "reasoning": "关键词匹配",
            "raw_response": text
        }
    
    def _extract_entities(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """提取实体信息"""
        
        entities = {}
        
        # 提取出生信息
        birth_info = self._extract_birth_info(message, context)
        if birth_info:
            entities["birth_info"] = birth_info
        
        # 提取时间信息
        time_info = self._extract_time_info(message)
        if time_info:
            entities["time_info"] = time_info
        
        # 提取其他实体
        entities.update(self._extract_other_entities(message))
        
        return entities
    
    def _extract_birth_info(self, message: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取出生信息"""
        
        # 如果上下文中已有出生信息，优先使用
        if context.get("birth_info"):
            return context["birth_info"]
        
        # 使用LLM提取出生信息
        if any(keyword in message for keyword in ["年", "月", "日", "时", "生于"]):
            prompt = f"""请从以下文本中提取出生信息，返回JSON格式：

文本：{message}

传统时辰对应表：
子时=0, 丑时=2, 寅时=4, 卯时=6, 辰时=8, 巳时=10, 
午时=12, 未时=14, 申时=16, 酉时=18, 戌时=20, 亥时=22

返回格式：
{{
  "year": 年份,
  "month": 月份,
  "day": 日期,
  "hour": 小时,
  "gender": "性别"
}}

如果无法提取完整信息，返回null。"""
            
            response = self.llm_client.chat(prompt, temperature=0.1)
            birth_info = self.llm_client.parse_json_response(response)
            
            if birth_info and self._validate_birth_info(birth_info):
                return birth_info
        
        return None
    
    def _validate_birth_info(self, birth_info: Dict[str, Any]) -> bool:
        """验证出生信息的有效性"""
        try:
            year = birth_info.get("year")
            month = birth_info.get("month")
            day = birth_info.get("day")
            hour = birth_info.get("hour")
            gender = birth_info.get("gender")
            
            return (
                isinstance(year, int) and 1900 <= year <= 2030 and
                isinstance(month, int) and 1 <= month <= 12 and
                isinstance(day, int) and 1 <= day <= 31 and
                isinstance(hour, int) and 0 <= hour <= 23 and
                gender in ["男", "女"]
            )
        except:
            return False
    
    def _extract_time_info(self, message: str) -> Optional[Dict[str, Any]]:
        """提取时间信息"""
        time_keywords = ["今年", "明年", "今天", "明天", "这个月", "下个月"]
        
        for keyword in time_keywords:
            if keyword in message:
                return {"time_reference": keyword}
        
        return None
    
    def _extract_other_entities(self, message: str) -> Dict[str, Any]:
        """提取其他实体"""
        entities = {}
        
        # 提取情感倾向
        positive_words = ["好", "顺利", "成功", "幸福", "发财"]
        negative_words = ["不好", "困难", "失败", "痛苦", "破财"]
        
        if any(word in message for word in positive_words):
            entities["sentiment"] = "positive"
        elif any(word in message for word in negative_words):
            entities["sentiment"] = "negative"
        else:
            entities["sentiment"] = "neutral"
        
        return entities
    
    def _enhance_with_context(self, basic_intent: Dict[str, Any], 
                            entities: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """使用上下文增强意图"""
        
        enhanced_intent = basic_intent.copy()
        enhanced_intent["entities"] = entities
        
        # 上下文相关性增强
        if context.get("last_analysis_type") and basic_intent["tool_name"] == "general":
            # 如果用户说"继续"等，使用上次的工具类型
            if any(keyword in enhanced_intent.get("raw_response", "").lower() 
                   for keyword in ["继续", "换个", "再看", "还有"]):
                enhanced_intent["tool_name"] = context["last_analysis_type"]
                enhanced_intent["reasoning"] += " (基于上下文继续)"
        
        # 出生信息增强
        if entities.get("birth_info") and enhanced_intent["tool_name"] == "general":
            enhanced_intent["tool_name"] = "comprehensive"
            enhanced_intent["reasoning"] += " (检测到出生信息)"
        
        return enhanced_intent
    
    def _validate_and_correct(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修正意图"""
        
        # 确保工具名称有效
        if intent["tool_name"] not in self.supported_tools:
            intent["tool_name"] = "general"
        
        # 确保问题类型有效
        if intent["question_type"] not in self.supported_questions:
            intent["question_type"] = "general"
        
        # 添加时间戳
        intent["timestamp"] = datetime.now()
        
        return intent
    
    def _create_fallback_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """创建备用意图"""
        return {
            "tool_name": "general",
            "question_type": "general",
            "confidence": 0.3,
            "reasoning": "意图识别失败，使用默认值",
            "entities": {},
            "timestamp": datetime.now(),
            "raw_response": message
        }
