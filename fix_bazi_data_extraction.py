#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复八字数据提取问题
确保LLM分析时使用正确的八字数据
"""

def fix_bazi_data_extraction():
    """修复八字数据提取问题"""
    print("🔧 修复八字数据提取问题")
    print("=" * 60)
    
    try:
        # 1. 测试当前的数据提取
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        from core.analysis.data_processor import DataProcessor
        
        # 获取融合分析数据
        fusion_engine = ZiweiBaziFusionEngine()
        result = fusion_engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if result["success"]:
            print("✅ 融合分析成功")
            
            # 检查原始数据中的八字
            bazi_analysis = result.get("bazi_analysis", {})
            if "bazi_info" in bazi_analysis:
                chinese_date = bazi_analysis["bazi_info"].get("chinese_date", "")
                print(f"原始八字数据: {chinese_date}")
                
                # 检查四柱数据
                year_pillar = bazi_analysis["bazi_info"].get("year_pillar", "")
                month_pillar = bazi_analysis["bazi_info"].get("month_pillar", "")
                day_pillar = bazi_analysis["bazi_info"].get("day_pillar", "")
                hour_pillar = bazi_analysis["bazi_info"].get("hour_pillar", "")
                
                print(f"四柱数据:")
                print(f"  年柱: {year_pillar}")
                print(f"  月柱: {month_pillar}")
                print(f"  日柱: {day_pillar}")
                print(f"  时柱: {hour_pillar}")
            
            # 测试数据处理器
            processor = DataProcessor()
            analysis_data = processor.extract_analysis_data(result, "personality_destiny")
            
            print(f"\n📊 数据处理器提取结果:")
            bazi_data = analysis_data.get("bazi", {})
            print(f"八字数据keys: {list(bazi_data.keys())}")
            
            # 检查四柱是否正确提取
            pillars = ["年柱", "月柱", "日柱", "时柱"]
            for pillar in pillars:
                value = bazi_data.get(pillar, "未找到")
                print(f"  {pillar}: {value}")
            
            # 检查五行数据
            wuxing = bazi_data.get("五行", {})
            print(f"五行数据: {wuxing}")
            
            return True
        else:
            print(f"❌ 融合分析失败: {result.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_generation():
    """测试提示词生成"""
    print("\n🔍 测试提示词生成")
    print("=" * 40)
    
    try:
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        from core.analysis.data_processor import DataProcessor
        from core.analysis.prompt_builder import PromptBuilder
        
        # 获取数据
        fusion_engine = ZiweiBaziFusionEngine()
        result = fusion_engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")
        
        if result["success"]:
            # 提取分析数据
            processor = DataProcessor()
            analysis_data = processor.extract_analysis_data(result, "personality_destiny")
            
            # 构建提示词
            prompt_builder = PromptBuilder()
            birth_info = {
                "year": 1988,
                "month": 6,
                "day": 1,
                "hour": 11,
                "gender": "男"
            }
            
            prompt = prompt_builder.build_analysis_prompt(analysis_data, birth_info, "personality_destiny")
            
            print(f"提示词长度: {len(prompt)}字符")
            
            # 检查提示词中的八字信息
            if "戊辰 丁巳 丁亥 丙午" in prompt:
                print("✅ 提示词包含正确八字")
            elif "戊午" in prompt:
                print("❌ 提示词包含错误八字")
            else:
                print("⚠️ 提示词中未找到八字信息")
            
            # 显示八字相关部分
            lines = prompt.split('\n')
            bazi_section = False
            for line in lines:
                if "八字" in line or "四柱" in line:
                    bazi_section = True
                    print(f"八字部分: {line}")
                elif bazi_section and line.strip() and not line.startswith('【'):
                    print(f"         {line}")
                elif bazi_section and line.startswith('【'):
                    break
            
            return True
        else:
            print("❌ 无法获取数据")
            return False
            
    except Exception as e:
        print(f"❌ 提示词测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_data_processor():
    """修复数据处理器"""
    print("\n🔧 修复数据处理器")
    print("=" * 40)
    
    # 这里我们需要修改数据处理器的代码
    # 确保正确提取八字四柱信息
    
    print("需要修改 core/analysis/data_processor.py 中的 _extract_bazi_data 方法")
    print("确保正确提取八字四柱信息")
    
    return True

def main():
    """主函数"""
    print("🔧 八字数据提取修复工具")
    print("=" * 70)
    
    # 1. 测试当前状态
    success1 = fix_bazi_data_extraction()
    
    # 2. 测试提示词生成
    success2 = test_prompt_generation()
    
    # 3. 修复建议
    fix_data_processor()
    
    print("\n" + "=" * 70)
    print("🎯 问题诊断结果:")
    
    if success1:
        print("✅ 融合分析数据获取正常")
    else:
        print("❌ 融合分析数据获取异常")
    
    if success2:
        print("✅ 提示词生成正常")
    else:
        print("❌ 提示词生成异常")
    
    print("\n💡 修复方案:")
    print("1. 检查数据处理器的八字数据提取逻辑")
    print("2. 确保四柱信息正确传递给LLM")
    print("3. 验证提示词中的八字数据准确性")
    print("4. 测试修复后的分析结果")

if __name__ == "__main__":
    main()
