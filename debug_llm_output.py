#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试LLM输出内容
检查分析结果中是否包含正确的八字信息
"""

import asyncio

async def debug_llm_output():
    """调试LLM输出内容"""
    print("🔍 调试LLM输出内容")
    print("=" * 60)
    
    try:
        from core.agents.fortune_calculator_agent import FortuneCalculatorAgent
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine
        
        # 创建计算代理
        calculator_agent = FortuneCalculatorAgent("debug_calc")
        
        # 测试生辰信息
        birth_info = {
            "year": 1988,
            "month": 6,
            "day": 1,
            "hour": 11,
            "gender": "男"
        }
        
        print(f"📅 测试生辰: {birth_info['year']}年{birth_info['month']}月{birth_info['day']}日{birth_info['hour']}时 {birth_info['gender']}")
        
        # 获取融合分析数据
        fusion_engine = ZiweiBaziFusionEngine()
        raw_data = fusion_engine.calculate_fusion_analysis(
            year=birth_info["year"],
            month=birth_info["month"],
            day=birth_info["day"],
            hour=birth_info["hour"],
            gender=birth_info["gender"]
        )
        
        if raw_data.get("success"):
            print("✅ 融合分析数据获取成功")
            
            # 检查传递给LLM的提示词
            from core.analysis.data_processor import DataProcessor
            from core.analysis.prompt_builder import PromptBuilder
            
            processor = DataProcessor()
            analysis_data = processor.extract_analysis_data(raw_data, "personality_destiny")
            
            prompt_builder = PromptBuilder()
            prompt = prompt_builder.build_analysis_prompt(analysis_data, birth_info, "personality_destiny")
            
            print(f"\n📝 提示词中的八字部分:")
            print("-" * 50)
            lines = prompt.split('\n')
            in_bazi_section = False
            for line in lines:
                if "八字" in line or "四柱" in line:
                    in_bazi_section = True
                    print(line)
                elif in_bazi_section and line.strip():
                    if line.startswith('【'):
                        break
                    print(line)
            print("-" * 50)
            
            # 执行分析并获取完整结果
            analysis_result = await calculator_agent._analyze_single_angle(
                "命宫分析", "personality_destiny", "性格命运核心特征与天赋潜能",
                raw_data, birth_info, "紫薇+八字融合分析"
            )
            
            if analysis_result:
                print(f"\n📊 分析结果统计:")
                print(f"总字数: {len(analysis_result)}")
                
                # 检查八字相关内容
                bazi_mentions = []
                if "戊辰" in analysis_result:
                    bazi_mentions.append("戊辰")
                if "丁巳" in analysis_result:
                    bazi_mentions.append("丁巳")
                if "丁亥" in analysis_result:
                    bazi_mentions.append("丁亥")
                if "丙午" in analysis_result:
                    bazi_mentions.append("丙午")
                
                print(f"八字干支提及: {bazi_mentions}")
                
                # 检查五行相关内容
                wuxing_mentions = []
                for element in ["木", "火", "土", "金", "水"]:
                    if element in analysis_result:
                        wuxing_mentions.append(element)
                
                print(f"五行提及: {wuxing_mentions}")
                
                # 检查八字术语
                bazi_terms = ["年柱", "月柱", "日柱", "时柱", "日主", "十神", "用神", "大运", "流年"]
                mentioned_terms = [term for term in bazi_terms if term in analysis_result]
                print(f"八字术语提及: {mentioned_terms}")
                
                # 显示完整分析内容
                print(f"\n📄 完整分析内容:")
                print("=" * 80)
                print(analysis_result)
                print("=" * 80)
                
                return True
            else:
                print("❌ 分析失败")
                return False
        else:
            print(f"❌ 融合分析失败: {raw_data.get('error', '')}")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 LLM输出内容调试工具")
    print("=" * 70)
    
    success = await debug_llm_output()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ 调试完成")
    else:
        print("❌ 调试失败")

if __name__ == "__main__":
    asyncio.run(main())
