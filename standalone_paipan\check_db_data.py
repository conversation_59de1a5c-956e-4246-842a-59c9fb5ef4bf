#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的六爻数据
"""

import sqlite3

def check_liuyao_data():
    """检查六爻数据"""
    
    # 连接数据库
    conn = sqlite3.connect('fortune_data.db')
    cursor = conn.cursor()
    
    # 先查看所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print("数据库中的表:", [table[0] for table in tables])

    # 获取最新的六爻记录
    cursor.execute("""
        SELECT liuyao_id, question, formatted_output, analysis
        FROM liuyao_divination
        ORDER BY created_at DESC
        LIMIT 1
    """)
    
    record = cursor.fetchone()
    if record:
        record_id, question, formatted_output, analysis = record
        
        print(f"=== 六爻记录调试 ===")
        print(f"ID: {record_id}")
        print(f"问题: {question}")
        print(f"格式化输出长度: {len(formatted_output) if formatted_output else 0}")
        print(f"分析长度: {len(analysis) if analysis else 0}")
        
        if formatted_output:
            print(f"\n=== 格式化输出 ===")
            print(formatted_output)
            
            print(f"\n=== 查找卦象图形部分 ===")
            lines = formatted_output.split('\n')
            for i, line in enumerate(lines):
                if '【卦象图形】' in line:
                    print(f"找到卦象图形标题在第{i+1}行")
                    # 显示接下来的15行
                    for j in range(i, min(i+20, len(lines))):
                        if lines[j].strip():
                            print(f"第{j+1}行: {repr(lines[j])}")
                    break
            else:
                print("未找到【卦象图形】标题")
                
        else:
            print("格式化输出为空")
            
    else:
        print("没有找到六爻记录")
    
    conn.close()

if __name__ == "__main__":
    check_liuyao_data()
