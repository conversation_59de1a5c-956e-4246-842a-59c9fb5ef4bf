#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成优雅的紫薇斗数图表
参考原版的配色和布局风格
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_elegant_ziwei():
    """生成优雅的紫薇斗数图表"""
    print("🎨 生成优雅紫薇斗数图表")
    print("=" * 50)

    try:
        # 获取数据
        from core.ziwei_bazi_fusion import ZiweiBaziFusionEngine

        engine = ZiweiBaziFusionEngine()
        result = engine.calculate_fusion_analysis(1988, 6, 1, 11, "男")

        if not result.get("success"):
            print(f"❌ 分析失败: {result.get('error')}")
            return False

        # 创建画布
        fig, ax = plt.subplots(1, 1, figsize=(14, 14))
        ax.set_xlim(0, 14)
        ax.set_ylim(0, 14)
        ax.set_aspect('equal')
        ax.axis('off')

        # 设置优雅的背景色
        fig.patch.set_facecolor('#f8f6f0')

        # 1. 绘制标题
        draw_elegant_title(ax, result)

        # 2. 绘制优雅的12宫格
        draw_elegant_palaces(ax, result)

        # 3. 在中央绘制简洁的信息
        draw_elegant_center(ax, result)

        # 保存图片
        output_file = "elegant_ziwei.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='#f8f6f0', edgecolor='none')

        print(f"✅ 优雅图表生成成功: {output_file}")
        plt.show()

        return True

    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def draw_elegant_title(ax, result):
    """绘制优雅标题"""
    birth_info = result.get("birth_info", {})

    # 主标题 - 使用优雅的紫色，字体加大
    ax.text(7, 12.5, "紫薇斗数命盘", ha='center', va='center',
           fontsize=22, fontweight='bold', color='#8B4A9C')

    # 出生信息 - 使用深灰色，字体加大
    datetime_str = birth_info.get('datetime', '1988年6月1日11时')
    ax.text(7, 11.8, datetime_str, ha='center', va='center',
           fontsize=14, color='#5D4E75')

def draw_elegant_palaces(ax, result):
    """绘制优雅的12宫格"""
    ziwei_data = result.get("ziwei_analysis", {})
    palaces = ziwei_data.get("palaces", {})

    # 12宫布局
    palace_positions = {
        # 第一行
        "子女宫": (0, 3), "财帛宫": (1, 3), "疾厄宫": (2, 3), "迁移宫": (3, 3),
        # 第二行
        "夫妻宫": (0, 2), "奴仆宫": (3, 2),
        # 第三行
        "兄弟宫": (0, 1), "官禄宫": (3, 1),
        # 第四行
        "命宫": (0, 0), "父母宫": (1, 0), "福德宫": (2, 0), "田宅宫": (3, 0)
    }

    # 宫格参数
    cell_size = 3.2
    start_x = 1.5
    start_y = 1.5

    for palace_name, (col, row) in palace_positions.items():
        x = start_x + col * cell_size
        y = start_y + row * cell_size

        palace_data = palaces.get(palace_name, {})
        draw_elegant_palace_cell(ax, x, y, cell_size, palace_name, palace_data)

def draw_elegant_palace_cell(ax, x, y, size, palace_name, palace_data):
    """绘制优雅的单个宫格"""
    # 根据宫位重要性选择背景色
    important_palaces = ["命宫", "财帛宫", "夫妻宫", "官禄宫"]

    if palace_name in important_palaces:
        # 重要宫位用淡紫色背景
        bg_color = '#F0E6FF'
        border_color = '#8B4A9C'
    else:
        # 普通宫位用淡蓝色背景
        bg_color = '#E6F3FF'
        border_color = '#5D4E75'

    # 宫格边框
    rect = Rectangle((x, y), size, size,
                    linewidth=2, edgecolor=border_color,
                    facecolor=bg_color, alpha=0.8)
    ax.add_patch(rect)

    # 宫位名称（左上角）- 字体加大
    ax.text(x + 0.15, y + size - 0.25, palace_name,
           ha='left', va='top', fontsize=12,
           fontweight='bold', color='#2C1810')

    # 地支（右上角）- 字体加大
    position = palace_data.get("position", "")
    if position:
        ax.text(x + size - 0.15, y + size - 0.25, f"({position})",
               ha='right', va='top', fontsize=11,
               color='#5D4E75')

    # 主星（使用优雅的红色）- 字体加大，对齐优化
    major_stars = palace_data.get("major_stars", [])
    if major_stars:
        y_start = y + size - 0.7
        # 改进对齐：使用固定间距
        for i, star in enumerate(major_stars[:4]):
            row = i // 2
            col = i % 2
            star_x = x + 0.25 + col * 1.4  # 增加间距
            star_y = y_start - row * 0.35   # 增加行间距

            ax.text(star_x, star_y, star,
                   ha='left', va='top', fontsize=11,  # 字体加大
                   fontweight='bold', color='#C41E3A')

    # 副星（使用优雅的绿色）- 字体加大，对齐优化
    minor_stars = palace_data.get("minor_stars", [])
    if minor_stars:
        y_start = y + size - 1.6
        # 改进对齐：3列布局，固定间距
        for i, star in enumerate(minor_stars[:9]):  # 增加到9个
            row = i // 3
            col = i % 3
            star_x = x + 0.2 + col * 1.0   # 固定列间距
            star_y = y_start - row * 0.25  # 固定行间距

            ax.text(star_x, star_y, star,
                   ha='left', va='top', fontsize=9,  # 字体加大
                   color='#228B22')

    # 四化（使用优雅的橙色）- 字体加大，位置调整
    transformations = palace_data.get("transformations", [])
    if transformations:
        for i, trans in enumerate(transformations[:3]):  # 增加到3个
            ax.text(x + 0.2, y + 0.7 - i * 0.25, trans,  # 调整位置和间距
                   ha='left', va='bottom', fontsize=9,  # 字体加大
                   color='#FF8C00')

def draw_elegant_center(ax, result):
    """在中央绘制优雅的信息"""
    bazi_data = result.get("bazi_analysis", {})

    # 中央区域 - 更小更精致
    center_x = 4.7
    center_y = 4.7
    center_width = 4.6
    center_height = 2.6

    # 中央背景 - 使用淡黄色
    center_rect = Rectangle((center_x, center_y), center_width, center_height,
                           linewidth=2, edgecolor='#8B4A9C',
                           facecolor='#FFFACD', alpha=0.9)
    ax.add_patch(center_rect)

    # 标题 - 字体加大
    ax.text(center_x + center_width/2, center_y + center_height - 0.4,
           "中宫", ha='center', va='center',
           fontsize=16, fontweight='bold', color='#2C1810')

    # 生辰信息 - 字体加大
    ax.text(center_x + center_width/2, center_y + center_height - 0.8,
           "戊辰年 丁巳月 丁亥日 丙午时", ha='center', va='center',
           fontsize=12, color='#5D4E75')

    if "bazi_info" in bazi_data:
        bazi_info = bazi_data["bazi_info"]
        chinese_date = bazi_info.get("chinese_date", "")

        # 分割四柱并显示 - 字体加大
        pillars = chinese_date.split()
        if len(pillars) >= 4:
            pillar_text = " ".join(pillars)
            ax.text(center_x + center_width/2, center_y + center_height - 1.2,
                   pillar_text, ha='center', va='center',
                   fontsize=11, color='#C41E3A')

    # 五行简要信息 - 字体加大
    if "analysis" in bazi_data and "wuxing" in bazi_data["analysis"]:
        wuxing = bazi_data["analysis"]["wuxing"]
        wuxing_count = wuxing.get("count", {})

        # 找出最旺和最弱的五行
        if wuxing_count:
            max_element = max(wuxing_count, key=wuxing_count.get)
            min_element = min(wuxing_count, key=wuxing_count.get)

            wuxing_text = f"{max_element}旺 {min_element}弱"
            ax.text(center_x + center_width/2, center_y + 0.4,
                   wuxing_text, ha='center', va='center',
                   fontsize=11, color='#228B22')

def main():
    """主函数"""
    print("🎨 优雅紫薇斗数图表生成器")
    print("=" * 60)

    success = generate_elegant_ziwei()

    if success:
        print("\n✅ 优雅图表生成成功！")
        print("📁 文件保存为: elegant_ziwei.png")
        print("🖼️ 优雅特点:")
        print("  - 参考原版的优雅配色")
        print("  - 淡紫色和淡蓝色背景")
        print("  - 柔和的颜色搭配")
        print("  - 简洁的中央信息区")
        print("  - 协调的视觉效果")
        print("  - 专业而不刺眼的色彩")
    else:
        print("\n❌ 优雅图表生成失败")

if __name__ == "__main__":
    main()
